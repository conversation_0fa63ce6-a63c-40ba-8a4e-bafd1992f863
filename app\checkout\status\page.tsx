import type { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"
import { PaymentStatus } from "@/components/checkout/payment-status"
import { Skeleton } from "@/components/ui/skeleton"

export const metadata: Metadata = {
  title: "Status Pembayaran | Sellzio",
  description: "Cek status pembayaran Anda",
}

export default function StatusPage() {
  return (
    <Suspense fallback={<StatusSkeleton />}>
      <PaymentStatus />
    </Suspense>
  )
}

function StatusSkeleton() {
  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      <Skeleton className="h-[400px] w-full rounded-lg" />
    </div>
  )
}
