"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Copy, Share2, Facebook, Twitter, Mail } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface ReferralLinkProps {
  referralLink: string
  commissionRate: number
}

export function ReferralLink({ referralLink, commissionRate }: ReferralLinkProps) {
  const [copied, setCopied] = useState(false)

  const copyToClipboard = () => {
    navigator.clipboard.writeText(referralLink)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const shareViaFacebook = () => {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralLink)}`, "_blank")
  }

  const shareViaTwitter = () => {
    window.open(
      `https://twitter.com/intent/tweet?url=${encodeURIComponent(referralLink)}&text=${encodeURIComponent("Gunakan link referral saya untuk berbelanja di Sellzio!")}`,
      "_blank",
    )
  }

  const shareViaEmail = () => {
    window.open(
      `mailto:?subject=${encodeURIComponent("Referral Sellzio")}&body=${encodeURIComponent(`Halo, saya ingin berbagi link referral Sellzio dengan Anda. Gunakan link ini untuk berbelanja: ${referralLink}`)}`,
      "_blank",
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Link Referral Anda</CardTitle>
        <CardDescription>Bagikan link ini dan dapatkan komisi {commissionRate}% dari setiap pembelian</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          <div className="flex space-x-2">
            <Input value={referralLink} readOnly className="flex-1" />
            <Button onClick={copyToClipboard} variant="outline">
              {copied ? "Disalin!" : <Copy className="h-4 w-4" />}
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Share2 className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={shareViaFacebook}>
                  <Facebook className="mr-2 h-4 w-4" />
                  <span>Facebook</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={shareViaTwitter}>
                  <Twitter className="mr-2 h-4 w-4" />
                  <span>Twitter</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={shareViaEmail}>
                  <Mail className="mr-2 h-4 w-4" />
                  <span>Email</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium">1. Bagikan</h3>
                <p className="text-sm text-muted-foreground">
                  Bagikan link referral Anda ke teman, keluarga, atau media sosial
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium">2. Belanja</h3>
                <p className="text-sm text-muted-foreground">Mereka berbelanja menggunakan link referral Anda</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium">3. Dapatkan Komisi</h3>
                <p className="text-sm text-muted-foreground">
                  Anda mendapatkan komisi {commissionRate}% dari setiap pembelian
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
