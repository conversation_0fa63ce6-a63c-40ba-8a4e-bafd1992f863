"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  FileText,
  Heart,
  User,
  Star,
  MessageSquare,
  Home,
  Store,
  PlusCircle,
  ExternalLink,
  DollarSign,
  Headset,
  CalendarDays,
  ChevronRight,
  ChevronDown,
  ShoppingBag,
  Settings
} from "lucide-react"
import { useSidebar } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { useAuth } from "@/contexts/auth-context"
import { Badge } from "@/components/ui/badge"

// Tambahkan tenantSlug sebagai properti opsional pada user yang diterima dari useAuth
interface ExtendedUser {
  id: string
  name: string
  email: string
  role: string
  storeId?: string
  isAffiliate?: boolean
  tenantSlug?: string // properti opsional untuk tenant slug
}

export function BuyerSidebar() {
  const pathname = usePathname()
  const router = useRouter()
  const { state: sidebarState } = useSidebar()
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const [isNavigating, setIsNavigating] = useState(false)
  const [isTablet, setIsTablet] = useState(false)
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null)
  const submenuRefs = useRef<{ [key: string]: HTMLDivElement | null }>({})
  const { user } = useAuth()
  
  // Cast user ke ExtendedUser untuk mengatasi error TypeScript
  const extendedUser = user as ExtendedUser | null
  // Gunakan nilai default untuk tenant slug jika tidak ada
  const tenantSlug = extendedUser?.tenantSlug || 'default'

  // Cek status user
  const hasStore = user?.storeId !== undefined
  const isAffiliate = user?.isAffiliate === true

  useEffect(() => {
    // Deteksi apakah tampilan tablet (768px-1024px)
    const checkIsTablet = () => {
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024)
    }
    
    // Cek saat load pertama
    checkIsTablet()
    
    // Tambahkan event listener untuk resize
    window.addEventListener('resize', checkIsTablet)
    
    // Cleanup listener
    return () => window.removeEventListener('resize', checkIsTablet)
  }, [])

  // Tutup submenu saat klik di luar
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (activeSubmenu && submenuRefs.current[activeSubmenu]) {
        const submenuEl = submenuRefs.current[activeSubmenu];
        if (submenuEl && !submenuEl.contains(event.target as Node)) {
          setActiveSubmenu(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeSubmenu]);

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`)
  }

  const handleNavigation = (href: string) => (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (isNavigating) {
      e.preventDefault()
      return
    }

    // Jika navigasi tidak menggunakan Link (misalnya karena onClick custom)
    if (!e.defaultPrevented) {
      e.preventDefault()
      setIsNavigating(true)

      // Gunakan timeout untuk mencegah multiple clicks
      setTimeout(() => {
        router.push(href)
        setIsNavigating(false)
      }, 100)
    }
  }

  const toggleSubmenu = (title: string) => {
    if (activeSubmenu === title) {
      setActiveSubmenu(null);
    } else {
      setActiveSubmenu(title);
    }
  };

  // Definisikan menu items dengan format yang sama seperti tenant
  const menuItems = [
    {
      title: "Dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
      href: "/buyer/dashboard",
      active: isActive("/buyer/dashboard") && !pathname.includes("/buyer/dashboard/"),
    },
    {
      title: "Marketplace",
      icon: <Home className="h-5 w-5" />,
      href: "/tenant/[slug]", // Ini akan diganti dengan link tenant yang sebenarnya saat runtime
      active: false,
      isExternal: true
    },
    {
      title: "Pesanan",
      icon: <FileText className="h-5 w-5" />,
      href: "/buyer/dashboard/orders",
      active: isActive("/buyer/dashboard/orders"),
      submenu: [
        { 
          title: "Semua Pesanan", 
          href: "/buyer/dashboard/orders", 
          active: pathname === "/buyer/dashboard/orders",
          icon: <FileText className="h-4 w-4" />
        },
        { 
          title: "Lacak Pesanan", 
          href: "/buyer/dashboard/orders/tracking", 
          active: pathname === "/buyer/dashboard/orders/tracking",
          icon: <ShoppingBag className="h-4 w-4" />
        }
      ]
    },
    {
      title: "Wishlist",
      icon: <Heart className="h-5 w-5" />,
      href: "/buyer/dashboard/wishlist",
      active: isActive("/buyer/dashboard/wishlist"),
    },
    {
      title: "Ulasan",
      icon: <Star className="h-5 w-5" />,
      href: "/buyer/dashboard/reviews",
      active: isActive("/buyer/dashboard/reviews"),
    },
    {
      title: "Events",
      icon: <CalendarDays className="h-5 w-5" />,
      href: "/buyer/dashboard/events",
      active: isActive("/buyer/dashboard/events"),
    },
    {
      title: "Pesan",
      icon: <MessageSquare className="h-5 w-5" />,
      href: "/buyer/dashboard/messages",
      active: isActive("/buyer/dashboard/messages"),
    },
    {
      title: "Customer Service",
      icon: <Headset className="h-5 w-5" />,
      href: "/buyer/dashboard/customer-service",
      active: isActive("/buyer/dashboard/customer-service"),
    },
    {
      title: "Akun",
      icon: <User className="h-5 w-5" />,
      href: "/buyer/dashboard/account",
      active: isActive("/buyer/dashboard/account"),
      submenu: [
        { 
          title: "Profil", 
          href: "/buyer/dashboard/account/profile", 
          active: pathname === "/buyer/dashboard/account/profile",
          icon: <User className="h-4 w-4" />
        },
        { 
          title: "Alamat", 
          href: "/buyer/dashboard/account/addresses", 
          active: pathname === "/buyer/dashboard/account/addresses",
          icon: <Home className="h-4 w-4" />
        },
        { 
          title: "Pengaturan", 
          href: "/buyer/dashboard/account/settings", 
          active: pathname === "/buyer/dashboard/account/settings",
          icon: <Settings className="h-4 w-4" />
        }
      ]
    }
  ]

  // Tambahkan menu Daftar Toko jika user belum memiliki toko
  if (!hasStore) {
    menuItems.push({
      title: "Daftar Toko",
      icon: <PlusCircle className="h-5 w-5" />,
      href: "/buyer/dashboard/store-application",
      active: isActive("/buyer/dashboard/store-application"),
    })
  } else {
    // Tambahkan menu Store Dashboard jika user memiliki toko
    menuItems.push({
      title: "Store Dashboard",
      icon: <Store className="h-5 w-5" />,
      href: "/store/dashboard",
      active: false,
      isExternal: true
    })
  }

  // Tambahkan menu Affiliate sesuai status
  if (isAffiliate) {
    // Jika sudah affiliate, tambahkan link ke affiliate dashboard
    menuItems.push({
      title: "Affiliate Dashboard",
      icon: <DollarSign className="h-5 w-5" />,
      href: "/buyer/dashboard/affiliate",
      active: isActive("/buyer/dashboard/affiliate")
    })
  } else {
    // Jika belum affiliate, tambahkan link ke pendaftaran affiliate
    menuItems.push({
      title: "Daftar Affiliate",
      icon: <DollarSign className="h-5 w-5" />,
      href: "/buyer/dashboard/affiliate-application",
      active: isActive("/buyer/dashboard/affiliate-application")
    })
  }

  // Menentukan lebar sidebar berdasarkan mode
  const sidebarWidth = isTablet ? "w-16" : "w-64"

  return (
    <div
      className={cn(
        "z-20 flex flex-col border-r bg-background transition-all",
        sidebarWidth,
        // Tidak lagi menggunakan fixed di tablet, tapi berubah menjadi static
        isTablet ? "static" : "fixed inset-y-0 left-0 lg:static",
        sidebarState === "collapsed" ? "-translate-x-full" : "translate-x-0"
      )}
    >
      <div className="flex h-14 items-center border-b px-4">
        <Link href="/buyer/dashboard" className="flex items-center gap-2 font-semibold">
          <span className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-xs font-bold text-primary-foreground">
            SZ
          </span>
          {!isTablet && <span>Sellzio</span>}
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-2">
        {!isTablet && (
          <div className="px-3 py-2">
            <div className="flex items-center gap-2 rounded-md bg-muted px-3 py-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-xs font-bold text-primary-foreground">
                {user?.name?.charAt(0) || 'U'}
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-medium">{user?.name || 'User'}</span>
                <span className="text-xs text-muted-foreground">{user?.email || '<EMAIL>'}</span>
              </div>
            </div>
          </div>
        )}
        <nav className={cn("space-y-1", isTablet ? "px-1 py-2" : "px-2 py-2")}>
          {menuItems.map((item, index) => (
            <div key={index} className="relative">
              {/* Render menu berbeda untuk tablet vs desktop */}
              {isTablet ? (
                <div>
                  {/* Untuk tablet - gunakan button agar bisa toggle submenu */}
                  <button
                    onClick={() => {
                      if (item.submenu) {
                        toggleSubmenu(item.title);
                      } else if (item.isExternal) {
                        window.open(`/tenant/${tenantSlug}`, "_blank");
                      } else {
                        router.push(item.href);
                      }
                    }}
                    className={cn(
                      "flex items-center rounded-md transition-colors",
                      "justify-center px-2 py-2",
                      "text-sm font-medium w-full",
                      item.active
                        ? "bg-muted text-foreground"
                        : "text-muted-foreground hover:bg-muted hover:text-foreground"
                    )}
                    title={item.title}
                  >
                    {item.icon}
                    {item.isExternal && <ExternalLink className="absolute top-0 right-0 h-3 w-3 text-muted-foreground/70" />}
                  </button>
                  
                  {/* Submenu untuk tablet */}
                  {item.submenu && activeSubmenu === item.title && (
                    <div 
                      ref={(el) => {
                        submenuRefs.current[item.title] = el;
                        return undefined;
                      }}
                      className="fixed ml-16 mt-1 w-48 bg-background border border-border rounded-md shadow-md p-1 z-50"
                    >
                      {item.submenu.map((subitem, subindex) => (
                        <Link
                          key={subindex}
                          href={subitem.href}
                          onClick={handleNavigation(subitem.href)}
                          className={cn(
                            "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                            subitem.active
                              ? "bg-muted text-foreground"
                              : "text-muted-foreground hover:bg-muted hover:text-foreground"
                          )}
                        >
                          {subitem.icon && <span className="mr-1">{subitem.icon}</span>}
                          {subitem.title}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                /* Untuk desktop - tetap gunakan tampilan admin-like */
                <>
                  {item.isExternal ? (
                    <a
                      href={`/tenant/${tenantSlug}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={cn(
                        "flex items-center rounded-md transition-colors",
                        "gap-3 px-3 py-2",
                        "text-sm font-medium",
                        "text-muted-foreground hover:bg-muted hover:text-foreground"
                      )}
                    >
                      {item.icon}
                      <span className="flex-1">{item.title}</span>
                      <ExternalLink className="h-3 w-3 text-muted-foreground/70" />
                    </a>
                  ) : (
                    <Link
                      href={item.href}
                      onClick={handleNavigation(item.href)}
                      className={cn(
                        "flex items-center rounded-md transition-colors",
                        "gap-3 px-3 py-2",
                        "text-sm font-medium",
                        item.active
                          ? "bg-muted text-foreground" 
                          : "text-muted-foreground hover:bg-muted hover:text-foreground"
                      )}
                    >
                      {item.icon}
                      <span className="flex-1">{item.title}</span>
                    </Link>
                  )}
                  
                  {/* Submenu desktop */}
                  {item.submenu && (activeSubmenu === item.title || item.active) && (
                    <div className="ml-4 mt-1 space-y-1 border-l pl-3">
                      {item.submenu.map((subitem, subindex) => (
                        <Link
                          key={subindex}
                          href={subitem.href}
                          onClick={handleNavigation(subitem.href)}
                          className={cn(
                            "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                            subitem.active
                              ? "bg-muted text-foreground"
                              : "text-muted-foreground hover:bg-muted hover:text-foreground"
                          )}
                        >
                          {subitem.icon && <span className="mr-1">{subitem.icon}</span>}
                          {subitem.title}
                        </Link>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          ))}
        </nav>
      </div>
    </div>
  )
}
