"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Search, Download, Filter, CalendarIcon, Plus, CheckCircle, XCircle } from "lucide-react"
import { format } from "date-fns"

// Mock data for payouts
const payouts = [
  {
    id: "PAY-12345",
    recipient: "Tech Haven",
    recipientType: "Store",
    amount: "$1,250.00",
    status: "Completed",
    scheduledDate: "2023-10-15",
    method: "Bank Transfer",
  },
  {
    id: "PAY-12346",
    recipient: "<PERSON>",
    recipientType: "Affiliate",
    amount: "$450.00",
    status: "Pending",
    scheduledDate: "2023-10-20",
    method: "PayPal",
  },
  {
    id: "PAY-12347",
    recipient: "Fashion Forward",
    recipientType: "Store",
    amount: "$890.00",
    status: "Processing",
    scheduledDate: "2023-10-18",
    method: "Bank Transfer",
  },
  {
    id: "PAY-12348",
    recipient: "Home Essentials",
    recipientType: "Store",
    amount: "$2,100.00",
    status: "Scheduled",
    scheduledDate: "2023-10-25",
    method: "Bank Transfer",
  },
  {
    id: "PAY-12349",
    recipient: "Sarah Johnson",
    recipientType: "Affiliate",
    amount: "$320.00",
    status: "Completed",
    scheduledDate: "2023-10-12",
    method: "PayPal",
  },
  {
    id: "PAY-12350",
    recipient: "Gourmet Delights",
    recipientType: "Store",
    amount: "$780.00",
    status: "Failed",
    scheduledDate: "2023-10-10",
    method: "Bank Transfer",
  },
  {
    id: "PAY-12351",
    recipient: "Michael Brown",
    recipientType: "Affiliate",
    amount: "$560.00",
    status: "Pending",
    scheduledDate: "2023-10-22",
    method: "Stripe",
  },
  {
    id: "PAY-12352",
    recipient: "Book Nook",
    recipientType: "Store",
    amount: "$1,450.00",
    status: "Scheduled",
    scheduledDate: "2023-10-28",
    method: "Bank Transfer",
  },
  {
    id: "PAY-12353",
    recipient: "Fitness Gear",
    recipientType: "Store",
    amount: "$920.00",
    status: "Processing",
    scheduledDate: "2023-10-19",
    method: "Bank Transfer",
  },
  {
    id: "PAY-12354",
    recipient: "Emily Davis",
    recipientType: "Affiliate",
    amount: "$280.00",
    status: "Completed",
    scheduledDate: "2023-10-14",
    method: "PayPal",
  },
]

// Mock data for payment methods
const paymentMethods = [
  {
    id: "method-1",
    name: "Bank Transfer",
    description: "Direct bank transfer (ACH/SEPA)",
    default: true,
  },
  {
    id: "method-2",
    name: "PayPal",
    description: "PayPal payment system",
    default: false,
  },
  {
    id: "method-3",
    name: "Stripe",
    description: "Stripe payment processing",
    default: false,
  },
  {
    id: "method-4",
    name: "Wise",
    description: "Wise international transfers",
    default: false,
  },
]

// Mock data for scheduled payouts
const scheduledPayouts = [
  {
    date: "2023-10-20",
    count: 5,
    amount: "$4,250.00",
  },
  {
    date: "2023-10-25",
    count: 8,
    amount: "$7,890.00",
  },
  {
    date: "2023-10-28",
    count: 3,
    amount: "$2,450.00",
  },
  {
    date: "2023-11-01",
    count: 12,
    amount: "$12,780.00",
  },
  {
    date: "2023-11-05",
    count: 6,
    amount: "$5,320.00",
  },
]

// Helper function to get badge variant based on status
function getStatusBadge(status: string) {
  switch (status) {
    case "Completed":
      return { variant: "success" as const, icon: CheckCircle }
    case "Processing":
      return { variant: "default" as const, icon: null }
    case "Pending":
      return { variant: "outline" as const, icon: null }
    case "Scheduled":
      return { variant: "secondary" as const, icon: CalendarIcon }
    case "Failed":
      return { variant: "destructive" as const, icon: XCircle }
    default:
      return { variant: "outline" as const, icon: null }
  }
}

export function Payouts() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [date, setDate] = useState<Date | undefined>(new Date())

  // Filter payouts based on search term and status
  const filteredPayouts = payouts.filter(
    (payout) =>
      (payout.recipient.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payout.id.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (statusFilter === "all" || payout.status === statusFilter),
  )

  return (
    <div className="grid gap-6">
      <Tabs defaultValue="payouts" className="w-full">
        <TabsList className="grid w-full grid-cols-3 lg:w-[600px]">
          <TabsTrigger value="payouts">Payout Management</TabsTrigger>
          <TabsTrigger value="calendar">Payout Calendar</TabsTrigger>
          <TabsTrigger value="methods">Payment Methods</TabsTrigger>
        </TabsList>
        <TabsContent value="payouts" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div>
                  <CardTitle>Payout Management</CardTitle>
                  <CardDescription>Manage payouts to stores and affiliates</CardDescription>
                </div>
                <div className="flex flex-col gap-2 sm:flex-row">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search payouts..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select defaultValue={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="Completed">Completed</SelectItem>
                      <SelectItem value="Processing">Processing</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="Scheduled">Scheduled</SelectItem>
                      <SelectItem value="Failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    New Payout
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Payout ID</TableHead>
                      <TableHead>Recipient</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Scheduled Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPayouts.map((payout) => {
                      const { variant, icon: StatusIcon } = getStatusBadge(payout.status)
                      return (
                        <TableRow key={payout.id}>
                          <TableCell className="font-medium">{payout.id}</TableCell>
                          <TableCell>{payout.recipient}</TableCell>
                          <TableCell>{payout.recipientType}</TableCell>
                          <TableCell className="text-right">{payout.amount}</TableCell>
                          <TableCell>{payout.method}</TableCell>
                          <TableCell>
                            <Badge variant={variant} className="flex w-28 justify-center">
                              {StatusIcon && <StatusIcon className="mr-1 h-3 w-3" />}
                              {payout.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">{payout.scheduledDate}</TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm">
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
              <div className="flex items-center justify-end space-x-2 py-4">
                <Button variant="outline" size="sm">
                  Previous
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="calendar" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Payout Calendar</CardTitle>
                <CardDescription>View and manage scheduled payouts</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {date ? format(date, "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Scheduled Payouts</CardTitle>
                <CardDescription>Payouts scheduled for the next 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-center">Count</TableHead>
                        <TableHead className="text-right">Total Amount</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {scheduledPayouts.map((payout, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{payout.date}</TableCell>
                          <TableCell className="text-center">{payout.count}</TableCell>
                          <TableCell className="text-right">{payout.amount}</TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm">
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button>Process All</Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="methods" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle>Payment Methods</CardTitle>
                  <CardDescription>Manage available payment methods for payouts</CardDescription>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Method
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Method Name</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-center">Default</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paymentMethods.map((method) => (
                      <TableRow key={method.id}>
                        <TableCell className="font-medium">{method.name}</TableCell>
                        <TableCell>{method.description}</TableCell>
                        <TableCell className="text-center">
                          {method.default ? (
                            <Badge variant="outline" className="bg-green-50">
                              Default
                            </Badge>
                          ) : (
                            ""
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="sm">
                              Edit
                            </Button>
                            {!method.default && (
                              <Button variant="ghost" size="sm">
                                Set Default
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
