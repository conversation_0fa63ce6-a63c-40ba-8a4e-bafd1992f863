import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface StoreApplication {
  id: string;
  store_name: string;
  owner_name: string;
  email: string;
  phone: string;
  category: string;
  location: string;
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  submitted_date: string;
  business_plan: string;
  experience: string;
  expected_revenue: string;
  documents: string[];
  description: string;
  social_media: {
    instagram?: string;
    website?: string;
  };
  reject_reason?: string;
  approve_reason?: string;
  review_notes?: string;
  reviewed_date?: string;
  created_at: string;
  updated_at: string;
}

export interface UseStoreApplicationsReturn {
  applications: StoreApplication[];
  loading: boolean;
  error: string | null;
  fetchApplications: (filters?: { status?: string; search?: string }) => Promise<void>;
  getApplication: (id: string) => Promise<StoreApplication | null>;
  approveApplication: (id: string, reason?: string) => Promise<boolean>;
  rejectApplication: (id: string, reason: string) => Promise<boolean>;
  updateApplicationStatus: (id: string, status: string, notes?: string) => Promise<boolean>;
  deleteApplication: (id: string) => Promise<boolean>;
  refreshApplications: () => Promise<void>;
}

export function useStoreApplications(): UseStoreApplicationsReturn {
  const [applications, setApplications] = useState<StoreApplication[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all applications with optional filters
  const fetchApplications = useCallback(async (filters?: { status?: string; search?: string }) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (filters?.status && filters.status !== 'all') {
        params.append('status', filters.status);
      }
      if (filters?.search) {
        params.append('search', filters.search);
      }

      const url = `/api/store-applications${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch applications');
      }

      const data = await response.json();
      setApplications(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, []); // Empty dependency array since it doesn't depend on any state

  // Get single application by ID
  const getApplication = async (id: string): Promise<StoreApplication | null> => {
    try {
      const response = await fetch(`/api/store-applications/${id}`);

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch application');
      }

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  };

  // Approve application
  const approveApplication = useCallback(async (id: string, reason?: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/store-applications/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'approve',
          reason,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to approve application');
      }

      const updatedApplication = await response.json();

      // Update local state
      setApplications(prev =>
        prev.map(app => app.id === id ? updatedApplication : app)
      );

      toast.success('Aplikasi berhasil disetujui');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Reject application
  const rejectApplication = useCallback(async (id: string, reason: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/store-applications/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'reject',
          reason,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reject application');
      }

      const updatedApplication = await response.json();

      // Update local state
      setApplications(prev =>
        prev.map(app => app.id === id ? updatedApplication : app)
      );

      toast.success('Aplikasi berhasil ditolak');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Update application status
  const updateApplicationStatus = useCallback(async (id: string, status: string, notes?: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/store-applications/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'review',
          status,
          notes,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update application status');
      }

      const updatedApplication = await response.json();

      // Update local state
      setApplications(prev =>
        prev.map(app => app.id === id ? updatedApplication : app)
      );

      toast.success('Status aplikasi berhasil diperbarui');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Delete application
  const deleteApplication = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/store-applications/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete application');
      }

      // Update local state
      setApplications(prev => prev.filter(app => app.id !== id));

      toast.success('Aplikasi berhasil dihapus');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Refresh applications (re-fetch with current filters)
  const refreshApplications = useCallback(async () => {
    await fetchApplications();
  }, [fetchApplications]);

  // Initial fetch on mount
  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  return {
    applications,
    loading,
    error,
    fetchApplications,
    getApplication,
    approveApplication,
    rejectApplication,
    updateApplicationStatus,
    deleteApplication,
    refreshApplications,
  };
}
