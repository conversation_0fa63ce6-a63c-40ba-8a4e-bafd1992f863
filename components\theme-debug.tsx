"use client"

import { useEffect, useState } from "react"

export function ThemeDebug() {
  const [debug, setDebug] = useState({
    theme: "",
    htmlClasses: "",
    localStorage: "",
  })

  useEffect(() => {
    const updateDebug = () => {
      setDebug({
        theme: document.documentElement.classList.contains("dark") ? "dark" : "light",
        htmlClasses: document.documentElement.className,
        localStorage: localStorage.getItem("theme") || "not set",
      })
    }

    updateDebug()

    // Update setiap kali class berubah
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "attributes" && mutation.attributeName === "class") {
          updateDebug()
        }
      })
    })

    observer.observe(document.documentElement, { attributes: true })

    return () => observer.disconnect()
  }, [])

  return (
    <div className="theme-debug">
      <div>Theme: {debug.theme}</div>
      <div>HTML Classes: {debug.htmlClasses}</div>
      <div>localStorage: {debug.localStorage}</div>
    </div>
  )
}
