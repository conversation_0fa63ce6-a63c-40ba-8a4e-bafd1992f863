import { NextRequest, NextResponse } from 'next/server';

// Referensi ke data orders (akan diganti dengan database)
let orders = [
  {
    id: "ORD-12345",
    date: "2025-05-15",
    status: "processing",
    items: [
      { name: "Kemeja Casual", quantity: 1, price: 150000, image: "/placeholder-3vk2t.png" },
      { name: "<PERSON><PERSON>", quantity: 1, price: 250000, image: "/folded-denim-stack.png" },
    ],
    total: 400000,
    shipping: {
      method: "JNE Regular",
      status: "preparing",
      trackingNumber: "JNE123456789",
      eta: "2025-05-20",
    },
  },
  {
    id: "ORD-12344",
    date: "2025-05-10",
    status: "shipped",
    items: [{ name: "Sepatu Sneakers", quantity: 1, price: 350000, image: "/diverse-sneaker-collection.png" }],
    total: 350000,
    shipping: {
      method: "SiCepat",
      status: "in_transit",
      trackingNumber: "SC987654321",
      eta: "2025-05-17",
    },
  },
  {
    id: "ORD-12343",
    date: "2025-05-05",
    status: "delivered",
    items: [
      { name: "<PERSON><PERSON>", quantity: 1, price: 200000, image: "/colorful-backpack-on-wooden-table.png" },
      { name: "Topi Baseball", quantity: 2, price: 75000, image: "/baseball-cap-display.png" },
      { name: "Kaos Polos", quantity: 1, price: 100000, image: "/plain-white-tshirt.png" },
    ],
    total: 450000,
    shipping: {
      method: "J&T Express",
      status: "delivered",
      trackingNumber: "JT123456789",
      eta: "2025-05-08",
      deliveredAt: "2025-05-07",
    },
  },
  {
    id: "ORD-12342",
    date: "2025-04-28",
    status: "cancelled",
    items: [{ name: "Jam Tangan", quantity: 1, price: 450000, image: "/wrist-watch-close-up.png" }],
    total: 450000,
    shipping: {
      method: "JNE Regular",
      status: "cancelled",
      trackingNumber: null,
      eta: null,
    },
    cancellationReason: "Out of stock",
  },
  {
    id: "ORD-12341",
    date: "2025-04-20",
    status: "delivered",
    items: [
      { name: "Hoodie", quantity: 1, price: 300000, image: "/cozy-hoodie.png" },
      { name: "Syal", quantity: 1, price: 100000, image: "/cozy-knit-scarf.png" },
    ],
    total: 400000,
    shipping: {
      method: "AnterAja",
      status: "delivered",
      trackingNumber: "AA123456789",
      eta: "2025-04-25",
      deliveredAt: "2025-04-23",
    },
  },
];

// GET - Mendapatkan order berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const orderId = params.id;
  
  // Cari order berdasarkan ID
  const order = orders.find(order => order.id === orderId);
  
  if (!order) {
    return NextResponse.json({ error: 'Order tidak ditemukan' }, { status: 404 });
  }
  
  return NextResponse.json(order);
}

// PUT - Memperbarui order berdasarkan ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const orderId = params.id;
    const body = await request.json();
    
    // Cari index order yang akan diupdate
    const orderIndex = orders.findIndex(order => order.id === orderId);
    
    if (orderIndex === -1) {
      return NextResponse.json({ error: 'Order tidak ditemukan' }, { status: 404 });
    }
    
    // Update order
    const updatedOrder = {
      ...orders[orderIndex],
      ...body,
      id: orderId, // Pastikan ID tidak berubah
    };
    
    // Simpan perubahan
    orders[orderIndex] = updatedOrder;
    
    return NextResponse.json(updatedOrder);
  } catch (error) {
    return NextResponse.json({ error: 'Terjadi kesalahan server' }, { status: 500 });
  }
}

// DELETE - Menghapus order berdasarkan ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const orderId = params.id;
  
  // Cari index order yang akan dihapus
  const orderIndex = orders.findIndex(order => order.id === orderId);
  
  if (orderIndex === -1) {
    return NextResponse.json({ error: 'Order tidak ditemukan' }, { status: 404 });
  }
  
  // Hapus order
  orders.splice(orderIndex, 1);
  
  return NextResponse.json({ message: 'Order berhasil dihapus' });
} 