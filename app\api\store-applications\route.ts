import { NextRequest, NextResponse } from 'next/server';
import { storeApplicationService } from '@/lib/services/store-applications';

// GET - Mendapatkan semua store applications atau difilter berdasarkan query
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    const filters = {
      status: status || undefined,
      search: search || undefined,
    };

    const applications = await storeApplicationService.getApplications(filters);

    return NextResponse.json(applications);
  } catch (error) {
    console.error('Error fetching applications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch applications' },
      { status: 500 }
    );
  }
}

// POST - Membuat store application baru (biasanya dari buyer side)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const newApplication = await storeApplicationService.createApplication({
      ...body,
      status: 'pending',
      submitted_date: new Date().toISOString().split('T')[0],
    });

    return NextResponse.json(newApplication, { status: 201 });
  } catch (error) {
    console.error('Error creating application:', error);
    return NextResponse.json(
      { error: 'Failed to create application' },
      { status: 500 }
    );
  }
}

// PUT - Update store application (approve, reject, edit status)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Application ID is required' },
        { status: 400 }
      );
    }

    const updatedApplication = await storeApplicationService.updateApplication(id, updateData);

    return NextResponse.json(updatedApplication);
  } catch (error) {
    console.error('Error updating application:', error);
    return NextResponse.json(
      { error: 'Failed to update application' },
      { status: 500 }
    );
  }
}

// DELETE - Hapus store application
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Application ID is required' },
        { status: 400 }
      );
    }

    await storeApplicationService.deleteApplication(id);

    return NextResponse.json({
      message: 'Application deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting application:', error);
    return NextResponse.json(
      { error: 'Failed to delete application' },
      { status: 500 }
    );
  }
}
