"use client"

import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DollarSign, TrendingUp, Users, ShoppingBag } from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { useState } from "react"

export function AffiliateOverview() {
  const [period, setPeriod] = useState<"day" | "week" | "month" | "year">("month")

  // Data dummy berdasarkan periode yang dipilih
  const data = {
    day: {
      currentEarnings: 25000,
      pendingCommissions: 75000,
      lifetimeEarnings: 1250000,
      nextPayout: 100000,
      nextPayoutDate: "15 Juni 2023",
      conversionRate: 2.8,
      clickCount: 120,
      commissionRate: 10,
      averageOrderValue: 350000,
    },
    week: {
      currentEarnings: 150000,
      pendingCommissions: 225000,
      lifetimeEarnings: 1250000,
      nextPayout: 375000,
      nextPayoutDate: "15 Juni 2023",
      conversionRate: 3.2,
      clickCount: 850,
      commissionRate: 10,
      averageOrderValue: 375000,
    },
    month: {
      currentEarnings: 450000,
      pendingCommissions: 550000,
      lifetimeEarnings: 1250000,
      nextPayout: 1000000,
      nextPayoutDate: "15 Juni 2023",
      conversionRate: 3.5,
      clickCount: 3200,
      commissionRate: 10,
      averageOrderValue: 400000,
    },
    year: {
      currentEarnings: 1250000,
      pendingCommissions: 550000,
      lifetimeEarnings: 1250000,
      nextPayout: 1000000,
      nextPayoutDate: "15 Juni 2023",
      conversionRate: 3.8,
      clickCount: 12500,
      commissionRate: 10,
      averageOrderValue: 425000,
    },
  }

  const currentData = data[period]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Earnings Overview</h2>
        <Tabs value={period} onValueChange={(value) => setPeriod(value as any)} className="w-auto">
          <TabsList>
            <TabsTrigger value="day">Hari Ini</TabsTrigger>
            <TabsTrigger value="week">Minggu Ini</TabsTrigger>
            <TabsTrigger value="month">Bulan Ini</TabsTrigger>
            <TabsTrigger value="year">Tahun Ini</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendapatan {getPeriodLabel(period)}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentData.currentEarnings)}</div>
            <p className="text-xs text-muted-foreground">
              Dari komisi penjualan {getPeriodLabel(period).toLowerCase()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Komisi Tertunda</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentData.pendingCommissions)}</div>
            <p className="text-xs text-muted-foreground">Menunggu persetujuan dan pembayaran</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentData.lifetimeEarnings)}</div>
            <p className="text-xs text-muted-foreground">Total pendapatan sepanjang waktu</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pembayaran Berikutnya</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentData.nextPayout)}</div>
            <p className="text-xs text-muted-foreground">Dijadwalkan pada {currentData.nextPayoutDate}</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentData.conversionRate}%</div>
            <p className="text-xs text-muted-foreground">Dari klik ke pembelian</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Jumlah Klik</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentData.clickCount}</div>
            <p className="text-xs text-muted-foreground">Total klik pada link affiliate</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Komisi Rate</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentData.commissionRate}%</div>
            <p className="text-xs text-muted-foreground">Dari setiap penjualan</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rata-rata Order</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentData.averageOrderValue)}</div>
            <p className="text-xs text-muted-foreground">Nilai rata-rata per order</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

function getPeriodLabel(period: string): string {
  switch (period) {
    case "day":
      return "Hari Ini"
    case "week":
      return "Minggu Ini"
    case "month":
      return "Bulan Ini"
    case "year":
      return "Tahun Ini"
    default:
      return "Periode Ini"
  }
}
