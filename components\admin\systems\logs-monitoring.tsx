"use client"

import { useState } from "react"
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Clock,
  Download,
  FileText,
  Filter,
  RefreshCw,
  Search,
  Terminal,
  X,
  Users,
  Activity,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRangePicker } from "@/components/analytics/date-range-picker"
import { AnalyticsTimeSeriesChart } from "@/components/analytics/analytics-time-series-chart"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data for application logs
const applicationLogs = [
  {
    id: "log-1",
    timestamp: "2025-05-13T05:32:14",
    level: "info",
    service: "API Server",
    message: "User authentication successful",
    details: "User ID: 12345, IP: ***********",
  },
  {
    id: "log-2",
    timestamp: "2025-05-13T05:30:45",
    level: "warning",
    service: "Payment Gateway",
    message: "Payment processing delayed",
    details: "Transaction ID: TX-9876, Delay: 2.5s",
  },
  {
    id: "log-3",
    timestamp: "2025-05-13T05:28:22",
    level: "error",
    service: "Database",
    message: "Connection timeout",
    details: "Query: SELECT * FROM users WHERE id = ?, Duration: 5000ms",
  },
  {
    id: "log-4",
    timestamp: "2025-05-13T05:25:10",
    level: "info",
    service: "Cache",
    message: "Cache invalidated",
    details: "Key pattern: user:profile:*, Reason: data update",
  },
  {
    id: "log-5",
    timestamp: "2025-05-13T05:22:05",
    level: "debug",
    service: "API Server",
    message: "Request processed",
    details: "Endpoint: /api/products, Method: GET, Duration: 120ms",
  },
  {
    id: "log-6",
    timestamp: "2025-05-13T05:20:30",
    level: "error",
    service: "Storage",
    message: "File upload failed",
    details: "File: product-image.jpg, Size: 2.5MB, Error: timeout",
  },
  {
    id: "log-7",
    timestamp: "2025-05-13T05:18:12",
    level: "info",
    service: "Email Service",
    message: "Email sent successfully",
    details: "Template: password-reset, Recipient: <EMAIL>",
  },
  {
    id: "log-8",
    timestamp: "2025-05-13T05:15:45",
    level: "warning",
    service: "API Server",
    message: "Rate limit approaching",
    details: "IP: ***********, Endpoint: /api/products, Current: 95/100",
  },
]

// Mock data for error logs
const errorLogs = [
  {
    id: "err-1",
    timestamp: "2025-05-13T05:28:22",
    severity: "high",
    service: "Database",
    message: "Connection timeout",
    details: "Query: SELECT * FROM users WHERE id = ?, Duration: 5000ms",
    count: 3,
  },
  {
    id: "err-2",
    timestamp: "2025-05-13T05:20:30",
    severity: "medium",
    service: "Storage",
    message: "File upload failed",
    details: "File: product-image.jpg, Size: 2.5MB, Error: timeout",
    count: 1,
  },
  {
    id: "err-3",
    timestamp: "2025-05-13T04:45:12",
    severity: "critical",
    service: "Payment Gateway",
    message: "Payment processing failed",
    details: "Transaction ID: TX-8765, Error: Gateway connection refused",
    count: 5,
  },
  {
    id: "err-4",
    timestamp: "2025-05-13T04:32:08",
    severity: "low",
    service: "Cache",
    message: "Cache miss rate high",
    details: "Current rate: 25%, Threshold: 20%",
    count: 1,
  },
  {
    id: "err-5",
    timestamp: "2025-05-13T04:15:30",
    severity: "medium",
    service: "API Server",
    message: "API rate limit exceeded",
    details: "IP: ***********, Endpoint: /api/products/bulk",
    count: 2,
  },
  {
    id: "err-6",
    timestamp: "2025-05-13T03:58:22",
    severity: "high",
    service: "Authentication",
    message: "Multiple failed login attempts",
    details: "User: <EMAIL>, IP: ************, Attempts: 5",
    count: 5,
  },
]

// Mock data for audit logs
const auditLogs = [
  {
    id: "audit-1",
    timestamp: "2025-05-13T05:40:22",
    user: "<EMAIL>",
    action: "user.permission.update",
    resource: "User ID: 54321",
    details: "Added admin role to user",
  },
  {
    id: "audit-2",
    timestamp: "2025-05-13T05:35:10",
    user: "system",
    action: "security.setting.update",
    resource: "Password Policy",
    details: "Changed minimum password length to 12 characters",
  },
  {
    id: "audit-3",
    timestamp: "2025-05-13T05:28:45",
    user: "<EMAIL>",
    action: "api.key.create",
    resource: "API Key",
    details: "Created new API key for Tenant ID: 789",
  },
  {
    id: "audit-4",
    timestamp: "2025-05-13T05:22:30",
    user: "<EMAIL>",
    action: "user.access",
    resource: "Customer Account",
    details: "Support accessed customer account ID: 456 with permission",
  },
  {
    id: "audit-5",
    timestamp: "2025-05-13T05:15:12",
    user: "<EMAIL>",
    action: "system.setting.update",
    resource: "Email Templates",
    details: "Modified password reset email template",
  },
  {
    id: "audit-6",
    timestamp: "2025-05-13T05:10:05",
    user: "system",
    action: "security.alert",
    resource: "Login System",
    details: "Blocked IP address ************ due to multiple failed login attempts",
  },
]

// Mock data for performance metrics
const performanceData = [
  { name: "00:00", apiResponseTime: 120, dbQueryTime: 45, cacheHitRate: 92 },
  { name: "01:00", apiResponseTime: 125, dbQueryTime: 48, cacheHitRate: 91 },
  { name: "02:00", apiResponseTime: 118, dbQueryTime: 42, cacheHitRate: 93 },
  { name: "03:00", apiResponseTime: 130, dbQueryTime: 50, cacheHitRate: 90 },
  { name: "04:00", apiResponseTime: 145, dbQueryTime: 55, cacheHitRate: 88 },
  { name: "05:00", apiResponseTime: 160, dbQueryTime: 60, cacheHitRate: 85 },
  { name: "06:00", apiResponseTime: 155, dbQueryTime: 58, cacheHitRate: 86 },
  { name: "07:00", apiResponseTime: 140, dbQueryTime: 52, cacheHitRate: 89 },
  { name: "08:00", apiResponseTime: 135, dbQueryTime: 50, cacheHitRate: 90 },
  { name: "09:00", apiResponseTime: 125, dbQueryTime: 45, cacheHitRate: 92 },
  { name: "10:00", apiResponseTime: 120, dbQueryTime: 42, cacheHitRate: 93 },
  { name: "11:00", apiResponseTime: 115, dbQueryTime: 40, cacheHitRate: 94 },
]

// Mock data for system health
const systemHealthData = [
  { name: "API Servers", status: "healthy", load: 42, memory: 38, disk: 55 },
  { name: "Database Cluster", status: "healthy", load: 65, memory: 72, disk: 68 },
  { name: "Cache Servers", status: "healthy", load: 35, memory: 45, disk: 30 },
  { name: "Storage Servers", status: "warning", load: 55, memory: 60, disk: 85 },
  { name: "Background Workers", status: "healthy", load: 48, memory: 52, disk: 45 },
]

export function LogsMonitoring() {
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(Date.now() - 24 * 60 * 60 * 1000),
    to: new Date(),
  })
  const [searchQuery, setSearchQuery] = useState("")
  const [logLevel, setLogLevel] = useState("all")
  const [service, setService] = useState("all")
  const [refreshing, setRefreshing] = useState(false)

  const handleRefresh = () => {
    setRefreshing(true)
    setTimeout(() => setRefreshing(false), 1000)
  }

  const getLogLevelBadge = (level: string) => {
    switch (level) {
      case "error":
        return <Badge variant="destructive">Error</Badge>
      case "warning":
        return (
          <Badge variant="warning" className="bg-yellow-500 hover:bg-yellow-600">
            Warning
          </Badge>
        )
      case "info":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
            Info
          </Badge>
        )
      case "debug":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 hover:bg-gray-100">
            Debug
          </Badge>
        )
      default:
        return <Badge variant="outline">{level}</Badge>
    }
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "critical":
        return (
          <Badge variant="destructive" className="bg-red-700 hover:bg-red-800">
            Critical
          </Badge>
        )
      case "high":
        return <Badge variant="destructive">High</Badge>
      case "medium":
        return (
          <Badge variant="warning" className="bg-yellow-500 hover:bg-yellow-600">
            Medium
          </Badge>
        )
      case "low":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
            Low
          </Badge>
        )
      default:
        return <Badge variant="outline">{severity}</Badge>
    }
  }

  const getStatusIndicator = (status: string) => {
    switch (status) {
      case "healthy":
        return (
          <div className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>Healthy
          </div>
        )
      case "warning":
        return (
          <div className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-yellow-500 mr-2"></div>Warning
          </div>
        )
      case "critical":
        return (
          <div className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-red-500 mr-2"></div>Critical
          </div>
        )
      default:
        return (
          <div className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-gray-500 mr-2"></div>
            {status}
          </div>
        )
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Logs & Monitoring</h2>
          <p className="text-muted-foreground">View system logs and monitor platform performance</p>
        </div>
        <div className="flex items-center gap-2">
          <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
          <Button size="sm" variant="outline" onClick={handleRefresh} disabled={refreshing}>
            {refreshing ? <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> : <RefreshCw className="mr-2 h-4 w-4" />}
            Refresh
          </Button>
          <Button size="sm" variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Tabs defaultValue="application">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="application">Application Logs</TabsTrigger>
          <TabsTrigger value="error">Error Logs</TabsTrigger>
          <TabsTrigger value="audit">Audit Logs</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="monitoring">Real-time Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="application" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Application Logs</CardTitle>
              <CardDescription>View and filter application logs across all services</CardDescription>
              <div className="flex flex-wrap items-center gap-3 pt-3">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search logs..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
                <Select value={logLevel} onValueChange={setLogLevel}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Log Level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="error">Error</SelectItem>
                    <SelectItem value="warning">Warning</SelectItem>
                    <SelectItem value="info">Info</SelectItem>
                    <SelectItem value="debug">Debug</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={service} onValueChange={setService}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Service" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Services</SelectItem>
                    <SelectItem value="api">API Server</SelectItem>
                    <SelectItem value="database">Database</SelectItem>
                    <SelectItem value="cache">Cache</SelectItem>
                    <SelectItem value="storage">Storage</SelectItem>
                    <SelectItem value="payment">Payment Gateway</SelectItem>
                    <SelectItem value="email">Email Service</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon">
                  <ArrowDownUp className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    setSearchQuery("")
                    setLogLevel("all")
                    setService("all")
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[180px]">Timestamp</TableHead>
                    <TableHead className="w-[100px]">Level</TableHead>
                    <TableHead className="w-[150px]">Service</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead className="text-right">Details</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {applicationLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="font-mono text-xs">{new Date(log.timestamp).toLocaleString()}</TableCell>
                      <TableCell>{getLogLevelBadge(log.level)}</TableCell>
                      <TableCell>{log.service}</TableCell>
                      <TableCell>{log.message}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <FileText className="mr-2 h-4 w-4" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="error" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Error Logs</CardTitle>
              <CardDescription>View and analyze system errors with severity indicators</CardDescription>
              <div className="flex flex-wrap items-center gap-3 pt-3">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Search errors..." className="pl-8" />
                  </div>
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severities</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Service" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Services</SelectItem>
                    <SelectItem value="api">API Server</SelectItem>
                    <SelectItem value="database">Database</SelectItem>
                    <SelectItem value="cache">Cache</SelectItem>
                    <SelectItem value="storage">Storage</SelectItem>
                    <SelectItem value="payment">Payment Gateway</SelectItem>
                    <SelectItem value="auth">Authentication</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[180px]">Timestamp</TableHead>
                    <TableHead className="w-[100px]">Severity</TableHead>
                    <TableHead className="w-[150px]">Service</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead className="w-[80px] text-center">Count</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {errorLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="font-mono text-xs">{new Date(log.timestamp).toLocaleString()}</TableCell>
                      <TableCell>{getSeverityBadge(log.severity)}</TableCell>
                      <TableCell>{log.service}</TableCell>
                      <TableCell>{log.message}</TableCell>
                      <TableCell className="text-center">
                        <Badge variant="outline">{log.count}</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <FileText className="mr-2 h-4 w-4" />
                          Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Audit Logs</CardTitle>
              <CardDescription>Security and access audit trail for compliance and security monitoring</CardDescription>
              <div className="flex flex-wrap items-center gap-3 pt-3">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Search audit logs..." className="pl-8" />
                  </div>
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Action Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Actions</SelectItem>
                    <SelectItem value="user">User Actions</SelectItem>
                    <SelectItem value="security">Security Events</SelectItem>
                    <SelectItem value="system">System Changes</SelectItem>
                    <SelectItem value="api">API Access</SelectItem>
                  </SelectContent>
                </Select>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="User" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Users</SelectItem>
                    <SelectItem value="admin">Admin Users</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                    <SelectItem value="support">Support Team</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[180px]">Timestamp</TableHead>
                    <TableHead className="w-[150px]">User</TableHead>
                    <TableHead className="w-[180px]">Action</TableHead>
                    <TableHead className="w-[180px]">Resource</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="font-mono text-xs">{new Date(log.timestamp).toLocaleString()}</TableCell>
                      <TableCell>{log.user}</TableCell>
                      <TableCell>
                        <span className="font-mono text-xs">{log.action}</span>
                      </TableCell>
                      <TableCell>{log.resource}</TableCell>
                      <TableCell className="max-w-[300px] truncate">{log.details}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <FileText className="mr-2 h-4 w-4" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>API Response Time</CardTitle>
                <CardDescription>Average response time in milliseconds</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <AnalyticsTimeSeriesChart
                  data={performanceData}
                  index="name"
                  categories={["apiResponseTime"]}
                  colors={["#3b82f6"]}
                  valueFormatter={(value) => `${value}ms`}
                  showLegend={false}
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Database Query Time</CardTitle>
                <CardDescription>Average query execution time in milliseconds</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <AnalyticsTimeSeriesChart
                  data={performanceData}
                  index="name"
                  categories={["dbQueryTime"]}
                  colors={["#10b981"]}
                  valueFormatter={(value) => `${value}ms`}
                  showLegend={false}
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Cache Hit Rate</CardTitle>
                <CardDescription>Percentage of cache hits vs. misses</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <AnalyticsTimeSeriesChart
                  data={performanceData}
                  index="name"
                  categories={["cacheHitRate"]}
                  colors={["#f59e0b"]}
                  valueFormatter={(value) => `${value}%`}
                  showLegend={false}
                />
              </CardContent>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>Key performance indicators across all services</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Avg. Response Time</span>
                    <span className="text-sm font-medium">128ms</span>
                  </div>
                  <Progress value={64} className="h-2" />
                  <p className="text-xs text-muted-foreground">Target: &lt;200ms</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Throughput</span>
                    <span className="text-sm font-medium">1,250 req/s</span>
                  </div>
                  <Progress value={78} className="h-2" />
                  <p className="text-xs text-muted-foreground">Target: &gt;1,000 req/s</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Error Rate</span>
                    <span className="text-sm font-medium">0.05%</span>
                  </div>
                  <Progress value={5} className="h-2" />
                  <p className="text-xs text-muted-foreground">Target: &lt;0.1%</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Cache Hit Rate</span>
                    <span className="text-sm font-medium">92%</span>
                  </div>
                  <Progress value={92} className="h-2" />
                  <p className="text-xs text-muted-foreground">Target: &gt;90%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">System Status</CardTitle>
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Operational</div>
                <p className="text-xs text-muted-foreground">All systems normal</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,248</div>
                <p className="text-xs text-muted-foreground">+12% from average</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Request Rate</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,156/s</div>
                <p className="text-xs text-muted-foreground">Last minute average</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0.03%</div>
                <p className="text-xs text-muted-foreground">Last minute average</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>System Health</CardTitle>
              <CardDescription>Real-time health status of all system components</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {systemHealthData.map((service, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Terminal className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{service.name}</span>
                      </div>
                      <div>{getStatusIndicator(service.status)}</div>
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">CPU</span>
                          <span>{service.load}%</span>
                        </div>
                        <Progress
                          value={service.load}
                          className={
                            service.load > 80 ? "bg-red-500" : service.load > 60 ? "bg-yellow-500" : "bg-green-500"
                          }
                        />
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Memory</span>
                          <span>{service.memory}%</span>
                        </div>
                        <Progress
                          value={service.memory}
                          className={
                            service.memory > 80 ? "bg-red-500" : service.memory > 60 ? "bg-yellow-500" : "bg-green-500"
                          }
                        />
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Disk</span>
                          <span>{service.disk}%</span>
                        </div>
                        <Progress
                          value={service.disk}
                          className={
                            service.disk > 80 ? "bg-red-500" : service.disk > 60 ? "bg-yellow-500" : "bg-green-500"
                          }
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Active Alerts</CardTitle>
              <CardDescription>Current system alerts requiring attention</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="rounded-md bg-yellow-50 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <AlertTriangle className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">Storage Server Disk Space Warning</h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>Storage server disk usage is at 85%, approaching the 90% threshold.</p>
                      </div>
                      <div className="mt-4">
                        <div className="-mx-2 -my-1.5 flex">
                          <Button
                            variant="outline"
                            size="sm"
                            className="mx-2 my-1.5 border-yellow-300 bg-yellow-50 text-yellow-800 hover:bg-yellow-100"
                          >
                            View Details
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mx-2 my-1.5 border-yellow-300 bg-yellow-50 text-yellow-800 hover:bg-yellow-100"
                          >
                            Dismiss
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="rounded-md bg-blue-50 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Clock className="h-5 w-5 text-blue-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-blue-800">Scheduled Maintenance</h3>
                      <div className="mt-2 text-sm text-blue-700">
                        <p>Database optimization scheduled for May 15, 2025, 02:00 - 04:00 UTC.</p>
                      </div>
                      <div className="mt-4">
                        <div className="-mx-2 -my-1.5 flex">
                          <Button
                            variant="outline"
                            size="sm"
                            className="mx-2 my-1.5 border-blue-300 bg-blue-50 text-blue-800 hover:bg-blue-100"
                          >
                            View Details
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
