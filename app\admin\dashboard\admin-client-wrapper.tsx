"use client"

import type React from "react"
import { NotificationsProvider } from "@/components/providers/notifications-provider"
import { ErrorBoundary } from "@/components/error-boundary"

interface AdminClientWrapperProps {
  children: React.ReactNode
}

export function AdminClientWrapper({ children }: AdminClientWrapperProps) {
  return (
    <ErrorBoundary>
      <NotificationsProvider>{children}</NotificationsProvider>
    </ErrorBoundary>
  )
}
