"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, CheckCircle2, Copy, ExternalLink, Key, RefreshCw, Search, Settings, Webhook } from "lucide-react"
import { cn } from "@/lib/utils"

// Mock data for integrations
const integrations = [
  {
    id: "payment-stripe",
    name: "Stripe",
    category: "Payment",
    description: "Process credit card payments and manage subscriptions",
    status: "connected",
    lastSync: "2023-05-15T10:30:00Z",
    icon: "/stripe-logo.svg",
  },
  {
    id: "payment-paypal",
    name: "PayPal",
    category: "Payment",
    description: "Accept PayPal payments and process refunds",
    status: "connected",
    lastSync: "2023-05-14T08:45:00Z",
    icon: "/paypal-logo.svg",
  },
  {
    id: "shipping-shippo",
    name: "Shippo",
    category: "Shipping",
    description: "Print labels, track shipments, and manage carriers",
    status: "connected",
    lastSync: "2023-05-13T14:20:00Z",
    icon: "/shippo-logo.svg",
  },
  {
    id: "marketing-mailchimp",
    name: "Mailchimp",
    category: "Marketing",
    description: "Email marketing, automation, and campaign management",
    status: "disconnected",
    lastSync: null,
    icon: "/mailchimp-logo.svg",
  },
  {
    id: "analytics-google",
    name: "Google Analytics",
    category: "Analytics",
    description: "Track website traffic and user behavior",
    status: "error",
    lastSync: "2023-05-10T09:15:00Z",
    icon: "/google-analytics-logo.svg",
  },
  {
    id: "social-facebook",
    name: "Facebook",
    category: "Social",
    description: "Publish products to Facebook and Instagram shops",
    status: "connected",
    lastSync: "2023-05-12T11:30:00Z",
    icon: "/facebook-logo.svg",
  },
  {
    id: "tax-avalara",
    name: "Avalara",
    category: "Tax",
    description: "Automated tax calculation and compliance",
    status: "pending",
    lastSync: null,
    icon: "/avalara-logo.svg",
  },
  {
    id: "crm-salesforce",
    name: "Salesforce",
    category: "CRM",
    description: "Customer relationship management and sales automation",
    status: "disconnected",
    lastSync: null,
    icon: "/salesforce-logo.svg",
  },
]

// Mock data for webhooks
const webhooks = [
  {
    id: "webhook-1",
    name: "Order Created",
    url: "https://example.com/webhooks/orders/created",
    events: ["order.created"],
    status: "active",
    lastTriggered: "2023-05-15T08:30:00Z",
    successRate: 98,
  },
  {
    id: "webhook-2",
    name: "Order Updated",
    url: "https://example.com/webhooks/orders/updated",
    events: ["order.updated", "order.fulfilled", "order.cancelled"],
    status: "active",
    lastTriggered: "2023-05-14T15:45:00Z",
    successRate: 100,
  },
  {
    id: "webhook-3",
    name: "Product Inventory",
    url: "https://example.com/webhooks/products/inventory",
    events: ["product.created", "product.updated", "inventory.updated"],
    status: "inactive",
    lastTriggered: "2023-05-10T11:20:00Z",
    successRate: 92,
  },
  {
    id: "webhook-4",
    name: "Customer Events",
    url: "https://example.com/webhooks/customers",
    events: ["customer.created", "customer.updated"],
    status: "error",
    lastTriggered: "2023-05-13T09:15:00Z",
    successRate: 45,
  },
]

// Mock data for API keys
const apiKeys = [
  {
    id: "key-1",
    name: "Production API Key",
    key: "sk_prod_*****************************a4b2",
    created: "2023-01-15T10:30:00Z",
    lastUsed: "2023-05-15T08:45:00Z",
    permissions: ["read", "write"],
    status: "active",
  },
  {
    id: "key-2",
    name: "Development API Key",
    key: "sk_dev_*****************************c6d3",
    created: "2023-02-20T14:15:00Z",
    lastUsed: "2023-05-14T16:30:00Z",
    permissions: ["read", "write"],
    status: "active",
  },
  {
    id: "key-3",
    name: "Analytics Read-Only",
    key: "sk_analytics_***********************e8f4",
    created: "2023-03-10T09:45:00Z",
    lastUsed: "2023-05-12T11:20:00Z",
    permissions: ["read"],
    status: "active",
  },
  {
    id: "key-4",
    name: "Legacy API Key",
    key: "sk_legacy_*************************g2h5",
    created: "2022-11-05T08:30:00Z",
    lastUsed: "2023-04-30T15:10:00Z",
    permissions: ["read", "write"],
    status: "revoked",
  },
]

// Mock data for OAuth connections
const oauthConnections = [
  {
    id: "oauth-1",
    name: "Google",
    connectedAccounts: 12,
    scopes: ["profile", "email", "analytics.read"],
    status: "active",
    lastUpdated: "2023-04-15T10:30:00Z",
  },
  {
    id: "oauth-2",
    name: "Facebook",
    connectedAccounts: 8,
    scopes: ["public_profile", "email", "pages_manage_posts"],
    status: "active",
    lastUpdated: "2023-04-20T14:15:00Z",
  },
  {
    id: "oauth-3",
    name: "Twitter",
    connectedAccounts: 5,
    scopes: ["tweet.read", "tweet.write", "users.read"],
    status: "inactive",
    lastUpdated: "2023-03-10T09:45:00Z",
  },
  {
    id: "oauth-4",
    name: "Microsoft",
    connectedAccounts: 3,
    scopes: ["user.read", "mail.read"],
    status: "active",
    lastUpdated: "2023-05-05T08:30:00Z",
  },
]

export function IntegrationHub() {
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")

  // Filter integrations based on search query, category, and status
  const filteredIntegrations = integrations.filter((integration) => {
    const matchesSearch =
      integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      integration.description.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory =
      categoryFilter === "all" || integration.category.toLowerCase() === categoryFilter.toLowerCase()

    const matchesStatus = statusFilter === "all" || integration.status === statusFilter

    return matchesSearch && matchesCategory && matchesStatus
  })

  // Get unique categories for filter
  const categories = ["all", ...new Set(integrations.map((i) => i.category.toLowerCase()))]

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Never"
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      dateStyle: "medium",
      timeStyle: "short",
    }).format(date)
  }

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "connected":
        return <Badge className="bg-green-500">Connected</Badge>
      case "disconnected":
        return <Badge variant="outline">Disconnected</Badge>
      case "error":
        return <Badge variant="destructive">Error</Badge>
      case "pending":
        return <Badge variant="secondary">Pending</Badge>
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "inactive":
        return <Badge variant="outline">Inactive</Badge>
      case "revoked":
        return <Badge variant="destructive">Revoked</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <Tabs defaultValue="directory" className="w-full">
      <TabsList className="grid w-full grid-cols-4 mb-6">
        <TabsTrigger value="directory">Integration Directory</TabsTrigger>
        <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
        <TabsTrigger value="api-keys">API Keys</TabsTrigger>
        <TabsTrigger value="oauth">OAuth Connections</TabsTrigger>
      </TabsList>

      {/* Integration Directory Tab */}
      <TabsContent value="directory" className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="relative w-full md:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search integrations..."
              className="w-full pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex flex-wrap gap-2">
            <div>
              <Label htmlFor="category-filter" className="sr-only">
                Filter by Category
              </Label>
              <select
                id="category-filter"
                className="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
              >
                <option value="all">All Categories</option>
                {categories
                  .filter((c) => c !== "all")
                  .map((category) => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
              </select>
            </div>
            <div>
              <Label htmlFor="status-filter" className="sr-only">
                Filter by Status
              </Label>
              <select
                id="status-filter"
                className="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="connected">Connected</option>
                <option value="disconnected">Disconnected</option>
                <option value="error">Error</option>
                <option value="pending">Pending</option>
              </select>
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                setSearchQuery("")
                setCategoryFilter("all")
                setStatusFilter("all")
              }}
            >
              <RefreshCw className="h-4 w-4" />
              <span className="sr-only">Reset filters</span>
            </Button>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredIntegrations.length > 0 ? (
            filteredIntegrations.map((integration) => (
              <Card key={integration.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-md border bg-muted">
                        {/* Placeholder for integration icon */}
                        <span className="text-xs font-semibold">{integration.name.substring(0, 2)}</span>
                      </div>
                      <div>
                        <CardTitle className="text-base">{integration.name}</CardTitle>
                        <CardDescription className="text-xs">{integration.category}</CardDescription>
                      </div>
                    </div>
                    {getStatusBadge(integration.status)}
                  </div>
                </CardHeader>
                <CardContent className="pb-3">
                  <p className="text-sm text-muted-foreground">{integration.description}</p>
                  {integration.lastSync && (
                    <p className="mt-2 text-xs text-muted-foreground">
                      Last synced: {formatDate(integration.lastSync)}
                    </p>
                  )}
                </CardContent>
                <CardFooter className="pt-3 border-t flex justify-between">
                  <Button variant={integration.status === "connected" ? "outline" : "default"} size="sm">
                    {integration.status === "connected" ? "Disconnect" : "Connect"}
                  </Button>
                  {integration.status === "connected" && (
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4 mr-2" />
                      Configure
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))
          ) : (
            <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
              <div className="rounded-full bg-muted p-3">
                <Search className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="mt-4 text-lg font-semibold">No integrations found</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Try adjusting your search or filters to find what you're looking for.
              </p>
            </div>
          )}
        </div>
      </TabsContent>

      {/* Webhooks Tab */}
      <TabsContent value="webhooks" className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Webhook Endpoints</h3>
          <Button>
            <Webhook className="h-4 w-4 mr-2" />
            Add Webhook
          </Button>
        </div>

        <div className="rounded-md border">
          <div className="relative w-full overflow-auto">
            <table className="w-full caption-bottom text-sm">
              <thead>
                <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                  <th className="h-12 px-4 text-left align-middle font-medium">Name</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">URL</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Events</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Last Triggered</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Success Rate</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {webhooks.map((webhook) => (
                  <tr
                    key={webhook.id}
                    className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                  >
                    <td className="p-4 align-middle">{webhook.name}</td>
                    <td className="p-4 align-middle">
                      <div className="flex items-center">
                        <span className="truncate max-w-[200px]">{webhook.url}</span>
                        <Button variant="ghost" size="icon" className="h-8 w-8 ml-1">
                          <Copy className="h-4 w-4" />
                          <span className="sr-only">Copy URL</span>
                        </Button>
                      </div>
                    </td>
                    <td className="p-4 align-middle">
                      <div className="flex flex-wrap gap-1">
                        {webhook.events.map((event) => (
                          <Badge key={event} variant="outline" className="text-xs">
                            {event}
                          </Badge>
                        ))}
                      </div>
                    </td>
                    <td className="p-4 align-middle">{getStatusBadge(webhook.status)}</td>
                    <td className="p-4 align-middle">{formatDate(webhook.lastTriggered)}</td>
                    <td className="p-4 align-middle">
                      <div className="flex items-center gap-2">
                        <div
                          className={cn(
                            "h-2 w-16 rounded-full",
                            webhook.successRate > 90
                              ? "bg-green-500"
                              : webhook.successRate > 75
                                ? "bg-yellow-500"
                                : "bg-red-500",
                          )}
                        >
                          <div
                            className="h-full rounded-full bg-primary"
                            style={{ width: `${webhook.successRate}%` }}
                          />
                        </div>
                        <span className="text-xs">{webhook.successRate}%</span>
                      </div>
                    </td>
                    <td className="p-4 align-middle">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Settings className="h-4 w-4" />
                          <span className="sr-only">Edit webhook</span>
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8 text-red-500">
                          {webhook.status === "active" ? (
                            <>
                              <AlertCircle className="h-4 w-4" />
                              <span className="sr-only">Disable webhook</span>
                            </>
                          ) : (
                            <>
                              <CheckCircle2 className="h-4 w-4" />
                              <span className="sr-only">Enable webhook</span>
                            </>
                          )}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </TabsContent>

      {/* API Keys Tab */}
      <TabsContent value="api-keys" className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">API Keys</h3>
          <Button>
            <Key className="h-4 w-4 mr-2" />
            Generate New Key
          </Button>
        </div>

        <div className="rounded-md border">
          <div className="relative w-full overflow-auto">
            <table className="w-full caption-bottom text-sm">
              <thead>
                <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                  <th className="h-12 px-4 text-left align-middle font-medium">Name</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Key</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Created</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Last Used</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Permissions</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {apiKeys.map((apiKey) => (
                  <tr
                    key={apiKey.id}
                    className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                  >
                    <td className="p-4 align-middle">{apiKey.name}</td>
                    <td className="p-4 align-middle">
                      <div className="flex items-center">
                        <code className="rounded bg-muted px-1 py-0.5 text-xs font-mono">{apiKey.key}</code>
                        <Button variant="ghost" size="icon" className="h-8 w-8 ml-1">
                          <Copy className="h-4 w-4" />
                          <span className="sr-only">Copy API key</span>
                        </Button>
                      </div>
                    </td>
                    <td className="p-4 align-middle">{formatDate(apiKey.created)}</td>
                    <td className="p-4 align-middle">{formatDate(apiKey.lastUsed)}</td>
                    <td className="p-4 align-middle">
                      <div className="flex flex-wrap gap-1">
                        {apiKey.permissions.map((permission) => (
                          <Badge key={permission} variant="outline" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                      </div>
                    </td>
                    <td className="p-4 align-middle">{getStatusBadge(apiKey.status)}</td>
                    <td className="p-4 align-middle">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Settings className="h-4 w-4" />
                          <span className="sr-only">Edit API key</span>
                        </Button>
                        {apiKey.status === "active" ? (
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-red-500">
                            <AlertCircle className="h-4 w-4" />
                            <span className="sr-only">Revoke API key</span>
                          </Button>
                        ) : (
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-green-500">
                            <CheckCircle2 className="h-4 w-4" />
                            <span className="sr-only">Activate API key</span>
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </TabsContent>

      {/* OAuth Connections Tab */}
      <TabsContent value="oauth" className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">OAuth Connections</h3>
          <Button>
            <ExternalLink className="h-4 w-4 mr-2" />
            Add OAuth Provider
          </Button>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          {oauthConnections.map((connection) => (
            <Card key={connection.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-md border bg-muted">
                      {/* Placeholder for OAuth provider icon */}
                      <span className="text-xs font-semibold">{connection.name.substring(0, 2)}</span>
                    </div>
                    <div>
                      <CardTitle className="text-base">{connection.name}</CardTitle>
                      <CardDescription className="text-xs">
                        {connection.connectedAccounts} connected accounts
                      </CardDescription>
                    </div>
                  </div>
                  {getStatusBadge(connection.status)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h4 className="text-sm font-medium">Scopes</h4>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {connection.scopes.map((scope) => (
                        <Badge key={scope} variant="outline" className="text-xs">
                          {scope}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Last Updated</h4>
                    <p className="text-sm text-muted-foreground">{formatDate(connection.lastUpdated)}</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-between">
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </Button>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Enabled</span>
                  <Switch checked={connection.status === "active"} />
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </TabsContent>
    </Tabs>
  )
}
