"use client"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { AlertCircle, CheckCircle2, Clock, ExternalLink, HelpCircle, MessageSquare, Phone, Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"

// Dummy data untuk tickets
const tickets = [
  {
    id: "T-1234",
    title: "Pembayaran tidak diproses",
    status: "open",
    priority: "high",
    created: "2 jam yang lalu",
    lastUpdate: "30 menit yang lalu",
  },
  {
    id: "T-1233",
    title: "Pertanyaan tentang pengiriman",
    status: "waiting",
    priority: "medium",
    created: "1 hari yang lalu",
    lastUpdate: "5 jam yang lalu",
  },
  {
    id: "T-1232",
    title: "Produk tidak sesuai deskripsi",
    status: "closed",
    priority: "medium",
    created: "5 hari yang lalu",
    lastUpdate: "2 hari yang lalu",
  },
]

// Dummy data untuk interaksi terbaru
const recentInteractions = [
  {
    id: 1,
    type: "ticket",
    title: "Pembayaran tidak diproses",
    time: "2 jam yang lalu",
    status: "open",
  },
  {
    id: 2,
    type: "chat",
    title: "Chat dengan CS Sellzio",
    time: "1 hari yang lalu",
    status: "closed",
  },
  {
    id: 3,
    type: "email",
    title: "Konfirmasi pengembalian dana",
    time: "3 hari yang lalu",
    status: "closed",
  },
]

// Dummy data untuk artikel bantuan
const helpArticles = [
  {
    id: 1,
    title: "Cara menambahkan produk baru",
    category: "Seller Guide",
    views: 1245,
    url: "/buyer/dashboard/customer-service/help-center",
  },
  {
    id: 2,
    title: "Panduan pengembalian barang",
    category: "Buyer Guide",
    views: 987,
    url: "/buyer/dashboard/customer-service/help-center",
  },
  {
    id: 3,
    title: "Cara mengatur pengiriman",
    category: "Seller Guide",
    views: 756,
    url: "/buyer/dashboard/customer-service/help-center",
  },
  {
    id: 4,
    title: "Mengatasi masalah pembayaran",
    category: "Payment",
    views: 1432,
    url: "/buyer/dashboard/customer-service/help-center",
  },
]

export function SupportDashboard() {
  const [searchQuery, setSearchQuery] = useState("")

  // Status badge renderer
  const renderStatusBadge = (status) => {
    switch (status) {
      case "open":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
            Aktif
          </Badge>
        )
      case "waiting":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
            Menunggu
          </Badge>
        )
      case "closed":
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
            Selesai
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Priority badge renderer
  const renderPriorityBadge = (priority) => {
    switch (priority) {
      case "high":
        return <Badge variant="destructive">Tinggi</Badge>
      case "medium":
        return <Badge variant="secondary">Sedang</Badge>
      case "low":
        return <Badge variant="outline">Rendah</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Customer Service</h2>
          <p className="text-muted-foreground">Dapatkan bantuan dan dukungan untuk akun SellZio Anda</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Phone className="mr-2 h-4 w-4" />
            Hubungi Kami
          </Button>
          <Button size="sm">
            <MessageSquare className="mr-2 h-4 w-4" />
            Live Chat
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tiket</CardTitle>
            <HelpCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">+0% dari bulan lalu</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tiket Aktif</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1</div>
            <p className="text-xs text-muted-foreground">-50% dari bulan lalu</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Menunggu Respon</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1</div>
            <p className="text-xs text-muted-foreground">+0% dari bulan lalu</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tiket Selesai</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1</div>
            <p className="text-xs text-muted-foreground">+100% dari bulan lalu</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>Tiket Dukungan</CardTitle>
            <CardDescription>Lihat dan kelola tiket dukungan Anda</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {tickets.map((ticket) => (
                <div key={ticket.id} className="flex items-center justify-between rounded-lg border p-3">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{ticket.id}</span>
                      {renderStatusBadge(ticket.status)}
                      {renderPriorityBadge(ticket.priority)}
                    </div>
                    <p className="text-sm">{ticket.title}</p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>Dibuat: {ticket.created}</span>
                      <span>•</span>
                      <span>Update: {ticket.lastUpdate}</span>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={`/buyer/dashboard/customer-service/tickets`}>Lihat</Link>
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/buyer/dashboard/customer-service/tickets">Lihat Semua Tiket</Link>
            </Button>
          </CardFooter>
        </Card>
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Bantuan Cepat</CardTitle>
            <CardDescription>Artikel bantuan yang mungkin berguna untuk Anda</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari artikel bantuan..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              {helpArticles.map((article) => (
                <div key={article.id} className="flex items-center justify-between rounded-lg border p-3">
                  <div className="space-y-1">
                    <p className="font-medium">{article.title}</p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Badge variant="secondary" className="text-xs">
                        {article.category}
                      </Badge>
                      <span>{article.views} dilihat</span>
                    </div>
                  </div>
                  <Button variant="ghost" size="icon" asChild>
                    <Link href={article.url}>
                      <ExternalLink className="h-4 w-4" />
                      <span className="sr-only">Buka artikel</span>
                    </Link>
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/buyer/dashboard/customer-service/help-center">Buka Help Center</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>Interaksi Terbaru</CardTitle>
            <CardDescription>Riwayat interaksi Anda dengan tim dukungan</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentInteractions.map((interaction) => (
                <div key={interaction.id} className="flex items-center justify-between rounded-lg border p-3">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-9 w-9">
                      <AvatarImage src="/placeholder.svg" alt="Avatar" />
                      <AvatarFallback>CS</AvatarFallback>
                    </Avatar>
                    <div className="space-y-1">
                      <p className="font-medium">{interaction.title}</p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>Via {interaction.type}</span>
                        <span>•</span>
                        <span>{interaction.time}</span>
                        {renderStatusBadge(interaction.status)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Kepuasan Pelanggan</CardTitle>
            <CardDescription>Berdasarkan interaksi dukungan Anda</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Kecepatan Respon</span>
                <span className="text-sm font-medium">85%</span>
              </div>
              <Progress value={85} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Kualitas Solusi</span>
                <span className="text-sm font-medium">92%</span>
              </div>
              <Progress value={92} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Kepuasan Keseluruhan</span>
                <span className="text-sm font-medium">90%</span>
              </div>
              <Progress value={90} className="h-2" />
            </div>
            <div className="pt-4">
              <div className="rounded-lg bg-muted p-4">
                <h4 className="mb-2 font-medium">Butuh bantuan segera?</h4>
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/buyer/dashboard/customer-service/contact">Kontak Kami</Link>
                  </Button>
                  <Button size="sm">Live Chat</Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
