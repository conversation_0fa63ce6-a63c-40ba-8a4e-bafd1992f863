import { useState, useEffect } from "react"
import { X, Filter } from "lucide-react"
import "./sellzio-styles.css"

interface FacetData {
  categories: Record<string, number>
  priceRanges: Record<string, number>
  ratings: Record<string, number>
  shipping: Record<string, number>
  features: Record<string, number>
}

interface ActiveFilters {
  categories?: string[]
  priceRanges?: string[]
  ratings?: string[]
  shipping?: string[]
  features?: string[]
  // Tambahkan properti untuk facet filter
  kategori?: string[]
  'rentang harga'?: string[]
  rating?: string[]
  pengiriman?: string[]
  fitur?: string[]
}

interface SellzioFacetProps {
  searchResults: any[]
  activeFilters: ActiveFilters
  onFiltersChange: (filters: ActiveFilters) => void
  isVisible: boolean
  onClose: () => void
  isDesktopSidebar?: boolean
  allProducts?: any[] // Add this to access all products for counting
  subcategoryContext?: {
    category: string
    selectedSubcategory: string
    allSubcategories: Array<{
      id: string
      name: string
      icon?: string
      color?: string
    }>
  } | null
}

export function SellzioFacet({
  searchResults,
  activeFilters,
  onFiltersChange,
  isVisible,
  onClose,
  isDesktopSidebar = false,
  allProducts = [],
  subcategoryContext
}: SellzioFacetProps) {
  const [tempFilters, setTempFilters] = useState<ActiveFilters>(activeFilters)
  const [facetData, setFacetData] = useState<FacetData>({
    categories: {},
    priceRanges: {},
    ratings: {},
    shipping: {},
    features: {}
  })
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)

  // Check screen size
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      setIsMobile(width < 768) // Mobile: < 768px
      setIsTablet(width >= 768 && width < 1025) // Tablet: 768px - 1024px
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Extract facets from search results
  useEffect(() => {
    console.log('Facet data useEffect triggered - searchResults:', searchResults.length, 'subcategoryContext:', subcategoryContext);
    const facets = extractFacets(searchResults)
    setFacetData(facets)
    console.log('Set facet data:', facets);
  }, [searchResults, subcategoryContext, tempFilters])

  // State to track if auto-check has been applied
  const [autoCheckApplied, setAutoCheckApplied] = useState(false);

  // Reset temp filters when activeFilters change
  useEffect(() => {
    const context = subcategoryContext || (window as any).subcategoryContext;
    console.log('Facet useEffect - context:', context);
    let newFilters = { ...activeFilters };

    // Auto-check selected subcategory and main category if context exists and not already applied
    if (context && context.selectedSubcategory && context.category && !autoCheckApplied) {
      console.log('Auto-checking subcategory:', context.selectedSubcategory);
      console.log('Auto-checking category:', context.category);

      if (!newFilters.kategori) newFilters.kategori = [];

      // Add main category if not already included
      if (!newFilters.kategori.includes(context.category)) {
        newFilters.kategori.push(context.category);
      }

      // Add selected subcategory if not already included
      if (!newFilters.kategori.includes(context.selectedSubcategory)) {
        newFilters.kategori.push(context.selectedSubcategory);
      }

      setAutoCheckApplied(true);
    }

    setTempFilters(newFilters);
  }, [activeFilters, subcategoryContext, autoCheckApplied])

  // Reset auto-check flag when subcategory context changes
  useEffect(() => {
    setAutoCheckApplied(false);
  }, [subcategoryContext])

  const extractFacets = (results: any[]): FacetData => {
    // Use subcategory context from props or window
    const context = subcategoryContext || (window as any).subcategoryContext;
    console.log('🔥 FACET DEBUG: extractFacets called');
    console.log('🔥 FACET DEBUG: results length:', results.length);
    console.log('🔥 FACET DEBUG: allProducts length:', allProducts.length);
    console.log('🔥 FACET DEBUG: context:', context);

    // HARDCODE DATA UNTUK DEBUG - pastikan data pasti ada
    const hardcodedProducts = [
      { id: 1, name: "PlayStation 5", price: "Rp 7.999.000", category: "Konsol Game" },
      { id: 2, name: "Xbox Series X", price: "Rp 7.499.000", category: "Konsol Game" },
      { id: 3, name: "Nintendo Switch OLED", price: "Rp 4.999.000", category: "Konsol Game" },
      { id: 4, name: "Steam Deck", price: "Rp 8.999.000", category: "Konsol Game" },
      { id: 5, name: "PlayStation 4 Pro", price: "Rp 4.299.000", category: "Konsol Game" },
      { id: 6, name: "DualSense Controller", price: "Rp 899.000", category: "Aksesoris Konsol" },
      { id: 7, name: "Xbox Wireless Controller", price: "Rp 799.000", category: "Aksesoris Konsol" },
      { id: 8, name: "Pro Controller Nintendo", price: "Rp 699.000", category: "Aksesoris Konsol" },
      { id: 9, name: "Gaming Headset", price: "Rp 1.299.000", category: "Aksesoris Konsol" },
      { id: 10, name: "Charging Station", price: "Rp 299.000", category: "Aksesoris Konsol" }
    ];
    console.log('🔥 FACET DEBUG: Using hardcoded products:', hardcodedProducts.length);

    // Check if any subcategory is selected in current filters
    const hasSubcategorySelected = context && context.allSubcategories?.some((sub: any) =>
      tempFilters.kategori?.includes(sub.name)
    );

    // Always show all subcategories if we have context
    const shouldShowAllSubcategories = context && context.allSubcategories;

    // Jika tidak ada hasil atau dalam konteks subkategori, berikan data sample untuk demo
    if (shouldShowAllSubcategories) {
      // If we have subcategory context, use it for categories
      let categories = {
        "Handphone & Tablet": 45,
        "Elektronik": 32,
        "Fashion Pria": 28,
        "Fashion Wanita": 41,
        "Tas & Travel": 19,
        "Sepatu": 23,
        "Aksesoris Fashion": 15
      };

      // Override with subcategory data if available
      if (context && context.allSubcategories && context.allSubcategories.length > 0) {
        console.log('🔥 FACET: Creating categories from subcategories:', context.allSubcategories);
        console.log('🔥 FACET: Selected subcategory:', context.selectedSubcategory);
        console.log('🔥 FACET: Category:', context.category);
        console.log('🔥 FACET: All subcategories count:', context.allSubcategories.length);
        console.log('🔥 FACET: Has subcategory selected:', hasSubcategorySelected);

        const dynamicCategories: { [key: string]: number } = {};

        // Calculate actual product counts for each subcategory based on ALL products (not filtered results)
        let totalCategoryCount = 0;
        context.allSubcategories.forEach((sub: any) => {
          // FIXED: Use search results instead of hardcoded data to match displayed products
          const productsToCount = searchResults;
          console.log(`🔍 FACET DEBUG: Subcategory "${sub.name}" - using SEARCH RESULTS:`, productsToCount.length);
          console.log(`🔍 FACET DEBUG: allProducts length:`, allProducts.length);
          console.log(`🔍 FACET DEBUG: searchResults length:`, searchResults.length);

          const subcategoryCount = productsToCount.filter((product: any) => {
            const subName = sub.name.toLowerCase();
            const productName = product.name?.toLowerCase() || '';
            const productCategory = product.category?.toLowerCase() || '';

            // FIXED: Use exact category matching instead of includes to prevent cross-matching
            if (productCategory === subName) return true;

            // Enhanced matching for specific subcategories - FIXED: More specific matching
            if (subName === 'konsol game') {
              // Only match console products for "Konsol Game"
              if (productName.includes('playstation') || productName.includes('ps') ||
                  productName.includes('xbox') || productName.includes('nintendo') ||
                  productName.includes('steam') || productName.includes('switch')) return true;
            }

            if (subName === 'aksesoris konsol') {
              // Only match accessory products for "Aksesoris Konsol"
              if (productName.includes('controller') || productName.includes('headset') ||
                  productName.includes('mouse') || productName.includes('keyboard') ||
                  productName.includes('gamepad') || productName.includes('joystick') ||
                  productName.includes('charging')) return true;
            }

            if (subName.includes('casing') || subName.includes('case')) {
              if (productName.includes('case') || productName.includes('casing') ||
                  productName.includes('cover') || productName.includes('housing')) return true;
            }

            if (subName.includes('foot bath') || subName.includes('spa')) {
              if (productName.includes('foot') || productName.includes('spa') ||
                  productName.includes('bath') || productName.includes('massage')) return true;
            }

            if (subName.includes('mesin jahit')) {
              if (productName.includes('sewing') || productName.includes('jahit') ||
                  productName.includes('mesin')) return true;
            }

            if (subName.includes('setrika') || subName.includes('mesin uap')) {
              if (productName.includes('iron') || productName.includes('setrika') ||
                  productName.includes('steam') || productName.includes('uap')) return true;
            }

            if (subName.includes('purifier') || subName.includes('humidifier')) {
              if (productName.includes('purifier') || productName.includes('humidifier') ||
                  productName.includes('air') || productName.includes('filter')) return true;
            }

            if (subName.includes('telepon')) {
              if (productName.includes('phone') || productName.includes('telepon') ||
                  productName.includes('telephone')) return true;
            }

            if (subName.includes('cuci') || subName.includes('pengering')) {
              if (productName.includes('wash') || productName.includes('cuci') ||
                  productName.includes('dryer') || productName.includes('pengering')) return true;
            }

            return false;
          }).length;

          // Show all subcategories with their actual product count (even if 0)
          const count = subcategoryCount;
          dynamicCategories[sub.name] = count;
          totalCategoryCount += count;
          console.log(`✅ FACET: Added subcategory: ${sub.name} with actual count: ${count}`);
          console.log(`🔍 FACET: Products used for counting:`, productsToCount.length);
          console.log(`🔍 FACET: allProducts length:`, allProducts.length);

          // Debug: Show which products matched for this subcategory
          if (sub.name === "Konsol Game" || sub.name === "Aksesoris Konsol") {
            const matchedProducts = productsToCount.filter((product: any) => {
              const subName = sub.name.toLowerCase();
              const productName = product.name?.toLowerCase() || '';
              const productCategory = product.category?.toLowerCase() || '';

              // FIXED: Use exact category matching instead of includes to prevent cross-matching
              if (productCategory === subName) return true;

              // Enhanced matching for specific subcategories - FIXED: More specific matching
              if (subName === 'konsol game') {
                // Only match console products for "Konsol Game"
                if (productName.includes('playstation') || productName.includes('ps') ||
                    productName.includes('xbox') || productName.includes('nintendo') ||
                    productName.includes('steam') || productName.includes('switch')) return true;
              }

              if (subName === 'aksesoris konsol') {
                // Only match accessory products for "Aksesoris Konsol"
                if (productName.includes('controller') || productName.includes('headset') ||
                    productName.includes('mouse') || productName.includes('keyboard') ||
                    productName.includes('gamepad') || productName.includes('joystick') ||
                    productName.includes('charging')) return true;
              }

              return false;
            });
            console.log(`🎮 FACET: ${sub.name} matched products:`, matchedProducts.map(p => `${p.name} (${p.category})`));
          }
        });

        // Add main category with total count from all subcategories
        dynamicCategories[context.category] = totalCategoryCount;
        console.log(`✅ FACET: Added main category: ${context.category} with count: ${totalCategoryCount}`);

        categories = dynamicCategories as typeof categories;
        console.log('✅ FACET: Final categories object:', categories);
      } else {
        console.log('❌ FACET: No subcategory context found');
        console.log('❌ FACET: Context:', context);
        console.log('❌ FACET: subcategoryContext prop:', subcategoryContext);
        console.log('❌ FACET: window context:', (window as any).subcategoryContext);

        // Fallback: Check if we're in Elektronik category based on search results
        const hasElektronikProducts = searchResults.some(product =>
          product.category && product.category.toLowerCase().includes('konsol')
        );

        if (hasElektronikProducts) {
          console.log('🔥 FACET: Detected Elektronik category, adding all subcategories');
          const elektronikSubcategories = [
            "Konsol Game", "Aksesoris Konsol", "Alat Casing", "Foot Bath & Spa",
            "Mesin Jahit & Aksesoris", "Setrika & Mesin Uap", "Purifier & Humidifier",
            "Perangkat Debu & Peralatan Perawatan Lantai", "Telepon", "Mesin Cuci & Pengering",
            "Water Heater", "Pendingin Ruangan", "Pengering Sepatu", "Penghangat Ruangan",
            "TV & Aksesoris", "Perangkat Dapur", "Lampu", "Kamera Keamanan",
            "Video Game", "Kelastrian", "Baterai", "Rokok Elektronik & Shisha",
            "Remote Kontrol", "Walkie Talkie", "Media Player", "Perangkat Audio & Speaker",
            "Elektronik Lainnya"
          ];

          const dynamicCategories: { [key: string]: number } = {};

          // Add main category - calculate from actual subcategory counts
          let totalElektronikCount = 0;

          // Add all subcategories with real counts
          elektronikSubcategories.forEach((subName) => {
            // FIXED: Use search results instead of hardcoded data to match displayed products
            const productsToCount = searchResults;
            console.log(`🔍 FACET FALLBACK: Subcategory "${subName}" - using SEARCH RESULTS:`, productsToCount.length);
            const subcategoryCount = productsToCount.filter((product: any) => {
              const subNameLower = subName.toLowerCase();
              const productName = product.name?.toLowerCase() || '';
              const productCategory = product.category?.toLowerCase() || '';

              // FIXED: Use exact category matching instead of includes to prevent cross-matching
              if (productCategory === subNameLower) return true;

              // FIXED: More specific matching to prevent double counting
              if (subNameLower === 'konsol game') {
                if (productName.includes('playstation') || productName.includes('ps') ||
                    productName.includes('xbox') || productName.includes('nintendo') ||
                    productName.includes('steam') || productName.includes('switch')) return true;
              }

              if (subNameLower === 'aksesoris konsol') {
                if (productName.includes('controller') || productName.includes('headset') ||
                    productName.includes('mouse') || productName.includes('keyboard') ||
                    productName.includes('gamepad') || productName.includes('joystick') ||
                    productName.includes('charging')) return true;
              }

              return false;
            }).length;

            dynamicCategories[subName] = subcategoryCount;
            totalElektronikCount += subcategoryCount;
            console.log(`🔍 FACET FALLBACK: Added subcategory: ${subName} with count: ${subcategoryCount}`);
          });

          // Set main category count as sum of all subcategories
          dynamicCategories["Elektronik"] = totalElektronikCount;

          categories = dynamicCategories as typeof categories;
          console.log('🔥 FACET: Added Elektronik subcategories:', Object.keys(dynamicCategories));
        }
      }

      return {
        categories,
        priceRanges: {
          "Di bawah Rp 100.000": 67,
          "Rp 100.000 - Rp 500.000": 89,
          "Rp 500.000 - Rp 1.000.000": 45,
          "Rp 1.000.000 - Rp 5.000.000": 32,
          "Di atas Rp 5.000.000": 12
        },
        ratings: {
          "5 Bintang": 78,
          "4 Bintang ke atas": 156,
          "3 Bintang ke atas": 203
        },
        shipping: {
          "Gratis Ongkir": 134,
          "Same Day": 67,
          "Next Day": 89
        },
        features: {
          "COD": 98,
          "SellZio Mall": 56,
          "Flash Sale": 34
        }
      }
    }

    const facets: FacetData = {
      categories: {},
      priceRanges: {
        "Di bawah Rp 100.000": 0,
        "Rp 100.000 - Rp 500.000": 0,
        "Rp 500.000 - Rp 1.000.000": 0,
        "Rp 1.000.000 - Rp 5.000.000": 0,
        "Di atas Rp 5.000.000": 0
      },
      ratings: {
        "5 Bintang": 0,
        "4 Bintang ke atas": 0,
        "3 Bintang ke atas": 0
      },
      shipping: {
        "Gratis Ongkir": 0,
        "Same Day": 0,
        "Next Day": 0
      },
      features: {
        "COD": 0,
        "SellZio Mall": 0,
        "Flash Sale": 0
      }
    }

    // Skip category counting here if we have subcategory context - already handled above
    results.forEach(product => {
      // Categories - only count if we don't have subcategory context (to avoid double counting)
      if (!context || !context.allSubcategories) {
        if (product.category) {
          facets.categories[product.category] = (facets.categories[product.category] || 0) + 1
        }
      }

      // Price ranges
      const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
      if (price < 100000) {
        facets.priceRanges["Di bawah Rp 100.000"]++
      } else if (price < 500000) {
        facets.priceRanges["Rp 100.000 - Rp 500.000"]++
      } else if (price < 1000000) {
        facets.priceRanges["Rp 500.000 - Rp 1.000.000"]++
      } else if (price < 5000000) {
        facets.priceRanges["Rp 1.000.000 - Rp 5.000.000"]++
      } else {
        facets.priceRanges["Di atas Rp 5.000.000"]++
      }

      // Ratings
      const rating = product.rating || 0
      if (rating >= 5) facets.ratings["5 Bintang"]++
      if (rating >= 4) facets.ratings["4 Bintang ke atas"]++
      if (rating >= 3) facets.ratings["3 Bintang ke atas"]++

      // Shipping
      if (product.shipping === "Gratis Ongkir") facets.shipping["Gratis Ongkir"]++
      if (product.shipping === "Same Day") facets.shipping["Same Day"]++
      if (product.shipping === "Next Day") facets.shipping["Next Day"]++

      // Features
      if (product.cod === true) facets.features["COD"]++
      if (product.isMall === true) facets.features["SellZio Mall"]++
      if (product.flashSale === true) facets.features["Flash Sale"]++
    })

    // Categories are already handled in the first system above when we have subcategory context
    // No need to add missing subcategories here as they're already calculated with real counts

    return facets
  }

  const handleFilterChange = (type: keyof ActiveFilters, value: string, checked: boolean) => {
    setTempFilters(prev => {
      const newFilters = { ...prev }
      if (!newFilters[type]) newFilters[type] = []

      if (checked) {
        if (!newFilters[type]!.includes(value)) {
          newFilters[type]!.push(value)
        }
      } else {
        newFilters[type] = newFilters[type]!.filter(item => item !== value)

        if (newFilters[type]!.length === 0) {
          delete newFilters[type]
        }
      }

      // Auto apply filters for desktop sidebar immediately
      if (isDesktopSidebar) {
        // Apply filters immediately for desktop
        setTimeout(() => {
          onFiltersChange(newFilters)
        }, 0)
      }

      return newFilters
    })
  }



  const applyFilters = () => {
    onFiltersChange(tempFilters)
    // Don't close if it's desktop sidebar
    if (!isDesktopSidebar) {
      onClose()
    }
  }

  const resetFilters = () => {
    setTempFilters({})
    onFiltersChange({})
  }

  const countActiveFilters = () => {
    return Object.values(tempFilters).reduce((total, values) => total + (values?.length || 0), 0)
  }

  const renderFacetSection = (title: string, items: Record<string, number>, type: keyof ActiveFilters) => {
    const hasItems = Object.keys(items).some(key => items[key] > 0)
    if (!hasItems) return null

    // Check if this is kategori section and we have subcategory context
    const context = subcategoryContext || (window as any).subcategoryContext;
    const isKategoriSection = type === 'kategori' && context && context.allSubcategories;

    return (
      <div className="facet-section">
        <h3>{title}</h3>
        <ul>
          {isKategoriSection ? (
            // Render category name first, then subcategories with indentation
            <>
              {/* Category name with checkbox - calculate total count */}
              {(() => {
                const categoryCount = items[context.category] || 0;
                const isCategoryChecked = tempFilters[type]?.includes(context.category) || false;
                const categoryCheckboxId = `facet-${type}-${context.category.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`;

                return (
                  <li>
                    <input
                      type="checkbox"
                      id={categoryCheckboxId}
                      className="orange-checkbox"
                      checked={isCategoryChecked}
                      onChange={(e) => handleFilterChange(type, context.category, e.target.checked)}
                      data-facet-type={type}
                      data-facet-value={context.category}
                    />
                    <label htmlFor={categoryCheckboxId} className="category-name">
                      {context.category} ({categoryCount})
                    </label>
                  </li>
                );
              })()}

              {/* Subcategories with indentation and checkboxes */}
              {Object.entries(items).map(([key, count]) => {
                // Skip main category - only show subcategories
                if (key === context.category) return null;
                // Show all subcategories regardless of count

                const isChecked = tempFilters[type]?.includes(key) || false
                const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

                return (
                  <li key={key} className="subcategory-item">
                    <input
                      type="checkbox"
                      id={checkboxId}
                      className="orange-checkbox"
                      checked={isChecked}
                      onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                      data-facet-type={type}
                      data-facet-value={key}
                      disabled={count === 0}
                    />
                    <label htmlFor={checkboxId}>
                      {key} ({count})
                    </label>
                  </li>
                )
              })}
            </>
          ) : (
            // Regular rendering for other sections
            Object.entries(items).map(([key, count]) => {
              // Show all items regardless of count
              const isChecked = tempFilters[type]?.includes(key) || false
              const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

              return (
                <li key={key}>
                  <input
                    type="checkbox"
                    id={checkboxId}
                    className="orange-checkbox"
                    checked={isChecked}
                    onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                    data-facet-type={type}
                    data-facet-value={key}
                    disabled={count === 0}
                  />
                  <label htmlFor={checkboxId}>
                    {key} ({count})
                  </label>
                </li>
              )
            })
          )}
        </ul>
      </div>
    )
  }



  // For desktop sidebar, always show when isDesktopSidebar is true
  // For mobile/tablet popup, only show when isVisible is true
  if (!isDesktopSidebar && !isVisible) return null

  // Desktop Sidebar Layout
  if (isDesktopSidebar) {
    return (
      <div className="facet-sidebar-desktop">
        <div className="facet-header">
          <div className="facet-title">
            <Filter size={18} className="facet-filter-icon" />
            Filter
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {renderFacetSection('Kategori', facetData.categories, 'kategori')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
            {renderFacetSection('Rating', facetData.ratings, 'rating')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
            {renderFacetSection('Fitur', facetData.features, 'fitur')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    )
  }

  // Mobile/Tablet Popup Layout
  return (
    <>
      {/* Mobile/Tablet Overlay */}
      <div className="facet-overlay" style={{ display: (isMobile || isTablet) ? 'flex' : 'none' }}>
        <div className="facet-panel">
          <div className="facet-header">
            <div className="facet-title">Filter</div>
            <div className="facet-close" onClick={onClose}>
              <X size={18} />
            </div>
          </div>

          <div className="facet-content-wrapper">
            <div className="facet-content">
              {renderFacetSection('Kategori', facetData.categories, 'kategori')}
              {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
              {renderFacetSection('Rating', facetData.ratings, 'rating')}
              {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
              {renderFacetSection('Fitur', facetData.features, 'fitur')}
            </div>
          </div>

          <div className="facet-buttons">
            <div className="facet-button facet-button-reset" onClick={resetFilters}>
              Reset
            </div>
            <div className="facet-button facet-button-apply" onClick={applyFilters}>
              Terapkan ({countActiveFilters()})
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Panel - Only for non-sidebar mode */}
      <div
        className="facet-panel-desktop"
        style={{ display: (!isMobile && !isTablet) ? 'block' : 'none' }}
      >
        <div className="facet-header">
          <div className="facet-title">Filter</div>
          <div className="facet-close" onClick={onClose}>
            <X size={18} />
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {renderFacetSection('Kategori', facetData.categories, 'kategori')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
            {renderFacetSection('Rating', facetData.ratings, 'rating')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
            {renderFacetSection('Fitur', facetData.features, 'fitur')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    </>
  )
}