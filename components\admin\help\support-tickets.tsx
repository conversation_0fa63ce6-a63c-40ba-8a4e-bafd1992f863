"use client"

import { useState } from "react"
import {
  Filter,
  Plus,
  Search,
  Clock,
  AlertCircle,
  CheckCircle2,
  MoreVertical,
  MessageSquare,
  Calendar,
  Tag,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Textarea } from "@/components/ui/textarea"

// Dummy data for tickets
const tickets = [
  {
    id: "T-1234",
    title: "API Integration Issue",
    description: "I'm having trouble connecting to the API endpoint for order processing.",
    status: "open",
    priority: "high",
    category: "API",
    created: "2023-05-10T14:30:00",
    updated: "2023-05-10T16:45:00",
    user: {
      name: "Alex Johnson",
      email: "<EMAIL>",
      avatar: "/abstract-geometric-shapes.png",
    },
    messages: [
      {
        id: 1,
        user: {
          name: "<PERSON> <PERSON>",
          email: "<EMAIL>",
          avatar: "/abstract-geometric-shapes.png",
          role: "customer",
        },
        content:
          "I'm trying to connect to the order processing API endpoint but keep getting a 403 error. I've double-checked my API key and it should be valid.",
        timestamp: "2023-05-10T14:30:00",
      },
      {
        id: 2,
        user: {
          name: "Support Team",
          email: "<EMAIL>",
          avatar: "/people-supporting-each-other.png",
          role: "support",
        },
        content:
          "Hi Alex, I'll look into this right away. Could you please provide your API key (via secure channel) and the exact endpoint you're trying to access?",
        timestamp: "2023-05-10T15:15:00",
      },
      {
        id: 3,
        user: {
          name: "Alex Johnson",
          email: "<EMAIL>",
          avatar: "/abstract-geometric-shapes.png",
          role: "customer",
        },
        content:
          "I've sent the API key via the secure form. The endpoint I'm trying to access is /api/v1/orders/process.",
        timestamp: "2023-05-10T15:45:00",
      },
    ],
  },
  {
    id: "T-1233",
    title: "Payment Gateway Error",
    description: "Customers are reporting errors when trying to complete checkout.",
    status: "in-progress",
    priority: "medium",
    category: "Payments",
    created: "2023-05-09T10:15:00",
    updated: "2023-05-10T09:30:00",
    user: {
      name: "Sarah Miller",
      email: "<EMAIL>",
      avatar: "/abstract-geometric-shapes.png",
    },
    messages: [
      {
        id: 1,
        user: {
          name: "Sarah Miller",
          email: "<EMAIL>",
          avatar: "/abstract-geometric-shapes.png",
          role: "customer",
        },
        content:
          "Several customers have reported that they're getting an error when trying to complete checkout. The error message says 'Payment processing failed'.",
        timestamp: "2023-05-09T10:15:00",
      },
    ],
  },
  {
    id: "T-1232",
    title: "Custom Domain Setup",
    description: "Need help setting up a custom domain for my tenant store.",
    status: "resolved",
    priority: "low",
    category: "Domains",
    created: "2023-05-07T09:00:00",
    updated: "2023-05-08T14:20:00",
    user: {
      name: "Michael Chen",
      email: "<EMAIL>",
      avatar: "/diverse-group-collaborating.png",
    },
    messages: [
      {
        id: 1,
        user: {
          name: "Michael Chen",
          email: "<EMAIL>",
          avatar: "/diverse-group-collaborating.png",
          role: "customer",
        },
        content:
          "I need help setting up a custom domain for my tenant store. I've purchased the domain but I'm not sure how to configure it with SellZio.",
        timestamp: "2023-05-07T09:00:00",
      },
    ],
  },
  {
    id: "T-1231",
    title: "Product Import Failing",
    description: "CSV import for products is failing with validation errors.",
    status: "open",
    priority: "medium",
    category: "Products",
    created: "2023-05-06T16:45:00",
    updated: "2023-05-06T17:30:00",
    user: {
      name: "Jessica Wong",
      email: "<EMAIL>",
      avatar: "/abstract-geometric-shapes.png",
    },
    messages: [
      {
        id: 1,
        user: {
          name: "Jessica Wong",
          email: "<EMAIL>",
          avatar: "/abstract-geometric-shapes.png",
          role: "customer",
        },
        content:
          "I'm trying to import products using the CSV template, but it keeps failing with validation errors. I've checked the format and it seems correct.",
        timestamp: "2023-05-06T16:45:00",
      },
    ],
  },
  {
    id: "T-1230",
    title: "Store Theme Not Applying",
    description: "Selected theme is not applying correctly to my store.",
    status: "resolved",
    priority: "low",
    category: "Themes",
    created: "2023-05-05T11:20:00",
    updated: "2023-05-05T14:15:00",
    user: {
      name: "David Smith",
      email: "<EMAIL>",
      avatar: "/abstract-geometric-shapes.png",
    },
    messages: [
      {
        id: 1,
        user: {
          name: "David Smith",
          email: "<EMAIL>",
          avatar: "/abstract-geometric-shapes.png",
          role: "customer",
        },
        content:
          "I selected a new theme for my store, but it's not applying correctly. Some elements are missing or not styled properly.",
        timestamp: "2023-05-05T11:20:00",
      },
    ],
  },
]

export default function SupportTickets() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTicket, setSelectedTicket] = useState(tickets[0])
  const [newMessage, setNewMessage] = useState("")
  const [activeTab, setActiveTab] = useState("all")

  // Filter tickets based on search query and active tab
  const filteredTickets = tickets.filter((ticket) => {
    const matchesSearch =
      ticket.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.description.toLowerCase().includes(searchQuery.toLowerCase())

    if (activeTab === "all") return matchesSearch
    if (activeTab === "open") return matchesSearch && ticket.status === "open"
    if (activeTab === "in-progress") return matchesSearch && ticket.status === "in-progress"
    if (activeTab === "resolved") return matchesSearch && ticket.status === "resolved"

    return matchesSearch
  })

  const handleSendMessage = () => {
    if (!newMessage.trim()) return

    // In a real app, you would send this to an API
    console.log("Sending message:", newMessage)

    // Clear the input
    setNewMessage("")
  }

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    })
  }

  // Status badge color mapping
  const getStatusBadge = (status) => {
    switch (status) {
      case "open":
        return <Badge className="bg-yellow-500 hover:bg-yellow-600">Open</Badge>
      case "in-progress":
        return <Badge className="bg-blue-500 hover:bg-blue-600">In Progress</Badge>
      case "resolved":
        return <Badge className="bg-green-500 hover:bg-green-600">Resolved</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // Priority badge color mapping
  const getPriorityBadge = (priority) => {
    switch (priority) {
      case "high":
        return <Badge className="bg-red-500 hover:bg-red-600">High</Badge>
      case "medium":
        return <Badge className="bg-orange-500 hover:bg-orange-600">Medium</Badge>
      case "low":
        return <Badge className="bg-blue-500 hover:bg-blue-600">Low</Badge>
      default:
        return <Badge>{priority}</Badge>
    }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col space-y-2 mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Support Tickets</h1>
        <p className="text-muted-foreground">Manage and respond to support tickets from users across the platform.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1 space-y-6">
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search tickets..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Filter By</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <CheckCircle2 className="mr-2 h-4 w-4" /> Status
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <AlertCircle className="mr-2 h-4 w-4" /> Priority
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Tag className="mr-2 h-4 w-4" /> Category
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Calendar className="mr-2 h-4 w-4" /> Date
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> New Ticket
            </Button>
          </div>

          <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 w-full">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="open">Open</TabsTrigger>
              <TabsTrigger value="in-progress">In Progress</TabsTrigger>
              <TabsTrigger value="resolved">Resolved</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="space-y-2 max-h-[calc(100vh-300px)] overflow-y-auto">
            {filteredTickets.length > 0 ? (
              filteredTickets.map((ticket) => (
                <Card
                  key={ticket.id}
                  className={`cursor-pointer hover:bg-accent/50 transition-colors ${selectedTicket.id === ticket.id ? "border-primary" : ""}`}
                  onClick={() => setSelectedTicket(ticket)}
                >
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="space-y-1">
                        <div className="font-medium">{ticket.title}</div>
                        <div className="text-xs text-muted-foreground">#{ticket.id}</div>
                      </div>
                      {getStatusBadge(ticket.status)}
                    </div>
                    <div className="mt-2 text-sm text-muted-foreground line-clamp-2">{ticket.description}</div>
                    <div className="mt-2 flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={ticket.user.avatar || "/placeholder.svg"} alt={ticket.user.name} />
                          <AvatarFallback>{ticket.user.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <span className="text-xs">{ticket.user.name}</span>
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center">
                        <Clock className="mr-1 h-3 w-3" />
                        {new Date(ticket.updated).toLocaleDateString()}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">No tickets found matching your criteria</div>
            )}
          </div>
        </div>

        <div className="md:col-span-2">
          {selectedTicket ? (
            <Card className="h-full flex flex-col">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{selectedTicket.title}</CardTitle>
                    <CardDescription>#{selectedTicket.id}</CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>Assign to me</DropdownMenuItem>
                      <DropdownMenuItem>Escalate ticket</DropdownMenuItem>
                      <DropdownMenuItem>Mark as resolved</DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">Close ticket</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {getStatusBadge(selectedTicket.status)}
                  {getPriorityBadge(selectedTicket.priority)}
                  <Badge variant="outline">{selectedTicket.category}</Badge>
                </div>
                <div className="grid grid-cols-2 gap-4 mt-4 text-sm">
                  <div className="space-y-1">
                    <div className="text-muted-foreground">Submitted by</div>
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage
                          src={selectedTicket.user.avatar || "/placeholder.svg"}
                          alt={selectedTicket.user.name}
                        />
                        <AvatarFallback>{selectedTicket.user.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <span>{selectedTicket.user.name}</span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-muted-foreground">Created</div>
                    <div>{formatDate(selectedTicket.created)}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-muted-foreground">Last updated</div>
                    <div>{formatDate(selectedTicket.updated)}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-muted-foreground">Assigned to</div>
                    <div>Support Team</div>
                  </div>
                </div>
                <Separator className="my-4" />
              </CardHeader>
              <CardContent className="flex-grow overflow-y-auto space-y-4 pb-0">
                {selectedTicket.messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.user.role === "support" ? "justify-start" : "justify-end"}`}
                  >
                    <div
                      className={`flex max-w-[80%] ${message.user.role === "support" ? "flex-row" : "flex-row-reverse"}`}
                    >
                      <Avatar className={`h-8 w-8 ${message.user.role === "support" ? "mr-2" : "ml-2"}`}>
                        <AvatarImage src={message.user.avatar || "/placeholder.svg"} alt={message.user.name} />
                        <AvatarFallback>{message.user.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className={`space-y-1 ${message.user.role === "support" ? "" : "text-right"}`}>
                        <div
                          className={`rounded-lg p-3 ${
                            message.user.role === "support"
                              ? "bg-muted text-foreground"
                              : "bg-primary text-primary-foreground"
                          }`}
                        >
                          {message.content}
                        </div>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <span>{message.user.name}</span>
                          <span className="mx-1">•</span>
                          <span>{formatDate(message.timestamp)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
              <CardFooter className="pt-4">
                <div className="w-full space-y-2">
                  <Textarea
                    placeholder="Type your reply..."
                    className="min-h-[100px]"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                  />
                  <div className="flex justify-between">
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        Add Attachment
                      </Button>
                      <Button variant="outline" size="sm">
                        Internal Note
                      </Button>
                    </div>
                    <Button onClick={handleSendMessage}>
                      <MessageSquare className="mr-2 h-4 w-4" /> Send Reply
                    </Button>
                  </div>
                </div>
              </CardFooter>
            </Card>
          ) : (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              Select a ticket to view details
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
