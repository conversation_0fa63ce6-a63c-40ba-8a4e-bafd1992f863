import type { <PERSON>ada<PERSON> } from "next"
import { HelpSupportDashboard } from "@/components/admin/help/help-support-dashboard"
import { PageHeader } from "@/components/admin/ui/page-header"

export const metadata: Metadata = {
  title: "Help & Support | Sellzio Admin",
  description: "Dapatkan bantuan dan dukungan untuk platform Anda",
}

export default function HelpPage() {
  return (
    <>
      <PageHeader
        title="Help & Support"
        description="Dapatkan bantuan dan dukungan untuk platform Anda"
        actions={[
          {
            label: "Buat Tiket",
            href: "/admin/dashboard/help/tickets/create",
            variant: "default",
          },
          {
            label: "Dokumentasi",
            href: "/admin/dashboard/help/documentation",
            variant: "outline",
          },
        ]}
      />
      <HelpSupportDashboard />
    </>
  )
}
