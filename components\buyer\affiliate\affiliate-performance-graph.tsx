"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { LineChart } from "@/components/ui/charts"
import { addDays, format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

export function AffiliatePerformanceGraph() {
  const [chartType, setChartType] = useState<"earnings" | "clicks">("earnings")
  const [date, setDate] = useState<{
    from: Date
    to: Date
  }>({
    from: addDays(new Date(), -30),
    to: new Date(),
  })

  // Data dummy untuk grafik
  const earningsData = [
    { date: "1 Mei", Earnings: 50000, Clicks: 120 },
    { date: "8 Mei", Earnings: 75000, Clicks: 180 },
    { date: "15 Mei", Earnings: 125000, Clicks: 250 },
    { date: "22 Mei", Earnings: 175000, Clicks: 320 },
    { date: "29 Mei", Earnings: 250000, Clicks: 420 },
  ]

  const clicksData = [
    { date: "1 Mei", Conversions: 12, Clicks: 120 },
    { date: "8 Mei", Conversions: 18, Clicks: 180 },
    { date: "15 Mei", Conversions: 25, Clicks: 250 },
    { date: "22 Mei", Conversions: 32, Clicks: 320 },
    { date: "29 Mei", Conversions: 42, Clicks: 420 },
  ]

  return (
    <Card className="col-span-4">
      <CardHeader className="flex flex-row items-center">
        <div className="grid gap-2">
          <CardTitle>Performance Graph</CardTitle>
          <CardDescription>
            {chartType === "earnings"
              ? "Tren pendapatan dan klik dari waktu ke waktu"
              : "Perbandingan klik dan konversi dari waktu ke waktu"}
          </CardDescription>
        </div>
        <div className="ml-auto flex items-center gap-2">
          <Tabs value={chartType} onValueChange={(value) => setChartType(value as any)} className="w-auto">
            <TabsList>
              <TabsTrigger value="earnings">Pendapatan</TabsTrigger>
              <TabsTrigger value="clicks">Klik & Konversi</TabsTrigger>
            </TabsList>
          </Tabs>

          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date"
                variant={"outline"}
                className={cn("w-[260px] justify-start text-left font-normal", !date && "text-muted-foreground")}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date?.from ? (
                  date.to ? (
                    <>
                      {format(date.from, "dd MMM yyyy")} - {format(date.to, "dd MMM yyyy")}
                    </>
                  ) : (
                    format(date.from, "dd MMM yyyy")
                  )
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={date?.from}
                selected={date}
                onSelect={setDate}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </div>
      </CardHeader>
      <CardContent className="h-[300px]">
        {chartType === "earnings" ? (
          <LineChart
            data={earningsData}
            index="date"
            categories={["Earnings", "Clicks"]}
            colors={["green", "blue"]}
            valueFormatter={(value) =>
              typeof value === "number" && value > 1000 ? `Rp ${value.toLocaleString()}` : `${value}`
            }
            yAxisWidth={80}
          />
        ) : (
          <LineChart
            data={clicksData}
            index="date"
            categories={["Conversions", "Clicks"]}
            colors={["green", "blue"]}
            valueFormatter={(value) => `${value}`}
            yAxisWidth={80}
          />
        )}
      </CardContent>
    </Card>
  )
}
