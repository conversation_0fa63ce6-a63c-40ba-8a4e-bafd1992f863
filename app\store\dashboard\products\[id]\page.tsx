"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Edit,
  Trash2,
  ChevronLeft,
  ImageIcon,
  Tag,
  DollarSign,
  Package,
  BarChart,
  Star,
  MessageSquare,
} from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { productsAPI, type Product } from "@/lib/api/products"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertD<PERSON>ogHead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialog<PERSON>rigger,
} from "@/components/ui/alert-dialog"

export default function ProductDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedImage, setSelectedImage] = useState(0)

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const data = await productsAPI.getById(params.id)
        setProduct(data)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching product:", err)
        setError("Gagal memuat data produk. Silakan coba lagi.")
        setLoading(false)
      }
    }

    fetchProduct()
  }, [params.id])

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await productsAPI.delete(params.id)
      router.push("/store/dashboard/products")
    } catch (err) {
      console.error("Error deleting product:", err)
      setError("Gagal menghapus produk. Silakan coba lagi.")
      setIsDeleting(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-8 w-64" />
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <Skeleton className="h-80 w-full" />
          <div className="space-y-6">
            <Skeleton className="h-10 w-1/2" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-8 w-1/3" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </div>
          </div>
        </div>
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!product) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold mb-2">Produk tidak ditemukan</h3>
        <p className="text-muted-foreground mb-4">Produk yang Anda cari tidak ditemukan atau telah dihapus.</p>
        <Button asChild>
          <Link href="/store/dashboard/products">Kembali ke Daftar Produk</Link>
        </Button>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="icon" asChild>
          <Link href="/store/dashboard/products">
            <ChevronLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">{product.name}</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <div className="bg-muted rounded-lg flex items-center justify-center h-80">
            {product.images && product.images.length > 0 ? (
              <img
                src={product.images[selectedImage] || "/placeholder.svg"}
                alt={product.name}
                className="w-full h-full object-contain rounded-lg"
              />
            ) : (
              <div className="text-6xl font-bold text-muted-foreground">{product.name.charAt(0)}</div>
            )}
          </div>

          {product.images && product.images.length > 1 && (
            <div className="flex space-x-2 overflow-x-auto">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  className={`h-20 w-20 rounded-md overflow-hidden flex-shrink-0 ${
                    selectedImage === index ? "ring-2 ring-primary" : ""
                  }`}
                  onClick={() => setSelectedImage(index)}
                >
                  <img
                    src={image || "/placeholder.svg"}
                    alt={`${product.name} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">{formatCurrency(product.price)}</h2>
            <div className="flex space-x-2">
              <Button asChild>
                <Link href={`/store/dashboard/products/${params.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </Button>
              <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Hapus
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Hapus Produk</AlertDialogTitle>
                    <AlertDialogDescription>
                      Apakah Anda yakin ingin menghapus produk "{product.name}"? Tindakan ini tidak dapat dibatalkan.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Batal</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDelete}
                      disabled={isDeleting}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      {isDeleting ? "Menghapus..." : "Hapus"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">Deskripsi</h3>
            <p className="text-muted-foreground">{product.description || "Tidak ada deskripsi"}</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4 flex items-center gap-2">
                <Tag className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-xs text-muted-foreground">Kategori</p>
                  <p className="font-medium">Fashion</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex items-center gap-2">
                <Package className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-xs text-muted-foreground">Stok</p>
                  <p className="font-medium">25 unit</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-xs text-muted-foreground">Terjual</p>
                  <p className="font-medium">18 unit</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex items-center gap-2">
                <Star className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-xs text-muted-foreground">Rating</p>
                  <p className="font-medium">4.5/5</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Button className="w-full" asChild>
            <Link href={`/store/${product.store?.slug}/products/${product.id}`} target="_blank">
              Lihat di Toko
            </Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="analytics">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="analytics">
            <BarChart className="mr-2 h-4 w-4" />
            Analitik
          </TabsTrigger>
          <TabsTrigger value="reviews">
            <Star className="mr-2 h-4 w-4" />
            Ulasan
          </TabsTrigger>
          <TabsTrigger value="variants">
            <Package className="mr-2 h-4 w-4" />
            Varian
          </TabsTrigger>
          <TabsTrigger value="media">
            <ImageIcon className="mr-2 h-4 w-4" />
            Media
          </TabsTrigger>
        </TabsList>
        <TabsContent value="analytics" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Analitik Produk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-3">
                <Card>
                  <CardContent className="p-4">
                    <div className="text-xs text-muted-foreground">Dilihat</div>
                    <div className="text-2xl font-bold">245</div>
                    <div className="text-xs text-green-500">+12% minggu ini</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-xs text-muted-foreground">Ditambahkan ke Keranjang</div>
                    <div className="text-2xl font-bold">32</div>
                    <div className="text-xs text-green-500">+8% minggu ini</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-xs text-muted-foreground">Konversi</div>
                    <div className="text-2xl font-bold">7.3%</div>
                    <div className="text-xs text-green-500">+2.1% minggu ini</div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="reviews" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Ulasan Produk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    name: "Budi Santoso",
                    date: "2025-05-10",
                    rating: 5,
                    comment: "Kualitas bagus, pengiriman cepat!",
                  },
                  {
                    name: "Siti Rahayu",
                    date: "2025-05-08",
                    rating: 4,
                    comment: "Sesuai dengan gambar, nyaman dipakai.",
                  },
                  {
                    name: "Agus Wijaya",
                    date: "2025-05-05",
                    rating: 5,
                    comment: "Sangat puas dengan produknya!",
                  },
                ].map((review, i) => (
                  <div key={i} className="border-b pb-4">
                    <div className="flex items-center justify-between">
                      <div className="font-medium">{review.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(review.date).toLocaleDateString("id-ID")}
                      </div>
                    </div>
                    <div className="flex my-1">
                      {Array(5)
                        .fill(0)
                        .map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < review.rating ? "text-yellow-500 fill-yellow-500" : "text-muted-foreground"
                            }`}
                          />
                        ))}
                    </div>
                    <p className="text-sm">{review.comment}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="variants" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Varian Produk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center p-8">
                <MessageSquare className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-2 text-lg font-semibold">Belum ada varian</h3>
                <p className="text-muted-foreground mb-4">Produk ini belum memiliki varian.</p>
                <Button>Tambah Varian</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="media" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Media Produk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {product.images && product.images.length > 0 ? (
                  product.images.map((image, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={image || "/placeholder.svg"}
                        alt={`${product.name} ${index + 1}`}
                        className="w-full h-40 object-cover rounded-md"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2 rounded-md">
                        <Button size="icon" variant="ghost" className="h-8 w-8 text-white">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="icon" variant="ghost" className="h-8 w-8 text-white">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-full text-center p-8">
                    <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-2 text-lg font-semibold">Belum ada media</h3>
                    <p className="text-muted-foreground mb-4">Produk ini belum memiliki media.</p>
                    <Button>Tambah Media</Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
