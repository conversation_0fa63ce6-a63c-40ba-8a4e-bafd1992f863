"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ArrowLeft,
  Shield,
  Lock,
  KeyRound,
  Eye,
  EyeOff,
  Smartphone,
  AlertTriangle,
  Check,
  X,
  Loader2,
  Save,
  RefreshCw,
  Copy,
  LogOut,
  User,
  Globe,
  UserX,
  Clock,
  Info
} from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

export default function SecurityPage() {
  const [activeTab, setActiveTab] = useState("password")
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  
  // Dummy data untuk sesi masuk
  const loginSessions = [
    {
      id: "session-1",
      device: "Windows PC",
      browser: "Chrome 115",
      ip: "************",
      location: "Jakarta, Indonesia",
      lastActive: "2024-05-22T14:30:00",
      isCurrent: true
    },
    {
      id: "session-2",
      device: "iPhone 13",
      browser: "Safari Mobile",
      ip: "************",
      location: "Bandung, Indonesia",
      lastActive: "2024-05-21T09:45:00",
      isCurrent: false
    },
    {
      id: "session-3",
      device: "MacBook Pro",
      browser: "Firefox 100",
      ip: "************",
      location: "Surabaya, Indonesia",
      lastActive: "2024-05-20T16:20:00",
      isCurrent: false
    }
  ]

  // Dummy data untuk aktivitas keamanan
  const securityActivity = [
    {
      id: "activity-1",
      type: "login",
      description: "Login berhasil",
      timestamp: "2024-05-22T14:30:00",
      ip: "************",
      location: "Jakarta, Indonesia",
      device: "Windows PC (Chrome)"
    },
    {
      id: "activity-2",
      type: "password_change",
      description: "Kata sandi diubah",
      timestamp: "2024-05-20T10:15:00",
      ip: "************",
      location: "Jakarta, Indonesia",
      device: "Windows PC (Chrome)"
    },
    {
      id: "activity-3",
      type: "login_failed",
      description: "Percobaan login gagal",
      timestamp: "2024-05-19T08:45:00",
      ip: "************",
      location: "Singapore",
      device: "Unknown"
    },
    {
      id: "activity-4",
      type: "2fa_enabled",
      description: "Autentikasi dua faktor diaktifkan",
      timestamp: "2024-05-15T11:30:00",
      ip: "************",
      location: "Jakarta, Indonesia",
      device: "Windows PC (Chrome)"
    }
  ]

  // Format tanggal & waktu
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  function formatDateTime(dateString: string) {
    return new Date(dateString).toLocaleString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  function getTimeAgo(dateString: string) {
    const date = new Date(dateString)
    const now = new Date()
    const diffSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
    
    if (diffSeconds < 60) return `${diffSeconds} detik yang lalu`
    if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)} menit yang lalu`
    if (diffSeconds < 86400) return `${Math.floor(diffSeconds / 3600)} jam yang lalu`
    if (diffSeconds < 604800) return `${Math.floor(diffSeconds / 86400)} hari yang lalu`
    return formatDate(dateString)
  }

  // Badge untuk tipe aktivitas
  function getActivityBadge(type: string) {
    switch (type) {
      case "login":
        return <Badge variant="outline" className="bg-green-100 text-green-800"><Check className="h-3 w-3 mr-1" />Login Berhasil</Badge>
      case "logout":
        return <Badge variant="outline" className="bg-gray-100 text-gray-800"><LogOut className="h-3 w-3 mr-1" />Logout</Badge>
      case "password_change":
        return <Badge variant="outline" className="bg-blue-100 text-blue-800"><Lock className="h-3 w-3 mr-1" />Kata Sandi Diubah</Badge>
      case "login_failed":
        return <Badge variant="outline" className="bg-red-100 text-red-800"><X className="h-3 w-3 mr-1" />Login Gagal</Badge>
      case "2fa_enabled":
        return <Badge variant="outline" className="bg-purple-100 text-purple-800"><Shield className="h-3 w-3 mr-1" />2FA Diaktifkan</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/settings">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Pengaturan Keamanan</h1>
            <p className="text-muted-foreground">
              Kelola keamanan akun dan sesi login Anda
            </p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 md:w-auto">
          <TabsTrigger value="password">Kata Sandi</TabsTrigger>
          <TabsTrigger value="2fa">Autentikasi 2FA</TabsTrigger>
          <TabsTrigger value="sessions">Sesi & Aktivitas</TabsTrigger>
        </TabsList>

        {/* Password Tab */}
        <TabsContent value="password" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Ubah Kata Sandi</CardTitle>
              <CardDescription>
                Perbarui kata sandi akun Anda secara berkala untuk keamanan yang lebih baik
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="current-password" className="text-sm font-medium">
                  Kata Sandi Saat Ini
                </label>
                <div className="relative">
                  <Input 
                    id="current-password" 
                    type={showCurrentPassword ? "text" : "password"} 
                    onChange={() => {}}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-7 w-7"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    {showCurrentPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <label htmlFor="new-password" className="text-sm font-medium">
                  Kata Sandi Baru
                </label>
                <div className="relative">
                  <Input 
                    id="new-password" 
                    type={showNewPassword ? "text" : "password"} 
                    onChange={() => {}}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-7 w-7"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <div className="text-sm text-muted-foreground">
                  Kata sandi harus minimal 8 karakter dan mengandung huruf, angka, dan simbol
                </div>
              </div>
              <div className="space-y-2">
                <label htmlFor="confirm-password" className="text-sm font-medium">
                  Konfirmasi Kata Sandi Baru
                </label>
                <div className="relative">
                  <Input 
                    id="confirm-password" 
                    type={showConfirmPassword ? "text" : "password"} 
                    onChange={() => {}}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-7 w-7"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
              <div className="space-y-2 pt-2">
                <h3 className="text-sm font-medium">Kekuatan Kata Sandi</h3>
                <div className="h-2 w-full bg-muted overflow-hidden rounded-full">
                  <div className="h-full w-3/4 bg-green-500 rounded-full"></div>
                </div>
                <div className="flex items-center gap-2 text-sm text-green-600">
                  <Check className="h-4 w-4" />
                  Kata sandi kuat
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <p className="text-sm text-muted-foreground">
                Terakhir diubah: 20 Mei 2024
              </p>
              <Button>
                <Save className="h-4 w-4 mr-2" />
                Simpan Perubahan
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Kebijakan Kata Sandi</CardTitle>
              <CardDescription>
                Aturan dan rekomendasi untuk kata sandi yang kuat
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Minimal 8 karakter</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Mengandung setidaknya satu huruf kapital</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Mengandung setidaknya satu angka</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Mengandung setidaknya satu simbol (e.g., !@#$%)</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Berbeda dari kata sandi yang digunakan sebelumnya</span>
                </div>
              </div>
              <Alert className="bg-amber-50">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Keamanan Kata Sandi</AlertTitle>
                <AlertDescription>
                  Disarankan untuk mengubah kata sandi Anda setiap 3 bulan sekali. Jangan pernah membagikan kata sandi Anda dengan orang lain.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 2FA Tab */}
        <TabsContent value="2fa" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Autentikasi Dua Faktor (2FA)</CardTitle>
              <CardDescription>
                Tambahkan lapisan keamanan tambahan untuk akun Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="font-medium">Status 2FA</div>
                  <div className="text-sm text-muted-foreground">
                    Autentikasi dua faktor saat ini diaktifkan
                  </div>
                </div>
                <Badge className="bg-green-100 text-green-800">
                  <Check className="h-3 w-3 mr-1" />
                  Diaktifkan
                </Badge>
              </div>

              <Separator />

              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Metode Utama</h3>
                    <div className="flex items-start space-x-4">
                      <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                        <Smartphone className="h-5 w-5 text-primary" />
                      </div>
                      <div className="space-y-1">
                        <p className="font-medium">Aplikasi Autentikator</p>
                        <p className="text-sm text-muted-foreground">
                          Gunakan aplikasi seperti Google Authenticator atau Authy
                        </p>
                        <Button variant="outline" size="sm" className="mt-2">
                          <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                          Atur Ulang
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Metode Cadangan</h3>
                    <div className="flex items-start space-x-4">
                      <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                        <KeyRound className="h-5 w-5 text-primary" />
                      </div>
                      <div className="space-y-1">
                        <p className="font-medium">Kode Pemulihan</p>
                        <p className="text-sm text-muted-foreground">
                          Simpan kode ini di tempat yang aman jika Anda kehilangan akses ke perangkat
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-3.5 w-3.5 mr-1.5" />
                            Lihat Kode
                          </Button>
                          <Button variant="outline" size="sm">
                            <Copy className="h-3.5 w-3.5 mr-1.5" />
                            Salin
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Alert className="bg-blue-50">
                <Info className="h-4 w-4" />
                <AlertTitle>Informasi Autentikasi</AlertTitle>
                <AlertDescription>
                  Autentikasi dua faktor (2FA) membantu melindungi akun Anda bahkan jika kata sandi Anda diretas. 
                  Setiap kali masuk, Anda perlu memasukkan kode sementara dari aplikasi autentikator di perangkat Anda.
                </AlertDescription>
              </Alert>
            </CardContent>
            <CardFooter className="flex flex-col items-start">
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" className="text-destructive">
                    <Shield className="h-4 w-4 mr-2" />
                    Nonaktifkan 2FA
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Nonaktifkan Autentikasi Dua Faktor?</AlertDialogTitle>
                    <AlertDialogDescription>
                      Menonaktifkan 2FA akan membuat akun Anda kurang aman. Anda yakin ingin melanjutkan?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Batal</AlertDialogCancel>
                    <AlertDialogAction className="bg-destructive text-destructive-foreground">
                      Nonaktifkan
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
              <p className="text-sm text-muted-foreground mt-2">
                Peringatan: Menonaktifkan 2FA akan mengurangi keamanan akun Anda secara signifikan
              </p>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Pengaturan Tambahan</CardTitle>
              <CardDescription>
                Opsi keamanan tambahan untuk akun Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="font-medium">Autentikasi untuk Tindakan Sensitif</div>
                  <div className="text-sm text-muted-foreground">
                    Minta kode 2FA untuk tindakan sensitif seperti mengubah pengaturan keamanan
                  </div>
                </div>
                <Switch defaultChecked={true} onCheckedChange={() => {}} />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="font-medium">Notifikasi Login Baru</div>
                  <div className="text-sm text-muted-foreground">
                    Dapatkan email saat akun Anda diakses dari perangkat baru
                  </div>
                </div>
                <Switch defaultChecked={true} onCheckedChange={() => {}} />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="font-medium">Pembatasan Lokasi Login</div>
                  <div className="text-sm text-muted-foreground">
                    Batasi login hanya dari lokasi tertentu
                  </div>
                </div>
                <Switch defaultChecked={false} onCheckedChange={() => {}} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Sessions Tab */}
        <TabsContent value="sessions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Sesi Aktif</CardTitle>
              <CardDescription>
                Perangkat dan browser yang saat ini login ke akun Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                {loginSessions.map((session) => (
                  <div key={session.id} className="border rounded-lg p-4">
                    <div className="flex flex-col md:flex-row justify-between gap-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          {session.device.includes("Windows") ? (
                            <Globe className="h-4 w-4 text-primary" />
                          ) : session.device.includes("iPhone") ? (
                            <Smartphone className="h-4 w-4 text-primary" />
                          ) : (
                            <Globe className="h-4 w-4 text-primary" />
                          )}
                          <span className="font-medium">{session.device} • {session.browser}</span>
                          {session.isCurrent && (
                            <Badge variant="secondary">Sesi Ini</Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span>IP: {session.ip}</span>
                          <span>•</span>
                          <span>{session.location}</span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Terakhir aktif: {getTimeAgo(session.lastActive)}
                        </div>
                      </div>
                      {!session.isCurrent && (
                        <div className="flex items-center">
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm" className="text-destructive">
                                <LogOut className="h-3.5 w-3.5 mr-1.5" />
                                Akhiri Sesi
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Akhiri Sesi Ini?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Mengakhiri sesi ini akan memaksa logout perangkat tersebut. Pengguna harus login kembali untuk mengakses akun.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Batal</AlertDialogCancel>
                                <AlertDialogAction className="bg-destructive text-destructive-foreground">
                                  Akhiri Sesi
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              <div className="flex justify-end">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" className="text-destructive">
                      <UserX className="h-4 w-4 mr-2" />
                      Akhiri Semua Sesi Lain
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Akhiri Semua Sesi Lain?</AlertDialogTitle>
                      <AlertDialogDescription>
                        Tindakan ini akan mengakhiri semua sesi aktif kecuali sesi saat ini. Semua perangkat lain akan diminta untuk login kembali.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Batal</AlertDialogCancel>
                      <AlertDialogAction className="bg-destructive text-destructive-foreground">
                        Akhiri Semua Sesi
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Aktivitas Keamanan</CardTitle>
              <CardDescription>
                Riwayat aktivitas keamanan terkini untuk akun Anda
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Aktivitas</TableHead>
                    <TableHead>Waktu</TableHead>
                    <TableHead className="hidden md:table-cell">Lokasi</TableHead>
                    <TableHead className="hidden md:table-cell">Perangkat</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {securityActivity.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {getActivityBadge(activity.type)}
                          <span className="text-sm text-muted-foreground">
                            {activity.description}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm">{formatDateTime(activity.timestamp)}</span>
                          <span className="text-xs text-muted-foreground">
                            {activity.ip}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {activity.location}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {activity.device}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className="flex justify-end mt-4">
                <Button variant="outline" size="sm">
                  <Clock className="h-3.5 w-3.5 mr-1.5" />
                  Lihat Semua Aktivitas
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 