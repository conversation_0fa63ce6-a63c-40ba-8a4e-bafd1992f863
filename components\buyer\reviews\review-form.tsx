"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Star, Upload, X } from "lucide-react"
import Image from "next/image"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export function ReviewForm({ review, onClose }) {
  const [rating, setRating] = useState(review?.rating || 0)
  const [comment, setComment] = useState(review?.comment || "")
  const [images, setImages] = useState(review?.images || [])
  const [hoveredStar, setHoveredStar] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (rating === 0) {
      alert("Silakan berikan rating terlebih dahulu")
      return
    }

    setIsSubmitting(true)

    // Simulasi pengiriman data
    await new Promise((resolve) => setTimeout(resolve, 1000))

    console.log({
      productId: review.productId,
      rating,
      comment,
      images,
      orderId: review.orderId,
    })

    setIsSubmitting(false)
    onClose()
  }

  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files)
    if (files.length === 0) return

    // Batasi jumlah gambar
    if (images.length + files.length > 5) {
      alert("Maksimal 5 gambar yang dapat diunggah")
      return
    }

    // Konversi file ke URL untuk preview
    const newImages = files.map((file) => URL.createObjectURL(file))
    setImages([...images, ...newImages])
  }

  const removeImage = (index) => {
    const newImages = [...images]
    newImages.splice(index, 1)
    setImages(newImages)
  }

  const handleCancel = () => {
    if (comment || rating > 0 || images.length > 0) {
      // Tampilkan konfirmasi jika ada perubahan
      document.getElementById("confirm-cancel-dialog").click()
    } else {
      onClose()
    }
  }

  return (
    <Card className="w-full">
      <form onSubmit={handleSubmit}>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <Button type="button" variant="ghost" size="icon" onClick={handleCancel}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <CardTitle>{review.isNew ? "Tulis Ulasan" : "Edit Ulasan"}</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative h-40 w-full md:h-auto md:w-40 rounded-md overflow-hidden bg-gray-100">
              <Image
                src={review.productImage || "/placeholder.svg"}
                alt={review.productName}
                fill
                className="object-cover"
              />
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-medium">{review.productName}</h3>
              <p className="text-sm text-muted-foreground">
                {review.storeName} • Order #{review.orderId.split("-")[1]}
              </p>

              <div className="mt-6">
                <Label htmlFor="rating" className="mb-2 block font-medium">
                  Rating
                </Label>
                <div className="flex items-center gap-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setRating(star)}
                      onMouseEnter={() => setHoveredStar(star)}
                      onMouseLeave={() => setHoveredStar(0)}
                      className="rounded-full p-1 focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <Star
                        className={`h-8 w-8 ${
                          star <= (hoveredStar || rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                        }`}
                      />
                    </button>
                  ))}
                  <span className="ml-2 text-sm font-medium">{rating > 0 ? `${rating}/5` : "Belum ada rating"}</span>
                </div>
              </div>
            </div>
          </div>

          <div>
            <Label htmlFor="comment" className="mb-2 block font-medium">
              Komentar
            </Label>
            <Textarea
              id="comment"
              placeholder="Bagikan pengalaman Anda dengan produk ini..."
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={5}
              className="resize-none"
            />
            <p className="mt-1 text-xs text-muted-foreground">Minimal 10 karakter, maksimal 500 karakter</p>
          </div>

          <div>
            <Label className="mb-2 block font-medium">Foto Produk (Opsional)</Label>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              {images.map((img, idx) => (
                <div key={idx} className="relative aspect-square rounded-md overflow-hidden bg-gray-100">
                  <Image
                    src={img || "/placeholder.svg"}
                    alt={`Review image ${idx + 1}`}
                    fill
                    className="object-cover"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="absolute right-1 top-1 h-6 w-6"
                    onClick={() => removeImage(idx)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}

              {images.length < 5 && (
                <label className="flex aspect-square cursor-pointer flex-col items-center justify-center rounded-md border border-dashed bg-muted/30 hover:bg-muted/50">
                  <Upload className="mb-1 h-6 w-6 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">Unggah Foto</span>
                  <input type="file" accept="image/*" multiple className="hidden" onChange={handleImageUpload} />
                </label>
              )}
            </div>
            <p className="mt-1 text-xs text-muted-foreground">Maksimal 5 foto, ukuran maksimal 5MB per foto</p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={handleCancel}>
            Batal
          </Button>
          <Button type="submit" disabled={isSubmitting || rating === 0}>
            {isSubmitting ? "Menyimpan..." : "Simpan Ulasan"}
          </Button>
        </CardFooter>
      </form>

      {/* Dialog konfirmasi batal */}
      <AlertDialog>
        <AlertDialogTrigger id="confirm-cancel-dialog" className="hidden" />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Batalkan Ulasan?</AlertDialogTitle>
            <AlertDialogDescription>
              Perubahan yang Anda buat tidak akan disimpan. Apakah Anda yakin ingin membatalkan?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Lanjutkan Mengedit</AlertDialogCancel>
            <AlertDialogAction onClick={onClose}>Ya, Batalkan</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  )
}
