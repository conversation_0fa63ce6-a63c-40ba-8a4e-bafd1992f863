"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { AlertCircle, Edit2 } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface ReviewSubmitProps {
  formData: any
  onEdit: (step: number) => void
}

export function ReviewSubmit({ formData, onEdit }: ReviewSubmitProps) {
  const [termsAccepted, setTermsAccepted] = useState(false)
  const [commissionAcknowledged, setCommissionAcknowledged] = useState(false)
  const [sellerAgreementAccepted, setSellerAgreementAccepted] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async () => {
    if (!termsAccepted || !commissionAcknowledged || !sellerAgreementAccepted) {
      setError("Anda harus menyetujui semua persyaratan untuk melanjutkan")
      return
    }

    setError(null)
    setSubmitting(true)

    try {
      // Simulasi pengiriman data
      await new Promise((resolve) => setTimeout(resolve, 1500))
      // onSubmit()
    } catch (err) {
      setError("Terjadi kesalahan saat mengirimkan aplikasi. Silakan coba lagi.")
    } finally {
      setSubmitting(false)
    }
  }

  const sections = [
    { title: "Informasi Toko", step: 1, data: formData },
    { title: "Informasi Produk", step: 2, data: formData },
    { title: "Detail Bisnis", step: 3, data: formData },
    { title: "Kebijakan Toko", step: 4, data: formData },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Tinjau & Kirim Aplikasi</h2>
        <p className="text-muted-foreground">
          Silakan tinjau semua informasi yang telah Anda masukkan sebelum mengirimkan aplikasi Anda.
        </p>
      </div>

      {sections.map((section, index) => (
        <div key={index} className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{section.title}</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(section.step)}
              className="flex items-center gap-1"
            >
              <Edit2 className="h-4 w-4" />
              Edit
            </Button>
          </div>
          <div className="rounded-md border border-border p-4 bg-muted/20">
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {Object.entries(section.data || {})
                .filter(
                  ([key]) => typeof key === "string" && !key.includes("agreed") && !key.includes("acknowledgment"),
                )
                .slice(0, 6)
                .map(([key, value]) => (
                  <div key={key}>
                    <dt className="text-sm font-medium text-muted-foreground capitalize">
                      {key.replace(/([A-Z])/g, " $1").trim()}
                    </dt>
                    <dd className="text-sm mt-1">
                      {typeof value === "boolean"
                        ? value
                          ? "Ya"
                          : "Tidak"
                        : value === null || value === ""
                          ? "-"
                          : String(value)}
                    </dd>
                  </div>
                ))}
            </dl>
          </div>
          {index < sections.length - 1 && <Separator className="my-4" />}
        </div>
      ))}

      <div className="space-y-4 pt-4 border-t border-border">
        <h3 className="text-lg font-medium">Persetujuan & Pengakuan</h3>

        <div className="space-y-3">
          <div className="flex items-start space-x-2">
            <Checkbox
              id="terms"
              checked={termsAccepted}
              onCheckedChange={(checked) => setTermsAccepted(checked === true)}
            />
            <div className="grid gap-1.5 leading-none">
              <label
                htmlFor="terms"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Saya telah membaca dan menyetujui Syarat & Ketentuan
              </label>
              <p className="text-sm text-muted-foreground">
                Saya memahami bahwa dengan mengirimkan aplikasi ini, saya terikat oleh syarat dan ketentuan platform.
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <Checkbox
              id="commission"
              checked={commissionAcknowledged}
              onCheckedChange={(checked) => setCommissionAcknowledged(checked === true)}
            />
            <div className="grid gap-1.5 leading-none">
              <label
                htmlFor="commission"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Saya mengakui struktur komisi platform
              </label>
              <p className="text-sm text-muted-foreground">
                Saya memahami bahwa platform akan mengambil persentase dari setiap penjualan sesuai dengan struktur
                komisi yang berlaku.
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <Checkbox
              id="agreement"
              checked={sellerAgreementAccepted}
              onCheckedChange={(checked) => setSellerAgreementAccepted(checked === true)}
            />
            <div className="grid gap-1.5 leading-none">
              <label
                htmlFor="agreement"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Saya menyetujui Perjanjian Penjual
              </label>
              <p className="text-sm text-muted-foreground">
                Saya memahami tanggung jawab saya sebagai penjual di platform ini dan akan mematuhi semua kebijakan yang
                berlaku.
              </p>
            </div>
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  )
}
