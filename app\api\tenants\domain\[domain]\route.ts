import { NextResponse } from "next/server"

// Konfigurasi untuk static export
export const dynamic = "force-static"
export const revalidate = false

// Simulasi database tenant domains
// Dalam implementasi sebenarnya, ini akan disimpan di database
const tenantDomains = [
  { domain: "store1.com", tenantId: "1", slug: "store1" },
  { domain: "fashionhub.com", tenantId: "2", slug: "fashion-hub" },
  { domain: "techstore.com", tenantId: "3", slug: "tech-store" },
]

export async function GET(request: Request, { params }: { params: { domain: string } }) {
  try {
    const domain = params.domain

    // Cari tenant berdasarkan domain
    const tenant = tenantDomains.find((t) => t.domain === domain)

    if (!tenant) {
      return new NextResponse(JSON.stringify({ error: "Domain not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      })
    }

    return NextResponse.json({
      id: tenant.tenantId,
      tenantId: tenant.tenantId,
      slug: tenant.slug,
      domain: tenant.domain,
    })
  } catch (error) {
    console.error("Error processing domain request:", error)
    return new NextResponse(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}
