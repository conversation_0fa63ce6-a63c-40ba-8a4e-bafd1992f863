"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Skeleton } from "@/components/ui/skeleton"
import {
  ArrowLeft,
  Search,
  Filter,
  Eye,
  Check,
  X,
  Clock,
  AlertTriangle,
  Package,
  Flag,
  MessageSquare,
  ShieldCheck,
  User,
  Calendar,
  Tag,
  Star,
  Image as ImageIcon,
  FileText,
  MoreVertical
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

import { useProductModeration, type ProductModeration } from "@/hooks/use-product-moderation"

// Helper functions
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount)
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

function getStatusBadge(status: string) {
  switch (status) {
    case "pending":
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
    case "approved":
      return <Badge variant="default" className="bg-green-100 text-green-800"><Check className="h-3 w-3 mr-1" />Approved</Badge>
    case "rejected":
      return <Badge variant="destructive"><X className="h-3 w-3 mr-1" />Rejected</Badge>
    case "under_review":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800"><Eye className="h-3 w-3 mr-1" />Under Review</Badge>
    case "requires_changes":
      return <Badge variant="outline" className="bg-orange-100 text-orange-800"><AlertTriangle className="h-3 w-3 mr-1" />Requires Changes</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

function getPriorityBadge(priority: string) {
  switch (priority) {
    case "high":
    case "urgent":
      return <Badge variant="destructive">High Priority</Badge>
    case "normal":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Normal Priority</Badge>
    case "low":
      return <Badge variant="secondary">Low Priority</Badge>
    default:
      return <Badge variant="secondary">{priority}</Badge>
  }
}



export default function ProductModerationPage() {
  const {
    moderations,
    loading,
    fetchModerations,
    approveProduct,
    rejectProduct,
    setUnderReview,
    getModerationStats
  } = useProductModeration()

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null)
  const [stats, setStats] = useState<any>(null)

  // Load stats on mount
  useEffect(() => {
    const loadStats = async () => {
      const moderationStats = await getModerationStats()
      setStats(moderationStats)
    }
    loadStats()
  }, [getModerationStats])

  // Apply filters
  const filteredModerations = moderations.filter(moderation => {
    const product = moderation.product
    if (!product) return false

    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || moderation.status === statusFilter
    const matchesPriority = priorityFilter === "all" || moderation.priority === priorityFilter
    return matchesSearch && matchesStatus && matchesPriority
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Product Moderation</h1>
            <p className="text-muted-foreground">
              Review dan moderasi produk yang memerlukan persetujuan
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{stats.total}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Flagged</CardTitle>
            <Flag className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-red-600">{stats.flagged}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Under Review</CardTitle>
            <Eye className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-blue-600">{stats.under_review}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <Check className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <X className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari produk, SKU, atau store..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="pending">Pending</option>
              <option value="flagged">Flagged</option>
              <option value="under_review">Under Review</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Priority</option>
              <option value="high">High Priority</option>
              <option value="medium">Medium Priority</option>
              <option value="low">Low Priority</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Products List */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="flex items-start gap-6">
                  <Skeleton className="w-20 h-20 rounded-lg" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-6 w-1/3" />
                    <Skeleton className="h-4 w-2/3" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredModerations.map((moderation) => {
            const product = moderation.product
            if (!product) return null

            return (
              <Card key={moderation.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex gap-6">
                    {/* Product Images */}
                    <div className="flex-shrink-0">
                      <div className="flex gap-2">
                        {product.featured_image ? (
                          <Image
                            src={product.featured_image}
                            alt={product.name}
                            width={80}
                            height={80}
                            className="w-20 h-20 rounded-lg object-cover bg-gray-100 border"
                          />
                        ) : (
                          <div className="w-20 h-20 rounded-lg bg-gray-100 border flex items-center justify-center">
                            <Package className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                        {product.images && product.images.length > 1 && (
                          <div className="w-20 h-20 rounded-lg bg-gray-100 border flex items-center justify-center">
                            <span className="text-xs text-muted-foreground">+{product.images.length - 1}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold">{product.name}</h3>
                            {getStatusBadge(moderation.status)}
                            {getPriorityBadge(moderation.priority)}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                            <span>SKU: {product.sku}</span>
                            {product.store && <span>Store: {product.store.name}</span>}
                          </div>
                          <p className="text-sm text-muted-foreground">{product.description}</p>
                        </div>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Product Details */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-xs text-muted-foreground">Price</p>
                          <p className="font-semibold text-green-600">{formatCurrency(product.price)}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Stock</p>
                          <p className="font-semibold">{product.inventory_quantity || 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Submitted</p>
                          <p className="font-semibold">{formatDate(moderation.submitted_at)}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Priority</p>
                          <p className="font-semibold">{moderation.priority}</p>
                        </div>
                      </div>

                      {/* Store Owner Info */}
                      {product.store && (
                        <div className="flex items-center gap-2 mb-4">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">
                            Store: {product.store.name}
                            {product.store.owner_name && ` (${product.store.owner_name})`}
                          </span>
                        </div>
                      )}

                      {/* Tags */}
                      {product.tags && product.tags.length > 0 && (
                        <div className="mb-4">
                          <p className="text-xs text-muted-foreground mb-2">Tags</p>
                          <div className="flex flex-wrap gap-1">
                            {product.tags.map((tag: string, index: number) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Flags */}
                      {moderation.flags && moderation.flags.length > 0 && (
                        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <h4 className="text-sm font-medium text-yellow-800 mb-2 flex items-center gap-2">
                            <Flag className="h-4 w-4" />
                            Flags ({moderation.flags.length})
                          </h4>
                          <div className="flex flex-wrap gap-1">
                            {moderation.flags.map((flag: string, index: number) => (
                              <Badge key={index} variant="outline" className="text-xs bg-yellow-100 text-yellow-800">
                                {flag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Required Changes */}
                      {moderation.required_changes && moderation.required_changes.length > 0 && (
                        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <h4 className="text-sm font-medium text-red-800 mb-2 flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4" />
                            Required Changes ({moderation.required_changes.length})
                          </h4>
                          <div className="space-y-1">
                            {moderation.required_changes.map((change: string, index: number) => (
                              <p key={index} className="text-sm text-red-700">• {change}</p>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Rejection Reason */}
                      {moderation.rejection_reason && (
                        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <h4 className="text-sm font-medium text-red-800 mb-1">Rejection Reason</h4>
                          <p className="text-sm text-red-700">{moderation.rejection_reason}</p>
                        </div>
                      )}

                      {/* Moderation Info */}
                      {moderation.reviewer_id && (
                        <div className="mb-4 text-sm text-muted-foreground">
                          <p>Reviewed by: {moderation.reviewer?.name || moderation.reviewer_id}</p>
                          {moderation.reviewed_at && (
                            <p>Review date: {formatDate(moderation.reviewed_at)}</p>
                          )}
                        </div>
                      )}

                      {/* Review Notes */}
                      {moderation.review_notes && (
                        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <h4 className="text-sm font-medium text-blue-800 mb-1">Review Notes</h4>
                          <p className="text-sm text-blue-700">{moderation.review_notes}</p>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex gap-2 pt-4 border-t">
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                        {moderation.status === "pending" && (
                          <>
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => approveProduct(moderation.id, 'current-user', 'Approved via dashboard')}
                            >
                              <Check className="h-4 w-4 mr-2" />
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-blue-600"
                              onClick={() => setUnderReview(moderation.id, 'current-user', 'Under review')}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              Review
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => rejectProduct(moderation.id, 'current-user', 'Rejected via dashboard')}
                            >
                              <X className="h-4 w-4 mr-2" />
                              Reject
                            </Button>
                          </>
                        )}
                        {moderation.status === "under_review" && (
                          <>
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => approveProduct(moderation.id, 'current-user', 'Approved after review')}
                            >
                              <Check className="h-4 w-4 mr-2" />
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => rejectProduct(moderation.id, 'current-user', 'Rejected after review')}
                            >
                              <X className="h-4 w-4 mr-2" />
                              Reject
                            </Button>
                          </>
                        )}
                        <Button size="sm" variant="outline">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Add Note
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {!loading && filteredModerations.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <ShieldCheck className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No products found</h3>
            <p className="text-muted-foreground mb-4">
              Adjust your search filters or there are no products requiring moderation
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}