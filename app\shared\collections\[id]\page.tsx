"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ChevronLeft, Grid3X3, List, Globe, Info, Share2, Eye, ShoppingCart } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"
import { ProductQuickView } from "@/components/buyer/product-quick-view"
import { ShareProduct } from "@/components/buyer/share-product"
import { use } from "react"

interface Collection {
  id: string
  name: string
  description: string
  isPublic: boolean
  items: string[]
  createdAt: string
  updatedAt: string
}

interface WishlistItem {
  id: string
  name: string
  price: number
  originalPrice: number
  image: string
  inStock: boolean
  store: string
  storeId: string
  dateAdded: string
  priceDropped: boolean
  productId: string
}

export default function SharedCollectionPage({ params }: { params: { id: string } }) {
  const [collection, setCollection] = useState<Collection | null>(null)
  const [items, setItems] = useState<WishlistItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const router = useRouter()

  // Fungsi untuk mengambil detail koleksi
  const fetchCollectionDetail = async () => {
    setIsLoading(true)
    try {
      // Ambil detail koleksi
      const collectionResponse = await fetch(`/api/collections/${params.id}`)
      if (!collectionResponse.ok) {
        throw new Error('Gagal mengambil detail koleksi')
      }
      const collectionData = await collectionResponse.json()
      
      // Verifikasi bahwa koleksi bersifat publik
      if (!collectionData.isPublic) {
        toast({
          title: "Akses Ditolak",
          description: "Koleksi ini bersifat privat dan tidak dapat diakses.",
          variant: "destructive",
        })
        router.push('/')
        return
      }
      
      setCollection(collectionData)

      // Ambil data produk dalam koleksi dari wishlist
      const wishlistResponse = await fetch('/api/wishlist')
      if (!wishlistResponse.ok) {
        throw new Error('Gagal mengambil data wishlist')
      }
      const wishlistData = await wishlistResponse.json()

      // Filter item yang ada dalam koleksi
      const collectionItems = wishlistData.filter((item: WishlistItem) => 
        collectionData.items.includes(item.id)
      )
      setItems(collectionItems)
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal memuat detail koleksi",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fungsi untuk menambahkan item ke keranjang
  const addToCart = async (item: WishlistItem) => {
    try {
      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: item.productId,
          name: item.name,
          price: item.price,
          image: item.image,
          store: item.store,
          storeId: item.storeId,
          quantity: 1
        }),
      })
      
      if (!response.ok) {
        throw new Error('Gagal menambahkan ke keranjang')
      }
      
      toast({
        title: "Berhasil",
        description: `${item.name} ditambahkan ke keranjang`,
      })
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal menambahkan item ke keranjang",
        variant: "destructive",
      })
    }
  }

  // Fungsi untuk menambahkan ke wishlist sendiri
  const addToWishlist = async (item: WishlistItem) => {
    try {
      const response = await fetch('/api/wishlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: item.productId,
          name: item.name,
          price: item.price,
          originalPrice: item.originalPrice,
          image: item.image,
          store: item.store,
          storeId: item.storeId,
          inStock: item.inStock
        }),
      })
      
      if (!response.ok) {
        throw new Error('Gagal menambahkan ke wishlist')
      }
      
      toast({
        title: "Berhasil",
        description: `${item.name} ditambahkan ke wishlist Anda`,
      })
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal menambahkan item ke wishlist",
        variant: "destructive",
      })
    }
  }

  // Fungsi untuk melihat detail produk
  const viewProductDetail = (productId: string) => {
    router.push(`/products/${productId}`)
  }

  // Fungsi untuk memformat tanggal
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Effect untuk mengambil data koleksi saat komponen dimuat
  useEffect(() => {
    fetchCollectionDetail()
  }, [params.id])

  // Tampilkan loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="gap-1">
            <ChevronLeft className="h-4 w-4" />
            <Skeleton className="h-4 w-16" />
          </Button>
        </div>
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
          {Array(6).fill(0).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <Skeleton className="aspect-square w-full" />
              <CardContent className="p-3">
                <Skeleton className="mb-1 h-4 w-16" />
                <Skeleton className="h-5 w-full" />
                <Skeleton className="mt-1 h-5 w-24" />
                <Skeleton className="mt-2 h-8 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // Tampilkan pesan jika koleksi tidak ditemukan
  if (!collection) {
    return (
      <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">
        <div className="mb-4 rounded-full bg-muted p-4">
          <Info className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="mb-2 text-xl font-medium">Koleksi Tidak Ditemukan</h3>
        <p className="mb-6 text-muted-foreground">
          Koleksi yang Anda cari tidak ditemukan, telah dihapus, atau bersifat privat.
        </p>
        <Button onClick={() => router.push('/')}>
          Kembali ke Beranda
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button 
          variant="ghost" 
          size="sm" 
          className="gap-1"
          onClick={() => router.push('/')}
        >
          <ChevronLeft className="h-4 w-4" />
          <span>Kembali ke Beranda</span>
        </Button>
      </div>

      <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold tracking-tight">{collection.name}</h1>
            <Badge variant="outline" className="flex items-center gap-1">
              <Globe className="h-3 w-3" />
              <span>Koleksi Publik</span>
            </Badge>
          </div>
          {collection.description && (
            <p className="mt-1 text-muted-foreground">{collection.description}</p>
          )}
          <div className="mt-2 text-sm text-muted-foreground">
            <span>{collection.items.length} item • </span>
            <span>Dibuat {formatDate(collection.createdAt)}</span>
          </div>
        </div>
        <div className="flex gap-2">
          <div className="flex rounded-md border">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="icon"
              className="rounded-none rounded-l-md"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="icon"
              className="rounded-none rounded-r-md"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Tampilkan pesan jika koleksi kosong */}
      {items.length === 0 ? (
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">
          <div className="mb-4 rounded-full bg-muted p-4">
            <Info className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="mb-2 text-xl font-medium">Koleksi Kosong</h3>
          <p className="mb-6 text-muted-foreground">
            Koleksi ini belum memiliki produk.
          </p>
          <Button onClick={() => router.push('/')}>
            Jelajahi Produk
          </Button>
        </div>
      ) : viewMode === "grid" ? (
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
          {items.map((item) => (
            <Card key={item.id} className="overflow-hidden">
              <div className="relative">
                <div 
                  className="aspect-square w-full bg-gray-100 cursor-pointer" 
                  onClick={() => viewProductDetail(item.productId)}
                >
                  <img 
                    src={item.image || "/placeholder.svg"} 
                    alt={item.name} 
                    className="h-full w-full object-cover" 
                  />
                </div>
                {!item.inStock && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                    <Badge variant="outline" className="bg-black text-white">
                      Stok Habis
                    </Badge>
                  </div>
                )}
                {item.priceDropped && <Badge className="absolute right-2 top-2 bg-red-500">Turun Harga</Badge>}
                <div className="absolute bottom-2 right-2 flex gap-1">
                  <ProductQuickView
                    productId={item.productId}
                    productName={item.name}
                    productPrice={item.price}
                    productImage={item.image}
                    storeName={item.store}
                    storeId={item.storeId}
                    inStock={item.inStock}
                    originalPrice={item.originalPrice}
                    priceDropped={item.priceDropped}
                    description={`Informasi detail produk untuk ${item.name}. Kunjungi halaman produk untuk melihat lebih detail.`}
                  >
                    <Button 
                      size="icon" 
                      variant="secondary" 
                      className="h-8 w-8 rounded-full"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </ProductQuickView>
                  
                  <ShareProduct
                    productId={item.productId}
                    productName={item.name}
                  >
                    <Button 
                      size="icon" 
                      variant="secondary" 
                      className="h-8 w-8 rounded-full"
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </ShareProduct>
                </div>
              </div>
              <CardContent className="p-3">
                <div 
                  className="mb-1 text-xs text-muted-foreground cursor-pointer" 
                  onClick={() => router.push(`/store/${item.storeId}`)}
                >
                  {item.store}
                </div>
                <h3 
                  className="line-clamp-1 font-medium cursor-pointer" 
                  onClick={() => viewProductDetail(item.productId)}
                >
                  {item.name}
                </h3>
                <div className="mt-1 flex items-center gap-2">
                  <p className="font-bold">{formatCurrency(item.price)}</p>
                  {item.priceDropped && (
                    <p className="text-xs text-muted-foreground line-through">{formatCurrency(item.originalPrice)}</p>
                  )}
                </div>
                <div className="mt-2 flex gap-2">
                  <Button 
                    className="flex-1" 
                    size="sm" 
                    disabled={!item.inStock}
                    onClick={() => addToCart(item)}
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Tambah ke Keranjang
                  </Button>
                  <Button 
                    variant="outline"
                    size="sm" 
                    className="flex-1"
                    onClick={() => addToWishlist(item)}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Simpan
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {items.map((item) => (
            <Card key={item.id}>
              <CardContent className="flex gap-4 p-4">
                <div 
                  className="relative h-24 w-24 flex-shrink-0 cursor-pointer" 
                  onClick={() => viewProductDetail(item.productId)}
                >
                  <img
                    src={item.image || "/placeholder.svg"}
                    alt={item.name}
                    className="h-full w-full rounded-md object-cover"
                  />
                  {!item.inStock && (
                    <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black/60">
                      <Badge variant="outline" className="bg-black text-white">
                        Stok Habis
                      </Badge>
                    </div>
                  )}
                  {item.priceDropped && <Badge className="absolute -right-2 -top-2 bg-red-500">Turun Harga</Badge>}
                </div>
                <div className="flex flex-1 flex-col justify-between">
                  <div>
                    <div 
                      className="text-xs text-muted-foreground cursor-pointer" 
                      onClick={() => router.push(`/store/${item.storeId}`)}
                    >
                      {item.store}
                    </div>
                    <h3 
                      className="font-medium cursor-pointer" 
                      onClick={() => viewProductDetail(item.productId)}
                    >
                      {item.name}
                    </h3>
                    <div className="mt-1 flex items-center gap-2">
                      <p className="font-bold">{formatCurrency(item.price)}</p>
                      {item.priceDropped && (
                        <p className="text-xs text-muted-foreground line-through">
                          {formatCurrency(item.originalPrice)}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Ditambahkan pada {new Date(item.dateAdded).toLocaleDateString("id-ID")}
                  </div>
                </div>
                <div className="flex flex-col items-end justify-between">
                  <div className="flex items-center gap-2">
                    <ProductQuickView
                      productId={item.productId}
                      productName={item.name}
                      productPrice={item.price}
                      productImage={item.image}
                      storeName={item.store}
                      storeId={item.storeId}
                      inStock={item.inStock}
                      originalPrice={item.originalPrice}
                      priceDropped={item.priceDropped}
                      description={`Informasi detail produk untuk ${item.name}. Kunjungi halaman produk untuk melihat lebih detail.`}
                    >
                      <Button 
                        variant="ghost" 
                        size="sm"
                        className="gap-1"
                      >
                        <Eye className="h-4 w-4" />
                        Lihat
                      </Button>
                    </ProductQuickView>
                    
                    <ShareProduct
                      productId={item.productId}
                      productName={item.name}
                    >
                      <Button 
                        variant="ghost" 
                        size="sm"
                        className="gap-1"
                      >
                        <Share2 className="h-4 w-4" />
                        Bagikan
                      </Button>
                    </ShareProduct>
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      className="w-full" 
                      size="sm" 
                      disabled={!item.inStock}
                      onClick={() => addToCart(item)}
                    >
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      Tambah ke Keranjang
                    </Button>
                    <Button 
                      variant="outline"
                      size="sm" 
                      className="w-full"
                      onClick={() => addToWishlist(item)}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      Simpan
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
} 