"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight, Home } from "lucide-react"
import { cn } from "@/lib/utils"

interface BreadcrumbsProps {
  items?: { title: string; href: string }[]
  className?: string
}

export function Breadcrumbs({ items, className }: BreadcrumbsProps) {
  const pathname = usePathname()

  // Jika items tidak disediakan, buat breadcrumbs otomatis dari pathname
  const breadcrumbs = items || generateBreadcrumbs(pathname)

  return (
    <nav className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        <li>
          <Link href="/admin/dashboard" className="flex items-center hover:text-foreground">
            <Home className="h-4 w-4" />
            <span className="sr-only">Home</span>
          </Link>
        </li>
        {breadcrumbs.map((breadcrumb, index) => (
          <li key={breadcrumb.href} className="flex items-center">
            <ChevronRight className="h-4 w-4" />
            {index === breadcrumbs.length - 1 ? (
              <span className="ml-1 font-medium text-foreground">{breadcrumb.title}</span>
            ) : (
              <Link href={breadcrumb.href} className="ml-1 hover:text-foreground">
                {breadcrumb.title}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

// Fungsi untuk menghasilkan breadcrumbs dari pathname
function generateBreadcrumbs(pathname: string) {
  const paths = pathname.split("/").filter(Boolean)

  // Hapus 'admin' dari paths jika ada
  const filteredPaths = paths[0] === "admin" ? paths.slice(1) : paths

  // Buat breadcrumbs
  return filteredPaths.map((path, index) => {
    // Buat title yang lebih user-friendly
    const title = path
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")

    // Buat href
    const href = "/" + paths.slice(0, index + 1).join("/")

    return { title, href }
  })
}
