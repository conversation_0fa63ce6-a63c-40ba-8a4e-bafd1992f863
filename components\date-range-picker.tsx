"use client"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import type { DateRange } from "react-day-picker"
import { id } from "date-fns/locale"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

interface DateRangePickerProps {
  className?: string
  dateRange: DateRange | undefined
  onDateRangeChange: (range: DateRange | undefined) => void
}

export function DateRangePicker({ className, dateRange, onDateRangeChange }: DateRangePickerProps) {
  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn("w-[300px] justify-start text-left font-normal", !dateRange && "text-muted-foreground")}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {dateRange?.from ? (
              dateRange.to ? (
                <>
                  {format(dateRange.from, "dd MMM yyyy", { locale: id })} -{" "}
                  {format(dateRange.to, "dd MMM yyyy", { locale: id })}
                </>
              ) : (
                format(dateRange.from, "dd MMM yyyy", { locale: id })
              )
            ) : (
              <span>Pilih rentang tanggal</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={dateRange?.from}
            selected={dateRange}
            onSelect={onDateRangeChange}
            numberOfMonths={2}
            locale={id}
          />
          <div className="flex items-center justify-between p-3 border-t">
            <Button variant="ghost" onClick={() => onDateRangeChange(undefined)} size="sm">
              Reset
            </Button>
            <Button
              size="sm"
              onClick={() => {
                const today = new Date()
                onDateRangeChange({
                  from: new Date(today.getFullYear(), today.getMonth(), 1),
                  to: today,
                })
              }}
            >
              Bulan Ini
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
