"use client"

import { useState, useEffect, useCallback } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Edit, Ban, CheckCircle, Trash2, LogIn } from "lucide-react"
import { useNotifications } from "@/components/providers/notifications-provider"
import { SuspendTenantModal } from "@/components/admin/tenants/suspend-tenant-modal"
import { LoginAsTenantModal } from "@/components/admin/tenants/login-as-tenant-modal"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  getTenantById,
  suspendTenant,
  reactivateTenant,
  deleteTenant,
  TENANTS_STORAGE_KEY,
} from "@/lib/services/tenant-service"

export default function TenantDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { addNotification } = useNotifications()
  const [tenant, setTenant] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const [suspendModalOpen, setSuspendModalOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [loginModalOpen, setLoginModalOpen] = useState(false)

  // Load tenant data
  const loadTenant = useCallback(async () => {
    if (!params.id) return

    try {
      setIsLoading(true)
      const data = await getTenantById(params.id)
      setTenant(data)
    } catch (error) {
      console.error("Error loading tenant:", error)
      addNotification({
        message: "Gagal memuat data tenant",
        type: "error",
      })
    } finally {
      setIsLoading(false)
    }
  }, [params.id, addNotification])

  // Load tenant on initial render
  useEffect(() => {
    loadTenant()
  }, [loadTenant])

  // Listen for storage events
  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event.key === TENANTS_STORAGE_KEY) {
        loadTenant()
      }
    }

    window.addEventListener("storage", handleStorageChange)
    return () => {
      window.removeEventListener("storage", handleStorageChange)
    }
  }, [loadTenant])

  // Handle suspend tenant
  const handleSuspendTenant = async (data) => {
    try {
      setIsLoading(true)
      await suspendTenant(tenant.id, data.reason, data.durationType === "temporary" ? data.endDate : undefined)

      // Reload tenant
      await loadTenant()

      // Close modal
      setSuspendModalOpen(false)

      addNotification({
        message: `Tenant ${tenant.name} berhasil ditangguhkan`,
        type: "success",
      })
    } catch (error) {
      console.error("Error suspending tenant:", error)
      addNotification({
        message: `Gagal menangguhkan tenant ${tenant.name}`,
        type: "error",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle activate tenant
  const handleActivateTenant = async () => {
    try {
      setIsLoading(true)
      await reactivateTenant(tenant.id, "Manually activated by admin")

      // Reload tenant
      await loadTenant()

      addNotification({
        message: `Tenant ${tenant.name} berhasil diaktifkan`,
        type: "success",
      })
    } catch (error) {
      console.error("Error activating tenant:", error)
      addNotification({
        message: `Gagal mengaktifkan tenant ${tenant.name}`,
        type: "error",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle delete tenant
  const handleDeleteTenant = async () => {
    try {
      setIsLoading(true)
      await deleteTenant(tenant.id)

      // Close dialog
      setDeleteDialogOpen(false)

      addNotification({
        message: `Tenant ${tenant.name} berhasil dihapus`,
        type: "success",
      })

      // Navigate back to tenants list
      router.push("/admin/dashboard/tenants")
    } catch (error) {
      console.error("Error deleting tenant:", error)
      addNotification({
        message: `Gagal menghapus tenant ${tenant.name}`,
        type: "error",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Render status badge
  const renderStatusBadge = (tenant) => {
    if (!tenant) return null

    const status = tenant.status
    const suspensionEndDate = tenant.suspensionEndDate
    const isTemporarySuspension = status === "suspended" && suspensionEndDate

    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Active</Badge>
      case "suspended":
        if (isTemporarySuspension) {
          // Format tanggal untuk tooltip
          const formattedDate = suspensionEndDate
            ? new Date(suspensionEndDate).toLocaleDateString("id-ID", {
                day: "numeric",
                month: "long",
                year: "numeric",
              })
            : ""

          return (
            <Badge
              className="bg-amber-100 text-amber-800 border-amber-200"
              title={`Tenant akan diaktifkan kembali pada ${formattedDate}`}
            >
              Suspended
            </Badge>
          )
        } else {
          return <Badge className="bg-red-100 text-red-800 border-red-200">Suspended</Badge>
        }
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (isLoading && !tenant) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!tenant) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
        <h2 className="text-2xl font-bold mb-4">Tenant tidak ditemukan</h2>
        <Button onClick={() => router.push("/admin/dashboard/tenants")}>Kembali ke Daftar Tenant</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => router.push("/admin/dashboard/tenants")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Kembali
        </Button>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push(`/admin/dashboard/tenants/${tenant.id}/edit`)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="outline" onClick={() => setLoginModalOpen(true)}>
            <LogIn className="mr-2 h-4 w-4" />
            Login as
          </Button>
          {tenant.status === "active" ? (
            <Button variant="outline" className="text-red-600" onClick={() => setSuspendModalOpen(true)}>
              <Ban className="mr-2 h-4 w-4" />
              Suspend
            </Button>
          ) : (
            <Button variant="outline" className="text-green-600" onClick={handleActivateTenant}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Activate
            </Button>
          )}
          <Button variant="destructive" onClick={() => setDeleteDialogOpen(true)}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <div className="bg-card rounded-lg p-6 border">
        <div className="flex flex-col gap-4">
          <div>
            <h1 className="text-3xl font-bold">{tenant.name}</h1>
            <p className="text-muted-foreground">{tenant.domain}</p>
          </div>
          <div className="flex items-center gap-4">
            {renderStatusBadge(tenant)}
            <Badge variant="outline">{tenant.plan}</Badge>
            <span className="text-sm text-muted-foreground">Created: {tenant.createdAt}</span>
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="stores">Stores</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4 mt-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Stores</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{tenant.storeCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{tenant.userCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{tenant.revenue}</div>
              </CardContent>
            </Card>
          </div>

          {tenant.status === "suspended" && (
            <Card className="border-red-200 bg-red-50">
              <CardHeader>
                <CardTitle className="text-red-800">Suspension Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <span className="font-medium">Reason:</span> {tenant.suspensionReason}
                </div>
                <div>
                  <span className="font-medium">Date:</span> {tenant.suspensionDate}
                </div>
                {tenant.suspensionEndDate && (
                  <div>
                    <span className="font-medium">End Date:</span> {tenant.suspensionEndDate}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>
        <TabsContent value="stores" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Stores</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Store management content will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="users" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Users</CardTitle>
            </CardHeader>
            <CardContent>
              <p>User management content will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="billing" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Billing</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Billing information will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="settings" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Tenant settings will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modal untuk suspend tenant */}
      <SuspendTenantModal
        tenant={tenant}
        open={suspendModalOpen}
        onOpenChange={setSuspendModalOpen}
        onSuspend={handleSuspendTenant}
        isLoading={isLoading}
      />

      {/* Dialog konfirmasi untuk delete tenant */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Apakah Anda yakin?</AlertDialogTitle>
            <AlertDialogDescription>
              Tindakan ini akan menghapus tenant {tenant.name} secara permanen dan tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteTenant}
              className="bg-red-600 hover:bg-red-700"
              disabled={isLoading}
            >
              {isLoading ? "Menghapus..." : "Hapus"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Modal Login as Tenant */}
      <LoginAsTenantModal tenant={tenant} open={loginModalOpen} onOpenChange={setLoginModalOpen} />
    </div>
  )
}
