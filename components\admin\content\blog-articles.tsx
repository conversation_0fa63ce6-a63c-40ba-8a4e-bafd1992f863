"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { PlusIcon, PencilIcon, TrashIcon, EyeIcon, CalendarIcon, SearchIcon, ImageIcon, TagIcon } from "lucide-react"

export function BlogArticles() {
  const [articles, setArticles] = useState([])
  const [selectedArticle, setSelectedArticle] = useState(null)
  const [activeTab, setActiveTab] = useState("articles")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulasi pengambilan data
    setTimeout(() => {
      const mockArticles = [
        {
          id: 1,
          title: "10 Tips for Better Product Photography",
          slug: "product-photography-tips",
          excerpt: "Learn how to take professional product photos with just your smartphone and some basic equipment.",
          author: {
            name: "Sarah Miller",
            avatar: "/placeholder.svg?key=x6phq",
          },
          category: "Photography",
          tags: ["product", "photography", "tips"],
          publishedDate: "2023-10-05",
          status: "published",
          views: 4250,
          featuredImage: "/placeholder.svg?key=vovmt",
        },
        {
          id: 2,
          title: "E-commerce Trends to Watch in 2024",
          slug: "ecommerce-trends-2024",
          excerpt: "Discover the upcoming trends that will shape the e-commerce landscape in 2024.",
          author: {
            name: "David Chen",
            avatar: "/placeholder.svg?key=tty25",
          },
          category: "E-commerce",
          tags: ["trends", "ecommerce", "2024"],
          publishedDate: "2023-10-12",
          status: "published",
          views: 3180,
          featuredImage: "/placeholder.svg?key=857p5",
        },
        {
          id: 3,
          title: "How to Optimize Your Store for Mobile Users",
          slug: "mobile-optimization-guide",
          excerpt:
            "A comprehensive guide to ensuring your online store provides the best experience for mobile shoppers.",
          author: {
            name: "Emily Wilson",
            avatar: "/placeholder.svg?key=xdei1",
          },
          category: "Mobile",
          tags: ["mobile", "optimization", "ux"],
          publishedDate: "2023-09-28",
          status: "published",
          views: 2890,
          featuredImage: "/placeholder.svg?key=fg8xa",
        },
        {
          id: 4,
          title: "Black Friday Preparation Checklist",
          slug: "black-friday-checklist",
          excerpt: "Everything you need to prepare your online store for the biggest shopping event of the year.",
          author: {
            name: "Michael Brown",
            avatar: "/placeholder.svg?key=jeny5",
          },
          category: "Marketing",
          tags: ["black friday", "sales", "preparation"],
          publishedDate: null,
          status: "draft",
          views: 0,
          featuredImage: "/placeholder.svg?key=y73ip",
        },
        {
          id: 5,
          title: "Sustainable Packaging Solutions for E-commerce",
          slug: "sustainable-packaging-ecommerce",
          excerpt:
            "Explore eco-friendly packaging options that can reduce your environmental impact while impressing customers.",
          author: {
            name: "Alex Johnson",
            avatar: "/placeholder.svg?key=4eo6m",
          },
          category: "Sustainability",
          tags: ["eco-friendly", "packaging", "sustainability"],
          publishedDate: "2023-09-15",
          status: "published",
          views: 1950,
          featuredImage: "/placeholder.svg?key=nx0g0",
        },
        {
          id: 6,
          title: "Holiday Marketing Campaign Ideas",
          slug: "holiday-marketing-ideas",
          excerpt: "Creative marketing strategies to boost your sales during the holiday season.",
          author: {
            name: "Jessica Lee",
            avatar: "/placeholder.svg?key=lx1am",
          },
          category: "Marketing",
          tags: ["holiday", "marketing", "campaigns"],
          publishedDate: null,
          status: "scheduled",
          scheduledDate: "2023-11-01",
          views: 0,
          featuredImage: "/placeholder.svg?key=owujs",
        },
      ]

      setArticles(mockArticles)
      setIsLoading(false)
    }, 1000)
  }, [])

  const handleArticleSelect = (article) => {
    setSelectedArticle(article)
    setActiveTab("editor")
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800"
      case "draft":
        return "bg-yellow-100 text-yellow-800"
      case "scheduled":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (isLoading) {
    return (
      <div className="grid gap-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <div className="aspect-video w-full bg-gray-200"></div>
              <CardHeader>
                <div className="h-5 w-32 rounded bg-gray-200"></div>
                <div className="h-4 w-48 rounded bg-gray-200"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 w-full rounded bg-gray-200"></div>
                <div className="mt-2 h-4 w-2/3 rounded bg-gray-200"></div>
              </CardContent>
              <CardFooter>
                <div className="h-9 w-full rounded bg-gray-200"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-3 lg:w-[400px]">
        <TabsTrigger value="articles">All Articles</TabsTrigger>
        <TabsTrigger value="editor" disabled={!selectedArticle}>
          Article Editor
        </TabsTrigger>
        <TabsTrigger value="categories">Categories & Tags</TabsTrigger>
      </TabsList>

      {/* All Articles Tab */}
      <TabsContent value="articles" className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-bold">Blog Articles</h2>
            <p className="text-sm text-muted-foreground">Manage your platform blog content</p>
          </div>
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" />
            Create New Article
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search articles..." className="pl-8" />
          </div>
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="scheduled">Scheduled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {articles.map((article) => (
            <Card key={article.id} className="overflow-hidden">
              <div className="aspect-video w-full overflow-hidden">
                <img
                  src={article.featuredImage || "/placeholder.svg"}
                  alt={article.title}
                  className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                />
              </div>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <Badge variant="outline">{article.category}</Badge>
                  <Badge variant="outline" className={getStatusColor(article.status)}>
                    {article.status}
                  </Badge>
                </div>
                <CardTitle className="line-clamp-2">{article.title}</CardTitle>
                <CardDescription className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={article.author.avatar || "/placeholder.svg"} alt={article.author.name} />
                    <AvatarFallback>{article.author.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <span>{article.author.name}</span>
                  {article.publishedDate && (
                    <>
                      <span>•</span>
                      <span>{article.publishedDate}</span>
                    </>
                  )}
                  {article.scheduledDate && (
                    <>
                      <span>•</span>
                      <span>Scheduled: {article.scheduledDate}</span>
                    </>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="line-clamp-2 text-sm text-muted-foreground">{article.excerpt}</p>
                <div className="mt-2 flex flex-wrap gap-1">
                  {article.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" onClick={() => handleArticleSelect(article)}>
                  <PencilIcon className="mr-2 h-4 w-4" />
                  Edit
                </Button>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <EyeIcon className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                  <Button variant="outline" size="sm" className="text-red-500 hover:text-red-700">
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </TabsContent>

      {/* Article Editor Tab */}
      <TabsContent value="editor" className="space-y-6">
        {selectedArticle && (
          <>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h2 className="text-2xl font-bold">Editing: {selectedArticle.title}</h2>
                <p className="text-sm text-muted-foreground">Last saved: 5 minutes ago</p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline">
                  <EyeIcon className="mr-2 h-4 w-4" />
                  Preview
                </Button>
                <Select defaultValue={selectedArticle.status}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                  </SelectContent>
                </Select>
                <Button>Save Changes</Button>
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-[1fr_3fr]">
              {/* Article Settings Panel */}
              <Card>
                <CardHeader>
                  <CardTitle>Article Settings</CardTitle>
                  <CardDescription>Configure article properties</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="article-title">Article Title</Label>
                    <Input id="article-title" defaultValue={selectedArticle.title} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="article-slug">URL Slug</Label>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">/blog/</span>
                      <Input id="article-slug" defaultValue={selectedArticle.slug} />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="article-excerpt">Excerpt</Label>
                    <Textarea
                      id="article-excerpt"
                      defaultValue={selectedArticle.excerpt}
                      placeholder="Brief summary of the article..."
                    />
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <Label htmlFor="article-category">Category</Label>
                    <Select defaultValue={selectedArticle.category}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="E-commerce">E-commerce</SelectItem>
                        <SelectItem value="Marketing">Marketing</SelectItem>
                        <SelectItem value="Photography">Photography</SelectItem>
                        <SelectItem value="Mobile">Mobile</SelectItem>
                        <SelectItem value="Sustainability">Sustainability</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="article-tags">Tags</Label>
                    <div className="flex flex-wrap gap-2">
                      {selectedArticle.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <button className="ml-1 rounded-full text-xs hover:bg-gray-200">×</button>
                        </Badge>
                      ))}
                      <Input placeholder="Add tag..." className="w-24 flex-grow" />
                    </div>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <Label>Featured Image</Label>
                    <div className="overflow-hidden rounded-md border">
                      <img
                        src={selectedArticle.featuredImage || "/placeholder.svg"}
                        alt="Featured"
                        className="aspect-video w-full object-cover"
                      />
                      <div className="flex items-center justify-between p-2">
                        <span className="text-xs text-muted-foreground">featured-image.jpg</span>
                        <Button variant="ghost" size="sm">
                          <ImageIcon className="mr-2 h-4 w-4" />
                          Change
                        </Button>
                      </div>
                    </div>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <Label>Publishing Options</Label>
                    <div className="space-y-4 rounded-md border p-4">
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Publication Date</span>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <Input type="date" defaultValue={selectedArticle.publishedDate || ""} />
                        <Input type="time" defaultValue="09:00" />
                      </div>
                      <div className="flex items-center gap-2">
                        <Input type="checkbox" id="featured" className="h-4 w-4" />
                        <Label htmlFor="featured" className="text-sm font-normal">
                          Feature this article
                        </Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Rich Text Editor */}
              <Card>
                <CardHeader>
                  <CardTitle>Article Content</CardTitle>
                  <CardDescription>Write your article using the rich text editor</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2 border-b pb-4">
                    <Button variant="outline" size="sm">
                      B
                    </Button>
                    <Button variant="outline" size="sm">
                      I
                    </Button>
                    <Button variant="outline" size="sm">
                      U
                    </Button>
                    <Button variant="outline" size="sm">
                      H1
                    </Button>
                    <Button variant="outline" size="sm">
                      H2
                    </Button>
                    <Button variant="outline" size="sm">
                      H3
                    </Button>
                    <Button variant="outline" size="sm">
                      List
                    </Button>
                    <Button variant="outline" size="sm">
                      Link
                    </Button>
                    <Button variant="outline" size="sm">
                      Image
                    </Button>
                    <Button variant="outline" size="sm">
                      Quote
                    </Button>
                    <Button variant="outline" size="sm">
                      Code
                    </Button>
                  </div>
                  <Textarea
                    className="min-h-[400px] resize-none border-0 focus-visible:ring-0"
                    placeholder="Start writing your article here..."
                    defaultValue={`# ${selectedArticle.title}

${selectedArticle.excerpt}

## Introduction

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.

## Main Content

Sed euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc, quis nisl. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.

### Subheading

- Point one about the topic
- Another important consideration
- Final key takeaway

## Conclusion

Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.`}
                  />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline">Save as Draft</Button>
                  <Button>Publish</Button>
                </CardFooter>
              </Card>
            </div>
          </>
        )}
      </TabsContent>

      {/* Categories & Tags Tab */}
      <TabsContent value="categories" className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-bold">Categories & Tags</h2>
            <p className="text-sm text-muted-foreground">Manage blog organization</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <PlusIcon className="mr-2 h-4 w-4" />
              New Category
            </Button>
            <Button>
              <TagIcon className="mr-2 h-4 w-4" />
              Manage Tags
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Categories Management */}
          <Card>
            <CardHeader>
              <CardTitle>Categories</CardTitle>
              <CardDescription>Organize your blog with categories</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between rounded-md border p-3">
                  <div>
                    <h4 className="font-medium">E-commerce</h4>
                    <p className="text-sm text-muted-foreground">12 articles</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700">
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between rounded-md border p-3">
                  <div>
                    <h4 className="font-medium">Marketing</h4>
                    <p className="text-sm text-muted-foreground">8 articles</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700">
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between rounded-md border p-3">
                  <div>
                    <h4 className="font-medium">Photography</h4>
                    <p className="text-sm text-muted-foreground">5 articles</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700">
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between rounded-md border p-3">
                  <div>
                    <h4 className="font-medium">Mobile</h4>
                    <p className="text-sm text-muted-foreground">3 articles</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700">
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between rounded-md border p-3">
                  <div>
                    <h4 className="font-medium">Sustainability</h4>
                    <p className="text-sm text-muted-foreground">2 articles</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700">
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tags Cloud */}
          <Card>
            <CardHeader>
              <CardTitle>Tags</CardTitle>
              <CardDescription>Popular tags used across articles</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="text-sm">
                  ecommerce (15)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  marketing (12)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  tips (10)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  product (8)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  photography (7)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  mobile (6)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  optimization (6)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  trends (5)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  2024 (5)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  sales (4)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  black friday (4)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  holiday (4)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  ux (3)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  sustainability (3)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  eco-friendly (2)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  packaging (2)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  campaigns (2)
                </Badge>
                <Badge variant="outline" className="text-sm">
                  preparation (1)
                </Badge>
              </div>
            </CardContent>
            <CardFooter>
              <div className="space-y-2 w-full">
                <Label htmlFor="new-tag">Add New Tag</Label>
                <div className="flex gap-2">
                  <Input id="new-tag" placeholder="Enter tag name" />
                  <Button>Add</Button>
                </div>
              </div>
            </CardFooter>
          </Card>
        </div>
      </TabsContent>
    </Tabs>
  )
}
