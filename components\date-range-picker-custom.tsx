"use client"

import { useState, useEffect } from "react"
import { format, addDays, subDays, startOfYear } from "date-fns"
import { id } from "date-fns/locale"
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRange } from "react-day-picker"
import { cn } from "@/lib/utils"

interface DateRangePickerKustomProps {
  value: DateRange | undefined
  onChange: (value: DateRange | undefined) => void
  className?: string
}

export function DateRangePickerKustom({
  value,
  onChange,
  className,
}: DateRangePickerKustomProps) {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false)
  const [selectedOption, setSelectedOption] = useState<string | undefined>(undefined)
  const [customDateRange, setCustomDateRange] = useState<DateRange | undefined>(undefined)
  const [displayValue, setDisplayValue] = useState<DateRange | undefined>(undefined)
  
  // Inisialisasi month view dengan bulan saat ini, bukan tanggal tetap
  const today = new Date()
  const [monthView, setMonthView] = useState<Date>(new Date(today.getFullYear(), today.getMonth(), 1))
  
  // Inisialisasi next month view sebagai bulan berikutnya
  const nextMonthDate = new Date(today.getFullYear(), today.getMonth() + 1, 1)
  const [nextMonthView, setNextMonthView] = useState<Date>(nextMonthDate)
  
  const [showMonthYearLeft, setShowMonthYearLeft] = useState(false)
  const [showMonthYearRight, setShowMonthYearRight] = useState(false)

  // Update date range when option changes
  useEffect(() => {
    if (!isCalendarOpen) return;
    
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    let dateRange: DateRange | undefined
    
    switch (selectedOption) {
      case "hari-ini":
        dateRange = { from: today, to: today }
        
        // Update kalender ke bulan saat ini
        const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        setMonthView(thisMonth);
        
        const nextThisMonth = new Date(thisMonth);
        nextThisMonth.setMonth(nextThisMonth.getMonth() + 1);
        setNextMonthView(nextThisMonth);
        break
        
      case "kemarin":
        const yesterday = subDays(today, 1)
        dateRange = { from: yesterday, to: yesterday }
        
        // Update kalender ke bulan dari kemarin
        const yesterdayMonth = new Date(yesterday.getFullYear(), yesterday.getMonth(), 1);
        setMonthView(yesterdayMonth);
        
        const nextYesterdayMonth = new Date(yesterdayMonth);
        nextYesterdayMonth.setMonth(nextYesterdayMonth.getMonth() + 1);
        setNextMonthView(nextYesterdayMonth);
        break
        
      case "7-hari-terakhir":
        dateRange = { from: subDays(today, 6), to: today }
        
        // Update kalender ke bulan dari 7 hari yang lalu
        const weekAgo = subDays(today, 6);
        const weekAgoMonth = new Date(weekAgo.getFullYear(), weekAgo.getMonth(), 1);
        setMonthView(weekAgoMonth);
        
        // Jika minggu lalu berbeda bulan, tampilkan bulan saat ini di sebelah kanan
        // Jika tidak, tampilkan bulan berikutnya
        if (weekAgo.getMonth() !== today.getMonth()) {
          const thisMonthView = new Date(today.getFullYear(), today.getMonth(), 1);
          setNextMonthView(thisMonthView);
        } else {
          const nextWeekAgoMonth = new Date(weekAgoMonth);
          nextWeekAgoMonth.setMonth(nextWeekAgoMonth.getMonth() + 1);
          setNextMonthView(nextWeekAgoMonth);
        }
        break
        
      case "bulan-ini":
        const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1)
        dateRange = { from: thisMonthStart, to: today }
        
        // Update kalender ke bulan ini
        setMonthView(thisMonthStart);
        
        const nextThisMonthStart = new Date(thisMonthStart);
        nextThisMonthStart.setMonth(nextThisMonthStart.getMonth() + 1);
        setNextMonthView(nextThisMonthStart);
        break
        
      case "bulan-lalu":
        const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0)
        dateRange = { from: lastMonthStart, to: lastMonthEnd }
        
        // Update kalender ke bulan lalu
        setMonthView(lastMonthStart);
        
        // Set kalender kedua ke bulan ini
        const currentMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        setNextMonthView(currentMonth);
        break
        
      case "tahun-ini":
        const thisYearStart = startOfYear(today)
        dateRange = { from: thisYearStart, to: today }
        
        // Update kalender ke Januari tahun ini
        const janThisYear = new Date(today.getFullYear(), 0, 1);
        setMonthView(janThisYear);
        
        // Set kalender kedua ke Februari tahun ini
        const febThisYear = new Date(today.getFullYear(), 1, 1);
        setNextMonthView(febThisYear);
        break
        
      case "kustom":
        dateRange = customDateRange
        break
        
      default:
        dateRange = undefined
    }
    
    setCustomDateRange(dateRange)
  }, [selectedOption, isCalendarOpen])

  // Format display untuk date range
  const formatDateRange = (range: DateRange) => {
    if (!range.from) return ""
    
    if (!range.to) {
      return format(range.from, "dd MMM yyyy", { locale: id })
    }
    
    if (range.from.getDate() === range.to.getDate() && 
        range.from.getMonth() === range.to.getMonth() &&
        range.from.getFullYear() === range.to.getFullYear()) {
      return format(range.from, "dd MMM yyyy", { locale: id })
    }
    
    return `${format(range.from, "dd MMM yyyy", { locale: id })} - ${format(range.to, "dd MMM yyyy", { locale: id })}`
  }

  const handleNextMonth = () => {
    const next = new Date(monthView)
    next.setMonth(next.getMonth() + 1)
    setMonthView(next)
    
    const nextNext = new Date(nextMonthView)
    nextNext.setMonth(nextNext.getMonth() + 1)
    setNextMonthView(nextNext)
  }

  const prevMonth = () => {
    const prev = new Date(monthView)
    prev.setMonth(prev.getMonth() - 1)
    setMonthView(prev)
    
    const prevNext = new Date(nextMonthView)
    prevNext.setMonth(prevNext.getMonth() - 1)
    setNextMonthView(prevNext)
  }

  const handleCalendarSelect = (range: DateRange | undefined) => {
    setCustomDateRange(range)
    if (range?.from && range?.to) {
      setSelectedOption("kustom")
      onChange(range)
    }
  }

  // Handle ketika tombol Update ditekan
  const handleUpdate = () => {
    setDisplayValue(customDateRange);
    onChange(customDateRange);
    
    // Simpan ke localStorage ketika update
    try {
      const rangeToSave = {
        from: customDateRange?.from ? customDateRange.from.toISOString() : null,
        to: customDateRange?.to ? customDateRange.to.toISOString() : null
      };
      localStorage.setItem('storesDateRange', JSON.stringify(rangeToSave));
      if (selectedOption) {
        localStorage.setItem('storesDateOption', selectedOption);
      }
    } catch (error) {
      console.error("Error saving date range to localStorage:", error);
    }
    
    setIsCalendarOpen(false);
  };

  // Handle ketika tombol Reset ditekan
  const handleReset = () => {
    // Reset ke undefined (tidak ada tanggal yang dipilih)
    setCustomDateRange(undefined);
    setDisplayValue(undefined);
    setSelectedOption(undefined);
    onChange(undefined);
    
    // Hapus data dari localStorage saat reset
    localStorage.removeItem('storesDateRange');
    localStorage.removeItem('storesDateOption');
  };

  // Handle ketika tombol Batalkan ditekan
  const handleCancel = () => {
    // Kembalikan ke nilai sebelumnya
    setCustomDateRange(displayValue);
    
    // Tutup date picker
    setIsCalendarOpen(false);
  };

  // Muat data dari localStorage jika tersedia
  useEffect(() => {
    try {
      const savedRangeStr = localStorage.getItem('storesDateRange');
      const savedOption = localStorage.getItem('storesDateOption');
      
      if (savedRangeStr && savedOption) {
        // Parse data dari localStorage
        const savedRange = JSON.parse(savedRangeStr);
        
        // Konversi string tanggal menjadi objek Date
        let dateRange: DateRange | undefined;
        
        if (savedRange.from) {
          dateRange = {
            from: new Date(savedRange.from),
            ...(savedRange.to ? { to: new Date(savedRange.to) } : {})
          };
          
          // Set state dengan data dari localStorage
          setSelectedOption(savedOption);
          setCustomDateRange(dateRange);
          setDisplayValue(dateRange);
          onChange(dateRange);
          
          // Atur tampilan kalender sesuai dengan tanggal yang dipilih
          if (dateRange && dateRange.from) {
            const month = new Date(dateRange.from);
            month.setDate(1);
            setMonthView(month);
            
            const nextMonth = new Date(month);
            nextMonth.setMonth(nextMonth.getMonth() + 1);
            setNextMonthView(nextMonth);
          }
        }
      }
      // Tidak perlu fallback ke default, biarkan undefined
    } catch (error) {
      console.error("Error loading date range from localStorage:", error);
      // Jangan setel default date saat error
    }
  }, []);

  useEffect(() => {
    if (value) {
      setCustomDateRange(value);
      setDisplayValue(value);
    }
  }, [value]);

  const handleMonthChangeLeft = (month: string) => {
    const newDate = new Date(monthView);
    newDate.setMonth(parseInt(month));
    setMonthView(newDate);
  };

  const handleYearChangeLeft = (year: string) => {
    const newDate = new Date(monthView);
    newDate.setFullYear(parseInt(year));
    setMonthView(newDate);
  };

  const handleMonthChangeRight = (month: string) => {
    const newDate = new Date(nextMonthView);
    newDate.setMonth(parseInt(month));
    setNextMonthView(newDate);
  };

  const handleYearChangeRight = (year: string) => {
    const newDate = new Date(nextMonthView);
    newDate.setFullYear(parseInt(year));
    setNextMonthView(newDate);
  };

  // Daftar bulan dan tahun untuk dropdown
  const months = [
    { value: "0", label: "Januari" },
    { value: "1", label: "Februari" },
    { value: "2", label: "Maret" },
    { value: "3", label: "April" },
    { value: "4", label: "Mei" },
    { value: "5", label: "Juni" },
    { value: "6", label: "Juli" },
    { value: "7", label: "Agustus" },
    { value: "8", label: "September" },
    { value: "9", label: "Oktober" },
    { value: "10", label: "November" },
    { value: "11", label: "Desember" },
  ];

  const years = Array.from({ length: 10 }, (_, i) => {
    const year = new Date().getFullYear() - 5 + i;
    return { value: year.toString(), label: year.toString() };
  });

  const MonthYearSelector = ({ 
    date, 
    showSelector, 
    setShowSelector, 
    handleMonthChange, 
    handleYearChange,
    isLeft = false
  }: { 
    date: Date, 
    showSelector: boolean, 
    setShowSelector: (show: boolean) => void,
    handleMonthChange: (month: string) => void,
    handleYearChange: (year: string) => void,
    isLeft?: boolean
  }) => {
    const [tempMonth, setTempMonth] = useState(date.getMonth().toString());
    const [tempYear, setTempYear] = useState(date.getFullYear().toString());
    
    // Reset nilai tempMonth dan tempYear saat dropdown dibuka atau date berubah
    useEffect(() => {
      setTempMonth(date.getMonth().toString());
      setTempYear(date.getFullYear().toString());
    }, [showSelector, date]);
    
    const handleApply = () => {
      console.log('Menerapkan perubahan bulan/tahun:', {
        bulan: months.find(m => m.value === tempMonth)?.label,
        tahun: tempYear,
        isLeft: isLeft
      });
      
      // Buat objek Date baru
      const newDate = new Date();
      newDate.setFullYear(parseInt(tempYear));
      newDate.setMonth(parseInt(tempMonth));
      newDate.setDate(1);
      
      // Set opsi ke "kustom" untuk memungkinkan interaksi dengan kalender
      setSelectedOption("kustom");
      
      // Update kedua kalender (kiri dan kanan)
      if (isLeft) {
        console.log('Mengupdate kalender kiri ke:', newDate);
        setMonthView(newDate);
        
        // Update kalender kanan juga (bulan berikutnya)
        const nextMonth = new Date(newDate);
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        setNextMonthView(nextMonth);
      } else {
        console.log('Mengupdate kalender kanan ke:', newDate);
        setNextMonthView(newDate);
        
        // Update kalender kiri juga (bulan sebelumnya)
        const prevMonth = new Date(newDate);
        prevMonth.setMonth(prevMonth.getMonth() - 1);
        setMonthView(prevMonth);
      }
      
      // Force rerender calendar dengan mengupdate customDateRange
      if (customDateRange?.from) {
        const updatedRange = { 
          from: new Date(customDateRange.from),
          to: customDateRange.to ? new Date(customDateRange.to) : undefined
        };
        setCustomDateRange(updatedRange);
      }
      
      // Tutup selector
      setShowSelector(false);
    };
    
    return (
      <div className="relative">
        <div
          className="flex items-center gap-1 cursor-pointer"
          onClick={() => setShowSelector(!showSelector)}
        >
          <span className="text-sm font-medium">
            {format(date, "MMM", { locale: id })} {format(date, "yyyy")}
          </span>
          <ChevronDown className="h-4 w-4" />
        </div>
        
        {showSelector && (
          <div className="absolute top-full left-0 mt-1 p-2 bg-background border shadow-md rounded-md z-10 w-[220px]">
            <div className="grid grid-cols-2 gap-2 mb-2">
              <div>
                <div className="text-xs mb-1 font-medium">Bulan</div>
                <Select
                  value={tempMonth}
                  onValueChange={setTempMonth}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="Pilih bulan" />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map((month) => (
                      <SelectItem key={month.value} value={month.value}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <div className="text-xs mb-1 font-medium">Tahun</div>
                <Select
                  value={tempYear}
                  onValueChange={setTempYear}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="Pilih tahun" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year.value} value={year.value}>
                        {year.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Button 
              size="sm" 
              className="w-full text-xs"
              onClick={handleApply}
            >
              Terapkan
            </Button>
          </div>
        )}
      </div>
    );
  };

  const handleOptionChange = (option: string) => {
    setSelectedOption(option);
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    switch (option) {
      case "hari-ini":
        // Set kalender kiri ke bulan saat ini
        const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        setMonthView(thisMonth);
        
        // Set kalender kanan ke bulan berikutnya
        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
        setNextMonthView(nextMonth);
        break;
        
      case "kemarin":
        // Kemarin juga menggunakan bulan saat ini
        const yesterdayMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        setMonthView(yesterdayMonth);
        
        const yesterdayNextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
        setNextMonthView(yesterdayNextMonth);
        break;
        
      case "7-hari-terakhir":
        // 7 hari terakhir - tampilkan bulan saat ini
        const last7DaysMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        setMonthView(last7DaysMonth);
        
        const last7DaysNextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
        setNextMonthView(last7DaysNextMonth);
        break;
        
      case "bulan-ini":
        // Set kalender kiri ke bulan ini
        const currentMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        setMonthView(currentMonth);
        
        // Set kalender kanan ke bulan berikutnya
        const afterCurrentMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
        setNextMonthView(afterCurrentMonth);
        break;
        
      case "bulan-lalu":
        // Set kalender kiri ke bulan lalu
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        setMonthView(lastMonth);
        
        // Set kalender kanan ke bulan ini
        const afterLastMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        setNextMonthView(afterLastMonth);
        break;
        
      case "tahun-ini":
        // Set kalender kiri ke Januari tahun ini
        const thisYear = new Date(today.getFullYear(), 0, 1);
        setMonthView(thisYear);
        
        // Set kalender kanan ke Februari tahun ini
        const febThisYear = new Date(today.getFullYear(), 1, 1);
        setNextMonthView(febThisYear);
        break;
        
      case "kustom":
        // Untuk kustom, biarkan user memilih sendiri
        // Tidak perlu mengubah tampilan kalender
        break;
        
      default:
        // Default ke bulan saat ini
        const defaultMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        setMonthView(defaultMonth);
        
        const defaultNextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
        setNextMonthView(defaultNextMonth);
    }
  }

  return (
    <div className={className}>
      <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-[240px] justify-start text-left font-normal",
              !displayValue && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {displayValue ? formatDateRange(displayValue) : "Pilih tanggal"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex flex-col md:flex-row">
            {/* Kolom kiri dengan opsi tanggal */}
            <div className="border-r p-4 min-w-[190px]">
              <RadioGroup
                value={selectedOption}
                onValueChange={handleOptionChange}
                className="space-y-3"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem id="hari-ini" value="hari-ini" />
                  <Label htmlFor="hari-ini">Hari Ini</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem id="kemarin" value="kemarin" />
                  <Label htmlFor="kemarin">Kemarin</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem id="7-hari-terakhir" value="7-hari-terakhir" />
                  <Label htmlFor="7-hari-terakhir">7 hari terakhir</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem id="bulan-ini" value="bulan-ini" />
                  <Label htmlFor="bulan-ini">Bulan ini</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem id="bulan-lalu" value="bulan-lalu" />
                  <Label htmlFor="bulan-lalu">Bulan lalu</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem id="tahun-ini" value="tahun-ini" />
                  <Label htmlFor="tahun-ini">Tahun ini</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem id="kustom" value="kustom" />
                  <Label htmlFor="kustom">Kustom</Label>
                </div>
              </RadioGroup>
            </div>
            
            {/* Kolom kanan dengan kalender */}
            <div className="p-4 min-w-[500px]">
              <div className="flex items-center justify-between mb-4">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-7 w-7 bg-transparent p-0"
                  onClick={prevMonth}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                
                <div className="flex justify-center gap-10">
                  <MonthYearSelector
                    date={monthView}
                    showSelector={showMonthYearLeft}
                    setShowSelector={setShowMonthYearLeft}
                    handleMonthChange={handleMonthChangeLeft}
                    handleYearChange={handleYearChangeLeft}
                    isLeft={true}
                  />
                  
                  <MonthYearSelector
                    date={nextMonthView}
                    showSelector={showMonthYearRight}
                    setShowSelector={setShowMonthYearRight}
                    handleMonthChange={handleMonthChangeRight}
                    handleYearChange={handleYearChangeRight}
                  />
                </div>
                
                <Button
                  variant="outline"
                  size="icon"
                  className="h-7 w-7 bg-transparent p-0"
                  onClick={handleNextMonth}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex gap-4">
                <Calendar
                  key={`left-calendar-${monthView.getMonth()}-${monthView.getFullYear()}`}
                  mode="range"
                  selected={customDateRange}
                  onSelect={handleCalendarSelect}
                  month={monthView}
                  onMonthChange={setMonthView}
                  weekStartsOn={1}
                  locale={id}
                  className="border-0 w-auto p-0"
                  numberOfMonths={1}
                  disabled={selectedOption !== "kustom"}
                  defaultMonth={new Date(2025, 4, 26)}
                  classNames={{
                    months: "",
                    month: "space-y-2",
                    caption: "flex justify-center pt-1 relative items-center",
                    caption_label: "text-sm font-medium hidden",
                    nav: "hidden",
                    table: "w-full border-collapse",
                    head_row: "flex",
                    head_cell: "text-muted-foreground rounded-md w-8 font-extralight text-[0.2rem]",
                    row: "flex w-full",
                    cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                    day: "h-6 w-6 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-md",
                    day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                    day_today: "bg-accent text-accent-foreground",
                    day_outside: "text-muted-foreground opacity-50",
                    day_disabled: "text-muted-foreground opacity-50",
                    day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                    day_hidden: "invisible",
                  }}
                />
                <Calendar
                  key={`right-calendar-${nextMonthView.getMonth()}-${nextMonthView.getFullYear()}`}
                  mode="range"
                  selected={customDateRange}
                  onSelect={handleCalendarSelect}
                  month={nextMonthView}
                  onMonthChange={setNextMonthView}
                  weekStartsOn={1}
                  locale={id}
                  className="border-0 w-auto p-0"
                  numberOfMonths={1}
                  disabled={selectedOption !== "kustom"}
                  classNames={{
                    months: "",
                    month: "space-y-2",
                    caption: "flex justify-center pt-1 relative items-center",
                    caption_label: "text-sm font-medium hidden",
                    nav: "hidden",
                    table: "w-full border-collapse",
                    head_row: "flex",
                    head_cell: "text-muted-foreground rounded-md w-8 font-extralight text-[0.2rem]",
                    row: "flex w-full",
                    cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                    day: "h-6 w-6 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-md",
                    day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                    day_today: "bg-accent text-accent-foreground",
                    day_outside: "text-muted-foreground opacity-50",
                    day_disabled: "text-muted-foreground opacity-50",
                    day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                    day_hidden: "invisible",
                  }}
                />
              </div>

              {selectedOption === "kustom" && customDateRange && customDateRange.from && (
                <div className="border-t border-b py-3 my-3">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <CalendarIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                      <div>
                        <p className="text-xs text-muted-foreground">Tanggal Awal</p>
                        <p className="text-sm font-medium">
                          {format(customDateRange.from, "dd MMM yyyy", { locale: id })}
                        </p>
                      </div>
                    </div>
                    
                    {customDateRange.to && customDateRange.from !== customDateRange.to && (
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                        <div>
                          <p className="text-xs text-muted-foreground">Tanggal Akhir</p>
                          <p className="text-sm font-medium">
                            {format(customDateRange.to, "dd MMM yyyy", { locale: id })}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="flex justify-between mt-4">
                <Button variant="outline" size="sm" onClick={handleCancel}>
                  Batalkan
                </Button>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleReset}>
                    Reset
                  </Button>
                  <Button size="sm" onClick={handleUpdate}>
                    Update
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
} 