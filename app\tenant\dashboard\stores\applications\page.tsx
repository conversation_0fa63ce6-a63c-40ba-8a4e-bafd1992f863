"use client"

import { useState, useEffect } from "react"
import { useStoreApplications } from "@/hooks/use-store-applications"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { toast } from "sonner"
import {
  ArrowLeft,
  Search,
  Filter,
  MoreVertical,
  Check,
  X,
  Clock,
  User,
  Mail,
  Phone,
  MapPin,
  Building,
  FileText,
  Star,
  Eye,
  Edit,
  Trash2,
  MessageSquare,
  Calendar,
  Globe,
  Instagram
} from "lucide-react"
import Link from "next/link"

// Helper functions

function getStatusBadge(status: string) {
  switch (status) {
    case "pending":
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
    case "approved":
      return <Badge variant="default" className="bg-green-100 text-green-800"><Check className="h-3 w-3 mr-1" />Approved</Badge>
    case "rejected":
      return <Badge variant="destructive"><X className="h-3 w-3 mr-1" />Rejected</Badge>
    case "under_review":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800"><FileText className="h-3 w-3 mr-1" />Under Review</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export default function StoreApplicationsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Use custom hook for store applications
  const {
    applications,
    loading,
    error,
    fetchApplications,
    approveApplication,
    rejectApplication,
    updateApplicationStatus,
    deleteApplication
  } = useStoreApplications()

  // Dialog states
  const [detailDialog, setDetailDialog] = useState({ open: false, application: null as any })
  const [approveDialog, setApproveDialog] = useState({ open: false, application: null as any })
  const [rejectDialog, setRejectDialog] = useState({ open: false, application: null as any })
  const [editDialog, setEditDialog] = useState({ open: false, application: null as any })
  const [deleteDialog, setDeleteDialog] = useState({ open: false, application: null as any })

  // Form states
  const [approveReason, setApproveReason] = useState("")
  const [rejectReason, setRejectReason] = useState("")
  const [editStatus, setEditStatus] = useState("")
  const [editNotes, setEditNotes] = useState("")

  // Fetch applications when filters change
  useEffect(() => {
    fetchApplications({ status: statusFilter, search: searchTerm })
  }, [statusFilter, searchTerm, fetchApplications]) // Now safe to include fetchApplications

  // CRUD Functions
  const openDetailDialog = (application: any) => {
    setDetailDialog({ open: true, application })
  }

  const openApproveDialog = (application: any) => {
    setApproveDialog({ open: true, application })
    setApproveReason("")
  }

  const openRejectDialog = (application: any) => {
    setRejectDialog({ open: true, application })
    setRejectReason("")
  }

  const openEditDialog = (application: any) => {
    setEditDialog({ open: true, application })
    setEditStatus(application.status)
    setEditNotes("")
  }

  const openDeleteDialog = (application: any) => {
    setDeleteDialog({ open: true, application })
  }

  const handleApprove = async () => {
    if (!approveDialog.application) return

    const success = await approveApplication(approveDialog.application.id, approveReason)
    if (success) {
      setApproveDialog({ open: false, application: null })
      setApproveReason("")
    }
  }

  const handleReject = async () => {
    if (!rejectDialog.application) return

    const success = await rejectApplication(rejectDialog.application.id, rejectReason)
    if (success) {
      setRejectDialog({ open: false, application: null })
      setRejectReason("")
    }
  }

  const handleEdit = async () => {
    if (!editDialog.application) return

    const success = await updateApplicationStatus(editDialog.application.id, editStatus, editNotes)
    if (success) {
      setEditDialog({ open: false, application: null })
      setEditStatus("")
      setEditNotes("")
    }
  }

  const handleDelete = async () => {
    if (!deleteDialog.application) return

    const success = await deleteApplication(deleteDialog.application.id)
    if (success) {
      setDeleteDialog({ open: false, application: null })
    }
  }

  // Applications are already filtered by the API based on search and status

  const stats = {
    total: applications.length,
    pending: applications.filter(app => app.status === "pending").length,
    approved: applications.filter(app => app.status === "approved").length,
    rejected: applications.filter(app => app.status === "rejected").length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/stores">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Store Applications</h1>
            <p className="text-muted-foreground">
              Kelola aplikasi pendaftaran store baru
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <Check className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <X className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari aplikasi, nama toko, atau pemilik..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="pending">Pending</option>
              <option value="under_review">Under Review</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Applications List */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Memuat aplikasi...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-4">
        {!loading && applications.map((app) => (
          <Card key={app.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <CardTitle className="text-lg">{app.store_name}</CardTitle>
                    {getStatusBadge(app.status)}
                  </div>
                  <CardDescription className="flex items-center gap-4">
                    <span className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      {app.owner_name}
                    </span>
                    <span className="flex items-center gap-1">
                      <Building className="h-4 w-4" />
                      {app.category}
                    </span>
                    <span className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      {app.location}
                    </span>
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => openDetailDialog(app)}>
                    <Eye className="h-4 w-4 mr-2" />
                    Detail
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => openEditDialog(app)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => openDeleteDialog(app)} className="text-red-600 hover:text-red-700">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Hapus
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                <div>
                  <h4 className="font-medium text-sm mb-2">Informasi Kontak</h4>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <p className="flex items-center gap-2">
                      <Mail className="h-3 w-3" />
                      {app.email}
                    </p>
                    <p className="flex items-center gap-2">
                      <Phone className="h-3 w-3" />
                      {app.phone}
                    </p>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-sm mb-2">Detail Bisnis</h4>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <p>Plan: {app.business_plan}</p>
                    <p>Pengalaman: {app.experience}</p>
                    <p>Target Revenue: {app.expected_revenue}</p>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-sm mb-2">Status & Tanggal</h4>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <p>Diajukan: {formatDate(app.submitted_date)}</p>
                    <p>Dokumen: {app.documents.length} file</p>
                  </div>
                </div>
              </div>

              {app.description && (
                <div className="mt-4">
                  <h4 className="font-medium text-sm mb-2">Deskripsi Bisnis</h4>
                  <p className="text-sm text-muted-foreground">{app.description}</p>
                </div>
              )}

              {app.reject_reason && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="font-medium text-sm text-red-800 mb-1">Alasan Penolakan</h4>
                  <p className="text-sm text-red-700">{app.reject_reason}</p>
                </div>
              )}

              {app.status === "pending" && (
                <div className="flex gap-2 mt-4">
                  <Button size="sm" className="bg-green-600 hover:bg-green-700" onClick={() => openApproveDialog(app)}>
                    <Check className="h-4 w-4 mr-2" />
                    Approve
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => openEditDialog(app)}>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Review
                  </Button>
                  <Button size="sm" variant="destructive" onClick={() => openRejectDialog(app)}>
                    <X className="h-4 w-4 mr-2" />
                    Reject
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detail Dialog */}
      <Dialog open={detailDialog.open} onOpenChange={(open) => setDetailDialog({ open, application: null })}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Detail Aplikasi Store</DialogTitle>
            <DialogDescription>
              Informasi lengkap aplikasi pendaftaran store
            </DialogDescription>
          </DialogHeader>
          {detailDialog.application && (
            <div className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h3 className="font-semibold mb-3">Informasi Store</h3>
                  <div className="space-y-2 text-sm">
                    <p><strong>Nama Store:</strong> {detailDialog.application.store_name}</p>
                    <p><strong>Kategori:</strong> {detailDialog.application.category}</p>
                    <p><strong>Lokasi:</strong> {detailDialog.application.location}</p>
                    <p><strong>Status:</strong> {getStatusBadge(detailDialog.application.status)}</p>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold mb-3">Informasi Pemilik</h3>
                  <div className="space-y-2 text-sm">
                    <p><strong>Nama:</strong> {detailDialog.application.owner_name}</p>
                    <p><strong>Email:</strong> {detailDialog.application.email}</p>
                    <p><strong>Telepon:</strong> {detailDialog.application.phone}</p>
                    <p><strong>Pengalaman:</strong> {detailDialog.application.experience}</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-3">Detail Bisnis</h3>
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <p className="text-sm"><strong>Business Plan:</strong> {detailDialog.application.business_plan}</p>
                  </div>
                  <div>
                    <p className="text-sm"><strong>Target Revenue:</strong> {detailDialog.application.expected_revenue}</p>
                  </div>
                  <div>
                    <p className="text-sm"><strong>Tanggal Submit:</strong> {formatDate(detailDialog.application.submitted_date)}</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-3">Deskripsi Bisnis</h3>
                <p className="text-sm text-muted-foreground">{detailDialog.application.description}</p>
              </div>

              <div>
                <h3 className="font-semibold mb-3">Dokumen</h3>
                <div className="flex flex-wrap gap-2">
                  {detailDialog.application.documents.map((doc: string, index: number) => (
                    <Badge key={index} variant="outline">{doc}</Badge>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-3">Media Sosial</h3>
                <div className="space-y-2 text-sm">
                  {detailDialog.application.social_media.instagram && (
                    <p className="flex items-center gap-2">
                      <Instagram className="h-4 w-4" />
                      {detailDialog.application.social_media.instagram}
                    </p>
                  )}
                  {detailDialog.application.social_media.website && (
                    <p className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      {detailDialog.application.social_media.website}
                    </p>
                  )}
                </div>
              </div>

              {detailDialog.application.reject_reason && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="font-medium text-sm text-red-800 mb-1">Alasan Penolakan</h4>
                  <p className="text-sm text-red-700">{detailDialog.application.reject_reason}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Approve Dialog */}
      <Dialog open={approveDialog.open} onOpenChange={(open) => setApproveDialog({ open, application: null })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Setujui Aplikasi Store</DialogTitle>
            <DialogDescription>
              Anda akan menyetujui aplikasi store "{approveDialog.application?.store_name}". Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="approve-reason">Catatan Persetujuan (Opsional)</Label>
              <Textarea
                id="approve-reason"
                placeholder="Tambahkan catatan untuk persetujuan ini..."
                value={approveReason}
                onChange={(e) => setApproveReason(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setApproveDialog({ open: false, application: null })}>
              Batal
            </Button>
            <Button onClick={handleApprove} className="bg-green-600 hover:bg-green-700">
              <Check className="h-4 w-4 mr-2" />
              Setujui Aplikasi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={rejectDialog.open} onOpenChange={(open) => setRejectDialog({ open, application: null })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Tolak Aplikasi Store</DialogTitle>
            <DialogDescription>
              Anda akan menolak aplikasi store "{rejectDialog.application?.store_name}". Berikan alasan penolakan yang jelas.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reject-reason">Alasan Penolakan *</Label>
              <Textarea
                id="reject-reason"
                placeholder="Jelaskan alasan penolakan aplikasi ini..."
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                rows={4}
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRejectDialog({ open: false, application: null })}>
              Batal
            </Button>
            <Button
              onClick={handleReject}
              variant="destructive"
              disabled={!rejectReason.trim()}
            >
              <X className="h-4 w-4 mr-2" />
              Tolak Aplikasi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={editDialog.open} onOpenChange={(open) => setEditDialog({ open, application: null })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Status Aplikasi</DialogTitle>
            <DialogDescription>
              Ubah status aplikasi store "{editDialog.application?.store_name}".
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-status">Status</Label>
              <Select value={editStatus} onValueChange={setEditStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="edit-notes">Catatan (Opsional)</Label>
              <Textarea
                id="edit-notes"
                placeholder="Tambahkan catatan untuk perubahan status..."
                value={editNotes}
                onChange={(e) => setEditNotes(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialog({ open: false, application: null })}>
              Batal
            </Button>
            <Button onClick={handleEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Update Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <AlertDialog open={deleteDialog.open} onOpenChange={(open) => setDeleteDialog({ open, application: null })}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus Aplikasi Store</AlertDialogTitle>
            <AlertDialogDescription>
              Anda yakin ingin menghapus aplikasi store "{deleteDialog.application?.store_name}"?
              Tindakan ini tidak dapat dibatalkan dan semua data aplikasi akan hilang permanen.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
              <Trash2 className="h-4 w-4 mr-2" />
              Hapus Aplikasi
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}