"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import {
  ArrowLeft,
  Package,
  Truck,
  CheckCircle,
  MapPin,
  Download,
  MessageSquare,
  RefreshCw,
  XCircle,
  Clock,
  CreditCard,
  Receipt,
  Star,
  ArrowRight,
  AlertCircle,
  FileText,
} from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"

// Interface untuk items
interface OrderItem {
  id?: string;
  name: string;
  quantity: number;
  price: number;
  image: string;
  options?: string[];
  store?: string;
}

// Interface untuk shipping
interface Shipping {
  cost?: number;
  method: string;
  status: string;
  trackingNumber: string | null;
  eta: string | null;
  deliveredAt?: string;
}

// Interface untuk payment
interface Payment {
  method: string;
  cardLast4?: string;
  status: string;
}

// Interface untuk timeline
interface TimelineStep {
  status: string;
  date: string | null;
  completed: boolean;
}

// Interface untuk address
interface Address {
  name: string;
  street: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
  phone: string;
}

// Interface untuk order
interface Order {
  id: string;
  date: string;
  status: string;
  items: OrderItem[];
  subtotal?: number;
  total: number;
  shipping: Shipping;
  tax?: number;
  discount?: number;
  payment?: Payment;
  timeline?: TimelineStep[];
  address?: Address;
  isEligibleForReturn?: boolean;
  cancellationReason?: string;
}

// Default order data (akan diisi dari API)
const defaultOrder: Order = {
  id: "",
  date: "",
  status: "processing",
  items: [],
  subtotal: 0,
  shipping: {
    cost: 0,
    method: "",
    status: "",
    trackingNumber: null,
    eta: null,
  },
  tax: 0,
  discount: 0,
  total: 0,
  payment: {
    method: "Credit Card",
    cardLast4: "1234",
    status: "paid",
  },
  timeline: [
    { status: "ordered", date: null, completed: false },
    { status: "processing", date: null, completed: false },
    { status: "shipped", date: null, completed: false },
    { status: "delivered", date: null, completed: false },
  ],
  address: {
    name: "John Doe",
    street: "Jl. Sudirman No. 123",
    city: "Jakarta",
    province: "DKI Jakarta",
    postalCode: "12930",
    country: "Indonesia",
    phone: "+62812345678",
  },
  isEligibleForReturn: false,
}

export default function OrderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const orderId = params.id as string
  const [order, setOrder] = useState<Order>(defaultOrder)
  const [activeTab, setActiveTab] = useState("details")
  const [loading, setLoading] = useState(true)

  // Fetch order data dari API
  useEffect(() => {
    const fetchOrderDetail = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/orders/${orderId}`);
        
        if (!response.ok) {
          throw new Error('Gagal memuat data pesanan');
        }
        
        const orderData = await response.json();
        
        // Prepare timeline based on order status
        const timeline = [
          { status: "ordered", date: orderData.date, completed: true },
          { status: "processing", date: orderData.status === "processing" ? orderData.date : null, completed: ["processing", "shipped", "delivered"].includes(orderData.status) },
          { status: "shipped", date: orderData.status === "shipped" ? new Date().toISOString() : null, completed: ["shipped", "delivered"].includes(orderData.status) },
          { status: "delivered", date: orderData.status === "delivered" ? orderData.shipping.deliveredAt : null, completed: ["delivered"].includes(orderData.status) },
        ];
        
        // Prepare full order data dengan menggabungkan data dari API dan default values
        const fullOrderData: Order = {
          ...orderData,
          subtotal: orderData.total,
          shipping: {
            ...orderData.shipping,
            cost: 20000, // Default shipping cost
          },
          tax: Math.round(orderData.total * 0.1), // Default tax 10%
          discount: 0,
          payment: {
            method: "Credit Card",
            cardLast4: "1234",
            status: "paid",
          },
          timeline: timeline,
          address: {
            name: "John Doe",
            street: "Jl. Sudirman No. 123",
            city: "Jakarta",
            province: "DKI Jakarta",
            postalCode: "12930",
            country: "Indonesia",
            phone: "+62812345678",
          },
          isEligibleForReturn: orderData.status === "delivered",
        };
        
        setOrder(fullOrderData);
      } catch (error) {
        console.error('Error fetching order detail:', error);
        toast({
          title: "Error",
          description: "Gagal memuat detail pesanan",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    
    if (orderId) {
      fetchOrderDetail();
    }
  }, [orderId]);

  // Calculate timeline progress
  const timelineProgress = () => {
    if (!order.timeline) return 0;
    const completedSteps = order.timeline.filter((step) => step.completed).length
    return (completedSteps / order.timeline.length) * 100
  }

  // Format timeline status
  const formatTimelineStatus = (status: string) => {
    switch (status) {
      case "ordered":
        return "Order Placed"
      case "processing":
        return "Processing"
      case "shipped":
        return "Shipped"
      case "delivered":
        return "Delivered"
      default:
        return status.charAt(0).toUpperCase() + status.slice(1)
    }
  }

  // Render timeline icon
  const renderTimelineIcon = (status: string, completed: boolean) => {
    if (!completed) {
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 border-muted bg-background" />
      )
    }

    switch (status) {
      case "ordered":
        return (
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
            <Receipt className="h-4 w-4" />
          </div>
        )
      case "processing":
        return (
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
            <Package className="h-4 w-4" />
          </div>
        )
      case "shipped":
        return (
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
            <Truck className="h-4 w-4" />
          </div>
        )
      case "delivered":
        return (
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
            <CheckCircle className="h-4 w-4" />
          </div>
        )
      default:
        return (
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
            <Clock className="h-4 w-4" />
          </div>
        )
    }
  }

  // Fungsi untuk cancel order
  const cancelOrder = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'cancelled',
          shipping: {
            ...order.shipping,
            status: 'cancelled',
          },
          cancellationReason: 'Dibatalkan oleh pelanggan',
        }),
      });
      
      if (!response.ok) {
        throw new Error('Gagal membatalkan pesanan');
      }
      
      const updatedOrder = await response.json();
      
      // Update local state
      setOrder({
        ...order,
        status: 'cancelled',
        shipping: {
          ...order.shipping,
          status: 'cancelled',
        },
        cancellationReason: 'Dibatalkan oleh pelanggan',
      });
      
      toast({
        title: "Sukses",
        description: "Pesanan berhasil dibatalkan",
      });
    } catch (error) {
      console.error('Error cancelling order:', error);
      toast({
        title: "Error",
        description: "Gagal membatalkan pesanan",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Order Details</h1>
      </div>

      {/* Order Header */}
      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <div className="flex items-center gap-2">
            <span className="text-xl font-semibold">{order.id}</span>
            <Badge
              variant={
                order.status === "processing"
                  ? "default"
                  : order.status === "shipped"
                    ? "secondary"
                    : order.status === "delivered"
                      ? "outline"
                      : "destructive"
              }
              className="capitalize"
            >
              {order.status}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">Ordered on {new Date(order.date).toLocaleDateString("id-ID")}</p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" className="gap-1">
            <Download className="h-4 w-4" />
            Invoice
          </Button>
          <Button variant="outline" size="sm" className="gap-1">
            <MessageSquare className="h-4 w-4" />
            Support
          </Button>
          {order.status === "processing" && (
            <Button variant="destructive" size="sm" className="gap-1" onClick={cancelOrder}>
              <XCircle className="h-4 w-4" />
              Cancel Order
            </Button>
          )}
          {(order.status === "delivered" || order.status === "cancelled") && (
            <Button variant="outline" size="sm" className="gap-1">
              <RefreshCw className="h-4 w-4" />
              Reorder
            </Button>
          )}
        </div>
      </div>

      {/* Tabs Navigation */}
      <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="w-full justify-start">
          <TabsTrigger value="details" className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            Details
          </TabsTrigger>
          <TabsTrigger value="timeline" className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            Timeline
          </TabsTrigger>
          {order.status === "delivered" && (
            <TabsTrigger value="review" className="flex items-center gap-1">
              <Star className="h-4 w-4" />
              Review
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="details" className="mt-6 space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            {/* Order Items */}
            <Card className="md:col-span-2">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Order Items</CardTitle>
                {order.status === "delivered" && (
                  <Button variant="outline" size="sm" className="gap-1">
                    <Star className="h-4 w-4" />
                    Write Review
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {order.items.map((item, index) => (
                    <div key={index} className="flex gap-4">
                      <img
                        src={item.image || "/placeholder.svg"}
                        alt={item.name}
                        className="h-24 w-24 flex-shrink-0 rounded-md border border-muted object-cover"
                      />
                      <div className="flex flex-1 flex-col justify-between">
                        <div>
                          <h3 className="font-medium">{item.name}</h3>
                          <p className="text-sm text-muted-foreground">Sold by: {item.store}</p>
                          <div className="mt-1 flex flex-wrap gap-2">
                            {item.options?.map((option, idx) => (
                              <Badge key={idx} variant="outline" className="font-normal">
                                {option}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Qty: {item.quantity}</span>
                          <span className="font-medium">{formatCurrency(item.price)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <Separator className="my-6" />

                {/* Shipping Information */}
                <div className="rounded-md bg-muted p-4">
                  <div className="flex items-center gap-2">
                    <Truck className="h-5 w-5 text-primary" />
                    <h3 className="font-medium">Shipping Information</h3>
                  </div>
                  <div className="mt-2 space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Method:</span>
                      <span className="text-sm">{order.shipping.method}</span>
                    </div>
                    {order.shipping.trackingNumber && (
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Tracking Number:</span>
                        <span className="text-sm font-medium">{order.shipping.trackingNumber}</span>
                      </div>
                    )}
                    {order.shipping.eta && (
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Estimated Delivery:</span>
                        <span className="text-sm">{new Date(order.shipping.eta).toLocaleDateString("id-ID")}</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Subtotal</span>
                    <span>{formatCurrency(order.subtotal ?? 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Shipping</span>
                    <span>{formatCurrency(order.shipping.cost ?? 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Tax</span>
                    <span>{formatCurrency(order.tax ?? 0)}</span>
                  </div>
                  {(order.discount ?? 0) > 0 && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Discount</span>
                      <span className="text-green-500">-{formatCurrency(order.discount ?? 0)}</span>
                    </div>
                  )}
                  <Separator />
                  <div className="flex justify-between font-medium">
                    <span>Total</span>
                    <span>{formatCurrency(order.total)}</span>
                  </div>

                  <div className="rounded-md bg-muted p-3">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Payment Method</span>
                    </div>
                    <div className="mt-2 text-sm">
                      <p>{order.payment?.method}</p>
                      {order.payment?.cardLast4 && (
                        <p className="text-muted-foreground">**** **** **** {order.payment?.cardLast4}</p>
                      )}
                      <p className="mt-1 capitalize">
                        Status: <span className="font-medium text-green-500">{order.payment?.status}</span>
                      </p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-2">
                    {order.status === "shipped" && (
                      <Button className="w-full gap-1">
                        <Truck className="h-4 w-4" />
                        Track Package
                      </Button>
                    )}
                    {order.status === "delivered" && order.isEligibleForReturn && (
                      <Button variant="outline" className="w-full gap-1">
                        <ArrowRight className="h-4 w-4" />
                        Return/Exchange
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle>Shipping Address</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{order.address?.name}</span>
                  </div>
                  <div className="mt-2 space-y-1 text-sm">
                    <p>{order.address?.street}</p>
                    <p>
                      {order.address?.city}, {order.address?.province} {order.address?.postalCode}
                    </p>
                    <p>{order.address?.country}</p>
                    <p className="mt-2">{order.address?.phone}</p>
                  </div>
                </div>
                <div className="h-[150px] w-full flex-1 rounded-md bg-muted sm:h-auto">
                  {/* Map preview would go here */}
                  <div className="flex h-full items-center justify-center">
                    <MapPin className="h-8 w-8 text-muted-foreground" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Order Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative">
                <div className="absolute left-4 top-0 h-full w-0.5 bg-muted" />
                <div className="space-y-8">
                  {order.timeline?.map((step, index) => (
                    <div key={index} className="relative flex gap-4">
                      {renderTimelineIcon(step.status, step.completed)}
                      <div className="flex-1 pt-1">
                        <div className="flex flex-col justify-between gap-2 sm:flex-row">
                          <p className="font-medium">{formatTimelineStatus(step.status)}</p>
                          {step.date ? (
                            <p className="text-sm text-muted-foreground">
                              {new Date(step.date).toLocaleString("id-ID")}
                            </p>
                          ) : (
                            <p className="text-sm text-muted-foreground">Pending</p>
                          )}
                        </div>
                        {step.status === "ordered" && step.completed && (
                          <div className="mt-1 text-sm text-muted-foreground">
                            Your order has been received and is being processed.
                          </div>
                        )}
                        {step.status === "processing" && step.completed && (
                          <div className="mt-1 text-sm text-muted-foreground">
                            Your order is being prepared for shipment.
                          </div>
                        )}
                        {step.status === "shipped" && step.completed && order.shipping.trackingNumber && (
                          <div className="mt-1 text-sm">
                            <span className="text-muted-foreground">Tracking Number: </span>
                            <span className="font-medium">{order.shipping.trackingNumber}</span>
                            <span className="ml-2 text-muted-foreground">via {order.shipping.method}</span>
                          </div>
                        )}
                        {step.status === "delivered" && step.completed && (
                          <div className="mt-1 text-sm text-green-500">Your order has been delivered successfully.</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Visual Progress Tracker */}
              <div className="mt-8">
                <div className="mb-2 flex justify-between text-xs text-muted-foreground">
                  <span>Order Placed</span>
                  <span>Processing</span>
                  <span>Shipped</span>
                  <span>Delivered</span>
                </div>
                <div className="h-2 w-full rounded-full bg-muted">
                  <div
                    className="h-full rounded-full bg-primary transition-all duration-500"
                    style={{ width: `${timelineProgress()}%` }}
                  />
                </div>
                {order.shipping.eta && order.status !== "delivered" && order.status !== "cancelled" && (
                  <div className="mt-4 flex items-center gap-2 rounded-md bg-muted p-3">
                    <AlertCircle className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">Estimated Delivery</p>
                      <p className="text-sm text-muted-foreground">
                        Your order is expected to arrive by{" "}
                        <span className="font-medium">{new Date(order.shipping.eta).toLocaleDateString("id-ID")}</span>
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {order.status === "delivered" && (
          <TabsContent value="review" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Write a Review</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {order.items.map((item, index) => (
                    <div key={index} className="space-y-4 rounded-lg border p-4">
                      <div className="flex gap-4">
                        <img
                          src={item.image || "/placeholder.svg"}
                          alt={item.name}
                          className="h-20 w-20 rounded-md border object-cover"
                        />
                        <div>
                          <h3 className="font-medium">{item.name}</h3>
                          <p className="text-sm text-muted-foreground">Sold by: {item.store}</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">Rating:</span>
                          <div className="flex">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star
                                key={star}
                                className="h-5 w-5 cursor-pointer text-muted-foreground hover:text-yellow-400"
                              />
                            ))}
                          </div>
                        </div>

                        <div>
                          <label htmlFor={`review-${item.id}`} className="text-sm font-medium">
                            Your Review:
                          </label>
                          <textarea
                            id={`review-${item.id}`}
                            className="mt-1 w-full rounded-md border p-2 text-sm"
                            rows={3}
                            placeholder="Share your experience with this product..."
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button size="sm">Submit Review</Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}
