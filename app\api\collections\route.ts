import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

// Sample data untuk koleksi (akan diganti dengan database nanti)
let collections = [
  {
    id: "col-1",
    name: "Pakaian Favorit",
    description: "Koleksi pakaian yang saya ingin beli di masa depan",
    imageUrl: "/placeholder.svg",
    createdAt: "2025-05-10",
    itemCount: 2,
    isPublic: true,
    items: [
      "1", // ID item wishlist
      "5"
    ]
  },
  {
    id: "col-2",
    name: "Gadget",
    description: "Gadget keren yang ingin saya miliki",
    imageUrl: "/placeholder.svg",
    createdAt: "2025-05-05",
    itemCount: 1,
    isPublic: false,
    items: [
      "4"
    ]
  }
];

// GET - Mendapatkan semua koleksi
export async function GET(request: NextRequest) {
  try {
    // Simulasi delay untuk mendemonstrasikan loading state
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Di implementasi nyata, ambil data dari database
    const collections = await getMockCollections();
    
    return NextResponse.json(collections);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Terjadi kesalahan saat mengambil daftar koleksi" },
      { status: 500 }
    );
  }
}

// POST - Membuat koleksi baru
export async function POST(request: NextRequest) {
  try {
    const { name, description, isPublic } = await request.json();
    
    // Validasi data
    if (!name || name.trim() === "") {
      return NextResponse.json(
        { error: "Nama koleksi tidak boleh kosong" },
        { status: 400 }
      );
    }
    
    // Simulasi delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Di implementasi nyata, simpan ke database
    const newCollection = {
      id: uuidv4(),
      name,
      description: description || "",
      isPublic: isPublic || false,
      items: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Simpan koleksi baru
    const collections = await getMockCollections();
    collections.push(newCollection);
    await saveMockCollections(collections);
    
    return NextResponse.json(newCollection);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Terjadi kesalahan saat membuat koleksi baru" },
      { status: 500 }
    );
  }
}

// Fungsi helper untuk mendapatkan koleksi (mock data)
async function getMockCollections() {
  // Di implementasi nyata, ini akan mengambil data dari database
  return [
    {
      id: "1",
      name: "Barang Favorit",
      description: "Koleksi barang favorit saya",
      isPublic: true,
      items: ["item1", "item2"],
      createdAt: "2023-10-15T10:00:00Z",
      updatedAt: "2023-10-15T10:00:00Z"
    },
    {
      id: "2",
      name: "Wishlist Pakaian",
      description: "Pakaian yang ingin dibeli",
      isPublic: false,
      items: ["item3"],
      createdAt: "2023-10-16T14:30:00Z",
      updatedAt: "2023-10-16T14:30:00Z"
    },
    {
      id: "3",
      name: "Peralatan Masak",
      description: "",
      isPublic: true,
      items: [],
      createdAt: "2023-10-17T09:15:00Z",
      updatedAt: "2023-10-17T09:15:00Z"
    }
  ];
}

// Fungsi helper untuk menyimpan koleksi (mock function)
async function saveMockCollections(collections: any[]) {
  // Di implementasi nyata, ini akan menyimpan data ke database
  console.log("Collections saved:", collections);
  return true;
} 