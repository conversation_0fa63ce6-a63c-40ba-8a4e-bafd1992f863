"use client"

import { useState } from "react"
import { VelozioSearchBar } from "./search-bar"
import { VelozioSearchResults } from "./search-results"
import { VelozioProductCard } from "./product-card"
import { useTenantTheme } from "@/components/tenant/tenant-theme-provider"

// Sample products data
const sampleProducts = [
  {
    id: 1,
    name: "Smartphone Android Samsung",
    price: "Rp 2.999.000",
    originalPrice: "Rp 3.499.000",
    discount: "15%",
    image: "/modern-smartphone.png",
    category: "handphone",
    shortName: "Samsung Galaxy",
    isMall: true,
    rating: 4.9,
    sold: 125,
    shipping: "Pengiriman Instan",
    cod: true,
  },
  {
    id: 2,
    name: "Sepatu Sneakers Pria",
    price: "Rp 299.000",
    originalPrice: "Rp 399.000",
    discount: "25%",
    image: "/diverse-sneaker-collection.png",
    category: "sepatu pria",
    shortName: "Sneakers Pria",
    isMall: false,
    rating: 4.8,
    sold: 215,
    shipping: "Pengiriman Reguler",
    cod: true,
  },
  {
    id: 3,
    name: "Tas Selempang Wanita",
    price: "Rp 179.000",
    originalPrice: "Rp 229.000",
    discount: "22%",
    image: "/stylish-leather-handbag.png",
    category: "tas wanita",
    shortName: "Tas Selempang",
    isMall: true,
    rating: 4.7,
    sold: 98,
    shipping: "Pengiriman Instan",
    cod: false,
  },
  {
    id: 4,
    name: "Headphone Bluetooth Wireless",
    price: "Rp 549.000",
    originalPrice: "Rp 699.000",
    discount: "21%",
    image: "/diverse-people-listening-headphones.png",
    category: "elektronik",
    shortName: "Headphone Bluetooth",
    isMall: true,
    rating: 4.6,
    sold: 85,
    shipping: "Pengiriman Next Day",
    cod: true,
  },
  {
    id: 5,
    name: "Keyboard Gaming Mechanical",
    price: "Rp 459.000",
    originalPrice: "Rp 559.000",
    discount: "18%",
    image: "/mechanical-keyboard.png",
    category: "elektronik",
    shortName: "Keyboard Gaming",
    isMall: false,
    rating: 4.9,
    sold: 64,
    shipping: "Pengiriman Reguler",
    cod: false,
  },
  {
    id: 6,
    name: "Power Bank Quick Charge",
    price: "Rp 229.000",
    originalPrice: "Rp 299.000",
    discount: "23%",
    image: "/portable-power-bank.png",
    category: "handphone",
    shortName: "Power Bank",
    isMall: true,
    rating: 4.7,
    sold: 178,
    shipping: "Pengiriman Instan",
    cod: true,
  },
]

export function VelozioSearchPage() {
  const { theme } = useTenantTheme()
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<typeof sampleProducts>([])
  const [showResults, setShowResults] = useState(false)

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query)

    if (!query.trim()) {
      setSearchResults([])
      setShowResults(false)
      return
    }

    // Filter products based on search query
    const results = sampleProducts.filter(
      (product) =>
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.category.toLowerCase().includes(query.toLowerCase()) ||
        (product.shortName && product.shortName.toLowerCase().includes(query.toLowerCase())),
    )

    setSearchResults(results)
    setShowResults(true)
  }

  // Handle product click
  const handleProductClick = (product: (typeof sampleProducts)[0]) => {
    console.log("Product clicked:", product)
    // Navigate to product page or show product details
    // This would typically use router.push in a real implementation
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Search bar */}
      <VelozioSearchBar onSearch={handleSearch} />

      {/* Main content */}
      <div className="max-w-6xl mx-auto pt-20 px-4">
        {showResults ? (
          <VelozioSearchResults query={searchQuery} products={searchResults} onProductClick={handleProductClick} />
        ) : (
          <div className="py-8">
            <h2 className="text-lg font-medium mb-4">Produk Populer</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {sampleProducts.slice(0, 4).map((product) => (
                <VelozioProductCard
                  key={product.id}
                  product={product}
                  variant="simple"
                  onClick={() => handleProductClick(product)}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
