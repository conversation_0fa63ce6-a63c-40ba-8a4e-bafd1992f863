"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Filter,
  Users,
  UserPlus,
  ShoppingBag,
  Wallet,
  Star,
  UserCheck,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Eye,
  PencilIcon,
  MoreHorizontal,
  Clock,
  User,
  MessageSquare,
  AlertCircle,
  Heart,
  Tag,
  BadgePercent
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk pelanggan
const customers = [
  {
    id: "cust-001",
    name: "<PERSON>i Santoso",
    email: "<EMAIL>",
    phone: "081234567890",
    avatar: "/api/placeholder/32/32",
    status: "active",
    type: "regular",
    totalSpent: 4850000,
    totalOrders: 12,
    lastOrder: "2024-01-10T15:30:00",
    joinDate: "2023-05-15T10:00:00",
    address: {
      street: "Jl. Sudirman No. 123",
      city: "Jakarta",
      province: "DKI Jakarta",
      postalCode: "10220",
      country: "Indonesia"
    },
    favoriteStores: ["TechWorld Store", "Fashion Zone"],
    favoriteCategories: ["Electronics", "Fashion"],
    lastLogin: "2024-01-15T08:45:00",
    notes: "Pelanggan loyal yang sering membeli produk elektronik",
    tags: ["loyal", "high-spender", "tech-enthusiast"],
    segment: "premium",
    rewardPoints: 1250,
    wishlistCount: 8,
    reviewCount: 7,
    averageRating: 4.5
  },
  {
    id: "cust-002",
    name: "Siti Nuraini",
    email: "<EMAIL>",
    phone: "081256789012",
    avatar: "/api/placeholder/32/32",
    status: "active",
    type: "regular",
    totalSpent: 3250000,
    totalOrders: 8,
    lastOrder: "2024-01-08T11:20:00",
    joinDate: "2023-06-20T14:30:00",
    address: {
      street: "Jl. Diponegoro No. 78",
      city: "Surabaya",
      province: "Jawa Timur",
      postalCode: "60115",
      country: "Indonesia"
    },
    favoriteStores: ["Beauty Corner", "Fashion Zone"],
    favoriteCategories: ["Beauty", "Fashion"],
    lastLogin: "2024-01-14T19:30:00",
    notes: "",
    tags: ["beauty-enthusiast", "regular-buyer"],
    segment: "regular",
    rewardPoints: 850,
    wishlistCount: 15,
    reviewCount: 6,
    averageRating: 4.8
  },
  {
    id: "cust-003",
    name: "Ahmad Rizki",
    email: "<EMAIL>",
    phone: "081234567890",
    avatar: "/api/placeholder/32/32",
    status: "active",
    type: "new",
    totalSpent: 750000,
    totalOrders: 2,
    lastOrder: "2024-01-12T09:45:00",
    joinDate: "2023-12-05T16:20:00",
    address: {
      street: "Jl. Pahlawan No. 45",
      city: "Bandung",
      province: "Jawa Barat",
      postalCode: "40115",
      country: "Indonesia"
    },
    favoriteStores: ["TechWorld Store"],
    favoriteCategories: ["Electronics"],
    lastLogin: "2024-01-15T10:15:00",
    notes: "Pelanggan baru yang tertarik dengan produk elektronik",
    tags: ["new-customer", "tech-enthusiast"],
    segment: "new",
    rewardPoints: 150,
    wishlistCount: 12,
    reviewCount: 1,
    averageRating: 4.0
  },
  {
    id: "cust-004",
    name: "Maya Putri",
    email: "<EMAIL>",
    phone: "081290123456",
    avatar: "/api/placeholder/32/32",
    status: "active",
    type: "regular",
    totalSpent: 6750000,
    totalOrders: 18,
    lastOrder: "2024-01-15T13:40:00",
    joinDate: "2023-03-10T11:30:00",
    address: {
      street: "Jl. Imam Bonjol No. 123",
      city: "Denpasar",
      province: "Bali",
      postalCode: "80113",
      country: "Indonesia"
    },
    favoriteStores: ["Home Decor Store", "Fashion Zone", "Beauty Corner"],
    favoriteCategories: ["Home & Decor", "Fashion", "Beauty"],
    lastLogin: "2024-01-15T17:20:00",
    notes: "Pelanggan VIP yang sering membeli berbagai kategori",
    tags: ["vip", "high-spender", "multi-category"],
    segment: "vip",
    rewardPoints: 3250,
    wishlistCount: 23,
    reviewCount: 15,
    averageRating: 4.7
  },
  {
    id: "cust-005",
    name: "Deni Hermawan",
    email: "<EMAIL>",
    phone: "081278901234",
    avatar: "/api/placeholder/32/32",
    status: "inactive",
    type: "dormant",
    totalSpent: 1200000,
    totalOrders: 3,
    lastOrder: "2023-09-20T10:15:00",
    joinDate: "2023-04-25T09:45:00",
    address: {
      street: "Jl. Ahmad Yani No. 56",
      city: "Medan",
      province: "Sumatera Utara",
      postalCode: "20111",
      country: "Indonesia"
    },
    favoriteStores: ["Electronic Hub"],
    favoriteCategories: ["Electronics"],
    lastLogin: "2023-10-05T14:30:00",
    notes: "Pelanggan tidak aktif sejak September 2023",
    tags: ["dormant", "reactivation-target"],
    segment: "dormant",
    rewardPoints: 300,
    wishlistCount: 5,
    reviewCount: 2,
    averageRating: 3.5
  },
  {
    id: "cust-006",
    name: "Indah Permata",
    email: "<EMAIL>",
    phone: "081290123456",
    avatar: "/api/placeholder/32/32",
    status: "active",
    type: "regular",
    totalSpent: 2950000,
    totalOrders: 9,
    lastOrder: "2024-01-14T16:30:00",
    joinDate: "2023-07-12T13:20:00",
    address: {
      street: "Jl. Gajah Mada No. 87",
      city: "Yogyakarta",
      province: "DI Yogyakarta",
      postalCode: "55112",
      country: "Indonesia"
    },
    favoriteStores: ["Fashion Zone", "Beauty Corner"],
    favoriteCategories: ["Fashion", "Beauty"],
    lastLogin: "2024-01-15T09:10:00",
    notes: "",
    tags: ["fashion-enthusiast", "regular-buyer"],
    segment: "regular",
    rewardPoints: 950,
    wishlistCount: 18,
    reviewCount: 8,
    averageRating: 4.2
  }
]

// Fungsi untuk generate badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "active":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>
    case "inactive": 
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Inactive</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk generate badge segment
function getSegmentBadge(segment: string) {
  switch (segment) {
    case "vip":
      return <Badge variant="outline" className="bg-purple-100 text-purple-800">VIP</Badge>
    case "premium": 
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">Premium</Badge>
    case "regular":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Regular</Badge>
    case "new":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">New</Badge>
    case "dormant":
      return <Badge variant="outline" className="bg-red-100 text-red-800">Dormant</Badge>
    default:
      return <Badge variant="outline">{segment}</Badge>
  }
}

// Fungsi untuk format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric'
  })
}

// Fungsi untuk menghitung waktu yang telah berlalu
function timeAgo(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(months / 12)

  if (years > 0) return `${years} tahun lalu`
  if (months > 0) return `${months} bulan lalu`
  if (days > 0) return `${days} hari lalu`
  if (hours > 0) return `${hours} jam lalu`
  if (minutes > 0) return `${minutes} menit lalu`
  return `${seconds} detik lalu`
}

export default function CustomersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [segmentFilter, setSegmentFilter] = useState("all")
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null)

  // Filter customers
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          customer.phone.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || customer.status === statusFilter
    const matchesSegment = segmentFilter === "all" || customer.segment === segmentFilter
    return matchesSearch && matchesStatus && matchesSegment
  })

  // Menghitung statistik pelanggan
  const stats = {
    total: customers.length,
    active: customers.filter(c => c.status === "active").length,
    new: customers.filter(c => c.type === "new").length,
    vip: customers.filter(c => c.segment === "vip").length,
    premium: customers.filter(c => c.segment === "premium").length,
    avgLifetimeValue: customers.reduce((sum, c) => sum + c.totalSpent, 0) / customers.length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customers Management</h1>
            <p className="text-muted-foreground">
              Kelola pelanggan, segmentasi, dan data interaksi
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/tenant/dashboard/customers/groups">
              <Users className="h-4 w-4 mr-2" />
              Customer Groups
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/tenant/dashboard/customers/reviews">
              <Star className="h-4 w-4 mr-2" />
              Reviews
            </Link>
          </Button>
          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            Add Customer
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Active: {stats.active}, New: {stats.new}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">VIP Customers</CardTitle>
            <UserCheck className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.vip}</div>
            <p className="text-xs text-muted-foreground">
              Premium: {stats.premium}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Lifetime Value</CardTitle>
            <Wallet className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.avgLifetimeValue)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingBag className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{customers.reduce((sum, c) => sum + c.totalOrders, 0)}</div>
            <p className="text-xs text-muted-foreground">
              From {stats.active} active customers
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reviews</CardTitle>
            <Star className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{customers.reduce((sum, c) => sum + c.reviewCount, 0)}</div>
            <p className="text-xs text-muted-foreground">
              Avg. Rating: {(customers.reduce((sum, c) => sum + c.averageRating * c.reviewCount, 0) / customers.reduce((sum, c) => sum + c.reviewCount, 0)).toFixed(1)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari nama, email, atau nomor telepon..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            <select
              value={segmentFilter}
              onChange={(e) => setSegmentFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Segment</option>
              <option value="vip">VIP</option>
              <option value="premium">Premium</option>
              <option value="regular">Regular</option>
              <option value="new">New</option>
              <option value="dormant">Dormant</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Customers List */}
      <div className="space-y-4">
        {filteredCustomers.map((customer) => (
          <Card key={customer.id} className={`hover:shadow-md transition-shadow ${selectedCustomer === customer.id ? 'ring-2 ring-primary' : ''}`}>
            <CardContent className="p-6">
              <div className="flex flex-col gap-4">
                {/* Customer Header */}
                <div className="flex flex-col md:flex-row justify-between gap-4">
                  <div className="flex items-start gap-3">
                    <div className="h-12 w-12 rounded-full overflow-hidden bg-gray-100">
                      <img src={customer.avatar} alt={customer.name} className="h-full w-full object-cover" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-lg font-semibold">{customer.name}</h3>
                        {getStatusBadge(customer.status)}
                        {getSegmentBadge(customer.segment)}
                      </div>
                      <div className="flex items-center gap-3 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Mail className="h-3.5 w-3.5" />
                          <span>{customer.email}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Phone className="h-3.5 w-3.5" />
                          <span>{customer.phone}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-muted-foreground">Customer ID: {customer.id}</p>
                    <div className="flex items-center gap-1 justify-end mt-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <p className="text-sm">Joined {formatDate(customer.joinDate)}</p>
                    </div>
                    <div className="flex items-center gap-1 justify-end mt-1">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <p className="text-sm">Last login {timeAgo(customer.lastLogin)}</p>
                    </div>
                  </div>
                </div>

                {/* Customer Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 p-4 bg-muted/50 rounded-lg">
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Total Spent</p>
                    <p className="font-semibold text-green-600">{formatCurrency(customer.totalSpent)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Orders</p>
                    <div className="flex items-center gap-1">
                      <ShoppingBag className="h-4 w-4 text-blue-600" />
                      <p className="font-semibold">{customer.totalOrders}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Last Order</p>
                    <p className="font-semibold">{timeAgo(customer.lastOrder)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Reviews</p>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-600" />
                      <p className="font-semibold">{customer.reviewCount} ({customer.averageRating})</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Reward Points</p>
                    <div className="flex items-center gap-1">
                      <BadgePercent className="h-4 w-4 text-purple-600" />
                      <p className="font-semibold">{customer.rewardPoints}</p>
                    </div>
                  </div>
                </div>

                {/* Address */}
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm">{customer.address.street}</p>
                    <p className="text-sm">{customer.address.city}, {customer.address.province}, {customer.address.postalCode}</p>
                    <p className="text-sm">{customer.address.country}</p>
                  </div>
                </div>

                {/* Preferences */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium mb-1">Favorite Stores</p>
                    <div className="flex flex-wrap gap-1">
                      {customer.favoriteStores.map((store, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {store}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium mb-1">Favorite Categories</p>
                    <div className="flex flex-wrap gap-1">
                      {customer.favoriteCategories.map((category, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Tags */}
                {customer.tags.length > 0 && (
                  <div>
                    <p className="text-sm font-medium mb-1">Tags</p>
                    <div className="flex flex-wrap gap-1">
                      {customer.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs bg-blue-50 text-blue-800">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Notes */}
                {customer.notes && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm font-medium text-yellow-800 mb-1">Notes</p>
                    <p className="text-sm">{customer.notes}</p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2 pt-4 border-t">
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    View Profile
                  </Button>
                  <Button size="sm" variant="outline">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    Order History
                  </Button>
                  <Button size="sm" variant="outline">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Send Message
                  </Button>
                  <Button size="sm" variant="outline">
                    <PencilIcon className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  {customer.status === "active" ? (
                    <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                      <AlertCircle className="h-4 w-4 mr-2" />
                      Deactivate
                    </Button>
                  ) : (
                    <Button size="sm" variant="outline" className="text-green-600 hover:text-green-700">
                      <UserCheck className="h-4 w-4 mr-2" />
                      Activate
                    </Button>
                  )}
                  <Button size="sm" variant="ghost">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredCustomers.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Tidak ada pelanggan ditemukan</h3>
              <p className="text-muted-foreground mb-4">
                Tidak ada pelanggan yang cocok dengan filter Anda
              </p>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                Add Customer
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
} 