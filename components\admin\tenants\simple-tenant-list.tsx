"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { PlusCircle, RefreshCw, CheckCircle, Ban, Clock } from "lucide-react"
import { useRouter, useSearchParams } from "next/navigation"
import type { SuspendTenantData } from "./suspend-tenant-modal"
import { TenantList } from "./tenant-list"

// Tipe data tenant
interface Tenant {
  id: string
  name: string
  domain: string
  plan: string
  status: string
  storeCount: number
  userCount: number
  revenue: string
  createdAt: string
}

// Data tenant default
const DEFAULT_TENANTS: Tenant[] = [
  {
    id: "1",
    name: "Acme Corporation",
    domain: "acme.sellzio.com",
    plan: "Enterprise",
    status: "active",
    storeCount: 12,
    userCount: 45,
    revenue: "$5,240",
    createdAt: "2023-01-15",
  },
  {
    id: "2",
    name: "TechStart Inc",
    domain: "techstart.sellzio.com",
    plan: "Professional",
    status: "active",
    storeCount: 5,
    userCount: 18,
    revenue: "$1,890",
    createdAt: "2023-02-22",
  },
  {
    id: "3",
    name: "Global Retail",
    domain: "globalretail.sellzio.com",
    plan: "Enterprise",
    status: "active",
    storeCount: 28,
    userCount: 120,
    revenue: "$12,450",
    createdAt: "2022-11-05",
  },
  {
    id: "4",
    name: "Fashion Forward",
    domain: "fashionforward.sellzio.com",
    plan: "Basic",
    status: "suspended",
    storeCount: 1,
    userCount: 3,
    revenue: "$240",
    createdAt: "2023-03-10",
  },
  {
    id: "5",
    name: "Digital Solutions",
    domain: "digitalsolutions.sellzio.com",
    plan: "Professional",
    status: "active",
    storeCount: 7,
    userCount: 22,
    revenue: "$3,120",
    createdAt: "2023-01-30",
  },
]

// Tambahkan data dummy untuk contoh
const dummyTenants = [
  {
    id: "1",
    name: "Acme Corporation",
    domain: "acme.sellzio.com",
    plan: "Enterprise",
    status: "active",
    storeCount: 12,
    userCount: 45,
    revenue: "$5,240",
    createdAt: "2023-01-15",
  },
  {
    id: "2",
    name: "TechStart Inc",
    domain: "techstart.sellzio.com",
    plan: "Professional",
    status: "active",
    storeCount: 5,
    userCount: 18,
    revenue: "$1,890",
    createdAt: "2023-02-22",
  },
  {
    id: "3",
    name: "Global Retail",
    domain: "globalretail.sellzio.com",
    plan: "Enterprise",
    status: "active",
    storeCount: 28,
    userCount: 120,
    revenue: "$12,450",
    createdAt: "2022-11-05",
  },
  {
    id: "4",
    name: "Fashion Forward",
    domain: "fashionforward.sellzio.com",
    plan: "Basic",
    status: "suspended",
    storeCount: 1,
    userCount: 3,
    revenue: "$240",
    createdAt: "2023-03-10",
  },
  {
    id: "5",
    name: "Digital Solutions",
    domain: "digitalsolutions.sellzio.com",
    plan: "Professional",
    status: "active",
    storeCount: 7,
    userCount: 22,
    revenue: "$3,120",
    createdAt: "2023-04-18",
  },
]

export function SimpleTenantList() {
  const router = useRouter()
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [filteredTenants, setFilteredTenants] = useState<Tenant[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [filters, setFilters] = useState({
    status: "",
    plan: "",
    dateFrom: "",
    dateTo: "",
  })
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [tenantToDelete, setTenantToDelete] = useState<Tenant | null>(null)
  const [actionLoading, setActionLoading] = useState(false)
  const [actionMessage, setActionMessage] = useState<{ type: "success" | "error"; text: string } | null>(null)

  // State untuk modal suspend tenant
  const [isSuspendModalOpen, setIsSuspendModalOpen] = useState(false)
  const [tenantToSuspend, setTenantToSuspend] = useState<Tenant | null>(null)

  // State untuk bulk actions
  const [selectedTenantIds, setSelectedTenantIds] = useState<string[]>([])
  const [isAllSelected, setIsAllSelected] = useState(false)
  const [isBulkSuspendModalOpen, setIsBulkSuspendModalOpen] = useState(false)
  const [isChangePlanModalOpen, setIsChangePlanModalOpen] = useState(false)
  const [isSendMessageModalOpen, setIsSendMessageModalOpen] = useState(false)

  const searchParams = useSearchParams()

  // Fungsi untuk memuat data tenant
  const loadTenants = useCallback(() => {
    setIsLoading(true)
    // Simulasi pemanggilan API
    setTimeout(() => {
      setTenants(dummyTenants)
      setIsLoading(false)
    }, 1000)
  }, [])

  // Tampilkan pesan notifikasi
  const showNotification = useCallback((type: "success" | "error", text: string) => {
    setActionMessage({ type, text })
    // Hapus notifikasi setelah 3 detik
    setTimeout(() => {
      setActionMessage(null)
    }, 3000)
  }, [])

  // Fungsi untuk memuat data tenant
  const loadTenantsFromLocalStorage = useCallback(() => {
    setIsLoading(true)
    setError(null)

    try {
      console.log("Loading tenants...")

      // Untuk server-side rendering, gunakan data default
      if (typeof window === "undefined") {
        console.log("Running on server, using default data")
        setTenants(DEFAULT_TENANTS)
        setFilteredTenants(DEFAULT_TENANTS)
        setIsLoading(false)
        return
      }

      // Client-side, coba ambil dari localStorage
      try {
        const storedTenants = localStorage.getItem("tenants")

        if (storedTenants) {
          const parsedTenants = JSON.parse(storedTenants)
          console.log("Tenants loaded from localStorage:", parsedTenants)
          setTenants(parsedTenants)
          setFilteredTenants(parsedTenants)
        } else {
          // Jika tidak ada di localStorage, gunakan data default
          console.log("No tenants in localStorage, using default data")
          setTenants(DEFAULT_TENANTS)
          setFilteredTenants(DEFAULT_TENANTS)

          // Simpan data default ke localStorage
          localStorage.setItem("tenants", JSON.stringify(DEFAULT_TENANTS))
        }
      } catch (err) {
        console.error("Error accessing localStorage:", err)
        setError("Error accessing localStorage. Using default data.")
        setTenants(DEFAULT_TENANTS)
        setFilteredTenants(DEFAULT_TENANTS)
      }
    } catch (err) {
      console.error("Error loading tenants:", err)
      setError("Failed to load tenants. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Fungsi untuk refresh halaman
  const refreshPage = useCallback(() => {
    // Reload data
    loadTenantsFromLocalStorage()

    // Reset selection
    setSelectedTenantIds([])
    setIsAllSelected(false)

    // Reset modals
    setIsBulkSuspendModalOpen(false)
    setIsChangePlanModalOpen(false)
    setIsSendMessageModalOpen(false)
    setIsSuspendModalOpen(false)
    setIsDeleteDialogOpen(false)

    // Reset action states
    setActionLoading(false)
    setTenantToDelete(null)
    setTenantToSuspend(null)

    // Show notification
    showNotification("success", "Data refreshed successfully")
  }, [showNotification, loadTenantsFromLocalStorage])

  // Load tenants on mount (client-side only)
  useEffect(() => {
    if (typeof window !== "undefined") {
      loadTenantsFromLocalStorage()
    }
  }, [loadTenantsFromLocalStorage])

  // Filter dan search tenants
  useEffect(() => {
    if (tenants.length === 0) return

    let result = [...tenants]

    // Apply search
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        (tenant) => tenant.name.toLowerCase().includes(query) || tenant.domain.toLowerCase().includes(query),
      )
    }

    // Apply filters - PERBAIKAN: Jangan filter jika nilai adalah "" atau "all"
    if (filters.status && filters.status !== "all") {
      result = result.filter((tenant) => tenant.status === filters.status)
    }

    if (filters.plan && filters.plan !== "all") {
      result = result.filter((tenant) => tenant.plan === filters.plan)
    }

    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom)
      result = result.filter((tenant) => new Date(tenant.createdAt) >= fromDate)
    }

    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo)
      toDate.setHours(23, 59, 59, 999) // End of day
      result = result.filter((tenant) => new Date(tenant.createdAt) <= toDate)
    }

    setFilteredTenants(result)

    // Reset selected tenants when filters change
    setSelectedTenantIds([])
    setIsAllSelected(false)
  }, [tenants, searchQuery, filters])

  // Handle select all checkbox
  const handleSelectAll = useCallback(() => {
    if (isAllSelected) {
      // Deselect all
      setSelectedTenantIds([])
    } else {
      // Select all filtered tenants
      setSelectedTenantIds(filteredTenants.map((tenant) => tenant.id))
    }
    setIsAllSelected(!isAllSelected)
  }, [isAllSelected, filteredTenants])

  // Handle individual tenant selection
  const handleSelectTenant = useCallback((tenantId: string, isChecked: boolean) => {
    if (isChecked) {
      setSelectedTenantIds((prev) => [...prev, tenantId])
    } else {
      setSelectedTenantIds((prev) => prev.filter((id) => id !== tenantId))
    }
  }, [])

  // Update isAllSelected when selectedTenantIds changes
  useEffect(() => {
    setIsAllSelected(filteredTenants.length > 0 && selectedTenantIds.length === filteredTenants.length)
  }, [selectedTenantIds, filteredTenants])

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Active
          </Badge>
        )
      case "suspended":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1">
            <Ban className="h-3 w-3" />
            Suspended
          </Badge>
        )
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Pending
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            {status}
          </Badge>
        )
    }
  }

  // Handle tenant actions
  const handleTenantAction = useCallback(
    (action: string, tenant: Tenant) => {
      console.log(`Action: ${action}, Tenant: ${tenant.name}`)

      // PERBAIKAN: Tambahkan try-catch untuk menangani error
      try {
        switch (action) {
          case "view":
            // PERBAIKAN: Navigasi ke halaman detail tenant
            router.push(`/admin/dashboard/tenants/${tenant.id}`)
            break
          case "edit":
            // PERBAIKAN: Navigasi ke halaman edit tenant
            router.push(`/admin/dashboard/tenants/${tenant.id}/edit`)
            break
          case "delete":
            setTenantToDelete(tenant)
            setIsDeleteDialogOpen(true)
            break
          case "login":
            // PERBAIKAN: Simulasi login sebagai tenant
            showNotification("success", `Logged in as ${tenant.name} admin`)
            // Simulasi redirect ke dashboard tenant
            setTimeout(() => {
              router.push(`/tenant/dashboard?tenant=${tenant.id}`)
            }, 1000)
            break
          case "suspend":
            // Buka modal suspend tenant
            setTenantToSuspend(tenant)
            setIsSuspendModalOpen(true)
            break
          case "activate":
            handleActivateTenant(tenant)
            break
          default:
            break
        }
      } catch (error) {
        console.error(`Error handling action ${action}:`, error)
        showNotification("error", `Failed to perform action: ${error}`)
      }
    },
    [router, showNotification],
  )

  // Handle bulk actions
  const handleBulkAction = useCallback(
    (action: string) => {
      if (selectedTenantIds.length === 0) {
        showNotification("error", "No tenants selected")
        return
      }

      console.log(`Bulk action: ${action}, Selected tenants: ${selectedTenantIds.length}`)

      switch (action) {
        case "suspend":
          setIsBulkSuspendModalOpen(true)
          break
        case "changePlan":
          setIsChangePlanModalOpen(true)
          break
        case "export":
          handleBulkExport()
          break
        case "sendMessage":
          setIsSendMessageModalOpen(true)
          break
        case "generateReport":
          handleGenerateReport()
          break
        default:
          break
      }
    },
    [selectedTenantIds, showNotification],
  )

  // Handle bulk suspend
  const handleBulkSuspend = useCallback(
    async (data: SuspendTenantData) => {
      setActionLoading(true)

      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // Get current tenants
        const currentTenants = [...tenants]

        // Find and update the selected tenants
        const updatedTenants = currentTenants.map((tenant) => {
          if (selectedTenantIds.includes(tenant.id)) {
            return { ...tenant, status: "suspended" }
          }
          return tenant
        })

        // Update localStorage
        if (typeof window !== "undefined") {
          localStorage.setItem("tenants", JSON.stringify(updatedTenants))
        }

        // Update state
        setTenants(updatedTenants)
        setFilteredTenants((prev) =>
          prev.map((tenant) => (selectedTenantIds.includes(tenant.id) ? { ...tenant, status: "suspended" } : tenant)),
        )

        // Show success message
        showNotification("success", `${selectedTenantIds.length} tenants have been suspended successfully`)

        // Reset selection
        setSelectedTenantIds([])
        setIsAllSelected(false)
      } catch (err) {
        console.error("Error suspending tenants:", err)
        showNotification("error", "Failed to suspend tenants. Please try again.")
      } finally {
        setIsBulkSuspendModalOpen(false)
        setTimeout(() => {
          setActionLoading(false)
        }, 100)
      }
    },
    [tenants, selectedTenantIds, showNotification],
  )

  // Handle bulk change plan
  const handleBulkChangePlan = useCallback(
    async (newPlan: string) => {
      setActionLoading(true)

      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // Get current tenants
        const currentTenants = [...tenants]

        // Find and update the selected tenants
        const updatedTenants = currentTenants.map((tenant) => {
          if (selectedTenantIds.includes(tenant.id)) {
            return { ...tenant, plan: newPlan }
          }
          return tenant
        })

        // Update localStorage
        if (typeof window !== "undefined") {
          localStorage.setItem("tenants", JSON.stringify(updatedTenants))
        }

        // Update state
        setTenants(updatedTenants)
        setFilteredTenants((prev) =>
          prev.map((tenant) => (selectedTenantIds.includes(tenant.id) ? { ...tenant, plan: newPlan } : tenant)),
        )

        // Show success message
        showNotification("success", `Plan updated to ${newPlan} for ${selectedTenantIds.length} tenants`)

        // Reset selection
        setSelectedTenantIds([])
        setIsAllSelected(false)
      } catch (err) {
        console.error("Error changing plan:", err)
        showNotification("error", "Failed to change plan. Please try again.")
      } finally {
        setIsChangePlanModalOpen(false)
        setTimeout(() => {
          setActionLoading(false)
        }, 100)
      }
    },
    [tenants, selectedTenantIds, showNotification],
  )

  // Handle bulk export
  const handleBulkExport = useCallback(async () => {
    setActionLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Get selected tenants data
      const selectedTenants = tenants.filter((tenant) => selectedTenantIds.includes(tenant.id))

      // In a real app, you would generate a CSV or Excel file here
      console.log("Exporting tenants:", selectedTenants)

      // Show success message
      showNotification("success", `Exported data for ${selectedTenantIds.length} tenants`)

      // Don't reset selection to allow for other actions
    } catch (err) {
      console.error("Error exporting tenants:", err)
      showNotification("error", "Failed to export tenants. Please try again.")
    } finally {
      setTimeout(() => {
        setActionLoading(false)
      }, 100)
    }
  }, [tenants, selectedTenantIds, showNotification])

  // Handle send message to selected tenants
  const handleSendMessage = useCallback(
    async (message: string) => {
      setActionLoading(true)

      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // In a real app, you would send messages to the selected tenants
        console.log("Sending message to tenants:", selectedTenantIds, message)

        // Show success message
        showNotification("success", `Message sent to ${selectedTenantIds.length} tenants`)

        // Reset selection
        setSelectedTenantIds([])
        setIsAllSelected(false)
      } catch (err) {
        console.error("Error sending message:", err)
        showNotification("error", "Failed to send message. Please try again.")
      } finally {
        setIsSendMessageModalOpen(false)
        setTimeout(() => {
          setActionLoading(false)
        }, 100)
      }
    },
    [selectedTenantIds, showNotification],
  )

  // Handle generate report for selected tenants
  const handleGenerateReport = useCallback(async () => {
    setActionLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Get selected tenants data
      const selectedTenants = tenants.filter((tenant) => selectedTenantIds.includes(tenant.id))

      // In a real app, you would generate a report here
      console.log("Generating report for tenants:", selectedTenants)

      // Show success message
      showNotification("success", `Report generated for ${selectedTenantIds.length} tenants`)

      // Reset selection
      setSelectedTenantIds([])
      setIsAllSelected(false)
    } catch (err) {
      console.error("Error generating report:", err)
      showNotification("error", "Failed to generate report. Please try again.")
    } finally {
      setTimeout(() => {
        setActionLoading(false)
      }, 100)
    }
  }, [tenants, selectedTenantIds, showNotification])

  // Handle delete tenant
  const handleDeleteTenantFromList = useCallback(async (id: string) => {
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        setTenants((prev) => prev.filter((tenant) => tenant.id !== id))
        resolve()
      }, 1000)
    })
  }, [])

  // Handle delete tenant
  const handleDeleteTenant = useCallback(async () => {
    if (!tenantToDelete) return

    setActionLoading(true)

    try {
      // Get current tenants
      const currentTenants = [...tenants]

      // Filter out the tenant to delete
      const updatedTenants = currentTenants.filter((t) => t.id !== tenantToDelete.id)

      // Update localStorage
      if (typeof window !== "undefined") {
        localStorage.setItem("tenants", JSON.stringify(updatedTenants))
      }

      // Update state
      setTenants(updatedTenants)

      // PERBAIKAN: Perbarui juga filteredTenants
      setFilteredTenants((prevFiltered) => prevFiltered.filter((t) => t.id !== tenantToDelete.id))

      // Show success message
      showNotification("success", `${tenantToDelete.name} has been deleted successfully`)
    } catch (err) {
      console.error("Error deleting tenant:", err)
      showNotification("error", `Failed to delete ${tenantToDelete.name}. Please try again.`)
    } finally {
      // PERBAIKAN: Pastikan dialog ditutup dan state direset sebelum actionLoading diubah
      setIsDeleteDialogOpen(false)
      setTenantToDelete(null)
      // PERBAIKAN: Gunakan setTimeout untuk memastikan UI diperbarui sebelum actionLoading diubah
      setTimeout(() => {
        setActionLoading(false)
      }, 100)
    }
  }, [tenants, tenantToDelete, showNotification])

  // Handle suspend tenant dengan modal
  const handleSuspendTenantSubmit = useCallback(
    async (data: SuspendTenantData) => {
      if (!tenantToSuspend) return

      setActionLoading(true)

      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // Get current tenants
        const currentTenants = [...tenants]

        // Find and update the tenant
        const updatedTenants = currentTenants.map((t) => {
          if (t.id === tenantToSuspend.id) {
            return { ...t, status: "suspended" }
          }
          return t
        })

        // Update localStorage
        if (typeof window !== "undefined") {
          localStorage.setItem("tenants", JSON.stringify(updatedTenants))
        }

        // Update state
        setTenants(updatedTenants)

        // Perbarui juga filteredTenants
        setFilteredTenants((prevFiltered) =>
          prevFiltered.map((t) => (t.id === tenantToSuspend.id ? { ...t, status: "suspended" } : t)),
        )

        // Log activity (in a real app, this would be sent to the server)
        console.log("Activity log:", {
          action: "suspend_tenant",
          tenantId: tenantToSuspend.id,
          tenantName: tenantToSuspend.name,
          reason: data.reason,
          notes: data.notes,
          durationType: data.durationType,
          endDate: data.endDate,
          timestamp: new Date().toISOString(),
        })

        // Show success message
        showNotification("success", `${tenantToSuspend.name} has been suspended successfully`)
      } catch (err) {
        console.error("Error suspending tenant:", err)
        showNotification("error", `Failed to suspend ${tenantToSuspend.name}. Please try again.`)
      } finally {
        setIsSuspendModalOpen(false)
        setTenantToSuspend(null)
        setTimeout(() => {
          setActionLoading(false)
        }, 100)
      }
    },
    [tenants, tenantToSuspend, showNotification],
  )

  // Handle activate tenant
  const handleActivateTenant = useCallback(
    async (tenant: Tenant) => {
      setActionLoading(true)

      try {
        // Get current tenants
        const currentTenants = [...tenants]

        // Find and update the tenant
        const updatedTenants = currentTenants.map((t) => {
          if (t.id === tenant.id) {
            return { ...t, status: "active" }
          }
          return t
        })

        // Update localStorage
        if (typeof window !== "undefined") {
          localStorage.setItem("tenants", JSON.stringify(updatedTenants))
        }

        // Update state
        setTenants(updatedTenants)

        // PERBAIKAN: Perbarui juga filteredTenants
        setFilteredTenants((prevFiltered) =>
          prevFiltered.map((t) => (t.id === tenant.id ? { ...t, status: "active" } : t)),
        )

        // Show success message
        showNotification("success", `${tenant.name} has been activated`)
      } catch (err) {
        console.error("Error activating tenant:", err)
        showNotification("error", `Failed to activate ${tenant.name}. Please try again.`)
      } finally {
        // PERBAIKAN: Gunakan setTimeout untuk memastikan UI diperbarui sebelum actionLoading diubah
        setTimeout(() => {
          setActionLoading(false)
        }, 100)
      }
    },
    [tenants, showNotification],
  )

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters({
      status: "",
      plan: "",
      dateFrom: "",
      dateTo: "",
    })
    setSearchQuery("")
  }, [])

  // State to track if the initial data load has been attempted
  const [initialLoadAttempted, setInitialLoadAttempted] = useState(false)

  // Combined data loading and refresh logic
  useEffect(() => {
    const shouldRefresh = searchParams.get("refresh") === "true"

    // Function to perform data loading
    const performDataLoad = () => {
      if (typeof window !== "undefined") {
        loadTenantsFromLocalStorage()
      }
    }

    // Function to handle refresh logic
    const handleRefresh = () => {
      // Remove the 'refresh' parameter from the URL without reloading the page
      const newUrl = window.location.pathname
      router.replace(newUrl)

      // Reload data
      loadTenants()
    }

    // Determine whether to refresh or load data based on the 'refresh' parameter
    if (shouldRefresh) {
      handleRefresh()
    } else if (!initialLoadAttempted) {
      // Only load data if it hasn't been loaded before
      performDataLoad()
      setInitialLoadAttempted(true)
    }
  }, [searchParams, router, loadTenants, loadTenantsFromLocalStorage, initialLoadAttempted])

  // If there's an error, show error state
  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Tenants</h2>
            <p className="text-muted-foreground">Manage all tenants in the Sellzio platform</p>
          </div>
          <Button onClick={() => router.push("/admin/dashboard/tenants/create")}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Tenant
          </Button>
        </div>

        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="text-red-500 mb-4">{error}</div>
          <Button onClick={loadTenantsFromLocalStorage} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Reload Tenants
          </Button>
        </div>
      </div>
    )
  }

  // If loading, show loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Tenants</h2>
            <p className="text-muted-foreground">Manage all tenants in the Sellzio platform</p>
          </div>
          <Button onClick={() => router.push("/admin/dashboard/tenants/create")}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Tenant
          </Button>
        </div>

        <div className="flex flex-col items-center justify-center p-8 text-center">
          <p>Loading tenants...</p>
        </div>
      </div>
    )
  }

  // Calculate summary data
  const activeTenants = tenants.filter((t) => t.status === "active").length
  const totalRevenue = tenants.reduce((sum, tenant) => {
    const revenue = Number.parseFloat(tenant.revenue.replace("$", "").replace(",", ""))
    return sum + revenue
  }, 0)

  return (
    <TenantList tenants={tenants} isLoading={isLoading} onRefresh={loadTenants} onDelete={handleDeleteTenantFromList} />
  )
}
