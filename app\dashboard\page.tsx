"use client"

import { useState, useEffect, Suspense } from "react"
import {
  CreditCard,
  DollarSign,
  Download,
  Package,
  RefreshCcw,
  ShieldCheck,
  ShoppingBag,
  ShoppingCart,
  Store,
  Upload,
  Users,
} from "lucide-react"
import { StatCard } from "@/components/dashboard/stat-card"
import { ChartCard } from "@/components/dashboard/chart-card"
import { SystemStatus } from "@/components/dashboard/system-status"
import { ActivityFeed } from "@/components/dashboard/activity-feed"
import { QuickActions } from "@/components/dashboard/quick-actions"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"

// Komponen untuk menampilkan tanggal yang hanya dirender di client-side
function FormattedDate({ date, locale = "id-ID" }: { date?: Date; locale?: string }) {
  const [formattedDate, setFormattedDate] = useState("");
  
  useEffect(() => {
    // Gunakan tanggal yang diberikan atau tanggal saat ini
    const dateToFormat = date || new Date();
    setFormattedDate(dateToFormat.toLocaleString(locale));
  }, [date, locale]);
  
  return <>{formattedDate}</>;
}

// Komponen untuk menampilkan nilai random yang hanya dirender di client-side
function RandomValue({ min = 100000, max = 1000000, formatter = (val: number) => val.toString() }: {
  min?: number;
  max?: number;
  formatter?: (val: number) => string;
}) {
  const [value, setValue] = useState("");
  
  useEffect(() => {
    // Hanya dijalankan di client-side
    const randomValue = Math.floor(Math.random() * (max - min)) + min;
    setValue(formatter(randomValue));
  }, [min, max, formatter]);
  
  return <>{value}</>;
}

// Data dummy untuk grafik
const revenueData = [
  { name: "Jan", total: 1500000 },
  { name: "Feb", total: 2300000 },
  { name: "Mar", total: 1900000 },
  { name: "Apr", total: 3200000 },
  { name: "Mei", total: 2800000 },
  { name: "Jun", total: 3800000 },
  { name: "Jul", total: 4100000 },
]

const ordersData = [
  { name: "Jan", total: 45 },
  { name: "Feb", total: 72 },
  { name: "Mar", total: 58 },
  { name: "Apr", total: 91 },
  { name: "Mei", total: 87 },
  { name: "Jun", total: 115 },
  { name: "Jul", total: 124 },
]

const systemStatusItems = [
  { name: "API Server", status: "online" as const, uptime: "99.9%" },
  { name: "Database", status: "online" as const, uptime: "99.8%" },
  { name: "Storage", status: "online" as const, uptime: "99.9%" },
  { name: "Payment Gateway", status: "online" as const, uptime: "99.7%" },
  { name: "Email Service", status: "warning" as const, uptime: "98.5%" },
]

const activityItems = [
  {
    id: "1",
    user: { name: "John Doe", email: "<EMAIL>" },
    action: "membuat toko baru",
    target: "Toko Fashion",
    date: "Baru saja",
    status: "success" as const,
  },
  {
    id: "2",
    user: { name: "Jane Smith", email: "<EMAIL>" },
    action: "menambahkan produk",
    target: "Sepatu Casual",
    date: "5 menit yang lalu",
    status: "success" as const,
  },
  {
    id: "3",
    user: { name: "Robert Johnson", email: "<EMAIL>" },
    action: "melakukan pembayaran",
    target: "Rp 750.000",
    date: "15 menit yang lalu",
    status: "success" as const,
  },
  {
    id: "4",
    user: { name: "Lisa Wang", email: "<EMAIL>" },
    action: "mendaftar sebagai",
    target: "affiliate",
    date: "30 menit yang lalu",
    status: "info" as const,
  },
  {
    id: "5",
    user: { name: "Michael Brown", email: "<EMAIL>" },
    action: "gagal melakukan pembayaran",
    target: "Rp 1.250.000",
    date: "1 jam yang lalu",
    status: "error" as const,
  },
]

const quickActions = [
  {
    name: "Tambah Toko",
    icon: Store,
    href: "/dashboard/stores/create",
  },
  {
    name: "Tambah Produk",
    icon: ShoppingBag,
    href: "/dashboard/products/create",
  },
  {
    name: "Lihat Pesanan",
    icon: Package,
    href: "/dashboard/orders",
  },
  {
    name: "Tambah Affiliate",
    icon: Users,
    href: "/dashboard/affiliates/create",
  },
  {
    name: "Sinkronisasi Data",
    icon: RefreshCcw,
    onClick: () => {
      console.log("Sync data")
    },
  },
  {
    name: "Ekspor Data",
    icon: Download,
    onClick: () => {
      console.log("Export data")
    },
  },
  {
    name: "Impor Data",
    icon: Upload,
    onClick: () => {
      console.log("Import data")
    },
  },
  {
    name: "Keamanan",
    icon: ShieldCheck,
    href: "/dashboard/security",
  },
]

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <div className="flex items-center gap-2">
          <div className="text-sm text-muted-foreground">
            Terakhir diperbarui: <FormattedDate />
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Total Pendapatan"
            value={formatCurrency(15750000)}
            description="Total pendapatan bulan ini"
            icon={DollarSign}
            trend="up"
            trendValue="12%"
            variant="primary"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Pesanan"
            value="124"
            description="Total pesanan bulan ini"
            icon={ShoppingCart}
            trend="up"
            trendValue="8%"
            variant="success"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Produk"
            value="87"
            description="Total produk aktif"
            icon={ShoppingBag}
            trend="up"
            trendValue="5%"
            variant="warning"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Toko"
            value="12"
            description="Total toko aktif"
            icon={Store}
            trend="neutral"
            trendValue="0%"
            variant="default"
          />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ChartCard
            title="Pendapatan"
            description="Pendapatan 7 bulan terakhir"
            data={revenueData}
            type="line"
            dataKey="total"
            valueFormatter={(value) => formatCurrency(value)}
            colors={{
              stroke: "hsl(var(--primary))",
              fill: "hsl(var(--primary) / 0.1)",
            }}
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ChartCard
            title="Pesanan"
            description="Jumlah pesanan 7 bulan terakhir"
            data={ordersData}
            type="bar"
            dataKey="total"
            valueFormatter={(value) => `${value} pesanan`}
            colors={{
              stroke: "hsl(var(--green-500))",
              fill: "hsl(var(--green-500) / 0.2)",
            }}
          />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <QuickActions
            title="Aksi Cepat"
            description="Akses cepat ke fitur utama"
            actions={quickActions}
            className="lg:col-span-2"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <SystemStatus title="Status Sistem" items={systemStatusItems} />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ActivityFeed title="Aktivitas Terbaru" description="Aktivitas terbaru di platform" items={activityItems} />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Transaksi Terbaru</CardTitle>
              <CardDescription>Transaksi terbaru di platform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                        <CreditCard className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">Pembayaran #{1000 + i}</p>
                        <p className="text-xs text-muted-foreground">
                          <FormattedDate date={new Date(Date.now() - i * 3600000)} />
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        <RandomValue 
                          min={100000} 
                          max={1000000} 
                          formatter={(val) => formatCurrency(val)} 
                        />
                      </p>
                      <p
                        className={`text-xs ${
                          i % 3 === 0 ? "text-yellow-500" : i % 4 === 0 ? "text-red-500" : "text-green-500"
                        }`}
                      >
                        {i % 3 === 0 ? "Menunggu" : i % 4 === 0 ? "Gagal" : "Sukses"}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </Suspense>
      </div>
    </div>
  )
}
