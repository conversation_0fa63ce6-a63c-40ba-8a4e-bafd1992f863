"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { PlusCircle, RefreshCw } from "lucide-react"
import { useNotifications } from "@/components/providers/notifications-provider"
import { TenantList } from "@/components/admin/tenants/tenant-list"

// Tenant service
import { getAllTenants, deleteTenant } from "@/lib/services/tenant-service"

export default function TenantsClient() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const shouldRefresh = searchParams.get("refresh") === "true"
  const { addNotification } = useNotifications()

  const [isClient, setIsClient] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [tenants, setTenants] = useState([])
  const [error, setError] = useState<string | null>(null)

  // Fetch tenants data
  const fetchTenants = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const data = await getAllTenants()

      // Validasi data yang diterima
      if (!data || !Array.isArray(data)) {
        throw new Error("Invalid tenant data received")
      }

      setTenants(data as any)
    } catch (error) {
      console.error("Error fetching tenants:", error)
      setError("Failed to load tenants. Please try again.")
      addNotification({
        message: "Failed to load tenants. Please try again.",
        type: "error",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle delete tenant
  const handleDeleteTenant = async (id: string) => {
    try {
      await deleteTenant(id)
      return true
    } catch (error) {
      console.error("Error deleting tenant:", error)
      throw error
    }
  }

  // Initialize localStorage with default data if empty
  const initializeLocalStorage = () => {
    if (typeof window !== "undefined") {
      const storedTenants = localStorage.getItem("tenants")
      if (!storedTenants) {
        // Import default tenants from service
        import("@/lib/services/tenant-service").then((module) => {
          const defaultTenants = module.tenants || []
          localStorage.setItem("tenants", JSON.stringify(defaultTenants))
        })
      }
    }
  }

  // Ensure component is mounted before rendering
  useEffect(() => {
    setIsClient(true)
    initializeLocalStorage()
    fetchTenants()
  }, [])

  // Refresh data when navigating back from create page
  useEffect(() => {
    if (shouldRefresh) {
      fetchTenants()
      // Remove the refresh parameter from URL
      const newUrl = window.location.pathname
      window.history.replaceState({}, "", newUrl)
    }
  }, [shouldRefresh])

  if (!isClient) {
    return null
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Tenants</h2>
            <p className="text-muted-foreground">Manage all tenants in the Sellzio platform</p>
          </div>
          <Button onClick={() => router.push("/admin/dashboard/tenants/create")}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Tenant
          </Button>
        </div>

        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="text-red-500 mb-4">{error}</div>
          <Button onClick={fetchTenants} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Reload Tenants
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Tenants</h2>
          <p className="text-muted-foreground">Manage all tenants in the Sellzio platform</p>
        </div>
        <Button onClick={() => router.push("/admin/dashboard/tenants/create")}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Create Tenant
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? <Skeleton className="h-8 w-16" /> : tenants.length}</div>
            <p className="text-xs text-muted-foreground">+12% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                (tenants as any[]).filter((t) => t.status === "active").length
              )}
            </div>
            <p className="text-xs text-muted-foreground">+5% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                "$" +
                (tenants as any[])
                  .reduce((sum, tenant) => {
                    const revenue = Number.parseFloat(tenant.revenue.replace("$", "").replace(",", ""))
                    return sum + revenue
                  }, 0)
                  .toLocaleString()
              )}
            </div>
            <p className="text-xs text-muted-foreground">+18% from last month</p>
          </CardContent>
        </Card>
      </div>

      <TenantList
        tenants={tenants as any[]}
        isLoading={isLoading}
        onRefresh={fetchTenants}
        onDelete={handleDeleteTenant}
      />
    </div>
  )
}
