"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ArrowLeft,
  Upload,
  Palette,
  Save,
  RefreshCw,
  Layout,
  Type,
  Image as ImageIcon,
  Eye
} from "lucide-react"

export default function BrandingPage() {
  const [activeTab, setActiveTab] = useState("logo")
  const [logoPreview, setLogoPreview] = useState("/placeholders/logo.svg")
  const [faviconPreview, setFaviconPreview] = useState("/placeholders/favicon.svg")
  const [bannerPreview, setBannerPreview] = useState("/placeholders/banner.jpg")
  
  // Dummy data untuk branding settings
  const brandingSettings = {
    colors: {
      primary: "#2563eb",
      secondary: "#7c3aed",
      accent: "#f97316",
      background: "#ffffff",
      text: "#0f172a"
    },
    fonts: {
      heading: "Inter",
      body: "Inter"
    },
    customCSS: `/* Custom CSS */
.custom-button {
  background: linear-gradient(to right, #2563eb, #7c3aed);
  border-radius: 8px;
}`,
    emailTemplate: "template-1",
    checkoutTemplate: "template-2",
    enableCustomDomain: true
  }

  const fontOptions = [
    { value: "inter", label: "Inter" },
    { value: "roboto", label: "Roboto" },
    { value: "poppins", label: "Poppins" },
    { value: "lato", label: "Lato" },
    { value: "open-sans", label: "Open Sans" }
  ]

  const emailTemplates = [
    { value: "template-1", label: "Template Modern" },
    { value: "template-2", label: "Template Klasik" },
    { value: "template-3", label: "Template Minimalis" }
  ]

  const checkoutTemplates = [
    { value: "template-1", label: "Checkout Standar" },
    { value: "template-2", label: "Checkout Satu Halaman" },
    { value: "template-3", label: "Checkout Multi-langkah" }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/settings">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Pengaturan Branding</h1>
            <p className="text-muted-foreground">
              Sesuaikan tampilan dan branding toko online Anda
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset ke Default
          </Button>
          <Button>
            <Save className="h-4 w-4 mr-2" />
            Simpan Perubahan
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-5 w-full md:w-auto">
          <TabsTrigger value="logo">Logo</TabsTrigger>
          <TabsTrigger value="colors">Warna</TabsTrigger>
          <TabsTrigger value="typography">Tipografi</TabsTrigger>
          <TabsTrigger value="templates">Template</TabsTrigger>
          <TabsTrigger value="advanced">Lanjutan</TabsTrigger>
        </TabsList>

        {/* Logo & Images Tab */}
        <TabsContent value="logo" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Logo & Favicon</CardTitle>
              <CardDescription>
                Upload logo dan favicon yang akan ditampilkan di toko online Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <Label>Logo Utama</Label>
                  <div className="border rounded-lg p-4 flex flex-col items-center justify-center gap-4">
                    <div className="w-40 h-40 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                      <img src={logoPreview} alt="Logo Preview" className="max-w-full max-h-full object-contain" />
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground mb-2">
                        Disarankan: PNG atau SVG dengan ukuran 400x400px
                      </p>
                      <div className="flex gap-2 justify-center">
                        <Button variant="outline" size="sm">
                          <Upload className="h-4 w-4 mr-2" />
                          Upload Logo
                        </Button>
                        <Button variant="outline" size="sm">
                          Hapus
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <Label>Favicon</Label>
                  <div className="border rounded-lg p-4 flex flex-col items-center justify-center gap-4">
                    <div className="w-20 h-20 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                      <img src={faviconPreview} alt="Favicon Preview" className="max-w-full max-h-full object-contain" />
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground mb-2">
                        Disarankan: PNG atau ICO dengan ukuran 32x32px
                      </p>
                      <div className="flex gap-2 justify-center">
                        <Button variant="outline" size="sm">
                          <Upload className="h-4 w-4 mr-2" />
                          Upload Favicon
                        </Button>
                        <Button variant="outline" size="sm">
                          Hapus
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Banner & Gambar Header</CardTitle>
              <CardDescription>
                Upload gambar banner untuk halaman dan email Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Label>Banner Halaman Utama</Label>
              <div className="border rounded-lg p-4 flex flex-col items-center justify-center gap-4">
                <div className="w-full h-44 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                  <img src={bannerPreview} alt="Banner Preview" className="w-full h-full object-cover" />
                </div>
                <div className="text-center">
                  <p className="text-sm text-muted-foreground mb-2">
                    Disarankan: JPG atau PNG dengan ukuran 1920x480px
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button variant="outline" size="sm">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Banner
                    </Button>
                    <Button variant="outline" size="sm">
                      Hapus
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Colors Tab */}
        <TabsContent value="colors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Skema Warna</CardTitle>
              <CardDescription>
                Sesuaikan warna utama yang digunakan di seluruh toko Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="primary-color">Warna Utama</Label>
                  <div className="flex gap-2">
                    <div className="w-10 h-10 rounded-md" style={{ backgroundColor: brandingSettings.colors.primary }}></div>
                    <Input id="primary-color" defaultValue={brandingSettings.colors.primary} onChange={() => {}} />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="secondary-color">Warna Sekunder</Label>
                  <div className="flex gap-2">
                    <div className="w-10 h-10 rounded-md" style={{ backgroundColor: brandingSettings.colors.secondary }}></div>
                    <Input id="secondary-color" defaultValue={brandingSettings.colors.secondary} onChange={() => {}} />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="accent-color">Warna Aksen</Label>
                  <div className="flex gap-2">
                    <div className="w-10 h-10 rounded-md" style={{ backgroundColor: brandingSettings.colors.accent }}></div>
                    <Input id="accent-color" defaultValue={brandingSettings.colors.accent} onChange={() => {}} />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="background-color">Warna Latar</Label>
                  <div className="flex gap-2">
                    <div className="w-10 h-10 rounded-md border" style={{ backgroundColor: brandingSettings.colors.background }}></div>
                    <Input id="background-color" defaultValue={brandingSettings.colors.background} onChange={() => {}} />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="text-color">Warna Teks</Label>
                  <div className="flex gap-2">
                    <div className="w-10 h-10 rounded-md" style={{ backgroundColor: brandingSettings.colors.text }}></div>
                    <Input id="text-color" defaultValue={brandingSettings.colors.text} onChange={() => {}} />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <Palette className="h-4 w-4 mr-2" />
                Pratinjau
              </Button>
              <Button>Simpan Perubahan</Button>
            </CardFooter>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Tema</CardTitle>
              <CardDescription>
                Pilih tema warna untuk toko Anda
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="border rounded-lg p-2 flex flex-col items-center cursor-pointer hover:border-primary">
                  <div className="grid grid-cols-2 gap-1 w-full mb-2">
                    <div className="h-6 rounded bg-blue-600"></div>
                    <div className="h-6 rounded bg-blue-800"></div>
                    <div className="h-6 rounded bg-white border"></div>
                    <div className="h-6 rounded bg-slate-900"></div>
                  </div>
                  <span className="text-sm">Default</span>
                </div>
                <div className="border rounded-lg p-2 flex flex-col items-center cursor-pointer hover:border-primary">
                  <div className="grid grid-cols-2 gap-1 w-full mb-2">
                    <div className="h-6 rounded bg-green-600"></div>
                    <div className="h-6 rounded bg-green-800"></div>
                    <div className="h-6 rounded bg-white border"></div>
                    <div className="h-6 rounded bg-slate-900"></div>
                  </div>
                  <span className="text-sm">Emerald</span>
                </div>
                <div className="border rounded-lg p-2 flex flex-col items-center cursor-pointer hover:border-primary">
                  <div className="grid grid-cols-2 gap-1 w-full mb-2">
                    <div className="h-6 rounded bg-purple-600"></div>
                    <div className="h-6 rounded bg-purple-800"></div>
                    <div className="h-6 rounded bg-white border"></div>
                    <div className="h-6 rounded bg-slate-900"></div>
                  </div>
                  <span className="text-sm">Violet</span>
                </div>
                <div className="border rounded-lg p-2 flex flex-col items-center cursor-pointer hover:border-primary">
                  <div className="grid grid-cols-2 gap-1 w-full mb-2">
                    <div className="h-6 rounded bg-amber-600"></div>
                    <div className="h-6 rounded bg-amber-800"></div>
                    <div className="h-6 rounded bg-white border"></div>
                    <div className="h-6 rounded bg-slate-900"></div>
                  </div>
                  <span className="text-sm">Amber</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Typography Tab */}
        <TabsContent value="typography" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Font dan Tipografi</CardTitle>
              <CardDescription>
                Pilih jenis font untuk teks di toko Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="heading-font">Font Heading</Label>
                  <Select defaultValue={brandingSettings.fonts.heading}>
                    <SelectTrigger id="heading-font">
                      <SelectValue placeholder="Pilih font heading" />
                    </SelectTrigger>
                    <SelectContent>
                      {fontOptions.map((font) => (
                        <SelectItem key={font.value} value={font.value}>{font.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Font ini akan digunakan untuk judul dan heading
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="body-font">Font Body</Label>
                  <Select defaultValue={brandingSettings.fonts.body}>
                    <SelectTrigger id="body-font">
                      <SelectValue placeholder="Pilih font body" />
                    </SelectTrigger>
                    <SelectContent>
                      {fontOptions.map((font) => (
                        <SelectItem key={font.value} value={font.value}>{font.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Font ini akan digunakan untuk teks utama dan konten
                  </p>
                </div>
              </div>
              <Separator />
              <div className="space-y-4">
                <Label>Preview Tipografi</Label>
                <div className="p-4 border rounded-lg">
                  <h1 className="text-3xl font-bold mb-2">Heading Font</h1>
                  <h2 className="text-2xl font-semibold mb-2">Subheading Example</h2>
                  <p className="mb-4">
                    Ini adalah contoh teks body yang menggunakan font body yang dipilih. Font yang baik akan meningkatkan keterbacaan dan pengalaman pengguna di toko online Anda.
                  </p>
                  <div className="flex gap-2">
                    <Button>Tombol Utama</Button>
                    <Button variant="outline">Tombol Sekunder</Button>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button>Simpan Perubahan</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Email</CardTitle>
              <CardDescription>
                Sesuaikan tampilan email yang dikirim ke pelanggan Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email-template">Pilih Template Email</Label>
                <Select defaultValue={brandingSettings.emailTemplate}>
                  <SelectTrigger id="email-template">
                    <SelectValue placeholder="Pilih template email" />
                  </SelectTrigger>
                  <SelectContent>
                    {emailTemplates.map((template) => (
                      <SelectItem key={template.value} value={template.value}>{template.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="border rounded-lg p-4">
                <div className="text-center mb-4">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    Pratinjau Email
                  </Button>
                </div>
                <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                  <p className="text-muted-foreground">Preview Template Email</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Template Checkout</CardTitle>
              <CardDescription>
                Sesuaikan tampilan halaman checkout toko Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="checkout-template">Pilih Template Checkout</Label>
                <Select defaultValue={brandingSettings.checkoutTemplate}>
                  <SelectTrigger id="checkout-template">
                    <SelectValue placeholder="Pilih template checkout" />
                  </SelectTrigger>
                  <SelectContent>
                    {checkoutTemplates.map((template) => (
                      <SelectItem key={template.value} value={template.value}>{template.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="border rounded-lg p-4">
                <div className="text-center mb-4">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    Pratinjau Checkout
                  </Button>
                </div>
                <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                  <p className="text-muted-foreground">Preview Template Checkout</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced Tab */}
        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Custom CSS</CardTitle>
              <CardDescription>
                Tambahkan kode CSS kustom untuk menyesuaikan tampilan toko lebih lanjut
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea 
                className="font-mono h-52"
                defaultValue={brandingSettings.customCSS}
                onChange={() => {}}
                placeholder="/* Masukkan kode CSS kustom di sini */"
              />
              <p className="text-sm text-muted-foreground mt-2">
                CSS kustom akan diterapkan ke seluruh toko Anda. Gunakan dengan hati-hati.
              </p>
            </CardContent>
            <CardFooter>
              <Button>Simpan Perubahan</Button>
            </CardFooter>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Pengaturan Lanjutan</CardTitle>
              <CardDescription>
                Opsi konfigurasi lanjutan untuk tampilan toko
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="custom-domain">Gunakan Domain Kustom</Label>
                  <p className="text-sm text-muted-foreground">
                    Aktifkan penggunaan domain kustom untuk branding
                  </p>
                </div>
                <Switch id="custom-domain" defaultChecked={brandingSettings.enableCustomDomain} onCheckedChange={() => {}} />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="display-logo">Tampilkan Logo di Email</Label>
                  <p className="text-sm text-muted-foreground">
                    Tampilkan logo toko di semua email yang dikirim
                  </p>
                </div>
                <Switch id="display-logo" defaultChecked={true} onCheckedChange={() => {}} />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="custom-footer">Kustomisasi Footer</Label>
                  <p className="text-sm text-muted-foreground">
                    Aktifkan kustomisasi footer di semua halaman
                  </p>
                </div>
                <Switch id="custom-footer" defaultChecked={true} onCheckedChange={() => {}} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 