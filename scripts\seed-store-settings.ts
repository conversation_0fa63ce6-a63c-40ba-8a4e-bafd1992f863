import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '../.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const storeSettings = [
  // General Settings
  {
    setting_key: 'store_name',
    setting_name: 'Nama Store',
    setting_value: 'My Store',
    setting_type: 'text',
    category: 'general',
    description: 'Nama resmi store Anda',
    is_public: true,
    is_required: true,
    sort_order: 1,
    validation_rules: {
      required: true,
      min_length: 2,
      max_length: 100
    }
  },
  {
    setting_key: 'store_description',
    setting_name: 'Deskripsi Store',
    setting_value: 'Toko online terpercaya dengan berbagai produk berkualitas',
    setting_type: 'textarea',
    category: 'general',
    description: 'Deskripsi singkat tentang store Anda',
    is_public: true,
    is_required: false,
    sort_order: 2,
    validation_rules: {
      max_length: 500
    }
  },
  {
    setting_key: 'store_email',
    setting_name: 'Email Store',
    setting_value: '<EMAIL>',
    setting_type: 'email',
    category: 'general',
    description: 'Email kontak untuk store',
    is_public: true,
    is_required: true,
    sort_order: 3,
    validation_rules: {
      required: true,
      email: true
    }
  },
  {
    setting_key: 'store_phone',
    setting_name: 'Nomor Telepon',
    setting_value: '+62812345678',
    setting_type: 'tel',
    category: 'general',
    description: 'Nomor telepon store untuk kontak',
    is_public: true,
    is_required: false,
    sort_order: 4,
    validation_rules: {
      pattern: '^\\+?[1-9]\\d{1,14}$'
    }
  },
  {
    setting_key: 'store_address',
    setting_name: 'Alamat Store',
    setting_value: 'Jl. Sudirman No. 123, Jakarta',
    setting_type: 'textarea',
    category: 'general',
    description: 'Alamat lengkap store',
    is_public: true,
    is_required: false,
    sort_order: 5,
    validation_rules: {
      max_length: 300
    }
  },

  // Commission Settings
  {
    setting_key: 'commission_rate',
    setting_name: 'Rate Komisi (%)',
    setting_value: '10',
    setting_type: 'number',
    category: 'commission',
    description: 'Persentase komisi untuk affiliate',
    is_public: false,
    is_required: true,
    sort_order: 1,
    validation_rules: {
      required: true,
      min: 0,
      max: 50,
      step: 0.1
    }
  },
  {
    setting_key: 'min_commission_payout',
    setting_name: 'Minimum Payout Komisi',
    setting_value: '100000',
    setting_type: 'number',
    category: 'commission',
    description: 'Minimum saldo komisi untuk bisa dicairkan (Rupiah)',
    is_public: false,
    is_required: true,
    sort_order: 2,
    validation_rules: {
      required: true,
      min: 10000
    }
  },
  {
    setting_key: 'commission_payout_schedule',
    setting_name: 'Jadwal Pembayaran Komisi',
    setting_value: 'monthly',
    setting_type: 'select',
    category: 'commission',
    description: 'Frekuensi pembayaran komisi',
    is_public: false,
    is_required: true,
    sort_order: 3,
    options: [
      { value: 'weekly', label: 'Mingguan' },
      { value: 'monthly', label: 'Bulanan' },
      { value: 'quarterly', label: 'Triwulan' }
    ],
    validation_rules: {
      required: true
    }
  },

  // Payment Settings
  {
    setting_key: 'payment_methods',
    setting_name: 'Metode Pembayaran',
    setting_value: 'bank_transfer,credit_card,ewallet',
    setting_type: 'text',
    category: 'payment',
    description: 'Metode pembayaran yang diterima (dipisah koma)',
    is_public: true,
    is_required: true,
    sort_order: 1,
    validation_rules: {
      required: true
    }
  },
  {
    setting_key: 'auto_accept_payment',
    setting_name: 'Auto Accept Payment',
    setting_value: 'true',
    setting_type: 'boolean',
    category: 'payment',
    description: 'Otomatis terima pembayaran yang valid',
    is_public: false,
    is_required: false,
    sort_order: 2,
    validation_rules: {}
  },

  // Shipping Settings
  {
    setting_key: 'free_shipping_threshold',
    setting_name: 'Minimum Free Shipping',
    setting_value: '100000',
    setting_type: 'number',
    category: 'shipping',
    description: 'Minimum pembelian untuk gratis ongkir (Rupiah)',
    is_public: true,
    is_required: false,
    sort_order: 1,
    validation_rules: {
      min: 0
    }
  },
  {
    setting_key: 'shipping_zones',
    setting_name: 'Area Pengiriman',
    setting_value: 'jakarta,bogor,depok,tangerang,bekasi',
    setting_type: 'text',
    category: 'shipping',
    description: 'Area yang dilayani pengiriman (dipisah koma)',
    is_public: true,
    is_required: true,
    sort_order: 2,
    validation_rules: {
      required: true
    }
  },

  // Notification Settings
  {
    setting_key: 'email_notifications',
    setting_name: 'Notifikasi Email',
    setting_value: 'true',
    setting_type: 'boolean',
    category: 'notification',
    description: 'Aktifkan notifikasi via email',
    is_public: false,
    is_required: false,
    sort_order: 1,
    validation_rules: {}
  },
  {
    setting_key: 'sms_notifications',
    setting_name: 'Notifikasi SMS',
    setting_value: 'false',
    setting_type: 'boolean',
    category: 'notification',
    description: 'Aktifkan notifikasi via SMS',
    is_public: false,
    is_required: false,
    sort_order: 2,
    validation_rules: {}
  },

  // Security Settings
  {
    setting_key: 'require_email_verification',
    setting_name: 'Verifikasi Email Wajib',
    setting_value: 'true',
    setting_type: 'boolean',
    category: 'security',
    description: 'Wajibkan verifikasi email untuk registrasi',
    is_public: false,
    is_required: false,
    sort_order: 1,
    validation_rules: {}
  },
  {
    setting_key: 'max_login_attempts',
    setting_name: 'Maksimal Percobaan Login',
    setting_value: '5',
    setting_type: 'number',
    category: 'security',
    description: 'Maksimal percobaan login sebelum akun dikunci',
    is_public: false,
    is_required: true,
    sort_order: 2,
    validation_rules: {
      required: true,
      min: 3,
      max: 10
    }
  },

  // SEO Settings
  {
    setting_key: 'meta_title',
    setting_name: 'Meta Title',
    setting_value: 'My Store - Toko Online Terpercaya',
    setting_type: 'text',
    category: 'seo',
    description: 'Judul halaman untuk SEO',
    is_public: true,
    is_required: false,
    sort_order: 1,
    validation_rules: {
      max_length: 60
    }
  },
  {
    setting_key: 'meta_description',
    setting_name: 'Meta Description',
    setting_value: 'Belanja online mudah dan aman di My Store. Berbagai produk berkualitas dengan harga terbaik.',
    setting_type: 'textarea',
    category: 'seo',
    description: 'Deskripsi halaman untuk SEO',
    is_public: true,
    is_required: false,
    sort_order: 2,
    validation_rules: {
      max_length: 160
    }
  },
  {
    setting_key: 'google_analytics_id',
    setting_name: 'Google Analytics ID',
    setting_value: '',
    setting_type: 'text',
    category: 'seo',
    description: 'ID Google Analytics untuk tracking',
    is_public: false,
    is_required: false,
    sort_order: 3,
    validation_rules: {
      pattern: '^(G-[A-Z0-9]+|UA-[0-9]+-[0-9]+)?$'
    }
  }
]

async function seedStoreSettings() {
  try {
    console.log('Seeding store settings...')

    // Insert settings
    const { data, error } = await supabase
      .from('store_settings')
      .insert(storeSettings)
      .select()

    if (error) {
      console.error('Error seeding store settings:', error)
      return
    }

    console.log(`Successfully seeded ${data.length} store settings`)
    console.log('Store settings seeded successfully!')
  } catch (error) {
    console.error('Error:', error)
  }
}

// Run the seed function
seedStoreSettings()
