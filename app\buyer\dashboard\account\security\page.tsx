"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import {
  Lock,
  Shield,
  Smartphone,
  LogOut,
  AlertTriangle,
  Eye,
  EyeOff,
  X,
  Laptop,
  SmartphoneIcon as MobileIcon,
  Tablet,
  Globe,
  Clock,
  RefreshCw,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"

// Sample login history data
const loginHistory = [
  {
    id: "1",
    device: "Laptop",
    browser: "Chrome",
    os: "Windows 11",
    location: "Jakarta, Indonesia",
    ip: "103.28.xx.xx",
    date: "2023-05-15T10:30:00",
    status: "success",
    current: true,
  },
  {
    id: "2",
    device: "Mobile",
    browser: "Safari",
    os: "iOS 16",
    location: "Jakarta, Indonesia",
    ip: "103.28.xx.xx",
    date: "2023-05-10T14:20:00",
    status: "success",
    current: false,
  },
  {
    id: "3",
    device: "Tablet",
    browser: "Chrome",
    os: "Android 13",
    location: "Bandung, Indonesia",
    ip: "36.72.xx.xx",
    date: "2023-05-05T09:15:00",
    status: "success",
    current: false,
  },
  {
    id: "4",
    device: "Unknown",
    browser: "Firefox",
    os: "Windows 10",
    location: "Singapore",
    ip: "128.53.xx.xx",
    date: "2023-04-28T11:45:00",
    status: "failed",
    current: false,
  },
]

// Sample connected apps data
const connectedApps = [
  {
    id: "1",
    name: "Google",
    icon: "G",
    permissions: ["Profile information", "Email address"],
    lastUsed: "2023-05-15T10:30:00",
  },
  {
    id: "2",
    name: "Facebook",
    icon: "F",
    permissions: ["Profile information", "Friend list"],
    lastUsed: "2023-05-01T14:20:00",
  },
  {
    id: "3",
    name: "Instagram",
    icon: "I",
    permissions: ["Profile information", "Media library"],
    lastUsed: "2023-04-20T09:15:00",
  },
]

export default function SecurityPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState(0)
  const [passwordFeedback, setPasswordFeedback] = useState("")
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const [showLogoutAllDialog, setShowLogoutAllDialog] = useState(false)
  const [showRevokeDialog, setShowRevokeDialog] = useState(false)
  const [appToRevoke, setAppToRevoke] = useState<string | null>(null)

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    // Simple password strength calculation
    let strength = 0
    let feedback = ""

    if (password.length > 0) {
      // Length check
      if (password.length >= 8) {
        strength += 25
      } else {
        feedback = "Password should be at least 8 characters long"
      }

      // Uppercase check
      if (/[A-Z]/.test(password)) {
        strength += 25
      } else if (feedback === "") {
        feedback = "Add uppercase letters"
      }

      // Lowercase check
      if (/[a-z]/.test(password)) {
        strength += 25
      } else if (feedback === "") {
        feedback = "Add lowercase letters"
      }

      // Number/special char check
      if (/[0-9!@#$%^&*(),.?":{}|<>]/.test(password)) {
        strength += 25
      } else if (feedback === "") {
        feedback = "Add numbers or special characters"
      }

      if (strength === 100) {
        feedback = "Strong password"
      } else if (strength >= 75) {
        feedback = "Good password"
      } else if (strength >= 50) {
        feedback = "Moderate password"
      } else if (strength >= 25) {
        feedback = "Weak password"
      }
    }

    setPasswordStrength(strength)
    setPasswordFeedback(feedback)
  }

  const handleRevokeApp = (id: string) => {
    setAppToRevoke(id)
    setShowRevokeDialog(true)
  }

  const getDeviceIcon = (device: string) => {
    switch (device.toLowerCase()) {
      case "laptop":
        return <Laptop className="h-4 w-4" />
      case "mobile":
        return <MobileIcon className="h-4 w-4" />
      case "tablet":
        return <Tablet className="h-4 w-4" />
      default:
        return <Globe className="h-4 w-4" />
    }
  }

  const getStatusBadge = (status: string, current: boolean) => {
    if (current) {
      return (
        <Badge variant="outline" className="bg-green-50 text-green-600">
          Current Session
        </Badge>
      )
    }
    return status === "success" ? (
      <Badge variant="outline" className="bg-green-50 text-green-600">
        Successful
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-red-50 text-red-600">
        Failed
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Account Settings</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/buyer/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="security" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6 lg:w-auto">
          <TabsTrigger value="profile" asChild>
            <Link href="/buyer/dashboard/account/profile">Profile</Link>
          </TabsTrigger>
          <TabsTrigger value="addresses" asChild>
            <Link href="/buyer/dashboard/account/addresses">Addresses</Link>
          </TabsTrigger>
          <TabsTrigger value="payment" asChild>
            <Link href="/buyer/dashboard/account/payment">Payment Methods</Link>
          </TabsTrigger>
          <TabsTrigger value="communication" asChild>
            <Link href="/buyer/dashboard/account/communication">Communication</Link>
          </TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="connected" asChild>
            <Link href="/buyer/dashboard/account/connected">Connected Accounts</Link>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="security" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold">Security Settings</h2>
            <p className="text-sm text-muted-foreground">
              Manage your account security, password, and authentication methods.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {/* Password Change */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Lock className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Change Password</CardTitle>
                </div>
                <CardDescription>Update your password to keep your account secure</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="current-password">Current Password</Label>
                  <div className="relative">
                    <Input
                      id="current-password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your current password"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3 py-2 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-password">New Password</Label>
                  <div className="relative">
                    <Input
                      id="new-password"
                      type={showNewPassword ? "text" : "password"}
                      placeholder="Enter your new password"
                      onChange={handleNewPasswordChange}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3 py-2 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  {passwordStrength > 0 && (
                    <div className="space-y-1">
                      <Progress value={passwordStrength} className="h-1" />
                      <p
                        className={`text-xs ${
                          passwordStrength === 100
                            ? "text-green-600"
                            : passwordStrength >= 75
                              ? "text-green-500"
                              : passwordStrength >= 50
                                ? "text-yellow-500"
                                : "text-red-500"
                        }`}
                      >
                        {passwordFeedback}
                      </p>
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirm-password">Confirm New Password</Label>
                  <div className="relative">
                    <Input
                      id="confirm-password"
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="Confirm your new password"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3 py-2 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button>Update Password</Button>
              </CardFooter>
            </Card>

            {/* Two-Factor Authentication */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Two-Factor Authentication</CardTitle>
                </div>
                <CardDescription>Add an extra layer of security to your account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="two-factor">Enable Two-Factor Authentication</Label>
                    <p className="text-xs text-muted-foreground">
                      Require a verification code when logging in from a new device
                    </p>
                  </div>
                  <Switch id="two-factor" checked={twoFactorEnabled} onCheckedChange={setTwoFactorEnabled} />
                </div>

                {twoFactorEnabled && (
                  <div className="rounded-md bg-muted p-4">
                    <div className="flex items-start gap-3">
                      <Smartphone className="mt-0.5 h-5 w-5 text-primary" />
                      <div>
                        <h3 className="font-medium">Set up authenticator app</h3>
                        <p className="text-sm text-muted-foreground">
                          Use an authenticator app like Google Authenticator, Microsoft Authenticator, or Authy to get
                          verification codes.
                        </p>
                        <Button variant="outline" size="sm" className="mt-2">
                          Set Up Authenticator
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                <div className="rounded-md bg-muted p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="mt-0.5 h-5 w-5 text-yellow-500" />
                    <div>
                      <h3 className="font-medium">Recovery Options</h3>
                      <p className="text-sm text-muted-foreground">
                        Make sure to set up recovery options in case you lose access to your authentication method.
                      </p>
                      <Button variant="outline" size="sm" className="mt-2">
                        Set Up Recovery
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Login History */}
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Login History</CardTitle>
                </div>
                <CardDescription>Recent login activity on your account</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {loginHistory.map((login) => (
                    <div
                      key={login.id}
                      className={`rounded-md border p-4 ${login.current ? "border-primary/20 bg-primary/5" : ""}`}
                    >
                      <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
                        <div className="flex items-center gap-3">
                          <div
                            className={`flex h-8 w-8 items-center justify-center rounded-full ${
                              login.status === "success" ? "bg-green-100" : "bg-red-100"
                            }`}
                          >
                            {getDeviceIcon(login.device)}
                          </div>
                          <div>
                            <div className="font-medium">
                              {login.browser} on {login.os}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {login.location} • IP: {login.ip}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="text-sm text-muted-foreground">
                            {new Date(login.date).toLocaleDateString("id-ID", {
                              year: "numeric",
                              month: "short",
                              day: "numeric",
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </div>
                          {getStatusBadge(login.status, login.current)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm">
                  View Full History
                </Button>
                <Button variant="destructive" size="sm" className="gap-1" onClick={() => setShowLogoutAllDialog(true)}>
                  <LogOut className="h-4 w-4" />
                  Log Out All Devices
                </Button>
              </CardFooter>
            </Card>

            {/* Connected Apps */}
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Globe className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Connected Applications</CardTitle>
                </div>
                <CardDescription>Manage third-party applications connected to your account</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {connectedApps.map((app) => (
                    <div key={app.id} className="rounded-md border p-4">
                      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
                        <div className="flex items-center gap-3">
                          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary">
                            {app.icon}
                          </div>
                          <div>
                            <div className="font-medium">{app.name}</div>
                            <div className="text-sm text-muted-foreground">{app.permissions.join(", ")}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="text-sm text-muted-foreground">
                            Last used:{" "}
                            {new Date(app.lastUsed).toLocaleDateString("id-ID", {
                              year: "numeric",
                              month: "short",
                              day: "numeric",
                            })}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="gap-1 text-red-500 hover:bg-red-50 hover:text-red-600"
                            onClick={() => handleRevokeApp(app.id)}
                          >
                            <X className="h-4 w-4" />
                            Revoke Access
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Account Recovery */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <RefreshCw className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Account Recovery</CardTitle>
                </div>
                <CardDescription>Set up recovery options for your account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="recovery-email">Recovery Email</Label>
                  <Input id="recovery-email" type="email" placeholder="<EMAIL>" />
                  <p className="text-xs text-muted-foreground">
                    We'll use this email to help you recover your account if you get locked out
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="recovery-phone">Recovery Phone Number</Label>
                  <Input id="recovery-phone" type="tel" placeholder="+62 812 3456 7890" />
                  <p className="text-xs text-muted-foreground">
                    We'll send a verification code to this number if you need to recover your account
                  </p>
                </div>
                <div className="rounded-md bg-muted p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="mt-0.5 h-5 w-5 text-yellow-500" />
                    <div>
                      <h3 className="font-medium">Security Questions</h3>
                      <p className="text-sm text-muted-foreground">
                        Set up security questions as an additional recovery method.
                      </p>
                      <Button variant="outline" size="sm" className="mt-2">
                        Set Up Questions
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button>Save Recovery Options</Button>
              </CardFooter>
            </Card>

            {/* Privacy Controls */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Privacy Controls</CardTitle>
                </div>
                <CardDescription>Manage your privacy settings and data</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="activity-tracking">Activity Tracking</Label>
                    <p className="text-xs text-muted-foreground">
                      Allow us to collect data about how you use our platform
                    </p>
                  </div>
                  <Switch id="activity-tracking" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="personalization">Personalization</Label>
                    <p className="text-xs text-muted-foreground">
                      Allow us to personalize your experience based on your activity
                    </p>
                  </div>
                  <Switch id="personalization" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="third-party-sharing">Third-Party Data Sharing</Label>
                    <p className="text-xs text-muted-foreground">Allow us to share your data with trusted partners</p>
                  </div>
                  <Switch id="third-party-sharing" />
                </div>
                <Separator className="my-2" />
                <div className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    Download My Data
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start text-red-500 hover:text-red-600">
                    Delete My Account
                  </Button>
                </div>
              </CardContent>
              <CardFooter>
                <Button>Save Privacy Settings</Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Logout All Devices Dialog */}
      <Dialog open={showLogoutAllDialog} onOpenChange={setShowLogoutAllDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Log Out All Devices</DialogTitle>
            <DialogDescription>
              This will log you out from all devices except your current session. Are you sure you want to continue?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4 flex gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => setShowLogoutAllDialog(false)}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button variant="destructive" onClick={() => setShowLogoutAllDialog(false)}>
              <LogOut className="mr-2 h-4 w-4" />
              Log Out All Devices
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Revoke App Access Dialog */}
      <Dialog open={showRevokeDialog} onOpenChange={setShowRevokeDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Revoke Application Access</DialogTitle>
            <DialogDescription>
              This will revoke access for this application. You will need to authorize it again if you want to use it in
              the future.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4 flex gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => setShowRevokeDialog(false)}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button variant="destructive" onClick={() => setShowRevokeDialog(false)}>
              <X className="mr-2 h-4 w-4" />
              Revoke Access
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
