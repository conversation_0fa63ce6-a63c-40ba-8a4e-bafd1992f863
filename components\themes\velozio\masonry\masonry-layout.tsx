"use client"

import type React from "react"

import { useEffect, useRef, useState, useCallback } from "react"
import { useThrottledCallback } from "./use-throttled-callback"

interface MasonryLayoutProps {
  children: React.ReactNode[]
  columnCount?: { mobile: number; tablet: number; desktop: number }
  gap?: number
}

export const MasonryLayout = ({
  children,
  columnCount = { mobile: 2, tablet: 3, desktop: 6 },
  gap = 8,
}: MasonryLayoutProps) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [columns, setColumns] = useState<React.ReactNode[][]>([])
  const [visibleCount, setVisibleCount] = useState(12)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadingRef = useRef<HTMLDivElement>(null)
  const currentColumnCountRef = useRef(0)
  const [initialized, setInitialized] = useState(false)
  const [columnWidth, setColumnWidth] = useState("100%")

  // Determine column count based on screen width
  const getColumnCount = useCallback(() => {
    if (typeof window === "undefined") return columnCount.mobile

    const width = window.innerWidth
    if (width >= 1024) return columnCount.desktop
    if (width >= 768) return columnCount.tablet
    return columnCount.mobile
  }, [columnCount.desktop, columnCount.mobile, columnCount.tablet])

  // Calculate column width based on container width and column count
  const calculateColumnWidth = useCallback(() => {
    if (!containerRef.current) return

    const containerWidth = containerRef.current.clientWidth
    const count = getColumnCount()
    // Calculate width accounting for gaps
    const totalGapWidth = (count - 1) * gap
    const width = (containerWidth - totalGapWidth) / count

    // Set fixed width in pixels
    setColumnWidth(`${width}px`)
  }, [gap, getColumnCount])

  // Distribute items into columns
  const distributeItems = useCallback(() => {
    const count = getColumnCount()
    currentColumnCountRef.current = count

    const visibleItems = children.slice(0, visibleCount)
    const newColumns: React.ReactNode[][] = Array.from({ length: count }, () => [])

    // Simple distribution - can be improved with height calculation
    visibleItems.forEach((child, index) => {
      const columnIndex = index % count
      newColumns[columnIndex].push(
        <div key={index} className="mb-2">
          {child}
        </div>,
      )
    })

    setColumns(newColumns)
    calculateColumnWidth()
  }, [children, getColumnCount, visibleCount, calculateColumnWidth])

  // Throttled resize handler
  const handleResize = useThrottledCallback(() => {
    const newColumnCount = getColumnCount()
    if (newColumnCount !== currentColumnCountRef.current || containerRef.current) {
      distributeItems()
    }
  }, 150)

  // Setup intersection observer for lazy loading
  useEffect(() => {
    if (!loadingRef.current) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting && visibleCount < children.length) {
          // Load next batch of items (6 at a time)
          setVisibleCount((prev) => Math.min(prev + 6, children.length))
        }
      },
      { threshold: 0.1 },
    )

    observerRef.current.observe(loadingRef.current)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [children.length, visibleCount])

  // Initial setup and when visible count changes
  useEffect(() => {
    if (!initialized) {
      distributeItems()
      setInitialized(true)
    } else if (visibleCount > 12) {
      // Only redistribute when loading more items, not on initial render
      distributeItems()
    }
  }, [distributeItems, initialized, visibleCount])

  // Setup resize listener
  useEffect(() => {
    window.addEventListener("resize", handleResize)

    // Initial calculation
    calculateColumnWidth()

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [handleResize, calculateColumnWidth])

  return (
    <div ref={containerRef} className="w-full">
      <div className="flex" style={{ gap: `${gap}px` }}>
        {columns.map((column, columnIndex) => (
          <div
            key={columnIndex}
            className="flex flex-col"
            style={{
              width: columnWidth,
              gap: `${gap}px`,
            }}
          >
            {column}
          </div>
        ))}
      </div>

      {/* Loading trigger element */}
      {visibleCount < children.length && (
        <div ref={loadingRef} className="w-full h-10 flex justify-center items-center mt-4">
          <div className="w-8 h-8 border-4 border-gray-200 border-t-[#ee4d2d] rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  )
}
