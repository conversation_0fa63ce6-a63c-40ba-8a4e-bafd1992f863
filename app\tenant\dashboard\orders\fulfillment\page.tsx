"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Filter,
  Clock,
  Package,
  TruckIcon,
  CheckCircle,
  AlertCircle,
  User,
  Calendar,
  BarChart,
  ShoppingBag,
  Store,
  MapPin,
  ClipboardCheck,
  PencilIcon,
  Eye,
  MoreHorizontal,
  RefreshCw,
  CheckIcon
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk pesanan
const orders = [
  {
    id: "ORD-20240101-001",
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "081234567890",
      avatar: "/api/placeholder/32/32"
    },
    store: {
      name: "TechWorld Store",
      id: "STORE-001"
    },
    orderDate: "2024-01-01T10:30:00",
    total: 1899000,
    items: [
      {
        id: "ITEM-001",
        name: "iPhone Case Premium",
        sku: "CASE-IP15-001",
        price: 299000,
        quantity: 1,
        image: "/api/placeholder/50/50"
      },
      {
        id: "ITEM-002",
        name: "iPhone Screen Protector",
        sku: "SCRN-IP15-001",
        price: 199000,
        quantity: 3,
        image: "/api/placeholder/50/50"
      }
    ],
    status: "processing",
    paymentStatus: "paid",
    fulfillmentStatus: "processing",
    shippingAddress: {
      street: "Jl. Sudirman No. 123",
      city: "Jakarta",
      province: "DKI Jakarta",
      postalCode: "10220",
      country: "Indonesia"
    },
    shippingMethod: "JNE Regular",
    trackingNumber: null,
    notes: "Tolong dibungkus dengan rapi sebagai kado",
    timeline: [
      { time: "2024-01-01T10:30:00", status: "created", note: "Order dibuat oleh pelanggan" },
      { time: "2024-01-01T10:35:00", status: "payment_confirmed", note: "Pembayaran dikonfirmasi" },
      { time: "2024-01-01T14:20:00", status: "processing", note: "Order sedang diproses oleh store" }
    ],
    assignedTo: "Fulfillment Team A",
    priority: "medium",
    estimatedDelivery: "2024-01-05",
    lastUpdated: "2024-01-01T14:20:00"
  },
  {
    id: "ORD-20240102-002",
    customer: {
      name: "Budi Santoso",
      email: "<EMAIL>",
      phone: "081298765432",
      avatar: "/api/placeholder/32/32"
    },
    store: {
      name: "Fashion Zone",
      id: "STORE-002"
    },
    orderDate: "2024-01-02T09:15:00",
    total: 850000,
    items: [
      {
        id: "ITEM-003",
        name: "Kemeja Pria Slim Fit",
        sku: "FASH-SHIRT-001",
        price: 250000,
        quantity: 2,
        image: "/api/placeholder/50/50"
      },
      {
        id: "ITEM-004",
        name: "Celana Jeans Premium",
        sku: "FASH-PANTS-001",
        price: 350000,
        quantity: 1,
        image: "/api/placeholder/50/50"
      }
    ],
    status: "shipped",
    paymentStatus: "paid",
    fulfillmentStatus: "shipped",
    shippingAddress: {
      street: "Jl. Gatot Subroto No. 45",
      city: "Bandung",
      province: "Jawa Barat",
      postalCode: "40112",
      country: "Indonesia"
    },
    shippingMethod: "SiCepat REG",
    trackingNumber: "SCPT00123456789",
    notes: "",
    timeline: [
      { time: "2024-01-02T09:15:00", status: "created", note: "Order dibuat oleh pelanggan" },
      { time: "2024-01-02T09:20:00", status: "payment_confirmed", note: "Pembayaran dikonfirmasi" },
      { time: "2024-01-02T11:30:00", status: "processing", note: "Order sedang diproses oleh store" },
      { time: "2024-01-03T14:00:00", status: "packed", note: "Paket telah dikemas" },
      { time: "2024-01-03T16:45:00", status: "shipped", note: "Paket telah dikirim dengan SiCepat" }
    ],
    assignedTo: "Fulfillment Team B",
    priority: "medium",
    estimatedDelivery: "2024-01-07",
    lastUpdated: "2024-01-03T16:45:00"
  },
  {
    id: "ORD-20240103-003",
    customer: {
      name: "Siti Nuraini",
      email: "<EMAIL>",
      phone: "081256789012",
      avatar: "/api/placeholder/32/32"
    },
    store: {
      name: "Beauty Corner",
      id: "STORE-003"
    },
    orderDate: "2024-01-03T16:20:00",
    total: 1250000,
    items: [
      {
        id: "ITEM-005",
        name: "Skincare Set Premium",
        sku: "BEAUTY-SET-001",
        price: 750000,
        quantity: 1,
        image: "/api/placeholder/50/50"
      },
      {
        id: "ITEM-006",
        name: "Parfum Wanita Eksklusif",
        sku: "BEAUTY-PERF-001",
        price: 500000,
        quantity: 1,
        image: "/api/placeholder/50/50"
      }
    ],
    status: "waiting_pickup",
    paymentStatus: "paid",
    fulfillmentStatus: "ready_for_pickup",
    shippingAddress: {
      street: "Jl. Diponegoro No. 78",
      city: "Surabaya",
      province: "Jawa Timur",
      postalCode: "60115",
      country: "Indonesia"
    },
    shippingMethod: "JNE YES",
    trackingNumber: "JNE00987654321",
    notes: "Mohon dipacking dengan bubble wrap ekstra untuk parfum",
    timeline: [
      { time: "2024-01-03T16:20:00", status: "created", note: "Order dibuat oleh pelanggan" },
      { time: "2024-01-03T16:25:00", status: "payment_confirmed", note: "Pembayaran dikonfirmasi" },
      { time: "2024-01-04T09:00:00", status: "processing", note: "Order sedang diproses oleh store" },
      { time: "2024-01-04T15:30:00", status: "packed", note: "Paket telah dikemas" },
      { time: "2024-01-04T16:00:00", status: "ready_for_pickup", note: "Paket siap untuk dijemput kurir" }
    ],
    assignedTo: "Fulfillment Team C",
    priority: "high",
    estimatedDelivery: "2024-01-06",
    lastUpdated: "2024-01-04T16:00:00"
  },
  {
    id: "ORD-20240104-004",
    customer: {
      name: "Deni Hermawan",
      email: "<EMAIL>",
      phone: "081278901234",
      avatar: "/api/placeholder/32/32"
    },
    store: {
      name: "Electronic Hub",
      id: "STORE-004"
    },
    orderDate: "2024-01-04T11:05:00",
    total: 4500000,
    items: [
      {
        id: "ITEM-007",
        name: "Headphone Wireless Premium",
        sku: "ELEC-HP-001",
        price: 2500000,
        quantity: 1,
        image: "/api/placeholder/50/50"
      },
      {
        id: "ITEM-008",
        name: "Smartwatch Series 5",
        sku: "ELEC-SW-001",
        price: 2000000,
        quantity: 1,
        image: "/api/placeholder/50/50"
      }
    ],
    status: "delivered",
    paymentStatus: "paid",
    fulfillmentStatus: "delivered",
    shippingAddress: {
      street: "Jl. Ahmad Yani No. 56",
      city: "Medan",
      province: "Sumatera Utara",
      postalCode: "20111",
      country: "Indonesia"
    },
    shippingMethod: "AnterAja Same Day",
    trackingNumber: "ANTER00123456789",
    notes: "",
    timeline: [
      { time: "2024-01-04T11:05:00", status: "created", note: "Order dibuat oleh pelanggan" },
      { time: "2024-01-04T11:10:00", status: "payment_confirmed", note: "Pembayaran dikonfirmasi" },
      { time: "2024-01-04T13:00:00", status: "processing", note: "Order sedang diproses oleh store" },
      { time: "2024-01-04T15:30:00", status: "packed", note: "Paket telah dikemas" },
      { time: "2024-01-04T16:45:00", status: "shipped", note: "Paket telah dikirim dengan AnterAja" },
      { time: "2024-01-05T14:20:00", status: "delivered", note: "Paket telah diterima oleh pelanggan" }
    ],
    assignedTo: "Fulfillment Team A",
    priority: "low",
    estimatedDelivery: "2024-01-05",
    lastUpdated: "2024-01-05T14:20:00"
  },
  {
    id: "ORD-20240105-005",
    customer: {
      name: "Maya Putri",
      email: "<EMAIL>",
      phone: "081290123456",
      avatar: "/api/placeholder/32/32"
    },
    store: {
      name: "Home Decor Store",
      id: "STORE-005"
    },
    orderDate: "2024-01-05T13:45:00",
    total: 3200000,
    items: [
      {
        id: "ITEM-009",
        name: "Lampu Gantung Vintage",
        sku: "HOME-LIGHT-001",
        price: 1200000,
        quantity: 1,
        image: "/api/placeholder/50/50"
      },
      {
        id: "ITEM-010",
        name: "Vas Bunga Keramik",
        sku: "HOME-VASE-001",
        price: 500000,
        quantity: 2,
        image: "/api/placeholder/50/50"
      },
      {
        id: "ITEM-011",
        name: "Frame Foto Set",
        sku: "HOME-FRAME-001",
        price: 250000,
        quantity: 4,
        image: "/api/placeholder/50/50"
      }
    ],
    status: "pending",
    paymentStatus: "pending",
    fulfillmentStatus: "pending",
    shippingAddress: {
      street: "Jl. Imam Bonjol No. 123",
      city: "Denpasar",
      province: "Bali",
      postalCode: "80113",
      country: "Indonesia"
    },
    shippingMethod: "JNE REG",
    trackingNumber: null,
    notes: "Tolong dipacking dengan sangat hati-hati karena barang mudah pecah",
    timeline: [
      { time: "2024-01-05T13:45:00", status: "created", note: "Order dibuat oleh pelanggan" }
    ],
    assignedTo: null,
    priority: "low",
    estimatedDelivery: "2024-01-10",
    lastUpdated: "2024-01-05T13:45:00"
  }
]

// Fungsi untuk generate badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "pending":
      return <Badge variant="outline" className="bg-gray-100 text-gray-800"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
    case "processing": 
      return <Badge variant="outline" className="bg-blue-100 text-blue-800"><RefreshCw className="h-3 w-3 mr-1" />Processing</Badge>
    case "packed":
      return <Badge variant="outline" className="bg-purple-100 text-purple-800"><Package className="h-3 w-3 mr-1" />Packed</Badge>
    case "ready_for_pickup":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800"><ClipboardCheck className="h-3 w-3 mr-1" />Ready for Pickup</Badge>
    case "waiting_pickup":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Waiting Pickup</Badge>
    case "shipped":
      return <Badge variant="outline" className="bg-indigo-100 text-indigo-800"><TruckIcon className="h-3 w-3 mr-1" />Shipped</Badge>
    case "delivered":
      return <Badge variant="outline" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Delivered</Badge>
    case "cancelled":
      return <Badge variant="outline" className="bg-red-100 text-red-800"><AlertCircle className="h-3 w-3 mr-1" />Cancelled</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk generate badge payment status
function getPaymentBadge(status: string) {
  switch (status) {
    case "pending":
      return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
    case "paid":
      return <Badge variant="outline" className="bg-green-100 text-green-800"><CheckIcon className="h-3 w-3 mr-1" />Paid</Badge>
    case "failed":
      return <Badge variant="destructive"><AlertCircle className="h-3 w-3 mr-1" />Failed</Badge>
    case "refunded":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800"><RefreshCw className="h-3 w-3 mr-1" />Refunded</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

// Fungsi untuk format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric'
  })
}

// Fungsi untuk format waktu
function formatDateTime(dateString: string) {
  return new Date(dateString).toLocaleString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function OrderFulfillmentPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null)

  // Filter pesanan berdasarkan status dan pencarian
  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          order.store.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || order.fulfillmentStatus === statusFilter
    return matchesSearch && matchesStatus
  })

  // Menghitung statistik pesanan
  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.fulfillmentStatus === "pending").length,
    processing: orders.filter(o => o.fulfillmentStatus === "processing").length,
    packed: orders.filter(o => o.fulfillmentStatus === "packed").length,
    readyForPickup: orders.filter(o => o.fulfillmentStatus === "ready_for_pickup").length,
    shipped: orders.filter(o => o.fulfillmentStatus === "shipped").length,
    delivered: orders.filter(o => o.fulfillmentStatus === "delivered").length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/orders">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order Fulfillment</h1>
            <p className="text-muted-foreground">
              Kelola proses pengiriman dan pemenuhan pesanan
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-7">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
            <RefreshCw className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.processing}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Packed</CardTitle>
            <Package className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.packed}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ready</CardTitle>
            <ClipboardCheck className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.readyForPickup}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shipped</CardTitle>
            <TruckIcon className="h-4 w-4 text-indigo-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-600">{stats.shipped}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.delivered}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari order ID, customer, atau store..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="packed">Packed</option>
              <option value="ready_for_pickup">Ready for Pickup</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Orders List */}
      <div className="space-y-4">
        {filteredOrders.map((order) => (
          <Card key={order.id} className={`hover:shadow-md transition-shadow ${selectedOrder === order.id ? 'ring-2 ring-primary' : ''}`}>
            <CardContent className="p-6">
              <div className="flex flex-col gap-4">
                {/* Order Header */}
                <div className="flex flex-col md:flex-row justify-between gap-4">
                  <div className="flex items-start gap-3">
                    <div>
                      <h3 className="text-lg font-semibold">{order.id}</h3>
                      <div className="flex items-center gap-3 mt-1">
                        {getStatusBadge(order.fulfillmentStatus)}
                        {getPaymentBadge(order.paymentStatus)}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-muted-foreground text-sm">Order Date: {formatDate(order.orderDate)}</p>
                    <p className="font-semibold text-green-600 mt-1">{formatCurrency(order.total)}</p>
                  </div>
                </div>

                {/* Order Info */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                  {/* Customer */}
                  <div className="flex items-start gap-3">
                    <div className="h-8 w-8 rounded-full overflow-hidden bg-gray-100">
                      <img src={order.customer.avatar} alt={order.customer.name} className="h-full w-full object-cover" />
                    </div>
                    <div>
                      <h4 className="font-medium flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        {order.customer.name}
                      </h4>
                      <p className="text-sm text-muted-foreground">{order.customer.email}</p>
                      <p className="text-sm text-muted-foreground">{order.customer.phone}</p>
                    </div>
                  </div>

                  {/* Store */}
                  <div>
                    <h4 className="font-medium flex items-center gap-2">
                      <Store className="h-4 w-4 text-muted-foreground" />
                      Store
                    </h4>
                    <p className="text-sm">{order.store.name}</p>
                    <p className="text-sm text-muted-foreground">ID: {order.store.id}</p>
                  </div>

                  {/* Shipping */}
                  <div>
                    <h4 className="font-medium flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      Pengiriman
                    </h4>
                    <p className="text-sm">{order.shippingMethod}</p>
                    {order.trackingNumber ? (
                      <p className="text-sm font-medium">#{order.trackingNumber}</p>
                    ) : (
                      <p className="text-sm text-muted-foreground">No tracking yet</p>
                    )}
                  </div>
                </div>

                {/* Items */}
                <div className="mt-2">
                  <h4 className="font-medium mb-2">Items</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex items-center gap-3 bg-muted/50 p-2 rounded-md">
                        <div className="h-10 w-10 rounded overflow-hidden bg-muted">
                          <img src={item.image} alt={item.name} className="h-full w-full object-cover" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="font-medium text-sm truncate">{item.name}</p>
                          <div className="flex justify-between text-sm text-muted-foreground">
                            <span>SKU: {item.sku}</span>
                            <span>{item.quantity} x {formatCurrency(item.price)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Shipping Address */}
                <div className="mt-2 p-3 bg-muted/50 rounded-md">
                  <h4 className="font-medium flex items-center gap-2 mb-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    Alamat Pengiriman
                  </h4>
                  <p className="text-sm">{order.shippingAddress.street}</p>
                  <p className="text-sm">{order.shippingAddress.city}, {order.shippingAddress.province}, {order.shippingAddress.postalCode}</p>
                  <p className="text-sm">{order.shippingAddress.country}</p>
                </div>

                {/* Timeline */}
                {order.timeline.length > 0 && (
                  <div className="mt-2">
                    <h4 className="font-medium flex items-center gap-2 mb-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      Timeline
                    </h4>
                    <div className="space-y-3">
                      {order.timeline.map((event, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <div className="h-5 w-5 rounded-full bg-primary flex items-center justify-center text-white text-xs">
                            {index + 1}
                          </div>
                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row sm:justify-between">
                              <p className="font-medium text-sm capitalize">
                                {event.status.replace(/_/g, ' ')}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {formatDateTime(event.time)}
                              </p>
                            </div>
                            <p className="text-sm text-muted-foreground">{event.note}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Notes */}
                {order.notes && (
                  <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <h4 className="text-sm font-medium text-yellow-800 mb-1">Customer Notes</h4>
                    <p className="text-sm">{order.notes}</p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex flex-wrap gap-2 pt-4 border-t">
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>

                  {order.fulfillmentStatus === "pending" && (
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Process Order
                    </Button>
                  )}

                  {order.fulfillmentStatus === "processing" && (
                    <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                      <Package className="h-4 w-4 mr-2" />
                      Mark as Packed
                    </Button>
                  )}

                  {order.fulfillmentStatus === "packed" && (
                    <Button size="sm" className="bg-yellow-600 hover:bg-yellow-700">
                      <ClipboardCheck className="h-4 w-4 mr-2" />
                      Ready for Pickup
                    </Button>
                  )}

                  {order.fulfillmentStatus === "ready_for_pickup" && (
                    <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
                      <TruckIcon className="h-4 w-4 mr-2" />
                      Mark as Shipped
                    </Button>
                  )}

                  {order.fulfillmentStatus === "shipped" && (
                    <Button size="sm" className="bg-green-600 hover:bg-green-700">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Mark as Delivered
                    </Button>
                  )}

                  <Button size="sm" variant="outline">
                    <PencilIcon className="h-4 w-4 mr-2" />
                    Edit
                  </Button>

                  {!order.trackingNumber && order.fulfillmentStatus !== "pending" && (
                    <Button size="sm" variant="outline">
                      <TruckIcon className="h-4 w-4 mr-2" />
                      Add Tracking
                    </Button>
                  )}

                  <Button size="sm" variant="ghost">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredOrders.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Tidak ada pesanan ditemukan</h3>
            <p className="text-muted-foreground mb-4">
              Tidak ada pesanan yang cocok dengan filter Anda
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
