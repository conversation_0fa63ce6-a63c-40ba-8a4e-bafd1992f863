"use client"
import Link from "next/link"
import {
  Package,
  Heart,
  Award,
  MapPin,
  Truck,
  FileText,
  User,
  HeadphonesIcon,
  ShoppingBag,
  ChevronRight,
  Store,
  DollarSign,
  Bell,
} from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { QuickActions } from "@/components/dashboard/quick-actions"
import { ActivityFeed } from "@/components/dashboard/activity-feed"
import { useAuth } from "@/contexts/auth-context"
import { useIsMobile } from "@/hooks/use-mobile"
import { PullToRefresh } from "@/components/buyer/mobile/pull-to-refresh"
import { NotificationOptin } from "@/components/buyer/mobile/notification-optin"
import { SwipeGallery } from "@/components/buyer/mobile/swipe-gallery"

const quickActions = [
  {
    name: "Track Packages",
    icon: Truck,
    href: "/buyer/dashboard/orders/tracking",
  },
  {
    name: "View Orders",
    icon: FileText,
    href: "/buyer/dashboard/orders",
  },
  {
    name: "Edit Profile",
    icon: User,
    href: "/buyer/dashboard/account/profile",
  },
  {
    name: "Contact Support",
    icon: HeadphonesIcon,
    href: "/buyer/dashboard/customer-service",
  },
]

const activityItems = [
  {
    id: "1",
    user: { name: "You", email: "<EMAIL>" },
    action: "placed an order",
    target: "#ORD-12345",
    date: "Just now",
    status: "success" as const,
  },
  {
    id: "2",
    user: { name: "You", email: "<EMAIL>" },
    action: "added a product to",
    target: "wishlist",
    date: "2 hours ago",
    status: "info" as const,
  },
  {
    id: "3",
    user: { name: "You", email: "<EMAIL>" },
    action: "wrote a review for",
    target: "Kemeja Casual",
    date: "Yesterday",
    status: "success" as const,
  },
  {
    id: "4",
    user: { name: "System", email: "<EMAIL>" },
    action: "processed your refund for",
    target: "#ORD-12340",
    date: "3 days ago",
    status: "info" as const,
  },
  {
    id: "5",
    user: { name: "You", email: "<EMAIL>" },
    action: "earned reward points from",
    target: "your purchase",
    date: "1 week ago",
    status: "success" as const,
  },
]

// Gallery data - diubah untuk menyesuaikan dengan format yang diharapkan oleh SwipeGallery
const galleryImages = [
  {
    src: "/product-image-1.png",
    alt: "New Arrivals",
  },
  {
    src: "/product-image-2.png",
    alt: "Flash Sale",
  },
  {
    src: "/product-image-3.png",
    alt: "Trending Now",
  },
  {
    src: "/product-image-4.png",
    alt: "Seasonal Collection",
  },
]

export default function BuyerDashboardPage() {
  const { user } = useAuth()
  const isMobile = useIsMobile()
  const currentHour = new Date().getHours()

  // Cek status user
  const hasStore = user?.storeId !== undefined
  const isAffiliate = user?.isAffiliate === true

  // Tampilkan user status untuk debugging
  console.log("Current user:", user?.name, user?.email)
  console.log("Has store:", hasStore, "Is affiliate:", isAffiliate)

  let greeting = "Good Morning"
  if (currentHour >= 12 && currentHour < 17) {
    greeting = "Good Afternoon"
  } else if (currentHour >= 17) {
    greeting = "Good Evening"
  }

  const handleRefresh = async () => {
    // Simulasi refresh data
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        console.log("Data refreshed")
        resolve()
      }, 1500)
    })
  }

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      <div className="space-y-6">
        {/* Notification Opt-in untuk mobile */}
        {isMobile && <NotificationOptin />}

        {/* Greeting Banner */}
        <div className="rounded-lg border border-border/40 bg-card p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                {greeting}, {user?.name || "User"}!
              </h1>
              <p className="mt-1 text-muted-foreground">Here's what's happening with your account today.</p>
              {/* Tampilkan status user untuk debugging */}
              <div className="mt-2 text-sm text-muted-foreground">
                <Badge className="mr-2">Email: {user?.email}</Badge>
                {hasStore && (
                  <Badge variant="outline" className="mr-2 bg-green-100">
                    Store Owner
                  </Badge>
                )}
                {isAffiliate && (
                  <Badge variant="outline" className="bg-blue-100">
                    Affiliate
                  </Badge>
                )}
              </div>
            </div>
            <div className="mt-4 flex items-center gap-2 md:mt-0">
              <Button variant="outline" size="sm" className="gap-1">
                <Bell className="h-4 w-4" />
                <span className="hidden sm:inline">Notifications</span>
                <Badge className="ml-1 h-5 w-5 rounded-full p-0 text-xs">3</Badge>
              </Button>
              <Button size="sm" className="gap-1">
                <ShoppingBag className="h-4 w-4" />
                <span className="hidden sm:inline">Shop Now</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Komponen SwipeGallery dihapus dari tampilan mobile */}

        {/* Account Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Package className="h-8 w-8 text-primary" />
                  <div className="text-2xl font-bold">3</div>
                </div>
                <div className="flex flex-col items-end">
                  <Badge variant="outline" className="mb-1">
                    2 In Progress
                  </Badge>
                  <Badge variant="outline">1 Delivered</Badge>
                </div>
              </div>
              <Button variant="link" size="sm" asChild className="mt-2 px-0">
                <Link href="/buyer/dashboard/orders" className="flex items-center">
                  View All Orders
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Wishlist</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Heart className="h-8 w-8 text-red-500" />
                  <div className="text-2xl font-bold">12</div>
                </div>
                <div className="flex -space-x-2">
                  <div className="h-8 w-8 rounded-full bg-gray-200"></div>
                  <div className="h-8 w-8 rounded-full bg-gray-300"></div>
                  <div className="h-8 w-8 rounded-full bg-gray-400"></div>
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-500 text-xs text-white">
                    +9
                  </div>
                </div>
              </div>
              <Button variant="link" size="sm" asChild className="mt-2 px-0">
                <Link href="/buyer/dashboard/wishlist" className="flex items-center">
                  View Wishlist
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Rewards</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Award className="h-8 w-8 text-yellow-500" />
                  <div className="text-2xl font-bold">750</div>
                </div>
                <Badge>Silver Tier</Badge>
              </div>
              <div className="mt-2 space-y-1">
                <div className="flex items-center justify-between text-xs">
                  <span>Progress to Gold</span>
                  <span>750/1000 points</span>
                </div>
                <Progress value={75} className="h-2" />
              </div>
              <Button variant="link" size="sm" asChild className="mt-2 px-0">
                <Link href="/buyer/dashboard/rewards" className="flex items-center">
                  View Rewards
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Saved Addresses</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MapPin className="h-8 w-8 text-blue-500" />
                  <div className="text-2xl font-bold">2</div>
                </div>
                <Badge variant="outline" className="capitalize">
                  Home, Office
                </Badge>
              </div>
              <Button variant="link" size="sm" asChild className="mt-2 px-0">
                <Link href="/buyer/dashboard/account/addresses" className="flex items-center">
                  Manage Addresses
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Quick Access and Activity */}
        <div className="grid gap-6 md:grid-cols-2">
          <QuickActions
            title="Quick Access"
            description="Shortcuts to frequently used features"
            actions={quickActions}
          />
          <ActivityFeed title="Recent Activity" description="Your recent actions and updates" items={activityItems} />
        </div>

        {/* Conditional Module - Store Registration Banner */}
        {!hasStore && (
          <Card className="overflow-hidden border-0 bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg">
            <CardContent className="p-0">
              <div className="flex flex-col md:flex-row">
                <div className="flex-1 p-6">
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-white/20 backdrop-blur">
                    <Store className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="mb-2 text-xl font-bold">Mulai Bisnis Online Anda Sekarang!</h3>
                  <p className="mb-4 text-white/80">
                    Jual produk Anda ke ribuan pembeli, kelola inventori dengan mudah, dan terima pembayaran secara
                    instan.
                  </p>
                  <div className="mb-4 flex flex-wrap gap-3">
                    <Badge variant="secondary" className="bg-white/20 text-white hover:bg-white/30">
                      Komisi Rendah
                    </Badge>
                    <Badge variant="secondary" className="bg-white/20 text-white hover:bg-white/30">
                      Pembayaran Cepat
                    </Badge>
                    <Badge variant="secondary" className="bg-white/20 text-white hover:bg-white/30">
                      Dukungan 24/7
                    </Badge>
                  </div>
                  <Button asChild className="bg-white text-blue-600 hover:bg-white/90">
                    <Link href="/buyer/dashboard/store-application">Daftar Toko Sekarang</Link>
                  </Button>
                </div>
                <div className="hidden md:block md:w-1/3">
                  <div className="h-full bg-[url('/online-store.png')] bg-cover bg-center"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Conditional Module - Affiliate Program Banner */}
        {!isAffiliate && (
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-center gap-4 sm:flex-row sm:justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-orange-500/10">
                    <DollarSign className="h-6 w-6 text-orange-500" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Join Affiliate Program</h3>
                    <p className="text-sm text-muted-foreground">Earn commission by promoting products</p>
                  </div>
                </div>
                <Button variant="outline" asChild>
                  <Link href="/buyer/dashboard/affiliate-application">Learn More</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recommended Products */}
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold">Recommended For You</h2>
            <Button variant="link" asChild>
              <Link href="/buyer/marketplace">View All</Link>
            </Button>
          </div>

          <Tabs defaultValue="recommended">
            <TabsList className="mb-4">
              <TabsTrigger value="recommended" className="flex items-center gap-1">
                Recommended
              </TabsTrigger>
              <TabsTrigger value="new-arrivals" className="flex items-center gap-1">
                New Arrivals
              </TabsTrigger>
              <TabsTrigger value="wishlist" className="flex items-center gap-1">
                Wishlist Updates
              </TabsTrigger>
            </TabsList>

            <TabsContent value="recommended" className="mt-0">
              <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                {[1, 2, 3, 4, 5].map((item) => (
                  <Card key={item} className="overflow-hidden">
                    <div className="aspect-square w-full bg-gray-100"></div>
                    <CardContent className="p-3">
                      <h3 className="line-clamp-1 font-medium">Product Name {item}</h3>
                      <p className="text-sm font-bold">{formatCurrency(150000 * item)}</p>
                      <div className="mt-2 flex items-center justify-between">
                        <Badge variant="outline" className="text-xs">
                          4.5 ★
                        </Badge>
                        <Button size="icon" variant="ghost" className="h-8 w-8">
                          <Heart className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="new-arrivals" className="mt-0">
              <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                {[6, 7, 8, 9, 10].map((item) => (
                  <Card key={item} className="overflow-hidden">
                    <div className="aspect-square w-full bg-gray-100"></div>
                    <CardContent className="p-3">
                      <h3 className="line-clamp-1 font-medium">New Product {item}</h3>
                      <p className="text-sm font-bold">{formatCurrency(200000 * (item - 5))}</p>
                      <div className="mt-2 flex items-center justify-between">
                        <Badge variant="secondary" className="text-xs">
                          New
                        </Badge>
                        <Button size="icon" variant="ghost" className="h-8 w-8">
                          <Heart className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="wishlist" className="mt-0">
              <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                {[11, 12, 13, 14, 15].map((item) => (
                  <Card key={item} className="overflow-hidden">
                    <div className="aspect-square w-full bg-gray-100"></div>
                    <CardContent className="p-3">
                      <h3 className="line-clamp-1 font-medium">Wishlist Item {item}</h3>
                      <div className="flex items-center gap-1">
                        <p className="text-sm font-bold">{formatCurrency(180000)}</p>
                        <p className="text-xs text-red-500 line-through">{formatCurrency(220000)}</p>
                      </div>
                      <div className="mt-2 flex items-center justify-between">
                        <Badge variant="destructive" className="text-xs">
                          Price Drop
                        </Badge>
                        <Button size="icon" variant="ghost" className="h-8 w-8">
                          <Heart className="h-4 w-4 fill-current text-red-500" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </PullToRefresh>
  )
}
