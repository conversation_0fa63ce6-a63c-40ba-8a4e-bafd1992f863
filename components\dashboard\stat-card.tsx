"use client"

import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { cva, type VariantProps } from "class-variance-authority"
import type { LucideIcon } from "lucide-react"

const statCardVariants = cva("relative overflow-hidden rounded-lg border border-border/40 bg-card p-6 shadow-sm", {
  variants: {
    variant: {
      default: "",
      primary: "border-primary/20 bg-primary/5",
      success: "border-green-500/20 bg-green-500/5",
      warning: "border-yellow-500/20 bg-yellow-500/5",
      danger: "border-red-500/20 bg-red-500/5",
    },
  },
  defaultVariants: {
    variant: "default",
  },
})

interface StatCardProps extends VariantProps<typeof statCardVariants> {
  title: string
  value: string | number
  description?: string
  icon?: LucideIcon
  trend?: "up" | "down" | "neutral"
  trendValue?: string
  className?: string
}

export function StatCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  trendValue,
  variant,
  className,
}: StatCardProps) {
  // Gunakan useState dan useEffect untuk menghindari hydration error
  const [barWidth, setBarWidth] = useState(0);
  
  useEffect(() => {
    // Hanya dijalankan di client-side
    setBarWidth(Math.random() * 100);
  }, []);
  
  return (
    <div className={cn(statCardVariants({ variant }), className)}>
      <div className="flex items-start justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <h3 className="mt-2 text-2xl font-semibold tracking-tight">{value}</h3>
          {description && <p className="text-xs text-muted-foreground mt-1">{description}</p>}
          {trend && trendValue && (
            <div className="mt-3 flex items-center text-xs">
              <span
                className={cn(
                  "mr-1 flex items-center",
                  trend === "up" && "text-green-500",
                  trend === "down" && "text-red-500",
                  trend === "neutral" && "text-muted-foreground",
                )}
              >
                {trend === "up" && "↑"}
                {trend === "down" && "↓"}
                {trend === "neutral" && "→"}
                {trendValue}
              </span>
              <span className="text-muted-foreground">dibanding bulan lalu</span>
            </div>
          )}
        </div>
        {Icon && (
          <div
            className={cn(
              "flex h-12 w-12 items-center justify-center rounded-full",
              variant === "primary" && "bg-primary/10 text-primary",
              variant === "success" && "bg-green-500/10 text-green-500",
              variant === "warning" && "bg-yellow-500/10 text-yellow-500",
              variant === "danger" && "bg-red-500/10 text-red-500",
              !variant && "bg-muted text-muted-foreground",
            )}
          >
            <Icon className="h-6 w-6" />
          </div>
        )}
      </div>
      <div className="absolute bottom-0 left-0 h-1.5 w-full bg-muted/50">
        <div
          className={cn(
            "h-full",
            variant === "primary" && "bg-primary",
            variant === "success" && "bg-green-500",
            variant === "warning" && "bg-yellow-500",
            variant === "danger" && "bg-red-500",
            !variant && "bg-foreground/20",
          )}
          style={{ width: `${barWidth}%` }}
        />
      </div>
    </div>
  )
}
