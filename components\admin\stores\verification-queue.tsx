"use client"

import { useState } from "react"
import { Search, CheckCircle, XCircle, AlertCircle, Eye } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "@/components/ui/use-toast"

// Mock data for verification queue
const mockVerificationQueue = [
  {
    id: "store-1",
    name: "Eco Friendly Products",
    tenant: "Green Earth",
    owner: "Lisa Johnson",
    applicationDate: "2023-05-15",
    status: "pending",
    description: "We sell eco-friendly household products made from sustainable materials.",
    category: "Home & Garden",
    documents: ["business_license.pdf", "id_verification.pdf", "product_certification.pdf"],
    requirements: [
      { id: "req-1", name: "Business License", status: "verified" },
      { id: "req-2", name: "Owner ID Verification", status: "verified" },
      { id: "req-3", name: "Product Certification", status: "pending" },
      { id: "req-4", name: "Terms Agreement", status: "verified" },
    ],
  },
  {
    id: "store-2",
    name: "Handmade Crafts",
    tenant: "Artisan Collective",
    owner: "Mark Wilson",
    applicationDate: "2023-05-18",
    status: "pending",
    description: "Handcrafted items made by local artisans using traditional techniques.",
    category: "Arts & Crafts",
    documents: ["business_license.pdf", "id_verification.pdf"],
    requirements: [
      { id: "req-1", name: "Business License", status: "verified" },
      { id: "req-2", name: "Owner ID Verification", status: "pending" },
      { id: "req-3", name: "Product Certification", status: "not_required" },
      { id: "req-4", name: "Terms Agreement", status: "verified" },
    ],
  },
  {
    id: "store-3",
    name: "Vintage Collectibles",
    tenant: "Retro Treasures",
    owner: "Sarah Brown",
    applicationDate: "2023-05-20",
    status: "pending",
    description: "Curated collection of vintage items and collectibles from the 50s to the 90s.",
    category: "Collectibles & Antiques",
    documents: ["business_license.pdf", "id_verification.pdf", "inventory_list.pdf"],
    requirements: [
      { id: "req-1", name: "Business License", status: "verified" },
      { id: "req-2", name: "Owner ID Verification", status: "verified" },
      { id: "req-3", name: "Product Certification", status: "not_required" },
      { id: "req-4", name: "Terms Agreement", status: "verified" },
    ],
  },
]

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const statusMap: Record<
    string,
    { label: string; variant: "default" | "outline" | "secondary" | "destructive" | "success" }
  > = {
    verified: { label: "Verified", variant: "success" },
    pending: { label: "Pending", variant: "secondary" },
    rejected: { label: "Rejected", variant: "destructive" },
    not_required: { label: "Not Required", variant: "outline" },
  }

  const { label, variant } = statusMap[status] || { label: status, variant: "default" }

  return <Badge variant={variant}>{label}</Badge>
}

export function VerificationQueue() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStore, setSelectedStore] = useState<(typeof mockVerificationQueue)[0] | null>(null)
  const [feedbackText, setFeedbackText] = useState("")
  const [showApproveDialog, setShowApproveDialog] = useState(false)
  const [showRejectDialog, setShowRejectDialog] = useState(false)
  const [verificationQueue, setVerificationQueue] = useState(mockVerificationQueue)
  const [requirementStatus, setRequirementStatus] = useState<Record<string, boolean>>({})

  // Initialize requirement status from selected store
  const initializeRequirementStatus = (store: (typeof mockVerificationQueue)[0]) => {
    const status: Record<string, boolean> = {}
    store.requirements.forEach((req) => {
      status[req.id] = req.status === "verified"
    })
    setRequirementStatus(status)
  }

  // Update requirement status
  const updateRequirementStatus = (reqId: string, checked: boolean) => {
    setRequirementStatus((prev) => ({
      ...prev,
      [reqId]: checked,
    }))
  }

  // Filter stores based on search term
  const filteredStores = verificationQueue.filter((store) => {
    return (
      store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.tenant.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })

  // Handle store selection
  const handleSelectStore = (store: (typeof mockVerificationQueue)[0]) => {
    setSelectedStore(store)
    setFeedbackText("")
    initializeRequirementStatus(store)
  }

  // Handle approve store
  const handleApproveStore = () => {
    setShowApproveDialog(true)
  }

  // Handle reject store
  const handleRejectStore = () => {
    setShowRejectDialog(true)
  }

  // Confirm approve store
  const confirmApproveStore = () => {
    if (selectedStore) {
      // Here you would call your API to approve the store
      console.log(`Approving store: ${selectedStore.name}`)

      // Update local state
      setVerificationQueue((prev) => prev.filter((store) => store.id !== selectedStore.id))
      setSelectedStore(null)
      setShowApproveDialog(false)

      // Show toast notification
      toast({
        title: "Store Approved",
        description: `${selectedStore.name} has been approved successfully.`,
      })
    }
  }

  // Confirm reject store
  const confirmRejectStore = () => {
    if (selectedStore) {
      // Here you would call your API to reject the store
      console.log(`Rejecting store: ${selectedStore.name}`)
      console.log(`Feedback: ${feedbackText}`)

      // Update local state
      setVerificationQueue((prev) => prev.filter((store) => store.id !== selectedStore.id))
      setSelectedStore(null)
      setShowRejectDialog(false)

      // Show toast notification
      toast({
        title: "Store Rejected",
        description: `${selectedStore.name} has been rejected.`,
        variant: "destructive",
      })
    }
  }

  // View document
  const handleViewDocument = (document: string) => {
    // Here you would open the document in a new tab or modal
    console.log(`Viewing document: ${document}`)
    toast({
      title: "Opening Document",
      description: `Opening ${document} for review.`,
    })
  }

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
      <div className="md:col-span-2">
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search stores..."
              className="w-full pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Store Name</TableHead>
                  <TableHead>Tenant</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead>Application Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStores.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No stores found in verification queue.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredStores.map((store) => (
                    <TableRow
                      key={store.id}
                      className={selectedStore?.id === store.id ? "bg-muted/50" : ""}
                      onClick={() => handleSelectStore(store)}
                      style={{ cursor: "pointer" }}
                    >
                      <TableCell className="font-medium">{store.name}</TableCell>
                      <TableCell>{store.tenant}</TableCell>
                      <TableCell>{store.owner}</TableCell>
                      <TableCell>{new Date(store.applicationDate).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <StatusBadge status={store.status} />
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleSelectStore(store)
                          }}
                        >
                          Review
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <div>
        {selectedStore ? (
          <Card>
            <CardHeader>
              <CardTitle>{selectedStore.name}</CardTitle>
              <CardDescription>
                Application submitted on {new Date(selectedStore.applicationDate).toLocaleDateString()}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Tabs defaultValue="details">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="requirements">Requirements</TabsTrigger>
                  <TabsTrigger value="documents">Documents</TabsTrigger>
                </TabsList>
                <TabsContent value="details" className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium">Owner</h4>
                    <p className="text-sm text-muted-foreground">{selectedStore.owner}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Tenant</h4>
                    <p className="text-sm text-muted-foreground">{selectedStore.tenant}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Category</h4>
                    <p className="text-sm text-muted-foreground">{selectedStore.category}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Description</h4>
                    <p className="text-sm text-muted-foreground">{selectedStore.description}</p>
                  </div>
                </TabsContent>
                <TabsContent value="requirements" className="space-y-4">
                  <div className="space-y-4">
                    {selectedStore.requirements.map((req) => (
                      <div key={req.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            id={req.id}
                            checked={requirementStatus[req.id] || req.status === "verified"}
                            onCheckedChange={(checked) => updateRequirementStatus(req.id, checked as boolean)}
                            disabled={req.status === "not_required"}
                          />
                          <Label htmlFor={req.id}>{req.name}</Label>
                        </div>
                        <StatusBadge status={req.status} />
                      </div>
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="documents" className="space-y-4">
                  <div className="space-y-2">
                    {selectedStore.documents.map((doc, index) => (
                      <div key={index} className="flex items-center justify-between rounded-md border p-2">
                        <span className="text-sm">{doc}</span>
                        <Button variant="ghost" size="sm" onClick={() => handleViewDocument(doc)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View
                        </Button>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>

              <Separator />

              <div>
                <h4 className="mb-2 text-sm font-medium">Feedback to Applicant</h4>
                <Textarea
                  placeholder="Provide feedback or request additional information..."
                  value={feedbackText}
                  onChange={(e) => setFeedbackText(e.target.value)}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" className="gap-1" onClick={handleRejectStore}>
                <XCircle className="h-4 w-4" />
                Reject
              </Button>
              <Button className="gap-1" onClick={handleApproveStore}>
                <CheckCircle className="h-4 w-4" />
                Approve
              </Button>
            </CardFooter>
          </Card>
        ) : (
          <Card>
            <CardContent className="flex h-[400px] items-center justify-center">
              <div className="text-center">
                <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground" />
                <h3 className="mt-2 text-lg font-medium">No Store Selected</h3>
                <p className="text-sm text-muted-foreground">Select a store from the list to review its application.</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Approve Store Dialog */}
      <AlertDialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Approve Store</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to approve {selectedStore?.name}? This will make the store live on the platform.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmApproveStore}>Approve</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Reject Store Dialog */}
      <AlertDialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject Store</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to reject {selectedStore?.name}? The applicant will be notified with your feedback.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmRejectStore} className="bg-destructive text-destructive-foreground">
              Reject
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
