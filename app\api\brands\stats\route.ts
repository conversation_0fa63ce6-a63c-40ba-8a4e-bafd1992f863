import { NextRequest, NextResponse } from 'next/server';
import { brandService } from '@/lib/services/brands';

// GET - Mendapatkan statistik brands
export async function GET(request: NextRequest) {
  try {
    const stats = await brandService.getBrandStats();
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching brand stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch brand stats' },
      { status: 500 }
    );
  }
}
