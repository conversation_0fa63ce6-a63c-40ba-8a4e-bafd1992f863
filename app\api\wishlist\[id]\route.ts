import { NextRequest, NextResponse } from 'next/server';

// Referensi ke data wishlist (akan diganti dengan database)
let wishlistItems = [
  {
    id: "1",
    name: "Kemeja Casual",
    price: 150000,
    originalPrice: 180000,
    image: "/placeholder.svg",
    inStock: true,
    store: "Fashion Store",
    storeId: "store-1",
    dateAdded: "2025-05-10",
    priceDropped: true,
    productId: "product-1"
  },
  {
    id: "2",
    name: "Sepatu Sneakers",
    price: 350000,
    originalPrice: 350000,
    image: "/assorted-shoes.png",
    inStock: true,
    store: "Footwear Shop",
    storeId: "store-2",
    dateAdded: "2025-05-08",
    priceDropped: false,
    productId: "product-2"
  },
  {
    id: "3",
    name: "<PERSON><PERSON>",
    price: 250000,
    originalPrice: 250000,
    image: "/colorful-backpack-on-wooden-table.png",
    inStock: false,
    store: "Bag Collection",
    storeId: "store-3",
    dateAdded: "2025-05-05",
    priceDropped: false,
    productId: "product-3"
  },
  {
    id: "4",
    name: "<PERSON> Tangan",
    price: 450000,
    originalPrice: 500000,
    image: "/wrist-watch-close-up.png",
    inStock: true,
    store: "Watch World",
    storeId: "store-4",
    dateAdded: "2025-05-01",
    priceDropped: true,
    productId: "product-4"
  },
  {
    id: "5",
    name: "Hoodie Premium",
    price: 300000,
    originalPrice: 300000,
    image: "/placeholder.svg",
    inStock: true,
    store: "Urban Clothing",
    storeId: "store-5",
    dateAdded: "2025-04-28",
    priceDropped: false,
    productId: "product-5"
  },
  {
    id: "6",
    name: "Celana Jeans",
    price: 280000,
    originalPrice: 320000,
    image: "/placeholder.svg",
    inStock: true,
    store: "Denim Store",
    storeId: "store-6",
    dateAdded: "2025-04-25",
    priceDropped: true,
    productId: "product-6"
  },
  {
    id: "7",
    name: "Topi Baseball",
    price: 75000,
    originalPrice: 75000,
    image: "/placeholder.svg",
    inStock: true,
    store: "Accessories Shop",
    storeId: "store-7",
    dateAdded: "2025-04-20",
    priceDropped: false,
    productId: "product-7"
  },
  {
    id: "8",
    name: "Kaos Polos",
    price: 100000,
    originalPrice: 100000,
    image: "/placeholder.svg",
    inStock: true,
    store: "Basic Apparel",
    storeId: "store-8",
    dateAdded: "2025-04-15",
    priceDropped: false,
    productId: "product-8"
  },
];

// GET - Mendapatkan item wishlist berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const itemId = params.id;
  
  // Cari item berdasarkan ID
  const item = wishlistItems.find(item => item.id === itemId);
  
  if (!item) {
    return NextResponse.json({ error: 'Item tidak ditemukan' }, { status: 404 });
  }
  
  return NextResponse.json(item);
}

// PUT - Memperbarui item wishlist berdasarkan ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const itemId = params.id;
    const body = await request.json();
    
    // Cari index item yang akan diupdate
    const itemIndex = wishlistItems.findIndex(item => item.id === itemId);
    
    if (itemIndex === -1) {
      return NextResponse.json({ error: 'Item tidak ditemukan' }, { status: 404 });
    }
    
    // Update item wishlist
    const updatedItem = {
      ...wishlistItems[itemIndex],
      ...body,
      id: itemId, // Pastikan ID tidak berubah
    };
    
    // Simpan perubahan
    wishlistItems[itemIndex] = updatedItem;
    
    return NextResponse.json(updatedItem);
  } catch (error) {
    return NextResponse.json({ error: 'Terjadi kesalahan server' }, { status: 500 });
  }
}

// DELETE - Menghapus item wishlist berdasarkan ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const itemId = params.id;
  
  // Cari index item yang akan dihapus
  const itemIndex = wishlistItems.findIndex(item => item.id === itemId);
  
  if (itemIndex === -1) {
    return NextResponse.json({ error: 'Item tidak ditemukan' }, { status: 404 });
  }
  
  // Hapus item
  wishlistItems.splice(itemIndex, 1);
  
  return NextResponse.json({ message: 'Item berhasil dihapus' });
} 