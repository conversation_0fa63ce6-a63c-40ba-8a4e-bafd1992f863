"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ArrowLeft,
  Bell,
  Mail,
  MessageSquare,
  Smartphone,
  Save,
  Plus,
  Eye,
  Edit,
  Trash,
  AlertTriangle,
  ShoppingCart,
  CreditCard,
  Package,
  UserPlus,
  RefreshCw,
  X
} from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState("channels")
  
  // Dummy data untuk kanal notifikasi
  const notificationChannels = [
    {
      id: "email",
      name: "Email",
      icon: Mail,
      isEnabled: true,
      settings: {
        fromName: "Toko Online",
        fromEmail: "<EMAIL>",
        replyToEmail: "<EMAIL>",
        footerText: "© 2024 Toko Online. Semua hak dilindungi."
      }
    },
    {
      id: "sms",
      name: "SMS",
      icon: Smartphone,
      isEnabled: false,
      settings: {
        sender: "TOKOONLINE",
        provider: "TwilioSMS",
        apiKey: "••••••••••••••••",
        phoneNumber: "+628123456789"
      }
    },
    {
      id: "whatsapp",
      name: "WhatsApp",
      icon: MessageSquare,
      isEnabled: true,
      settings: {
        provider: "Twilio",
        apiKey: "••••••••••••••••",
        phoneNumber: "+628123456789",
        templateApproved: true
      }
    },
    {
      id: "push",
      name: "Notifikasi Push",
      icon: Bell,
      isEnabled: true,
      settings: {
        serviceName: "Firebase",
        apiKey: "••••••••••••••••",
        projectId: "tokoonline-123"
      }
    }
  ]

  // Dummy data untuk template notifikasi
  const notificationTemplates = [
    {
      id: "order-placed",
      name: "Pesanan Baru",
      description: "Dikirim ke pelanggan saat pesanan berhasil dibuat",
      event: "order.created",
      channels: ["email", "sms", "whatsapp"],
      lastUpdated: "2024-05-15T10:30:00",
      icon: ShoppingCart
    },
    {
      id: "order-confirmed",
      name: "Konfirmasi Pesanan",
      description: "Dikirim ke pelanggan saat pesanan dikonfirmasi",
      event: "order.confirmed",
      channels: ["email", "whatsapp"],
      lastUpdated: "2024-05-14T14:20:00",
      icon: ShoppingCart
    },
    {
      id: "payment-received",
      name: "Pembayaran Diterima",
      description: "Dikirim ke pelanggan saat pembayaran berhasil",
      event: "payment.success",
      channels: ["email", "sms", "whatsapp"],
      lastUpdated: "2024-05-10T09:45:00",
      icon: CreditCard
    },
    {
      id: "order-shipped",
      name: "Pesanan Dikirim",
      description: "Dikirim ke pelanggan saat pesanan dikirim",
      event: "order.shipped",
      channels: ["email", "sms", "whatsapp"],
      lastUpdated: "2024-05-12T11:30:00",
      icon: Package
    },
    {
      id: "customer-registration",
      name: "Pendaftaran Pelanggan",
      description: "Dikirim ke pelanggan saat berhasil mendaftar",
      event: "customer.created",
      channels: ["email"],
      lastUpdated: "2024-05-08T08:15:00",
      icon: UserPlus
    }
  ]

  // Dummy data untuk pemberitahuan admin
  const adminNotifications = [
    {
      id: "admin-new-order",
      name: "Pesanan Baru",
      description: "Dikirim ke admin saat pesanan baru dibuat",
      event: "order.created",
      isEnabled: true,
      recipients: ["<EMAIL>", "<EMAIL>"]
    },
    {
      id: "admin-low-stock",
      name: "Stok Rendah",
      description: "Dikirim ke admin saat stok produk di bawah batas minimum",
      event: "product.low_stock",
      isEnabled: true,
      recipients: ["<EMAIL>", "<EMAIL>"]
    },
    {
      id: "admin-failed-payment",
      name: "Pembayaran Gagal",
      description: "Dikirim ke admin saat pembayaran gagal",
      event: "payment.failed",
      isEnabled: false,
      recipients: ["<EMAIL>", "<EMAIL>"]
    }
  ]

  // Format tanggal
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  // Fungsi untuk menampilkan badge channel
  function getChannelBadge(channelId: string) {
    const channel = notificationChannels.find(c => c.id === channelId)
    if (!channel) return null

    let badgeClass = ""
    switch (channelId) {
      case "email":
        badgeClass = "bg-blue-100 text-blue-800 hover:bg-blue-200"
        break
      case "sms":
        badgeClass = "bg-green-100 text-green-800 hover:bg-green-200"
        break
      case "whatsapp":
        badgeClass = "bg-emerald-100 text-emerald-800 hover:bg-emerald-200"
        break
      case "push":
        badgeClass = "bg-purple-100 text-purple-800 hover:bg-purple-200"
        break
      default:
        badgeClass = "bg-gray-100 text-gray-800 hover:bg-gray-200"
    }

    return (
      <Badge className={badgeClass}>
        <channel.icon className="h-3 w-3 mr-1" />
        {channel.name}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/settings">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Pengaturan Notifikasi</h1>
            <p className="text-muted-foreground">
              Kelola pemberitahuan yang dikirim ke pelanggan dan admin
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button>
            <Save className="h-4 w-4 mr-2" />
            Simpan Perubahan
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 md:w-auto">
          <TabsTrigger value="channels">Kanal</TabsTrigger>
          <TabsTrigger value="templates">Template</TabsTrigger>
          <TabsTrigger value="admin">Notifikasi Admin</TabsTrigger>
        </TabsList>

        {/* Channels Tab */}
        <TabsContent value="channels" className="space-y-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Konfigurasi Kanal Notifikasi</AlertTitle>
            <AlertDescription>
              Pastikan Anda telah mengatur kredensial yang benar untuk setiap kanal notifikasi. Notifikasi tidak akan terkirim jika konfigurasi tidak lengkap.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {notificationChannels.map((channel) => (
              <Card key={channel.id}>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-md bg-primary/10 flex items-center justify-center">
                        <channel.icon className="h-4 w-4 text-primary" />
                      </div>
                      <CardTitle>{channel.name}</CardTitle>
                    </div>
                    <Switch defaultChecked={channel.isEnabled} onCheckedChange={() => {}} />
                  </div>
                  <CardDescription>
                    {channel.id === "email" && "Kirim notifikasi melalui email"}
                    {channel.id === "sms" && "Kirim notifikasi melalui SMS"}
                    {channel.id === "whatsapp" && "Kirim notifikasi melalui WhatsApp"}
                    {channel.id === "push" && "Kirim notifikasi push ke browser/aplikasi"}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {channel.id === "email" && (
                    <>
                      <div className="space-y-2">
                        <label htmlFor="from-name" className="text-sm font-medium">
                          Nama Pengirim
                        </label>
                        <Input 
                          id="from-name" 
                          defaultValue={channel.settings.fromName} 
                          onChange={() => {}}
                        />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="from-email" className="text-sm font-medium">
                          Email Pengirim
                        </label>
                        <Input 
                          id="from-email" 
                          defaultValue={channel.settings.fromEmail} 
                          onChange={() => {}}
                        />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="reply-to" className="text-sm font-medium">
                          Email Balas Ke
                        </label>
                        <Input 
                          id="reply-to" 
                          defaultValue={channel.settings.replyToEmail} 
                          onChange={() => {}}
                        />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="footer-text" className="text-sm font-medium">
                          Teks Footer
                        </label>
                        <Input 
                          id="footer-text" 
                          defaultValue={channel.settings.footerText} 
                          onChange={() => {}}
                        />
                      </div>
                    </>
                  )}

                  {channel.id === "sms" && (
                    <>
                      <div className="space-y-2">
                        <label htmlFor="sms-sender" className="text-sm font-medium">
                          ID Pengirim SMS
                        </label>
                        <Input 
                          id="sms-sender" 
                          defaultValue={channel.settings.sender} 
                          onChange={() => {}}
                        />
                        <p className="text-xs text-muted-foreground">
                          ID Pengirim yang akan ditampilkan di perangkat penerima
                        </p>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="sms-provider" className="text-sm font-medium">
                          Penyedia Layanan SMS
                        </label>
                        <Select defaultValue={channel.settings.provider}>
                          <SelectTrigger id="sms-provider">
                            <SelectValue placeholder="Pilih penyedia layanan" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="TwilioSMS">Twilio SMS</SelectItem>
                            <SelectItem value="Nexmo">Nexmo</SelectItem>
                            <SelectItem value="AwsSNS">AWS SNS</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="sms-api-key" className="text-sm font-medium">
                          Kunci API
                        </label>
                        <div className="flex gap-2">
                          <Input 
                            id="sms-api-key" 
                            type="password" 
                            defaultValue={channel.settings.apiKey} 
                            onChange={() => {}}
                          />
                          <Button variant="outline" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="sms-phone" className="text-sm font-medium">
                          Nomor Telepon
                        </label>
                        <Input 
                          id="sms-phone" 
                          defaultValue={channel.settings.phoneNumber} 
                          onChange={() => {}}
                        />
                      </div>
                    </>
                  )}

                  {channel.id === "whatsapp" && (
                    <>
                      <div className="space-y-2">
                        <label htmlFor="wa-provider" className="text-sm font-medium">
                          Penyedia Layanan WhatsApp
                        </label>
                        <Select defaultValue={channel.settings.provider}>
                          <SelectTrigger id="wa-provider">
                            <SelectValue placeholder="Pilih penyedia layanan" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Twilio">Twilio</SelectItem>
                            <SelectItem value="FacebookAPI">Facebook Business API</SelectItem>
                            <SelectItem value="WhatsAppBusiness">WhatsApp Business API</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="wa-api-key" className="text-sm font-medium">
                          Kunci API
                        </label>
                        <div className="flex gap-2">
                          <Input 
                            id="wa-api-key" 
                            type="password" 
                            defaultValue={channel.settings.apiKey} 
                            onChange={() => {}}
                          />
                          <Button variant="outline" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="wa-phone" className="text-sm font-medium">
                          Nomor WhatsApp
                        </label>
                        <Input 
                          id="wa-phone" 
                          defaultValue={channel.settings.phoneNumber} 
                          onChange={() => {}}
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="wa-template"
                          defaultChecked={channel.settings.templateApproved}
                          onCheckedChange={() => {}}
                        />
                        <label htmlFor="wa-template" className="text-sm font-medium">
                          Template Telah Disetujui
                        </label>
                      </div>
                    </>
                  )}

                  {channel.id === "push" && (
                    <>
                      <div className="space-y-2">
                        <label htmlFor="push-service" className="text-sm font-medium">
                          Layanan Notifikasi Push
                        </label>
                        <Select defaultValue={channel.settings.serviceName}>
                          <SelectTrigger id="push-service">
                            <SelectValue placeholder="Pilih layanan" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Firebase">Firebase Cloud Messaging</SelectItem>
                            <SelectItem value="OneSignal">OneSignal</SelectItem>
                            <SelectItem value="PushWoosh">PushWoosh</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="push-api-key" className="text-sm font-medium">
                          Kunci API
                        </label>
                        <div className="flex gap-2">
                          <Input 
                            id="push-api-key" 
                            type="password" 
                            defaultValue={channel.settings.apiKey} 
                            onChange={() => {}}
                          />
                          <Button variant="outline" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="push-project" className="text-sm font-medium">
                          ID Proyek
                        </label>
                        <Input 
                          id="push-project" 
                          defaultValue={channel.settings.projectId} 
                          onChange={() => {}}
                        />
                      </div>
                    </>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" size="sm">
                    <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                    Uji Koneksi
                  </Button>
                  <Button size="sm">
                    <Save className="h-3.5 w-3.5 mr-1.5" />
                    Simpan
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Template Notifikasi</h2>
              <p className="text-sm text-muted-foreground">
                Sesuaikan konten dan format notifikasi yang dikirim ke pelanggan
              </p>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Tambah Template
            </Button>
          </div>

          <div className="space-y-4">
            {notificationTemplates.map((template) => (
              <Card key={template.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row justify-between gap-6">
                    <div className="flex items-start gap-4">
                      <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                        <template.icon className="h-5 w-5 text-primary" />
                      </div>
                      <div className="space-y-1">
                        <h3 className="font-medium text-lg">{template.name}</h3>
                        <p className="text-sm text-muted-foreground">{template.description}</p>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                          <code className="text-xs bg-muted px-2 py-1 rounded">{template.event}</code>
                          <span>•</span>
                          <span>Diperbarui {formatDate(template.lastUpdated)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-start md:items-end gap-4">
                      <div className="flex flex-wrap gap-2">
                        {template.channels.map(channelId => (
                          <div key={channelId}>
                            {getChannelBadge(channelId)}
                          </div>
                        ))}
                      </div>
                      <div className="flex items-center gap-2 mt-auto">
                        <Button variant="outline" size="sm">
                          <Eye className="h-3.5 w-3.5 mr-1.5" />
                          Pratinjau
                        </Button>
                        <Button size="sm">
                          <Edit className="h-3.5 w-3.5 mr-1.5" />
                          Edit Template
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Editor Template</CardTitle>
              <CardDescription>
                Pilih salah satu template untuk mulai mengedit
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih template untuk diedit" />
                </SelectTrigger>
                <SelectContent>
                  {notificationTemplates.map(template => (
                    <SelectItem key={template.id} value={template.id}>{template.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="border rounded-md p-4 flex flex-col items-center justify-center h-64 bg-muted/30">
                <div className="text-muted-foreground text-center">
                  <p>Pilih template untuk mulai mengedit</p>
                  <p className="text-sm">Template yang dipilih akan ditampilkan di sini</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Admin Notifications Tab */}
        <TabsContent value="admin" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Notifikasi Admin</h2>
              <p className="text-sm text-muted-foreground">
                Kelola notifikasi yang dikirim ke tim admin Anda
              </p>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Tambah Notifikasi
            </Button>
          </div>

          <div className="space-y-4">
            {adminNotifications.map((notification) => (
              <Card key={notification.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row justify-between gap-6">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium text-lg">{notification.name}</h3>
                        <Switch defaultChecked={notification.isEnabled} onCheckedChange={() => {}} />
                      </div>
                      <p className="text-sm text-muted-foreground">{notification.description}</p>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                        <code className="text-xs bg-muted px-2 py-1 rounded">{notification.event}</code>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Penerima:</div>
                      <div className="flex flex-wrap gap-2">
                        {notification.recipients.map((email, index) => (
                          <Badge key={index} variant="secondary">
                            <Mail className="h-3 w-3 mr-1" />
                            {email}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-3.5 w-3.5 mr-1.5" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm" className="text-destructive">
                          <Trash className="h-3.5 w-3.5 mr-1.5" />
                          Hapus
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Tambah Penerima</CardTitle>
              <CardDescription>
                Tambahkan alamat email yang akan menerima notifikasi admin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="admin-email" className="text-sm font-medium">
                  Alamat Email
                </label>
                <div className="flex gap-2">
                  <Input 
                    id="admin-email" 
                    placeholder="<EMAIL>" 
                    type="email" 
                    onChange={() => {}}
                  />
                  <Button>Tambah</Button>
                </div>
              </div>

              <div className="pt-4">
                <h4 className="text-sm font-medium mb-2">Penerima Notifikasi Terdaftar:</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary">
                    <Mail className="h-3 w-3 mr-1" />
                    <EMAIL>
                    <Button variant="ghost" size="icon" className="h-4 w-4 ml-1 -mr-1 text-muted-foreground hover:text-foreground">
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                  <Badge variant="secondary">
                    <Mail className="h-3 w-3 mr-1" />
                    <EMAIL>
                    <Button variant="ghost" size="icon" className="h-4 w-4 ml-1 -mr-1 text-muted-foreground hover:text-foreground">
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                  <Badge variant="secondary">
                    <Mail className="h-3 w-3 mr-1" />
                    <EMAIL>
                    <Button variant="ghost" size="icon" className="h-4 w-4 ml-1 -mr-1 text-muted-foreground hover:text-foreground">
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                  <Badge variant="secondary">
                    <Mail className="h-3 w-3 mr-1" />
                    <EMAIL>
                    <Button variant="ghost" size="icon" className="h-4 w-4 ml-1 -mr-1 text-muted-foreground hover:text-foreground">
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button>Simpan Perubahan</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 