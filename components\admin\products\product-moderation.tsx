"use client"

import { useState } from "react"
import Image from "next/image"
import {
  Search,
  Filter,
  ChevronDown,
  Flag,
  ShieldAlert,
  Clock,
  Eye,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Mock data for products requiring moderation
const mockProducts = [
  {
    id: "p1",
    name: "Premium Wireless Headphones",
    image: "/product-image-1.png",
    store: "TechGadgets",
    reason: "Reported for misleading description",
    dateFlagged: "2023-05-15T10:30:00Z",
    status: "pending",
    reporter: "user123",
    description: "High-quality wireless headphones with noise cancellation and 20-hour battery life.",
    price: 129.99,
    reportDetails: "The product description claims 30-hour battery life but actual performance is much lower.",
    policyViolations: ["Misleading product information"],
  },
  {
    id: "p2",
    name: "Designer Handbag",
    image: "/product-image-2.png",
    store: "LuxuryGoods",
    reason: "Suspected counterfeit item",
    dateFlagged: "2023-05-14T14:45:00Z",
    status: "pending",
    reporter: "admin",
    description: "Authentic designer handbag made from premium materials.",
    price: 899.99,
    reportDetails: "This appears to be a counterfeit item based on the images and price point.",
    policyViolations: ["Counterfeit goods", "Intellectual property violation"],
  },
  {
    id: "p3",
    name: "Herbal Supplement Pills",
    image: "/product-image-3.png",
    store: "NaturalHealth",
    reason: "Unverified health claims",
    dateFlagged: "2023-05-13T09:15:00Z",
    status: "pending",
    reporter: "user456",
    description: "Natural supplement that boosts immunity and prevents various diseases.",
    price: 45.99,
    reportDetails: "Product makes unverified medical claims about preventing diseases.",
    policyViolations: ["Unverified health claims", "Misleading product information"],
  },
  {
    id: "p4",
    name: "Smart Home Security Camera",
    image: "/product-image-4.png",
    store: "TechGadgets",
    reason: "Inappropriate content in images",
    dateFlagged: "2023-05-12T16:20:00Z",
    status: "pending",
    reporter: "user789",
    description: "HD security camera with motion detection and night vision.",
    price: 89.99,
    reportDetails: "One of the product images contains inappropriate content.",
    policyViolations: ["Inappropriate content"],
  },
  {
    id: "p5",
    name: "Vintage Collectible Toy",
    image: "/product-image-5.png",
    store: "RetroCollectibles",
    reason: "Prohibited item",
    dateFlagged: "2023-05-11T11:10:00Z",
    status: "pending",
    reporter: "admin",
    description: "Rare vintage toy from the 1950s in excellent condition.",
    price: 349.99,
    reportDetails: "This item contains materials that are prohibited for sale on our platform.",
    policyViolations: ["Prohibited items"],
  },
  {
    id: "p6",
    name: "Weight Loss Supplement",
    image: "/product-image-6.png",
    store: "FitLife",
    reason: "Misleading claims",
    dateFlagged: "2023-05-10T13:25:00Z",
    status: "pending",
    reporter: "user321",
    description: "Lose 10 pounds in one week with this revolutionary supplement.",
    price: 59.99,
    reportDetails: "Product makes unrealistic weight loss claims.",
    policyViolations: ["Misleading product information", "Unverified health claims"],
  },
]

// Violation types for the checklist
const violationTypes = [
  "Counterfeit goods",
  "Intellectual property violation",
  "Prohibited items",
  "Inappropriate content",
  "Misleading product information",
  "Unverified health claims",
  "Price manipulation",
  "Other policy violation",
]

type Product = {
  id: string
  name: string
  image: string
  store: string
  reason: string
  dateFlagged: string
  status: string
  reporter: string
  description: string
  price: number
  reportDetails: string
  policyViolations: string[]
}

export function ProductModeration() {
  const [searchQuery, setSearchQuery] = useState("")
  const [showFilters, setShowFilters] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [activeTab, setActiveTab] = useState("all")
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false)
  const [selectedViolations, setSelectedViolations] = useState<string[]>([])

  const filteredProducts = mockProducts.filter((product) => {
    const matchesSearch =
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.store.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.reason.toLowerCase().includes(searchQuery.toLowerCase())

    if (activeTab === "all") return matchesSearch
    // Add more filters if needed

    return matchesSearch
  })

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product)
    setSelectedViolations(product.policyViolations)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="border-amber-500 text-amber-500">
            Pending Review
          </Badge>
        )
      case "approved":
        return <Badge className="bg-green-500">Approved</Badge>
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const handleViolationToggle = (violation: string) => {
    setSelectedViolations((prev) =>
      prev.includes(violation) ? prev.filter((v) => v !== violation) : [...prev, violation],
    )
  }

  const handleRequestChanges = () => {
    setFeedbackDialogOpen(true)
  }

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <div className="lg:col-span-1">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between px-4 py-3">
            <CardTitle className="text-base">Moderation Queue</CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-1"
              >
                <Filter className="h-4 w-4" />
                Filters
                <ChevronDown className={`h-4 w-4 transition-transform ${showFilters ? "rotate-180" : ""}`} />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="px-2 py-2">
            <div className="mb-4 px-2">
              <Input
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
                prefix={<Search className="h-4 w-4 text-muted-foreground" />}
              />
            </div>
            {showFilters && (
              <div className="mb-4 rounded-md border p-3">
                <h3 className="mb-2 text-sm font-medium">Filter by</h3>
                <div className="space-y-2">
                  <div className="grid gap-2">
                    <Label className="text-xs">Reason</Label>
                    <Select defaultValue="all">
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="All reasons" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All reasons</SelectItem>
                        <SelectItem value="misleading">Misleading information</SelectItem>
                        <SelectItem value="counterfeit">Counterfeit item</SelectItem>
                        <SelectItem value="health">Unverified health claims</SelectItem>
                        <SelectItem value="inappropriate">Inappropriate content</SelectItem>
                        <SelectItem value="prohibited">Prohibited item</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid gap-2">
                    <Label className="text-xs">Date Range</Label>
                    <Select defaultValue="all">
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="All time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All time</SelectItem>
                        <SelectItem value="today">Today</SelectItem>
                        <SelectItem value="week">This week</SelectItem>
                        <SelectItem value="month">This month</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid gap-2">
                    <Label className="text-xs">Reporter</Label>
                    <Select defaultValue="all">
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="All reporters" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All reporters</SelectItem>
                        <SelectItem value="user">Users</SelectItem>
                        <SelectItem value="admin">Admins</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="urgent">Urgent</TabsTrigger>
                <TabsTrigger value="recent">Recent</TabsTrigger>
              </TabsList>
            </Tabs>
            <div className="mt-4 max-h-[600px] overflow-y-auto space-y-1">
              {filteredProducts.map((product) => (
                <div
                  key={product.id}
                  className={`flex items-center gap-3 rounded-md px-3 py-2 hover:bg-muted ${selectedProduct?.id === product.id ? "bg-muted" : ""}`}
                  onClick={() => handleProductClick(product)}
                >
                  <div className="h-10 w-10 overflow-hidden rounded-md border">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      width={40}
                      height={40}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{product.name}</div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{product.store}</span>
                      <span>•</span>
                      <span>{formatDate(product.dateFlagged)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="lg:col-span-2">
        {selectedProduct ? (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
              <CardTitle>Product Review</CardTitle>
              <div className="flex items-center gap-2">{getStatusBadge(selectedProduct.status)}</div>
            </CardHeader>
            <CardContent className="px-6 py-4">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <div className="relative h-64 w-full overflow-hidden rounded-md border">
                    <Image
                      src={selectedProduct.image || "/placeholder.svg"}
                      alt={selectedProduct.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="mt-4 space-y-3">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Product Name</h3>
                      <p className="mt-1 text-base font-medium">{selectedProduct.name}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Store</h3>
                      <p className="mt-1 text-base">{selectedProduct.store}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Price</h3>
                      <p className="mt-1 text-base">${selectedProduct.price.toFixed(2)}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                      <p className="mt-1 text-base">{selectedProduct.description}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-6">
                  <div className="rounded-md border p-4">
                    <div className="flex items-center gap-2">
                      <Flag className="h-5 w-5 text-amber-500" />
                      <h3 className="font-medium">Report Details</h3>
                    </div>
                    <div className="mt-3 space-y-3">
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground">Reason for Moderation</h4>
                        <p className="mt-1">{selectedProduct.reason}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground">Reported By</h4>
                        <p className="mt-1">{selectedProduct.reporter}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground">Date Flagged</h4>
                        <p className="mt-1">{formatDate(selectedProduct.dateFlagged)}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground">Report Details</h4>
                        <p className="mt-1">{selectedProduct.reportDetails}</p>
                      </div>
                    </div>
                  </div>
                  <div className="rounded-md border p-4">
                    <div className="flex items-center gap-2">
                      <ShieldAlert className="h-5 w-5 text-destructive" />
                      <h3 className="font-medium">Policy Violation Check</h3>
                    </div>
                    <div className="mt-3 space-y-2">
                      {violationTypes.map((violation, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            id={`violation-${index}`}
                            checked={selectedViolations.includes(violation)}
                            onChange={() => handleViolationToggle(violation)}
                          />
                          <Label htmlFor={`violation-${index}`} className="text-sm">
                            {violation}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="rounded-md border p-4">
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-blue-500" />
                      <h3 className="font-medium">Moderation Actions</h3>
                    </div>
                    <div className="mt-3 flex flex-col gap-3">
                      <Button variant="outline" className="justify-start">
                        <Eye className="mr-2 h-4 w-4" />
                        View Full Product Details
                      </Button>
                      <div className="grid grid-cols-2 gap-3">
                        <Button
                          variant="outline"
                          className="bg-green-500/10 text-green-600 hover:bg-green-500/20 hover:text-green-700"
                        >
                          <ThumbsUp className="mr-2 h-4 w-4" />
                          Approve
                        </Button>
                        <Button
                          variant="outline"
                          className="bg-destructive/10 text-destructive hover:bg-destructive/20"
                        >
                          <ThumbsDown className="mr-2 h-4 w-4" />
                          Reject
                        </Button>
                      </div>
                      <Button variant="outline" onClick={handleRequestChanges}>
                        <MessageCircle className="mr-2 h-4 w-4" />
                        Request Changes
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="flex h-full items-center justify-center rounded-lg border border-dashed p-8 text-center">
            <div>
              <ShieldAlert className="mx-auto h-10 w-10 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No Product Selected</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Select a product from the moderation queue to review and take action.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Feedback Dialog */}
      <Dialog open={feedbackDialogOpen} onOpenChange={setFeedbackDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Request Changes</DialogTitle>
            <DialogDescription>Provide feedback to the seller about what changes are needed.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="flex items-center gap-4">
              <div className="h-12 w-12 overflow-hidden rounded-md border">
                <Image
                  src={selectedProduct?.image || ""}
                  alt={selectedProduct?.name || ""}
                  width={48}
                  height={48}
                  className="h-full w-full object-cover"
                />
              </div>
              <div>
                <h3 className="font-medium">{selectedProduct?.name}</h3>
                <p className="text-sm text-muted-foreground">{selectedProduct?.store}</p>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="feedback">Feedback Message</Label>
              <Textarea id="feedback" placeholder="Explain what changes are needed..." rows={5} />
            </div>
            <div className="rounded-md bg-muted p-3">
              <h4 className="mb-2 text-sm font-medium">Policy Violations</h4>
              <ul className="ml-5 list-disc text-sm">
                {selectedViolations.map((violation, index) => (
                  <li key={index}>{violation}</li>
                ))}
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setFeedbackDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setFeedbackDialogOpen(false)}>Send Feedback</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
