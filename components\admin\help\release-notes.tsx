"use client"

import { useState } from "react"
import { Search, Calendar, Sparkles, Bug, Zap, Shield, ChevronDown, ChevronUp, AlertCircle, Info } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible"

// Dummy data for releases
const releases = [
  {
    version: "v2.5.0",
    name: "Performance Boost",
    date: "2023-05-01",
    status: "stable",
    description: "Major performance improvements and new features for store management.",
    highlights: [
      "Improved page load times by 40%",
      "New dashboard analytics widgets",
      "Enhanced store customization options",
    ],
    changes: [
      { type: "feature", description: "Added new analytics dashboard with real-time metrics" },
      { type: "feature", description: "Introduced bulk product import/export functionality" },
      { type: "feature", description: "Added support for custom domain verification via DNS records" },
      { type: "improvement", description: "Optimized database queries for faster page loads" },
      { type: "improvement", description: "Enhanced mobile responsiveness across all store templates" },
      { type: "improvement", description: "Updated user permission management interface" },
      { type: "bugfix", description: "Fixed issue with payment processing in certain regions" },
      { type: "bugfix", description: "Resolved image upload errors on Safari browsers" },
      { type: "security", description: "Enhanced password hashing algorithm for better security" },
    ],
  },
  {
    version: "v2.4.2",
    name: "Bug Fix Release",
    date: "2023-04-15",
    status: "stable",
    description: "Critical bug fixes and minor improvements.",
    highlights: ["Fixed critical payment processing issue", "Improved error handling", "Performance optimizations"],
    changes: [
      { type: "bugfix", description: "Fixed critical issue with payment gateway integration" },
      { type: "bugfix", description: "Resolved product search functionality in the admin dashboard" },
      { type: "improvement", description: "Enhanced error logging and reporting" },
      { type: "improvement", description: "Optimized image loading for faster page rendering" },
      { type: "security", description: "Patched potential XSS vulnerability in the comment system" },
    ],
  },
  {
    version: "v2.4.0",
    name: "Multi-currency Support",
    date: "2023-03-20",
    status: "stable",
    description: "Added support for multiple currencies and improved internationalization.",
    highlights: ["Support for 15+ currencies", "Automatic currency conversion", "Enhanced localization features"],
    changes: [
      { type: "feature", description: "Added support for 15+ currencies with automatic conversion" },
      { type: "feature", description: "Introduced localized pricing display options" },
      { type: "feature", description: "Added currency selector for storefront" },
      { type: "improvement", description: "Enhanced date and time formatting for different locales" },
      { type: "improvement", description: "Updated translation system with more language options" },
      { type: "bugfix", description: "Fixed issues with decimal handling in certain currencies" },
      { type: "bugfix", description: "Resolved tax calculation errors for international orders" },
    ],
  },
  {
    version: "v2.3.5",
    name: "Security Update",
    date: "2023-02-28",
    status: "stable",
    description: "Important security patches and authentication improvements.",
    highlights: ["Enhanced authentication system", "Security vulnerability patches", "Improved admin audit logs"],
    changes: [
      { type: "security", description: "Patched critical authentication vulnerability" },
      { type: "security", description: "Enhanced password policy enforcement" },
      { type: "security", description: "Implemented additional CSRF protections" },
      { type: "feature", description: "Added detailed admin action audit logs" },
      { type: "improvement", description: "Enhanced two-factor authentication flow" },
      { type: "improvement", description: "Updated session management for better security" },
    ],
  },
  {
    version: "v2.3.0",
    name: "API Expansion",
    date: "2023-01-15",
    status: "stable",
    description: "Expanded API capabilities and developer tools.",
    highlights: [
      "New API endpoints for orders and customers",
      "Improved developer documentation",
      "Enhanced webhook system",
    ],
    changes: [
      { type: "feature", description: "Added new API endpoints for order management" },
      { type: "feature", description: "Introduced customer data API with filtering options" },
      { type: "feature", description: "Added webhook system for real-time event notifications" },
      { type: "improvement", description: "Enhanced API rate limiting with better feedback" },
      { type: "improvement", description: "Updated API documentation with interactive examples" },
      { type: "improvement", description: "Added API versioning support for better compatibility" },
      { type: "bugfix", description: "Fixed inconsistent response formats in several endpoints" },
    ],
  },
]

// Upcoming releases
const upcomingReleases = [
  {
    version: "v2.6.0",
    name: "AI-Powered Recommendations",
    plannedDate: "2023-06-15",
    status: "beta",
    description: "Introducing AI-powered product recommendations and search improvements.",
    highlights: [
      "AI-based product recommendations",
      "Enhanced search with natural language processing",
      "Personalized shopping experiences",
    ],
    plannedFeatures: [
      "AI-powered product recommendation engine",
      "Natural language search processing",
      "Customer behavior analysis dashboard",
      "Personalized email content generation",
      "Smart inventory management suggestions",
    ],
  },
  {
    version: "v2.7.0",
    name: "Advanced Marketing Suite",
    plannedDate: "2023-08-01",
    status: "alpha",
    description: "Comprehensive marketing tools for campaigns, promotions, and customer engagement.",
    highlights: [
      "Integrated email marketing campaigns",
      "Advanced discount and promotion rules",
      "Customer segmentation tools",
    ],
    plannedFeatures: [
      "Email marketing campaign builder",
      "Advanced promotion rule engine",
      "Customer segmentation and targeting",
      "Social media integration",
      "Marketing performance analytics",
    ],
  },
]

export default function ReleaseNotes() {
  const [searchQuery, setSearchQuery] = useState("")
  const [expandedVersions, setExpandedVersions] = useState<string[]>([releases[0].version])
  const [activeTab, setActiveTab] = useState("released")

  // Filter releases based on search query
  const filteredReleases = releases.filter((release) => {
    const matchesSearch =
      release.version.toLowerCase().includes(searchQuery.toLowerCase()) ||
      release.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      release.description.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesSearch
  })

  const filteredUpcoming = upcomingReleases.filter((release) => {
    const matchesSearch =
      release.version.toLowerCase().includes(searchQuery.toLowerCase()) ||
      release.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      release.description.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesSearch
  })

  // Toggle expanded state for a version
  const toggleExpanded = (version: string) => {
    setExpandedVersions((prev) => (prev.includes(version) ? prev.filter((v) => v !== version) : [...prev, version]))
  }

  // Check if a version is expanded
  const isExpanded = (version: string) => {
    return expandedVersions.includes(version)
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    })
  }

  // Get icon for change type
  const getChangeTypeIcon = (type: string) => {
    switch (type) {
      case "feature":
        return <Sparkles className="h-4 w-4 text-blue-500" />
      case "improvement":
        return <Zap className="h-4 w-4 text-green-500" />
      case "bugfix":
        return <Bug className="h-4 w-4 text-orange-500" />
      case "security":
        return <Shield className="h-4 w-4 text-red-500" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  // Get badge for release status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "stable":
        return <Badge className="bg-green-500 hover:bg-green-600">Stable</Badge>
      case "beta":
        return <Badge className="bg-blue-500 hover:bg-blue-600">Beta</Badge>
      case "alpha":
        return <Badge className="bg-orange-500 hover:bg-orange-600">Alpha</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col space-y-2 mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Release Notes</h1>
        <p className="text-muted-foreground">
          View platform update history and release notes for the SellZio platform.
        </p>
      </div>

      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search releases..."
          className="pl-10 h-12"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <Tabs defaultValue="released" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="w-full max-w-md mx-auto grid grid-cols-2">
          <TabsTrigger value="released">Released Versions</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming Releases</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="mt-6">
        {activeTab === "released" ? (
          <div className="space-y-6">
            {filteredReleases.length > 0 ? (
              filteredReleases.map((release) => (
                <Card key={release.version}>
                  <CardHeader className="pb-3">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                      <div>
                        <CardTitle className="flex items-center">
                          {release.version} - {release.name}
                          <span className="ml-3">{getStatusBadge(release.status)}</span>
                        </CardTitle>
                        <CardDescription className="flex items-center mt-1">
                          <Calendar className="mr-1 h-4 w-4" />
                          Released on {formatDate(release.date)}
                        </CardDescription>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => toggleExpanded(release.version)}>
                        {isExpanded(release.version) ? (
                          <>
                            <ChevronUp className="mr-2 h-4 w-4" /> Hide Details
                          </>
                        ) : (
                          <>
                            <ChevronDown className="mr-2 h-4 w-4" /> Show Details
                          </>
                        )}
                      </Button>
                    </div>
                    <p className="mt-2">{release.description}</p>
                    <div className="flex flex-wrap gap-2 mt-3">
                      {release.highlights.map((highlight, index) => (
                        <Badge key={index} variant="outline" className="bg-primary/10">
                          {highlight}
                        </Badge>
                      ))}
                    </div>
                  </CardHeader>
                  <Collapsible open={isExpanded(release.version)}>
                    <CollapsibleContent>
                      <Separator />
                      <CardContent className="pt-4">
                        <h3 className="font-semibold mb-3">Changes in this release:</h3>
                        <div className="space-y-3">
                          {release.changes
                            .filter((change) => change.type === "feature")
                            .map((change, index) => (
                              <div key={`feature-${index}`} className="flex items-start">
                                {getChangeTypeIcon("feature")}
                                <span className="ml-2 font-medium">New Feature:</span>
                                <span className="ml-2">{change.description}</span>
                              </div>
                            ))}

                          {release.changes
                            .filter((change) => change.type === "improvement")
                            .map((change, index) => (
                              <div key={`improvement-${index}`} className="flex items-start">
                                {getChangeTypeIcon("improvement")}
                                <span className="ml-2 font-medium">Improvement:</span>
                                <span className="ml-2">{change.description}</span>
                              </div>
                            ))}

                          {release.changes
                            .filter((change) => change.type === "bugfix")
                            .map((change, index) => (
                              <div key={`bugfix-${index}`} className="flex items-start">
                                {getChangeTypeIcon("bugfix")}
                                <span className="ml-2 font-medium">Bug Fix:</span>
                                <span className="ml-2">{change.description}</span>
                              </div>
                            ))}

                          {release.changes
                            .filter((change) => change.type === "security")
                            .map((change, index) => (
                              <div key={`security-${index}`} className="flex items-start">
                                {getChangeTypeIcon("security")}
                                <span className="ml-2 font-medium">Security:</span>
                                <span className="ml-2">{change.description}</span>
                              </div>
                            ))}
                        </div>
                      </CardContent>
                    </CollapsibleContent>
                  </Collapsible>
                </Card>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No releases found matching your search criteria
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {filteredUpcoming.length > 0 ? (
              filteredUpcoming.map((release, index) => (
                <Card key={release.version}>
                  <CardHeader className="pb-3">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                      <div>
                        <CardTitle className="flex items-center">
                          {release.version} - {release.name}
                          <span className="ml-3">{getStatusBadge(release.status)}</span>
                        </CardTitle>
                        <CardDescription className="flex items-center mt-1">
                          <Calendar className="mr-1 h-4 w-4" />
                          Planned for {formatDate(release.plannedDate)}
                        </CardDescription>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => toggleExpanded(release.version)}>
                        {isExpanded(release.version) ? (
                          <>
                            <ChevronUp className="mr-2 h-4 w-4" /> Hide Details
                          </>
                        ) : (
                          <>
                            <ChevronDown className="mr-2 h-4 w-4" /> Show Details
                          </>
                        )}
                      </Button>
                    </div>
                    <p className="mt-2">{release.description}</p>
                    <div className="flex flex-wrap gap-2 mt-3">
                      {release.highlights.map((highlight, index) => (
                        <Badge key={index} variant="outline" className="bg-primary/10">
                          {highlight}
                        </Badge>
                      ))}
                    </div>
                  </CardHeader>
                  <Collapsible open={isExpanded(release.version)}>
                    <CollapsibleContent>
                      <Separator />
                      <CardContent className="pt-4">
                        <h3 className="font-semibold mb-3">Planned features:</h3>
                        <div className="space-y-3">
                          {release.plannedFeatures.map((feature, index) => (
                            <div key={index} className="flex items-start">
                              <Sparkles className="h-4 w-4 text-blue-500" />
                              <span className="ml-2">{feature}</span>
                            </div>
                          ))}
                        </div>
                        <div className="mt-4 p-4 bg-muted rounded-lg">
                          <div className="flex items-center">
                            <AlertCircle className="h-5 w-5 text-orange-500 mr-2" />
                            <span className="font-medium">Note:</span>
                          </div>
                          <p className="mt-1 text-sm text-muted-foreground">
                            Features and release dates are subject to change. This roadmap is provided for informational
                            purposes only.
                          </p>
                        </div>
                      </CardContent>
                    </CollapsibleContent>
                  </Collapsible>
                </Card>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No upcoming releases found matching your search criteria
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
