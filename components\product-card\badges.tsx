import { Star, Truck, DollarSign } from "lucide-react"

export const MallBadge = () => (
  <div className="inline-flex items-center rounded-md overflow-hidden text-xs font-bold h-4 mb-1 mr-1">
    <div className="bg-[#ee4d2d] text-white px-1 h-full flex items-center">Mall</div>
    <div className="bg-[#fff7f7] text-[#ee4d2d] px-1 h-full flex items-center">ORI</div>
  </div>
)

export const StarBadge = ({ hasPlus = true }: { hasPlus?: boolean }) => (
  <div className="inline-flex items-center rounded-md overflow-hidden text-xs font-bold h-4 mb-1 mr-1">
    <div className="bg-[#ee4d2d] text-white px-1 h-full flex items-center">Star</div>
    {hasPlus && <div className="bg-[#fff0e5] text-[#ee4d2d] px-1 h-full flex items-center">Plus</div>}
  </div>
)

export const TermurahDiTokoBadge = () => {
  return (
    <div className="relative inline-flex items-center bg-[#ee4d2d] text-white text-[10px] font-semibold px-2 py-0.5 pl-6 rounded-sm mb-1.5 shadow-sm">
      <span className="absolute -left-1 top-1/2 -translate-y-1/2 bg-white text-[#ee4d2d] w-5 h-5 rounded-full flex items-center justify-center border border-[#ee4d2d] shadow-sm">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-3 h-3">
          <path d="M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z" />
        </svg>
      </span>
      Termurah di Toko
    </div>
  )
}

export const CodBadge = () => (
  <div className="absolute top-0 right-0 bg-[#ee4d2d] text-white text-[9px] font-bold px-1 py-0.5 h-4 w-10 flex items-center justify-center">
    <DollarSign className="h-2.5 w-2.5 mr-0.5" />
    COD
  </div>
)

export const LiveBadge = () => (
  <div className="absolute top-2 right-2 bg-[#ee4d2d] text-white text-[9px] font-bold px-2 py-0.5 rounded-sm z-10 flex items-center">
    <div className="w-1.5 h-1.5 bg-white rounded-full mr-1 animate-pulse"></div>
    LIVE
  </div>
)

export const ShopeeStarIcon = () => (
  <span className="inline-flex items-center bg-[#ee4d2d] text-white text-[9px] font-bold px-1 py-0.5 rounded-sm mr-1">
    Star
  </span>
)

export const RatingStars = ({ rating }: { rating: number }) => (
  <div className="flex items-center text-[#ee4d2d]">
    <Star className="w-3 h-3 fill-[#ee4d2d] text-[#ee4d2d] mr-0.5" />
    <span>{rating.toFixed(1)}</span>
  </div>
)

export const ShippingBadge = ({ text = "Pengiriman Instan" }: { text?: string }) => (
  <div className="flex items-center text-xs text-gray-600 mb-1.5">
    <Truck className="w-3 h-3 text-[#00bfa5] mr-1" />
    <span>{text}</span>
  </div>
)
