"use client"

import { useState, useEffect, useRef, type ReactNode } from "react"
import { Loader2 } from "lucide-react"

interface PullToRefreshProps {
  onRefresh: () => Promise<void>
  children: ReactNode
}

export function PullToRefresh({ onRefresh, children }: PullToRefreshProps) {
  const [isPulling, setIsPulling] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)
  const startY = useRef(0)
  const currentY = useRef(0)
  const refreshThreshold = 80 // pixels to pull down to trigger refresh

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const handleTouchStart = (e: TouchEvent) => {
      // Only enable pull to refresh when at the top of the page
      if (window.scrollY === 0) {
        startY.current = e.touches[0].clientY
        setIsPulling(true)
      }
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!isPulling) return

      currentY.current = e.touches[0].clientY
      const distance = currentY.current - startY.current

      // Only allow pulling down, not up
      if (distance > 0) {
        // Apply resistance to make it harder to pull
        const pullWithResistance = Math.min(distance * 0.4, refreshThreshold * 1.5)
        setPullDistance(pullWithResistance)

        // Prevent default scrolling when pulling
        e.preventDefault()
      }
    }

    const handleTouchEnd = async () => {
      if (!isPulling) return

      if (pullDistance >= refreshThreshold) {
        // Trigger refresh
        setIsRefreshing(true)
        setPullDistance(refreshThreshold) // Keep showing the refresh indicator

        try {
          await onRefresh()
        } catch (error) {
          console.error("Refresh failed:", error)
        }

        // Reset after refresh
        setTimeout(() => {
          setIsRefreshing(false)
          setPullDistance(0)
        }, 500)
      } else {
        // Not pulled enough, reset
        setPullDistance(0)
      }

      setIsPulling(false)
    }

    container.addEventListener("touchstart", handleTouchStart)
    container.addEventListener("touchmove", handleTouchMove, { passive: false })
    container.addEventListener("touchend", handleTouchEnd)

    return () => {
      container.removeEventListener("touchstart", handleTouchStart)
      container.removeEventListener("touchmove", handleTouchMove)
      container.removeEventListener("touchend", handleTouchEnd)
    }
  }, [isPulling, pullDistance, onRefresh])

  return (
    <div ref={containerRef} className="relative min-h-full">
      {/* Pull indicator */}
      <div
        className="absolute left-0 right-0 flex justify-center transition-transform duration-200 ease-out"
        style={{
          transform: `translateY(${pullDistance - 50}px)`,
          opacity: pullDistance / refreshThreshold,
        }}
      >
        <div className="flex items-center justify-center w-10 h-10 bg-white rounded-full shadow-md">
          {isRefreshing ? (
            <Loader2 className="w-6 h-6 text-primary animate-spin" />
          ) : (
            <svg
              className="w-6 h-6 text-primary"
              style={{
                transform: `rotate(${Math.min((pullDistance / refreshThreshold) * 180, 180)}deg)`,
                transition: isPulling ? "none" : "transform 0.2s ease-out",
              }}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="18 15 12 9 6 15"></polyline>
            </svg>
          )}
        </div>
      </div>

      {/* Content with pull down effect */}
      <div
        style={{
          transform: `translateY(${pullDistance}px)`,
          transition: isPulling ? "none" : "transform 0.2s ease-out",
        }}
      >
        {children}
      </div>
    </div>
  )
}

// Ensure we have both default and named exports
export default PullToRefresh
