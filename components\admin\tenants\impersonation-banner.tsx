"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { AlertCircle, LogOut } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useNotifications } from "@/components/providers/notifications-provider"

interface ImpersonationData {
  isImpersonating: boolean
  originalRole: string
  impersonatedTenant: {
    id: string
    name: string
    domain: string
  }
  timestamp: string
  expiresAt: string | null
}

export function ImpersonationBanner() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const [impersonationData, setImpersonationData] = useState<ImpersonationData | null>(null)
  const [timeLeft, setTimeLeft] = useState<string | null>(null)

  useEffect(() => {
    // Cek apakah ada data impersonasi di localStorage atau sessionStorage
    const localData = localStorage.getItem("sellzio_impersonation")
    const sessionData = sessionStorage.getItem("sellzio_impersonation")

    const data = localData || sessionData

    if (data) {
      try {
        const parsedData = JSON.parse(data) as ImpersonationData
        setImpersonationData(parsedData)

        // Jika ada expiry time, hitung waktu tersisa
        if (parsedData.expiresAt) {
          const updateTimeLeft = () => {
            const now = new Date()
            const expiresAt = new Date(parsedData.expiresAt!)
            const diffMs = expiresAt.getTime() - now.getTime()

            if (diffMs <= 0) {
              // Sesi sudah kedaluwarsa, hapus data impersonasi
              endImpersonation()
              return
            }

            const diffMins = Math.floor(diffMs / 60000)
            const diffSecs = Math.floor((diffMs % 60000) / 1000)

            setTimeLeft(`${diffMins}:${diffSecs.toString().padStart(2, "0")}`)
          }

          updateTimeLeft()
          const interval = setInterval(updateTimeLeft, 1000)

          return () => clearInterval(interval)
        }
      } catch (error) {
        console.error("Error parsing impersonation data:", error)
      }
    }
  }, [])

  const endImpersonation = () => {
    // Hapus data impersonasi dari storage
    localStorage.removeItem("sellzio_impersonation")
    sessionStorage.removeItem("sellzio_impersonation")

    // Reset state
    setImpersonationData(null)
    setTimeLeft(null)

    // Tampilkan notifikasi
    addNotification({
      message: "Sesi impersonasi telah berakhir",
      type: "success",
    })

    // Redirect ke dashboard admin
    router.push("/admin/dashboard")
  }

  if (!impersonationData) {
    return null
  }

  return (
    <div className="bg-yellow-500 text-yellow-900 py-2 px-4">
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5" />
          <span>
            Anda sedang login sebagai tenant <strong>{impersonationData.impersonatedTenant.name}</strong>
            {timeLeft && (
              <span className="ml-2">
                (Sesi berakhir dalam <strong>{timeLeft}</strong>)
              </span>
            )}
          </span>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="bg-yellow-600 text-yellow-50 border-yellow-700 hover:bg-yellow-700 hover:text-yellow-50"
          onClick={endImpersonation}
        >
          <LogOut className="h-4 w-4 mr-2" />
          Keluar dari Impersonasi
        </Button>
      </div>
    </div>
  )
}
