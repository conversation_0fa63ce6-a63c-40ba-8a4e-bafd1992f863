import type { Metadata } from "next"
import { Suspense } from "react"

import { VerificationQueue } from "@/components/admin/stores/verification-queue"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export const metadata: Metadata = {
  title: "Store Verification Queue | Admin Dashboard",
  description: "Verify and approve store applications",
}

export default function StoreVerificationPage() {
  return (
    <div className="flex flex-col gap-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Store Verification</h1>
        <p className="text-muted-foreground">Review and verify store applications before they go live.</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Verification Queue</CardTitle>
          <CardDescription>Stores waiting for verification and approval.</CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
            <VerificationQueue />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
