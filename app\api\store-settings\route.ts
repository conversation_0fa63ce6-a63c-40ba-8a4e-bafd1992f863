import { NextRequest, NextResponse } from 'next/server';
import { storeSettingService } from '@/lib/services/store-settings';

// GET - Mendapatkan semua store settings atau difilter berdasarkan query
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get('category');
    const is_public = searchParams.get('is_public');
    const search = searchParams.get('search');
    const grouped = searchParams.get('grouped');
    
    const filters = {
      category: category || undefined,
      is_public: is_public !== null ? is_public === 'true' : undefined,
      search: search || undefined,
    };
    
    let result;
    if (grouped === 'true') {
      result = await storeSettingService.getSettingsByCategory(filters);
    } else {
      result = await storeSettingService.getSettings(filters);
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// POST - Membuat store setting baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newSetting = await storeSettingService.createSetting(body);
    
    return NextResponse.json(newSetting, { status: 201 });
  } catch (error) {
    console.error('Error creating setting:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to create setting';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// PUT - Update multiple settings at once
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { settings } = body;
    
    if (!settings || typeof settings !== 'object') {
      return NextResponse.json(
        { error: 'Settings object is required' },
        { status: 400 }
      );
    }
    
    const updatedSettings = await storeSettingService.updateMultipleSettings(settings);
    
    return NextResponse.json({
      message: 'Settings updated successfully',
      data: updatedSettings
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    );
  }
}
