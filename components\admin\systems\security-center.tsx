"use client"

import { useState } from "react"
import {
  AlertTriangle,
  CheckCircle,
  Lock,
  Shield,
  ShieldAlert,
  ShieldCheck,
  ShieldX,
  Smartphone,
  UserCheck,
  Users,
  XCircle,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { DateRangePicker } from "@/components/analytics/date-range-picker"

// Mock data for security events
const securityEvents = [
  {
    id: "event-1",
    type: "login_attempt_failed",
    timestamp: "2025-05-13T05:23:12",
    user: "<EMAIL>",
    ip: "************",
    location: "Jakarta, Indonesia",
    details: "Failed login attempt (incorrect password)",
    severity: "medium",
  },
  {
    id: "event-2",
    type: "permission_change",
    timestamp: "2025-05-13T04:15:33",
    user: "system",
    ip: "Internal",
    location: "N/A",
    details: "Admin role permissions updated",
    severity: "high",
  },
  {
    id: "event-3",
    type: "api_key_created",
    timestamp: "2025-05-12T18:42:01",
    user: "<EMAIL>",
    ip: "*************",
    location: "Singapore",
    details: "New API key created with read/write access",
    severity: "medium",
  },
  {
    id: "event-4",
    type: "suspicious_activity",
    timestamp: "2025-05-12T15:30:45",
    user: "<EMAIL>",
    ip: "***********",
    location: "Unknown",
    details: "Multiple rapid requests from unusual location",
    severity: "high",
  },
  {
    id: "event-5",
    type: "account_locked",
    timestamp: "2025-05-12T14:22:18",
    user: "<EMAIL>",
    ip: "***********12",
    location: "Bandung, Indonesia",
    details: "Account locked after 5 failed login attempts",
    severity: "medium",
  },
  {
    id: "event-6",
    type: "data_export",
    timestamp: "2025-05-11T09:15:02",
    user: "<EMAIL>",
    ip: "*************",
    location: "Jakarta, Indonesia",
    details: "Full customer data export initiated",
    severity: "high",
  },
  {
    id: "event-7",
    type: "login_success",
    timestamp: "2025-05-11T08:03:55",
    user: "<EMAIL>",
    ip: "************",
    location: "Jakarta, Indonesia",
    details: "Successful login from recognized device",
    severity: "low",
  },
]

// Mock data for blocked IPs
const blockedIPs = [
  {
    id: "ip-1",
    ip: "***********78",
    reason: "Brute force attack",
    timestamp: "2025-05-10T14:22:18",
    attempts: 27,
    duration: "Permanent",
    status: "active",
  },
  {
    id: "ip-2",
    ip: "**************",
    reason: "Suspicious activity",
    timestamp: "2025-05-09T18:45:32",
    attempts: 15,
    duration: "24 hours",
    status: "active",
  },
  {
    id: "ip-3",
    ip: "***********",
    reason: "API abuse",
    timestamp: "2025-05-08T11:12:45",
    attempts: 342,
    duration: "7 days",
    status: "active",
  },
  {
    id: "ip-4",
    ip: "************",
    reason: "Brute force attack",
    timestamp: "2025-05-05T09:34:21",
    attempts: 18,
    duration: "Permanent",
    status: "active",
  },
  {
    id: "ip-5",
    ip: "*************",
    reason: "Suspicious activity",
    timestamp: "2025-05-02T22:15:08",
    attempts: 8,
    duration: "24 hours",
    status: "expired",
  },
]

// Mock data for compliance status
const complianceItems = [
  {
    id: "comp-1",
    standard: "PCI DSS",
    status: "compliant",
    lastCheck: "2025-05-10",
    nextCheck: "2025-08-10",
    issues: 0,
    criticalIssues: 0,
  },
  {
    id: "comp-2",
    standard: "GDPR",
    status: "compliant",
    lastCheck: "2025-05-05",
    nextCheck: "2025-08-05",
    issues: 0,
    criticalIssues: 0,
  },
  {
    id: "comp-3",
    standard: "ISO 27001",
    status: "partial",
    lastCheck: "2025-05-01",
    nextCheck: "2025-08-01",
    issues: 3,
    criticalIssues: 0,
  },
  {
    id: "comp-4",
    standard: "SOC 2",
    status: "compliant",
    lastCheck: "2025-04-28",
    nextCheck: "2025-07-28",
    issues: 0,
    criticalIssues: 0,
  },
  {
    id: "comp-5",
    standard: "HIPAA",
    status: "non-compliant",
    lastCheck: "2025-04-25",
    nextCheck: "2025-05-25",
    issues: 5,
    criticalIssues: 2,
  },
]

export function SecurityCenter() {
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    to: new Date(),
  })

  const getSecurityScore = () => {
    // Calculate a security score based on various factors
    return 87
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "low":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            Low
          </Badge>
        )
      case "medium":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            Medium
          </Badge>
        )
      case "high":
        return <Badge variant="destructive">High</Badge>
      case "critical":
        return (
          <Badge variant="destructive" className="bg-red-700">
            Critical
          </Badge>
        )
      default:
        return <Badge variant="outline">{severity}</Badge>
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "compliant":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700">
            Compliant
          </Badge>
        )
      case "partial":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            Partial
          </Badge>
        )
      case "non-compliant":
        return <Badge variant="destructive">Non-Compliant</Badge>
      case "active":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            Active
          </Badge>
        )
      case "expired":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700">
            Expired
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Security Center</h2>
          <p className="text-muted-foreground">Monitor and manage platform security</p>
        </div>
        <div className="flex items-center gap-2">
          <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
          <Button size="sm" variant="outline">
            <Shield className="mr-2 h-4 w-4" />
            Security Scan
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="tools">Tools</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Security Score</CardTitle>
                <ShieldCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  <span className={getScoreColor(getSecurityScore())}>{getSecurityScore()}/100</span>
                </div>
                <p className="text-xs text-muted-foreground">+5 from last scan</p>
                <Progress value={getSecurityScore()} className="mt-2" />
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Threats</CardTitle>
                <ShieldAlert className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2</div>
                <p className="text-xs text-muted-foreground">-3 from last week</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Blocked Attempts</CardTitle>
                <ShieldX className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">142</div>
                <p className="text-xs text-muted-foreground">+28 from last week</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">2FA Adoption</CardTitle>
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">78%</div>
                <p className="text-xs text-muted-foreground">+12% from last month</p>
                <Progress value={78} className="mt-2" />
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Security Recommendations</CardTitle>
                <CardDescription>Actions to improve your security posture</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-4 rounded-md border p-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-yellow-100">
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Enable 2FA for All Admin Users</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        3 admin users don't have two-factor authentication enabled
                      </p>
                      <Button size="sm" className="mt-2">
                        Enable Now
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-start gap-4 rounded-md border p-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-yellow-100">
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Update Password Policy</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Current password policy doesn't meet recommended standards
                      </p>
                      <Button size="sm" className="mt-2">
                        Review Policy
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-start gap-4 rounded-md border p-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">API Rate Limiting</h4>
                      <p className="text-sm text-muted-foreground mt-1">API rate limiting is properly configured</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4 rounded-md border p-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100">
                      <XCircle className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Security Headers</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Missing important security headers on public endpoints
                      </p>
                      <Button size="sm" className="mt-2">
                        Fix Now
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Recent Security Events</CardTitle>
                <CardDescription>Latest security-related activities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {securityEvents.slice(0, 5).map((event) => (
                    <div key={event.id} className="flex items-start gap-4">
                      <div
                        className={`flex h-10 w-10 items-center justify-center rounded-full ${
                          event.severity === "high"
                            ? "bg-red-100"
                            : event.severity === "medium"
                              ? "bg-yellow-100"
                              : "bg-blue-100"
                        }`}
                      >
                        <Shield
                          className={`h-5 w-5 ${
                            event.severity === "high"
                              ? "text-red-600"
                              : event.severity === "medium"
                                ? "text-yellow-600"
                                : "text-blue-600"
                          }`}
                        />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="text-sm font-medium">
                            {event.type
                              .split("_")
                              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                              .join(" ")}
                          </h4>
                          {getSeverityBadge(event.severity)}
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">{event.details}</p>
                        <div className="flex items-center gap-4 mt-1">
                          <p className="text-xs text-muted-foreground">{event.user}</p>
                          <p className="text-xs text-muted-foreground">{event.ip}</p>
                          <p className="text-xs text-muted-foreground">{new Date(event.timestamp).toLocaleString()}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  View All Events
                </Button>
              </CardFooter>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Security Status</CardTitle>
              <CardDescription>Current security status of your platform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-medium mb-4">Authentication & Access</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="text-sm">HTTPS Enforced</span>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        Secure
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="text-sm">Session Management</span>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        Secure
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-yellow-600" />
                        <span className="text-sm">Two-Factor Authentication</span>
                      </div>
                      <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
                        Partial
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="text-sm">Password Policy</span>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        Secure
                      </Badge>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-4">Data Protection</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="text-sm">Data Encryption at Rest</span>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        Secure
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="text-sm">Data Encryption in Transit</span>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        Secure
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <XCircle className="h-5 w-5 text-red-600" />
                        <span className="text-sm">Data Backup Encryption</span>
                      </div>
                      <Badge variant="destructive">Vulnerable</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="text-sm">PII Protection</span>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        Secure
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Events</CardTitle>
              <CardDescription>All security-related events and activities</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[180px]">Timestamp</TableHead>
                    <TableHead className="w-[150px]">Event Type</TableHead>
                    <TableHead className="w-[150px]">User</TableHead>
                    <TableHead className="w-[120px]">IP Address</TableHead>
                    <TableHead className="w-[150px]">Location</TableHead>
                    <TableHead className="w-[100px]">Severity</TableHead>
                    <TableHead>Details</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {securityEvents.map((event) => (
                    <TableRow key={event.id}>
                      <TableCell>{new Date(event.timestamp).toLocaleString()}</TableCell>
                      <TableCell>
                        {event.type
                          .split("_")
                          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                          .join(" ")}
                      </TableCell>
                      <TableCell>{event.user}</TableCell>
                      <TableCell>{event.ip}</TableCell>
                      <TableCell>{event.location}</TableCell>
                      <TableCell>{getSeverityBadge(event.severity)}</TableCell>
                      <TableCell>{event.details}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <div className="flex items-center justify-between w-full">
                <Button variant="outline">Export Events</Button>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <polyline points="15 18 9 12 15 6" />
                    </svg>
                  </Button>
                  <Button variant="outline" size="icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <polyline points="9 18 15 12 9 6" />
                    </svg>
                  </Button>
                </div>
              </div>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Blocked IP Addresses</CardTitle>
              <CardDescription>IP addresses that have been blocked due to suspicious activity</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[150px]">IP Address</TableHead>
                    <TableHead className="w-[180px]">Blocked On</TableHead>
                    <TableHead className="w-[150px]">Reason</TableHead>
                    <TableHead className="w-[100px]">Attempts</TableHead>
                    <TableHead className="w-[120px]">Duration</TableHead>
                    <TableHead className="w-[100px]">Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {blockedIPs.map((ip) => (
                    <TableRow key={ip.id}>
                      <TableCell>{ip.ip}</TableCell>
                      <TableCell>{new Date(ip.timestamp).toLocaleString()}</TableCell>
                      <TableCell>{ip.reason}</TableCell>
                      <TableCell>{ip.attempts}</TableCell>
                      <TableCell>{ip.duration}</TableCell>
                      <TableCell>{getStatusBadge(ip.status)}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          Unblock
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button className="ml-auto">Block IP Manually</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Password Policy</CardTitle>
              <CardDescription>Configure password requirements for all users</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="min-length">Minimum Password Length</Label>
                <Select defaultValue="12">
                  <SelectTrigger id="min-length">
                    <SelectValue placeholder="Select minimum length" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="8">8 characters</SelectItem>
                    <SelectItem value="10">10 characters</SelectItem>
                    <SelectItem value="12">12 characters</SelectItem>
                    <SelectItem value="14">14 characters</SelectItem>
                    <SelectItem value="16">16 characters</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="complexity">Password Complexity</Label>
                <Select defaultValue="high">
                  <SelectTrigger id="complexity">
                    <SelectValue placeholder="Select complexity level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low (Letters and numbers)</SelectItem>
                    <SelectItem value="medium">Medium (Letters, numbers, and symbols)</SelectItem>
                    <SelectItem value="high">High (Upper/lowercase, numbers, and symbols)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="expiration">Password Expiration</Label>
                <Select defaultValue="90days">
                  <SelectTrigger id="expiration">
                    <SelectValue placeholder="Select expiration period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30days">30 days</SelectItem>
                    <SelectItem value="60days">60 days</SelectItem>
                    <SelectItem value="90days">90 days</SelectItem>
                    <SelectItem value="180days">180 days</SelectItem>
                    <SelectItem value="never">Never</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="history">Password History</Label>
                <Select defaultValue="5">
                  <SelectTrigger id="history">
                    <SelectValue placeholder="Select history count" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">Remember 3 previous passwords</SelectItem>
                    <SelectItem value="5">Remember 5 previous passwords</SelectItem>
                    <SelectItem value="10">Remember 10 previous passwords</SelectItem>
                    <SelectItem value="0">Don't remember previous passwords</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="lockout" defaultChecked />
                <Label htmlFor="lockout">Account lockout after failed attempts</Label>
              </div>
              <div className="space-y-2">
                <Label htmlFor="lockout-attempts">Failed Attempts Before Lockout</Label>
                <Select defaultValue="5">
                  <SelectTrigger id="lockout-attempts">
                    <SelectValue placeholder="Select attempts count" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">3 attempts</SelectItem>
                    <SelectItem value="5">5 attempts</SelectItem>
                    <SelectItem value="10">10 attempts</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Reset to Defaults</Button>
              <Button>Save Policy</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Two-Factor Authentication</CardTitle>
              <CardDescription>Configure two-factor authentication settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch id="require-2fa-admins" defaultChecked />
                <Label htmlFor="require-2fa-admins">Require 2FA for all admin users</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="require-2fa-all" />
                <Label htmlFor="require-2fa-all">Require 2FA for all users</Label>
              </div>
              <div className="space-y-2">
                <Label htmlFor="2fa-methods">Allowed 2FA Methods</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="2fa-app" defaultChecked />
                    <Label htmlFor="2fa-app">Authenticator App</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="2fa-sms" defaultChecked />
                    <Label htmlFor="2fa-sms">SMS</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="2fa-email" defaultChecked />
                    <Label htmlFor="2fa-email">Email</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="2fa-security-key" />
                    <Label htmlFor="2fa-security-key">Security Key (WebAuthn)</Label>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="2fa-remember">Remember Device Duration</Label>
                <Select defaultValue="30days">
                  <SelectTrigger id="2fa-remember">
                    <SelectValue placeholder="Select duration" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="never">Never remember</SelectItem>
                    <SelectItem value="7days">7 days</SelectItem>
                    <SelectItem value="30days">30 days</SelectItem>
                    <SelectItem value="90days">90 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">Save 2FA Settings</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Session Management</CardTitle>
              <CardDescription>Configure user session settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="session-timeout">Session Timeout</Label>
                <Select defaultValue="30min">
                  <SelectTrigger id="session-timeout">
                    <SelectValue placeholder="Select timeout period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15min">15 minutes</SelectItem>
                    <SelectItem value="30min">30 minutes</SelectItem>
                    <SelectItem value="1hour">1 hour</SelectItem>
                    <SelectItem value="4hours">4 hours</SelectItem>
                    <SelectItem value="8hours">8 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="session-ip-lock" defaultChecked />
                <Label htmlFor="session-ip-lock">Lock session to IP address</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="session-concurrent" />
                <Label htmlFor="session-concurrent">Allow concurrent sessions</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="session-remember-me" defaultChecked />
                <Label htmlFor="session-remember-me">Allow "Remember Me" functionality</Label>
              </div>
              <div className="space-y-2">
                <Label htmlFor="remember-duration">Remember Me Duration</Label>
                <Select defaultValue="30days">
                  <SelectTrigger id="remember-duration">
                    <SelectValue placeholder="Select duration" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7days">7 days</SelectItem>
                    <SelectItem value="30days">30 days</SelectItem>
                    <SelectItem value="90days">90 days</SelectItem>
                    <SelectItem value="1year">1 year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">Save Session Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Status</CardTitle>
              <CardDescription>Current compliance status with various standards</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[150px]">Standard</TableHead>
                    <TableHead className="w-[120px]">Status</TableHead>
                    <TableHead className="w-[150px]">Last Check</TableHead>
                    <TableHead className="w-[150px]">Next Check</TableHead>
                    <TableHead className="w-[100px]">Issues</TableHead>
                    <TableHead className="w-[120px]">Critical Issues</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {complianceItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.standard}</TableCell>
                      <TableCell>{getStatusBadge(item.status)}</TableCell>
                      <TableCell>{item.lastCheck}</TableCell>
                      <TableCell>{item.nextCheck}</TableCell>
                      <TableCell>{item.issues}</TableCell>
                      <TableCell>{item.criticalIssues}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          View Report
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button className="ml-auto">Run Compliance Check</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Data Protection</CardTitle>
              <CardDescription>Data protection and privacy compliance settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="data-retention">Data Retention Policy</Label>
                <Select defaultValue="1year">
                  <SelectTrigger id="data-retention">
                    <SelectValue placeholder="Select retention period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="90days">90 days</SelectItem>
                    <SelectItem value="180days">180 days</SelectItem>
                    <SelectItem value="1year">1 year</SelectItem>
                    <SelectItem value="2years">2 years</SelectItem>
                    <SelectItem value="5years">5 years</SelectItem>
                    <SelectItem value="indefinite">Indefinite</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="data-anonymization" defaultChecked />
                <Label htmlFor="data-anonymization">Anonymize personal data after retention period</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="data-export" defaultChecked />
                <Label htmlFor="data-export">Allow users to export their data</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="data-deletion" defaultChecked />
                <Label htmlFor="data-deletion">Allow users to request data deletion</Label>
              </div>
              <div className="space-y-2">
                <Label htmlFor="consent-management">Consent Management</Label>
                <Select defaultValue="explicit">
                  <SelectTrigger id="consent-management">
                    <SelectValue placeholder="Select consent type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="explicit">Explicit Consent (Opt-in)</SelectItem>
                    <SelectItem value="implicit">Implicit Consent (Opt-out)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="cookie-policy">Cookie Policy</Label>
                <Select defaultValue="essential">
                  <SelectTrigger id="cookie-policy">
                    <SelectValue placeholder="Select cookie policy" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="essential">Essential Cookies Only</SelectItem>
                    <SelectItem value="functional">Essential + Functional</SelectItem>
                    <SelectItem value="analytics">Essential + Functional + Analytics</SelectItem>
                    <SelectItem value="marketing">All Cookies (Including Marketing)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Reset to Defaults</Button>
              <Button>Save Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="tools" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Tools</CardTitle>
              <CardDescription>Tools to help secure your platform</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                      <Shield className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-lg font-medium">Security Scanner</h4>
                      <p className="text-sm text-muted-foreground">Scan your platform for vulnerabilities</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button className="w-full">Run Security Scan</Button>
                  </div>
                </div>
                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                      <Lock className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-lg font-medium">API Security Audit</h4>
                      <p className="text-sm text-muted-foreground">Audit your API endpoints for security issues</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button className="w-full">Run API Audit</Button>
                  </div>
                </div>
                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-lg font-medium">User Access Review</h4>
                      <p className="text-sm text-muted-foreground">Review user permissions and access</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button className="w-full">Start Access Review</Button>
                  </div>
                </div>
                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                      <UserCheck className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-lg font-medium">2FA Enforcement</h4>
                      <p className="text-sm text-muted-foreground">Enforce 2FA for all users</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button className="w-full">Enforce 2FA</Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>IP Blocking</CardTitle>
              <CardDescription>Block IP addresses manually</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="ip-address">IP Address</Label>
                <Input id="ip-address" placeholder="e.g., ***********" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="block-reason">Reason</Label>
                <Select defaultValue="suspicious">
                  <SelectTrigger id="block-reason">
                    <SelectValue placeholder="Select reason" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="suspicious">Suspicious Activity</SelectItem>
                    <SelectItem value="brute-force">Brute Force Attack</SelectItem>
                    <SelectItem value="spam">Spam</SelectItem>
                    <SelectItem value="abuse">API Abuse</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="block-duration">Duration</Label>
                <Select defaultValue="24hours">
                  <SelectTrigger id="block-duration">
                    <SelectValue placeholder="Select duration" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1hour">1 hour</SelectItem>
                    <SelectItem value="24hours">24 hours</SelectItem>
                    <SelectItem value="7days">7 days</SelectItem>
                    <SelectItem value="30days">30 days</SelectItem>
                    <SelectItem value="permanent">Permanent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="block-notes">Notes</Label>
                <Input id="block-notes" placeholder="Additional notes about this block" />
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">Block IP Address</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Security Reports</CardTitle>
              <CardDescription>Generate security reports</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="report-type">Report Type</Label>
                <Select defaultValue="security-overview">
                  <SelectTrigger id="report-type">
                    <SelectValue placeholder="Select report type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="security-overview">Security Overview</SelectItem>
                    <SelectItem value="login-attempts">Login Attempts</SelectItem>
                    <SelectItem value="user-activity">User Activity</SelectItem>
                    <SelectItem value="api-usage">API Usage</SelectItem>
                    <SelectItem value="compliance">Compliance Status</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="report-format">Format</Label>
                <Select defaultValue="pdf">
                  <SelectTrigger id="report-format">
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">PDF</SelectItem>
                    <SelectItem value="csv">CSV</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>Date Range</Label>
                <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">Generate Report</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
