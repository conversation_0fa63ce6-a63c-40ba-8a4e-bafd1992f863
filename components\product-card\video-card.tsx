"use client"

import { useState } from "react"
import { Play } from "lucide-react"
import { LiveBadge, RatingStars } from "./badges"

interface VideoCardProps {
  thumbnailSrc: string
  videoSrc?: string
  isLive?: boolean
  productImage: string
  productName: string
  rating: number
  sold: number
  price: string
  originalPrice?: string
}

export const VideoCard = ({
  thumbnailSrc,
  videoSrc,
  isLive = false,
  productImage,
  productName,
  rating,
  sold,
  price,
  originalPrice,
}: VideoCardProps) => {
  const [isPlaying, setIsPlaying] = useState(false)

  const handlePlay = () => {
    if (videoSrc) {
      setIsPlaying(true)
    }
  }

  return (
    <div className="bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 cursor-pointer">
      <div className="relative w-full pt-[177.78%] overflow-hidden bg-black">
        {isPlaying && videoSrc ? (
          <iframe
            src={`${videoSrc}?autoplay=1&mute=1`}
            className="absolute top-0 left-0 w-full h-full"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        ) : (
          <>
            <img
              src={thumbnailSrc || "/placeholder.svg"}
              alt="Video thumbnail"
              className="absolute top-0 left-0 w-full h-full object-cover"
            />
            <button
              onClick={handlePlay}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black bg-opacity-50 rounded-full p-3 hover:bg-opacity-70 transition-all"
            >
              <Play className="w-6 h-6 text-white fill-white" />
            </button>
            {isLive && <LiveBadge />}
          </>
        )}
      </div>

      <div className="p-2 pb-3">
        <div className="flex items-start space-x-2">
          <div className="w-12 h-12 flex-shrink-0">
            <img src={productImage || "/placeholder.svg"} alt={productName} className="w-full h-full object-contain" />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-xs text-gray-800 line-clamp-2 mb-1">{productName}</h3>
            <div className="flex items-center text-xs text-gray-500 mb-1">
              <RatingStars rating={rating} />
              <div className="mx-1 w-[1px] h-2 bg-gray-300"></div>
              <span>Terjual {sold}</span>
            </div>
            <div className="flex items-center">
              <div className="text-sm font-bold text-[#ee4d2d]">{price}</div>
              {originalPrice && (
                <div className="ml-1 text-xs text-gray-400 line-through overflow-hidden text-ellipsis max-w-[40%]">
                  {originalPrice}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
