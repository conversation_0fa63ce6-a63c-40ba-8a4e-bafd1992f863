"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useState, type ReactNode } from "react"
import { X } from "lucide-react"

type NotificationType = "success" | "info" | "warning" | "error"

type Notification = {
  id: string
  title: string
  message: string
  type: NotificationType
  duration?: number
}

type NotificationsContextType = {
  notifications: Notification[]
  addNotification: (notification: Omit<Notification, "id">) => void
  removeNotification: (id: string) => void
}

const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined)

export const useNotifications = () => {
  const context = useContext(NotificationsContext)
  if (!context) {
    throw new Error("useNotifications must be used within a NotificationsProvider")
  }
  return context
}

export function NotificationsProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([])

  const addNotification = (notification: Omit<Notification, "id">) => {
    const id = Math.random().toString(36).substring(2, 9)
    const newNotification = { ...notification, id }

    setNotifications((prev) => [...prev, newNotification])

    // Auto-remove notification after duration (default: 5000ms)
    if (notification.duration !== 0) {
      setTimeout(() => {
        removeNotification(id)
      }, notification.duration || 5000)
    }
  }

  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id))
  }

  return (
    <NotificationsContext.Provider value={{ notifications, addNotification, removeNotification }}>
      {children}

      {/* Notifications container */}
      <div className="fixed top-4 right-4 z-50 flex flex-col gap-2 w-80">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`p-4 rounded-md shadow-md flex items-start gap-3 animate-in slide-in-from-right-5 duration-300 ${
              notification.type === "success"
                ? "bg-green-50 text-green-800 border-l-4 border-green-500"
                : notification.type === "error"
                  ? "bg-red-50 text-red-800 border-l-4 border-red-500"
                  : notification.type === "warning"
                    ? "bg-yellow-50 text-yellow-800 border-l-4 border-yellow-500"
                    : "bg-blue-50 text-blue-800 border-l-4 border-blue-500"
            }`}
          >
            <div className="flex-1">
              {notification.title && <h4 className="font-medium mb-1">{notification.title}</h4>}
              <p className="text-sm">{notification.message}</p>
            </div>
            <button onClick={() => removeNotification(notification.id)} className="text-gray-500 hover:text-gray-700">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>
        ))}
      </div>
    </NotificationsContext.Provider>
  )
}
