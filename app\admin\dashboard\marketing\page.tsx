import type { <PERSON>ada<PERSON> } from "next"
import { MarketingOverview } from "@/components/admin/marketing/marketing-overview"
import { PageHeader } from "@/components/admin/ui/page-header"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Marketing Overview | Sellzio Admin",
  description: "Kelola dan analisis kampanye marketing platform Anda",
}

export default function MarketingPage() {
  return (
    <>
      <PageHeader
        title="Marketing Overview"
        description="Kelola dan analisis kampanye marketing platform Anda"
        actions={
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/admin/dashboard/marketing/campaigns/create">Buat Kampanye</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/admin/dashboard/analytics">Lihat Analytics</Link>
            </Button>
          </div>
        }
      />
      <MarketingOverview />
    </>
  )
}
