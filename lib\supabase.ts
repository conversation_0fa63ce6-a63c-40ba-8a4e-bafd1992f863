import { createClient } from '@supabase/supabase-js'

// Inisialisasi variabel untuk menyimpan instance Supabase
let supabaseClient: ReturnType<typeof createClient> | null = null
let supabaseServerClient: ReturnType<typeof createClient> | null = null

// Fungsi untuk mendapatkan instance Supabase
export function getClient() {
  // Jika sudah ada instance, kembalikan instance yang ada
  if (supabaseClient) {
    return supabaseClient
  }

  // Pastikan environment variables terdefinisi
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables')
  }

  // Buat instance baru
  supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      storage: typeof window !== 'undefined' ? window.localStorage : undefined,
      storageKey: 'sb-auth-token',
      flowType: 'pkce',
    },
  })

  return supabaseClient
}

// Fungsi untuk mendapatkan instance Supabase dengan service role (untuk server-side)
export function getServerClient() {
  // Jika sudah ada instance, kembalikan instance yang ada
  if (supabaseServerClient) {
    return supabaseServerClient
  }

  // Pastikan environment variables terdefinisi
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase environment variables for server client')
  }

  // Buat instance baru dengan service role key
  supabaseServerClient = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })

  return supabaseServerClient
}

// Ekspor instance default untuk kompatibilitas
export const supabase = getClient()
