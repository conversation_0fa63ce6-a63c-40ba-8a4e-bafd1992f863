"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ApplicationProgressTracker } from "./application-progress-tracker"
import { StoreInformation } from "./store-information"
import { ProductInformation } from "./product-information"
import { BusinessDetails } from "./business-details"
import { StorePolicies } from "./store-policies"
import { ReviewSubmit } from "./review-submit"
import { ArrowLeft, ArrowRight, Save } from "lucide-react"

interface StoreApplicationWizardProps {
  applicationId: string
}

export function StoreApplicationWizard({ applicationId }: StoreApplicationWizardProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // Store Information
    storeName: "",
    storeDescription: "",
    storeCategory: "",
    storeLogo: null,
    businessType: "",
    storeEmail: "",
    storePhone: "",
    sellerExperience: "",

    // Product Information
    productCategories: [],
    estimatedProducts: "",
    productSource: "",
    sampleDescriptions: "",
    sampleImages: [],
    pricingRange: { min: 0, max: 0 },
    shippingCapabilities: [],

    // Business Details
    businessRegistration: "",
    taxId: "",
    businessAddress: "",
    ownerIdCard: null,
    bankAccount: {
      bankName: "",
      accountNumber: "",
      accountHolder: "",
    },
    businessDocuments: [],

    // Store Policies
    returnPolicy: "",
    shippingPolicy: "",
    privacyPolicy: "",
    termsOfService: "",
    customerServiceStandards: "",
    processingTimes: "",
    shippingOptions: [],

    // Review & Submit
    termsAgreed: false,
    sellerAgreement: false,
    commissionAcknowledgment: false,
  })

  const totalSteps = 5

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
      window.scrollTo(0, 0)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
      window.scrollTo(0, 0)
    }
  }

  const handleStepClick = (step: number) => {
    if (step <= currentStep) {
      setCurrentStep(step)
      window.scrollTo(0, 0)
    }
  }

  const handleSaveProgress = () => {
    // Simpan progress ke localStorage atau API
    localStorage.setItem(`storeApplication_${applicationId}`, JSON.stringify(formData))
    alert("Progress berhasil disimpan!")
  }

  const updateFormData = (stepData: Partial<typeof formData>) => {
    setFormData({ ...formData, ...stepData })
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <StoreInformation formData={formData} updateFormData={updateFormData} />
      case 2:
        return <ProductInformation formData={formData} updateFormData={updateFormData} />
      case 3:
        return <BusinessDetails formData={formData} updateFormData={updateFormData} />
      case 4:
        return <StorePolicies formData={formData} updateFormData={updateFormData} />
      case 5:
        return <ReviewSubmit formData={formData} updateFormData={updateFormData} />
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <ApplicationProgressTracker
        currentStep={currentStep}
        totalSteps={totalSteps}
        applicationId={applicationId}
        onStepClick={handleStepClick}
      />

      <Card>
        <div className="p-6">{renderStep()}</div>

        <div className="flex items-center justify-between p-6 bg-muted/30 border-t border-border">
          <div>
            {currentStep > 1 && (
              <Button variant="outline" onClick={handlePrevious}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Sebelumnya
              </Button>
            )}
          </div>
          <Button variant="outline" onClick={handleSaveProgress}>
            <Save className="mr-2 h-4 w-4" /> Simpan Progress
          </Button>
          <div>
            {currentStep < totalSteps ? (
              <Button onClick={handleNext}>
                Selanjutnya <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button onClick={() => console.log("Submit application", formData)}>Kirim Aplikasi</Button>
            )}
          </div>
        </div>
      </Card>
    </div>
  )
}
