import { Suspense } from "react"
import { StoreApplicationIntro } from "@/components/buyer/store-application/store-application-intro"
import { StoreApplicationWizard } from "@/components/buyer/store-application/store-application-wizard"
import { ApplicationStatus } from "@/components/buyer/store-application/application-status"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function StoreApplicationPage() {
  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <Card>
        <CardHeader className="pb-0">
          <CardTitle className="text-3xl font-bold">Pendaftaran Toko</CardTitle>
          <p className="text-muted-foreground mt-1"><PERSON><PERSON> perjalanan Anda sebagai penjual di platform Sellzio</p>
        </CardHeader>
        <CardContent className="space-y-8 pt-6">
          <Suspense fallback={<Skeleton className="h-[200px] w-full rounded-lg" />}>
            <StoreApplicationIntro />
          </Suspense>

          <Suspense fallback={<Skeleton className="h-[600px] w-full rounded-lg" />}>
            <StoreApplicationWrapper />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}

// Wrapper untuk menentukan apakah menampilkan wizard atau status
function StoreApplicationWrapper() {
  // Di implementasi nyata, kita akan menggunakan API untuk memeriksa status aplikasi
  const applicationStatus = {
    started: true,
    submitted: false,
    status: "draft", // 'draft', 'submitted', 'under_review', 'approved', 'rejected', 'needs_info'
    applicationId: "SA-" + Math.floor(100000 + Math.random() * 900000),
  }

  if (!applicationStatus.started) {
    return null // Tidak tampil jika belum memulai aplikasi
  }

  if (applicationStatus.submitted) {
    return <ApplicationStatus applicationId={applicationStatus.applicationId} status={applicationStatus.status} />
  }

  return <StoreApplicationWizard applicationId={applicationStatus.applicationId} />
}
