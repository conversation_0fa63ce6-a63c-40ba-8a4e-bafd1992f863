"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Filter,
  Settings,
  TruckIcon,
  MapPin,
  Clock,
  Package,
  CheckCircle,
  ArrowUpDown,
  BarChart3,
  GlobeIcon,
  Building2,
  CircleDollarSign,
  Scale,
  Home,
  ListChecks,
  ClipboardEdit,
  Plus,
  PencilIcon,
  Eye,
  MoreHorizontal,
  Trash2,
  Check
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk metode pengiriman
const shippingMethods = [
  {
    id: "shipping-001",
    name: "JNE Regular",
    provider: "JNE",
    status: "active",
    type: "regular",
    cost: {
      base: 9000,
      perKg: 5000,
      insurance: 0.002 // 0.2% dari nilai barang
    },
    estimatedDelivery: {
      min: 2,
      max: 3
    },
    coverage: ["Jakarta", "Bandung", "Surabaya", "Medan", "Makassar"],
    weightLimit: 30, // in kg
    dimensions: {
      maxLength: 100, // in cm
      maxWidth: 100, // in cm
      maxHeight: 100 // in cm
    },
    ratingAvg: 4.2,
    orderCount: 1245,
    lastUpdated: "2024-01-10T09:30:00"
  },
  {
    id: "shipping-002",
    name: "JNE YES (Yakin Esok Sampai)",
    provider: "JNE",
    status: "active",
    type: "express",
    cost: {
      base: 18000,
      perKg: 9000,
      insurance: 0.002
    },
    estimatedDelivery: {
      min: 1,
      max: 1
    },
    coverage: ["Jakarta", "Bandung", "Surabaya", "Medan", "Makassar", "Semarang"],
    weightLimit: 30,
    dimensions: {
      maxLength: 100,
      maxWidth: 100,
      maxHeight: 100
    },
    ratingAvg: 4.5,
    orderCount: 890,
    lastUpdated: "2024-01-10T09:30:00"
  },
  {
    id: "shipping-003",
    name: "SiCepat REG",
    provider: "SiCepat",
    status: "active",
    type: "regular",
    cost: {
      base: 8000,
      perKg: 4500,
      insurance: 0.002
    },
    estimatedDelivery: {
      min: 1,
      max: 2
    },
    coverage: ["Jakarta", "Bandung", "Surabaya", "Semarang", "Yogyakarta"],
    weightLimit: 50,
    dimensions: {
      maxLength: 150,
      maxWidth: 150,
      maxHeight: 150
    },
    ratingAvg: 4.3,
    orderCount: 1103,
    lastUpdated: "2024-01-08T14:20:00"
  },
  {
    id: "shipping-004",
    name: "AnterAja Same Day",
    provider: "AnterAja",
    status: "active",
    type: "same_day",
    cost: {
      base: 25000,
      perKg: 10000,
      insurance: 0.003
    },
    estimatedDelivery: {
      min: 0,
      max: 0
    },
    coverage: ["Jakarta", "Tangerang", "Bekasi", "Depok", "Bogor"],
    weightLimit: 20,
    dimensions: {
      maxLength: 80,
      maxWidth: 80,
      maxHeight: 80
    },
    ratingAvg: 4.7,
    orderCount: 568,
    lastUpdated: "2024-01-09T11:45:00"
  },
  {
    id: "shipping-005",
    name: "J&T Express",
    provider: "J&T",
    status: "active",
    type: "regular",
    cost: {
      base: 9500,
      perKg: 5500,
      insurance: 0.002
    },
    estimatedDelivery: {
      min: 2,
      max: 3
    },
    coverage: ["Jakarta", "Bandung", "Surabaya", "Medan", "Makassar", "Palembang"],
    weightLimit: 40,
    dimensions: {
      maxLength: 120,
      maxWidth: 120,
      maxHeight: 120
    },
    ratingAvg: 4.1,
    orderCount: 975,
    lastUpdated: "2024-01-07T16:30:00"
  },
  {
    id: "shipping-006",
    name: "Ninja Xpress",
    provider: "Ninja",
    status: "inactive",
    type: "regular",
    cost: {
      base: 10000,
      perKg: 6000,
      insurance: 0.002
    },
    estimatedDelivery: {
      min: 2,
      max: 4
    },
    coverage: ["Jakarta", "Bandung", "Surabaya", "Semarang"],
    weightLimit: 30,
    dimensions: {
      maxLength: 100,
      maxWidth: 100,
      maxHeight: 100
    },
    ratingAvg: 4.0,
    orderCount: 430,
    lastUpdated: "2023-12-20T10:15:00"
  }
]

// Data dummy untuk zona pengiriman
const shippingZones = [
  {
    id: "zone-001",
    name: "Zona 1 - Jawa",
    regions: ["DKI Jakarta", "Jawa Barat", "Jawa Tengah", "Jawa Timur", "Banten", "DI Yogyakarta"],
    methods: ["shipping-001", "shipping-002", "shipping-003", "shipping-004", "shipping-005"],
    status: "active",
    lastUpdated: "2024-01-10T09:30:00"
  },
  {
    id: "zone-002",
    name: "Zona 2 - Sumatera",
    regions: ["Sumatera Utara", "Sumatera Barat", "Sumatera Selatan", "Riau", "Jambi", "Bengkulu", "Lampung", "Kep. Bangka Belitung", "Kep. Riau"],
    methods: ["shipping-001", "shipping-002", "shipping-005"],
    status: "active",
    lastUpdated: "2024-01-09T14:25:00"
  },
  {
    id: "zone-003",
    name: "Zona 3 - Kalimantan",
    regions: ["Kalimantan Barat", "Kalimantan Tengah", "Kalimantan Selatan", "Kalimantan Timur", "Kalimantan Utara"],
    methods: ["shipping-001", "shipping-005"],
    status: "active",
    lastUpdated: "2024-01-08T11:10:00"
  },
  {
    id: "zone-004",
    name: "Zona 4 - Sulawesi",
    regions: ["Sulawesi Utara", "Sulawesi Tengah", "Sulawesi Selatan", "Sulawesi Tenggara", "Gorontalo", "Sulawesi Barat"],
    methods: ["shipping-001", "shipping-005"],
    status: "active",
    lastUpdated: "2024-01-07T16:45:00"
  },
  {
    id: "zone-005",
    name: "Zona 5 - Bali & Nusa Tenggara",
    regions: ["Bali", "Nusa Tenggara Barat", "Nusa Tenggara Timur"],
    methods: ["shipping-001", "shipping-002", "shipping-005"],
    status: "active",
    lastUpdated: "2024-01-06T13:20:00"
  },
  {
    id: "zone-006",
    name: "Zona 6 - Maluku & Papua",
    regions: ["Maluku", "Maluku Utara", "Papua", "Papua Barat", "Papua Selatan", "Papua Tengah", "Papua Pegunungan"],
    methods: ["shipping-001"],
    status: "limited",
    lastUpdated: "2024-01-05T10:35:00"
  }
]

// Data untuk statistik
const shippingStats = {
  totalShipments: 3256,
  pendingShipments: 145,
  inTransit: 534,
  delivered: 2577,
  avgDeliveryTime: 2.3, // days
  onTimeDelivery: 94.5, // percentage
  lateDelivery: 5.5, // percentage
  returnedPackages: 23,
  topProvider: "JNE",
  mostUsedMethod: "JNE Regular",
  revenueFromShipping: 58750000
}

// Fungsi untuk generate badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "active":
      return <Badge variant="outline" className="bg-green-100 text-green-800"><Check className="h-3 w-3 mr-1" />Active</Badge>
    case "inactive": 
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Inactive</Badge>
    case "limited":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Limited</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric'
  })
}

export default function OrderShippingPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("methods")
  const [statusFilter, setStatusFilter] = useState("all")

  // Filter metode pengiriman
  const filteredMethods = shippingMethods.filter(method => {
    const matchesSearch = method.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          method.provider.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || method.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Filter zona pengiriman
  const filteredZones = shippingZones.filter(zone => {
    const matchesSearch = zone.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          zone.regions.some(region => region.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesStatus = statusFilter === "all" || zone.status === statusFilter
    return matchesSearch && matchesStatus
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/orders">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Shipping Management</h1>
            <p className="text-muted-foreground">
              Kelola metode pengiriman, biaya, dan konfigurasi zona pengiriman
            </p>
          </div>
        </div>
        <Button className="gap-2">
          <Settings className="h-4 w-4" />
          Shipping Settings
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Shipments</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{shippingStats.totalShipments.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Pending: {shippingStats.pendingShipments}, In Transit: {shippingStats.inTransit}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On-Time Delivery</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{shippingStats.onTimeDelivery}%</div>
            <p className="text-xs text-muted-foreground">
              Avg. delivery time: {shippingStats.avgDeliveryTime} days
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Provider</CardTitle>
            <TruckIcon className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{shippingStats.topProvider}</div>
            <p className="text-xs text-muted-foreground">
              Most used: {shippingStats.mostUsedMethod}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shipping Revenue</CardTitle>
            <CircleDollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(shippingStats.revenueFromShipping)}</div>
            <p className="text-xs text-muted-foreground">
              From {shippingStats.totalShipments} shipments
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex border-b space-x-4">
        <button
          className={`pb-2 px-4 ${activeTab === "methods" ? "border-b-2 border-primary font-medium" : "text-muted-foreground"}`}
          onClick={() => setActiveTab("methods")}
        >
          <div className="flex items-center gap-2">
            <TruckIcon className="h-4 w-4" />
            Shipping Methods
          </div>
        </button>
        <button
          className={`pb-2 px-4 ${activeTab === "zones" ? "border-b-2 border-primary font-medium" : "text-muted-foreground"}`}
          onClick={() => setActiveTab("zones")}
        >
          <div className="flex items-center gap-2">
            <GlobeIcon className="h-4 w-4" />
            Shipping Zones
          </div>
        </button>
        <button
          className={`pb-2 px-4 ${activeTab === "analytics" ? "border-b-2 border-primary font-medium" : "text-muted-foreground"}`}
          onClick={() => setActiveTab("analytics")}
        >
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Shipping Analytics
          </div>
        </button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={activeTab === "methods" ? "Cari metode atau provider..." : "Cari zona atau region..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="limited">Limited</option>
            </select>
            {activeTab === "methods" && (
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Shipping Method
              </Button>
            )}
            {activeTab === "zones" && (
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Shipping Zone
              </Button>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Shipping Methods Tab */}
      {activeTab === "methods" && (
        <div className="space-y-4">
          {filteredMethods.map((method) => (
            <Card key={method.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex flex-col gap-4">
                  {/* Method Header */}
                  <div className="flex flex-col md:flex-row justify-between gap-4">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-lg font-semibold">{method.name}</h3>
                        {getStatusBadge(method.status)}
                      </div>
                      <p className="text-sm text-muted-foreground">Provider: {method.provider}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm">Last Updated: {formatDate(method.lastUpdated)}</p>
                      <div className="flex items-center gap-1 justify-end mt-1">
                        <div className="flex items-center">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <svg
                              key={i}
                              className={`h-4 w-4 ${
                                i < Math.floor(method.ratingAvg)
                                  ? "text-yellow-400 fill-current"
                                  : i < method.ratingAvg
                                  ? "text-yellow-400 fill-current opacity-50"
                                  : "text-gray-300 fill-current"
                              }`}
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          ))}
                        </div>
                        <span className="text-sm">{method.ratingAvg} ({method.orderCount} orders)</span>
                      </div>
                    </div>
                  </div>

                  {/* Method Details */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <h4 className="text-sm font-medium">Tipe</h4>
                      <p className="text-sm capitalize">{method.type.replace(/_/g, ' ')}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Biaya Dasar</h4>
                      <p className="text-sm">{formatCurrency(method.cost.base)} + {formatCurrency(method.cost.perKg)}/kg</p>
                      <p className="text-xs text-muted-foreground">Asuransi: {method.cost.insurance * 100}%</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Estimasi Pengiriman</h4>
                      <p className="text-sm">
                        {method.estimatedDelivery.min === 0 ? "Same day" : 
                          `${method.estimatedDelivery.min}${method.estimatedDelivery.max > method.estimatedDelivery.min ? '-' + method.estimatedDelivery.max : ''} hari`}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Batas Berat/Dimensi</h4>
                      <p className="text-sm">{method.weightLimit} kg</p>
                      <p className="text-xs text-muted-foreground">
                        {method.dimensions.maxLength} x {method.dimensions.maxWidth} x {method.dimensions.maxHeight} cm
                      </p>
                    </div>
                  </div>

                  {/* Coverage */}
                  <div>
                    <h4 className="text-sm font-medium mb-1">Coverage</h4>
                    <div className="flex flex-wrap gap-1">
                      {method.coverage.map((area, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {area}
                        </Badge>
                      ))}
                      {method.coverage.length > 5 && (
                        <Badge variant="outline" className="text-xs">
                          +{method.coverage.length - 5} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-4 border-t">
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                    <Button size="sm" variant="outline">
                      <PencilIcon className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    {method.status === "active" ? (
                      <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                        Deactivate
                      </Button>
                    ) : (
                      <Button size="sm" variant="outline" className="text-green-600 hover:text-green-700">
                        Activate
                      </Button>
                    )}
                    <Button size="sm" variant="ghost">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {filteredMethods.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <TruckIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada metode pengiriman ditemukan</h3>
                <p className="text-muted-foreground mb-4">
                  Tidak ada metode pengiriman yang cocok dengan filter Anda
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Shipping Method
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Shipping Zones Tab */}
      {activeTab === "zones" && (
        <div className="space-y-4">
          {filteredZones.map((zone) => (
            <Card key={zone.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex flex-col gap-4">
                  {/* Zone Header */}
                  <div className="flex flex-col md:flex-row justify-between gap-4">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-lg font-semibold">{zone.name}</h3>
                        {getStatusBadge(zone.status)}
                      </div>
                      <p className="text-sm text-muted-foreground">ID: {zone.id}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm">Last Updated: {formatDate(zone.lastUpdated)}</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        {zone.methods.length} shipping methods available
                      </p>
                    </div>
                  </div>

                  {/* Regions */}
                  <div>
                    <h4 className="text-sm font-medium mb-1">Regions</h4>
                    <div className="flex flex-wrap gap-1">
                      {zone.regions.map((region, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {region}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Available Methods */}
                  <div>
                    <h4 className="text-sm font-medium mb-1">Available Shipping Methods</h4>
                    <div className="flex flex-wrap gap-1">
                      {zone.methods.map((methodId) => {
                        const method = shippingMethods.find(m => m.id === methodId)
                        return method ? (
                          <Badge key={methodId} variant="secondary" className="text-xs">
                            {method.name}
                          </Badge>
                        ) : null
                      })}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-4 border-t">
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                    <Button size="sm" variant="outline">
                      <PencilIcon className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    {zone.status === "active" ? (
                      <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                        Deactivate
                      </Button>
                    ) : (
                      <Button size="sm" variant="outline" className="text-green-600 hover:text-green-700">
                        Activate
                      </Button>
                    )}
                    <Button size="sm" variant="ghost">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {filteredZones.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <GlobeIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada zona pengiriman ditemukan</h3>
                <p className="text-muted-foreground mb-4">
                  Tidak ada zona pengiriman yang cocok dengan filter Anda
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Shipping Zone
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Shipping Analytics Tab */}
      {activeTab === "analytics" && (
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Shipping Performance Overview</CardTitle>
              <CardDescription>
                Analisis performa pengiriman dalam 30 hari terakhir
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center border rounded-md">
                <p className="text-muted-foreground">Chart visualisasi pengiriman akan ditampilkan di sini</p>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Top Shipping Methods</CardTitle>
                <CardDescription>
                  Metode pengiriman yang paling sering digunakan
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>JNE Regular</span>
                    <span>42%</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full">
                    <div className="h-2 bg-blue-600 rounded-full" style={{ width: "42%" }}></div>
                  </div>

                  <div className="flex justify-between">
                    <span>SiCepat REG</span>
                    <span>25%</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full">
                    <div className="h-2 bg-blue-600 rounded-full" style={{ width: "25%" }}></div>
                  </div>

                  <div className="flex justify-between">
                    <span>J&T Express</span>
                    <span>18%</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full">
                    <div className="h-2 bg-blue-600 rounded-full" style={{ width: "18%" }}></div>
                  </div>

                  <div className="flex justify-between">
                    <span>JNE YES</span>
                    <span>10%</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full">
                    <div className="h-2 bg-blue-600 rounded-full" style={{ width: "10%" }}></div>
                  </div>

                  <div className="flex justify-between">
                    <span>AnterAja Same Day</span>
                    <span>5%</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full">
                    <div className="h-2 bg-blue-600 rounded-full" style={{ width: "5%" }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Shipping Issues</CardTitle>
                <CardDescription>
                  Masalah pengiriman dalam 30 hari terakhir
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                      <span>Late Deliveries</span>
                    </div>
                    <span className="font-medium">5.5%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-amber-500 rounded-full"></div>
                      <span>Package Damaged</span>
                    </div>
                    <span className="font-medium">1.2%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-orange-500 rounded-full"></div>
                      <span>Wrong Delivery Address</span>
                    </div>
                    <span className="font-medium">0.8%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-purple-500 rounded-full"></div>
                      <span>Lost Packages</span>
                    </div>
                    <span className="font-medium">0.3%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                      <span>Other Issues</span>
                    </div>
                    <span className="font-medium">0.7%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Shipping by Region</CardTitle>
              <CardDescription>
                Distribusi pengiriman berdasarkan region
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center border rounded-md">
                <p className="text-muted-foreground">Map visualisasi pengiriman per region akan ditampilkan di sini</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
} 