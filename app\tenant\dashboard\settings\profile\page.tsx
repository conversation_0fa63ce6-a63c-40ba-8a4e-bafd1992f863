"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ArrowLeft,
  User,
  Building,
  Mail,
  Phone,
  Globe,
  MapPin,
  Image,
  FileText,
  Save,
  Trash
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"

// Data dummy untuk informasi profile
const profileData = {
  user: {
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder-avatar.jpg",
    phone: "+62 812 3456 7890",
    role: "Admin",
    joinedAt: "2023-05-15T10:30:00"
  },
  business: {
    name: "PT Contoh Bisnis Indonesia",
    logo: "/placeholder-logo.jpg",
    description: "Perusahaan e-commerce yang fokus pada produk lokal Indonesia",
    website: "https://contoh-bisnis.id",
    industry: "retail",
    size: "11-50",
    established: "2020",
    taxId: "09.123.456.7-123.000"
  },
  address: {
    street: "Jl. Sudirman No. 123",
    city: "Jakarta",
    state: "DKI Jakarta",
    zipCode: "12930",
    country: "Indonesia"
  },
  social: {
    facebook: "contohbisnis",
    instagram: "contohbisnis",
    twitter: "contohbisnis",
    linkedin: "company/contohbisnis"
  }
}

// Daftar industri untuk pilihan select
const industries = [
  { value: "retail", label: "Retail & E-commerce" },
  { value: "food", label: "Makanan & Minuman" },
  { value: "technology", label: "Teknologi & Software" },
  { value: "education", label: "Pendidikan" },
  { value: "health", label: "Kesehatan" },
  { value: "finance", label: "Keuangan & Perbankan" },
  { value: "manufacturing", label: "Manufaktur" },
  { value: "service", label: "Jasa & Konsultan" },
  { value: "other", label: "Lainnya" }
]

// Daftar ukuran perusahaan untuk pilihan select
const companySizes = [
  { value: "1-10", label: "1-10 karyawan" },
  { value: "11-50", label: "11-50 karyawan" },
  { value: "51-200", label: "51-200 karyawan" },
  { value: "201-500", label: "201-500 karyawan" },
  { value: "501-1000", label: "501-1000 karyawan" },
  { value: "1000+", label: "Lebih dari 1000 karyawan" }
]

export default function ProfileSettingsPage() {
  const [selectedTab, setSelectedTab] = useState("personal")
  
  // Format tanggal
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/settings">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Pengaturan Profil</h1>
            <p className="text-muted-foreground">
              Kelola informasi profil pribadi dan bisnis Anda
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button>
            <Save className="h-4 w-4 mr-2" />
            Simpan Perubahan
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="personal" value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="personal">Informasi Pribadi</TabsTrigger>
          <TabsTrigger value="business">Profil Bisnis</TabsTrigger>
          <TabsTrigger value="address">Alamat & Kontak</TabsTrigger>
        </TabsList>
        
        {/* Personal Info Tab */}
        <TabsContent value="personal" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Informasi Pribadi</CardTitle>
              <CardDescription>
                Kelola detail profil pribadi Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col gap-6 md:flex-row">
                <div className="space-y-4 md:w-1/3">
                  <div className="flex flex-col items-center justify-center gap-4">
                    <Avatar className="h-24 w-24">
                      <AvatarImage src={profileData.user.avatar} alt={profileData.user.name} />
                      <AvatarFallback>{profileData.user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <Button variant="outline" size="sm">
                      <Image className="h-4 w-4 mr-2" />
                      Ubah Foto
                    </Button>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">
                      Bergabung sejak {formatDate(profileData.user.joinedAt)}
                    </p>
                  </div>
                </div>
                
                <div className="space-y-4 md:w-2/3">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="name">Nama Lengkap</Label>
                      <Input id="name" defaultValue={profileData.user.name} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input id="email" type="email" defaultValue={profileData.user.email} />
                    </div>
                  </div>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Nomor Telepon</Label>
                      <Input id="phone" defaultValue={profileData.user.phone} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="role">Peran</Label>
                      <Select defaultValue={profileData.user.role.toLowerCase()}>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih peran" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="owner">Pemilik</SelectItem>
                          <SelectItem value="manager">Manajer</SelectItem>
                          <SelectItem value="staff">Staf</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium">Keamanan Akun</h3>
                    <p className="text-sm text-muted-foreground">
                      Kelola kata sandi dan keamanan akun Anda
                    </p>
                  </div>
                  <Button variant="outline" asChild>
                    <Link href="/tenant/dashboard/settings/security">
                      Pengaturan Keamanan
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Business Profile Tab */}
        <TabsContent value="business" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Profil Bisnis</CardTitle>
              <CardDescription>
                Kelola informasi bisnis dan perusahaan Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col gap-6 md:flex-row">
                <div className="space-y-4 md:w-1/3">
                  <div className="flex flex-col items-center justify-center gap-4">
                    <div className="border rounded-md p-4 flex items-center justify-center h-24 w-24">
                      <img 
                        src={profileData.business.logo} 
                        alt={profileData.business.name} 
                        className="max-h-full max-w-full object-contain" 
                      />
                    </div>
                    <Button variant="outline" size="sm">
                      <Image className="h-4 w-4 mr-2" />
                      Ubah Logo
                    </Button>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">
                      Logo disarankan berukuran 512x512 pixel
                    </p>
                  </div>
                </div>
                
                <div className="space-y-4 md:w-2/3">
                  <div className="space-y-2">
                    <Label htmlFor="business-name">Nama Bisnis / Perusahaan</Label>
                    <Input id="business-name" defaultValue={profileData.business.name} />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="business-description">Deskripsi Bisnis</Label>
                    <Textarea 
                      id="business-description" 
                      defaultValue={profileData.business.description}
                      rows={4}
                    />
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Input id="website" defaultValue={profileData.business.website} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="taxId">NPWP</Label>
                      <Input id="taxId" defaultValue={profileData.business.taxId} />
                    </div>
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="industry">Industri</Label>
                      <Select defaultValue={profileData.business.industry}>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih industri" />
                        </SelectTrigger>
                        <SelectContent>
                          {industries.map(industry => (
                            <SelectItem key={industry.value} value={industry.value}>
                              {industry.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="company-size">Ukuran Perusahaan</Label>
                      <Select defaultValue={profileData.business.size}>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih ukuran perusahaan" />
                        </SelectTrigger>
                        <SelectContent>
                          {companySizes.map(size => (
                            <SelectItem key={size.value} value={size.value}>
                              {size.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="established">Tahun Berdiri</Label>
                    <Input id="established" defaultValue={profileData.business.established} />
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">Media Sosial</h3>
                  <p className="text-sm text-muted-foreground">
                    Tambahkan tautan media sosial bisnis Anda
                  </p>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="facebook">Facebook</Label>
                    <div className="flex">
                      <span className="inline-flex items-center px-3 bg-muted border border-r-0 border-input rounded-l-md text-sm text-muted-foreground">
                        facebook.com/
                      </span>
                      <Input id="facebook" className="rounded-l-none" defaultValue={profileData.social.facebook} />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="instagram">Instagram</Label>
                    <div className="flex">
                      <span className="inline-flex items-center px-3 bg-muted border border-r-0 border-input rounded-l-md text-sm text-muted-foreground">
                        instagram.com/
                      </span>
                      <Input id="instagram" className="rounded-l-none" defaultValue={profileData.social.instagram} />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="twitter">Twitter</Label>
                    <div className="flex">
                      <span className="inline-flex items-center px-3 bg-muted border border-r-0 border-input rounded-l-md text-sm text-muted-foreground">
                        twitter.com/
                      </span>
                      <Input id="twitter" className="rounded-l-none" defaultValue={profileData.social.twitter} />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="linkedin">LinkedIn</Label>
                    <div className="flex">
                      <span className="inline-flex items-center px-3 bg-muted border border-r-0 border-input rounded-l-md text-sm text-muted-foreground">
                        linkedin.com/
                      </span>
                      <Input id="linkedin" className="rounded-l-none" defaultValue={profileData.social.linkedin} />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Address Tab */}
        <TabsContent value="address" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Alamat & Kontak</CardTitle>
              <CardDescription>
                Kelola alamat dan informasi kontak bisnis Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">Alamat Kantor Utama</h3>
                  <p className="text-sm text-muted-foreground">
                    Alamat ini akan digunakan untuk dokumentasi resmi
                  </p>
                </div>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="street">Alamat Lengkap</Label>
                    <Textarea id="street" rows={2} defaultValue={profileData.address.street} />
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="city">Kota</Label>
                      <Input id="city" defaultValue={profileData.address.city} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="state">Provinsi</Label>
                      <Input id="state" defaultValue={profileData.address.state} />
                    </div>
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="zipCode">Kode Pos</Label>
                      <Input id="zipCode" defaultValue={profileData.address.zipCode} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="country">Negara</Label>
                      <Select defaultValue="indonesia">
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih negara" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="indonesia">Indonesia</SelectItem>
                          <SelectItem value="malaysia">Malaysia</SelectItem>
                          <SelectItem value="singapore">Singapura</SelectItem>
                          <SelectItem value="other">Lainnya</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium">Kontak Bisnis</h3>
                    <p className="text-sm text-muted-foreground">
                      Kontak yang akan ditampilkan kepada pelanggan
                    </p>
                  </div>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="business-email">Email Bisnis</Label>
                    <Input id="business-email" type="email" defaultValue="<EMAIL>" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="business-phone">Telepon Bisnis</Label>
                    <Input id="business-phone" defaultValue="+62 21 1234 5678" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="customer-service">Email Layanan Pelanggan</Label>
                    <Input id="customer-service" type="email" defaultValue="<EMAIL>" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="customer-service-phone">Telepon Layanan Pelanggan</Label>
                    <Input id="customer-service-phone" defaultValue="+62 812 9876 5432" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 