"use client"
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

// Dummy data untuk chart
const data = [
  { name: "<PERSON>", value: 4000 },
  { name: "Feb", value: 3000 },
  { name: "<PERSON>", value: 5000 },
  { name: "Apr", value: 4000 },
  { name: "May", value: 7000 },
  { name: "<PERSON>", value: 6000 },
  { name: "<PERSON>", value: 8000 },
  { name: "Aug", value: 9000 },
  { name: "Sep", value: 8000 },
  { name: "Oct", value: 10000 },
  { name: "Nov", value: 12000 },
  { name: "Dec", value: 15000 },
]

export function AnalyticsTimeSeriesChart() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 10, left: 10, bottom: 5 }}>
          <XAxis dataKey="name" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
          <YAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `$${value}`}
          />
          <Tooltip
            formatter={(value: number) => [`$${value}`, "Revenue"]}
            contentStyle={{ backgroundColor: "hsl(var(--card))", borderColor: "hsl(var(--border))" }}
            labelStyle={{ color: "hsl(var(--foreground))" }}
          />
          <Line type="monotone" dataKey="value" stroke="hsl(var(--primary))" strokeWidth={2} dot={false} />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
