"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { format } from "date-fns"
import { id } from "date-fns/locale"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CustomDatePicker } from "@/components/ui/custom-date-picker"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { InfoIcon } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

// Daftar alasan penangguhan yang umum
const SUSPENSION_REASONS = [
  "Pelanggaran ketentuan layanan",
  "Aktivitas mencurigakan",
  "Permintaan dari tenant",
  "Pembayaran tertunggak",
  "Penyalahgunaan platform",
  "Konten yang tidak sesuai",
  "Lainnya",
]

export interface SuspendTenantData {
  reason: string
  customReason?: string
  durationType: "temporary" | "permanent"
  endDate?: string
  sendNotification: boolean
  notifyUsers: boolean
  notes?: string
  emailContent?: string
}

interface SuspendTenantModalProps {
  tenant: {
    id: string
    name: string
  }
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuspend: (data: SuspendTenantData) => void
  isLoading?: boolean
  isBulkAction?: boolean
}

// Default form values
const DEFAULT_FORM_DATA = {
  reason: "",
  customReason: "",
  durationType: "temporary",
  endDate: getFormattedDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days from now
  sendNotification: true,
  notifyUsers: true,
  notes: "",
  emailContent: "",
}

// Fungsi untuk mendapatkan tanggal dalam format YYYY-MM-DD
function getFormattedDate(date: Date): string {
  return date.toISOString().split("T")[0]
}

// Fungsi untuk memformat tanggal untuk tampilan
function formatDisplayDate(dateStr: string): string {
  if (!dateStr) return ""
  const date = new Date(dateStr)
  return format(date, "dd MMMM yyyy", { locale: id })
}

export function SuspendTenantModal({
  tenant,
  open,
  onOpenChange,
  onSuspend,
  isLoading = false,
  isBulkAction = false,
}: SuspendTenantModalProps) {
  const [activeTab, setActiveTab] = useState("details")
  const [formData, setFormData] = useState<SuspendTenantData>({ ...DEFAULT_FORM_DATA })
  const [modalKey, setModalKey] = useState(0) // Used to force re-render
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    formData.endDate ? new Date(formData.endDate) : undefined,
  )
  const { toast } = useToast()

  const addNotification = (props: { message: string; type: "success" | "error" }) => {
    toast({
      title: props.type === "success" ? "Success" : "Error",
      description: props.message,
      variant: props.type,
    })
  }

  // Reset form and force re-render when modal opens/closes or tenant changes
  useEffect(() => {
    if (open) {
      // Initialize with default values when modal opens
      const defaultData = { ...DEFAULT_FORM_DATA }
      setFormData(defaultData)
      setSelectedDate(defaultData.endDate ? new Date(defaultData.endDate) : undefined)
      setActiveTab("details")
      setModalKey((prev) => prev + 1) // Force re-render
    }
  }, [open, tenant?.id])

  // Update endDate when selectedDate changes
  useEffect(() => {
    if (selectedDate) {
      setFormData((prev) => ({
        ...prev,
        endDate: getFormattedDate(selectedDate),
      }))
    }
  }, [selectedDate])

  // Update email content when tenant or form data changes
  useEffect(() => {
    if (tenant?.id) {
      const actualReason = formData.reason === "Lainnya" ? formData.customReason : formData.reason
      const endDateStr = formData.endDate ? formatDisplayDate(formData.endDate) : "[END_DATE]"

      const emailContent = `Kepada ${tenant.name} Administrator,

Kami ingin memberitahukan bahwa akun tenant Anda telah ditangguhkan karena alasan berikut:

${actualReason || "[ALASAN]"}

Selama masa penangguhan ini, Anda dan pengguna Anda tidak akan dapat mengakses platform. ${
        formData.durationType === "temporary"
          ? `Penangguhan akan dicabut pada ${endDateStr}.`
          : "Penangguhan ini bersifat permanen hingga pemberitahuan lebih lanjut."
      }

${formData.notes ? `Catatan tambahan: ${formData.notes}` : ""}

Jika Anda merasa penangguhan ini dibuat karena kesalahan atau memiliki pertanyaan, silakan hubungi tim dukungan kami.

Salam,
Tim Sellzio SaaS Platform`

      setFormData((prev) => ({ ...prev, emailContent }))
    }
  }, [tenant, formData.reason, formData.customReason, formData.durationType, formData.endDate, formData.notes])

  // Validasi form
  const isFormValid = () => {
    if (!formData.reason) return false
    if (formData.reason === "Lainnya" && !formData.customReason?.trim()) return false
    if (formData.durationType === "temporary" && !formData.endDate) return false
    return true
  }

  // Handle form submission
  const handleSubmit = () => {
    if (!isFormValid()) return

    // Prepare final data
    const finalData = {
      ...formData,
      // Use custom reason if "Lainnya" is selected
      reason: formData.reason === "Lainnya" ? formData.customReason : formData.reason,
    }

    // Pastikan data durationType dan endDate dikirim dengan benar
    if (finalData.durationType === "temporary" && !finalData.endDate) {
      addNotification({
        message: "Tanggal berakhir penangguhan harus diisi untuk penangguhan sementara",
        type: "error",
      })
      return
    }

    // Call onSuspend callback
    onSuspend(finalData)
  }

  // Handle form changes
  const handleChange = (field: keyof SuspendTenantData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  // Mendapatkan tanggal minimum (hari ini)
  const today = new Date()

  return (
    <Dialog key={modalKey} open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Suspend {isBulkAction ? "Selected Tenants" : tenant?.name}</DialogTitle>
          <DialogDescription>
            {isBulkAction
              ? "Tentukan alasan dan durasi untuk menangguhkan tenant yang dipilih."
              : `Tentukan alasan dan durasi untuk menangguhkan ${tenant?.name}.`}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="details">Detail</TabsTrigger>
            <TabsTrigger value="email-preview">Preview Email</TabsTrigger>
          </TabsList>

          <div className="overflow-y-auto pr-1 mt-4 max-h-[60vh]">
            <TabsContent value="details" className="space-y-5">
              <div className="space-y-2">
                <Label htmlFor="reason">
                  Alasan Penangguhan <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.reason} onValueChange={(value) => handleChange("reason", value)} name="reason">
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Pilih alasan penangguhan" />
                  </SelectTrigger>
                  <SelectContent>
                    {SUSPENSION_REASONS.map((reason) => (
                      <SelectItem key={reason} value={reason}>
                        {reason}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {formData.reason === "Lainnya" && (
                  <div className="mt-4 pt-2">
                    <Label htmlFor="customReason" className="mb-1 block">
                      Alasan Kustom <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="customReason"
                      placeholder="Masukkan alasan kustom"
                      value={formData.customReason || ""}
                      onChange={(e) => handleChange("customReason", e.target.value)}
                      required
                    />
                  </div>
                )}
              </div>

              <div className="space-y-3 pt-2">
                <Label>
                  Durasi Penangguhan <span className="text-red-500">*</span>
                </Label>
                <RadioGroup
                  value={formData.durationType}
                  onValueChange={(value) => handleChange("durationType", value)}
                  className="flex flex-col space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="temporary" id="temporary" />
                    <Label htmlFor="temporary" className="cursor-pointer">
                      Sementara
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="permanent" id="permanent" />
                    <Label htmlFor="permanent" className="cursor-pointer">
                      Permanen
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {formData.durationType === "temporary" && (
                <div className="space-y-3 pt-2">
                  <Label htmlFor="endDate">
                    Tanggal Berakhir <span className="text-red-500">*</span>
                  </Label>
                  <CustomDatePicker
                    value={selectedDate}
                    onChange={setSelectedDate}
                    placeholder="Pilih tanggal berakhir"
                    minDate={today}
                    required
                  />
                  {formData.endDate && (
                    <p className="text-sm text-muted-foreground">
                      Tenant akan ditangguhkan hingga {formatDisplayDate(formData.endDate)}
                    </p>
                  )}

                  <Alert variant="info" className="bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
                    <InfoIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <AlertTitle className="text-blue-800 dark:text-blue-300">Pengaktifan Otomatis</AlertTitle>
                    <AlertDescription className="text-blue-700 dark:text-blue-400">
                      Tenant akan otomatis diaktifkan kembali pada tanggal {formatDisplayDate(formData.endDate)}. Anda
                      tidak perlu melakukan tindakan manual untuk mengaktifkan kembali tenant ini.
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              <div className="space-y-3 pt-2">
                <Label>Notifikasi</Label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="sendNotification"
                      checked={formData.sendNotification}
                      onCheckedChange={(checked) => handleChange("sendNotification", !!checked)}
                    />
                    <Label htmlFor="sendNotification" className="cursor-pointer">
                      Kirim email notifikasi ke admin tenant
                    </Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="notifyUsers"
                      checked={formData.notifyUsers}
                      onCheckedChange={(checked) => handleChange("notifyUsers", !!checked)}
                    />
                    <Label htmlFor="notifyUsers" className="cursor-pointer">
                      Beritahu pengguna tenant saat mereka mencoba login
                    </Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2 pt-2">
                <Label htmlFor="notes">Catatan Tambahan (Opsional)</Label>
                <Textarea
                  id="notes"
                  value={formData.notes || ""}
                  onChange={(e) => handleChange("notes", e.target.value)}
                  placeholder="Catatan internal yang tidak akan dikirim ke tenant"
                  className="h-20"
                />
              </div>
            </TabsContent>

            <TabsContent value="email-preview" className="space-y-4">
              {formData.sendNotification ? (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="emailContent">Konten Email</Label>
                    <Textarea
                      id="emailContent"
                      value={formData.emailContent || ""}
                      onChange={(e) => handleChange("emailContent", e.target.value)}
                      className="h-[300px] font-mono text-sm"
                    />
                  </div>
                </>
              ) : (
                <div className="p-4 border rounded-md bg-muted">
                  <p className="text-muted-foreground">
                    Notifikasi email dinonaktifkan. Aktifkan notifikasi email di tab Detail untuk melihat dan mengedit
                    konten email.
                  </p>
                </div>
              )}
            </TabsContent>
          </div>
        </Tabs>

        <DialogFooter className="gap-2">
          <DialogClose asChild>
            <Button type="button" variant="outline" disabled={isLoading}>
              Batal
            </Button>
          </DialogClose>

          <Button onClick={handleSubmit} disabled={!isFormValid() || isLoading}>
            {isLoading ? "Memproses..." : "Tangguhkan Tenant"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
