import { Skeleton } from "@/components/ui/skeleton"

export function VelozioProductSkeleton() {
  return (
    <div className="bg-white rounded-[3px] overflow-hidden" style={{ boxShadow: "0 2px 8px rgba(0,0,0,0.15)" }}>
      {/* Image skeleton */}
      <div className="relative pt-[100%]">
        <Skeleton className="absolute inset-0 rounded-none" />
      </div>

      {/* Content skeleton */}
      <div className="p-2.5">
        {/* Name */}
        <Skeleton className="h-4 w-full mb-1" />
        <Skeleton className="h-4 w-3/4 mb-3" />

        {/* Rating & sold */}
        <Skeleton className="h-3 w-1/2 mb-2" />

        {/* Shipping */}
        <Skeleton className="h-3 w-2/3 mb-3" />

        {/* Price */}
        <Skeleton className="h-5 w-1/3 mb-2" />

        {/* Discount & COD */}
        <div className="flex justify-between">
          <Skeleton className="h-4 w-1/4" />
          <Skeleton className="h-4 w-1/6" />
        </div>
      </div>
    </div>
  )
}
