"use client"

import { useState } from "react"
import Image from "next/image"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Upload,
  Eye,
  ShoppingBag,
  Store,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Mock data for brands
const mockBrands = [
  {
    id: "b1",
    name: "TechVision",
    logo: "/brand-logo-1.png",
    description: "High-quality electronics and gadgets",
    website: "https://techvision.example.com",
    productCount: 78,
    storeCount: 3,
    verified: true,
    featured: true,
    categories: ["Electronics", "Gadgets"],
  },
  {
    id: "b2",
    name: "EcoStyle",
    logo: "/brand-logo-2.png",
    description: "Sustainable fashion and accessories",
    website: "https://ecostyle.example.com",
    productCount: 124,
    storeCount: 5,
    verified: true,
    featured: false,
    categories: ["Clothing", "Accessories"],
  },
  {
    id: "b3",
    name: "HomeComfort",
    logo: "/brand-logo-3.png",
    description: "Quality furniture and home decor",
    website: "https://homecomfort.example.com",
    productCount: 92,
    storeCount: 2,
    verified: true,
    featured: true,
    categories: ["Home & Kitchen", "Furniture"],
  },
  {
    id: "b4",
    name: "SportLife",
    logo: "/brand-logo-4.png",
    description: "Athletic wear and fitness equipment",
    website: "https://sportlife.example.com",
    productCount: 67,
    storeCount: 4,
    verified: true,
    featured: false,
    categories: ["Sports", "Fitness"],
  },
  {
    id: "b5",
    name: "GourmetTaste",
    logo: "/brand-logo-5.png",
    description: "Premium food and kitchen products",
    website: "https://gourmettaste.example.com",
    productCount: 45,
    storeCount: 1,
    verified: false,
    featured: false,
    categories: ["Food", "Kitchen"],
  },
  {
    id: "b6",
    name: "LuxuryEssentials",
    logo: "/brand-logo-6.png",
    description: "High-end beauty and personal care",
    website: "https://luxuryessentials.example.com",
    productCount: 56,
    storeCount: 3,
    verified: true,
    featured: true,
    categories: ["Beauty", "Personal Care"],
  },
]

type Brand = {
  id: string
  name: string
  logo: string
  description: string
  website: string
  productCount: number
  storeCount: number
  verified: boolean
  featured: boolean
  categories: string[]
}

export function BrandManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null)
  const [showBrandDialog, setShowBrandDialog] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [verificationDialogOpen, setVerificationDialogOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("all")

  const filteredBrands = mockBrands.filter((brand) => {
    const matchesSearch =
      brand.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      brand.description.toLowerCase().includes(searchQuery.toLowerCase())

    if (activeTab === "all") return matchesSearch
    if (activeTab === "verified") return matchesSearch && brand.verified
    if (activeTab === "unverified") return matchesSearch && !brand.verified
    if (activeTab === "featured") return matchesSearch && brand.featured

    return matchesSearch
  })

  const handleBrandClick = (brand: Brand) => {
    setSelectedBrand(brand)
  }

  const handleAddBrand = () => {
    setSelectedBrand(null)
    setShowBrandDialog(true)
  }

  const handleEditBrand = (brand: Brand) => {
    setSelectedBrand(brand)
    setShowBrandDialog(true)
  }

  const handleDeleteClick = (brand: Brand) => {
    setSelectedBrand(brand)
    setDeleteDialogOpen(true)
  }

  const handleVerificationClick = (brand: Brand) => {
    setSelectedBrand(brand)
    setVerificationDialogOpen(true)
  }

  const confirmDelete = () => {
    // In a real app, you would delete the brand here
    console.log(`Deleting brand ${selectedBrand?.id}`)
    setDeleteDialogOpen(false)
    setSelectedBrand(null)
  }

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <div className="lg:col-span-1">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between px-4 py-3">
            <CardTitle className="text-base">Brands</CardTitle>
            <Button size="sm" onClick={handleAddBrand}>
              <Plus className="mr-1 h-4 w-4" />
              Add Brand
            </Button>
          </CardHeader>
          <CardContent className="px-2 py-2">
            <div className="mb-4 px-2">
              <Input
                placeholder="Search brands..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
                prefix={<Search className="h-4 w-4 text-muted-foreground" />}
              />
            </div>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="verified">Verified</TabsTrigger>
                <TabsTrigger value="unverified">Pending</TabsTrigger>
                <TabsTrigger value="featured">Featured</TabsTrigger>
              </TabsList>
            </Tabs>
            <div className="mt-4 max-h-[600px] overflow-y-auto space-y-1">
              {filteredBrands.map((brand) => (
                <div
                  key={brand.id}
                  className={`flex items-center gap-3 rounded-md px-3 py-2 hover:bg-muted ${selectedBrand?.id === brand.id ? "bg-muted" : ""}`}
                  onClick={() => handleBrandClick(brand)}
                >
                  <div className="h-10 w-10 overflow-hidden rounded-md border">
                    <Image
                      src={brand.logo || "/placeholder.svg"}
                      alt={brand.name}
                      width={40}
                      height={40}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{brand.name}</span>
                      {brand.verified && <CheckCircle2 className="h-4 w-4 text-green-500" />}
                      {brand.featured && (
                        <Badge variant="outline" className="ml-auto">
                          Featured
                        </Badge>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {brand.productCount} products • {brand.storeCount} stores
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="lg:col-span-2">
        {selectedBrand ? (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
              <CardTitle>Brand Details</CardTitle>
              <div className="flex gap-2">
                {!selectedBrand.verified && (
                  <Button variant="outline" size="sm" onClick={() => handleVerificationClick(selectedBrand)}>
                    <CheckCircle2 className="mr-1 h-4 w-4" />
                    Verify
                  </Button>
                )}
                <Button variant="outline" size="sm" onClick={() => handleEditBrand(selectedBrand)}>
                  <Edit className="mr-1 h-4 w-4" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-destructive hover:bg-destructive/10"
                  onClick={() => handleDeleteClick(selectedBrand)}
                >
                  <Trash2 className="mr-1 h-4 w-4" />
                  Delete
                </Button>
              </div>
            </CardHeader>
            <CardContent className="px-6 py-4">
              <div className="flex flex-col gap-6 md:flex-row">
                <div className="flex flex-col items-center gap-4">
                  <div className="h-32 w-32 overflow-hidden rounded-lg border">
                    <Image
                      src={selectedBrand.logo || "/placeholder.svg"}
                      alt={selectedBrand.name}
                      width={128}
                      height={128}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div className="flex flex-col items-center">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Verification Status:</span>
                      {selectedBrand.verified ? (
                        <Badge className="bg-green-500">Verified</Badge>
                      ) : (
                        <Badge variant="outline" className="border-amber-500 text-amber-500">
                          Pending
                        </Badge>
                      )}
                    </div>
                    <div className="mt-2 flex items-center gap-2">
                      <span className="font-medium">Featured:</span>
                      <Badge variant={selectedBrand.featured ? "default" : "outline"}>
                        {selectedBrand.featured ? "Yes" : "No"}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="flex-1 space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Brand Name</h3>
                    <p className="mt-1 text-base">{selectedBrand.name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                    <p className="mt-1 text-base">{selectedBrand.description}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Website</h3>
                    <p className="mt-1 text-base">
                      <a
                        href={selectedBrand.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        {selectedBrand.website}
                      </a>
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Categories</h3>
                    <div className="mt-1 flex flex-wrap gap-2">
                      {selectedBrand.categories.map((category, index) => (
                        <Badge key={index} variant="outline">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 grid grid-cols-1 gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader className="px-4 py-3">
                    <div className="flex items-center gap-2">
                      <ShoppingBag className="h-5 w-5 text-muted-foreground" />
                      <CardTitle className="text-base">Products</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="px-4 py-3">
                    <div className="text-3xl font-bold">{selectedBrand.productCount}</div>
                    <p className="text-sm text-muted-foreground">Total products under this brand</p>
                    <Button variant="outline" size="sm" className="mt-4">
                      <Eye className="mr-1 h-4 w-4" />
                      View Products
                    </Button>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="px-4 py-3">
                    <div className="flex items-center gap-2">
                      <Store className="h-5 w-5 text-muted-foreground" />
                      <CardTitle className="text-base">Stores</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="px-4 py-3">
                    <div className="text-3xl font-bold">{selectedBrand.storeCount}</div>
                    <p className="text-sm text-muted-foreground">Stores selling this brand</p>
                    <Button variant="outline" size="sm" className="mt-4">
                      <Eye className="mr-1 h-4 w-4" />
                      View Stores
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="flex h-full items-center justify-center rounded-lg border border-dashed p-8 text-center">
            <div>
              <ShoppingBag className="mx-auto h-10 w-10 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No Brand Selected</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Select a brand from the list or create a new one to view and edit its details.
              </p>
              <Button className="mt-4" onClick={handleAddBrand}>
                <Plus className="mr-1 h-4 w-4" />
                Add Brand
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Brand Dialog */}
      <Dialog open={showBrandDialog} onOpenChange={setShowBrandDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{selectedBrand ? "Edit Brand" : "Add New Brand"}</DialogTitle>
            <DialogDescription>
              {selectedBrand ? "Update the details for this brand" : "Create a new product brand"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="brand-name">Brand Name</Label>
              <Input id="brand-name" defaultValue={selectedBrand?.name || ""} placeholder="e.g. TechVision" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="brand-description">Description</Label>
              <Textarea
                id="brand-description"
                defaultValue={selectedBrand?.description || ""}
                placeholder="Describe this brand..."
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="brand-website">Website</Label>
              <Input
                id="brand-website"
                defaultValue={selectedBrand?.website || ""}
                placeholder="e.g. https://example.com"
              />
            </div>
            <div className="grid gap-2">
              <Label>Brand Logo</Label>
              <div className="flex items-center gap-4">
                {selectedBrand?.logo && (
                  <div className="h-16 w-16 overflow-hidden rounded-md border">
                    <Image
                      src={selectedBrand.logo || "/placeholder.svg"}
                      alt={selectedBrand.name}
                      width={64}
                      height={64}
                      className="h-full w-full object-cover"
                    />
                  </div>
                )}
                <Button variant="outline" className="flex-1">
                  <Upload className="mr-2 h-4 w-4" />
                  {selectedBrand?.logo ? "Change Logo" : "Upload Logo"}
                </Button>
              </div>
            </div>
            <div className="grid gap-2">
              <Label>Categories</Label>
              <Select defaultValue={selectedBrand?.categories[0] || ""}>
                <SelectTrigger>
                  <SelectValue placeholder="Select primary category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Electronics">Electronics</SelectItem>
                  <SelectItem value="Clothing">Clothing</SelectItem>
                  <SelectItem value="Home & Kitchen">Home & Kitchen</SelectItem>
                  <SelectItem value="Beauty">Beauty</SelectItem>
                  <SelectItem value="Sports">Sports</SelectItem>
                  <SelectItem value="Food">Food</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Label htmlFor="featured" className="flex-1">
                Featured Brand
              </Label>
              <Switch id="featured" defaultChecked={selectedBrand?.featured || false} />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBrandDialog(false)}>
              Cancel
            </Button>
            <Button>{selectedBrand ? "Update Brand" : "Create Brand"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the brand "{selectedBrand?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center gap-4 py-4">
            <div className="rounded-full bg-destructive/10 p-2 text-destructive">
              <AlertCircle className="h-6 w-6" />
            </div>
            <div>
              <p className="font-medium">This will permanently delete the brand</p>
              <p className="text-sm text-muted-foreground">The brand will be removed from all products and stores.</p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete Brand
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Verification Dialog */}
      <Dialog open={verificationDialogOpen} onOpenChange={setVerificationDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Verify Brand</DialogTitle>
            <DialogDescription>Review and verify the brand "{selectedBrand?.name}".</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="flex items-center gap-4">
              <div className="h-16 w-16 overflow-hidden rounded-md border">
                <Image
                  src={selectedBrand?.logo || ""}
                  alt={selectedBrand?.name || ""}
                  width={64}
                  height={64}
                  className="h-full w-full object-cover"
                />
              </div>
              <div>
                <h3 className="font-medium">{selectedBrand?.name}</h3>
                <p className="text-sm text-muted-foreground">{selectedBrand?.description}</p>
              </div>
            </div>
            <div className="rounded-md border p-4">
              <h3 className="mb-2 font-medium">Verification Checklist</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="check-1" />
                  <Label htmlFor="check-1" className="text-sm">
                    Brand information is accurate and complete
                  </Label>
                </div>
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="check-2" />
                  <Label htmlFor="check-2" className="text-sm">
                    Brand logo is high quality and appropriate
                  </Label>
                </div>
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="check-3" />
                  <Label htmlFor="check-3" className="text-sm">
                    Website is valid and operational
                  </Label>
                </div>
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="check-4" />
                  <Label htmlFor="check-4" className="text-sm">
                    Brand complies with platform policies
                  </Label>
                </div>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="verification-notes">Notes (Optional)</Label>
              <Textarea id="verification-notes" placeholder="Add any notes about this verification..." />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setVerificationDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={() => setVerificationDialogOpen(false)}>
              <XCircle className="mr-1 h-4 w-4" />
              Reject
            </Button>
            <Button onClick={() => setVerificationDialogOpen(false)}>
              <CheckCircle2 className="mr-1 h-4 w-4" />
              Verify Brand
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
