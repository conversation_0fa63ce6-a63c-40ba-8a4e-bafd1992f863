"use client"

import { <PERSON>, <PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { UserMenu } from "@/components/dashboard/user-menu"
import { Badge } from "@/components/ui/badge"

export function DashboardHeader() {
  return (
    <header className="sticky top-0 z-30 flex h-14 items-center gap-4 border-b border-border/40 bg-background/95 px-4 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="relative flex-1 max-w-md">
        <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Cari..."
          className="w-full bg-background pl-8 focus-visible:ring-primary/20"
        />
      </div>
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              <Badge className="absolute -right-1 -top-1 h-4 w-4 p-0 text-[10px]">3</Badge>
            </Button>
          </div>
        </div>
        <div className="hidden md:flex">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <div className="flex flex-col items-end">
              <span className="text-foreground">Admin</span>
              <span className="text-xs"><EMAIL></span>
            </div>
            <UserMenu />
          </div>
        </div>
      </div>
    </header>
  )
}
