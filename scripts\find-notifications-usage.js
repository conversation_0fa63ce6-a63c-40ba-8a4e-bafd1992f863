const fs = require("fs")
const path = require("path")

// <PERSON>rek<PERSON>i yang akan di-scan
const directoriesToScan = ["./app", "./components"]

// Pola untuk mendeteksi penggunaan useNotifications
const useNotificationsPattern = /useNotifications$$$$/g

// Fungsi untuk mencari file berdasarkan ekstensi
function findFiles(dir, extensions) {
  let results = []

  const list = fs.readdirSync(dir)

  list.forEach((file) => {
    file = path.join(dir, file)
    const stat = fs.statSync(file)

    if (stat && stat.isDirectory()) {
      // Rekursif untuk direktori
      results = results.concat(findFiles(file, extensions))
    } else {
      // Cek ekstensi file
      if (extensions.includes(path.extname(file).toLowerCase())) {
        results.push(file)
      }
    }
  })

  return results
}

// Fungsi untuk memeriksa file
function checkFile(file) {
  const content = fs.readFileSync(file, "utf-8")

  // Cek apakah file menggunakan useNotifications
  const hasUseNotifications = useNotificationsPattern.test(content)

  // Cek apakah file memiliki 'use client'
  const hasUseClient = content.includes('"use client"') || content.includes("'use client'")

  if (hasUseNotifications) {
    return {
      file,
      hasUseClient,
      hasUseNotifications,
    }
  }

  return null
}

// Main function
function main() {
  console.log("Scanning for useNotifications usage...")

  const problematicFiles = []

  // Scan setiap direktori
  directoriesToScan.forEach((dir) => {
    const files = findFiles(dir, [".ts", ".tsx", ".js", ".jsx"])

    files.forEach((file) => {
      const result = checkFile(file)
      if (result) {
        // Periksa potensi masalah
        if (!result.hasUseClient) {
          problematicFiles.push({
            ...result,
            issue: 'Missing "use client" directive',
          })
        } else {
          console.log(`[OK] ${file} (has both useNotifications and "use client")`)
        }
      }
    })
  })

  // Tampilkan hasil
  if (problematicFiles.length > 0) {
    console.log("\n⚠️ Potential issues found:")
    problematicFiles.forEach((file) => {
      console.log(`- ${file.file}: ${file.issue}`)
    })

    console.log("\nSuggested fixes:")
    console.log('1. Add "use client" directive at the top of the file')
    console.log("2. OR Split the component into client and server parts")
    console.log("3. Ensure the component is wrapped by NotificationsProvider")
  } else {
    console.log("\n✅ No issues found!")
  }
}

main()
