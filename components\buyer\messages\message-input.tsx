"use client"

import type React from "react"

import { useState, useRef } from "react"
import { <PERSON><PERSON><PERSON>, ImageIcon, Send, Smile, X } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>rigger, TooltipProvider } from "@/components/ui/tooltip"

interface MessageInputProps {
  value: string
  onChange: (value: string) => void
  onSend: (content: string, image?: string) => void
}

export function MessageInput({ value, onChange, onSend }: MessageInputProps) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleSend = () => {
    if (value.trim() || selectedImage) {
      onSend(value, selectedImage || undefined)
      setSelectedImage(null)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Dalam aplikasi nyata, ini akan mengunggah file ke server
      // Untuk demo, kita gunakan URL lokal
      const imageUrl = URL.createObjectURL(file)
      setSelectedImage(imageUrl)
    }
  }

  const handleRemoveImage = () => {
    setSelectedImage(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <TooltipProvider>
      <div className="p-4 border-t border-border">
        {selectedImage && (
          <div className="mb-2 relative inline-block">
            <img src={selectedImage || "/placeholder.svg"} alt="Selected" className="h-20 rounded-md object-cover" />
            <Button
              variant="destructive"
              size="icon"
              className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
              onClick={handleRemoveImage}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        )}
        <div className="flex items-center gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full"
                onClick={() => fileInputRef.current?.click()}
              >
                <Paperclip className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Lampirkan file</TooltipContent>
          </Tooltip>
          <input type="file" ref={fileInputRef} className="hidden" accept="image/*" onChange={handleFileChange} />
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full"
                onClick={() => fileInputRef.current?.click()}
              >
                <ImageIcon className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Kirim gambar</TooltipContent>
          </Tooltip>
          <Input
            placeholder="Ketik pesan..."
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1"
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" className="rounded-full">
                <Smile className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Emoji</TooltipContent>
          </Tooltip>
          <Button
            variant="default"
            size="icon"
            className="rounded-full"
            onClick={handleSend}
            disabled={!value.trim() && !selectedImage}
          >
            <Send className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </TooltipProvider>
  )
}
