import { NextRequest, NextResponse } from 'next/server';
import { storeSettingService } from '@/lib/services/store-settings';
import { productCategoryService } from '@/lib/services/product-categories';
import { brandService } from '@/lib/services/brands';

const storeSettings = [
  // General Settings
  {
    setting_key: 'store_name',
    setting_name: 'Nama Store',
    setting_value: 'My Store',
    setting_type: 'text' as const,
    category: 'general',
    description: 'Nama resmi store Anda',
    is_public: true,
    is_required: true,
    sort_order: 1,
    validation_rules: {
      required: true,
      min_length: 2,
      max_length: 100
    }
  },
  {
    setting_key: 'store_description',
    setting_name: 'Deskripsi Store',
    setting_value: 'Toko online terpercaya dengan berbagai produk berkualitas',
    setting_type: 'textarea' as const,
    category: 'general',
    description: 'Deskripsi singkat tentang store Anda',
    is_public: true,
    is_required: false,
    sort_order: 2,
    validation_rules: {
      max_length: 500
    }
  },
  {
    setting_key: 'store_email',
    setting_name: 'Email Store',
    setting_value: '<EMAIL>',
    setting_type: 'email' as const,
    category: 'general',
    description: '<PERSON><PERSON> kontak untuk store',
    is_public: true,
    is_required: true,
    sort_order: 3,
    validation_rules: {
      required: true,
      email: true
    }
  },
  {
    setting_key: 'store_phone',
    setting_name: 'Nomor Telepon',
    setting_value: '+62812345678',
    setting_type: 'tel' as const,
    category: 'general',
    description: 'Nomor telepon store untuk kontak',
    is_public: true,
    is_required: false,
    sort_order: 4,
    validation_rules: {
      pattern: '^\\+?[1-9]\\d{1,14}$'
    }
  },
  {
    setting_key: 'commission_rate',
    setting_name: 'Rate Komisi (%)',
    setting_value: '10',
    setting_type: 'number' as const,
    category: 'commission',
    description: 'Persentase komisi untuk affiliate',
    is_public: false,
    is_required: true,
    sort_order: 1,
    validation_rules: {
      required: true,
      min: 0,
      max: 50,
      step: 0.1
    }
  },
  {
    setting_key: 'min_commission_payout',
    setting_name: 'Minimum Payout Komisi',
    setting_value: '100000',
    setting_type: 'number' as const,
    category: 'commission',
    description: 'Minimum saldo komisi untuk bisa dicairkan (Rupiah)',
    is_public: false,
    is_required: true,
    sort_order: 2,
    validation_rules: {
      required: true,
      min: 10000
    }
  },
  {
    setting_key: 'payment_methods',
    setting_name: 'Metode Pembayaran',
    setting_value: 'bank_transfer,credit_card,ewallet',
    setting_type: 'text' as const,
    category: 'payment',
    description: 'Metode pembayaran yang diterima (dipisah koma)',
    is_public: true,
    is_required: true,
    sort_order: 1,
    validation_rules: {
      required: true
    }
  },
  {
    setting_key: 'auto_accept_payment',
    setting_name: 'Auto Accept Payment',
    setting_value: 'true',
    setting_type: 'boolean' as const,
    category: 'payment',
    description: 'Otomatis terima pembayaran yang valid',
    is_public: false,
    is_required: false,
    sort_order: 2,
    validation_rules: {}
  },
  {
    setting_key: 'free_shipping_threshold',
    setting_name: 'Minimum Free Shipping',
    setting_value: '100000',
    setting_type: 'number' as const,
    category: 'shipping',
    description: 'Minimum pembelian untuk gratis ongkir (Rupiah)',
    is_public: true,
    is_required: false,
    sort_order: 1,
    validation_rules: {
      min: 0
    }
  },
  {
    setting_key: 'shipping_zones',
    setting_name: 'Area Pengiriman',
    setting_value: 'jakarta,bogor,depok,tangerang,bekasi',
    setting_type: 'text' as const,
    category: 'shipping',
    description: 'Area yang dilayani pengiriman (dipisah koma)',
    is_public: true,
    is_required: true,
    sort_order: 2,
    validation_rules: {
      required: true
    }
  },
  {
    setting_key: 'email_notifications',
    setting_name: 'Notifikasi Email',
    setting_value: 'true',
    setting_type: 'boolean' as const,
    category: 'notification',
    description: 'Aktifkan notifikasi via email',
    is_public: false,
    is_required: false,
    sort_order: 1,
    validation_rules: {}
  },
  {
    setting_key: 'sms_notifications',
    setting_name: 'Notifikasi SMS',
    setting_value: 'false',
    setting_type: 'boolean' as const,
    category: 'notification',
    description: 'Aktifkan notifikasi via SMS',
    is_public: false,
    is_required: false,
    sort_order: 2,
    validation_rules: {}
  },
  {
    setting_key: 'require_email_verification',
    setting_name: 'Verifikasi Email Wajib',
    setting_value: 'true',
    setting_type: 'boolean' as const,
    category: 'security',
    description: 'Wajibkan verifikasi email untuk registrasi',
    is_public: false,
    is_required: false,
    sort_order: 1,
    validation_rules: {}
  },
  {
    setting_key: 'max_login_attempts',
    setting_name: 'Maksimal Percobaan Login',
    setting_value: '5',
    setting_type: 'number' as const,
    category: 'security',
    description: 'Maksimal percobaan login sebelum akun dikunci',
    is_public: false,
    is_required: true,
    sort_order: 2,
    validation_rules: {
      required: true,
      min: 3,
      max: 10
    }
  },
  {
    setting_key: 'meta_title',
    setting_name: 'Meta Title',
    setting_value: 'My Store - Toko Online Terpercaya',
    setting_type: 'text' as const,
    category: 'seo',
    description: 'Judul halaman untuk SEO',
    is_public: true,
    is_required: false,
    sort_order: 1,
    validation_rules: {
      max_length: 60
    }
  },
  {
    setting_key: 'meta_description',
    setting_name: 'Meta Description',
    setting_value: 'Belanja online mudah dan aman di My Store. Berbagai produk berkualitas dengan harga terbaik.',
    setting_type: 'textarea' as const,
    category: 'seo',
    description: 'Deskripsi halaman untuk SEO',
    is_public: true,
    is_required: false,
    sort_order: 2,
    validation_rules: {
      max_length: 160
    }
  }
]

// POST - Seed store settings data
export async function POST(request: NextRequest) {
  try {
    // Check if settings already exist
    const existingSettings = await storeSettingService.getSettings()

    if (existingSettings.length > 0) {
      return NextResponse.json(
        { message: 'Store settings already exist', count: existingSettings.length },
        { status: 200 }
      )
    }

    // Create settings one by one to handle validation
    const createdSettings = []
    const errors = []

    for (const setting of storeSettings) {
      try {
        const created = await storeSettingService.createSetting(setting)
        createdSettings.push(created)
      } catch (error) {
        console.error(`Error creating setting ${setting.setting_key}:`, error)
        errors.push({
          setting_key: setting.setting_key,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      message: 'Store settings seeded successfully',
      created: createdSettings.length,
      errors: errors.length,
      errorDetails: errors
    }, { status: 201 })

  } catch (error) {
    console.error('Error seeding store settings:', error)
    const errorMessage = error instanceof Error ? error.message : 'Failed to seed store settings'
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}
