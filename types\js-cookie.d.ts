declare module 'js-cookie' {
  export interface CookieAttributes {
    expires?: number | Date;
    path?: string;
    domain?: string;
    secure?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
  }

  export interface CookiesStatic<T extends object = object> {
    set(name: string, value: string, options?: CookieAttributes): void;
    get(name: string): string | undefined;
    get(name: string, options: { json: true }): T | undefined;
    get(name: string, options?: { json?: false }): string | undefined;
    remove(name: string, options?: CookieAttributes): void;
    getJSON(name: string): T | undefined;
  }

  const Cookies: CookiesStatic;
  export default Cookies;
}
