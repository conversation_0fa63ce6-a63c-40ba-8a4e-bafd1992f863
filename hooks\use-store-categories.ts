import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface StoreCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color: string;
  image_url?: string;
  is_active: boolean;
  sort_order: number;
  meta_title?: string;
  meta_description?: string;
  commission_rate: number;
  min_commission: number;
  max_commission?: number;
  requirements: {
    min_products?: number;
    required_docs?: string[];
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
}

export interface StoreCategoryFilters {
  search?: string;
  is_active?: boolean;
}

export interface StoreCategoryCreate {
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color?: string;
  image_url?: string;
  is_active?: boolean;
  sort_order?: number;
  meta_title?: string;
  meta_description?: string;
  commission_rate?: number;
  min_commission?: number;
  max_commission?: number;
  requirements?: object;
}

export function useStoreCategories() {
  const [categories, setCategories] = useState<StoreCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch categories with optional filters
  const fetchCategories = useCallback(async (filters?: StoreCategoryFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      
      if (filters?.search) {
        params.append('search', filters.search);
      }
      
      if (filters?.is_active !== undefined) {
        params.append('is_active', filters.is_active.toString());
      }
      
      const response = await fetch(`/api/store-categories?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      
      const data = await response.json();
      setCategories(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Get single category
  const getCategory = useCallback(async (id: string): Promise<StoreCategory | null> => {
    try {
      const response = await fetch(`/api/store-categories/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch category');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Create category
  const createCategory = useCallback(async (categoryData: StoreCategoryCreate): Promise<boolean> => {
    try {
      const response = await fetch('/api/store-categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(categoryData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create category');
      }
      
      const newCategory = await response.json();
      
      // Update local state
      setCategories(prev => [...prev, newCategory].sort((a, b) => a.sort_order - b.sort_order));
      
      toast.success('Kategori berhasil dibuat');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Update category
  const updateCategory = useCallback(async (id: string, updates: Partial<StoreCategoryCreate>): Promise<boolean> => {
    try {
      const response = await fetch(`/api/store-categories/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update category');
      }
      
      const updatedCategory = await response.json();
      
      // Update local state
      setCategories(prev => 
        prev.map(cat => cat.id === id ? updatedCategory : cat)
          .sort((a, b) => a.sort_order - b.sort_order)
      );
      
      toast.success('Kategori berhasil diperbarui');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Delete category
  const deleteCategory = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/store-categories/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete category');
      }
      
      // Update local state
      setCategories(prev => prev.filter(cat => cat.id !== id));
      
      toast.success('Kategori berhasil dihapus');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Toggle category status
  const toggleCategoryStatus = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/store-categories/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'toggle_status',
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to toggle category status');
      }
      
      const updatedCategory = await response.json();
      
      // Update local state
      setCategories(prev => 
        prev.map(cat => cat.id === id ? updatedCategory : cat)
      );
      
      toast.success(`Kategori ${updatedCategory.is_active ? 'diaktifkan' : 'dinonaktifkan'}`);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Reorder categories
  const reorderCategories = useCallback(async (categoryOrders: { id: string; sort_order: number }[]): Promise<boolean> => {
    try {
      const response = await fetch('/api/store-categories', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'reorder',
          data: categoryOrders,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to reorder categories');
      }
      
      // Update local state
      setCategories(prev => 
        prev.map(cat => {
          const newOrder = categoryOrders.find(order => order.id === cat.id);
          return newOrder ? { ...cat, sort_order: newOrder.sort_order } : cat;
        }).sort((a, b) => a.sort_order - b.sort_order)
      );
      
      toast.success('Urutan kategori berhasil diperbarui');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Refresh categories (re-fetch with current filters)
  const refreshCategories = useCallback(async () => {
    await fetchCategories();
  }, [fetchCategories]);

  // Initial fetch on mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    loading,
    error,
    fetchCategories,
    getCategory,
    createCategory,
    updateCategory,
    deleteCategory,
    toggleCategoryStatus,
    reorderCategories,
    refreshCategories,
  };
}
