"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import {
  AlertCircle,
  ArrowUpDown,
  Book,
  Clock,
  Code,
  Copy,
  Download,
  ExternalLink,
  FileJson,
  FileText,
  Filter,
  RefreshCw,
  Search,
  Settings,
  Users,
} from "lucide-react"
import { cn } from "@/lib/utils"

// Mock data for API endpoints
const apiEndpoints = [
  {
    id: "endpoint-1",
    name: "Products API",
    description: "Create, read, update, and delete products",
    version: "v1",
    status: "active",
    auth: ["api_key", "oauth"],
    rateLimits: {
      basic: 100,
      premium: 1000,
      enterprise: 10000,
    },
  },
  {
    id: "endpoint-2",
    name: "Orders API",
    description: "Manage orders and fulfillment",
    version: "v1",
    status: "active",
    auth: ["api_key", "oauth"],
    rateLimits: {
      basic: 50,
      premium: 500,
      enterprise: 5000,
    },
  },
  {
    id: "endpoint-3",
    name: "Customers API",
    description: "Manage customer data and profiles",
    version: "v1",
    status: "active",
    auth: ["api_key", "oauth"],
    rateLimits: {
      basic: 80,
      premium: 800,
      enterprise: 8000,
    },
  },
  {
    id: "endpoint-4",
    name: "Analytics API",
    description: "Access sales and performance data",
    version: "v1",
    status: "active",
    auth: ["api_key"],
    rateLimits: {
      basic: 20,
      premium: 200,
      enterprise: 2000,
    },
  },
  {
    id: "endpoint-5",
    name: "Inventory API",
    description: "Manage product inventory and stock levels",
    version: "v1",
    status: "active",
    auth: ["api_key", "oauth"],
    rateLimits: {
      basic: 60,
      premium: 600,
      enterprise: 6000,
    },
  },
  {
    id: "endpoint-6",
    name: "Webhooks API",
    description: "Configure and manage webhook subscriptions",
    version: "v1",
    status: "active",
    auth: ["api_key"],
    rateLimits: {
      basic: 30,
      premium: 300,
      enterprise: 3000,
    },
  },
  {
    id: "endpoint-7",
    name: "Payments API (Beta)",
    description: "Process payments and refunds",
    version: "v1-beta",
    status: "beta",
    auth: ["api_key", "oauth"],
    rateLimits: {
      basic: 40,
      premium: 400,
      enterprise: 4000,
    },
  },
  {
    id: "endpoint-8",
    name: "Legacy Catalog API",
    description: "Deprecated product catalog management",
    version: "v0",
    status: "deprecated",
    auth: ["api_key"],
    rateLimits: {
      basic: 10,
      premium: 100,
      enterprise: 1000,
    },
  },
]

// Mock data for API usage
const apiUsage = [
  {
    endpoint: "Products API",
    requests: 8750,
    limit: 10000,
    errorRate: 0.5,
    avgResponseTime: 120,
  },
  {
    endpoint: "Orders API",
    requests: 4200,
    limit: 5000,
    errorRate: 0.8,
    avgResponseTime: 150,
  },
  {
    endpoint: "Customers API",
    requests: 3600,
    limit: 8000,
    errorRate: 0.3,
    avgResponseTime: 90,
  },
  {
    endpoint: "Analytics API",
    requests: 1800,
    limit: 2000,
    errorRate: 1.2,
    avgResponseTime: 200,
  },
  {
    endpoint: "Inventory API",
    requests: 2500,
    limit: 6000,
    errorRate: 0.4,
    avgResponseTime: 110,
  },
]

// Mock data for API consumers
const apiConsumers = [
  {
    id: "consumer-1",
    name: "Mobile App",
    type: "application",
    requests: 12500,
    endpoints: ["Products API", "Orders API", "Customers API"],
    lastActive: "2023-05-15T10:30:00Z",
  },
  {
    id: "consumer-2",
    name: "Partner Integration",
    type: "service",
    requests: 8200,
    endpoints: ["Products API", "Inventory API"],
    lastActive: "2023-05-15T09:45:00Z",
  },
  {
    id: "consumer-3",
    name: "Admin Dashboard",
    type: "application",
    requests: 5600,
    endpoints: ["Products API", "Orders API", "Analytics API"],
    lastActive: "2023-05-15T11:20:00Z",
  },
  {
    id: "consumer-4",
    name: "Data Warehouse Sync",
    type: "service",
    requests: 3200,
    endpoints: ["Analytics API", "Customers API"],
    lastActive: "2023-05-15T08:15:00Z",
  },
  {
    id: "consumer-5",
    name: "Marketplace Integration",
    type: "service",
    requests: 4800,
    endpoints: ["Products API", "Orders API", "Inventory API"],
    lastActive: "2023-05-15T10:10:00Z",
  },
]

export function ApiManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [versionFilter, setVersionFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")

  // Filter API endpoints based on search query, version, and status
  const filteredEndpoints = apiEndpoints.filter((endpoint) => {
    const matchesSearch =
      endpoint.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      endpoint.description.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesVersion = versionFilter === "all" || endpoint.version === versionFilter

    const matchesStatus = statusFilter === "all" || endpoint.status === statusFilter

    return matchesSearch && matchesVersion && matchesStatus
  })

  // Get unique versions for filter
  const versions = ["all", ...new Set(apiEndpoints.map((e) => e.version))]

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      dateStyle: "medium",
      timeStyle: "short",
    }).format(date)
  }

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "beta":
        return <Badge variant="secondary">Beta</Badge>
      case "deprecated":
        return <Badge variant="destructive">Deprecated</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <Tabs defaultValue="endpoints" className="w-full">
      <TabsList className="grid w-full grid-cols-4 mb-6">
        <TabsTrigger value="endpoints">API Endpoints</TabsTrigger>
        <TabsTrigger value="usage">API Usage</TabsTrigger>
        <TabsTrigger value="consumers">API Consumers</TabsTrigger>
        <TabsTrigger value="documentation">Documentation</TabsTrigger>
      </TabsList>

      {/* API Endpoints Tab */}
      <TabsContent value="endpoints" className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="relative w-full md:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search API endpoints..."
              className="w-full pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex flex-wrap gap-2">
            <div>
              <Label htmlFor="version-filter" className="sr-only">
                Filter by Version
              </Label>
              <select
                id="version-filter"
                className="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                value={versionFilter}
                onChange={(e) => setVersionFilter(e.target.value)}
              >
                <option value="all">All Versions</option>
                {versions
                  .filter((v) => v !== "all")
                  .map((version) => (
                    <option key={version} value={version}>
                      {version}
                    </option>
                  ))}
              </select>
            </div>
            <div>
              <Label htmlFor="status-filter" className="sr-only">
                Filter by Status
              </Label>
              <select
                id="status-filter"
                className="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="active">Active</option>
                <option value="beta">Beta</option>
                <option value="deprecated">Deprecated</option>
              </select>
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                setSearchQuery("")
                setVersionFilter("all")
                setStatusFilter("all")
              }}
            >
              <RefreshCw className="h-4 w-4" />
              <span className="sr-only">Reset filters</span>
            </Button>
          </div>
        </div>

        <div className="rounded-md border">
          <div className="relative w-full overflow-auto">
            <table className="w-full caption-bottom text-sm">
              <thead>
                <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                  <th className="h-12 px-4 text-left align-middle font-medium">Name</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Description</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Version</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Authentication</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Rate Limits</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredEndpoints.map((endpoint) => (
                  <tr
                    key={endpoint.id}
                    className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                  >
                    <td className="p-4 align-middle font-medium">{endpoint.name}</td>
                    <td className="p-4 align-middle text-muted-foreground">{endpoint.description}</td>
                    <td className="p-4 align-middle">
                      <Badge variant="outline">{endpoint.version}</Badge>
                    </td>
                    <td className="p-4 align-middle">{getStatusBadge(endpoint.status)}</td>
                    <td className="p-4 align-middle">
                      <div className="flex flex-wrap gap-1">
                        {endpoint.auth.map((auth) => (
                          <Badge key={auth} variant="outline" className="text-xs">
                            {auth === "api_key" ? "API Key" : "OAuth"}
                          </Badge>
                        ))}
                      </div>
                    </td>
                    <td className="p-4 align-middle">
                      <div className="space-y-1 text-xs">
                        <div className="flex items-center justify-between">
                          <span>Basic:</span>
                          <span>{endpoint.rateLimits.basic}/hour</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Premium:</span>
                          <span>{endpoint.rateLimits.premium}/hour</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Enterprise:</span>
                          <span>{endpoint.rateLimits.enterprise}/hour</span>
                        </div>
                      </div>
                    </td>
                    <td className="p-4 align-middle">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Settings className="h-4 w-4" />
                          <span className="sr-only">Configure endpoint</span>
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Book className="h-4 w-4" />
                          <span className="sr-only">View documentation</span>
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </TabsContent>

      {/* API Usage Tab */}
      <TabsContent value="usage" className="space-y-6">
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Total API Requests</CardTitle>
              <CardDescription>Last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">
                {apiUsage.reduce((sum, item) => sum + item.requests, 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground mt-1">+12.5% from previous period</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Average Response Time</CardTitle>
              <CardDescription>Last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">
                {Math.round(apiUsage.reduce((sum, item) => sum + item.avgResponseTime, 0) / apiUsage.length)}ms
              </div>
              <p className="text-xs text-muted-foreground mt-1">-5.2% from previous period</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Error Rate</CardTitle>
              <CardDescription>Last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">
                {(apiUsage.reduce((sum, item) => sum + item.errorRate, 0) / apiUsage.length).toFixed(2)}%
              </div>
              <p className="text-xs text-muted-foreground mt-1">+0.3% from previous period</p>
            </CardContent>
          </Card>
        </div>

        <h3 className="text-lg font-semibold mt-6">API Usage by Endpoint</h3>
        <div className="rounded-md border">
          <div className="relative w-full overflow-auto">
            <table className="w-full caption-bottom text-sm">
              <thead>
                <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                  <th className="h-12 px-4 text-left align-middle font-medium">Endpoint</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Requests</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Usage</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Error Rate</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Avg. Response Time</th>
                </tr>
              </thead>
              <tbody>
                {apiUsage.map((usage) => (
                  <tr
                    key={usage.endpoint}
                    className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                  >
                    <td className="p-4 align-middle font-medium">{usage.endpoint}</td>
                    <td className="p-4 align-middle">{usage.requests.toLocaleString()}</td>
                    <td className="p-4 align-middle">
                      <div className="flex items-center gap-2">
                        <Progress value={(usage.requests / usage.limit) * 100} className="h-2 w-40" />
                        <span className="text-xs text-muted-foreground">
                          {Math.round((usage.requests / usage.limit) * 100)}%
                        </span>
                      </div>
                    </td>
                    <td className="p-4 align-middle">
                      <div className="flex items-center gap-2">
                        <span className={cn("text-xs", usage.errorRate > 1 ? "text-red-500" : "text-muted-foreground")}>
                          {usage.errorRate.toFixed(2)}%
                        </span>
                        {usage.errorRate > 1 && <AlertCircle className="h-4 w-4 text-red-500" />}
                      </div>
                    </td>
                    <td className="p-4 align-middle">
                      <div className="flex items-center gap-2">
                        <span
                          className={cn(
                            "text-xs",
                            usage.avgResponseTime > 150 ? "text-yellow-500" : "text-muted-foreground",
                          )}
                        >
                          {usage.avgResponseTime}ms
                        </span>
                        {usage.avgResponseTime > 150 && <Clock className="h-4 w-4 text-yellow-500" />}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </TabsContent>

      {/* API Consumers Tab */}
      <TabsContent value="consumers" className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">API Consumers</h3>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <ArrowUpDown className="h-4 w-4 mr-2" />
              Sort
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {apiConsumers.map((consumer) => (
            <Card key={consumer.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">{consumer.name}</CardTitle>
                  <Badge variant="outline">{consumer.type}</Badge>
                </div>
                <CardDescription>Last active: {formatDate(consumer.lastActive)}</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-3">
                  <div>
                    <h4 className="text-sm font-medium">API Usage</h4>
                    <p className="text-2xl font-bold mt-1">{consumer.requests.toLocaleString()}</p>
                    <p className="text-xs text-muted-foreground">requests in the last 30 days</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Endpoints Used</h4>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {consumer.endpoints.map((endpoint) => (
                        <Badge key={endpoint} variant="outline" className="text-xs">
                          {endpoint}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-between">
                <Button variant="outline" size="sm">
                  <Users className="h-4 w-4 mr-2" />
                  Access Control
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </TabsContent>

      {/* API Documentation Tab */}
      <TabsContent value="documentation" className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">API Documentation</h3>
          <div className="flex gap-2">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              View Full Documentation
            </Button>
            <Button>
              <Code className="h-4 w-4 mr-2" />
              API Explorer
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Getting Started</CardTitle>
              <CardDescription>Learn how to use the Sellzio API to build integrations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Authentication</h4>
                <p className="text-sm text-muted-foreground">
                  Learn how to authenticate with the API using API keys or OAuth 2.0
                </p>
                <Button variant="link" className="p-0 h-auto">
                  Read Authentication Guide
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </div>
              <Separator />
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Rate Limiting</h4>
                <p className="text-sm text-muted-foreground">
                  Understand rate limits and how to handle rate limit errors
                </p>
                <Button variant="link" className="p-0 h-auto">
                  Read Rate Limiting Guide
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </div>
              <Separator />
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Error Handling</h4>
                <p className="text-sm text-muted-foreground">Learn about error codes and how to handle API errors</p>
                <Button variant="link" className="p-0 h-auto">
                  Read Error Handling Guide
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>API Reference</CardTitle>
              <CardDescription>Detailed documentation for all API endpoints</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Products API</h4>
                <p className="text-sm text-muted-foreground">Create, read, update, and delete products</p>
                <Button variant="link" className="p-0 h-auto">
                  View Products API Reference
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </div>
              <Separator />
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Orders API</h4>
                <p className="text-sm text-muted-foreground">Manage orders and fulfillment</p>
                <Button variant="link" className="p-0 h-auto">
                  View Orders API Reference
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </div>
              <Separator />
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Customers API</h4>
                <p className="text-sm text-muted-foreground">Manage customer data and profiles</p>
                <Button variant="link" className="p-0 h-auto">
                  View Customers API Reference
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                <FileJson className="h-4 w-4 mr-2" />
                Download OpenAPI Specification
              </Button>
            </CardFooter>
          </Card>

          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Code Examples</CardTitle>
              <CardDescription>Sample code for common API operations in various languages</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="javascript" className="w-full">
                <TabsList className="w-full grid grid-cols-5">
                  <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                  <TabsTrigger value="python">Python</TabsTrigger>
                  <TabsTrigger value="php">PHP</TabsTrigger>
                  <TabsTrigger value="ruby">Ruby</TabsTrigger>
                  <TabsTrigger value="curl">cURL</TabsTrigger>
                </TabsList>
                <TabsContent value="javascript" className="mt-4">
                  <div className="rounded-md bg-muted p-4">
                    <pre className="text-sm">
                      <code>
                        {`// Fetch products using the Sellzio API
const fetchProducts = async () => {
  const response = await fetch('https://api.sellzio.com/v1/products', {
    headers: {
      'Authorization': 'Bearer YOUR_API_KEY',
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(\`API error: \${response.status}\`);
  }
  
  const data = await response.json();
  return data.products;
};`}
                      </code>
                    </pre>
                  </div>
                  <Button variant="outline" size="sm" className="mt-2">
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Code
                  </Button>
                </TabsContent>
                <TabsContent value="python" className="mt-4">
                  <div className="rounded-md bg-muted p-4">
                    <pre className="text-sm">
                      <code>
                        {`# Fetch products using the Sellzio API
import requests

def fetch_products():
    url = "https://api.sellzio.com/v1/products"
    headers = {
        "Authorization": "Bearer YOUR_API_KEY",
        "Content-Type": "application/json"
    }
    
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    
    data = response.json()
    return data["products"]`}
                      </code>
                    </pre>
                  </div>
                  <Button variant="outline" size="sm" className="mt-2">
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Code
                  </Button>
                </TabsContent>
                <TabsContent value="php" className="mt-4">
                  <div className="rounded-md bg-muted p-4">
                    <pre className="text-sm">
                      <code>
                        {`// Fetch products using the Sellzio API
<?php
function fetchProducts() {
    $url = "https://api.sellzio.com/v1/products";
    $headers = [
        "Authorization: Bearer YOUR_API_KEY",
        "Content-Type: application/json"
    ];
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    $response = curl_exec($ch);
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($statusCode !== 200) {
        throw new Exception("API error: " . $statusCode);
    }
    
    $data = json_decode($response, true);
    return $data["products"];
}
?>`}
                      </code>
                    </pre>
                  </div>
                  <Button variant="outline" size="sm" className="mt-2">
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Code
                  </Button>
                </TabsContent>
                <TabsContent value="ruby" className="mt-4">
                  <div className="rounded-md bg-muted p-4">
                    <pre className="text-sm">
                      <code>
                        {`# Fetch products using the Sellzio API
require 'net/http'
require 'json'

def fetch_products
  url = URI("https://api.sellzio.com/v1/products")
  http = Net::HTTP.new(url.host, url.port)
  http.use_ssl = true
  
  request = Net::HTTP::Get.new(url)
  request["Authorization"] = "Bearer YOUR_API_KEY"
  request["Content-Type"] = "application/json"
  
  response = http.request(request)
  
  if response.code.to_i != 200
    raise "API error: #{response.code}"
  end
  
  data = JSON.parse(response.body)
  data["products"]
end`}
                      </code>
                    </pre>
                  </div>
                  <Button variant="outline" size="sm" className="mt-2">
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Code
                  </Button>
                </TabsContent>
                <TabsContent value="curl" className="mt-4">
                  <div className="rounded-md bg-muted p-4">
                    <pre className="text-sm">
                      <code>
                        {`# Fetch products using the Sellzio API
curl -X GET "https://api.sellzio.com/v1/products" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"`}
                      </code>
                    </pre>
                  </div>
                  <Button variant="outline" size="sm" className="mt-2">
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Code
                  </Button>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
    </Tabs>
  )
}
