// Script ini akan dijalankan di browser untuk memuat komponen client
console.log("Memuat formulir pembuatan toko...")

// Fungsi untuk menampilkan formulir sederhana
function renderSimpleForm() {
  const container = document.getElementById("store-form-container")
  if (!container) return

  container.innerHTML = `
    <form class="mt-4 space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700"><PERSON><PERSON></label>
        <input type="text" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="Masukkan nama toko">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700">Des<PERSON>ripsi</label>
        <textarea class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" rows="3" placeholder="Deskripsi toko"></textarea>
      </div>
      
      <div>
        <button type="button" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          Simpan
        </button>
        <button type="button" class="ml-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" onclick="window.location.href='/admin/dashboard/stores'">
          Batal
        </button>
      </div>
    </form>
  `

  // Tambahkan event listener untuk tombol simpan
  const saveButton = container.querySelector("button")
  if (saveButton) {
    saveButton.addEventListener("click", () => {
      alert("Fitur penyimpanan belum diimplementasikan")
      window.location.href = "/admin/dashboard/stores"
    })
  }
}

// Jalankan setelah DOM selesai dimuat
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", renderSimpleForm)
} else {
  renderSimpleForm()
}
