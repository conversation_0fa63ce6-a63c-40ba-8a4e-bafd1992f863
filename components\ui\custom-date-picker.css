/* Custom styling for date input */
input[type="date"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: white;
  border: 1px solid hsl(var(--input));
  border-radius: var(--radius);
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  width: 100%;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input[type="date"]:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* Dark mode support */
.dark input[type="date"] {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-color: hsl(var(--input));
}

.dark input[type="date"]:focus {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.3);
}

/* Calendar icon styling */
input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(0.5);
  cursor: pointer;
  padding: 0.25rem;
  opacity: 0.6;
  transition: opacity 0.15s ease-in-out;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

.dark input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(0.8);
}

/* Placeholder styling */
input[type="date"]::placeholder {
  color: hsl(var(--muted-foreground));
}

/* Custom date input container */
.date-input-container {
  position: relative;
}

.date-input-container .calendar-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: hsl(var(--muted-foreground));
  pointer-events: none;
}

.date-input-container input[type="date"] {
  padding-left: 2.5rem;
}
