"use client"

import { Suspense } from "react"
import dynamic from "next/dynamic"

// Komponen loading sederhana
function StoresLoading() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Stores</h1>
      <p>Loading stores list...</p>
    </div>
  )
}

// Import client component dengan dynamic import
const StoresClient = dynamic(() => import("./stores-client"), {
  loading: () => <StoresLoading />,
  ssr: false, // Disable SSR untuk komponen client
})

export default function ClientWrapper() {
  return (
    <Suspense fallback={<StoresLoading />}>
      <StoresClient />
    </Suspense>
  )
}
