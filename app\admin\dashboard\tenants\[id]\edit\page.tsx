"use client"

import { <PERSON><PERSON>ell } from "@/components/ui/table"

import { TableBody } from "@/components/ui/table"

import { TableHead } from "@/components/ui/table"

import { TableRow } from "@/components/ui/table"

import { TableHeader } from "@/components/ui/table"

import { Table } from "@/components/ui/table"

import type React from "react"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Save, X, AlertTriangle } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"

// Dummy data for tenant details
const getTenantDetails = (id: string) => {
  return {
    id,
    name: "Acme Corporation",
    status: "active",
    plan: "Enterprise",
    createdAt: "2023-01-15",
    storeCount: 12,
    userCount: 45,
    revenue: "$5,240",
    logo: "/generic-company-logo.png",
    email: "<EMAIL>",
    phone: "+****************",
    address: "123 Business Ave, Suite 100, San Francisco, CA 94107",
    website: "https://acmecorp.example.com",
    description: "A leading provider of innovative solutions for businesses of all sizes.",
    businessType: "Technology",
    country: "United States",
    language: "English",
    subscription: {
      plan: "Enterprise",
      billingCycle: "Monthly",
      nextPayment: "2023-06-15",
      amount: "$499/month",
      status: "Active",
      paymentMethod: "Credit Card",
      billingEmail: "<EMAIL>",
    },
    domain: {
      custom: "acmecorp.example.com",
      subdomain: "acme",
      verified: true,
      ssl: true,
    },
    features: {
      multipleStores: true,
      customDomain: true,
      apiAccess: true,
      whiteLabel: false,
      advancedAnalytics: true,
      prioritySupport: true,
    },
    limits: {
      productCount: 1000,
      storageGB: 50,
      bandwidth: "Unlimited",
      userCount: 50,
    },
    branding: {
      primaryColor: "#3B82F6",
      secondaryColor: "#1E40AF",
      fontPrimary: "Inter",
      fontSecondary: "Roboto",
      theme: "Light",
    },
  }
}

export default function EditTenantPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const tenant = getTenantDetails(params.id)
  const [isLoading, setIsLoading] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    name: tenant.name,
    description: tenant.description,
    email: tenant.email,
    phone: tenant.phone,
    address: tenant.address,
    website: tenant.website,
    businessType: tenant.businessType,
    country: tenant.country,
    language: tenant.language,
    plan: tenant.subscription.plan,
    billingCycle: tenant.subscription.billingCycle,
    status: tenant.status,
    paymentMethod: tenant.subscription.paymentMethod,
    billingEmail: tenant.subscription.billingEmail,
    customDomain: tenant.domain.custom,
    subdomain: tenant.domain.subdomain,
    features: tenant.features,
    limits: tenant.limits,
    branding: tenant.branding,
  })

  // Handle form changes
  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
    setHasUnsavedChanges(true)
  }

  // Handle nested object changes
  const handleNestedChange = (parent: string, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof typeof prev],
        [field]: value,
      },
    }))
    setHasUnsavedChanges(true)
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Show success message and redirect
      alert("Tenant updated successfully!")
      router.push(`/admin/dashboard/tenants/${params.id}`)
    } catch (error) {
      console.error("Error updating tenant:", error)
      alert("Failed to update tenant. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href={`/admin/dashboard/tenants/${params.id}`}>
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back</span>
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Edit Tenant</h1>
            <p className="text-muted-foreground">Update tenant information and settings</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {hasUnsavedChanges && (
            <div className="flex items-center text-amber-600 text-sm mr-2">
              <AlertTriangle className="h-4 w-4 mr-1" />
              Unsaved changes
            </div>
          )}
          <Button variant="outline" asChild>
            <Link href={`/admin/dashboard/tenants/${params.id}`}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Link>
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>

      {/* Edit Form with Tabs */}
      <form onSubmit={handleSubmit}>
        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid grid-cols-5 w-full">
            <TabsTrigger value="basic">Basic Information</TabsTrigger>
            <TabsTrigger value="subscription">Subscription</TabsTrigger>
            <TabsTrigger value="domain">Domain Settings</TabsTrigger>
            <TabsTrigger value="features">Features & Permissions</TabsTrigger>
            <TabsTrigger value="branding">Branding</TabsTrigger>
          </TabsList>

          {/* Basic Information Tab */}
          <TabsContent value="basic">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="name">Tenant Name</Label>
                      <Input id="name" value={formData.name} onChange={(e) => handleChange("name", e.target.value)} />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleChange("email", e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleChange("phone", e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        value={formData.website}
                        onChange={(e) => handleChange("website", e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleChange("address", e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      rows={4}
                      value={formData.description}
                      onChange={(e) => handleChange("description", e.target.value)}
                    />
                  </div>

                  <Separator />

                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <Label htmlFor="businessType">Business Type</Label>
                      <Select
                        value={formData.businessType}
                        onValueChange={(value) => handleChange("businessType", value)}
                      >
                        <SelectTrigger id="businessType">
                          <SelectValue placeholder="Select business type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Technology">Technology</SelectItem>
                          <SelectItem value="Retail">Retail</SelectItem>
                          <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                          <SelectItem value="Healthcare">Healthcare</SelectItem>
                          <SelectItem value="Education">Education</SelectItem>
                          <SelectItem value="Finance">Finance</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Select value={formData.country} onValueChange={(value) => handleChange("country", value)}>
                        <SelectTrigger id="country">
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="United States">United States</SelectItem>
                          <SelectItem value="Canada">Canada</SelectItem>
                          <SelectItem value="United Kingdom">United Kingdom</SelectItem>
                          <SelectItem value="Australia">Australia</SelectItem>
                          <SelectItem value="Germany">Germany</SelectItem>
                          <SelectItem value="France">France</SelectItem>
                          <SelectItem value="Japan">Japan</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="language">Primary Language</Label>
                      <Select value={formData.language} onValueChange={(value) => handleChange("language", value)}>
                        <SelectTrigger id="language">
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="English">English</SelectItem>
                          <SelectItem value="Spanish">Spanish</SelectItem>
                          <SelectItem value="French">French</SelectItem>
                          <SelectItem value="German">German</SelectItem>
                          <SelectItem value="Japanese">Japanese</SelectItem>
                          <SelectItem value="Chinese">Chinese</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="logo">Logo</Label>
                    <div className="flex items-center gap-4">
                      <div className="h-16 w-16 rounded border flex items-center justify-center overflow-hidden">
                        <img
                          src={tenant.logo || "/placeholder.svg"}
                          alt="Tenant logo"
                          className="h-full w-full object-contain"
                        />
                      </div>
                      <Button variant="outline" type="button">
                        Upload New Logo
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">Recommended size: 512x512px. Max file size: 2MB.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Subscription Tab */}
          <TabsContent value="subscription">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="plan">Subscription Plan</Label>
                      <Select value={formData.plan} onValueChange={(value) => handleChange("plan", value)}>
                        <SelectTrigger id="plan">
                          <SelectValue placeholder="Select plan" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Basic">Basic ($99/month)</SelectItem>
                          <SelectItem value="Professional">Professional ($299/month)</SelectItem>
                          <SelectItem value="Enterprise">Enterprise ($499/month)</SelectItem>
                          <SelectItem value="Custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="billingCycle">Billing Cycle</Label>
                      <Select
                        value={formData.billingCycle}
                        onValueChange={(value) => handleChange("billingCycle", value)}
                      >
                        <SelectTrigger id="billingCycle">
                          <SelectValue placeholder="Select billing cycle" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Monthly">Monthly</SelectItem>
                          <SelectItem value="Quarterly">Quarterly (10% discount)</SelectItem>
                          <SelectItem value="Annual">Annual (20% discount)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="status">Subscription Status</Label>
                      <Select value={formData.status} onValueChange={(value) => handleChange("status", value)}>
                        <SelectTrigger id="status">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="paymentMethod">Payment Method</Label>
                      <Select
                        value={formData.paymentMethod}
                        onValueChange={(value) => handleChange("paymentMethod", value)}
                      >
                        <SelectTrigger id="paymentMethod">
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Credit Card">Credit Card</SelectItem>
                          <SelectItem value="Bank Transfer">Bank Transfer</SelectItem>
                          <SelectItem value="PayPal">PayPal</SelectItem>
                          <SelectItem value="Invoice">Invoice</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="billingEmail">Billing Email</Label>
                    <Input
                      id="billingEmail"
                      type="email"
                      value={formData.billingEmail}
                      onChange={(e) => handleChange("billingEmail", e.target.value)}
                    />
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <Label htmlFor="promoCode">Promo Code</Label>
                    <div className="flex gap-2">
                      <Input id="promoCode" placeholder="Enter promo code" />
                      <Button type="button" variant="outline">
                        Apply
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Special Rate</Label>
                    <div className="flex items-center gap-2">
                      <Switch id="specialRate" />
                      <Label htmlFor="specialRate" className="font-normal">
                        Apply special rate to this tenant
                      </Label>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Special rates override standard pricing and are applied manually.
                    </p>
                  </div>

                  <div className="rounded-md border p-4 bg-muted/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Current Plan: {formData.plan}</h3>
                        <p className="text-sm text-muted-foreground">
                          Next billing date: {tenant.subscription.nextPayment}
                        </p>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        Active
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Domain Settings Tab */}
          <TabsContent value="domain">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="customDomain">Custom Domain</Label>
                    <div className="flex gap-2">
                      <Input
                        id="customDomain"
                        value={formData.customDomain}
                        onChange={(e) => handleChange("customDomain", e.target.value)}
                      />
                      <Button type="button" variant="outline">
                        Verify
                      </Button>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        Verified
                      </Badge>
                      <p className="text-xs text-muted-foreground">Last verified: 2 days ago</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subdomain">Subdomain</Label>
                    <div className="flex items-center gap-0">
                      <Input
                        id="subdomain"
                        value={formData.subdomain}
                        onChange={(e) => handleChange("subdomain", e.target.value)}
                        className="rounded-r-none"
                      />
                      <div className="bg-muted px-3 py-2 border border-l-0 rounded-r-md text-muted-foreground">
                        .sellzio.com
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>SSL Certificate</Label>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        Active
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      SSL certificate is automatically provisioned for all verified domains.
                    </p>
                  </div>

                  <div className="rounded-md border p-4">
                    <h3 className="font-medium mb-2">DNS Configuration Instructions</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      To verify your domain, add the following DNS records:
                    </p>

                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-2 text-sm font-medium">
                        <div>Type</div>
                        <div>Name</div>
                        <div>Value</div>
                      </div>
                      <Separator />
                      <div className="grid grid-cols-3 gap-2 text-sm">
                        <div>CNAME</div>
                        <div>www</div>
                        <div>tenant.sellzio.com</div>
                      </div>
                      <div className="grid grid-cols-3 gap-2 text-sm">
                        <div>TXT</div>
                        <div>_sellzio-verification</div>
                        <div>sellzio-verify=abc123def456</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Features & Permissions Tab */}
          <TabsContent value="features">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="font-medium">Platform Features</h3>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="flex items-center justify-between space-x-2 border p-4 rounded-md">
                        <div>
                          <Label htmlFor="multipleStores" className="font-medium">
                            Multiple Stores
                          </Label>
                          <p className="text-sm text-muted-foreground">Allow tenant to create multiple stores</p>
                        </div>
                        <Switch
                          id="multipleStores"
                          checked={formData.features.multipleStores}
                          onCheckedChange={(checked) => handleNestedChange("features", "multipleStores", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-x-2 border p-4 rounded-md">
                        <div>
                          <Label htmlFor="customDomain" className="font-medium">
                            Custom Domain
                          </Label>
                          <p className="text-sm text-muted-foreground">Allow tenant to use custom domains</p>
                        </div>
                        <Switch
                          id="customDomain"
                          checked={formData.features.customDomain}
                          onCheckedChange={(checked) => handleNestedChange("features", "customDomain", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-x-2 border p-4 rounded-md">
                        <div>
                          <Label htmlFor="apiAccess" className="font-medium">
                            API Access
                          </Label>
                          <p className="text-sm text-muted-foreground">Allow tenant to use API endpoints</p>
                        </div>
                        <Switch
                          id="apiAccess"
                          checked={formData.features.apiAccess}
                          onCheckedChange={(checked) => handleNestedChange("features", "apiAccess", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-x-2 border p-4 rounded-md">
                        <div>
                          <Label htmlFor="whiteLabel" className="font-medium">
                            White Label
                          </Label>
                          <p className="text-sm text-muted-foreground">Remove Sellzio branding from tenant stores</p>
                        </div>
                        <Switch
                          id="whiteLabel"
                          checked={formData.features.whiteLabel}
                          onCheckedChange={(checked) => handleNestedChange("features", "whiteLabel", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-x-2 border p-4 rounded-md">
                        <div>
                          <Label htmlFor="advancedAnalytics" className="font-medium">
                            Advanced Analytics
                          </Label>
                          <p className="text-sm text-muted-foreground">Provide advanced analytics dashboard</p>
                        </div>
                        <Switch
                          id="advancedAnalytics"
                          checked={formData.features.advancedAnalytics}
                          onCheckedChange={(checked) => handleNestedChange("features", "advancedAnalytics", checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-x-2 border p-4 rounded-md">
                        <div>
                          <Label htmlFor="prioritySupport" className="font-medium">
                            Priority Support
                          </Label>
                          <p className="text-sm text-muted-foreground">Provide priority customer support</p>
                        </div>
                        <Switch
                          id="prioritySupport"
                          checked={formData.features.prioritySupport}
                          onCheckedChange={(checked) => handleNestedChange("features", "prioritySupport", checked)}
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h3 className="font-medium">Resource Limits</h3>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="productCount">Product Count Limit</Label>
                        <Select
                          value={formData.limits.productCount.toString()}
                          onValueChange={(value) =>
                            handleNestedChange("limits", "productCount", Number.parseInt(value))
                          }
                        >
                          <SelectTrigger id="productCount">
                            <SelectValue placeholder="Select limit" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="100">100 products</SelectItem>
                            <SelectItem value="500">500 products</SelectItem>
                            <SelectItem value="1000">1,000 products</SelectItem>
                            <SelectItem value="5000">5,000 products</SelectItem>
                            <SelectItem value="10000">10,000 products</SelectItem>
                            <SelectItem value="unlimited">Unlimited</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="storageGB">Storage Limit (GB)</Label>
                        <Select
                          value={formData.limits.storageGB.toString()}
                          onValueChange={(value) => handleNestedChange("limits", "storageGB", Number.parseInt(value))}
                        >
                          <SelectTrigger id="storageGB">
                            <SelectValue placeholder="Select limit" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="10">10 GB</SelectItem>
                            <SelectItem value="25">25 GB</SelectItem>
                            <SelectItem value="50">50 GB</SelectItem>
                            <SelectItem value="100">100 GB</SelectItem>
                            <SelectItem value="250">250 GB</SelectItem>
                            <SelectItem value="500">500 GB</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bandwidth">Bandwidth</Label>
                        <Select
                          value={formData.limits.bandwidth}
                          onValueChange={(value) => handleNestedChange("limits", "bandwidth", value)}
                        >
                          <SelectTrigger id="bandwidth">
                            <SelectValue placeholder="Select limit" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="100GB">100 GB/month</SelectItem>
                            <SelectItem value="500GB">500 GB/month</SelectItem>
                            <SelectItem value="1TB">1 TB/month</SelectItem>
                            <SelectItem value="5TB">5 TB/month</SelectItem>
                            <SelectItem value="Unlimited">Unlimited</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="userCount">User Count Limit</Label>
                        <Select
                          value={formData.limits.userCount.toString()}
                          onValueChange={(value) => handleNestedChange("limits", "userCount", Number.parseInt(value))}
                        >
                          <SelectTrigger id="userCount">
                            <SelectValue placeholder="Select limit" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="5">5 users</SelectItem>
                            <SelectItem value="10">10 users</SelectItem>
                            <SelectItem value="25">25 users</SelectItem>
                            <SelectItem value="50">50 users</SelectItem>
                            <SelectItem value="100">100 users</SelectItem>
                            <SelectItem value="unlimited">Unlimited</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h3 className="font-medium">Permission Matrix</h3>
                    <p className="text-sm text-muted-foreground">Define default permissions for tenant user roles</p>

                    <div className="border rounded-md overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-[200px]">Permission</TableHead>
                            <TableHead>Admin</TableHead>
                            <TableHead>Manager</TableHead>
                            <TableHead>Staff</TableHead>
                            <TableHead>Viewer</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell className="font-medium">Manage Stores</TableCell>
                            <TableCell>
                              <Checkbox defaultChecked disabled />
                            </TableCell>
                            <TableCell>
                              <Checkbox defaultChecked />
                            </TableCell>
                            <TableCell>
                              <Checkbox />
                            </TableCell>
                            <TableCell>
                              <Checkbox disabled />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Manage Products</TableCell>
                            <TableCell>
                              <Checkbox defaultChecked disabled />
                            </TableCell>
                            <TableCell>
                              <Checkbox defaultChecked />
                            </TableCell>
                            <TableCell>
                              <Checkbox defaultChecked />
                            </TableCell>
                            <TableCell>
                              <Checkbox disabled />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Manage Orders</TableCell>
                            <TableCell>
                              <Checkbox defaultChecked disabled />
                            </TableCell>
                            <TableCell>
                              <Checkbox defaultChecked />
                            </TableCell>
                            <TableCell>
                              <Checkbox defaultChecked />
                            </TableCell>
                            <TableCell>
                              <Checkbox disabled />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Manage Users</TableCell>
                            <TableCell>
                              <Checkbox defaultChecked disabled />
                            </TableCell>
                            <TableCell>
                              <Checkbox defaultChecked />
                            </TableCell>
                            <TableCell>
                              <Checkbox disabled />
                            </TableCell>
                            <TableCell>
                              <Checkbox disabled />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">View Reports</TableCell>
                            <TableCell>
                              <Checkbox defaultChecked disabled />
                            </TableCell>
                            <TableCell>
                              <Checkbox defaultChecked />
                            </TableCell>
                            <TableCell>
                              <Checkbox defaultChecked />
                            </TableCell>
                            <TableCell>
                              <Checkbox defaultChecked />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Manage Settings</TableCell>
                            <TableCell>
                              <Checkbox defaultChecked disabled />
                            </TableCell>
                            <TableCell>
                              <Checkbox />
                            </TableCell>
                            <TableCell>
                              <Checkbox disabled />
                            </TableCell>
                            <TableCell>
                              <Checkbox disabled />
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Branding Tab */}
          <TabsContent value="branding">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="primaryColor">Primary Color</Label>
                      <div className="flex gap-2">
                        <div
                          className="h-10 w-10 rounded-md border"
                          style={{ backgroundColor: formData.branding.primaryColor }}
                        />
                        <Input
                          id="primaryColor"
                          value={formData.branding.primaryColor}
                          onChange={(e) => handleNestedChange("branding", "primaryColor", e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="secondaryColor">Secondary Color</Label>
                      <div className="flex gap-2">
                        <div
                          className="h-10 w-10 rounded-md border"
                          style={{ backgroundColor: formData.branding.secondaryColor }}
                        />
                        <Input
                          id="secondaryColor"
                          value={formData.branding.secondaryColor}
                          onChange={(e) => handleNestedChange("branding", "secondaryColor", e.target.value)}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="fontPrimary">Primary Font</Label>
                      <Select
                        value={formData.branding.fontPrimary}
                        onValueChange={(value) => handleNestedChange("branding", "fontPrimary", value)}
                      >
                        <SelectTrigger id="fontPrimary">
                          <SelectValue placeholder="Select font" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Inter">Inter</SelectItem>
                          <SelectItem value="Roboto">Roboto</SelectItem>
                          <SelectItem value="Open Sans">Open Sans</SelectItem>
                          <SelectItem value="Lato">Lato</SelectItem>
                          <SelectItem value="Montserrat">Montserrat</SelectItem>
                          <SelectItem value="Poppins">Poppins</SelectItem>
                          <SelectItem value="Source Sans Pro">Source Sans Pro</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="fontSecondary">Secondary Font</Label>
                      <Select
                        value={formData.branding.fontSecondary}
                        onValueChange={(value) => handleNestedChange("branding", "fontSecondary", value)}
                      >
                        <SelectTrigger id="fontSecondary">
                          <SelectValue placeholder="Select font" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Roboto">Roboto</SelectItem>
                          <SelectItem value="Inter">Inter</SelectItem>
                          <SelectItem value="Open Sans">Open Sans</SelectItem>
                          <SelectItem value="Lato">Lato</SelectItem>
                          <SelectItem value="Montserrat">Montserrat</SelectItem>
                          <SelectItem value="Poppins">Poppins</SelectItem>
                          <SelectItem value="Source Sans Pro">Source Sans Pro</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Theme</Label>
                    <RadioGroup
                      defaultValue={formData.branding.theme}
                      onValueChange={(value) => handleNestedChange("branding", "theme", value)}
                      className="flex gap-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Light" id="theme-light" />
                        <Label htmlFor="theme-light">Light</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Dark" id="theme-dark" />
                        <Label htmlFor="theme-dark">Dark</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Auto" id="theme-auto" />
                        <Label htmlFor="theme-auto">Auto (follow system)</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h3 className="font-medium">Email Template Customization</h3>
                    <p className="text-sm text-muted-foreground">
                      Customize the appearance of email templates sent to customers
                    </p>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="border rounded-md p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">Order Confirmation</h4>
                          <Button variant="outline" size="sm">
                            Customize
                          </Button>
                        </div>
                        <div className="h-40 bg-muted/20 rounded-md flex items-center justify-center">
                          <p className="text-sm text-muted-foreground">Email template preview</p>
                        </div>
                      </div>

                      <div className="border rounded-md p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">Shipping Notification</h4>
                          <Button variant="outline" size="sm">
                            Customize
                          </Button>
                        </div>
                        <div className="h-40 bg-muted/20 rounded-md flex items-center justify-center">
                          <p className="text-sm text-muted-foreground">Email template preview</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="whiteLabel">White Label</Label>
                      <Switch
                        id="whiteLabel"
                        checked={formData.features.whiteLabel}
                        onCheckedChange={(checked) => handleNestedChange("features", "whiteLabel", checked)}
                      />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Remove all Sellzio branding from the tenant's storefront and emails. Only available on Enterprise
                      plans.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </form>
    </div>
  )
}
