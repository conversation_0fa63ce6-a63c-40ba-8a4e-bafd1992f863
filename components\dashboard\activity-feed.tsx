import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"

interface ActivityItem {
  id: string
  user: {
    name: string
    email: string
  }
  action: string
  target: string
  date: string
  status: "success" | "error" | "warning" | "info"
}

interface ActivityFeedProps {
  title: string
  description?: string
  items: ActivityItem[]
}

export function ActivityFeed({ title, description, items }: ActivityFeedProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {items.map((item) => (
            <div key={item.id} className="flex items-start gap-4">
              <div
                className={`mt-0.5 h-2 w-2 rounded-full ${
                  item.status === "success"
                    ? "bg-green-500"
                    : item.status === "error"
                      ? "bg-red-500"
                      : item.status === "warning"
                        ? "bg-yellow-500"
                        : "bg-blue-500"
                }`}
              />
              <div className="flex-1 space-y-1">
                <p className="text-sm font-medium leading-none">
                  <span className="font-semibold">{item.user.name}</span> {item.action}{" "}
                  <span className="font-semibold">{item.target}</span>
                </p>
                <p className="text-xs text-muted-foreground">{item.date}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
