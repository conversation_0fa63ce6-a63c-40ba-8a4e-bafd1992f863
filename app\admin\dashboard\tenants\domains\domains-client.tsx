"use client"

import { DomainManagementList } from "@/components/admin/tenants/domain-management-list"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PageHeader } from "@/components/admin/ui/page-header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function DomainsClient() {
  return (
    <div className="flex flex-col gap-6">
      <PageHeader
        title="Domain Management"
        description="Manage tenant domains and SSL certificates"
        breadcrumbs={[
          { title: "Dashboard", href: "/admin/dashboard" },
          { title: "Tenants", href: "/admin/dashboard/tenants" },
          { title: "Domains", href: "/admin/dashboard/tenants/domains" },
        ]}
        actions={
          <Link href="/admin/dashboard/tenants">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Tenants
            </Button>
          </Link>
        }
      />

      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Total Domains</CardTitle>
            <CardDescription>All registered domains</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">312</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Verified</CardTitle>
            <CardDescription>Verified domains</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">287</div>
            <p className="text-xs text-muted-foreground">92% of total domains</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Pending</CardTitle>
            <CardDescription>Pending verification</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">18</div>
            <p className="text-xs text-muted-foreground">6% of total domains</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>SSL Expiring</CardTitle>
            <CardDescription>Within 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">7</div>
            <p className="text-xs text-muted-foreground">2% of total domains</p>
          </CardContent>
        </Card>
      </div>

      <DomainManagementList />
    </div>
  )
}
