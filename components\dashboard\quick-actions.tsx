"use client"

import Link from "next/link"
import type { LucideIcon } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface QuickAction {
  name: string
  icon: LucideIcon
  href?: string
  onClick?: () => void
}

interface QuickActionsProps {
  title: string
  description?: string
  actions: QuickAction[]
  className?: string
}

export function QuickActions({ title, description, actions, className }: QuickActionsProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-2 lg:grid-cols-3">
          {actions.map((action) => {
            const commonClasses = "flex flex-col items-center justify-center rounded-md border border-border/50 bg-card p-4 text-center transition-colors hover:bg-accent/50";
            
            // Render sebagai button jika memiliki onClick
            if (action.onClick) {
              return (
                <button
                  key={action.name}
                  type="button"
                  onClick={action.onClick}
                  className={commonClasses}
                >
                  <action.icon className="mb-2 h-6 w-6 text-primary" />
                  <span className="text-sm font-medium">{action.name}</span>
                </button>
              );
            }
            
            // Render sebagai Link jika memiliki href
            return (
              <Link
                key={action.name}
                href={action.href || "#"}
                className={commonClasses}
              >
                <action.icon className="mb-2 h-6 w-6 text-primary" />
                <span className="text-sm font-medium">{action.name}</span>
              </Link>
            );
          })}
        </div>
      </CardContent>
    </Card>
  )
}
