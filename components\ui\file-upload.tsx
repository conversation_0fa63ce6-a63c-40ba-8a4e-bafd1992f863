"use client"

import type React from "react"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { uploadImage } from "@/lib/storage"
import { Loader2, Upload, X } from "lucide-react"

interface FileUploadProps {
  label: string
  folder?: string
  value?: string
  onChange: (url: string) => void
  onError?: (error: Error) => void
}

export function FileUpload({ label, folder = "misc", value, onChange, onError }: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [preview, setPreview] = useState<string | null>(value || null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validasi tipe file (hanya gambar)
    if (!file.type.startsWith("image/")) {
      onError?.(new Error("Hanya file gambar yang diperbolehkan"))
      return
    }

    // Validasi ukuran file (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      onError?.(new Error("Ukuran file maksimal 5MB"))
      return
    }

    // Buat preview
    const objectUrl = URL.createObjectURL(file)
    setPreview(objectUrl)

    // Upload ke Supabase
    setIsUploading(true)
    try {
      const url = await uploadImage(file, folder)
      onChange(url)
    } catch (error) {
      console.error("Error uploading file:", error)
      onError?.(error instanceof Error ? error : new Error("Gagal mengupload file"))
      // Hapus preview jika gagal
      setPreview(null)
    } finally {
      setIsUploading(false)
      // Revoke object URL untuk menghindari memory leak
      URL.revokeObjectURL(objectUrl)
    }
  }

  const handleRemove = () => {
    setPreview(null)
    onChange("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className="space-y-2">
      <Label htmlFor={`file-${label}`}>{label}</Label>
      <div className="space-y-2">
        {preview ? (
          <div className="relative">
            <img src={preview || "/placeholder.svg"} alt={label} className="h-40 w-full object-cover rounded-md" />
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2 h-8 w-8"
              onClick={handleRemove}
              disabled={isUploading}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div className="flex items-center justify-center h-40 bg-muted rounded-md border-2 border-dashed border-muted-foreground/25">
            {isUploading ? (
              <div className="flex flex-col items-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Mengupload...</span>
              </div>
            ) : (
              <div className="flex flex-col items-center space-y-2">
                <Upload className="h-8 w-8 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Klik untuk upload {label}</span>
              </div>
            )}
          </div>
        )}
        <Input
          id={`file-${label}`}
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/*"
          disabled={isUploading}
          className="hidden"
        />
        {!preview && (
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="w-full"
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Mengupload...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload {label}
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  )
}
