"use client"

import { Suspense } from "react"
import dynamic from "next/dynamic"
import { ErrorBoundary } from "react-error-boundary"
import TenantCreateLoading from "./loading"

// Import client component with SSR disabled
const TenantCreateClient = dynamic(() => import("./client"), {
  loading: () => <TenantCreateLoading />,
  ssr: false,
})

// Fallback component for error boundary
function TenantCreateFallback() {
  return (
    <div className="flex h-[50vh] w-full flex-col items-center justify-center rounded-md border border-dashed p-8 text-center animate-in fade-in-50">
      <div className="flex h-20 w-20 items-center justify-center rounded-full bg-red-100">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="h-10 w-10 text-red-600"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"
          />
        </svg>
      </div>
      <h2 className="mt-6 text-xl font-semibold">Something went wrong</h2>
      <p className="mt-2 text-sm text-gray-500">
        An error occurred while loading the tenant creation form. Please try refreshing the page.
      </p>
      <button
        onClick={() => window.location.reload()}
        className="mt-6 rounded-md bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90"
      >
        Refresh Page
      </button>
    </div>
  )
}

export default function TenantCreateClientWrapper() {
  return (
    <ErrorBoundary FallbackComponent={TenantCreateFallback}>
      <Suspense fallback={<TenantCreateLoading />}>
        <TenantCreateClient />
      </Suspense>
    </ErrorBoundary>
  )
}
