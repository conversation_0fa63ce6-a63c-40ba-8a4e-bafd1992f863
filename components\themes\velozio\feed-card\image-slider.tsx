"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"

interface ImageSliderProps {
  images: { src: string; alt: string }[]
  onClick?: () => void
}

export const ImageSlider = ({ images = [], onClick }: ImageSliderProps) => {
  // Ensure we have valid images and have at least 5 images (duplicate if needed)
  const validImages = images && images.length > 0 ? [...images] : [{ src: "/placeholder.svg", alt: "Placeholder" }]

  // If we have less than 5 images, duplicate the existing ones to reach 5
  if (validImages.length < 5) {
    const originalLength = validImages.length
    for (let i = 0; i < 5 - originalLength; i++) {
      validImages.push(validImages[i % originalLength])
    }
  }

  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLoaded, setIsLoaded] = useState<boolean[]>(Array(validImages.length).fill(false))
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [autoplayPaused, setAutoplayPaused] = useState(false)
  const [manualInteraction, setManualInteraction] = useState(false)

  const autoplayTimerRef = useRef<NodeJS.Timeout | null>(null)
  const resumeAutoplayTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50

  // Setup autoplay
  useEffect(() => {
    if (validImages.length <= 1 || autoplayPaused) return

    const startAutoplay = () => {
      if (autoplayTimerRef.current) {
        clearInterval(autoplayTimerRef.current)
      }

      autoplayTimerRef.current = setInterval(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % validImages.length)
      }, 3000)
    }

    startAutoplay()

    return () => {
      if (autoplayTimerRef.current) {
        clearInterval(autoplayTimerRef.current)
      }
    }
  }, [validImages.length, autoplayPaused])

  // Resume autoplay after manual interaction
  useEffect(() => {
    if (!manualInteraction) return

    // Clear any existing timer
    if (resumeAutoplayTimerRef.current) {
      clearTimeout(resumeAutoplayTimerRef.current)
    }

    // Set a new timer to resume autoplay after 5 seconds of inactivity
    resumeAutoplayTimerRef.current = setTimeout(() => {
      setManualInteraction(false)
      setAutoplayPaused(false)
    }, 5000)

    return () => {
      if (resumeAutoplayTimerRef.current) {
        clearTimeout(resumeAutoplayTimerRef.current)
      }
    }
  }, [manualInteraction])

  // Handle image load
  const handleImageLoad = (index: number) => {
    setIsLoaded((prev) => {
      const newState = [...prev]
      newState[index] = true
      return newState
    })
  }

  // Dot indicator click handler
  const handleDotClick = (index: number) => {
    setCurrentIndex(index)
    setManualInteraction(true)
    setAutoplayPaused(true)
  }

  // Safe event handlers that don't use destructuring
  const onTouchStart = (event: React.TouchEvent) => {
    if (event && event.touches && event.touches.length > 0) {
      setTouchStart(event.touches[0].clientX)
      setManualInteraction(true)
      setAutoplayPaused(true)
      setIsDragging(true)
    }
  }

  const onTouchMove = (event: React.TouchEvent) => {
    if (!isDragging || !event || !event.touches || event.touches.length === 0) return
    setTouchEnd(event.touches[0].clientX)
  }

  const onTouchEnd = () => {
    setIsDragging(false)

    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isLeftSwipe) {
      // Next slide
      setCurrentIndex((prev) => (prev + 1) % validImages.length)
    } else if (isRightSwipe) {
      // Previous slide
      setCurrentIndex((prev) => (prev - 1 + validImages.length) % validImages.length)
    }

    // Reset
    setTouchStart(null)
    setTouchEnd(null)
  }

  const onMouseDown = (event: React.MouseEvent) => {
    if (event) {
      setTouchStart(event.clientX)
      setManualInteraction(true)
      setAutoplayPaused(true)
      setIsDragging(true)
    }
  }

  const onMouseMove = (event: React.MouseEvent) => {
    if (!isDragging || !event) return
    setTouchEnd(event.clientX)
  }

  const onMouseUp = () => {
    onTouchEnd()
  }

  const onMouseLeave = () => {
    if (isDragging) {
      onTouchEnd()
    }
  }

  const handleDotButtonClick = (event: React.MouseEvent, index: number) => {
    if (event) {
      event.stopPropagation()
      handleDotClick(index)
    }
  }

  return (
    <div
      className="relative pt-[177.78%] overflow-hidden bg-white shadow-sm transition-all duration-300 hover:shadow-md group"
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
      onMouseDown={onMouseDown}
      onMouseMove={onMouseMove}
      onMouseUp={onMouseUp}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
    >
      {/* Images */}
      {validImages.map((image, index) => (
        <div
          key={index}
          className={`absolute top-0 left-0 w-full h-full transition-all duration-300 ${
            currentIndex === index
              ? "opacity-100 z-10 translate-x-0"
              : index === (currentIndex + 1) % validImages.length
                ? "opacity-0 z-0 translate-x-full"
                : index === (currentIndex - 1 + validImages.length) % validImages.length
                  ? "opacity-0 z-0 -translate-x-full"
                  : "opacity-0 z-0"
          }`}
          aria-hidden={currentIndex !== index}
        >
          <Image
            src={image.src || "/placeholder.svg"}
            alt={image.alt || "Promo image"}
            fill
            className={`object-cover p-0 transition-opacity duration-300 ${
              isLoaded[index] ? "opacity-100" : "opacity-0"
            }`}
            onLoad={() => handleImageLoad(index)}
            sizes="(max-width: 768px) 100vw, 33vw"
            priority={index === currentIndex}
          />
          {!isLoaded[index] && <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>}
        </div>
      ))}

      {/* Dot indicators */}
      {validImages.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 z-20 flex justify-center">
          {validImages.map((_, index) => (
            <button
              key={index}
              onClick={(event) => handleDotButtonClick(event, index)}
              className={`w-1.5 h-1.5 rounded-full mx-1 transition-all duration-200 ${
                currentIndex === index ? "bg-[#ee4d2d]" : "bg-white/70 hover:bg-[#ee4d2d]"
              }`}
              aria-label={`Go to slide ${index + 1}`}
              aria-current={currentIndex === index ? "true" : "false"}
              type="button"
            />
          ))}
        </div>
      )}
    </div>
  )
}
