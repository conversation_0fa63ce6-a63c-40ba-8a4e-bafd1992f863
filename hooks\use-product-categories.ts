import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parent_id?: string;
  image_url?: string;
  icon?: string;
  color: string;
  sort_order: number;
  is_active: boolean;
  meta_title?: string;
  meta_description?: string;
  meta_keywords: string[];
  created_at: string;
  updated_at: string;
  // Relations
  parent?: ProductCategory;
  children?: ProductCategory[];
  product_count?: number;
}

export interface ProductCategoryFilters {
  search?: string;
  parent_id?: string;
  is_active?: boolean;
}

export interface ProductCategoryCreate {
  name: string;
  slug: string;
  description?: string;
  parent_id?: string;
  image_url?: string;
  icon?: string;
  color?: string;
  sort_order?: number;
  is_active?: boolean;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string[];
}

export function useProductCategories() {
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch categories with optional filters
  const fetchCategories = useCallback(async (filters?: ProductCategoryFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      
      if (filters?.search) params.append('search', filters.search);
      if (filters?.parent_id !== undefined) params.append('parent_id', filters.parent_id);
      if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
      
      // Get categories with product counts
      params.append('with_counts', 'true');
      
      const response = await fetch(`/api/product-categories?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      
      const data = await response.json();
      setCategories(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Get single category
  const getCategory = useCallback(async (id: string): Promise<ProductCategory | null> => {
    try {
      const response = await fetch(`/api/product-categories/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch category');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Create category
  const createCategory = useCallback(async (categoryData: ProductCategoryCreate): Promise<boolean> => {
    try {
      const response = await fetch('/api/product-categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(categoryData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create category');
      }
      
      const newCategory = await response.json();
      
      // Update local state
      setCategories(prev => [newCategory, ...prev]);
      
      toast.success('Kategori berhasil dibuat');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Update category
  const updateCategory = useCallback(async (id: string, updates: Partial<ProductCategoryCreate>): Promise<boolean> => {
    try {
      const response = await fetch(`/api/product-categories/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update category');
      }
      
      const updatedCategory = await response.json();
      
      // Update local state
      setCategories(prev => 
        prev.map(category => category.id === id ? updatedCategory : category)
      );
      
      toast.success('Kategori berhasil diperbarui');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Delete category
  const deleteCategory = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/product-categories/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete category');
      }
      
      // Update local state
      setCategories(prev => prev.filter(category => category.id !== id));
      
      toast.success('Kategori berhasil dihapus');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Toggle category status
  const toggleCategoryStatus = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/product-categories/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'toggle_status',
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to toggle category status');
      }
      
      const updatedCategory = await response.json();
      
      // Update local state
      setCategories(prev => 
        prev.map(category => category.id === id ? updatedCategory : category)
      );
      
      toast.success(`Kategori ${updatedCategory.is_active ? 'diaktifkan' : 'dinonaktifkan'}`);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Get category statistics
  const getCategoryStats = useCallback(async () => {
    try {
      const response = await fetch('/api/product-categories/stats');
      
      if (!response.ok) {
        throw new Error('Failed to fetch category stats');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Get categories tree
  const getCategoriesTree = useCallback(async () => {
    try {
      const response = await fetch('/api/product-categories?tree=true');
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories tree');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return [];
    }
  }, []);

  // Refresh categories (re-fetch with current filters)
  const refreshCategories = useCallback(async () => {
    await fetchCategories();
  }, [fetchCategories]);

  // Initial fetch on mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    loading,
    error,
    fetchCategories,
    getCategory,
    createCategory,
    updateCategory,
    deleteCategory,
    toggleCategoryStatus,
    getCategoryStats,
    getCategoriesTree,
    refreshCategories,
  };
}
