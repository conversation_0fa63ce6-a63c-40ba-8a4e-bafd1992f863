"use client"

import { useState } from "react"
import Link from "next/link"
import {
  ArrowLeft,
  Package,
  Search,
  Filter,
  ChevronDown,
  RefreshCcw,
  Clock,
  CheckCircle,
  XCircle,
  Plus,
  Upload,
  ArrowRight,
  FileText,
} from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"

// Sample return requests data
const returnRequests = [
  {
    id: "RET-001",
    orderId: "ORD-12343",
    date: "2025-05-10",
    status: "pending",
    items: [
      {
        name: "Tas Ransel",
        quantity: 1,
        price: 200000,
        image: "/colorful-backpack-on-wooden-table.png",
        reason: "Damaged on arrival",
      },
    ],
    returnMethod: "refund",
    trackingNumber: null,
    refundAmount: 200000,
  },
  {
    id: "RET-002",
    orderId: "ORD-12341",
    date: "2025-04-25",
    status: "approved",
    items: [
      {
        name: "Hoodie",
        quantity: 1,
        price: 300000,
        image: "/cozy-hoodie.png",
        reason: "Wrong size",
      },
    ],
    returnMethod: "exchange",
    exchangeItem: "Hoodie - Size L",
    trackingNumber: "RET123456789",
  },
  {
    id: "RET-003",
    orderId: "ORD-12340",
    date: "2025-04-15",
    status: "completed",
    items: [
      {
        name: "Sepatu Running",
        quantity: 1,
        price: 450000,
        image: "/running-shoes-on-track.png",
        reason: "Changed mind",
      },
    ],
    returnMethod: "refund",
    refundAmount: 450000,
    completedDate: "2025-04-20",
  },
  {
    id: "RET-004",
    orderId: "ORD-12338",
    date: "2025-04-05",
    status: "rejected",
    items: [
      {
        name: "Kacamata Hitam",
        quantity: 1,
        price: 150000,
        image: "/stylish-sunglasses.png",
        reason: "No longer needed",
      },
    ],
    returnMethod: "refund",
    rejectionReason: "Return period expired",
  },
]

// Sample orders eligible for return
const eligibleOrders = [
  {
    id: "ORD-12343",
    date: "2025-05-05",
    items: [
      {
        id: "ITEM-001",
        name: "Tas Ransel",
        quantity: 1,
        price: 200000,
        image: "/colorful-backpack-on-wooden-table.png",
        isReturnable: true,
      },
      {
        id: "ITEM-002",
        name: "Topi Baseball",
        quantity: 2,
        price: 75000,
        image: "/baseball-cap-display.png",
        isReturnable: true,
      },
    ],
  },
  {
    id: "ORD-12341",
    date: "2025-04-20",
    items: [
      {
        id: "ITEM-003",
        name: "Hoodie",
        quantity: 1,
        price: 300000,
        image: "/cozy-hoodie.png",
        isReturnable: true,
      },
      {
        id: "ITEM-004",
        name: "Syal",
        quantity: 1,
        price: 100000,
        image: "/cozy-knit-scarf.png",
        isReturnable: false,
      },
    ],
  },
]

// Return reasons options
const returnReasons = [
  "Damaged on arrival",
  "Wrong item received",
  "Wrong size or fit",
  "Item not as described",
  "Missing parts or accessories",
  "Changed mind",
  "Found better price elsewhere",
  "Other",
]

export default function ReturnsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("active")
  const [isCreatingReturn, setIsCreatingReturn] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null)
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [returnReason, setReturnReason] = useState<string>("")
  const [returnMethod, setReturnMethod] = useState<string>("refund")
  const [returnDescription, setReturnDescription] = useState<string>("")

  // Filter returns based on search query
  const filteredReturns = returnRequests.filter(
    (request) =>
      request.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.orderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.items.some((item) => item.name.toLowerCase().includes(searchQuery.toLowerCase())),
  )

  // Get active returns
  const activeReturns = filteredReturns.filter(
    (request) => request.status === "pending" || request.status === "approved",
  )

  // Get completed returns
  const completedReturns = filteredReturns.filter(
    (request) => request.status === "completed" || request.status === "rejected",
  )

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="default" className="capitalize">
            Pending Review
          </Badge>
        )
      case "approved":
        return (
          <Badge variant="secondary" className="capitalize">
            Approved
          </Badge>
        )
      case "completed":
        return (
          <Badge variant="outline" className="border-green-500 text-green-500 capitalize">
            Completed
          </Badge>
        )
      case "rejected":
        return (
          <Badge variant="destructive" className="capitalize">
            Rejected
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="capitalize">
            {status}
          </Badge>
        )
    }
  }

  // Handle order selection
  const handleOrderSelect = (orderId: string) => {
    setSelectedOrder(orderId)
    setSelectedItems([])
  }

  // Handle item selection
  const handleItemSelect = (itemId: string) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter((id) => id !== itemId))
    } else {
      setSelectedItems([...selectedItems, itemId])
    }
  }

  // Handle return submission
  const handleSubmitReturn = () => {
    // In a real app, this would submit the return request to the backend
    alert("Return request submitted successfully!")
    setIsCreatingReturn(false)
    setSelectedOrder(null)
    setSelectedItems([])
    setReturnReason("")
    setReturnMethod("refund")
    setReturnDescription("")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/buyer/dashboard/orders">
              <ArrowLeft className="h-5 w-5" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Returns & Exchanges</h1>
        </div>
        {!isCreatingReturn && (
          <Button onClick={() => setIsCreatingReturn(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Return
          </Button>
        )}
      </div>

      {isCreatingReturn ? (
        <Card>
          <CardHeader>
            <CardTitle>Create Return Request</CardTitle>
            <CardDescription>
              Please fill out the form below to request a return or exchange for your order.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Step 1: Select Order */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Step 1: Select Order</h3>
              <Select value={selectedOrder || ""} onValueChange={handleOrderSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Select an order" />
                </SelectTrigger>
                <SelectContent>
                  {eligibleOrders.map((order) => (
                    <SelectItem key={order.id} value={order.id}>
                      {order.id} - {new Date(order.date).toLocaleDateString("id-ID")}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Step 2: Select Items */}
            {selectedOrder && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Step 2: Select Items to Return</h3>
                <div className="space-y-4">
                  {eligibleOrders
                    .find((order) => order.id === selectedOrder)
                    ?.items.map((item) => (
                      <div key={item.id} className="flex items-start space-x-3 rounded-md border p-4">
                        <Checkbox
                          id={item.id}
                          checked={selectedItems.includes(item.id)}
                          onCheckedChange={() => handleItemSelect(item.id)}
                          disabled={!item.isReturnable}
                        />
                        <div className="flex flex-1 gap-4">
                          <img
                            src={item.image || "/placeholder.svg"}
                            alt={item.name}
                            className="h-16 w-16 rounded-md border object-cover"
                          />
                          <div className="flex-1">
                            <label
                              htmlFor={item.id}
                              className={`font-medium ${!item.isReturnable ? "text-muted-foreground" : ""}`}
                            >
                              {item.name}
                            </label>
                            <div className="mt-1 flex justify-between">
                              <span className="text-sm text-muted-foreground">
                                Qty: {item.quantity} × {formatCurrency(item.price)}
                              </span>
                              <span className="font-medium">{formatCurrency(item.price * item.quantity)}</span>
                            </div>
                            {!item.isReturnable && (
                              <p className="mt-1 text-xs text-red-500">This item is not eligible for return</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* Step 3: Return Reason */}
            {selectedItems.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Step 3: Return Reason</h3>
                <Select value={returnReason} onValueChange={setReturnReason}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a reason for return" />
                  </SelectTrigger>
                  <SelectContent>
                    {returnReasons.map((reason) => (
                      <SelectItem key={reason} value={reason}>
                        {reason}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <div className="space-y-2">
                  <Label htmlFor="description">Additional Details</Label>
                  <Textarea
                    id="description"
                    placeholder="Please provide more details about your return request..."
                    value={returnDescription}
                    onChange={(e) => setReturnDescription(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Upload Photos (Optional)</Label>
                  <div className="flex h-32 cursor-pointer items-center justify-center rounded-md border border-dashed">
                    <div className="flex flex-col items-center space-y-2 text-center">
                      <Upload className="h-8 w-8 text-muted-foreground" />
                      <div className="text-xs text-muted-foreground">
                        <span className="font-medium text-primary">Click to upload</span> or drag and drop
                      </div>
                      <div className="text-xs text-muted-foreground">PNG, JPG up to 5MB</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Return Method */}
            {returnReason && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Step 4: Return Method</h3>
                <RadioGroup value={returnMethod} onValueChange={setReturnMethod}>
                  <div className="flex flex-col space-y-3">
                    <div className="flex items-start space-x-3 rounded-md border p-4">
                      <RadioGroupItem value="refund" id="refund" />
                      <div className="flex-1">
                        <Label htmlFor="refund" className="font-medium">
                          Refund to Original Payment Method
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          Get your money back to the original payment method used for the purchase.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 rounded-md border p-4">
                      <RadioGroupItem value="exchange" id="exchange" />
                      <div className="flex-1">
                        <Label htmlFor="exchange" className="font-medium">
                          Exchange for Another Item
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          Exchange your item for another size, color, or similar product.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 rounded-md border p-4">
                      <RadioGroupItem value="store-credit" id="store-credit" />
                      <div className="flex-1">
                        <Label htmlFor="store-credit" className="font-medium">
                          Store Credit
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          Receive store credit that you can use for future purchases.
                        </p>
                      </div>
                    </div>
                  </div>
                </RadioGroup>
              </div>
            )}

            {/* Step 5: Return Shipping */}
            {returnMethod && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Step 5: Return Shipping</h3>
                <div className="rounded-md bg-muted p-4">
                  <h4 className="font-medium">Return Instructions</h4>
                  <ol className="ml-4 mt-2 list-decimal space-y-2 text-sm">
                    <li>Pack the item(s) securely in the original packaging if possible.</li>
                    <li>Include a copy of your order receipt or return confirmation.</li>
                    <li>Ship the package to the address provided in your return confirmation email.</li>
                    <li>Keep your tracking number for reference.</li>
                  </ol>
                </div>

                <div className="rounded-md border p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Return Shipping Label</h4>
                      <p className="text-sm text-muted-foreground">
                        A prepaid shipping label will be sent to your email after submission.
                      </p>
                    </div>
                    <Button variant="outline" size="sm" className="gap-1">
                      <FileText className="h-4 w-4" />
                      Preview
                    </Button>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-between pt-4">
              <Button variant="outline" onClick={() => setIsCreatingReturn(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleSubmitReturn}
                disabled={!selectedOrder || selectedItems.length === 0 || !returnReason || !returnMethod}
              >
                Submit Return Request
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Search and Filter */}
          <div className="flex flex-col gap-4 sm:flex-row">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search returns by ID or product..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select defaultValue="all-time">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Time period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last-30-days">Last 30 days</SelectItem>
                  <SelectItem value="last-6-months">Last 6 months</SelectItem>
                  <SelectItem value="last-year">Last year</SelectItem>
                  <SelectItem value="all-time">All time</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="gap-1">
                <Filter className="h-4 w-4" />
                <span className="hidden sm:inline">Filters</span>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Returns List */}
          <Tabs defaultValue="active" value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="active" className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                Active Returns
              </TabsTrigger>
              <TabsTrigger value="completed" className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4" />
                Return History
              </TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="mt-6">
              <div className="space-y-4">
                {activeReturns.length > 0 ? (
                  activeReturns.map((request) => (
                    <Card key={request.id} className="overflow-hidden">
                      <CardContent className="p-0">
                        {/* Return Header */}
                        <div className="flex flex-wrap items-center justify-between border-b p-4">
                          <div className="mb-2 flex flex-col sm:mb-0">
                            <div className="flex items-center gap-2">
                              <span className="font-semibold">{request.id}</span>
                              {renderStatusBadge(request.status)}
                            </div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>Order: {request.orderId}</span>
                              <span>•</span>
                              <span>Requested on {new Date(request.date).toLocaleDateString("id-ID")}</span>
                            </div>
                          </div>
                          <div className="flex flex-col items-end">
                            <div className="font-medium">
                              {request.returnMethod === "refund"
                                ? `Refund: ${formatCurrency(request.refundAmount || 0)}`
                                : "Exchange"}
                            </div>
                            <div className="text-sm text-muted-foreground capitalize">{request.returnMethod}</div>
                          </div>
                        </div>

                        {/* Return Items */}
                        <div className="p-4">
                          <div className="mb-4 flex flex-col gap-4 sm:flex-row">
                            <div className="flex flex-1 flex-wrap gap-3">
                              {request.items.map((item, idx) => (
                                <div key={idx} className="flex items-center gap-2">
                                  <img
                                    src={item.image || "/placeholder.svg"}
                                    alt={item.name}
                                    className="h-16 w-16 rounded-md border object-cover"
                                  />
                                  <div>
                                    <div className="font-medium">{item.name}</div>
                                    <div className="text-sm text-muted-foreground">Reason: {item.reason}</div>
                                  </div>
                                </div>
                              ))}
                            </div>

                            <div className="flex flex-col justify-between gap-2 sm:items-end">
                              {request.status === "approved" && (
                                <div className="flex items-center gap-1 text-sm">
                                  <RefreshCcw className="h-4 w-4 text-primary" />
                                  <span>
                                    {request.returnMethod === "exchange"
                                      ? `Exchange for: ${request.exchangeItem}`
                                      : "Refund processing"}
                                  </span>
                                </div>
                              )}
                              {request.trackingNumber && (
                                <div className="text-sm text-muted-foreground">Tracking: {request.trackingNumber}</div>
                              )}
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-wrap justify-end gap-2">
                            <Button variant="outline" size="sm" asChild>
                              <Link href={`/buyer/dashboard/orders/returns/${request.id}`}>
                                <ArrowRight className="mr-1 h-4 w-4" />
                                Details
                              </Link>
                            </Button>
                            {request.status === "pending" && (
                              <Button variant="ghost" size="sm">
                                <XCircle className="mr-1 h-4 w-4" />
                                Cancel
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
                    <Package className="h-10 w-10 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">No active returns</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      You don't have any active return or exchange requests
                    </p>
                    <Button className="mt-4" onClick={() => setIsCreatingReturn(true)}>
                      Create Return Request
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="completed" className="mt-6">
              <div className="space-y-4">
                {completedReturns.length > 0 ? (
                  completedReturns.map((request) => (
                    <Card key={request.id} className="overflow-hidden">
                      <CardContent className="p-0">
                        {/* Return Header */}
                        <div className="flex flex-wrap items-center justify-between border-b p-4">
                          <div className="mb-2 flex flex-col sm:mb-0">
                            <div className="flex items-center gap-2">
                              <span className="font-semibold">{request.id}</span>
                              {renderStatusBadge(request.status)}
                            </div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>Order: {request.orderId}</span>
                              <span>•</span>
                              <span>Requested on {new Date(request.date).toLocaleDateString("id-ID")}</span>
                            </div>
                          </div>
                          <div className="flex flex-col items-end">
                            <div className="font-medium">
                              {request.returnMethod === "refund"
                                ? `Refund: ${formatCurrency(request.refundAmount || 0)}`
                                : "Exchange"}
                            </div>
                            <div className="text-sm text-muted-foreground capitalize">{request.returnMethod}</div>
                          </div>
                        </div>

                        {/* Return Items */}
                        <div className="p-4">
                          <div className="mb-4 flex flex-col gap-4 sm:flex-row">
                            <div className="flex flex-1 flex-wrap gap-3">
                              {request.items.map((item, idx) => (
                                <div key={idx} className="flex items-center gap-2">
                                  <img
                                    src={item.image || "/placeholder.svg"}
                                    alt={item.name}
                                    className="h-16 w-16 rounded-md border object-cover"
                                  />
                                  <div>
                                    <div className="font-medium">{item.name}</div>
                                    <div className="text-sm text-muted-foreground">Reason: {item.reason}</div>
                                  </div>
                                </div>
                              ))}
                            </div>

                            <div className="flex flex-col justify-between gap-2 sm:items-end">
                              {request.status === "completed" && request.completedDate && (
                                <div className="flex items-center gap-1 text-sm text-green-500">
                                  <CheckCircle className="h-4 w-4" />
                                  <span>
                                    Completed on {new Date(request.completedDate).toLocaleDateString("id-ID")}
                                  </span>
                                </div>
                              )}
                              {request.status === "rejected" && request.rejectionReason && (
                                <div className="flex items-center gap-1 text-sm text-red-500">
                                  <XCircle className="h-4 w-4" />
                                  <span>Reason: {request.rejectionReason}</span>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-wrap justify-end gap-2">
                            <Button variant="outline" size="sm" asChild>
                              <Link href={`/buyer/dashboard/orders/returns/${request.id}`}>
                                <ArrowRight className="mr-1 h-4 w-4" />
                                Details
                              </Link>
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
                    <Package className="h-10 w-10 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">No return history</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      You don't have any completed return or exchange requests
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}
