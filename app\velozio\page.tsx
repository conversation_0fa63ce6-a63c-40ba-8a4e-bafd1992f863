"use client"

import { VelozioHeader } from "@/components/themes/velozio/velozio-header"
import { FeedCard } from "@/components/themes/velozio/feed-card/feed-card"
import { MasonryLayout } from "@/components/themes/velozio/masonry/masonry-layout"
import Categories from "@/components/themes/velozio/categories"
import type { FeedCardType, BadgeType } from "@/components/themes/velozio/feed-card/feed-card"

interface Product {
  id: number
  type: FeedCardType
  name: string
  price: string
  originalPrice: string
  discount?: string
  image: string
  badgeType: BadgeType
  rating: number
  sold: number
  hasCod?: boolean
  isLive?: boolean
  link: string
  flashSale?: {
    endTime: Date
    remaining: number
    total: number
  }
  videoThumbnail?: string
  videoSrc?: string
  images?: Array<{ src: string; alt: string }>
}

// Contoh data produk
const products: Product[] = [
{
    id: 1,
    type: "image-slider",
    name: "Sepatu Sneakers Pria Casual Running Sport Original",
    price: "Rp 349.000",
    originalPrice: "Rp 499.000",
    discount: "30%",
    image: "/diverse-sneaker-collection.png",
    images: [
      { src: "/diverse-sneaker-collection.png", alt: "Sneakers collection" },
      { src: "/sneakers-front-view.png", alt: "Sneakers front view" },
      { src: "/sneakers-side-view.png", alt: "Sneakers side view" }
    ],
    badgeType: "termurah",
    rating: 4.6,
    sold: 320,
    link: "#"
  },
  
  {
    id: 2,
    type: "flash-sale",
    name: "Blender Portable Mini Rechargeable",
    price: "Rp 149.000",
    originalPrice: "Rp 299.000",
    discount: "50%",
    image: "/placeholder-kwsmp.png",
    badgeType: "mall",
    rating: 4.5,
    sold: 1250,
    link: "#",
    flashSale: {
      endTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
      remaining: 8,
      total: 100
    }
  },
  {
    id: 3,
    type: "standard",
    name: "Smart TV LED 55 Inch 4K HDR Android TV",
    price: "Rp 6.999.000",
    originalPrice: "Rp 7.999.000",
    discount: "13%",
    image: "/modern-tv.png",
    badgeType: "terlaris",
    rating: 4.8,
    sold: 89,
    hasCod: true,
    link: "#"
  },
  {
    id: 4,
    type: "standard",
    name: "Laptop Gaming RTX 4060 RAM 16GB SSD 1TB",
    price: "Rp 18.999.000",
    originalPrice: "Rp 21.999.000",
    discount: "14%",
    image: "/modern-laptop.png",
    badgeType: "komisi-xtra",
    rating: 4.9,
    sold: 45,
    hasCod: true,
    link: "#"
  },
  {
    id: 5,
    type: "video",
    name: "Headphone JBL Premium Wireless Bluetooth Noise Cancelling",
    price: "Rp 1.299.000",
    originalPrice: "Rp 1.599.000",
    discount: "19%",
    image: "/wireless-over-ear-headphones.png",
    videoThumbnail: "/person-headphones.png",
    videoSrc: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    badgeType: "star-lite",
    rating: 4.8,
    sold: 75,
    isLive: true,
    link: "#"
  },
  {
    id: 6,
    type: "standard",
    name: "Smartphone Samsung Galaxy A54 RAM 8GB Storage 256GB Original",
    price: "Rp 5.499.000",
    originalPrice: "Rp 6.299.000",
    discount: "13%",
    image: "/modern-smartphone.png",
    badgeType: "mall",
    rating: 4.9,
    sold: 120,
    hasCod: true,
    link: "#"
  },
  
  {
    id: 7,
    type: "standard",
    name: "Power Bank 10000mAh Fast Charging 33W Original",
    price: "Rp 249.000",
    originalPrice: "Rp 299.000",
    discount: "17%",
    image: "/portable-power-bank.png",
    badgeType: "star-lite",
    rating: 4.5,
    sold: 210,
    isLive: true,
    link: "#"
  },
  {
    id: 8,
    type: "standard",
    name: "Earbuds Wireless Bluetooth 5.0 Touch Control",
    price: "Rp 199.000",
    originalPrice: "Rp 299.000",
    discount: "33%",
    image: "/wireless-earbuds.png",
    badgeType: "star-lite",
    rating: 4.3,
    sold: 89,
    link: "#"
  },
  {
    id: 9,
    type: "standard",
    name: "Smart Watch Fitness Tracker Waterproof",
    price: "Rp 399.000",
    originalPrice: "Rp 599.000",
    discount: "33%",
    image: "/smartwatch-lifestyle.png",
    badgeType: "star",
    rating: 4.6,
    sold: 156,
    link: "#"
  },
  {
    id: 10,
    type: "standard",
    name: "Laptop Gaming 15.6 inch Core i7 RTX 3060",
    price: "Rp 15.999.000",
    originalPrice: "Rp 18.999.000",
    discount: "16%",
    image: "/laptop-gaming.png",
    badgeType: "mall",
    rating: 4.8,
    sold: 42,
    link: "#"
  },
  {
    id: 11,
    type: "standard",
    name: "Kamera Mirrorless 24MP Full Frame",
    price: "Rp 12.499.000",
    originalPrice: "Rp 13.999.000",
    discount: "11%",
    image: "/mirrorless-camera.png",
    badgeType: "termurah",
    rating: 4.9,
    sold: 28,
    link: "#"
  },
  {
    id: 12,
    type: "flash-sale",
    name: "Blender Portable Mini Rechargeable",
    price: "Rp 149.000",
    originalPrice: "Rp 299.000",
    discount: "50%",
    image: "/placeholder-kwsmp.png",
    badgeType: "mall",
    rating: 4.5,
    sold: 1250,
    link: "#",
    flashSale: {
      endTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
      remaining: 8,
      total: 100
    }
  },
  {
    id: 13,
    type: "standard",
    name: "Kipas Angin Portable USB Rechargeable",
    price: "Rp 89.000",
    originalPrice: "Rp 129.000",
    discount: "31%",
    image: "/portable-fan.png",
    badgeType: "star",
    rating: 4.4,
    sold: 3200,
    link: "#"
  },
  {
    id: 14,
    type: "video",
    name: "Drone Mini dengan Kamera HD 4K",
    price: "Rp 1.799.000",
    originalPrice: "Rp 2.499.000",
    discount: "28%",
    image: "/mini-drone.png",
    videoThumbnail: "/drone-video.png",
    videoSrc: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    badgeType: "mall",
    rating: 4.7,
    sold: 89,
    isLive: true,
    link: "#"
  },
  {
    id: 15,
    type: "image-slider",
    name: "Jam Tangan Pria Automatic Mechanical",
    price: "Rp 899.000",
    originalPrice: "Rp 1.299.000",
    discount: "31%",
    image: "/mechanical-watch.png",
    images: [
      { src: "/watch-front.png", alt: "Watch front view" },
      { src: "/watch-side.png", alt: "Watch side view" },
      { src: "/watch-back.png", alt: "Watch back view" }
    ],
    badgeType: "star",
    rating: 4.8,
    sold: 156,
    link: "#"
  },
  {
    id: 16,
    type: "standard",
    name: "Keyboard Mechanical RGB Gaming",
    price: "Rp 599.000",
    originalPrice: "Rp 799.000",
    discount: "25%",
    image: "/mechanical-keyboard.png",
    badgeType: "termurah",
    rating: 4.7,
    sold: 420,
    link: "#"
  },
  {
    id: 17,
    type: "standard",
    name: "Mouse Gaming Wireless 16000 DPI",
    price: "Rp 349.000",
    originalPrice: "Rp 499.000",
    discount: "30%",
    image: "/placeholder-7mn5n.png",
    badgeType: "star-lite",
    rating: 4.6,
    sold: 287,
    link: "#"
  }
]

const VelozioPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <VelozioHeader />
      <Categories />
      <main className="container mx-auto py-4 px-2">
        <section className="mb-12">
          <MasonryLayout>
            {products.map((product) => (
              <div key={product.id} className="mb-4">
                <FeedCard 
                  type={product.type}
                  name={product.name}
                  price={product.price}
                  originalPrice={product.originalPrice}
                  discount={product.discount}
                  image={product.image}
                  images={product.images}
                  badgeType={product.badgeType}
                  rating={product.rating}
                  sold={product.sold}
                  hasCod={product.hasCod}
                  isLive={product.isLive}
                  link={product.link}
                  flashSale={product.flashSale}
                  videoThumbnail={product.videoThumbnail}
                  videoSrc={product.videoSrc}
                />
              </div>
            ))}
          </MasonryLayout>
        </section>
      </main>
    </div>
  )
}

export default VelozioPage
