import { NextRequest, NextResponse } from 'next/server';
import { storeApplicationService } from '@/lib/services/store-applications';

// GET - Mendapatkan store application berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const application = await storeApplicationService.getApplication(id);

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(application);
  } catch (error) {
    console.error('Error fetching application:', error);
    return NextResponse.json(
      { error: 'Failed to fetch application' },
      { status: 500 }
    );
  }
}

// PUT - Update store application berdasarkan ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();

    const updatedApplication = await storeApplicationService.updateApplication(id, body);

    return NextResponse.json(updatedApplication);
  } catch (error) {
    console.error('Error updating application:', error);
    return NextResponse.json(
      { error: 'Failed to update application' },
      { status: 500 }
    );
  }
}

// DELETE - Hapus store application berdasarkan ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    await storeApplicationService.deleteApplication(id);

    return NextResponse.json({
      message: 'Application deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting application:', error);
    return NextResponse.json(
      { error: 'Failed to delete application' },
      { status: 500 }
    );
  }
}

// PATCH - Update status aplikasi (approve/reject/review)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { action, reason, notes, status } = body;

    let updatedApplication;

    switch (action) {
      case 'approve':
        updatedApplication = await storeApplicationService.approveApplication(id, reason);
        break;
      case 'reject':
        if (!reason) {
          return NextResponse.json(
            { error: 'Reason is required for rejection' },
            { status: 400 }
          );
        }
        updatedApplication = await storeApplicationService.rejectApplication(id, reason);
        break;
      case 'review':
        // Jika ada status yang diberikan, gunakan itu, jika tidak set ke under_review
        if (status) {
          const updates = {
            status,
            review_notes: notes,
            reviewed_date: new Date().toISOString().split('T')[0],
          };
          updatedApplication = await storeApplicationService.updateApplication(id, updates);
        } else {
          updatedApplication = await storeApplicationService.setUnderReview(id, notes);
        }
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: approve, reject, or review' },
          { status: 400 }
        );
    }

    return NextResponse.json(updatedApplication);
  } catch (error) {
    console.error('Error updating application status:', error);
    return NextResponse.json(
      { error: 'Failed to update application status' },
      { status: 500 }
    );
  }
}
