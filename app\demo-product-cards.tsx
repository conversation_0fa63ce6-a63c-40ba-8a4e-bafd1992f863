"use client"

import { FeedCard } from "@/components/themes/velozio/feed-card/feed-card"
import { MasonryLayout } from "@/components/themes/velozio/masonry/masonry-layout"

export default function DemoProductCards() {
  // Contoh data untuk flash sale
  const flashSaleEndTime = new Date()
  flashSaleEndTime.setHours(flashSaleEndTime.getHours() + 2) // 2 jam dari sekarang

  // Data produk dengan variasi konten untuk membuat tinggi berbeda
  const products = [
    {
      type: "standard",
      name: "Smartphone Samsung Galaxy A54 RAM 8GB Storage 256GB Original",
      price: "Rp 5.499.000",
      originalPrice: "Rp 6.299.000",
      discount: "13%",
      image: "/modern-smartphone.png",
      badgeType: "mall",
      rating: 4.9,
      sold: 120,
      hasCod: true,
    },
    {
      type: "flash-sale",
      name: "Tas Selempang Wanita Model Korea Premium Quality",
      price: "Rp 129.000",
      originalPrice: "Rp 199.000",
      image: "/stylish-leather-handbag.png",
      badgeType: "star",
      rating: 4.7,
      sold: 500,
      flashSale: {
        endTime: flashSaleEndTime,
        remaining: 15,
        total: 100,
      },
    },
    {
      type: "video",
      name: "Headphone JBL Premium Wireless Bluetooth Noise Cancelling",
      price: "Rp 1.299.000",
      originalPrice: "Rp 1.599.000",
      image: "/wireless-over-ear-headphones.png",
      videoThumbnail: "/person-headphones.png",
      videoSrc: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      badgeType: "star-lite",
      rating: 4.8,
      sold: 75,
      isLive: true,
    },
    {
      type: "image-slider",
      name: "Sepatu Sneakers Pria Casual Running Sport Original",
      price: "Rp 349.000",
      originalPrice: "Rp 499.000",
      discount: "30%",
      image: "/diverse-sneaker-collection.png",
      images: [
        { src: "/diverse-sneaker-collection.png", alt: "Sneakers collection" },
        { src: "/sneakers-front-view.png", alt: "Sneakers front view" },
        { src: "/sneakers-side-view.png", alt: "Sneakers side view" },
      ],
      badgeType: "termurah",
      rating: 4.6,
      sold: 320,
    },
    {
      type: "standard",
      name: "Power Bank 10000mAh Fast Charging 33W Original",
      price: "Rp 249.000",
      originalPrice: "Rp 299.000",
      discount: "17%",
      image: "/portable-power-bank.png",
      badgeType: "star-lite",
      rating: 4.5,
      sold: 210,
      isLive: true,
    },
    {
      type: "standard",
      name: "Earbuds Wireless Bluetooth 5.0 Touch Control",
      price: "Rp 199.000",
      originalPrice: "Rp 299.000",
      discount: "33%",
      image: "/wireless-earbuds.png",
      badgeType: "star-lite",
      rating: 4.3,
      sold: 89,
    },
    {
      type: "standard",
      name: "Smart Watch Fitness Tracker Waterproof",
      price: "Rp 399.000",
      originalPrice: "Rp 599.000",
      discount: "33%",
      image: "/smartwatch-lifestyle.png",
      badgeType: "star",
      rating: 4.6,
      sold: 156,
    },
    {
      type: "standard",
      name: "Laptop Gaming 15.6 inch Core i7 RTX 3060",
      price: "Rp 15.999.000",
      originalPrice: "Rp 18.999.000",
      discount: "16%",
      image: "/laptop-gaming.png",
      badgeType: "mall",
      rating: 4.8,
      sold: 42,
    },
    {
      type: "standard",
      name: "Kamera Mirrorless 24MP Full Frame",
      price: "Rp 12.499.000",
      originalPrice: "Rp 13.999.000",
      discount: "11%",
      image: "/mirrorless-camera.png",
      badgeType: "termurah",
      rating: 4.9,
      sold: 28,
    },
    {
      type: "flash-sale",
      name: "Blender Portable Mini Rechargeable",
      price: "Rp 149.000",
      originalPrice: "Rp 299.000",
      discount: "50%",
      image: "/placeholder-kwsmp.png",
      badgeType: "mall",
      rating: 4.5,
      sold: 1250,
      flashSale: {
        endTime: flashSaleEndTime,
        remaining: 8,
        total: 100,
      },
    },
    {
      type: "standard",
      name: "Kipas Angin Portable USB Rechargeable",
      price: "Rp 89.000",
      originalPrice: "Rp 129.000",
      discount: "31%",
      image: "/portable-fan.png",
      badgeType: "star",
      rating: 4.4,
      sold: 3200,
    },
    {
      type: "video",
      name: "Drone Mini dengan Kamera HD 4K",
      price: "Rp 1.799.000",
      originalPrice: "Rp 2.499.000",
      discount: "28%",
      image: "/mini-drone.png",
      videoThumbnail: "/drone-video.png",
      videoSrc: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      badgeType: "mall",
      rating: 4.7,
      sold: 89,
      isLive: true,
    },
    {
      type: "image-slider",
      name: "Jam Tangan Pria Automatic Mechanical",
      price: "Rp 899.000",
      originalPrice: "Rp 1.299.000",
      discount: "31%",
      image: "/mechanical-watch.png",
      images: [
        { src: "/watch-front.png", alt: "Watch front view" },
        { src: "/watch-side.png", alt: "Watch side view" },
        { src: "/watch-back.png", alt: "Watch back view" },
      ],
      badgeType: "star",
      rating: 4.8,
      sold: 156,
    },
    {
      type: "standard",
      name: "Keyboard Mechanical RGB Gaming",
      price: "Rp 599.000",
      originalPrice: "Rp 799.000",
      discount: "25%",
      image: "/mechanical-keyboard.png",
      badgeType: "termurah",
      rating: 4.7,
      sold: 420,
    },
    {
      type: "standard",
      name: "Mouse Gaming Wireless 16000 DPI",
      price: "Rp 349.000",
      originalPrice: "Rp 499.000",
      discount: "30%",
      image: "/placeholder-7mn5n.png",
      badgeType: "star-lite",
      rating: 4.6,
      sold: 310,
    },
    {
      type: "flash-sale",
      name: "Microphone Condenser USB untuk Streaming",
      price: "Rp 299.000",
      originalPrice: "Rp 499.000",
      discount: "40%",
      image: "/condenser-mic.png",
      badgeType: "mall",
      rating: 4.5,
      sold: 180,
      flashSale: {
        endTime: flashSaleEndTime,
        remaining: 23,
        total: 100,
      },
    },
    {
      type: "standard",
      name: "Webcam HD 1080p dengan Microphone",
      price: "Rp 249.000",
      originalPrice: "Rp 349.000",
      discount: "29%",
      image: "/webcam-hd.png",
      badgeType: "star",
      rating: 4.4,
      sold: 230,
    },
    {
      type: "standard",
      name: "Speaker Bluetooth Waterproof Outdoor",
      price: "Rp 399.000",
      originalPrice: "Rp 599.000",
      discount: "33%",
      image: "/portable-speaker.png",
      badgeType: "mall",
      rating: 4.7,
      sold: 520,
      isLive: true,
    },
    // Produk baru dengan badge Terlaris
    {
      type: "standard",
      name: "Sepatu Lari Nike Air Zoom Pegasus 38 Original",
      price: "Rp 1.899.000",
      originalPrice: "Rp 2.199.000",
      discount: "14%",
      image: "/running-shoes-on-track.png",
      badgeType: "terlaris",
      rating: 4.9,
      sold: 1250,
      shipping: "Pengiriman Instan",
      hasCod: true,
      isLive: true,
    },
    // Produk baru dengan badge Komisi Xtra
    {
      type: "standard",
      name: "Parfum Pria Luxury Long Lasting EDP 100ml",
      price: "Rp 799.000",
      originalPrice: "Rp 999.000",
      discount: "20%",
      image: "/luxury-perfume-bottle.png",
      badgeType: "komisi-xtra",
      rating: 4.8,
      sold: 876,
      shipping: "Pengiriman Reguler",
      hasCod: true,
    },
  ]

  return (
    <div className="bg-gray-100 p-4">
      <h1 className="text-xl font-bold mb-4">Demo Product Cards - Masonry Layout</h1>

      <MasonryLayout columnCount={{ mobile: 2, tablet: 3, desktop: 6 }} gap={8}>
        {products.map((product, index) => (
          <FeedCard
            key={index}
            type={product.type as any}
            name={product.name}
            price={product.price}
            originalPrice={product.originalPrice}
            discount={product.discount}
            image={product.image}
            images={product.images}
            videoThumbnail={product.videoThumbnail}
            videoSrc={product.videoSrc}
            badgeType={product.badgeType as any}
            rating={product.rating || 0}
            sold={product.sold || 0}
            hasCod={product.hasCod}
            isLive={product.isLive}
            flashSale={product.flashSale}
          />
        ))}
      </MasonryLayout>
    </div>
  )
}
