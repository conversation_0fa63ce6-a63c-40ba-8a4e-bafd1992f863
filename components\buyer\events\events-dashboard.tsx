"use client"

import { useState } from "react"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { EventCalendarView } from "./event-calendar-view"
import { EventListView } from "./event-list-view"
import { EventCategories } from "./event-categories"
import { Button } from "@/components/ui/button"
import { CalendarDays, List, Search } from "lucide-react"
import { Input } from "@/components/ui/input"

export function EventsDashboard() {
  const [view, setView] = useState<"calendar" | "list">("calendar")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])

  const handleCategoryChange = (category: string) => {
    setSelectedCategories((prev) =>
      prev.includes(category) ? prev.filter((c) => c !== category) : [...prev, category],
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Events</h1>
          <p className="text-muted-foreground">Jelajahi dan ikuti berbagai event menarik</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Cari event..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex rounded-md border">
            <Button
              variant={view === "calendar" ? "default" : "ghost"}
              size="icon"
              onClick={() => setView("calendar")}
              className="rounded-r-none"
              aria-label="Calendar view"
            >
              <CalendarDays className="h-4 w-4" />
            </Button>
            <Button
              variant={view === "list" ? "default" : "ghost"}
              size="icon"
              onClick={() => setView("list")}
              className="rounded-l-none"
              aria-label="List view"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <div className="md:col-span-1">
          <EventCategories selectedCategories={selectedCategories} onCategoryChange={handleCategoryChange} />
        </div>
        <div className="md:col-span-3">
          <Tabs defaultValue={view} value={view} onValueChange={(v) => setView(v as "calendar" | "list")}>
            <TabsContent value="calendar" className="mt-0">
              <EventCalendarView searchQuery={searchQuery} selectedCategories={selectedCategories} />
            </TabsContent>
            <TabsContent value="list" className="mt-0">
              <EventListView searchQuery={searchQuery} selectedCategories={selectedCategories} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
