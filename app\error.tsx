"use client"

import { useEffect } from "react"
import { Button } from "@/components/ui/button"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log error ke sistem analitik
    console.error("Application error:", error)
  }, [error])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="w-full max-w-md text-center">
        <h2 className="text-2xl font-bold mb-4"><PERSON><PERSON><PERSON><PERSON></h2>
        <p className="mb-6 text-gray-600 dark:text-gray-400"><PERSON><PERSON>, terjadi kesalahan saat memuat halaman ini.</p>
        <div className="space-y-4">
          <Button onClick={() => reset()} variant="default">
            Coba <PERSON>gi
          </Button>
          <Button onClick={() => (window.location.href = "/")} variant="outline" className="ml-4">
            <PERSON><PERSON><PERSON> ke Be<PERSON>
          </Button>
        </div>
      </div>
    </div>
  )
}
