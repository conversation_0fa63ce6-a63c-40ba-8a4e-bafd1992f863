import type { Metada<PERSON> } from "next"
import SystemsDashboardClient from "./systems-client"
import { PageHeader } from "@/components/admin/ui/page-header"

export const metadata: Metadata = {
  title: "Systems Dashboard | Sellzio Admin",
  description: "Monitor dan kelola kesehatan sistem, sumber daya, dan infrastruktur",
}

export default function SystemsDashboardPage() {
  return (
    <>
      <PageHeader
        title="Systems Dashboard"
        description="Monitor dan kelola kesehatan sistem, sumber daya, dan infrastruktur"
        actions={[
          {
            label: "Pengaturan Sistem",
            href: "/admin/dashboard/systems/settings",
            variant: "default",
          },
          {
            label: "Lihat Log",
            href: "/admin/dashboard/systems/logs",
            variant: "outline",
          },
        ]}
      />
      <SystemsDashboardClient />
    </>
  )
}
