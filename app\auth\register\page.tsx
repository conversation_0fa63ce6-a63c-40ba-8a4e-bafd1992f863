"use client"

import { useSearchParams } from "next/navigation"
import { useState } from "react"
import { TenantRegisterForm } from "@/components/auth/tenant-register-form"
import { StoreRegisterForm } from "@/components/auth/store-register-form"
import { BuyerRegisterForm } from "@/components/auth/buyer-register-form"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function RegisterPage() {
  const searchParams = useSearchParams()
  const roleParam = searchParams.get("role")
  const planParam = searchParams.get("plan")

  const [activeTab, setActiveTab] = useState<string>(roleParam || "buyer")

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="w-full max-w-md">
        <h1 className="text-2xl font-bold mb-6 text-center">Daftar Akun Baru</h1>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="buyer">Pembeli</TabsTrigger>
            <TabsTrigger value="store">Toko</TabsTrigger>
            <TabsTrigger value="tenant">Tenant</TabsTrigger>
          </TabsList>

          <TabsContent value="buyer">
            <BuyerRegisterForm />
          </TabsContent>

          <TabsContent value="store">
            <StoreRegisterForm />
          </TabsContent>

          <TabsContent value="tenant">
            <TenantRegisterForm selectedPlan={planParam} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
