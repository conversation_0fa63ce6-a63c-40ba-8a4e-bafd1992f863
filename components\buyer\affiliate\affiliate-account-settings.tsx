import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"

export function AffiliateAccountSettings() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Account Settings</CardTitle>
        <CardDescription>Manage your affiliate profile and preferences</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Profile Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="display-name">Display Name</Label>
              <Input id="display-name" defaultValue="John's Tech Reviews" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="website">Website URL</Label>
              <Input id="website" defaultValue="https://johnstechreviews.com" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="bio">Bio/Description</Label>
            <Textarea
              id="bio"
              defaultValue="Tech enthusiast reviewing the latest gadgets and electronics. Specializing in honest, in-depth reviews of smartphones, laptops, and smart home devices."
              rows={4}
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Promotion Channels</h3>

          <div className="space-y-2">
            <Label>Select your primary promotion channels</Label>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="blog" defaultChecked />
                <Label htmlFor="blog">Blog/Website</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="youtube" defaultChecked />
                <Label htmlFor="youtube">YouTube</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="instagram" defaultChecked />
                <Label htmlFor="instagram">Instagram</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="tiktok" />
                <Label htmlFor="tiktok">TikTok</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="twitter" />
                <Label htmlFor="twitter">Twitter</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="facebook" />
                <Label htmlFor="facebook">Facebook</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="email" />
                <Label htmlFor="email">Email Newsletter</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="other" />
                <Label htmlFor="other">Other</Label>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="audience-size">Audience Size</Label>
              <Select defaultValue="10000-50000">
                <SelectTrigger id="audience-size">
                  <SelectValue placeholder="Select audience size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="under-1000">Under 1,000</SelectItem>
                  <SelectItem value="1000-10000">1,000 - 10,000</SelectItem>
                  <SelectItem value="10000-50000">10,000 - 50,000</SelectItem>
                  <SelectItem value="50000-100000">50,000 - 100,000</SelectItem>
                  <SelectItem value="over-100000">Over 100,000</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="content-frequency">Content Frequency</Label>
              <Select defaultValue="weekly">
                <SelectTrigger id="content-frequency">
                  <SelectValue placeholder="Select content frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="biweekly">Bi-weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="occasionally">Occasionally</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Category Specializations</h3>

          <div className="space-y-2">
            <Label>Select your specialized product categories</Label>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="electronics" defaultChecked />
                <Label htmlFor="electronics">Electronics</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="fashion" />
                <Label htmlFor="fashion">Fashion</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="home" />
                <Label htmlFor="home">Home & Kitchen</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="beauty" />
                <Label htmlFor="beauty">Beauty & Personal Care</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="sports" defaultChecked />
                <Label htmlFor="sports">Sports & Outdoors</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="toys" />
                <Label htmlFor="toys">Toys & Games</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="books" />
                <Label htmlFor="books">Books & Media</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="health" />
                <Label htmlFor="health">Health & Wellness</Label>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Notification Preferences</h3>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox id="email-commission" defaultChecked />
              <Label htmlFor="email-commission">Email notifications for commissions</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="email-payment" defaultChecked />
              <Label htmlFor="email-payment">Email notifications for payments</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="email-promotions" defaultChecked />
              <Label htmlFor="email-promotions">Email notifications for new promotions</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="email-products" />
              <Label htmlFor="email-products">Email notifications for new products</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="email-newsletter" />
              <Label htmlFor="email-newsletter">Affiliate newsletter</Label>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Cancel</Button>
        <Button>Save Changes</Button>
      </CardFooter>
    </Card>
  )
}
