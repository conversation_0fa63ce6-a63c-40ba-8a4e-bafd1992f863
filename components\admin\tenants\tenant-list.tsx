"use client"

import { <PERSON><PERSON>ontent } from "@/components/ui/card"

import { CardTitle } from "@/components/ui/card"

import { CardHeader } from "@/components/ui/card"

import { Card } from "@/components/ui/card"

import { useState, useEffect, useCallback } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  MoreHorizontal,
  Search,
  ArrowUpDown,
  Eye,
  Edit,
  LogIn,
  Ban,
  CheckCircle,
  RefreshCw,
  Trash2,
} from "lucide-react"
import { useRouter } from "next/navigation"
import { useConfirmation } from "@/components/admin/ui/confirmation-dialog"
import { useNotifications } from "@/components/providers/notifications-provider"
import { Skeleton } from "@/components/ui/skeleton"
import { format } from "date-fns"
import { id } from "date-fns/locale"
import { LoginAsTenantModal } from "./login-as-tenant-modal"
import {
  getAllTenants,
  suspendTenant,
  reactivateTenant,
  deleteTenant,
  TENANTS_STORAGE_KEY,
} from "@/lib/services/tenant-service"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import type { DateRange } from "react-day-picker"
import { isWithinInterval, parseISO } from "date-fns"

interface Tenant {
  id: string
  name: string
  domain: string
  plan: string
  status: string
  storeCount: number
  userCount: number
  revenue: string
  createdAt: string
  suspensionEndDate?: string
}

interface TenantListProps {
  initialTenants?: Tenant[]
  isLoading?: boolean
  onRefresh?: () => void
  onDelete?: (id: string) => Promise<void>
}

export function TenantList({
  initialTenants = [],
  isLoading: initialLoading = false,
  onRefresh,
  onDelete,
}: TenantListProps) {
  const router = useRouter()
  const { confirm } = useConfirmation()
  const { addNotification } = useNotifications()
  const [tenants, setTenants] = useState<Tenant[]>(initialTenants)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [planFilter, setPlanFilter] = useState("all")
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined)
  const [isLoading, setIsLoading] = useState(initialLoading)
  const [isActionLoading, setIsActionLoading] = useState(false)
  const [loginModalOpen, setLoginModalOpen] = useState(false)
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null)

  // Format date function
  const formatDate = (dateStr: string) => {
    if (!dateStr) return ""
    try {
      return format(new Date(dateStr), "dd MMMM yyyy", { locale: id })
    } catch (error) {
      return dateStr
    }
  }

  // Load tenants
  const loadTenants = useCallback(async () => {
    try {
      setIsLoading(true)
      const data = (await getAllTenants()) as Tenant[]
      setTenants(data)
    } catch (error) {
      console.error("Error loading tenants:", error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Load tenants on initial render if no initialTenants provided
  useEffect(() => {
    if (initialTenants.length === 0) {
      loadTenants()
    }
  }, [initialTenants, loadTenants])

  // Listen for storage events
  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event.key === TENANTS_STORAGE_KEY) {
        loadTenants()
      }
    }

    window.addEventListener("storage", handleStorageChange)
    return () => {
      window.removeEventListener("storage", handleStorageChange)
    }
  }, [loadTenants])

  // Filter function for a single tenant
  const filterTenant = useCallback(
    (tenant: Tenant) => {
      const matchesSearch =
        tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tenant.domain.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesStatus = statusFilter === "all" || tenant.status === statusFilter
      const matchesPlan = planFilter === "all" || tenant.plan === planFilter

      // Filter berdasarkan rentang tanggal
      let matchesDateRange = true
      if (dateRange?.from && dateRange?.to) {
        try {
          const createdDate = parseISO(tenant.createdAt)
          // Set waktu ke 00:00:00 untuk from dan 23:59:59 untuk to
          const fromDate = new Date(dateRange.from)
          fromDate.setHours(0, 0, 0, 0)

          const toDate = new Date(dateRange.to)
          toDate.setHours(23, 59, 59, 999)

          matchesDateRange = isWithinInterval(createdDate, {
            start: fromDate,
            end: toDate,
          })
        } catch (error) {
          console.error("Error parsing date:", error)
          matchesDateRange = true
        }
      }

      return matchesSearch && matchesStatus && matchesPlan && matchesDateRange
    },
    [searchTerm, statusFilter, planFilter, dateRange],
  )

  // Apply filter to get filtered tenants
  const filteredTenants = tenants.filter(filterTenant)

  // Calculate statistics based on filtered tenants
  const activeTenants = filteredTenants.filter((t) => t.status === "active").length
  const totalRevenue = filteredTenants.reduce((sum, tenant) => {
    const revenue = Number.parseFloat(tenant.revenue.replace("$", "").replace(",", ""))
    return sum + revenue
  }, 0)

  // Status badge renderer
  const renderStatusBadge = (tenant: Tenant) => {
    const { status, suspensionEndDate } = tenant
    const isTemporarySuspension = status === "suspended" && suspensionEndDate

    switch (status) {
      case "active":
        return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>
      case "suspended":
        if (isTemporarySuspension) {
          // Format tanggal untuk tooltip
          const formattedDate = formatDate(suspensionEndDate!)

          return (
            <Badge
              className="bg-amber-500 hover:bg-amber-600"
              title={`Tenant akan diaktifkan kembali pada ${formattedDate}`}
            >
              Suspended
            </Badge>
          )
        } else {
          return (
            <Badge className="bg-red-500 hover:bg-red-600" title="Tenant ditangguhkan secara permanen">
              Suspended
            </Badge>
          )
        }
      case "pending":
        return <Badge className="bg-yellow-500 hover:bg-yellow-600">Pending</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // Auto-activation badge renderer - tidak perlu menampilkan badge terpisah
  const renderAutoActivationBadge = (tenant: Tenant) => {
    // Kita tidak menampilkan badge terpisah lagi, karena sudah ada tooltip pada badge status
    return null
  }

  // Handle row click
  const handleRowClick = (tenant: any) => {
    router.push(`/admin/dashboard/tenants/${tenant.id}`)
  }

  // Handle tenant action
  const handleTenantAction = (action: string, tenant: any) => {
    switch (action) {
      case "view":
        router.push(`/admin/dashboard/tenants/${tenant.id}`)
        break
      case "edit":
        router.push(`/admin/dashboard/tenants/${tenant.id}/edit`)
        break
      case "login":
        // Buka modal login as tenant
        setSelectedTenant(tenant)
        setLoginModalOpen(true)
        break
      case "suspend":
        confirm({
          title: "Suspend Tenant",
          description: `Apakah Anda yakin ingin menangguhkan ${tenant.name}? Ini akan mencegah semua pengguna mengakses tenant ini.`,
          confirmLabel: "Suspend",
          cancelLabel: "Batal",
          variant: "destructive",
          onConfirm: async () => {
            setIsActionLoading(true)
            try {
              await suspendTenant(tenant.id, "Suspended by admin")

              addNotification({
                message: `${tenant.name} telah ditangguhkan.`,
                type: "success",
              })

              // Reload tenants
              await loadTenants()

              // Call onRefresh if provided
              if (onRefresh) onRefresh()
            } catch (error) {
              console.error("Error suspending tenant:", error)
              addNotification({
                message: `Gagal menangguhkan ${tenant.name}.`,
                type: "error",
              })
            } finally {
              setIsActionLoading(false)
            }
          },
        })
        break
      case "activate":
        confirm({
          title: "Aktifkan Tenant",
          description: `Apakah Anda yakin ingin mengaktifkan ${tenant.name}?`,
          confirmLabel: "Aktifkan",
          cancelLabel: "Batal",
          onConfirm: async () => {
            setIsActionLoading(true)
            try {
              await reactivateTenant(tenant.id, "Activated by admin")

              addNotification({
                message: `${tenant.name} telah diaktifkan.`,
                type: "success",
              })

              // Reload tenants
              await loadTenants()

              // Call onRefresh if provided
              if (onRefresh) onRefresh()
            } catch (error) {
              console.error("Error activating tenant:", error)
              addNotification({
                message: `Gagal mengaktifkan ${tenant.name}.`,
                type: "error",
              })
            } finally {
              setIsActionLoading(false)
            }
          },
        })
        break
      case "delete":
        confirm({
          title: "Hapus Tenant",
          description: `Apakah Anda yakin ingin menghapus ${tenant.name}? Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data terkait.`,
          confirmLabel: "Hapus",
          cancelLabel: "Batal",
          variant: "destructive",
          onConfirm: async () => {
            try {
              setIsActionLoading(true)

              // Use service function if available, otherwise use provided onDelete
              if (onDelete) {
                await onDelete(tenant.id)
              } else {
                await deleteTenant(tenant.id)
              }

              addNotification({
                message: `${tenant.name} telah berhasil dihapus.`,
                type: "success",
              })

              // Reload tenants
              await loadTenants()

              // Call onRefresh if provided
              if (onRefresh) onRefresh()
            } catch (error) {
              console.error("Error deleting tenant:", error)
              addNotification({
                message: `Gagal menghapus ${tenant.name}. Silakan coba lagi.`,
                type: "error",
              })
            } finally {
              setIsActionLoading(false)
            }
          },
        })
        break
      default:
        break
    }
  }

  // Handle refresh
  const handleRefresh = async () => {
    setIsLoading(true)
    try {
      await loadTenants()

      addNotification({
        message: "Data tenant berhasil dimuat ulang",
        type: "success",
      })

      // Call onRefresh if provided
      if (onRefresh) onRefresh()
    } catch (error) {
      console.error("Error refreshing tenants:", error)
      addNotification({
        message: "Gagal memuat ulang data tenant",
        type: "error",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Render loading skeleton
  if (isLoading && tenants.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <Skeleton className="h-10 w-full max-w-sm" />
          <div className="flex flex-col gap-4 sm:flex-row">
            <Skeleton className="h-10 w-[180px]" />
            <Skeleton className="h-10 w-[180px]" />
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Name</TableHead>
                <TableHead>Domain</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-center">Store Count</TableHead>
                <TableHead className="text-center">User Count</TableHead>
                <TableHead className="text-right">Revenue</TableHead>
                <TableHead>Created Date</TableHead>
                <TableHead className="w-[50px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array(5)
                .fill(0)
                .map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Skeleton className="h-5 w-32" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-5 w-40" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-5 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-5 w-20" />
                    </TableCell>
                    <TableCell className="text-center">
                      <Skeleton className="h-5 w-8 mx-auto" />
                    </TableCell>
                    <TableCell className="text-center">
                      <Skeleton className="h-5 w-8 mx-auto" />
                    </TableCell>
                    <TableCell className="text-right">
                      <Skeleton className="h-5 w-16 ml-auto" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-5 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-8 w-8 rounded-full" />
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search tenants..."
            className="w-full pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex flex-col gap-4 sm:flex-row">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
            </SelectContent>
          </Select>
          <Select value={planFilter} onValueChange={setPlanFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by plan" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Plans</SelectItem>
              <SelectItem value="Basic">Basic</SelectItem>
              <SelectItem value="Professional">Professional</SelectItem>
              <SelectItem value="Enterprise">Enterprise</SelectItem>
            </SelectContent>
          </Select>
          <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
          <Button
            variant="outline"
            size="icon"
            onClick={handleRefresh}
            disabled={isLoading || isActionLoading}
            title="Refresh"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {dateRange?.from && dateRange?.to && (
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredTenants.length}</div>
              <p className="text-xs text-muted-foreground">
                Periode: {format(dateRange.from, "dd MMM yyyy", { locale: id })} -{" "}
                {format(dateRange.to, "dd MMM yyyy", { locale: id })}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tenant Aktif</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeTenants}</div>
              <p className="text-xs text-muted-foreground">
                {((activeTenants / (filteredTenants.length || 1)) * 100).toFixed(0)}% dari total
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${totalRevenue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Rata-rata: ${(totalRevenue / (filteredTenants.length || 1)).toFixed(0)}/tenant
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">
                <div className="flex items-center gap-1">
                  Name
                  <ArrowUpDown className="h-3 w-3" />
                </div>
              </TableHead>
              <TableHead>Domain</TableHead>
              <TableHead>Plan</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-center">Store Count</TableHead>
              <TableHead className="text-center">User Count</TableHead>
              <TableHead className="text-right">Revenue</TableHead>
              <TableHead>Created Date</TableHead>
              <TableHead className="w-[80px] text-center">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTenants.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  No tenants found.
                </TableCell>
              </TableRow>
            ) : (
              filteredTenants.map((tenant) => (
                <TableRow
                  key={tenant.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleRowClick(tenant)}
                >
                  <TableCell className="font-medium">{tenant.name}</TableCell>
                  <TableCell>{tenant.domain}</TableCell>
                  <TableCell>{tenant.plan}</TableCell>
                  <TableCell>
                    <div className="flex flex-col gap-1">
                      {renderStatusBadge(tenant)}
                      {renderAutoActivationBadge(tenant)}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">{tenant.storeCount}</TableCell>
                  <TableCell className="text-center">{tenant.userCount}</TableCell>
                  <TableCell className="text-right">{tenant.revenue}</TableCell>
                  <TableCell>{tenant.createdAt}</TableCell>
                  <TableCell onClick={(e) => e.stopPropagation()} className="text-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0" disabled={isLoading || isActionLoading}>
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleTenantAction("view", tenant)}>
                          <Eye className="mr-2 h-4 w-4" /> View
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleTenantAction("edit", tenant)}>
                          <Edit className="mr-2 h-4 w-4" /> Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleTenantAction("login", tenant)}>
                          <LogIn className="mr-2 h-4 w-4" /> Login as
                        </DropdownMenuItem>
                        {tenant.status === "active" ? (
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleTenantAction("suspend", tenant)}
                          >
                            <Ban className="mr-2 h-4 w-4" /> Suspend
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem
                            className="text-green-600"
                            onClick={() => handleTenantAction("activate", tenant)}
                          >
                            <CheckCircle className="mr-2 h-4 w-4" /> Activate
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem className="text-red-600" onClick={() => handleTenantAction("delete", tenant)}>
                          <Trash2 className="mr-2 h-4 w-4" /> Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Modal Login as Tenant */}
      {selectedTenant && (
        <LoginAsTenantModal tenant={selectedTenant} open={loginModalOpen} onOpenChange={setLoginModalOpen} />
      )}
    </div>
  )
}

export default TenantList
