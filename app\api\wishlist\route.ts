import { NextRequest, NextResponse } from 'next/server';

// Sample wishlist data (akan diganti dengan database nanti)
let wishlistItems = [
  {
    id: "1",
    name: "Kemeja Casual",
    price: 150000,
    originalPrice: 180000,
    image: "/placeholder.svg",
    inStock: true,
    store: "Fashion Store",
    storeId: "store-1",
    dateAdded: "2025-05-10",
    priceDropped: true,
    productId: "product-1"
  },
  {
    id: "2",
    name: "Sepatu Sneakers",
    price: 350000,
    originalPrice: 350000,
    image: "/assorted-shoes.png",
    inStock: true,
    store: "Footwear Shop",
    storeId: "store-2",
    dateAdded: "2025-05-08",
    priceDropped: false,
    productId: "product-2"
  },
  {
    id: "3",
    name: "<PERSON><PERSON>",
    price: 250000,
    originalPrice: 250000,
    image: "/colorful-backpack-on-wooden-table.png",
    inStock: false,
    store: "Bag Collection",
    storeId: "store-3",
    dateAdded: "2025-05-05",
    priceDropped: false,
    productId: "product-3"
  },
  {
    id: "4",
    name: "<PERSON> Tangan",
    price: 450000,
    originalPrice: 500000,
    image: "/wrist-watch-close-up.png",
    inStock: true,
    store: "Watch World",
    storeId: "store-4",
    dateAdded: "2025-05-01",
    priceDropped: true,
    productId: "product-4"
  },
  {
    id: "5",
    name: "Hoodie Premium",
    price: 300000,
    originalPrice: 300000,
    image: "/placeholder.svg",
    inStock: true,
    store: "Urban Clothing",
    storeId: "store-5",
    dateAdded: "2025-04-28",
    priceDropped: false,
    productId: "product-5"
  },
  {
    id: "6",
    name: "Celana Jeans",
    price: 280000,
    originalPrice: 320000,
    image: "/placeholder.svg",
    inStock: true,
    store: "Denim Store",
    storeId: "store-6",
    dateAdded: "2025-04-25",
    priceDropped: true,
    productId: "product-6"
  },
  {
    id: "7",
    name: "Topi Baseball",
    price: 75000,
    originalPrice: 75000,
    image: "/placeholder.svg",
    inStock: true,
    store: "Accessories Shop",
    storeId: "store-7",
    dateAdded: "2025-04-20",
    priceDropped: false,
    productId: "product-7"
  },
  {
    id: "8",
    name: "Kaos Polos",
    price: 100000,
    originalPrice: 100000,
    image: "/placeholder.svg",
    inStock: true,
    store: "Basic Apparel",
    storeId: "store-8",
    dateAdded: "2025-04-15",
    priceDropped: false,
    productId: "product-8"
  },
];

// GET - Mendapatkan semua wishlist items atau difilter berdasarkan query
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  const storeId = searchParams.get('storeId');
  const inStock = searchParams.get('inStock');
  const priceDropped = searchParams.get('priceDropped');
  
  let filteredItems = [...wishlistItems];
  
  // Filter berdasarkan query pencarian
  if (query) {
    const lowerQuery = query.toLowerCase();
    filteredItems = filteredItems.filter(item => 
      item.name.toLowerCase().includes(lowerQuery) || 
      item.store.toLowerCase().includes(lowerQuery)
    );
  }
  
  // Filter berdasarkan store
  if (storeId) {
    filteredItems = filteredItems.filter(item => item.storeId === storeId);
  }
  
  // Filter berdasarkan status stok
  if (inStock !== null) {
    const inStockBool = inStock === 'true';
    filteredItems = filteredItems.filter(item => item.inStock === inStockBool);
  }
  
  // Filter berdasarkan status penurunan harga
  if (priceDropped !== null) {
    const priceDroppedBool = priceDropped === 'true';
    filteredItems = filteredItems.filter(item => item.priceDropped === priceDroppedBool);
  }
  
  return NextResponse.json(filteredItems);
}

// POST - Menambahkan item baru ke wishlist
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validasi dasar
    if (!body.productId || !body.name) {
      return NextResponse.json({ error: 'productId dan name diperlukan' }, { status: 400 });
    }
    
    // Cek apakah produk sudah ada di wishlist
    const existingItem = wishlistItems.find(item => item.productId === body.productId);
    if (existingItem) {
      return NextResponse.json({ error: 'Produk sudah ada di wishlist', item: existingItem }, { status: 409 });
    }
    
    // Buat ID baru
    const newItemId = (Math.max(...wishlistItems.map(item => parseInt(item.id))) + 1).toString();
    
    // Buat item wishlist baru
    const newItem = {
      id: newItemId,
      name: body.name,
      price: body.price || 0,
      originalPrice: body.originalPrice || body.price || 0,
      image: body.image || "/placeholder.svg",
      inStock: body.inStock !== undefined ? body.inStock : true,
      store: body.store || "Unknown Store",
      storeId: body.storeId || "unknown",
      dateAdded: new Date().toISOString().split('T')[0],
      priceDropped: body.priceDropped !== undefined ? body.priceDropped : false,
      productId: body.productId
    };
    
    // Tambahkan ke array wishlist
    wishlistItems.unshift(newItem);
    
    return NextResponse.json(newItem, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: 'Terjadi kesalahan server' }, { status: 500 });
  }
} 