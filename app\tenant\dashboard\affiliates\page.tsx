"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Users,
  Link2,
  DollarSign,
  TrendingUp,
  Eye,
  PencilIcon,
  Trash,
  MoreHorizontal,
  CalendarDays,
  UserPlus,
  FileText,
  Settings
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk program afiliasi
const affiliatePrograms = [
  {
    id: "aff-001",
    name: "Program Referral Standar",
    status: "active",
    affiliatesCount: 120,
    commissionRate: 10,
    totalSales: 45000000,
    totalCommission: 4500000,
    clicks: 2500,
    conversions: 150,
    startDate: "2023-10-01",
    endDate: null
  },
  {
    id: "aff-002",
    name: "Program Khusus Influencer",
    status: "active",
    affiliatesCount: 25,
    commissionRate: 15,
    totalSales: 30000000,
    totalCommission: 4500000,
    clicks: 1800,
    conversions: 90,
    startDate: "2023-11-15",
    endDate: "2024-02-15"
  },
  {
    id: "aff-003",
    name: "Program Afiliasi Blog",
    status: "draft",
    affiliatesCount: 0,
    commissionRate: 8,
    totalSales: 0,
    totalCommission: 0,
    clicks: 0,
    conversions: 0,
    startDate: null,
    endDate: null
  },
  {
    id: "aff-004",
    name: "Program Referral Musiman",
    status: "ended",
    affiliatesCount: 85,
    commissionRate: 12,
    totalSales: 25000000,
    totalCommission: 3000000,
    clicks: 1200,
    conversions: 80,
    startDate: "2023-08-01",
    endDate: "2023-09-30"
  }
]

// Fungsi untuk badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "active":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>
    case "draft":
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>
    case "ended":
      return <Badge variant="outline" className="bg-red-100 text-red-800">Ended</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Fungsi untuk format persentase
function formatPercentage(value: number) {
  return `${value}%`
}

export default function AffiliatesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Filter affiliate programs
  const filteredPrograms = affiliatePrograms.filter(program => {
    const matchesSearch = program.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || program.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Statistik
  const stats = {
    totalPrograms: affiliatePrograms.length,
    activePrograms: affiliatePrograms.filter(p => p.status === "active").length,
    totalAffiliates: affiliatePrograms.reduce((sum, p) => sum + p.affiliatesCount, 0),
    totalSales: affiliatePrograms.reduce((sum, p) => sum + p.totalSales, 0),
    totalCommission: affiliatePrograms.reduce((sum, p) => sum + p.totalCommission, 0),
    totalClicks: affiliatePrograms.reduce((sum, p) => sum + p.clicks, 0),
    totalConversions: affiliatePrograms.reduce((sum, p) => sum + p.conversions, 0),
    conversionRate: affiliatePrograms.reduce((sum, p) => sum + p.conversions, 0) / 
                    affiliatePrograms.reduce((sum, p) => sum + p.clicks, 0) * 100 || 0
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Affiliate Management</h1>
            <p className="text-muted-foreground">
              Kelola program afiliasi, performa, dan komisi
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/tenant/dashboard/affiliates/applications">
              <UserPlus className="h-4 w-4 mr-2" />
              Applications
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/tenant/dashboard/affiliates/commission">
              <DollarSign className="h-4 w-4 mr-2" />
              Commission
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/tenant/dashboard/affiliates/materials">
              <FileText className="h-4 w-4 mr-2" />
              Materials
            </Link>
          </Button>
          <Button>
            <Link2 className="h-4 w-4 mr-2" />
            Buat Program
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Affiliates</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalAffiliates}</div>
            <p className="text-xs text-muted-foreground">
              Dalam {stats.activePrograms} program aktif
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatCurrency(stats.totalSales)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Commission</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalCommission)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{formatPercentage(Number(stats.conversionRate.toFixed(2)))}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalConversions} dari {stats.totalClicks} klik
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari nama program..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="active">Active</option>
              <option value="draft">Draft</option>
              <option value="ended">Ended</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Affiliate Programs List */}
      <div className="space-y-4">
        {filteredPrograms.map((program) => (
          <Card key={program.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex flex-col gap-4">
                <div className="flex flex-col md:flex-row justify-between gap-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-semibold text-lg">{program.name}</span>
                      {getStatusBadge(program.status)}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      {program.startDate && (
                        <>
                          <CalendarDays className="h-3.5 w-3.5" />
                          <span>
                            {program.startDate} {program.endDate ? `- ${program.endDate}` : "(No End Date)"}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">{formatPercentage(program.commissionRate)} komisi</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">{program.affiliatesCount} afiliasi</span>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Total Sales</p>
                    <p className="font-semibold text-blue-600">{formatCurrency(program.totalSales)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Total Commission</p>
                    <p className="font-semibold text-green-600">{formatCurrency(program.totalCommission)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Clicks</p>
                    <p className="font-semibold">{program.clicks}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Conversions</p>
                    <p className="font-semibold">{program.conversions} ({(program.conversions / program.clicks * 100 || 0).toFixed(2)}%)</p>
                  </div>
                </div>
                <div className="flex gap-2 pt-2 border-t mt-2">
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button size="sm" variant="outline">
                    <PencilIcon className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button size="sm" variant="outline">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Button>
                  <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                    <Trash className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                  <Button size="sm" variant="ghost">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        {filteredPrograms.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <Link2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Tidak ada program afiliasi ditemukan</h3>
              <p className="text-muted-foreground mb-4">
                Tidak ada program yang cocok dengan filter Anda
              </p>
              <Button>
                <Link2 className="h-4 w-4 mr-2" />
                Buat Program
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
} 