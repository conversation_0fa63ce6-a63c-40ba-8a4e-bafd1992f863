import { NextRequest, NextResponse } from 'next/server';
import { productCategoryService } from '@/lib/services/product-categories';

const productCategories = [
  // Root Categories
  {
    name: 'Elektronik',
    slug: 'elektronik',
    description: 'Perangkat elektronik dan gadget terbaru',
    color: '#3B82F6',
    sort_order: 1,
    is_active: true,
    meta_title: 'Elektronik - Gadget dan Perangkat Terbaru',
    meta_description: 'Temukan berbagai perangkat elektronik dan gadget terbaru dengan kualitas terbaik',
    meta_keywords: ['elektronik', 'gadget', 'smartphone', 'laptop']
  },
  {
    name: 'Fashion',
    slug: 'fashion',
    description: 'Pakaian dan aksesoris fashion terkini',
    color: '#EC4899',
    sort_order: 2,
    is_active: true,
    meta_title: 'Fashion - Pakaian dan Aksesoris Terkini',
    meta_description: 'Koleksi fashion terlengkap untuk pria dan wanita',
    meta_keywords: ['fashion', 'pakaian', 'baju', 'aksesoris']
  },
  {
    name: '<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>',
    slug: 'rumah-taman',
    description: 'Peralatan rumah tangga dan taman',
    color: '#10B981',
    sort_order: 3,
    is_active: true,
    meta_title: 'Rumah & Taman - Peralatan Rumah Tangga',
    meta_description: 'Lengkapi kebutuhan rumah dan taman Anda',
    meta_keywords: ['rumah', 'taman', 'furniture', 'dekorasi']
  },
  {
    name: 'Olahraga',
    slug: 'olahraga',
    description: 'Peralatan dan perlengkapan olahraga',
    color: '#F59E0B',
    sort_order: 4,
    is_active: true,
    meta_title: 'Olahraga - Peralatan dan Perlengkapan',
    meta_description: 'Peralatan olahraga berkualitas untuk berbagai aktivitas',
    meta_keywords: ['olahraga', 'fitness', 'sepatu', 'jersey']
  },
  {
    name: 'Kesehatan & Kecantikan',
    slug: 'kesehatan-kecantikan',
    description: 'Produk kesehatan dan kecantikan',
    color: '#8B5CF6',
    sort_order: 5,
    is_active: true,
    meta_title: 'Kesehatan & Kecantikan - Produk Perawatan',
    meta_description: 'Produk kesehatan dan kecantikan untuk perawatan diri',
    meta_keywords: ['kesehatan', 'kecantikan', 'skincare', 'makeup']
  }
]

const subCategories = [
  // Elektronik subcategories
  {
    name: 'Smartphone',
    slug: 'smartphone',
    description: 'Smartphone dan aksesoris',
    parent_slug: 'elektronik',
    color: '#3B82F6',
    sort_order: 1,
    is_active: true,
    meta_title: 'Smartphone - HP dan Aksesoris Terbaru',
    meta_description: 'Koleksi smartphone terbaru dari berbagai brand',
    meta_keywords: ['smartphone', 'hp', 'android', 'iphone']
  },
  {
    name: 'Laptop & Komputer',
    slug: 'laptop-komputer',
    description: 'Laptop, komputer, dan aksesoris',
    parent_slug: 'elektronik',
    color: '#3B82F6',
    sort_order: 2,
    is_active: true,
    meta_title: 'Laptop & Komputer - Perangkat Computing',
    meta_description: 'Laptop dan komputer untuk berbagai kebutuhan',
    meta_keywords: ['laptop', 'komputer', 'pc', 'notebook']
  },
  {
    name: 'Audio & Video',
    slug: 'audio-video',
    description: 'Perangkat audio dan video',
    parent_slug: 'elektronik',
    color: '#3B82F6',
    sort_order: 3,
    is_active: true,
    meta_title: 'Audio & Video - Perangkat Multimedia',
    meta_description: 'Perangkat audio dan video berkualitas tinggi',
    meta_keywords: ['audio', 'video', 'speaker', 'headphone']
  },

  // Fashion subcategories
  {
    name: 'Pakaian Pria',
    slug: 'pakaian-pria',
    description: 'Koleksi pakaian untuk pria',
    parent_slug: 'fashion',
    color: '#EC4899',
    sort_order: 1,
    is_active: true,
    meta_title: 'Pakaian Pria - Fashion Pria Terkini',
    meta_description: 'Koleksi pakaian pria dengan gaya terkini',
    meta_keywords: ['pakaian pria', 'baju pria', 'kemeja', 'celana']
  },
  {
    name: 'Pakaian Wanita',
    slug: 'pakaian-wanita',
    description: 'Koleksi pakaian untuk wanita',
    parent_slug: 'fashion',
    color: '#EC4899',
    sort_order: 2,
    is_active: true,
    meta_title: 'Pakaian Wanita - Fashion Wanita Terkini',
    meta_description: 'Koleksi pakaian wanita dengan gaya terkini',
    meta_keywords: ['pakaian wanita', 'baju wanita', 'dress', 'blouse']
  },
  {
    name: 'Sepatu',
    slug: 'sepatu',
    description: 'Koleksi sepatu untuk pria dan wanita',
    parent_slug: 'fashion',
    color: '#EC4899',
    sort_order: 3,
    is_active: true,
    meta_title: 'Sepatu - Koleksi Sepatu Terlengkap',
    meta_description: 'Sepatu berkualitas untuk berbagai aktivitas',
    meta_keywords: ['sepatu', 'shoes', 'sneakers', 'sandal']
  },

  // Rumah & Taman subcategories
  {
    name: 'Furniture',
    slug: 'furniture',
    description: 'Perabotan rumah tangga',
    parent_slug: 'rumah-taman',
    color: '#10B981',
    sort_order: 1,
    is_active: true,
    meta_title: 'Furniture - Perabotan Rumah Tangga',
    meta_description: 'Furniture berkualitas untuk rumah Anda',
    meta_keywords: ['furniture', 'meja', 'kursi', 'lemari']
  },
  {
    name: 'Dekorasi',
    slug: 'dekorasi',
    description: 'Dekorasi dan hiasan rumah',
    parent_slug: 'rumah-taman',
    color: '#10B981',
    sort_order: 2,
    is_active: true,
    meta_title: 'Dekorasi - Hiasan Rumah',
    meta_description: 'Dekorasi cantik untuk mempercantik rumah',
    meta_keywords: ['dekorasi', 'hiasan', 'ornamen', 'wall art']
  },

  // Olahraga subcategories
  {
    name: 'Fitness',
    slug: 'fitness',
    description: 'Peralatan fitness dan gym',
    parent_slug: 'olahraga',
    color: '#F59E0B',
    sort_order: 1,
    is_active: true,
    meta_title: 'Fitness - Peralatan Gym dan Fitness',
    meta_description: 'Peralatan fitness untuk latihan di rumah',
    meta_keywords: ['fitness', 'gym', 'dumbbell', 'treadmill']
  },
  {
    name: 'Sepatu Olahraga',
    slug: 'sepatu-olahraga',
    description: 'Sepatu untuk berbagai olahraga',
    parent_slug: 'olahraga',
    color: '#F59E0B',
    sort_order: 2,
    is_active: true,
    meta_title: 'Sepatu Olahraga - Sepatu Sport Terbaik',
    meta_description: 'Sepatu olahraga berkualitas untuk performa terbaik',
    meta_keywords: ['sepatu olahraga', 'sepatu sport', 'running shoes', 'basketball shoes']
  }
]

// POST - Seed product categories data
export async function POST(request: NextRequest) {
  try {
    // Check if categories already exist
    const existingCategories = await productCategoryService.getCategories()
    
    if (existingCategories.length > 0) {
      return NextResponse.json(
        { message: 'Product categories already exist', count: existingCategories.length },
        { status: 200 }
      )
    }
    
    // Create root categories first
    const createdRootCategories = []
    const rootErrors = []
    
    for (const category of productCategories) {
      try {
        const created = await productCategoryService.createCategory(category)
        createdRootCategories.push(created)
      } catch (error) {
        console.error(`Error creating category ${category.name}:`, error)
        rootErrors.push({
          name: category.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    // Create subcategories
    const createdSubCategories = []
    const subErrors = []
    
    for (const subCategory of subCategories) {
      try {
        // Find parent category
        const parentCategory = createdRootCategories.find(cat => cat.slug === subCategory.parent_slug)
        if (!parentCategory) {
          throw new Error(`Parent category not found: ${subCategory.parent_slug}`)
        }
        
        const categoryData = {
          ...subCategory,
          parent_id: parentCategory.id
        }
        delete categoryData.parent_slug
        
        const created = await productCategoryService.createCategory(categoryData)
        createdSubCategories.push(created)
      } catch (error) {
        console.error(`Error creating subcategory ${subCategory.name}:`, error)
        subErrors.push({
          name: subCategory.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    return NextResponse.json({
      message: 'Product categories seeded successfully',
      rootCategories: createdRootCategories.length,
      subCategories: createdSubCategories.length,
      errors: rootErrors.length + subErrors.length,
      errorDetails: [...rootErrors, ...subErrors]
    }, { status: 201 })
    
  } catch (error) {
    console.error('Error seeding product categories:', error)
    const errorMessage = error instanceof Error ? error.message : 'Failed to seed product categories'
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}
