import type { Metadata } from "next"
import { Suspense } from "react"

import { StoreTemplates } from "@/components/admin/stores/store-templates"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export const metadata: Metadata = {
  title: "Store Templates | Admin Dashboard",
  description: "Manage store templates for different tenant plans",
}

export default function StoreTemplatesPage() {
  return (
    <div className="flex flex-col gap-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Store Templates</h1>
        <p className="text-muted-foreground">Manage templates that tenants can use for their stores.</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Template Gallery</CardTitle>
          <CardDescription>Browse, create, and manage store templates.</CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
            <StoreTemplates />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
