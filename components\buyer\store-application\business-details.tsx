"use client"

import type React from "react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { File, Upload, X } from "lucide-react"
import { useState } from "react"

interface BusinessDetailsProps {
  formData: any
  onChange: (field: string, value: any) => void
  onSubFieldChange: (parent: string, field: string, value: any) => void
}

export function BusinessDetails({ formData, onChange, onSubFieldChange }: BusinessDetailsProps) {
  const [ownerIdPreview, setOwnerIdPreview] = useState<string | null>(null)
  const [businessDocs, setBusinessDocs] = useState<{ name: string; size: string }[]>([])

  const handleIdCardChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      onChange("ownerIdCard", file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        if (e.target?.result) {
          setOwnerIdPreview(e.target.result as string)
        }
      }
      reader.readAsDataURL(file)
    }
  }

  const handleBusinessDocsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files)
      const updatedFiles = [...(formData.businessDocuments || []), ...newFiles]
      onChange("businessDocuments", updatedFiles)

      // Update file list display
      const newDocs = newFiles.map((file) => ({
        name: file.name,
        size: formatFileSize(file.size),
      }))

      setBusinessDocs((prev) => [...prev, ...newDocs])
    }
  }

  const removeDocument = (index: number) => {
    const updatedFiles = [...(formData.businessDocuments || [])]
    updatedFiles.splice(index, 1)
    onChange("businessDocuments", updatedFiles)

    const updatedDocs = [...businessDocs]
    updatedDocs.splice(index, 1)
    setBusinessDocs(updatedDocs)
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + " B"
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB"
    else return (bytes / 1048576).toFixed(1) + " MB"
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Detail Bisnis</h2>
        <p className="text-gray-500">Berikan informasi legal dan detail bisnis Anda untuk verifikasi.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label htmlFor="businessRegistration">Nomor Registrasi Bisnis</Label>
          <Input
            id="businessRegistration"
            value={formData.businessRegistration}
            onChange={(e) => onChange("businessRegistration", e.target.value)}
            placeholder="Contoh: NIB.1234567890"
          />
          <p className="text-xs text-gray-500">Kosongkan jika Anda adalah penjual individu.</p>
        </div>

        <div className="space-y-3">
          <Label htmlFor="taxId">
            NPWP <span className="text-red-500">*</span>
          </Label>
          <Input
            id="taxId"
            value={formData.taxId}
            onChange={(e) => onChange("taxId", e.target.value)}
            placeholder="Contoh: 12.345.678.9-012.000"
            required
          />
        </div>

        <div className="space-y-3 md:col-span-2">
          <h3 className="font-medium text-base">
            Alamat Bisnis <span className="text-red-500">*</span>
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="street">Jalan</Label>
              <Input
                id="street"
                value={formData.businessAddress?.street || ""}
                onChange={(e) => onSubFieldChange("businessAddress", "street", e.target.value)}
                placeholder="Jalan dan nomor"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="city">Kota/Kabupaten</Label>
              <Input
                id="city"
                value={formData.businessAddress?.city || ""}
                onChange={(e) => onSubFieldChange("businessAddress", "city", e.target.value)}
                placeholder="Kota atau kabupaten"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="province">Provinsi</Label>
              <Input
                id="province"
                value={formData.businessAddress?.province || ""}
                onChange={(e) => onSubFieldChange("businessAddress", "province", e.target.value)}
                placeholder="Provinsi"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="postalCode">Kode Pos</Label>
              <Input
                id="postalCode"
                value={formData.businessAddress?.postalCode || ""}
                onChange={(e) => onSubFieldChange("businessAddress", "postalCode", e.target.value)}
                placeholder="Kode pos"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">Negara</Label>
              <Select
                value={formData.businessAddress?.country || ""}
                onValueChange={(value) => onSubFieldChange("businessAddress", "country", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih negara" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="indonesia">Indonesia</SelectItem>
                  <SelectItem value="malaysia">Malaysia</SelectItem>
                  <SelectItem value="singapore">Singapura</SelectItem>
                  <SelectItem value="thailand">Thailand</SelectItem>
                  <SelectItem value="philippines">Filipina</SelectItem>
                  <SelectItem value="vietnam">Vietnam</SelectItem>
                  <SelectItem value="other">Lainnya</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="space-y-3 md:col-span-2">
          <Label>
            KTP/Identitas Pemilik <span className="text-red-500">*</span>
          </Label>
          <div className="flex items-start gap-4">
            {ownerIdPreview ? (
              <div className="relative w-full max-w-md border rounded-lg overflow-hidden">
                <img src={ownerIdPreview || "/placeholder.svg"} alt="ID Card Preview" className="w-full h-auto" />
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2 w-6 h-6 p-0 rounded-full"
                  onClick={() => {
                    setOwnerIdPreview(null)
                    onChange("ownerIdCard", null)
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ) : (
              <div className="border border-dashed rounded-lg p-4 w-full">
                <div className="flex flex-col items-center justify-center py-6">
                  <Upload className="h-10 w-10 text-gray-400 mb-2" />
                  <p className="text-sm font-medium mb-1">Upload KTP/ID Card</p>
                  <p className="text-xs text-gray-500 mb-3 text-center">Format PDF, PNG atau JPG (max. 2MB)</p>
                  <input
                    type="file"
                    id="ownerIdCard"
                    accept="application/pdf, image/png, image/jpeg"
                    className="hidden"
                    onChange={handleIdCardChange}
                  />
                  <Button variant="outline" size="sm" onClick={() => document.getElementById("ownerIdCard")?.click()}>
                    Pilih File
                  </Button>
                </div>
              </div>
            )}
          </div>
          <p className="text-xs text-gray-500">
            Dokumen ini dibutuhkan untuk verifikasi dan keamanan. Kami menjamin kerahasiaan data Anda.
          </p>
        </div>

        <div className="space-y-3 md:col-span-2">
          <h3 className="font-medium text-base">
            Informasi Rekening Bank <span className="text-red-500">*</span>
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="bankName">Nama Bank</Label>
              <Select
                value={formData.bankAccount?.bankName || ""}
                onValueChange={(value) => onSubFieldChange("bankAccount", "bankName", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih bank" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bca">Bank Central Asia (BCA)</SelectItem>
                  <SelectItem value="mandiri">Bank Mandiri</SelectItem>
                  <SelectItem value="bni">Bank Negara Indonesia (BNI)</SelectItem>
                  <SelectItem value="bri">Bank Rakyat Indonesia (BRI)</SelectItem>
                  <SelectItem value="cimb">CIMB Niaga</SelectItem>
                  <SelectItem value="permata">Bank Permata</SelectItem>
                  <SelectItem value="other">Bank Lainnya</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="accountNumber">Nomor Rekening</Label>
              <Input
                id="accountNumber"
                value={formData.bankAccount?.accountNumber || ""}
                onChange={(e) => onSubFieldChange("bankAccount", "accountNumber", e.target.value)}
                placeholder="Nomor rekening"
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="accountHolderName">Nama Pemilik Rekening</Label>
              <Input
                id="accountHolderName"
                value={formData.bankAccount?.accountHolderName || ""}
                onChange={(e) => onSubFieldChange("bankAccount", "accountHolderName", e.target.value)}
                placeholder="Nama sesuai rekening"
              />
              <p className="text-xs text-gray-500">
                Pastikan nama pemilik rekening sesuai dengan yang tertera di rekening koran Anda.
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-3 md:col-span-2">
          <Label>Dokumen Bisnis Tambahan</Label>
          <p className="text-sm text-gray-500 mb-2">
            Upload dokumen pendukung seperti SIUP, TDP, atau sertifikasi lainnya (opsional).
          </p>

          <div className="border border-dashed rounded-lg p-4">
            {businessDocs.length > 0 ? (
              <div className="space-y-2 mb-4">
                {businessDocs.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                    <div className="flex items-center">
                      <File className="h-4 w-4 text-blue-500 mr-2" />
                      <div>
                        <p className="text-sm font-medium">{doc.name}</p>
                        <p className="text-xs text-gray-500">{doc.size}</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="h-7 w-7 p-0" onClick={() => removeDocument(index)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : null}

            <div className="flex flex-col items-center justify-center py-4">
              <Upload className="h-8 w-8 text-gray-400 mb-2" />
              <p className="text-sm font-medium mb-1">Upload Dokumen Bisnis</p>
              <p className="text-xs text-gray-500 mb-3 text-center">Format PDF atau gambar (max. 5MB per file)</p>
              <input
                type="file"
                id="businessDocuments"
                accept="application/pdf, image/png, image/jpeg"
                className="hidden"
                multiple
                onChange={handleBusinessDocsChange}
              />
              <Button variant="outline" size="sm" onClick={() => document.getElementById("businessDocuments")?.click()}>
                Pilih File
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
