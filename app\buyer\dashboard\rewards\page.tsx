"use client"

import { useState } from "react"
import Link from "next/link"
import {
  Award,
  Calendar,
  ChevronRight,
  Star,
  ShoppingBag,
  UserPlus,
  MessageSquare,
  Clock,
  ArrowRight,
  Check,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Sample rewards data
const rewardsData = {
  points: 750,
  tier: "Silver",
  nextTier: "Gold",
  pointsToNextTier: 250,
  pointsExpiring: 100,
  expiryDate: "2025-06-30",
  history: [
    { id: "1", date: "2025-05-15", description: "Purchase #ORD-12345", points: 150, type: "earned" },
    { id: "2", date: "2025-05-10", description: "Product Review", points: 50, type: "earned" },
    { id: "3", date: "2025-05-05", description: "Purchase #ORD-12343", points: 200, type: "earned" },
    { id: "4", date: "2025-04-28", description: "Referral Bonus", points: 100, type: "earned" },
    { id: "5", date: "2025-04-20", description: "Purchase #ORD-12341", points: 150, type: "earned" },
    { id: "6", date: "2025-04-15", description: "Discount Redemption", points: 200, type: "redeemed" },
    { id: "7", date: "2025-04-10", description: "Birthday Bonus", points: 100, type: "earned" },
    { id: "8", date: "2025-04-05", description: "Free Shipping Redemption", points: 50, type: "redeemed" },
  ],
  tiers: [
    { name: "Bronze", minPoints: 0, benefits: ["Free shipping on orders over 500K", "Birthday gift"] },
    {
      name: "Silver",
      minPoints: 500,
      benefits: ["Free shipping on all orders", "Birthday gift", "Early access to sales", "Exclusive discounts"],
    },
    {
      name: "Gold",
      minPoints: 1000,
      benefits: [
        "Free shipping on all orders",
        "Birthday gift",
        "Early access to sales",
        "Exclusive discounts",
        "Priority customer service",
        "Free returns",
      ],
    },
    {
      name: "Platinum",
      minPoints: 2000,
      benefits: [
        "Free shipping on all orders",
        "Birthday gift",
        "Early access to sales",
        "Exclusive discounts",
        "Priority customer service",
        "Free returns",
        "Personal shopping assistant",
        "Invitation to exclusive events",
      ],
    },
  ],
  rewards: [
    { id: "1", name: "10% Discount Coupon", points: 200, image: "/placeholder.svg", category: "Discount" },
    { id: "2", name: "Free Shipping Voucher", points: 50, image: "/placeholder.svg", category: "Shipping" },
    { id: "3", name: "50K Store Credit", points: 500, image: "/placeholder.svg", category: "Credit" },
    { id: "4", name: "Mystery Gift Box", points: 800, image: "/placeholder.svg", category: "Gift" },
    { id: "5", name: "15% Discount Coupon", points: 300, image: "/placeholder.svg", category: "Discount" },
    { id: "6", name: "100K Store Credit", points: 1000, image: "/placeholder.svg", category: "Credit" },
    { id: "7", name: "Premium Gift Wrapping", points: 100, image: "/placeholder.svg", category: "Service" },
    { id: "8", name: "Early Access to New Collection", points: 400, image: "/placeholder.svg", category: "Access" },
  ],
  challenges: [
    { id: "1", name: "Complete 3 purchases this month", points: 200, progress: 1, total: 3, endDate: "2025-05-31" },
    { id: "2", name: "Write 5 product reviews", points: 250, progress: 2, total: 5, endDate: "2025-05-31" },
    { id: "3", name: "Refer a friend", points: 100, progress: 0, total: 1, endDate: "2025-06-30" },
    { id: "4", name: "Complete your profile", points: 50, progress: 1, total: 1, endDate: null, completed: true },
  ],
}

export default function RewardsPage() {
  const [rewards, setRewards] = useState(rewardsData)

  // Calculate progress percentage to next tier
  const progressToNextTier = () => {
    const currentTierIndex = rewards.tiers.findIndex((tier) => tier.name === rewards.tier)
    const currentTierPoints = rewards.tiers[currentTierIndex].minPoints
    const nextTierPoints = rewards.tiers[currentTierIndex + 1].minPoints
    const totalPointsNeeded = nextTierPoints - currentTierPoints
    const pointsEarned = rewards.points - currentTierPoints
    return (pointsEarned / totalPointsNeeded) * 100
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Rewards Program</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/buyer/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
      </div>

      {/* Rewards Dashboard */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-between gap-6 md:flex-row">
            <div className="flex flex-col items-center md:items-start">
              <div className="flex items-center gap-2">
                <Award className="h-6 w-6 text-primary" />
                <h2 className="text-2xl font-bold">{rewards.tier} Member</h2>
              </div>
              <div className="mt-2 text-center text-4xl font-bold md:text-left">
                {rewards.points} <span className="text-lg text-muted-foreground">points</span>
              </div>
              {rewards.pointsExpiring > 0 && (
                <div className="mt-1 text-sm text-amber-500">
                  <Clock className="mr-1 inline-block h-4 w-4" />
                  {rewards.pointsExpiring} points expiring on {new Date(rewards.expiryDate).toLocaleDateString("id-ID")}
                </div>
              )}
            </div>

            <div className="flex w-full flex-col md:w-1/2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{rewards.tier}</span>
                <span className="text-sm font-medium">{rewards.nextTier}</span>
              </div>
              <Progress value={progressToNextTier()} className="h-2.5 w-full" />
              <div className="mt-2 text-center text-sm text-muted-foreground">
                {rewards.pointsToNextTier} more points to reach {rewards.nextTier}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Earning Opportunities */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Ways to Earn Points</CardTitle>
            <CardDescription>Complete these activities to earn more rewards points</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary">
                  <ShoppingBag className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">Make a Purchase</h3>
                  <p className="text-sm text-muted-foreground">Earn 1 point for every 1,000 spent</p>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/buyer/marketplace">
                    Shop Now
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary">
                  <Star className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">Write Reviews</h3>
                  <p className="text-sm text-muted-foreground">Earn 50 points for each product review</p>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/buyer/dashboard/reviews">
                    Write Review
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary">
                  <UserPlus className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">Refer Friends</h3>
                  <p className="text-sm text-muted-foreground">Earn 100 points for each friend who signs up</p>
                </div>
                <Button variant="outline" size="sm">
                  Refer Now
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary">
                  <MessageSquare className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">Engage on Community</h3>
                  <p className="text-sm text-muted-foreground">Earn points by participating in discussions</p>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/buyer/dashboard/community">
                    Join Now
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Challenges */}
        <Card>
          <CardHeader>
            <CardTitle>Current Challenges</CardTitle>
            <CardDescription>Complete challenges to earn bonus points</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {rewards.challenges.map((challenge) => (
                <div key={challenge.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">{challenge.name}</h3>
                    <Badge variant="outline">{challenge.points} pts</Badge>
                  </div>
                  {challenge.completed ? (
                    <div className="flex items-center text-sm text-green-500">
                      <Check className="mr-1 h-4 w-4" />
                      Completed
                    </div>
                  ) : (
                    <>
                      <Progress value={(challenge.progress / challenge.total) * 100} className="h-2" />
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>
                          {challenge.progress} of {challenge.total} completed
                        </span>
                        {challenge.endDate && (
                          <span className="flex items-center">
                            <Calendar className="mr-1 h-3 w-3" />
                            Ends {new Date(challenge.endDate).toLocaleDateString("id-ID")}
                          </span>
                        )}
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/buyer/dashboard/rewards/challenges">
                View All Challenges
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Membership Tiers */}
      <Card>
        <CardHeader>
          <CardTitle>Membership Tiers</CardTitle>
          <CardDescription>Benefits for each membership level</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            {rewards.tiers.map((tier, index) => (
              <Card key={index} className={`overflow-hidden ${tier.name === rewards.tier ? "border-primary" : ""}`}>
                <div
                  className={`h-2 w-full ${
                    tier.name === "Bronze"
                      ? "bg-amber-700"
                      : tier.name === "Silver"
                        ? "bg-gray-400"
                        : tier.name === "Gold"
                          ? "bg-amber-400"
                          : "bg-slate-700"
                  }`}
                />
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    {tier.name}
                    {tier.name === rewards.tier && <Badge className="ml-2 bg-primary">Current</Badge>}
                  </CardTitle>
                  <CardDescription>{tier.minPoints}+ points</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    {tier.benefits.map((benefit, i) => (
                      <li key={i} className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-green-500" />
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Rewards Catalog & History */}
      <Tabs defaultValue="catalog" className="space-y-6">
        <TabsList>
          <TabsTrigger value="catalog">Rewards Catalog</TabsTrigger>
          <TabsTrigger value="history">Points History</TabsTrigger>
        </TabsList>

        <TabsContent value="catalog" className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {rewards.rewards.map((reward) => (
              <Card key={reward.id} className="overflow-hidden">
                <div className="aspect-video w-full bg-gray-100">
                  <img
                    src={reward.image || "/placeholder.svg"}
                    alt={reward.name}
                    className="h-full w-full object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <Badge variant="outline" className="mb-2">
                    {reward.category}
                  </Badge>
                  <h3 className="font-medium">{reward.name}</h3>
                  <div className="mt-2 flex items-center justify-between">
                    <div className="flex items-center gap-1 text-sm">
                      <Award className="h-4 w-4 text-primary" />
                      <span className="font-bold">{reward.points} points</span>
                    </div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button size="sm" disabled={rewards.points < reward.points}>
                            Redeem
                          </Button>
                        </TooltipTrigger>
                        {rewards.points < reward.points && (
                          <TooltipContent>
                            <p>You need {reward.points - rewards.points} more points</p>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="flex justify-center">
            <Button variant="outline" asChild>
              <Link href="/buyer/dashboard/rewards/catalog">
                View Full Catalog
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Points History</CardTitle>
              <CardDescription>Your points activity</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {rewards.history.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0"
                  >
                    <div>
                      <div className="font-medium">{item.description}</div>
                      <div className="text-sm text-muted-foreground">
                        {new Date(item.date).toLocaleDateString("id-ID")}
                      </div>
                    </div>
                    <div className={`font-bold ${item.type === "earned" ? "text-green-500" : "text-amber-500"}`}>
                      {item.type === "earned" ? "+" : "-"}
                      {item.points} points
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/buyer/dashboard/rewards/history">
                  View Complete History
                  <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
