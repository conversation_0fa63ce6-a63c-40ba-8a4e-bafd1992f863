"use client"

import { useState } from "react"
import { ChevronRight, ChevronDown, Plus, Edit, Trash2, AlertCircle, Layers, Tag, Settings } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Mock data for categories
const mockCategories = [
  {
    id: "c1",
    name: "Electronics",
    slug: "electronics",
    description: "Electronic devices and gadgets",
    productCount: 245,
    children: [
      {
        id: "c1-1",
        name: "Smartphones",
        slug: "smartphones",
        description: "Mobile phones and accessories",
        productCount: 78,
        children: [],
      },
      {
        id: "c1-2",
        name: "Laptops",
        slug: "laptops",
        description: "Notebook computers and accessories",
        productCount: 56,
        children: [],
      },
      {
        id: "c1-3",
        name: "Audio",
        slug: "audio",
        description: "Headphones, speakers, and audio equipment",
        productCount: 42,
        children: [
          {
            id: "c1-3-1",
            name: "Headphones",
            slug: "headphones",
            description: "Over-ear, on-ear, and in-ear headphones",
            productCount: 28,
            children: [],
          },
          {
            id: "c1-3-2",
            name: "Speakers",
            slug: "speakers",
            description: "Bluetooth, wireless, and wired speakers",
            productCount: 14,
            children: [],
          },
        ],
      },
    ],
  },
  {
    id: "c2",
    name: "Clothing",
    slug: "clothing",
    description: "Apparel and fashion items",
    productCount: 320,
    children: [
      {
        id: "c2-1",
        name: "Men's Clothing",
        slug: "mens-clothing",
        description: "Shirts, pants, and accessories for men",
        productCount: 120,
        children: [],
      },
      {
        id: "c2-2",
        name: "Women's Clothing",
        slug: "womens-clothing",
        description: "Dresses, tops, and accessories for women",
        productCount: 180,
        children: [],
      },
      {
        id: "c2-3",
        name: "Kids' Clothing",
        slug: "kids-clothing",
        description: "Clothing for children and infants",
        productCount: 20,
        children: [],
      },
    ],
  },
  {
    id: "c3",
    name: "Home & Kitchen",
    slug: "home-kitchen",
    description: "Home goods, appliances, and kitchenware",
    productCount: 185,
    children: [
      {
        id: "c3-1",
        name: "Furniture",
        slug: "furniture",
        description: "Tables, chairs, sofas, and other furniture",
        productCount: 65,
        children: [],
      },
      {
        id: "c3-2",
        name: "Kitchen Appliances",
        slug: "kitchen-appliances",
        description: "Blenders, mixers, and other kitchen tools",
        productCount: 45,
        children: [],
      },
      {
        id: "c3-3",
        name: "Bedding",
        slug: "bedding",
        description: "Sheets, pillows, and bedding accessories",
        productCount: 35,
        children: [],
      },
      {
        id: "c3-4",
        name: "Decor",
        slug: "decor",
        description: "Home decoration and accessories",
        productCount: 40,
        children: [],
      },
    ],
  },
]

// Mock data for attributes
const mockAttributes = [
  {
    id: "a1",
    name: "Color",
    type: "select",
    required: true,
    options: ["Red", "Blue", "Green", "Black", "White", "Yellow"],
    categories: ["Electronics", "Clothing", "Home & Kitchen"],
  },
  {
    id: "a2",
    name: "Size",
    type: "select",
    required: true,
    options: ["XS", "S", "M", "L", "XL", "XXL"],
    categories: ["Clothing"],
  },
  {
    id: "a3",
    name: "Material",
    type: "select",
    required: false,
    options: ["Cotton", "Polyester", "Leather", "Metal", "Wood", "Glass"],
    categories: ["Clothing", "Home & Kitchen"],
  },
  {
    id: "a4",
    name: "Weight",
    type: "number",
    required: false,
    unit: "kg",
    categories: ["Electronics", "Home & Kitchen"],
  },
  {
    id: "a5",
    name: "Dimensions",
    type: "text",
    required: false,
    categories: ["Electronics", "Home & Kitchen"],
  },
  {
    id: "a6",
    name: "Brand",
    type: "select",
    required: true,
    options: ["Apple", "Samsung", "Sony", "LG", "Nike", "Adidas"],
    categories: ["Electronics", "Clothing"],
  },
]

// Mock data for attribute templates
const mockTemplates = [
  {
    id: "t1",
    name: "Electronics Basic",
    attributes: ["Color", "Weight", "Dimensions", "Brand"],
    categories: ["Electronics"],
  },
  {
    id: "t2",
    name: "Clothing Basic",
    attributes: ["Color", "Size", "Material", "Brand"],
    categories: ["Clothing"],
  },
  {
    id: "t3",
    name: "Furniture Basic",
    attributes: ["Color", "Material", "Weight", "Dimensions"],
    categories: ["Home & Kitchen"],
  },
]

type Category = {
  id: string
  name: string
  slug: string
  description: string
  productCount: number
  children: Category[]
}

type Attribute = {
  id: string
  name: string
  type: string
  required: boolean
  options?: string[]
  unit?: string
  categories: string[]
}

type Template = {
  id: string
  name: string
  attributes: string[]
  categories: string[]
}

export function CategoriesAttributes() {
  const [expandedCategories, setExpandedCategories] = useState<string[]>([])
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [selectedAttribute, setSelectedAttribute] = useState<Attribute | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null)
  const [activeTab, setActiveTab] = useState("categories")
  const [showCategoryDialog, setShowCategoryDialog] = useState(false)
  const [showAttributeDialog, setShowAttributeDialog] = useState(false)
  const [showTemplateDialog, setShowTemplateDialog] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deleteType, setDeleteType] = useState<"category" | "attribute" | "template">("category")
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null)

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryId) ? prev.filter((id) => id !== categoryId) : [...prev, categoryId],
    )
  }

  const handleCategoryClick = (category: Category) => {
    setSelectedCategory(category)
  }

  const handleAttributeClick = (attribute: Attribute) => {
    setSelectedAttribute(attribute)
  }

  const handleTemplateClick = (template: Template) => {
    setSelectedTemplate(template)
  }

  const handleAddCategory = () => {
    setSelectedCategory(null)
    setShowCategoryDialog(true)
  }

  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category)
    setShowCategoryDialog(true)
  }

  const handleAddAttribute = () => {
    setSelectedAttribute(null)
    setShowAttributeDialog(true)
  }

  const handleEditAttribute = (attribute: Attribute) => {
    setSelectedAttribute(attribute)
    setShowAttributeDialog(true)
  }

  const handleAddTemplate = () => {
    setSelectedTemplate(null)
    setShowTemplateDialog(true)
  }

  const handleEditTemplate = (template: Template) => {
    setSelectedTemplate(template)
    setShowTemplateDialog(true)
  }

  const handleDeleteClick = (type: "category" | "attribute" | "template", id: string) => {
    setDeleteType(type)
    setDeleteItemId(id)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    // In a real app, you would delete the item here
    console.log(`Deleting ${deleteType} ${deleteItemId}`)
    setDeleteDialogOpen(false)
    setDeleteItemId(null)
  }

  const renderCategoryTree = (categories: Category[], level = 0) => {
    return categories.map((category) => (
      <div key={category.id} className="mb-1">
        <div
          className={`flex items-center rounded-md px-2 py-1.5 hover:bg-muted ${selectedCategory?.id === category.id ? "bg-muted" : ""}`}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
        >
          {category.children.length > 0 && (
            <button onClick={() => toggleCategory(category.id)} className="mr-1 p-1">
              {expandedCategories.includes(category.id) ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>
          )}
          {category.children.length === 0 && <div className="w-6" />}
          <div
            className="flex flex-1 cursor-pointer items-center justify-between"
            onClick={() => handleCategoryClick(category)}
          >
            <span className="font-medium">{category.name}</span>
            <Badge variant="outline">{category.productCount}</Badge>
          </div>
        </div>
        {expandedCategories.includes(category.id) && category.children.length > 0 && (
          <div className="ml-2">{renderCategoryTree(category.children, level + 1)}</div>
        )}
      </div>
    ))
  }

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <div className="lg:col-span-1">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="categories">
              <Layers className="mr-2 h-4 w-4" />
              Categories
            </TabsTrigger>
            <TabsTrigger value="attributes">
              <Tag className="mr-2 h-4 w-4" />
              Attributes
            </TabsTrigger>
            <TabsTrigger value="templates">
              <Settings className="mr-2 h-4 w-4" />
              Templates
            </TabsTrigger>
          </TabsList>
          <TabsContent value="categories" className="mt-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between px-4 py-3">
                <CardTitle className="text-base">Product Categories</CardTitle>
                <Button size="sm" onClick={handleAddCategory}>
                  <Plus className="mr-1 h-4 w-4" />
                  Add Category
                </Button>
              </CardHeader>
              <CardContent className="px-2 py-2">
                <div className="max-h-[600px] overflow-y-auto">{renderCategoryTree(mockCategories)}</div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="attributes" className="mt-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between px-4 py-3">
                <CardTitle className="text-base">Product Attributes</CardTitle>
                <Button size="sm" onClick={handleAddAttribute}>
                  <Plus className="mr-1 h-4 w-4" />
                  Add Attribute
                </Button>
              </CardHeader>
              <CardContent className="px-2 py-2">
                <div className="max-h-[600px] overflow-y-auto space-y-1">
                  {mockAttributes.map((attribute) => (
                    <div
                      key={attribute.id}
                      className={`flex items-center justify-between rounded-md px-3 py-2 hover:bg-muted ${selectedAttribute?.id === attribute.id ? "bg-muted" : ""}`}
                      onClick={() => handleAttributeClick(attribute)}
                    >
                      <div>
                        <div className="font-medium">{attribute.name}</div>
                        <div className="text-xs text-muted-foreground">
                          Type: {attribute.type} {attribute.required && "(Required)"}
                        </div>
                      </div>
                      <Badge variant="outline">{attribute.categories.length}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="templates" className="mt-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between px-4 py-3">
                <CardTitle className="text-base">Attribute Templates</CardTitle>
                <Button size="sm" onClick={handleAddTemplate}>
                  <Plus className="mr-1 h-4 w-4" />
                  Add Template
                </Button>
              </CardHeader>
              <CardContent className="px-2 py-2">
                <div className="max-h-[600px] overflow-y-auto space-y-1">
                  {mockTemplates.map((template) => (
                    <div
                      key={template.id}
                      className={`flex items-center justify-between rounded-md px-3 py-2 hover:bg-muted ${selectedTemplate?.id === template.id ? "bg-muted" : ""}`}
                      onClick={() => handleTemplateClick(template)}
                    >
                      <div>
                        <div className="font-medium">{template.name}</div>
                        <div className="text-xs text-muted-foreground">{template.attributes.length} attributes</div>
                      </div>
                      <Badge variant="outline">{template.categories.length}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <div className="lg:col-span-2">
        {activeTab === "categories" && selectedCategory && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
              <CardTitle>Category Details</CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => handleEditCategory(selectedCategory)}>
                  <Edit className="mr-1 h-4 w-4" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-destructive hover:bg-destructive/10"
                  onClick={() => handleDeleteClick("category", selectedCategory.id)}
                >
                  <Trash2 className="mr-1 h-4 w-4" />
                  Delete
                </Button>
              </div>
            </CardHeader>
            <CardContent className="px-6 py-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Category Name</h3>
                  <p className="mt-1 text-base">{selectedCategory.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Slug</h3>
                  <p className="mt-1 text-base">{selectedCategory.slug}</p>
                </div>
                <div className="md:col-span-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                  <p className="mt-1 text-base">{selectedCategory.description}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Product Count</h3>
                  <p className="mt-1 text-base">{selectedCategory.productCount}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Subcategories</h3>
                  <p className="mt-1 text-base">{selectedCategory.children.length}</p>
                </div>
              </div>

              {selectedCategory.children.length > 0 && (
                <div className="mt-6">
                  <h3 className="mb-2 text-sm font-medium">Subcategories</h3>
                  <div className="rounded-md border">
                    <div className="grid grid-cols-3 gap-4 border-b p-3 font-medium">
                      <div>Name</div>
                      <div>Products</div>
                      <div className="text-right">Actions</div>
                    </div>
                    {selectedCategory.children.map((child) => (
                      <div key={child.id} className="grid grid-cols-3 gap-4 border-b p-3 last:border-0">
                        <div>{child.name}</div>
                        <div>{child.productCount}</div>
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="mt-6">
                <h3 className="mb-2 text-sm font-medium">Applicable Attributes</h3>
                <div className="grid grid-cols-2 gap-2 md:grid-cols-3">
                  {mockAttributes
                    .filter((attr) => attr.categories.includes(selectedCategory.name))
                    .map((attr) => (
                      <div key={attr.id} className="rounded-md border p-2">
                        <div className="font-medium">{attr.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {attr.type} {attr.required && "(Required)"}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === "attributes" && selectedAttribute && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
              <CardTitle>Attribute Details</CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => handleEditAttribute(selectedAttribute)}>
                  <Edit className="mr-1 h-4 w-4" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-destructive hover:bg-destructive/10"
                  onClick={() => handleDeleteClick("attribute", selectedAttribute.id)}
                >
                  <Trash2 className="mr-1 h-4 w-4" />
                  Delete
                </Button>
              </div>
            </CardHeader>
            <CardContent className="px-6 py-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Attribute Name</h3>
                  <p className="mt-1 text-base">{selectedAttribute.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Type</h3>
                  <p className="mt-1 text-base capitalize">{selectedAttribute.type}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Required</h3>
                  <p className="mt-1 text-base">{selectedAttribute.required ? "Yes" : "No"}</p>
                </div>
                {selectedAttribute.unit && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Unit</h3>
                    <p className="mt-1 text-base">{selectedAttribute.unit}</p>
                  </div>
                )}
              </div>

              {selectedAttribute.options && selectedAttribute.options.length > 0 && (
                <div className="mt-6">
                  <h3 className="mb-2 text-sm font-medium">Options</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedAttribute.options.map((option, index) => (
                      <Badge key={index} variant="outline">
                        {option}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <div className="mt-6">
                <h3 className="mb-2 text-sm font-medium">Applied to Categories</h3>
                <div className="grid grid-cols-2 gap-2 md:grid-cols-3">
                  {selectedAttribute.categories.map((category, index) => (
                    <div key={index} className="rounded-md border p-2">
                      <div className="font-medium">{category}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === "templates" && selectedTemplate && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
              <CardTitle>Template Details</CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => handleEditTemplate(selectedTemplate)}>
                  <Edit className="mr-1 h-4 w-4" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-destructive hover:bg-destructive/10"
                  onClick={() => handleDeleteClick("template", selectedTemplate.id)}
                >
                  <Trash2 className="mr-1 h-4 w-4" />
                  Delete
                </Button>
              </div>
            </CardHeader>
            <CardContent className="px-6 py-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="md:col-span-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Template Name</h3>
                  <p className="mt-1 text-base">{selectedTemplate.name}</p>
                </div>
              </div>

              <div className="mt-6">
                <h3 className="mb-2 text-sm font-medium">Attributes</h3>
                <div className="rounded-md border">
                  <div className="grid grid-cols-3 gap-4 border-b p-3 font-medium">
                    <div>Name</div>
                    <div>Type</div>
                    <div>Required</div>
                  </div>
                  {selectedTemplate.attributes.map((attrName, index) => {
                    const attr = mockAttributes.find((a) => a.name === attrName)
                    return attr ? (
                      <div key={index} className="grid grid-cols-3 gap-4 border-b p-3 last:border-0">
                        <div>{attr.name}</div>
                        <div className="capitalize">{attr.type}</div>
                        <div>{attr.required ? "Yes" : "No"}</div>
                      </div>
                    ) : null
                  })}
                </div>
              </div>

              <div className="mt-6">
                <h3 className="mb-2 text-sm font-medium">Applied to Categories</h3>
                <div className="grid grid-cols-2 gap-2 md:grid-cols-3">
                  {selectedTemplate.categories.map((category, index) => (
                    <div key={index} className="rounded-md border p-2">
                      <div className="font-medium">{category}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === "categories" && !selectedCategory && (
          <div className="flex h-full items-center justify-center rounded-lg border border-dashed p-8 text-center">
            <div>
              <Layers className="mx-auto h-10 w-10 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No Category Selected</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Select a category from the list or create a new one to view and edit its details.
              </p>
              <Button className="mt-4" onClick={handleAddCategory}>
                <Plus className="mr-1 h-4 w-4" />
                Add Category
              </Button>
            </div>
          </div>
        )}

        {activeTab === "attributes" && !selectedAttribute && (
          <div className="flex h-full items-center justify-center rounded-lg border border-dashed p-8 text-center">
            <div>
              <Tag className="mx-auto h-10 w-10 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No Attribute Selected</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Select an attribute from the list or create a new one to view and edit its details.
              </p>
              <Button className="mt-4" onClick={handleAddAttribute}>
                <Plus className="mr-1 h-4 w-4" />
                Add Attribute
              </Button>
            </div>
          </div>
        )}

        {activeTab === "templates" && !selectedTemplate && (
          <div className="flex h-full items-center justify-center rounded-lg border border-dashed p-8 text-center">
            <div>
              <Settings className="mx-auto h-10 w-10 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No Template Selected</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Select a template from the list or create a new one to view and edit its details.
              </p>
              <Button className="mt-4" onClick={handleAddTemplate}>
                <Plus className="mr-1 h-4 w-4" />
                Add Template
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Category Dialog */}
      <Dialog open={showCategoryDialog} onOpenChange={setShowCategoryDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedCategory ? "Edit Category" : "Add New Category"}</DialogTitle>
            <DialogDescription>
              {selectedCategory ? "Update the details for this category" : "Create a new product category"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Category Name</Label>
              <Input id="name" defaultValue={selectedCategory?.name || ""} placeholder="e.g. Electronics" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="slug">Slug</Label>
              <Input id="slug" defaultValue={selectedCategory?.slug || ""} placeholder="e.g. electronics" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                defaultValue={selectedCategory?.description || ""}
                placeholder="Describe this category..."
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="parent">Parent Category (Optional)</Label>
              <Select defaultValue="none">
                <SelectTrigger>
                  <SelectValue placeholder="Select a parent category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None (Top Level)</SelectItem>
                  {mockCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCategoryDialog(false)}>
              Cancel
            </Button>
            <Button>{selectedCategory ? "Update Category" : "Create Category"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Attribute Dialog */}
      <Dialog open={showAttributeDialog} onOpenChange={setShowAttributeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedAttribute ? "Edit Attribute" : "Add New Attribute"}</DialogTitle>
            <DialogDescription>
              {selectedAttribute ? "Update the details for this attribute" : "Create a new product attribute"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="attr-name">Attribute Name</Label>
              <Input id="attr-name" defaultValue={selectedAttribute?.name || ""} placeholder="e.g. Color" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="attr-type">Type</Label>
              <Select defaultValue={selectedAttribute?.type || "select"}>
                <SelectTrigger>
                  <SelectValue placeholder="Select attribute type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="select">Select (Multiple Options)</SelectItem>
                  <SelectItem value="text">Text</SelectItem>
                  <SelectItem value="number">Number</SelectItem>
                  <SelectItem value="boolean">Boolean (Yes/No)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Label htmlFor="required" className="flex-1">
                Required
              </Label>
              <Switch id="required" defaultChecked={selectedAttribute?.required || false} />
            </div>
            <div className="grid gap-2">
              <Label>Options (for Select type)</Label>
              <Input
                placeholder="Enter options separated by commas"
                defaultValue={selectedAttribute?.options?.join(", ") || ""}
              />
            </div>
            <div className="grid gap-2">
              <Label>Apply to Categories</Label>
              <div className="grid grid-cols-2 gap-2">
                {mockCategories.map((category) => (
                  <div key={category.id} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id={`cat-${category.id}`}
                      defaultChecked={selectedAttribute?.categories.includes(category.name) || false}
                    />
                    <Label htmlFor={`cat-${category.id}`} className="text-sm">
                      {category.name}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAttributeDialog(false)}>
              Cancel
            </Button>
            <Button>{selectedAttribute ? "Update Attribute" : "Create Attribute"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Template Dialog */}
      <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedTemplate ? "Edit Template" : "Add New Template"}</DialogTitle>
            <DialogDescription>
              {selectedTemplate ? "Update the details for this template" : "Create a new attribute template"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="template-name">Template Name</Label>
              <Input
                id="template-name"
                defaultValue={selectedTemplate?.name || ""}
                placeholder="e.g. Electronics Basic"
              />
            </div>
            <div className="grid gap-2">
              <Label>Attributes</Label>
              <div className="max-h-40 overflow-y-auto rounded-md border p-2">
                {mockAttributes.map((attr) => (
                  <div key={attr.id} className="flex items-center gap-2 py-1">
                    <input
                      type="checkbox"
                      id={`attr-${attr.id}`}
                      defaultChecked={selectedTemplate?.attributes.includes(attr.name) || false}
                    />
                    <Label htmlFor={`attr-${attr.id}`} className="text-sm">
                      {attr.name} ({attr.type})
                    </Label>
                  </div>
                ))}
              </div>
            </div>
            <div className="grid gap-2">
              <Label>Apply to Categories</Label>
              <div className="grid grid-cols-2 gap-2">
                {mockCategories.map((category) => (
                  <div key={category.id} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id={`tcat-${category.id}`}
                      defaultChecked={selectedTemplate?.categories.includes(category.name) || false}
                    />
                    <Label htmlFor={`tcat-${category.id}`} className="text-sm">
                      {category.name}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowTemplateDialog(false)}>
              Cancel
            </Button>
            <Button>{selectedTemplate ? "Update Template" : "Create Template"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this {deleteType}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center gap-4 py-4">
            <div className="rounded-full bg-destructive/10 p-2 text-destructive">
              <AlertCircle className="h-6 w-6" />
            </div>
            <div>
              <p className="font-medium">This will permanently delete the {deleteType}</p>
              <p className="text-sm text-muted-foreground">
                {deleteType === "category" && "All subcategories will also be deleted."}
                {deleteType === "attribute" && "This attribute will be removed from all products."}
                {deleteType === "template" && "This template will no longer be available for new products."}
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete {deleteType}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
