"use client"

import type React from "react"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Upload } from "lucide-react"
import { useState } from "react"

interface StoreInformationProps {
  formData: any
  onChange: (field: string, value: any) => void
}

export function StoreInformation({ formData, onChange }: StoreInformationProps) {
  const [logoPreview, setLogoPreview] = useState<string | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      onChange("storeLogo", file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        if (e.target?.result) {
          setLogoPreview(e.target.result as string)
        }
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Informasi Toko</h2>
        <p className="text-gray-500">
          Berikan informasi dasar tentang toko Anda untuk membantu calon pembeli mengenal bisnis Anda.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label htmlFor="storeName">
            Nama Toko <span className="text-red-500">*</span>
          </Label>
          <Input
            id="storeName"
            value={formData.storeName}
            onChange={(e) => onChange("storeName", e.target.value)}
            placeholder="Contoh: Fashion Nusantara"
            required
          />
          <p className="text-xs text-gray-500">
            Nama toko akan ditampilkan kepada pembeli dan tidak dapat diubah setelah disetujui.
          </p>
        </div>

        <div className="space-y-3">
          <Label htmlFor="storeCategory">
            Kategori Toko <span className="text-red-500">*</span>
          </Label>
          <Select value={formData.storeCategory} onValueChange={(value) => onChange("storeCategory", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Pilih kategori toko" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="fashion">Fashion & Pakaian</SelectItem>
              <SelectItem value="electronics">Elektronik & Gadget</SelectItem>
              <SelectItem value="home">Rumah & Perabotan</SelectItem>
              <SelectItem value="beauty">Kecantikan & Perawatan</SelectItem>
              <SelectItem value="food">Makanan & Minuman</SelectItem>
              <SelectItem value="sports">Olahraga & Outdoor</SelectItem>
              <SelectItem value="toys">Mainan & Hobi</SelectItem>
              <SelectItem value="automotive">Otomotif</SelectItem>
              <SelectItem value="health">Kesehatan</SelectItem>
              <SelectItem value="books">Buku & Alat Tulis</SelectItem>
              <SelectItem value="other">Lainnya</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3 md:col-span-2">
          <Label htmlFor="storeDescription">
            Deskripsi Toko <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="storeDescription"
            value={formData.storeDescription}
            onChange={(e) => onChange("storeDescription", e.target.value)}
            placeholder="Jelaskan tentang toko Anda, produk yang dijual, dan nilai unik yang ditawarkan..."
            rows={4}
            required
          />
          <p className="text-xs text-gray-500">Minimum 100 karakter, maksimum 500 karakter.</p>
        </div>

        <div className="space-y-3">
          <Label htmlFor="storeEmail">
            Email Toko <span className="text-red-500">*</span>
          </Label>
          <Input
            id="storeEmail"
            type="email"
            value={formData.storeEmail}
            onChange={(e) => onChange("storeEmail", e.target.value)}
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div className="space-y-3">
          <Label htmlFor="storePhone">
            Nomor Telepon Toko <span className="text-red-500">*</span>
          </Label>
          <Input
            id="storePhone"
            value={formData.storePhone}
            onChange={(e) => onChange("storePhone", e.target.value)}
            placeholder="***********"
            required
          />
        </div>

        <div className="space-y-3">
          <Label htmlFor="businessType">
            Jenis Bisnis <span className="text-red-500">*</span>
          </Label>
          <Select value={formData.businessType} onValueChange={(value) => onChange("businessType", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Pilih jenis bisnis" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="individual">Individu/Perorangan</SelectItem>
              <SelectItem value="cv">CV (Commanditaire Vennootschap)</SelectItem>
              <SelectItem value="pt">PT (Perseroan Terbatas)</SelectItem>
              <SelectItem value="partnership">Kemitraan</SelectItem>
              <SelectItem value="cooperative">Koperasi</SelectItem>
              <SelectItem value="other">Lainnya</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3">
          <Label htmlFor="sellerExperience">
            Pengalaman Sebagai Penjual <span className="text-red-500">*</span>
          </Label>
          <Select value={formData.sellerExperience} onValueChange={(value) => onChange("sellerExperience", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Pilih level pengalaman" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="beginner">Pemula (belum pernah jualan online)</SelectItem>
              <SelectItem value="intermediate">Menengah (pernah jualan online di platform lain)</SelectItem>
              <SelectItem value="advanced">Berpengalaman (sudah punya toko online sendiri)</SelectItem>
              <SelectItem value="expert">Ahli (sudah menjalankan bisnis retail yang mapan)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3 md:col-span-2">
          <Label htmlFor="storeLogo">Logo Toko</Label>
          <div className="flex items-start gap-4">
            {logoPreview ? (
              <div className="relative w-24 h-24 border rounded-lg overflow-hidden">
                <img
                  src={logoPreview || "/placeholder.svg"}
                  alt="Store Logo Preview"
                  className="w-full h-full object-cover"
                />
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-1 right-1 w-6 h-6 p-0 rounded-full"
                  onClick={() => {
                    setLogoPreview(null)
                    onChange("storeLogo", null)
                  }}
                >
                  ✕
                </Button>
              </div>
            ) : (
              <div className="border border-dashed rounded-lg p-4 w-full">
                <div className="flex flex-col items-center justify-center py-4">
                  <Upload className="h-10 w-10 text-gray-400 mb-2" />
                  <p className="text-sm font-medium mb-1">Upload logo toko</p>
                  <p className="text-xs text-gray-500 mb-3 text-center">
                    Format PNG atau JPG (max. 2MB)
                    <br />
                    Dimensi ideal 500x500 piksel
                  </p>
                  <input
                    type="file"
                    id="storeLogo"
                    accept="image/png, image/jpeg"
                    className="hidden"
                    onChange={handleFileChange}
                  />
                  <Button variant="outline" size="sm" onClick={() => document.getElementById("storeLogo")?.click()}>
                    Pilih File
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
