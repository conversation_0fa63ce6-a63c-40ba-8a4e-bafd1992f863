"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useNotifications } from "@/components/providers/notifications-provider"
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Search,
  Plus,
  MoreHorizontal,
  ExternalLink,
  RefreshCw,
  Shield,
  Trash2,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Mock data for domains
const domains = [
  {
    id: "dom_1",
    domain: "fashionista.sellzio.com",
    tenantName: "Fashionista",
    status: "verified",
    sslStatus: "active",
    sslExpiry: "2024-12-15",
    createdAt: "2023-05-10",
  },
  {
    id: "dom_2",
    domain: "techgadgets.sellzio.com",
    tenantName: "Tech Gadgets",
    status: "verified",
    sslStatus: "active",
    sslExpiry: "2024-11-22",
    createdAt: "2023-06-05",
  },
  {
    id: "dom_3",
    domain: "homeessentials.sellzio.com",
    tenantName: "Home Essentials",
    status: "pending",
    sslStatus: "pending",
    sslExpiry: null,
    createdAt: "2023-09-18",
  },
  {
    id: "dom_4",
    domain: "beautysecrets.com",
    tenantName: "Beauty Secrets",
    status: "verified",
    sslStatus: "expiring",
    sslExpiry: "2023-10-30",
    createdAt: "2023-04-12",
  },
  {
    id: "dom_5",
    domain: "sportsgear.sellzio.com",
    tenantName: "Sports Gear",
    status: "failed",
    sslStatus: "failed",
    sslExpiry: null,
    createdAt: "2023-08-25",
  },
]

export function DomainManagementList() {
  const [searchQuery, setSearchQuery] = useState("")
  const { showNotification } = useNotifications()

  const filteredDomains = domains.filter(
    (domain) =>
      domain.domain.toLowerCase().includes(searchQuery.toLowerCase()) ||
      domain.tenantName.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleVerifyDomain = (domainId: string) => {
    // Simulate API call
    setTimeout(() => {
      showNotification("Domain verification initiated. This may take a few minutes.", "info")
    }, 500)
  }

  const handleRenewSSL = (domainId: string) => {
    // Simulate API call
    setTimeout(() => {
      showNotification("SSL certificate renewal initiated.", "success")
    }, 500)
  }

  const handleDeleteDomain = (domainId: string) => {
    // Simulate API call
    setTimeout(() => {
      showNotification("Domain deleted successfully.", "success")
    }, 500)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search domains..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Domain
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Domain</TableHead>
              <TableHead>Tenant</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>SSL</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredDomains.map((domain) => (
              <TableRow key={domain.id}>
                <TableCell className="font-medium">{domain.domain}</TableCell>
                <TableCell>{domain.tenantName}</TableCell>
                <TableCell>
                  {domain.status === "verified" ? (
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Verified
                    </Badge>
                  ) : domain.status === "pending" ? (
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                      <AlertTriangle className="mr-1 h-3 w-3" />
                      Pending
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-100 text-red-800">
                      <XCircle className="mr-1 h-3 w-3" />
                      Failed
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  {domain.sslStatus === "active" ? (
                    <Badge className="bg-green-100 text-green-800">
                      <Shield className="mr-1 h-3 w-3" />
                      Active
                    </Badge>
                  ) : domain.sslStatus === "expiring" ? (
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                      <AlertTriangle className="mr-1 h-3 w-3" />
                      Expiring
                    </Badge>
                  ) : domain.sslStatus === "pending" ? (
                    <Badge variant="outline" className="bg-blue-100 text-blue-800">
                      <RefreshCw className="mr-1 h-3 w-3" />
                      Pending
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-100 text-red-800">
                      <XCircle className="mr-1 h-3 w-3" />
                      Failed
                    </Badge>
                  )}
                </TableCell>
                <TableCell>{new Date(domain.createdAt).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => window.open(`https://${domain.domain}`, "_blank")}>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Visit Domain
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {domain.status !== "verified" && (
                        <DropdownMenuItem onClick={() => handleVerifyDomain(domain.id)}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Verify Domain
                        </DropdownMenuItem>
                      )}
                      {(domain.sslStatus === "expiring" || domain.sslStatus === "failed") && (
                        <DropdownMenuItem onClick={() => handleRenewSSL(domain.id)}>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Renew SSL
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteDomain(domain.id)}>
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Domain
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
