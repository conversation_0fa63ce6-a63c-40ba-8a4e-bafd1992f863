"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { Plus, Trash } from "lucide-react"

interface PromotionChannelsProps {
  data: {
    channels: string[]
    urls: Record<string, string>
    audienceSize: Record<string, string>
    categories: string[]
    sampleLinks: string[]
  }
  onUpdate: (data: any) => void
}

export function PromotionChannels({ data, onUpdate }: PromotionChannelsProps) {
  const [formData, setFormData] = useState(data)
  const [newSampleLink, setNewSampleLink] = useState("")

  const channelOptions = [
    { id: "website", label: "Website/Blog" },
    { id: "social", label: "Social Media Profiles" },
    { id: "youtube", label: "YouTube Channel" },
    { id: "email", label: "Email Newsletter" },
    { id: "other", label: "Other" },
  ]

  const contentCategories = [
    { id: "reviews", label: "Product Reviews" },
    { id: "tutorials", label: "Tutorials & How-tos" },
    { id: "comparisons", label: "Product Comparisons" },
    { id: "lifestyle", label: "Lifestyle Content" },
    { id: "deals", label: "Deals & Discounts" },
    { id: "unboxing", label: "Unboxing Videos" },
    { id: "guides", label: "Buying Guides" },
  ]

  const handleChannelChange = (id: string, checked: boolean) => {
    setFormData((prev) => {
      const channels = checked ? [...prev.channels, id] : prev.channels.filter((item) => item !== id)

      const newData = { ...prev, channels }
      onUpdate(newData)
      return newData
    })
  }

  const handleUrlChange = (channel: string, value: string) => {
    setFormData((prev) => {
      const urls = { ...prev.urls, [channel]: value }
      const newData = { ...prev, urls }
      onUpdate(newData)
      return newData
    })
  }

  const handleAudienceSizeChange = (channel: string, value: string) => {
    setFormData((prev) => {
      const audienceSize = { ...prev.audienceSize, [channel]: value }
      const newData = { ...prev, audienceSize }
      onUpdate(newData)
      return newData
    })
  }

  const handleCategoryChange = (id: string, checked: boolean) => {
    setFormData((prev) => {
      const categories = checked ? [...prev.categories, id] : prev.categories.filter((item) => item !== id)

      const newData = { ...prev, categories }
      onUpdate(newData)
      return newData
    })
  }

  const addSampleLink = () => {
    if (newSampleLink && !formData.sampleLinks.includes(newSampleLink)) {
      setFormData((prev) => {
        const sampleLinks = [...prev.sampleLinks, newSampleLink]
        const newData = { ...prev, sampleLinks }
        onUpdate(newData)
        return newData
      })
      setNewSampleLink("")
    }
  }

  const removeSampleLink = (link: string) => {
    setFormData((prev) => {
      const sampleLinks = prev.sampleLinks.filter((item) => item !== link)
      const newData = { ...prev, sampleLinks }
      onUpdate(newData)
      return newData
    })
  }

  return (
    <div className="space-y-6">
      {/* Channel Types */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Promotion Channels</h3>
        <p className="text-xs text-muted-foreground">
          Select all the channels you plan to use for promoting Sellzio products.
        </p>
        <div className="space-y-3">
          {channelOptions.map((option) => (
            <div key={option.id} className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={`channel-${option.id}`}
                  checked={formData.channels.includes(option.id)}
                  onCheckedChange={(checked) => handleChannelChange(option.id, checked as boolean)}
                />
                <Label htmlFor={`channel-${option.id}`} className="text-sm font-medium">
                  {option.label}
                </Label>
              </div>

              {formData.channels.includes(option.id) && (
                <div className="ml-6 grid gap-4 rounded-md border border-border/60 bg-muted/30 p-3 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor={`url-${option.id}`} className="text-xs">
                      URL / Username
                    </Label>
                    <Input
                      id={`url-${option.id}`}
                      value={formData.urls[option.id] || ""}
                      onChange={(e) => handleUrlChange(option.id, e.target.value)}
                      placeholder={`Enter your ${option.label} URL or username`}
                      className="h-8 text-sm"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`audience-${option.id}`} className="text-xs">
                      Audience Size / Followers
                    </Label>
                    <Input
                      id={`audience-${option.id}`}
                      value={formData.audienceSize[option.id] || ""}
                      onChange={(e) => handleAudienceSizeChange(option.id, e.target.value)}
                      placeholder="e.g., 5,000 followers"
                      className="h-8 text-sm"
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Content Categories */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Content Categories</h3>
        <p className="text-xs text-muted-foreground">What type of content do you create or plan to create?</p>
        <div className="grid gap-2 sm:grid-cols-2">
          {contentCategories.map((category) => (
            <div key={category.id} className="flex items-center space-x-2">
              <Checkbox
                id={`category-${category.id}`}
                checked={formData.categories.includes(category.id)}
                onCheckedChange={(checked) => handleCategoryChange(category.id, checked as boolean)}
              />
              <Label htmlFor={`category-${category.id}`} className="text-sm font-normal">
                {category.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Sample Content Links */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Sample Content Links</h3>
        <p className="text-xs text-muted-foreground">
          Please provide links to sample content that represents your work.
        </p>

        <div className="flex space-x-2">
          <Input
            value={newSampleLink}
            onChange={(e) => setNewSampleLink(e.target.value)}
            placeholder="https://example.com/your-content"
            className="text-sm"
          />
          <Button type="button" variant="outline" size="sm" onClick={addSampleLink} disabled={!newSampleLink}>
            <Plus className="mr-1 h-4 w-4" />
            Add
          </Button>
        </div>

        {formData.sampleLinks.length > 0 && (
          <div className="rounded-md border border-border/60 bg-muted/30 p-3">
            <h4 className="mb-2 text-xs font-medium">Your Sample Links:</h4>
            <ul className="space-y-2">
              {formData.sampleLinks.map((link, index) => (
                <li key={index} className="flex items-center justify-between text-sm">
                  <a
                    href={link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="truncate text-primary hover:underline"
                  >
                    {link}
                  </a>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeSampleLink(link)}
                    className="h-6 w-6 p-0"
                  >
                    <Trash className="h-4 w-4 text-muted-foreground" />
                  </Button>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Engagement Metrics */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Engagement Metrics</h3>
        <div className="space-y-2">
          <Label htmlFor="engagement">Please share any relevant engagement metrics for your channels</Label>
          <Textarea
            id="engagement"
            placeholder="e.g., Average views per post, click-through rates, engagement rates, etc."
            className="min-h-[100px] text-sm"
          />
        </div>
      </div>
    </div>
  )
}
