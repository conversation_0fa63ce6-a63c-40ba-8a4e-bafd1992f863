import type { PaymentGateway, Payment, PaymentRequest, PaymentResponse } from "../models/payment"

// Mock data untuk development
const mockPaymentGateways: PaymentGateway[] = [
  {
    id: "midtrans",
    name: "Midtrans",
    isActive: true,
    methods: [
      {
        id: "midtrans_bca_va",
        name: "BCA Virtual Account",
        type: "virtual_account",
        isActive: true,
        logo: "/payment/bca.png",
        instructions: [
          "Buka aplikasi mobile banking BCA atau ATM BCA",
          "Pilih menu Transfer > Virtual Account",
          "Masukkan nomor virtual account yang diberikan",
          "Konfirmasi detail pembayaran dan selesaikan transaksi",
        ],
      },
      {
        id: "midtrans_mandiri_va",
        name: "Mandiri Virtual Account",
        type: "virtual_account",
        isActive: true,
        logo: "/payment/mandiri.png",
      },
      {
        id: "midtrans_bni_va",
        name: "BNI Virtual Account",
        type: "virtual_account",
        isActive: true,
        logo: "/payment/bni.png",
      },
      {
        id: "midtrans_gopay",
        name: "GoPay",
        type: "e_wallet",
        isActive: true,
        logo: "/payment/gopay.png",
      },
      {
        id: "midtrans_credit_card",
        name: "Kartu Kredit",
        type: "credit_card",
        isActive: true,
        logo: "/payment/credit-card.png",
      },
    ],
  },
  {
    id: "xendit",
    name: "Xendit",
    isActive: true,
    methods: [
      {
        id: "xendit_bca_va",
        name: "BCA Virtual Account",
        type: "virtual_account",
        isActive: true,
        logo: "/payment/bca.png",
      },
      {
        id: "xendit_dana",
        name: "DANA",
        type: "e_wallet",
        isActive: true,
        logo: "/payment/dana.png",
      },
      {
        id: "xendit_ovo",
        name: "OVO",
        type: "e_wallet",
        isActive: true,
        logo: "/payment/ovo.png",
      },
      {
        id: "xendit_alfamart",
        name: "Alfamart",
        type: "retail_outlet",
        isActive: true,
        logo: "/payment/alfamart.png",
      },
    ],
  },
]

const mockPayments: Payment[] = [
  {
    id: "1",
    orderId: "ORD-123456",
    amount: 250000,
    status: "completed",
    paymentMethod: {
      id: "midtrans_bca_va",
      name: "BCA Virtual Account",
      type: "virtual_account",
    },
    paymentGateway: {
      id: "midtrans",
      name: "Midtrans",
    },
    transactionId: "TRX-123456",
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: "2",
    orderId: "ORD-234567",
    amount: 175000,
    status: "pending",
    paymentMethod: {
      id: "midtrans_gopay",
      name: "GoPay",
      type: "e_wallet",
    },
    paymentGateway: {
      id: "midtrans",
      name: "Midtrans",
    },
    expiryTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: "3",
    orderId: "ORD-345678",
    amount: 320000,
    status: "failed",
    paymentMethod: {
      id: "xendit_bca_va",
      name: "BCA Virtual Account",
      type: "virtual_account",
    },
    paymentGateway: {
      id: "xendit",
      name: "Xendit",
    },
    createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
  },
]

export const paymentAPI = {
  // Payment Gateways
  getPaymentGateways: async (): Promise<PaymentGateway[]> => {
    // Dalam implementasi sebenarnya, ini akan memanggil API
    // return api.get('/payment/gateways');
    return Promise.resolve(mockPaymentGateways)
  },

  // Payments
  getPayments: async (): Promise<Payment[]> => {
    // return api.get('/payments');
    return Promise.resolve(mockPayments)
  },

  getPayment: async (id: string): Promise<Payment> => {
    // return api.get(`/payments/${id}`);
    const payment = mockPayments.find((p) => p.id === id)
    if (!payment) {
      throw new Error("Pembayaran tidak ditemukan")
    }
    return Promise.resolve(payment)
  },

  createPayment: async (request: PaymentRequest): Promise<PaymentResponse> => {
    // return api.post('/payments', request);

    // Simulasi pembayaran baru
    const paymentGateway = mockPaymentGateways.find((g) => g.id === request.paymentGatewayId)
    const paymentMethod = paymentGateway?.methods.find((m) => m.id === request.paymentMethodId)

    if (!paymentGateway || !paymentMethod) {
      throw new Error("Metode pembayaran tidak valid")
    }

    const newPayment: Payment = {
      id: `${Date.now()}`,
      orderId: request.orderId,
      amount: request.amount,
      status: "pending",
      paymentMethod: {
        id: paymentMethod.id,
        name: paymentMethod.name,
        type: paymentMethod.type,
      },
      paymentGateway: {
        id: paymentGateway.id,
        name: paymentGateway.name,
      },
      expiryTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      paymentInstructions: paymentMethod.instructions,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    const response: PaymentResponse = {
      payment: newPayment,
    }

    // Simulasi respons berdasarkan jenis pembayaran
    if (paymentMethod.type === "virtual_account") {
      response.virtualAccountNumber = "8888" + Math.floor(******** + Math.random() * ********).toString()
    } else if (paymentMethod.type === "e_wallet") {
      response.redirectUrl = `https://example.com/pay/${newPayment.id}`
      response.qrCode = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(`https://example.com/pay/${newPayment.id}`)}`
    } else if (paymentMethod.type === "retail_outlet") {
      response.paymentCode = Math.floor(********0000 + Math.random() * ************).toString()
    } else if (paymentMethod.type === "credit_card") {
      response.redirectUrl = `https://example.com/pay/${newPayment.id}`
    }

    return Promise.resolve(response)
  },

  checkPaymentStatus: async (id: string): Promise<Payment> => {
    // return api.get(`/payments/${id}/status`);
    const payment = mockPayments.find((p) => p.id === id)
    if (!payment) {
      throw new Error("Pembayaran tidak ditemukan")
    }

    // Simulasi perubahan status
    const randomStatus = Math.random()
    if (randomStatus > 0.7) {
      payment.status = "completed"
    } else if (randomStatus > 0.4) {
      payment.status = "processing"
    }

    payment.updatedAt = new Date().toISOString()

    return Promise.resolve(payment)
  },

  cancelPayment: async (id: string): Promise<Payment> => {
    // return api.post(`/payments/${id}/cancel`);
    const payment = mockPayments.find((p) => p.id === id)
    if (!payment) {
      throw new Error("Pembayaran tidak ditemukan")
    }

    if (payment.status !== "pending") {
      throw new Error("Hanya pembayaran dengan status pending yang dapat dibatalkan")
    }

    payment.status = "failed"
    payment.updatedAt = new Date().toISOString()

    return Promise.resolve(payment)
  },
}
