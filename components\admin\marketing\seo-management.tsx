"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { DateRangePicker } from "@/components/analytics/date-range-picker"
import { LineChart } from "@/components/ui/charts"
import {
  Search,
  ArrowUpRight,
  ArrowDownRight,
  AlertCircle,
  CheckCircle,
  XCircle,
  FileCode,
  Globe,
  Code,
  Plus,
  Trash,
  Edit,
  RefreshCw,
} from "lucide-react"

// Mock data for SEO health
const seoHealthData = {
  score: 78,
  issues: {
    critical: 3,
    warnings: 12,
    passed: 85,
  },
  metrics: [
    { name: "Mobile Usability", score: 92, status: "good" },
    { name: "Page Speed", score: 76, status: "average" },
    { name: "Meta Tags", score: 85, status: "good" },
    { name: "Content Quality", score: 68, status: "average" },
    { name: "Backlinks", score: 62, status: "average" },
    { name: "Structured Data", score: 90, status: "good" },
  ],
  recentIssues: [
    { page: "/products/category/electronics", issue: "Missing meta description", severity: "warning" },
    { page: "/blog/top-10-products", issue: "Slow page load (3.8s)", severity: "critical" },
    { page: "/about-us", issue: "Image missing alt text", severity: "warning" },
    { page: "/contact", issue: "Duplicate title tag", severity: "warning" },
    { page: "/store/premium-items", issue: "Broken links detected", severity: "critical" },
  ],
}

// Mock data for keyword performance
const keywordData = {
  keywords: [
    { keyword: "online marketplace platform", position: 8, change: 3, volume: 5400, difficulty: "High" },
    { keyword: "multi vendor ecommerce", position: 12, change: -2, volume: 8200, difficulty: "High" },
    { keyword: "create online store", position: 15, change: 5, volume: 12500, difficulty: "Medium" },
    { keyword: "sell products online", position: 22, change: 1, volume: 18300, difficulty: "High" },
    { keyword: "ecommerce solution", position: 18, change: -4, volume: 6700, difficulty: "Medium" },
    { keyword: "marketplace software", position: 9, change: 2, volume: 4300, difficulty: "Medium" },
    { keyword: "start selling online", position: 25, change: 0, volume: 9100, difficulty: "Medium" },
    { keyword: "best ecommerce platform", position: 31, change: -3, volume: 22400, difficulty: "Very High" },
  ],
  performance: [
    { date: "Jan", position: 24 },
    { date: "Feb", position: 22 },
    { date: "Mar", position: 19 },
    { date: "Apr", position: 21 },
    { date: "May", position: 18 },
    { date: "Jun", position: 16 },
    { date: "Jul", position: 15 },
    { date: "Aug", position: 14 },
    { date: "Sep", position: 12 },
    { date: "Oct", position: 13 },
    { date: "Nov", position: 11 },
    { date: "Dec", position: 10 },
  ],
}

// Mock data for meta tags
const metaTagsData = {
  pages: [
    {
      url: "/",
      title: "Sellzio - Multi-vendor Marketplace Platform",
      description: "Create and manage your own multi-vendor marketplace with Sellzio's powerful SaaS platform.",
      keywords: "marketplace, ecommerce, multi-vendor, online store",
      ogImage: "/images/home-og.jpg",
      status: "good",
    },
    {
      url: "/features",
      title: "Platform Features - Sellzio",
      description: "Explore the powerful features that make Sellzio the best choice for your online marketplace.",
      keywords: "features, marketplace features, ecommerce tools",
      ogImage: "/images/features-og.jpg",
      status: "good",
    },
    {
      url: "/pricing",
      title: "Pricing Plans - Sellzio",
      description: "Affordable pricing plans for businesses of all sizes. Start your marketplace today.",
      keywords: "pricing, plans, subscription, marketplace cost",
      ogImage: "/images/pricing-og.jpg",
      status: "warning",
    },
    {
      url: "/blog",
      title: "Ecommerce Blog - Sellzio",
      description: "",
      keywords: "blog, ecommerce tips, marketplace guides",
      ogImage: "",
      status: "error",
    },
    {
      url: "/contact",
      title: "Contact Us - Sellzio",
      description: "Get in touch with our team for support or inquiries about our marketplace platform.",
      keywords: "contact, support, help, inquiries",
      ogImage: "/images/contact-og.jpg",
      status: "good",
    },
  ],
}

// Mock data for sitemaps
const sitemapData = {
  sitemaps: [
    { name: "Main Sitemap", url: "/sitemap.xml", pages: 124, lastUpdated: "2023-10-15", status: "active" },
    {
      name: "Products Sitemap",
      url: "/sitemap-products.xml",
      pages: 1542,
      lastUpdated: "2023-10-14",
      status: "active",
    },
    { name: "Blog Sitemap", url: "/sitemap-blog.xml", pages: 87, lastUpdated: "2023-10-10", status: "active" },
    {
      name: "Categories Sitemap",
      url: "/sitemap-categories.xml",
      pages: 48,
      lastUpdated: "2023-09-28",
      status: "active",
    },
    { name: "Stores Sitemap", url: "/sitemap-stores.xml", pages: 215, lastUpdated: "2023-10-12", status: "active" },
  ],
  settings: {
    autoGenerate: true,
    frequency: "daily",
    includedSections: ["products", "categories", "blog", "stores", "pages"],
    excludedUrls: ["/admin", "/checkout", "/user", "/cart"],
    pingSearchEngines: true,
  },
}

// Mock data for structured data
const structuredDataData = {
  templates: [
    {
      name: "Product",
      type: "Product",
      usage: 1542,
      fields: ["name", "description", "price", "image", "brand", "sku", "availability"],
    },
    {
      name: "Store",
      type: "Organization",
      usage: 215,
      fields: ["name", "description", "logo", "address", "telephone", "email"],
    },
    {
      name: "Blog Post",
      type: "Article",
      usage: 87,
      fields: ["headline", "author", "datePublished", "image", "articleBody"],
    },
    {
      name: "FAQ",
      type: "FAQPage",
      usage: 12,
      fields: ["questions", "answers"],
    },
    {
      name: "Review",
      type: "Review",
      usage: 856,
      fields: ["reviewBody", "ratingValue", "author", "itemReviewed"],
    },
  ],
}

export function SEOManagement() {
  const [dateRange, setDateRange] = useState({ from: new Date(2023, 0, 1), to: new Date() })
  const [activeTab, setActiveTab] = useState("health")

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">SEO Management</h2>
        <div className="flex items-center gap-2">
          <DateRangePicker dateRange={dateRange} onChange={setDateRange} />
          <Button>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Data
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="health">SEO Health</TabsTrigger>
          <TabsTrigger value="keywords">Keywords</TabsTrigger>
          <TabsTrigger value="metatags">Meta Tags</TabsTrigger>
          <TabsTrigger value="sitemap">Sitemap</TabsTrigger>
          <TabsTrigger value="structured">Structured Data</TabsTrigger>
        </TabsList>

        {/* SEO Health Dashboard */}
        <TabsContent value="health" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overall SEO Score</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center space-y-2">
                  <div className="relative flex h-24 w-24 items-center justify-center rounded-full bg-muted">
                    <span className="text-3xl font-bold">{seoHealthData.score}</span>
                    <svg className="absolute h-24 w-24" viewBox="0 0 100 100">
                      <circle
                        className="stroke-primary stroke-2 fill-none"
                        cx="50"
                        cy="50"
                        r="40"
                        strokeDasharray={`${seoHealthData.score * 2.51} 251`}
                        strokeLinecap="round"
                        transform="rotate(-90 50 50)"
                      />
                    </svg>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {seoHealthData.score >= 80 ? "Good" : seoHealthData.score >= 60 ? "Average" : "Poor"}
                  </span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Critical Issues</CardTitle>
                <XCircle className="h-4 w-4 text-destructive" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{seoHealthData.issues.critical}</div>
                <p className="text-xs text-muted-foreground">
                  {seoHealthData.issues.critical > 0 ? "Needs immediate attention" : "No critical issues"}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Warnings</CardTitle>
                <AlertCircle className="h-4 w-4 text-amber-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{seoHealthData.issues.warnings}</div>
                <p className="text-xs text-muted-foreground">
                  {seoHealthData.issues.warnings > 0 ? "Should be addressed" : "No warnings"}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Passed Checks</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{seoHealthData.issues.passed}</div>
                <p className="text-xs text-muted-foreground">
                  {Math.round(
                    (seoHealthData.issues.passed /
                      (seoHealthData.issues.passed + seoHealthData.issues.warnings + seoHealthData.issues.critical)) *
                      100,
                  )}
                  % of all checks
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>SEO Metrics</CardTitle>
                <CardDescription>Performance across key SEO factors</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {seoHealthData.metrics.map((metric) => (
                  <div key={metric.name} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{metric.name}</span>
                      <span className="text-sm font-medium">{metric.score}/100</span>
                    </div>
                    <Progress
                      value={metric.score}
                      className={
                        metric.status === "good"
                          ? "bg-muted [&>div]:bg-green-500"
                          : metric.status === "average"
                            ? "bg-muted [&>div]:bg-amber-500"
                            : "bg-muted [&>div]:bg-destructive"
                      }
                    />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Issues</CardTitle>
                <CardDescription>Issues that need attention</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Page</TableHead>
                      <TableHead>Issue</TableHead>
                      <TableHead>Severity</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {seoHealthData.recentIssues.map((issue, i) => (
                      <TableRow key={i}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            <span className="max-w-[200px] truncate">{issue.page}</span>
                          </div>
                        </TableCell>
                        <TableCell>{issue.issue}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={
                              issue.severity === "critical"
                                ? "border-destructive text-destructive"
                                : "border-amber-500 text-amber-500"
                            }
                          >
                            {issue.severity}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>SEO Improvement Recommendations</CardTitle>
              <CardDescription>Actions to improve your SEO performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="rounded-lg border p-4">
                  <h3 className="flex items-center text-sm font-medium">
                    <AlertCircle className="mr-2 h-4 w-4 text-destructive" />
                    Fix critical page speed issues
                  </h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    3 pages have load times exceeding 3.5 seconds. Optimize images and reduce JavaScript to improve
                    performance.
                  </p>
                  <Button variant="outline" size="sm" className="mt-2">
                    View Details
                  </Button>
                </div>
                <div className="rounded-lg border p-4">
                  <h3 className="flex items-center text-sm font-medium">
                    <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
                    Add missing meta descriptions
                  </h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    12 pages are missing meta descriptions. Add unique, descriptive meta descriptions to improve
                    click-through rates.
                  </p>
                  <Button variant="outline" size="sm" className="mt-2">
                    View Pages
                  </Button>
                </div>
                <div className="rounded-lg border p-4">
                  <h3 className="flex items-center text-sm font-medium">
                    <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
                    Fix broken links
                  </h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    5 broken internal links detected. Update or remove these links to improve user experience and
                    crawlability.
                  </p>
                  <Button variant="outline" size="sm" className="mt-2">
                    View Links
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Keyword Performance Tracker */}
        <TabsContent value="keywords" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Keyword Rankings</CardTitle>
                <CardDescription>Average position over time</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <LineChart
                  data={keywordData.performance}
                  index="date"
                  categories={["position"]}
                  colors={["blue"]}
                  valueFormatter={(value) => `Pos. ${value}`}
                  showLegend={false}
                  startEndOnly={false}
                  className="h-full"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0">
                <div>
                  <CardTitle>Keyword Research</CardTitle>
                  <CardDescription>Find new keyword opportunities</CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <div className="grid flex-1 gap-2">
                    <Label htmlFor="keyword" className="sr-only">
                      Keyword
                    </Label>
                    <Input id="keyword" placeholder="Enter a keyword to research..." className="h-9" />
                  </div>
                  <Button type="submit" size="sm" className="h-9">
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
                <div className="mt-4 space-y-3">
                  <div className="flex items-center justify-between rounded-lg border p-3">
                    <div>
                      <p className="font-medium">ecommerce platform</p>
                      <p className="text-sm text-muted-foreground">22,800 monthly searches</p>
                    </div>
                    <Badge>High Competition</Badge>
                  </div>
                  <div className="flex items-center justify-between rounded-lg border p-3">
                    <div>
                      <p className="font-medium">multi vendor marketplace</p>
                      <p className="text-sm text-muted-foreground">6,400 monthly searches</p>
                    </div>
                    <Badge>Medium Competition</Badge>
                  </div>
                  <div className="flex items-center justify-between rounded-lg border p-3">
                    <div>
                      <p className="font-medium">create online store</p>
                      <p className="text-sm text-muted-foreground">12,500 monthly searches</p>
                    </div>
                    <Badge>High Competition</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Tracked Keywords</CardTitle>
                  <CardDescription>Performance of your tracked keywords</CardDescription>
                </div>
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Keywords
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Keyword</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Change</TableHead>
                    <TableHead>Search Volume</TableHead>
                    <TableHead>Difficulty</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {keywordData.keywords.map((keyword, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">{keyword.keyword}</TableCell>
                      <TableCell>{keyword.position}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {keyword.change > 0 ? (
                            <>
                              <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />
                              <span className="text-green-500">+{keyword.change}</span>
                            </>
                          ) : keyword.change < 0 ? (
                            <>
                              <ArrowDownRight className="mr-1 h-4 w-4 text-destructive" />
                              <span className="text-destructive">{keyword.change}</span>
                            </>
                          ) : (
                            <span>0</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{keyword.volume.toLocaleString()}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{keyword.difficulty}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon">
                            <Search className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Meta Tag Manager */}
        <TabsContent value="metatags" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Meta Tag Manager</CardTitle>
                  <CardDescription>Manage meta tags for your pages</CardDescription>
                </div>
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Page
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>URL</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {metaTagsData.pages.map((page, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <span className="max-w-[200px] truncate">{page.url}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-[200px] truncate" title={page.title}>
                          {page.title}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-[200px] truncate" title={page.description}>
                          {page.description || <span className="text-destructive">Missing</span>}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={
                            page.status === "good"
                              ? "border-green-500 text-green-500"
                              : page.status === "warning"
                                ? "border-amber-500 text-amber-500"
                                : "border-destructive text-destructive"
                          }
                        >
                          {page.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Search className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Edit Meta Tags</CardTitle>
              <CardDescription>Update meta information for a specific page</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="page-url">Page URL</Label>
                  <Input id="page-url" placeholder="/features" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="page-title">Page Title</Label>
                  <Input id="page-title" placeholder="Platform Features - Sellzio" />
                  <p className="text-xs text-muted-foreground">Recommended length: 50-60 characters (currently: 25)</p>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="meta-description">Meta Description</Label>
                  <Textarea
                    id="meta-description"
                    placeholder="Explore the powerful features that make Sellzio the best choice for your online marketplace."
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    Recommended length: 150-160 characters (currently: 92)
                  </p>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="meta-keywords">Meta Keywords</Label>
                  <Input id="meta-keywords" placeholder="features, marketplace features, ecommerce tools" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="og-image">OG Image URL</Label>
                  <Input id="og-image" placeholder="/images/features-og.jpg" />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline">Cancel</Button>
                  <Button>Save Changes</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Sitemap Configuration */}
        <TabsContent value="sitemap" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Sitemap Files</CardTitle>
                <CardDescription>Manage your XML sitemaps</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Pages</TableHead>
                      <TableHead>Last Updated</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sitemapData.sitemaps.map((sitemap, i) => (
                      <TableRow key={i}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            <FileCode className="mr-2 h-4 w-4 text-muted-foreground" />
                            {sitemap.name}
                          </div>
                        </TableCell>
                        <TableCell>{sitemap.pages.toLocaleString()}</TableCell>
                        <TableCell>{sitemap.lastUpdated}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="ghost" size="icon">
                              <Globe className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                <div className="mt-4 flex justify-end">
                  <Button>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Regenerate All
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Sitemap Settings</CardTitle>
                <CardDescription>Configure sitemap generation</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="auto-generate" checked={sitemapData.settings.autoGenerate} />
                    <Label htmlFor="auto-generate">Auto-generate sitemaps</Label>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="frequency">Generation Frequency</Label>
                    <Select defaultValue={sitemapData.settings.frequency}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hourly">Hourly</SelectItem>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Included Sections</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {["products", "categories", "blog", "stores", "pages"].map((section) => (
                        <div key={section} className="flex items-center space-x-2">
                          <Checkbox
                            id={`section-${section}`}
                            checked={sitemapData.settings.includedSections.includes(section)}
                          />
                          <Label htmlFor={`section-${section}`}>
                            {section.charAt(0).toUpperCase() + section.slice(1)}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="excluded-urls">Excluded URLs (one per line)</Label>
                    <Textarea id="excluded-urls" value={sitemapData.settings.excludedUrls.join("\n")} rows={4} />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="ping-search-engines" checked={sitemapData.settings.pingSearchEngines} />
                    <Label htmlFor="ping-search-engines">Ping search engines after generation</Label>
                  </div>
                  <div className="flex justify-end">
                    <Button>Save Settings</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Structured Data Manager */}
        <TabsContent value="structured" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Structured Data Templates</CardTitle>
                  <CardDescription>Manage JSON-LD schema templates</CardDescription>
                </div>
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  New Template
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Schema Type</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Fields</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {structuredDataData.templates.map((template, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <Code className="mr-2 h-4 w-4 text-muted-foreground" />
                          {template.name}
                        </div>
                      </TableCell>
                      <TableCell>{template.type}</TableCell>
                      <TableCell>{template.usage.toLocaleString()}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {template.fields.slice(0, 3).map((field, i) => (
                            <Badge key={i} variant="secondary" className="text-xs">
                              {field}
                            </Badge>
                          ))}
                          {template.fields.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{template.fields.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Code className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Edit Schema Template</CardTitle>
              <CardDescription>Modify structured data template</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="template-name">Template Name</Label>
                    <Input id="template-name" placeholder="Product" />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="schema-type">Schema Type</Label>
                    <Select defaultValue="Product">
                      <SelectTrigger>
                        <SelectValue placeholder="Select schema type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Product">Product</SelectItem>
                        <SelectItem value="Organization">Organization</SelectItem>
                        <SelectItem value="Article">Article</SelectItem>
                        <SelectItem value="FAQPage">FAQPage</SelectItem>
                        <SelectItem value="Review">Review</SelectItem>
                        <SelectItem value="Event">Event</SelectItem>
                        <SelectItem value="LocalBusiness">LocalBusiness</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="schema-json">JSON-LD Schema</Label>
                  <Textarea
                    id="schema-json"
                    className="font-mono text-sm"
                    rows={10}
                    value={`{
  "@context": "https://schema.org",
  "@type": "Product",
  "name": "{{product.name}}",
  "description": "{{product.description}}",
  "image": "{{product.image}}",
  "sku": "{{product.sku}}",
  "brand": {
    "@type": "Brand",
    "name": "{{product.brand}}"
  },
  "offers": {
    "@type": "Offer",
    "price": "{{product.price}}",
    "priceCurrency": "{{currency}}",
    "availability": "{{product.availability}}"
  }
}`}
                  />
                </div>
                <div className="grid gap-2">
                  <Label>Template Variables</Label>
                  <div className="rounded-md border p-4">
                    <div className="grid grid-cols-2 gap-2">
                      {[
                        "product.name",
                        "product.description",
                        "product.image",
                        "product.sku",
                        "product.brand",
                        "product.price",
                        "currency",
                        "product.availability",
                      ].map((variable) => (
                        <div key={variable} className="flex items-center space-x-2">
                          <Badge variant="outline" className="font-mono">
                            {`{{${variable}}}`}
                          </Badge>
                          <span className="text-sm text-muted-foreground">→</span>
                          <span className="text-sm">Dynamic value</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline">Cancel</Button>
                  <Button>Save Template</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
