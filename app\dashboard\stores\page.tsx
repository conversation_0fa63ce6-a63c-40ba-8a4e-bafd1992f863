import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PlusCircle } from "lucide-react"
import { StoreList } from "@/components/dashboard/stores/store-list"

export default function StoresPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Toko</h1>
        <Button asChild>
          <Link href="/dashboard/stores/create">
            <PlusCircle className="mr-2 h-4 w-4" />
            Buat Toko Baru
          </Link>
        </Button>
      </div>

      <StoreList />
    </div>
  )
}
