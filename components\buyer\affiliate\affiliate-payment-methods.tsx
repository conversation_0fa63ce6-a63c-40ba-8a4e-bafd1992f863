import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Plus, Trash2 } from "lucide-react"

export function AffiliatePaymentMethods() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Payment Methods</CardTitle>
        <CardDescription>Manage your payment methods and preferences</CardDescription>
      </CardHeader>
      <CardContent>
        <RadioGroup defaultValue="bank-transfer" className="space-y-4">
          <div className="flex items-center space-x-2 rounded-md border p-4">
            <RadioGroupItem value="bank-transfer" id="bank-transfer" />
            <Label htmlFor="bank-transfer" className="flex-1 cursor-pointer">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Bank Transfer</p>
                  <p className="text-sm text-gray-500">Account ending in 4589 • Chase Bank</p>
                </div>
                <Badge variant="outline" className="ml-2">
                  Default
                </Badge>
              </div>
            </Label>
            <Button variant="ghost" size="icon">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center space-x-2 rounded-md border p-4">
            <RadioGroupItem value="paypal" id="paypal" />
            <Label htmlFor="paypal" className="flex-1 cursor-pointer">
              <div>
                <p className="font-medium">PayPal</p>
                <p className="text-sm text-gray-500"><EMAIL></p>
              </div>
            </Label>
            <Button variant="ghost" size="icon">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center space-x-2 rounded-md border p-4">
            <RadioGroupItem value="wise" id="wise" />
            <Label htmlFor="wise" className="flex-1 cursor-pointer">
              <div>
                <p className="font-medium">Wise</p>
                <p className="text-sm text-gray-500">Account ending in 7823</p>
              </div>
            </Label>
            <Button variant="ghost" size="icon">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </RadioGroup>

        <Button variant="outline" className="mt-6 w-full">
          <Plus className="mr-2 h-4 w-4" />
          Add New Payment Method
        </Button>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div>
          <p className="text-sm font-medium">Minimum Payout Threshold</p>
          <p className="text-sm text-gray-500">Current: $1,000.00</p>
        </div>
        <Button variant="outline">Change Threshold</Button>
      </CardFooter>
    </Card>
  )
}
