/* Date picker styling for theme compatibility */
input[type="date"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  color: hsl(var(--foreground));
  background-color: hsl(var(--background));
  border-color: hsl(var(--input));
  border-radius: var(--radius);
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  width: 100%;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input[type="date"]:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* Calendar icon styling */
input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(0.5);
  cursor: pointer;
  padding: 0.25rem;
  opacity: 0.7;
  transition: opacity 0.15s ease-in-out;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

/* Dark mode adjustments */
.dark input[type="date"] {
  color: hsl(var(--foreground));
  background-color: hsl(var(--background));
  border-color: hsl(var(--input));
}

.dark input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(0.8);
}
