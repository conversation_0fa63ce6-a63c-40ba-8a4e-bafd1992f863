"use client"

import { useState, useEffect } from "react"
import { VelozioProductGrid } from "@/components/velozio-product-grid"
import { sampleProducts } from "@/data/sample-products"

export default function ProductsPage() {
  const [loading, setLoading] = useState(true)

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1500)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen">
      {/* Header spacer - to account for fixed header */}
      <div className="h-[60px]"></div>

      <main>
        <div className="py-4">
          <h1 className="text-xl font-bold text-center mb-4">Produk Terbaru</h1>
          <VelozioProductGrid products={sampleProducts} loading={loading} />
        </div>
      </main>
    </div>
  )
}
