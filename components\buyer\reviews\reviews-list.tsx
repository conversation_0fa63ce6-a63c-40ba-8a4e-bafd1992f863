"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, Edit, AlertCircle } from "lucide-react"
import Image from "next/image"
import { ReviewDetail } from "./review-detail"
import { ReviewForm } from "./review-form"

// Dummy data untuk ulasan
const mockReviews = [
  {
    id: "1",
    productId: "prod-1",
    productName: "Sepatu Sneakers Premium",
    productImage: "/product-image-1.png",
    rating: 5,
    comment: "Sepatu ini sangat nyaman dipakai dan kualitasnya bagus. Pengiriman juga cepat!",
    date: "2023-10-15",
    storeId: "store-1",
    storeName: "Fashion Store",
    status: "published",
    likes: 12,
    hasImages: true,
    images: ["/product-image-1.png", "/product-image-2.png"],
    orderId: "order-123",
  },
  {
    id: "2",
    productId: "prod-2",
    productName: "Tas Ransel Waterproof",
    productImage: "/product-image-3.png",
    rating: 4,
    comment: "Tas bagus dan tahan air. Namun jahitannya kurang rapi di beberapa bagian.",
    date: "2023-09-28",
    storeId: "store-2",
    storeName: "Outdoor Gear",
    status: "published",
    likes: 5,
    hasImages: false,
    images: [],
    orderId: "order-124",
  },
  {
    id: "3",
    productId: "prod-3",
    productName: "Headphone Bluetooth",
    productImage: "/product-image-4.png",
    rating: 3,
    comment: "Suara bagus tapi baterai cepat habis. Nyaman dipakai untuk waktu singkat.",
    date: "2023-11-02",
    storeId: "store-3",
    storeName: "Tech Gadgets",
    status: "published",
    likes: 2,
    hasImages: false,
    images: [],
    orderId: "order-125",
  },
  {
    id: "4",
    productId: "prod-4",
    productName: "Kemeja Formal Slim Fit",
    productImage: "/product-image-5.png",
    rating: 5,
    comment: "Kemeja sangat pas di badan dan bahannya nyaman. Cocok untuk acara formal.",
    date: "2023-10-20",
    storeId: "store-1",
    storeName: "Fashion Store",
    status: "published",
    likes: 8,
    hasImages: true,
    images: ["/product-image-5.png"],
    orderId: "order-126",
  },
  {
    id: "5",
    productId: "prod-5",
    productName: "Smart Watch Series X",
    productImage: "/product-image-6.png",
    rating: 4,
    comment: "Fitur lengkap dan baterai tahan lama. Aplikasi pendukung masih perlu perbaikan.",
    date: "2023-11-10",
    storeId: "store-3",
    storeName: "Tech Gadgets",
    status: "published",
    likes: 15,
    hasImages: false,
    images: [],
    orderId: "order-127",
  },
]

// Dummy data untuk produk yang bisa diulas
const pendingReviews = [
  {
    id: "pending-1",
    productId: "prod-6",
    productName: "Celana Jeans Premium",
    productImage: "/folded-denim-stack.png",
    orderId: "order-128",
    orderDate: "2023-11-15",
    storeId: "store-1",
    storeName: "Fashion Store",
  },
  {
    id: "pending-2",
    productId: "prod-7",
    productName: "Kacamata Hitam Polarized",
    productImage: "/stylish-sunglasses.png",
    orderId: "order-129",
    orderDate: "2023-11-18",
    storeId: "store-4",
    storeName: "Accessories Shop",
  },
]

export function ReviewsList() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("published")
  const [selectedReview, setSelectedReview] = useState(null)
  const [isEditMode, setIsEditMode] = useState(false)
  const [reviewToEdit, setReviewToEdit] = useState(null)

  // Filter ulasan berdasarkan tab aktif
  const filteredReviews = activeTab === "published" ? mockReviews : pendingReviews

  const handleViewDetail = (review) => {
    setSelectedReview(review)
    setIsEditMode(false)
  }

  const handleEditReview = (review) => {
    setReviewToEdit(review)
    setIsEditMode(true)
  }

  const handleCloseDetail = () => {
    setSelectedReview(null)
  }

  const handleCloseEdit = () => {
    setIsEditMode(false)
    setReviewToEdit(null)
  }

  const handleWriteReview = (pendingReview) => {
    setReviewToEdit({
      productId: pendingReview.productId,
      productName: pendingReview.productName,
      productImage: pendingReview.productImage,
      storeId: pendingReview.storeId,
      storeName: pendingReview.storeName,
      orderId: pendingReview.orderId,
      isNew: true,
    })
    setIsEditMode(true)
  }

  // Render bintang rating
  const renderStars = (rating) => {
    return Array(5)
      .fill(0)
      .map((_, i) => (
        <Star key={i} className={`h-4 w-4 ${i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`} />
      ))
  }

  return (
    <>
      {isEditMode ? (
        <ReviewForm review={reviewToEdit} onClose={handleCloseEdit} />
      ) : selectedReview ? (
        <ReviewDetail
          review={selectedReview}
          onClose={handleCloseDetail}
          onEdit={() => handleEditReview(selectedReview)}
        />
      ) : (
        <Tabs defaultValue="published" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6 grid w-full grid-cols-2">
            <TabsTrigger value="published">Ulasan Saya</TabsTrigger>
            <TabsTrigger value="pending">Belum Diulas</TabsTrigger>
          </TabsList>

          <TabsContent value="published" className="space-y-4">
            {mockReviews.length > 0 ? (
              mockReviews.map((review) => (
                <Card key={review.id} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="flex flex-col md:flex-row">
                      <div className="relative h-32 w-full md:h-auto md:w-32 bg-gray-100">
                        <Image
                          src={review.productImage || "/placeholder.svg"}
                          alt={review.productName}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="flex flex-1 flex-col p-4">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium">{review.productName}</h3>
                          <Badge variant={review.status === "published" ? "success" : "outline"}>
                            {review.status === "published" ? "Dipublikasikan" : "Menunggu"}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{review.storeName}</p>
                        <div className="mt-2 flex items-center">
                          {renderStars(review.rating)}
                          <span className="ml-2 text-sm text-muted-foreground">
                            {new Date(review.date).toLocaleDateString("id-ID", {
                              year: "numeric",
                              month: "long",
                              day: "numeric",
                            })}
                          </span>
                        </div>
                        <p className="mt-2 line-clamp-2 text-sm">{review.comment}</p>
                        <div className="mt-4 flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm" onClick={() => handleViewDetail(review)}>
                              Lihat Detail
                            </Button>
                            <Button variant="ghost" size="icon" onClick={() => handleEditReview(review)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                          {review.hasImages && (
                            <Badge variant="outline" className="ml-auto mr-2">
                              Dengan Foto
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
                <AlertCircle className="mb-2 h-10 w-10 text-muted-foreground" />
                <h3 className="mb-1 font-medium">Belum Ada Ulasan</h3>
                <p className="mb-4 text-sm text-muted-foreground">
                  Anda belum memberikan ulasan untuk produk yang dibeli
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="pending" className="space-y-4">
            {pendingReviews.length > 0 ? (
              pendingReviews.map((item) => (
                <Card key={item.id} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="flex flex-col md:flex-row">
                      <div className="relative h-32 w-full md:h-auto md:w-32 bg-gray-100">
                        <Image
                          src={item.productImage || "/placeholder.svg"}
                          alt={item.productName}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="flex flex-1 flex-col p-4">
                        <h3 className="font-medium">{item.productName}</h3>
                        <p className="text-sm text-muted-foreground">{item.storeName}</p>
                        <p className="mt-1 text-sm">
                          Order #{item.orderId.split("-")[1]} •{" "}
                          {new Date(item.orderDate).toLocaleDateString("id-ID", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}
                        </p>
                        <div className="mt-4">
                          <Button onClick={() => handleWriteReview(item)}>Tulis Ulasan</Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
                <AlertCircle className="mb-2 h-10 w-10 text-muted-foreground" />
                <h3 className="mb-1 font-medium">Tidak Ada Produk untuk Diulas</h3>
                <p className="mb-4 text-sm text-muted-foreground">
                  Semua produk yang Anda beli sudah diulas atau belum ada pembelian
                </p>
                <Button variant="outline" onClick={() => router.push("/buyer/marketplace")}>
                  Jelajahi Produk
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}
    </>
  )
}
