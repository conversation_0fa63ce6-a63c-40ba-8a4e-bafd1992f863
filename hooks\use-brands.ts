import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface Brand {
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo_url?: string;
  website_url?: string;
  email?: string;
  phone?: string;
  address?: string;
  country?: string;
  is_active: boolean;
  sort_order: number;
  meta_title?: string;
  meta_description?: string;
  meta_keywords: string[];
  social_links: Record<string, string>;
  created_at: string;
  updated_at: string;
  // Relations
  product_count?: number;
}

export interface BrandFilters {
  search?: string;
  is_active?: boolean;
  country?: string;
}

export interface BrandCreate {
  name: string;
  slug: string;
  description?: string;
  logo_url?: string;
  website_url?: string;
  email?: string;
  phone?: string;
  address?: string;
  country?: string;
  is_active?: boolean;
  sort_order?: number;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string[];
  social_links?: Record<string, string>;
}

export function useBrands() {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch brands with optional filters
  const fetchBrands = useCallback(async (filters?: BrandFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      
      if (filters?.search) params.append('search', filters.search);
      if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
      if (filters?.country) params.append('country', filters.country);
      
      // Get brands with product counts
      params.append('with_counts', 'true');
      
      const response = await fetch(`/api/brands?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch brands');
      }
      
      const data = await response.json();
      setBrands(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Get single brand
  const getBrand = useCallback(async (id: string): Promise<Brand | null> => {
    try {
      const response = await fetch(`/api/brands/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch brand');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Create brand
  const createBrand = useCallback(async (brandData: BrandCreate): Promise<boolean> => {
    try {
      const response = await fetch('/api/brands', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(brandData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create brand');
      }
      
      const newBrand = await response.json();
      
      // Update local state
      setBrands(prev => [newBrand, ...prev]);
      
      toast.success('Brand berhasil dibuat');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Update brand
  const updateBrand = useCallback(async (id: string, updates: Partial<BrandCreate>): Promise<boolean> => {
    try {
      const response = await fetch(`/api/brands/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update brand');
      }
      
      const updatedBrand = await response.json();
      
      // Update local state
      setBrands(prev => 
        prev.map(brand => brand.id === id ? updatedBrand : brand)
      );
      
      toast.success('Brand berhasil diperbarui');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Delete brand
  const deleteBrand = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/brands/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete brand');
      }
      
      // Update local state
      setBrands(prev => prev.filter(brand => brand.id !== id));
      
      toast.success('Brand berhasil dihapus');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Toggle brand status
  const toggleBrandStatus = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/brands/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'toggle_status',
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to toggle brand status');
      }
      
      const updatedBrand = await response.json();
      
      // Update local state
      setBrands(prev => 
        prev.map(brand => brand.id === id ? updatedBrand : brand)
      );
      
      toast.success(`Brand ${updatedBrand.is_active ? 'diaktifkan' : 'dinonaktifkan'}`);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Get brand statistics
  const getBrandStats = useCallback(async () => {
    try {
      const response = await fetch('/api/brands/stats');
      
      if (!response.ok) {
        throw new Error('Failed to fetch brand stats');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Get countries list
  const getCountries = useCallback(async () => {
    try {
      const response = await fetch('/api/brands/countries');
      
      if (!response.ok) {
        throw new Error('Failed to fetch countries');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return [];
    }
  }, []);

  // Search brands
  const searchBrands = useCallback(async (query: string, limit = 10) => {
    try {
      const response = await fetch(`/api/brands/search?q=${encodeURIComponent(query)}&limit=${limit}`);
      
      if (!response.ok) {
        throw new Error('Failed to search brands');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return [];
    }
  }, []);

  // Refresh brands (re-fetch with current filters)
  const refreshBrands = useCallback(async () => {
    await fetchBrands();
  }, [fetchBrands]);

  // Initial fetch on mount
  useEffect(() => {
    fetchBrands();
  }, [fetchBrands]);

  return {
    brands,
    loading,
    error,
    fetchBrands,
    getBrand,
    createBrand,
    updateBrand,
    deleteBrand,
    toggleBrandStatus,
    getBrandStats,
    getCountries,
    searchBrands,
    refreshBrands,
  };
}
