"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, CheckCircle, DollarSign, TrendingUp, Users } from "lucide-react"

export function AffiliateApplicationIntro() {
  const [showWizard, setShowWizard] = useState(false)

  const handleJoinNow = () => {
    setShowWizard(true)
    // Scroll to wizard
    document.getElementById("affiliate-application-wizard")?.scrollIntoView({ behavior: "smooth" })
  }

  return (
    <div className="space-y-8">
      {/* Hero Banner */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-primary/90 to-primary dark:from-primary/80 dark:to-primary/60">
        <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,transparent,#fff)]"></div>
        <div className="relative grid gap-4 p-8 text-white md:grid-cols-2 md:gap-8 md:p-10">
          <div className="space-y-4">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tight md:text-4xl">Become an Affiliate Partner</h2>
              <p className="text-primary-foreground/90 md:text-lg">
                Earn commissions by promoting products and stores on Sellzio. Join our affiliate program today!
              </p>
            </div>
            <div className="flex flex-wrap gap-2">
              <Badge className="bg-white/20 text-white hover:bg-white/30">Passive Income</Badge>
              <Badge className="bg-white/20 text-white hover:bg-white/30">No Investment</Badge>
              <Badge className="bg-white/20 text-white hover:bg-white/30">Flexible Hours</Badge>
              <Badge className="bg-white/20 text-white hover:bg-white/30">Global Reach</Badge>
            </div>
            <Button onClick={handleJoinNow} size="lg" className="bg-white text-primary hover:bg-white/90">
              Join Now
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
          <div className="hidden items-center justify-center md:flex">
            <Image
              src="/affiliate-marketing.png"
              alt="Affiliate Marketing"
              width={400}
              height={300}
              className="rounded-lg object-cover dark:brightness-110 dark:contrast-125"
            />
          </div>
        </div>
      </div>

      {/* Benefits Cards */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="border-border/40 bg-card">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-xl">
              <DollarSign className="mr-2 h-5 w-5 text-primary" />
              Earning Potential
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-0.5 h-4 w-4 text-primary" />
                <span>Up to 15% commission on each sale</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-0.5 h-4 w-4 text-primary" />
                <span>Recurring commissions on subscription products</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-0.5 h-4 w-4 text-primary" />
                <span>Tiered commission rates based on performance</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-0.5 h-4 w-4 text-primary" />
                <span>Bonuses for high-performing affiliates</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-border/40 bg-card">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-xl">
              <TrendingUp className="mr-2 h-5 w-5 text-primary" />
              Success Stories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Ahmad from Jakarta</p>
                <p className="text-xs text-muted-foreground">
                  "I earned Rp 5,000,000 in my first month promoting fashion products on my Instagram."
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Siti from Surabaya</p>
                <p className="text-xs text-muted-foreground">
                  "My blog about home decor generates Rp 8,000,000 monthly through Sellzio affiliate links."
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Budi from Bandung</p>
                <p className="text-xs text-muted-foreground">
                  "I quit my day job after my YouTube channel started earning Rp 15,000,000 monthly with Sellzio."
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/40 bg-card">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-xl">
              <Users className="mr-2 h-5 w-5 text-primary" />
              Commission Structure
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="font-medium">Standard Rate:</div>
                <div>5-10%</div>
                <div className="font-medium">Premium Products:</div>
                <div>Up to 15%</div>
                <div className="font-medium">Digital Products:</div>
                <div>Up to 30%</div>
                <div className="font-medium">Subscription:</div>
                <div>Recurring 5%</div>
                <div className="font-medium">Cookie Duration:</div>
                <div>30 days</div>
                <div className="font-medium">Payment Threshold:</div>
                <div>Rp 500,000</div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-3">
            <Button onClick={handleJoinNow} className="w-full">
              Join Now
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
