"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Plus,
  FileText,
  Eye,
  PencilIcon,
  Trash,
  MoreHorizontal,
  Copy,
  Filter,
  ChevronDown,
  Calendar,
  Clock,
  LayoutGrid,
  LayoutList,
  Settings,
  CheckCircle2,
  AlertCircle,
  MessageSquare,
  User,
  Tag,
  ImageIcon,
  Bookmark
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk artikel blog
const blogPosts = [
  {
    id: "blog-001",
    title: "10 Tips Memilih Produk Berkualitas di Marketplace",
    slug: "10-tips-memilih-produk-berkualitas",
    status: "published",
    featuredImage: "/assets/blog/product-quality.jpg",
    excerpt: "Panduan lengkap untuk memilih produk berkualitas tinggi ketika berbelanja di marketplace online.",
    author: {
      name: "<PERSON>",
      avatar: "/assets/authors/ahmad.jpg",
      role: "Content Writer"
    },
    category: "Shopping Guide",
    tags: ["tips", "produk", "belanja", "kualitas"],
    createdAt: "2024-01-05T10:00:00",
    updatedAt: "2024-01-10T14:30:00",
    publishedAt: "2024-01-10T15:00:00",
    views: 5600,
    commentsCount: 23,
    isFeatured: true,
    seo: {
      title: "10 Tips Memilih Produk Berkualitas di Marketplace | Blog",
      description: "Panduan lengkap untuk memilih produk berkualitas tinggi ketika berbelanja di marketplace online.",
      keywords: "tips belanja, produk berkualitas, marketplace, belanja online"
    }
  },
  {
    id: "blog-002",
    title: "Cara Menjadi Seller Sukses di Platform Marketplace",
    slug: "cara-menjadi-seller-sukses",
    status: "published",
    featuredImage: "/assets/blog/successful-seller.jpg",
    excerpt: "Panduan lengkap untuk memulai dan mengembangkan bisnis online Anda sebagai seller di marketplace.",
    author: {
      name: "Siti Nurhaliza",
      avatar: "/assets/authors/siti.jpg",
      role: "Business Analyst"
    },
    category: "Seller Tips",
    tags: ["seller", "bisnis online", "tips sukses", "marketplace"],
    createdAt: "2024-01-08T09:30:00",
    updatedAt: "2024-01-12T10:45:00",
    publishedAt: "2024-01-12T11:00:00",
    views: 4200,
    commentsCount: 18,
    isFeatured: true,
    seo: {
      title: "Cara Menjadi Seller Sukses di Platform Marketplace | Blog",
      description: "Panduan lengkap untuk memulai dan mengembangkan bisnis online Anda sebagai seller di marketplace.",
      keywords: "seller sukses, bisnis online, tips marketplace"
    }
  },
  {
    id: "blog-003",
    title: "Tren E-commerce yang Akan Berkembang di 2024",
    slug: "tren-ecommerce-2024",
    status: "published",
    featuredImage: "/assets/blog/ecommerce-trends.jpg",
    excerpt: "Analisis mendalam tentang tren e-commerce yang diprediksi akan berkembang pesat di tahun 2024.",
    author: {
      name: "Budi Santoso",
      avatar: "/assets/authors/budi.jpg",
      role: "E-commerce Specialist"
    },
    category: "Industry Insights",
    tags: ["e-commerce", "tren", "prediksi", "teknologi"],
    createdAt: "2024-01-15T13:00:00",
    updatedAt: "2024-01-18T09:20:00",
    publishedAt: "2024-01-18T10:00:00",
    views: 3800,
    commentsCount: 14,
    isFeatured: false,
    seo: {
      title: "Tren E-commerce yang Akan Berkembang di 2024 | Blog",
      description: "Analisis mendalam tentang tren e-commerce yang diprediksi akan berkembang pesat di tahun 2024.",
      keywords: "tren e-commerce, prediksi 2024, teknologi marketplace"
    }
  },
  {
    id: "blog-004",
    title: "Keamanan Transaksi Online: Apa yang Perlu Diketahui",
    slug: "keamanan-transaksi-online",
    status: "published",
    featuredImage: "/assets/blog/online-security.jpg",
    excerpt: "Informasi penting tentang keamanan transaksi online dan cara melindungi diri dari penipuan.",
    author: {
      name: "Dewi Safitri",
      avatar: "/assets/authors/dewi.jpg",
      role: "Security Expert"
    },
    category: "Security",
    tags: ["keamanan", "transaksi online", "tips", "penipuan"],
    createdAt: "2024-01-20T08:45:00",
    updatedAt: "2024-01-22T14:10:00",
    publishedAt: "2024-01-22T15:00:00",
    views: 5100,
    commentsCount: 27,
    isFeatured: false,
    seo: {
      title: "Keamanan Transaksi Online: Apa yang Perlu Diketahui | Blog",
      description: "Informasi penting tentang keamanan transaksi online dan cara melindungi diri dari penipuan.",
      keywords: "keamanan online, transaksi aman, pencegahan penipuan"
    }
  },
  {
    id: "blog-005",
    title: "Cara Mengoptimalkan Foto Produk untuk Penjualan Online",
    slug: "optimasi-foto-produk",
    status: "draft",
    featuredImage: "/assets/blog/product-photography.jpg",
    excerpt: "Panduan lengkap untuk mengambil dan mengedit foto produk yang menarik untuk meningkatkan penjualan online.",
    author: {
      name: "Rini Wijaya",
      avatar: "/assets/authors/rini.jpg",
      role: "Photography Expert"
    },
    category: "Photography",
    tags: ["fotografi", "produk", "optimasi", "penjualan"],
    createdAt: "2024-01-25T10:20:00",
    updatedAt: "2024-01-26T13:40:00",
    publishedAt: null,
    views: 0,
    commentsCount: 0,
    isFeatured: false,
    seo: {
      title: "Cara Mengoptimalkan Foto Produk untuk Penjualan Online | Blog",
      description: "Panduan lengkap untuk mengambil dan mengedit foto produk yang menarik untuk meningkatkan penjualan online.",
      keywords: "foto produk, fotografi produk, optimasi gambar, penjualan online"
    }
  },
  {
    id: "blog-006",
    title: "Manfaat Program Loyalitas untuk Bisnis Online",
    slug: "manfaat-program-loyalitas",
    status: "scheduled",
    featuredImage: "/assets/blog/loyalty-program.jpg",
    excerpt: "Mengapa program loyalitas penting dan bagaimana mengimplementasikannya dalam bisnis online Anda.",
    author: {
      name: "Ahmad Fauzi",
      avatar: "/assets/authors/ahmad.jpg",
      role: "Content Writer"
    },
    category: "Marketing",
    tags: ["program loyalitas", "customer retention", "marketing", "strategi bisnis"],
    createdAt: "2024-01-27T14:30:00",
    updatedAt: "2024-01-28T11:15:00",
    publishedAt: "2024-02-01T08:00:00",
    views: 0,
    commentsCount: 0,
    isFeatured: false,
    seo: {
      title: "Manfaat Program Loyalitas untuk Bisnis Online | Blog",
      description: "Mengapa program loyalitas penting dan bagaimana mengimplementasikannya dalam bisnis online Anda.",
      keywords: "program loyalitas, customer retention, strategi bisnis online"
    }
  }
]

// Data dummy untuk kategori blog
const blogCategories = [
  { id: "cat-001", name: "Shopping Guide", count: 5 },
  { id: "cat-002", name: "Seller Tips", count: 8 },
  { id: "cat-003", name: "Industry Insights", count: 4 },
  { id: "cat-004", name: "Security", count: 3 },
  { id: "cat-005", name: "Photography", count: 2 },
  { id: "cat-006", name: "Marketing", count: 6 }
]

// Fungsi untuk badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "published":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Dipublikasikan</Badge>
    case "draft":
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>
    case "scheduled":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">Terjadwal</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk format tanggal
function formatDate(dateString: string | null) {
  if (!dateString) return "-"
  
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Fungsi untuk format views
function formatViews(views: number) {
  return views.toLocaleString('id-ID')
}

export default function ContentBlogPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  
  // Filter artikel blog
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = 
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.author.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || post.status === statusFilter
    const matchesCategory = categoryFilter === "all" || post.category === categoryFilter
    
    return matchesSearch && matchesStatus && matchesCategory
  })
  
  // Statistik
  const stats = {
    total: blogPosts.length,
    published: blogPosts.filter(p => p.status === "published").length,
    draft: blogPosts.filter(p => p.status === "draft").length,
    scheduled: blogPosts.filter(p => p.status === "scheduled").length,
    totalViews: blogPosts.reduce((sum, p) => sum + p.views, 0),
    totalComments: blogPosts.reduce((sum, p) => sum + p.commentsCount, 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/content">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Blog</h1>
            <p className="text-muted-foreground">
              Kelola artikel blog dan kategori
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/tenant/dashboard/content/blog/categories">
              <Tag className="h-4 w-4 mr-2" />
              Kategori
            </Link>
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Tulis Artikel
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Artikel</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dipublikasikan</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.published}</div>
            <p className="text-xs text-muted-foreground">
              Draft: {stats.draft}, Terjadwal: {stats.scheduled}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatViews(stats.totalViews)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Komentar</CardTitle>
            <MessageSquare className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.totalComments}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search & Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari artikel..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 flex-wrap">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border rounded-md bg-background"
              >
                <option value="all">Semua Status</option>
                <option value="published">Dipublikasikan</option>
                <option value="draft">Draft</option>
                <option value="scheduled">Terjadwal</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border rounded-md bg-background"
              >
                <option value="all">Semua Kategori</option>
                {blogCategories.map(category => (
                  <option key={category.id} value={category.name}>
                    {category.name} ({category.count})
                  </option>
                ))}
              </select>
              <div className="flex border rounded-md overflow-hidden">
                <Button 
                  variant={viewMode === "list" ? "default" : "ghost"} 
                  size="sm"
                  className="rounded-none"
                  onClick={() => setViewMode("list")}
                >
                  <LayoutList className="h-4 w-4" />
                </Button>
                <Button 
                  variant={viewMode === "grid" ? "default" : "ghost"} 
                  size="sm"
                  className="rounded-none"
                  onClick={() => setViewMode("grid")}
                >
                  <LayoutGrid className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Blog Posts */}
      {viewMode === "list" ? (
        <div className="space-y-4">
          {filteredPosts.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada artikel ditemukan</h3>
                <p className="text-muted-foreground mb-4 text-center">
                  Tidak ada artikel yang cocok dengan filter atau pencarian Anda
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Tulis Artikel
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredPosts.map((post) => (
              <Card key={post.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-semibold text-lg">{post.title}</span>
                          {getStatusBadge(post.status)}
                          {post.isFeatured && (
                            <Badge variant="outline" className="bg-amber-100 text-amber-800">
                              Featured
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <User className="h-3.5 w-3.5" />
                          <span>{post.author.name}</span>
                          <span className="text-muted-foreground">•</span>
                          <Badge variant="outline">{post.category}</Badge>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                          <Calendar className="h-3.5 w-3.5" />
                          <span>
                            {post.status === "published" 
                              ? `Dipublikasikan: ${formatDate(post.publishedAt)}`
                              : post.status === "scheduled" 
                                ? `Dijadwalkan: ${formatDate(post.publishedAt)}`
                                : `Terakhir diupdate: ${formatDate(post.updatedAt)}`}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                          {post.excerpt}
                        </p>
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4 text-blue-600" />
                          <span className="text-sm">{formatViews(post.views)} views</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageSquare className="h-4 w-4 text-purple-600" />
                          <span className="text-sm">{post.commentsCount} komentar</span>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2 justify-end">
                          {post.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {post.tags.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{post.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 pt-2 border-t mt-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        Lihat
                      </Button>
                      <Button size="sm" variant="outline">
                        <PencilIcon className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button size="sm" variant="outline">
                        <Settings className="h-4 w-4 mr-2" />
                        SEO
                      </Button>
                      <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                        <Trash className="h-4 w-4 mr-2" />
                        Hapus
                      </Button>
                      <Button size="sm" variant="outline">
                        <Copy className="h-4 w-4 mr-2" />
                        Duplikat
                      </Button>
                      <Button size="sm" variant="ghost">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      ) : (
        // Grid View
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredPosts.length === 0 ? (
            <Card className="col-span-full">
              <CardContent className="flex flex-col items-center justify-center py-8">
                <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada artikel ditemukan</h3>
                <p className="text-muted-foreground mb-4 text-center">
                  Tidak ada artikel yang cocok dengan filter atau pencarian Anda
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Tulis Artikel
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredPosts.map((post) => (
              <Card key={post.id} className="hover:shadow-md transition-shadow overflow-hidden">
                <div className="bg-muted h-40 relative">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <ImageIcon className="h-12 w-12 text-muted-foreground opacity-30" />
                  </div>
                  <div className="absolute top-2 right-2 flex gap-1">
                    {getStatusBadge(post.status)}
                    {post.isFeatured && (
                      <Badge variant="outline" className="bg-amber-100 text-amber-800">
                        <Bookmark className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    )}
                  </div>
                </div>
                <CardContent className="p-4">
                  <div className="flex flex-col gap-2">
                    <h3 className="font-medium line-clamp-2">{post.title}</h3>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <User className="h-3.5 w-3.5" />
                      <span>{post.author.name}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <Badge variant="outline">{post.category}</Badge>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 text-xs">
                          <Eye className="h-3 w-3 text-blue-600" />
                          <span>{formatViews(post.views)}</span>
                        </div>
                        <div className="flex items-center gap-1 text-xs">
                          <MessageSquare className="h-3 w-3 text-purple-600" />
                          <span>{post.commentsCount}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(post.publishedAt || post.updatedAt)}</span>
                    </div>
                    <div className="flex gap-2 pt-2 mt-2 border-t">
                      <Button size="sm" variant="outline" className="w-full">
                        <PencilIcon className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button size="sm" variant="ghost">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}
      
      {filteredPosts.length > 0 && (
        <div className="flex justify-center mt-4">
          <Button variant="outline" className="mr-2" size="sm">
            Sebelumnya
          </Button>
          <Button variant="outline" size="sm">
            Selanjutnya
          </Button>
        </div>
      )}
    </div>
  )
} 