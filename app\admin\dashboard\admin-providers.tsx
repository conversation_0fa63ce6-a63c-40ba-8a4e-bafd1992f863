"use client"

import type { ReactNode } from "react"
import { ConfirmationProvider } from "@/components/admin/ui/confirmation-dialog"
import { NotificationsProvider } from "@/components/admin/ui/notifications"
import { SidebarProvider } from "@/components/ui/sidebar"

export function AdminProviders({ children }: { children: ReactNode }) {
  return (
    <NotificationsProvider>
      <ConfirmationProvider>
        <SidebarProvider>{children}</SidebarProvider>
      </ConfirmationProvider>
    </NotificationsProvider>
  )
}
