import { NextRequest, NextResponse } from 'next/server';
import { storeSettingService } from '@/lib/services/store-settings';

// GET - Mendapatkan store setting berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    const setting = await storeSettingService.getSettingById(id);
    
    if (!setting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(setting);
  } catch (error) {
    console.error('Error fetching setting:', error);
    return NextResponse.json(
      { error: 'Failed to fetch setting' },
      { status: 500 }
    );
  }
}

// PUT - Update store setting berdasarkan ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    
    const updatedSetting = await storeSettingService.updateSettingById(id, body);
    
    return NextResponse.json(updatedSetting);
  } catch (error) {
    console.error('Error updating setting:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update setting';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// DELETE - Hapus store setting berdasarkan ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    await storeSettingService.deleteSetting(id);
    
    return NextResponse.json({ 
      message: 'Setting deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting setting:', error);
    return NextResponse.json(
      { error: 'Failed to delete setting' },
      { status: 500 }
    );
  }
}

// PATCH - Update setting value by key
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { setting_value } = body;
    
    if (setting_value === undefined) {
      return NextResponse.json(
        { error: 'setting_value is required' },
        { status: 400 }
      );
    }
    
    // Get setting first to validate
    const setting = await storeSettingService.getSettingById(id);
    if (!setting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      );
    }
    
    // Validate the value
    const validation = storeSettingService.validateSettingValue(setting, setting_value);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }
    
    const updatedSetting = await storeSettingService.updateSettingById(id, { setting_value });
    
    return NextResponse.json(updatedSetting);
  } catch (error) {
    console.error('Error updating setting value:', error);
    return NextResponse.json(
      { error: 'Failed to update setting value' },
      { status: 500 }
    );
  }
}
