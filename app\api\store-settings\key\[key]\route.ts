import { NextRequest, NextResponse } from 'next/server';
import { storeSettingService } from '@/lib/services/store-settings';

// GET - Mendapatkan store setting berdasarkan key
export async function GET(
  request: NextRequest,
  { params }: { params: { key: string } }
) {
  try {
    const { key } = params;
    
    const setting = await storeSettingService.getSetting(key);
    
    if (!setting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(setting);
  } catch (error) {
    console.error('Error fetching setting:', error);
    return NextResponse.json(
      { error: 'Failed to fetch setting' },
      { status: 500 }
    );
  }
}

// PATCH - Update setting value by key
export async function PATCH(
  request: NextRequest,
  { params }: { params: { key: string } }
) {
  try {
    const { key } = params;
    const body = await request.json();
    const { setting_value } = body;
    
    if (setting_value === undefined) {
      return NextResponse.json(
        { error: 'setting_value is required' },
        { status: 400 }
      );
    }
    
    // Get setting first to validate
    const setting = await storeSettingService.getSetting(key);
    if (!setting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      );
    }
    
    // Validate the value
    const validation = storeSettingService.validateSettingValue(setting, setting_value);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }
    
    const updatedSetting = await storeSettingService.updateSetting(key, setting_value);
    
    return NextResponse.json(updatedSetting);
  } catch (error) {
    console.error('Error updating setting:', error);
    return NextResponse.json(
      { error: 'Failed to update setting' },
      { status: 500 }
    );
  }
}
