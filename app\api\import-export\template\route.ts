import { NextRequest, NextResponse } from 'next/server';

// GET - Download import template
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type') || 'csv';
    
    if (!['csv', 'xlsx'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid template type. Use csv or xlsx.' },
        { status: 400 }
      );
    }
    
    // Template headers for product import
    const headers = [
      'name',
      'sku',
      'description',
      'price',
      'compare_at_price',
      'cost_per_item',
      'category',
      'brand',
      'tags',
      'status',
      'inventory_quantity',
      'track_inventory',
      'weight',
      'weight_unit',
      'requires_shipping',
      'featured_image',
      'images',
      'seo_title',
      'seo_description',
      'handle'
    ];
    
    // Sample data
    const sampleData = [
      [
        'iPhone 15 Pro Max',
        'IPHONE-15-PRO-MAX-256',
        'iPhone 15 Pro Max dengan storage 256GB, warna Natural Titanium',
        '18999000',
        '19999000',
        '15000000',
        'Electronics > Smartphones',
        'Apple',
        'smartphone,apple,5g,premium',
        'active',
        '10',
        'true',
        '221',
        'g',
        'true',
        'https://example.com/iphone-15-pro-max.jpg',
        'https://example.com/iphone-1.jpg,https://example.com/iphone-2.jpg',
        'iPhone 15 Pro Max - Smartphone Premium Terbaru',
        'Beli iPhone 15 Pro Max dengan harga terbaik. Garansi resmi, cicilan 0%.',
        'iphone-15-pro-max-256gb'
      ],
      [
        'Nike Air Max 270',
        'NIKE-AM270-BLK-42',
        'Sepatu running Nike Air Max 270 warna hitam ukuran 42',
        '1299000',
        '1499000',
        '800000',
        'Fashion > Shoes > Athletic',
        'Nike',
        'sepatu,running,sport,nike',
        'active',
        '25',
        'true',
        '350',
        'g',
        'true',
        'https://example.com/nike-air-max-270.jpg',
        'https://example.com/nike-1.jpg,https://example.com/nike-2.jpg',
        'Nike Air Max 270 - Sepatu Running Premium',
        'Sepatu running Nike Air Max 270 dengan teknologi terdepan untuk kenyamanan maksimal.',
        'nike-air-max-270-black-42'
      ]
    ];
    
    if (type === 'csv') {
      // Generate CSV content
      const csvContent = [
        headers.join(','),
        ...sampleData.map(row => 
          row.map(cell => `"${cell.toString().replace(/"/g, '""')}"`).join(',')
        )
      ].join('\n');
      
      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="product_import_template.csv"',
        },
      });
    } else {
      // For XLSX, we'll return a simple CSV for now
      // In a real implementation, you would use a library like xlsx to generate proper Excel files
      const csvContent = [
        headers.join(','),
        ...sampleData.map(row => 
          row.map(cell => `"${cell.toString().replace(/"/g, '""')}"`).join(',')
        )
      ].join('\n');
      
      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': 'attachment; filename="product_import_template.xlsx"',
        },
      });
    }
  } catch (error) {
    console.error('Error generating template:', error);
    return NextResponse.json(
      { error: 'Failed to generate template' },
      { status: 500 }
    );
  }
}
