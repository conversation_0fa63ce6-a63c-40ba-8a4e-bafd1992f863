// Pastikan file ini ada dan berisi implementasi yang benar
export const TENANTS_STORAGE_KEY = "sellzio_tenants_data"

// Data tenant default
const DEFAULT_TENANTS = [
  {
    id: "1",
    name: "Acme Corporation",
    domain: "acme.sellzio.com",
    plan: "Enterprise",
    status: "active",
    storeCount: 12,
    userCount: 45,
    revenue: "$5,240",
    createdAt: "2023-01-15",
  },
  {
    id: "2",
    name: "TechStart Inc",
    domain: "techstart.sellzio.com",
    plan: "Professional",
    status: "active",
    storeCount: 5,
    userCount: 18,
    revenue: "$1,890",
    createdAt: "2023-02-22",
  },
  {
    id: "3",
    name: "Global Retail",
    domain: "globalretail.sellzio.com",
    plan: "Enterprise",
    status: "active",
    storeCount: 28,
    userCount: 120,
    revenue: "$12,450",
    createdAt: "2022-11-05",
  },
  {
    id: "4",
    name: "Fashion Forward",
    domain: "fashionforward.sellzio.com",
    plan: "Basic",
    status: "suspended",
    storeCount: 1,
    userCount: 3,
    revenue: "$240",
    createdAt: "2023-03-10",
    suspensionReason: "Pelanggaran ketentuan layanan",
    suspensionDate: "2023-03-10",
  },
  {
    id: "5",
    name: "Digital Solutions",
    domain: "digitalsolutions.sellzio.com",
    plan: "Professional",
    status: "active",
    storeCount: 7,
    userCount: 22,
    revenue: "$3,120",
    createdAt: "2023-01-30",
  },
]

// Get all tenants
export async function getAllTenants() {
  try {
    const storedTenants = localStorage.getItem(TENANTS_STORAGE_KEY)
    if (storedTenants) {
      return JSON.parse(storedTenants)
    } else {
      // Initialize with default data if not exists
      localStorage.setItem(TENANTS_STORAGE_KEY, JSON.stringify(DEFAULT_TENANTS))
      return DEFAULT_TENANTS
    }
  } catch (error) {
    console.error("Error loading tenants:", error)
    return DEFAULT_TENANTS
  }
}

// Get tenant by ID
export async function getTenantById(id: string) {
  try {
    const tenants = await getAllTenants()
    const tenant = tenants.find((t) => t.id === id)
    if (!tenant) {
      throw new Error(`Tenant with ID ${id} not found`)
    }
    return tenant
  } catch (error) {
    console.error("Error getting tenant by ID:", error)
    throw error
  }
}

// Add tenant
export async function addTenant(tenant: any) {
  try {
    const tenants = await getAllTenants()
    const newTenants = [...tenants, tenant]
    localStorage.setItem(TENANTS_STORAGE_KEY, JSON.stringify(newTenants))
    return { success: true }
  } catch (error) {
    console.error("Error adding tenant:", error)
    throw error
  }
}

// Suspend tenant
export async function suspendTenant(tenantId, reason, endDate = undefined) {
  try {
    const tenants = await getAllTenants()
    const updatedTenants = tenants.map((tenant) =>
      tenant.id === tenantId
        ? {
            ...tenant,
            status: "suspended",
            suspensionReason: reason,
            suspensionDate: new Date().toISOString().split("T")[0],
            suspensionEndDate: endDate,
          }
        : tenant,
    )

    localStorage.setItem(TENANTS_STORAGE_KEY, JSON.stringify(updatedTenants))

    // Trigger storage event for other tabs
    window.dispatchEvent(
      new StorageEvent("storage", {
        key: TENANTS_STORAGE_KEY,
        newValue: localStorage.getItem(TENANTS_STORAGE_KEY),
      }),
    )

    return { success: true }
  } catch (error) {
    console.error("Error suspending tenant:", error)
    throw error
  }
}

// Reactivate tenant
export async function reactivateTenant(tenantId, reason) {
  try {
    const tenants = await getAllTenants()
    const updatedTenants = tenants.map((tenant) =>
      tenant.id === tenantId
        ? {
            ...tenant,
            status: "active",
            suspensionReason: undefined,
            suspensionDate: undefined,
            suspensionEndDate: undefined,
            reactivationReason: reason,
            reactivationDate: new Date().toISOString().split("T")[0],
          }
        : tenant,
    )

    localStorage.setItem(TENANTS_STORAGE_KEY, JSON.stringify(updatedTenants))

    // Trigger storage event for other tabs
    window.dispatchEvent(
      new StorageEvent("storage", {
        key: TENANTS_STORAGE_KEY,
        newValue: localStorage.getItem(TENANTS_STORAGE_KEY),
      }),
    )

    return { success: true }
  } catch (error) {
    console.error("Error reactivating tenant:", error)
    throw error
  }
}

// Delete tenant
export async function deleteTenant(tenantId) {
  try {
    const tenants = await getAllTenants()
    const updatedTenants = tenants.filter((tenant) => tenant.id !== tenantId)

    localStorage.setItem(TENANTS_STORAGE_KEY, JSON.stringify(updatedTenants))

    // Trigger storage event for other tabs
    window.dispatchEvent(
      new StorageEvent("storage", {
        key: TENANTS_STORAGE_KEY,
        newValue: localStorage.getItem(TENANTS_STORAGE_KEY),
      }),
    )

    return { success: true }
  } catch (error) {
    console.error("Error deleting tenant:", error)
    throw error
  }
}

// Reset tenants to default
export async function resetTenants() {
  try {
    localStorage.setItem(TENANTS_STORAGE_KEY, JSON.stringify(DEFAULT_TENANTS))

    // Trigger storage event for other tabs
    window.dispatchEvent(
      new StorageEvent("storage", {
        key: TENANTS_STORAGE_KEY,
        newValue: localStorage.getItem(TENANTS_STORAGE_KEY),
      }),
    )

    return { success: true }
  } catch (error) {
    console.error("Error resetting tenants:", error)
    throw error
  }
}

export const tenants = DEFAULT_TENANTS
