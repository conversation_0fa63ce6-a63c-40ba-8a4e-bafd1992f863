"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

export function StoreRegisterForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const { register } = useAuth()

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    storeName: "",
    storeDescription: "",
    storeCategory: "",
    phone: "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData((prev) => ({ ...prev, [id]: value }))
  }

  const handleSelectChange = (value: string, field: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setIsLoading(true)
    setError(null)

    // Validasi password
    if (formData.password !== formData.confirmPassword) {
      setError("Password dan konfirmasi password tidak cocok")
      setIsLoading(false)
      return
    }

    try {
      await register(formData.name, formData.email, formData.password, "store", {
        storeName: formData.storeName,
        storeDescription: formData.storeDescription,
        storeCategory: formData.storeCategory,
        phone: formData.phone,
      })
      router.push("/store/dashboard")
    } catch (err) {
      console.error("Registration error:", err)
      setError("Pendaftaran gagal. Silakan coba lagi.")
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <form onSubmit={onSubmit}>
        <CardContent className="space-y-4 pt-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="name">Nama Pemilik</Label>
            <Input id="name" value={formData.name} onChange={handleChange} placeholder="Nama Lengkap" required />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Nomor Telepon</Label>
            <Input
              id="phone"
              type="tel"
              value={formData.phone}
              onChange={handleChange}
              placeholder="08123456789"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="storeName">Nama Toko</Label>
            <Input
              id="storeName"
              value={formData.storeName}
              onChange={handleChange}
              placeholder="Nama Toko Anda"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="storeCategory">Kategori Toko</Label>
            <Select
              value={formData.storeCategory}
              onValueChange={(value) => handleSelectChange(value, "storeCategory")}
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih kategori toko" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="fashion">Fashion</SelectItem>
                <SelectItem value="electronics">Elektronik</SelectItem>
                <SelectItem value="beauty">Kecantikan</SelectItem>
                <SelectItem value="food">Makanan & Minuman</SelectItem>
                <SelectItem value="health">Kesehatan</SelectItem>
                <SelectItem value="home">Rumah Tangga</SelectItem>
                <SelectItem value="other">Lainnya</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="storeDescription">Deskripsi Toko</Label>
            <Textarea
              id="storeDescription"
              value={formData.storeDescription}
              onChange={handleChange}
              placeholder="Deskripsi singkat tentang toko Anda"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={formData.password}
              onChange={handleChange}
              required
              minLength={6}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Konfirmasi Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
            />
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Memproses..." : "Daftar sebagai Toko"}
          </Button>
          <div className="text-center text-sm">
            Sudah punya akun?{" "}
            <Link href="/auth/login?role=store" className="text-primary hover:underline">
              Masuk
            </Link>
          </div>
        </CardFooter>
      </form>
    </Card>
  )
}
