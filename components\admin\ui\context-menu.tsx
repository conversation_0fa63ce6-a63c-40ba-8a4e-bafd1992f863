"use client"

import type React from "react"

import { MoreHorizontal } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"

interface ContextAction {
  id: string
  label: string
  icon?: React.ReactNode
  onClick: () => void
  destructive?: boolean
}

interface ContextMenuProps {
  itemId: string
  itemName?: string
  actions: ContextAction[]
  className?: string
}

export function ContextMenu({ itemId, itemName, actions, className }: ContextMenuProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className={className}>
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {itemName && (
          <>
            <DropdownMenuLabel>{itemName}</DropdownMenuLabel>
            <DropdownMenuSeparator />
          </>
        )}
        {actions.map((action) => (
          <DropdownMenuItem
            key={action.id}
            onClick={(e) => {
              e.stopPropagation()
              action.onClick()
            }}
            className={action.destructive ? "text-destructive" : ""}
          >
            {action.icon}
            {action.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
