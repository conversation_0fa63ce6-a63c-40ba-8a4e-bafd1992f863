"use client"

import { useState } from "react"
import { MapPin, Home, Briefcase } from "lucide-react"

// Sample address data
const addresses = [
  {
    id: "1",
    nickname: "Home",
    fullName: "John Doe",
    street: "Jl. Sudirman No. 123",
    detail: "Apartment 4B, Tower C",
    city: "Jakarta",
    province: "DKI Jakarta",
    postalCode: "12930",
    country: "Indonesia",
    phone: "+62812345678",
    type: "home",
    isDefaultShipping: true,
    isDefaultBilling: true,
  },
  {
    id: "2",
    nickname: "Office",
    fullName: "John Doe",
    street: "Jl. Gatot Subroto No. 456",
    detail: "12th Floor, XYZ Building",
    city: "Jakarta",
    province: "DKI Jakarta",
    postalCode: "12950",
    country: "Indonesia",
    phone: "+62812345678",
    type: "work",
    isDefaultShipping: false,
    isDefaultBilling: false,
  },
]

export const dynamic = "force-dynamic"

export default function AddressesPage() {
  const [userAddresses, setUserAddresses] = useState(addresses)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null)

  const handleDeleteAddress = (id: string) => {
    setAddressToDelete(id)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (addressToDelete) {
      setUserAddresses(userAddresses.filter((address) => address.id !== addressToDelete))
      setIsDeleteDialogOpen(false)
      setAddressToDelete(null)
    }
  }

  const renderAddressTypeIcon = (type: string) => {
    switch (type) {
      case "home":
        return <Home className="h-4 w-4" />
      case "work":
        return <Briefcase className="h-4 w-4" />
      default:
        return <MapPin className="h-4 w-4" />
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Loading Addresses...</h1>
        <p className="text-gray-500">Please wait while we redirect you to your addresses.</p>

        {/* Client-side redirect script */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Check if we're in the browser
              if (typeof window !== 'undefined') {
                // Redirect to the client-side page
                window.location.href = '/buyer/dashboard/account/addresses-client';
              }
            `,
          }}
        />
      </div>
    </div>
  )
}
