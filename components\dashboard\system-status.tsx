import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"

interface SystemStatusItem {
  name: string
  status: "online" | "offline" | "warning"
  uptime: string
}

interface SystemStatusProps {
  title: string
  items: SystemStatusItem[]
}

export function SystemStatus({ title, items }: SystemStatusProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {items.map((item) => (
            <div key={item.name} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div
                  className={`h-2.5 w-2.5 rounded-full ${
                    item.status === "online"
                      ? "bg-green-500"
                      : item.status === "offline"
                        ? "bg-red-500"
                        : "bg-yellow-500"
                  }`}
                />
                <span className="text-sm font-medium">{item.name}</span>
              </div>
              <div className="text-right">
                <p
                  className={`text-xs ${
                    item.status === "online"
                      ? "text-green-500"
                      : item.status === "offline"
                        ? "text-red-500"
                        : "text-yellow-500"
                  }`}
                >
                  {item.status === "online" ? "Online" : item.status === "offline" ? "Offline" : "Warning"}
                </p>
                <p className="text-xs text-muted-foreground">Uptime: {item.uptime}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
