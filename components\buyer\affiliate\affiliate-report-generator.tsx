import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Download, FileText, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Line<PERSON>hart } from "lucide-react"

export function AffiliateReportGenerator() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Report Generator</CardTitle>
        <CardDescription>Create custom reports based on your affiliate performance data</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="date-range">Date Range</Label>
              <Select defaultValue="last30">
                <SelectTrigger id="date-range">
                  <SelectValue placeholder="Select date range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last7">Last 7 days</SelectItem>
                  <SelectItem value="last30">Last 30 days</SelectItem>
                  <SelectItem value="last90">Last 90 days</SelectItem>
                  <SelectItem value="lastYear">Last year</SelectItem>
                  <SelectItem value="custom">Custom range</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Metrics to Include</Label>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="clicks" defaultChecked />
                  <Label htmlFor="clicks">Clicks</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="conversions" defaultChecked />
                  <Label htmlFor="conversions">Conversions</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="revenue" defaultChecked />
                  <Label htmlFor="revenue">Revenue</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="commission" defaultChecked />
                  <Label htmlFor="commission">Commission</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="products" />
                  <Label htmlFor="products">Products</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="categories" />
                  <Label htmlFor="categories">Categories</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="customers" />
                  <Label htmlFor="customers">Customers</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="geography" />
                  <Label htmlFor="geography">Geography</Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="grouping">Group By</Label>
              <Select defaultValue="day">
                <SelectTrigger id="grouping">
                  <SelectValue placeholder="Select grouping" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Day</SelectItem>
                  <SelectItem value="week">Week</SelectItem>
                  <SelectItem value="month">Month</SelectItem>
                  <SelectItem value="quarter">Quarter</SelectItem>
                  <SelectItem value="year">Year</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Visualization Options</Label>
              <div className="grid grid-cols-2 gap-4">
                <Button variant="outline" className="justify-start">
                  <BarChart className="mr-2 h-4 w-4" />
                  Bar Chart
                </Button>
                <Button variant="outline" className="justify-start">
                  <LineChart className="mr-2 h-4 w-4" />
                  Line Chart
                </Button>
                <Button variant="outline" className="justify-start">
                  <PieChart className="mr-2 h-4 w-4" />
                  Pie Chart
                </Button>
                <Button variant="outline" className="justify-start">
                  <FileText className="mr-2 h-4 w-4" />
                  Table
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Export Format</Label>
              <div className="grid grid-cols-2 gap-4">
                <Button variant="outline" className="justify-start">
                  <Download className="mr-2 h-4 w-4" />
                  CSV
                </Button>
                <Button variant="outline" className="justify-start">
                  <Download className="mr-2 h-4 w-4" />
                  PDF
                </Button>
              </div>
            </div>

            <div className="pt-4">
              <div className="h-[100px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                <p className="text-sm text-gray-500 dark:text-gray-400">Report preview will appear here</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Reset</Button>
        <Button>Generate Report</Button>
      </CardFooter>
    </Card>
  )
}
