"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { CalendarIcon, FileTextIcon, MailIcon, HelpCircleIcon, PaletteIcon, LayoutIcon } from "lucide-react"

export function ContentOverview() {
  const [contentStats, setContentStats] = useState({
    pages: 0,
    blogs: 0,
    emailTemplates: 0,
    helpArticles: 0,
    themes: 0,
  })
  const [recentChanges, setRecentChanges] = useState([])
  const [scheduledContent, setScheduledContent] = useState([])
  const [contentPerformance, setContentPerformance] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulasi pengambilan data
    setTimeout(() => {
      setContentStats({
        pages: 24,
        blogs: 47,
        emailTemplates: 18,
        helpArticles: 56,
        themes: 12,
      })

      setRecentChanges([
        {
          id: 1,
          title: "Updated Homepage Hero Section",
          type: "landing-page",
          author: {
            name: "Alex Johnson",
            avatar: "/placeholder.svg?key=qezvj",
          },
          timestamp: "2 hours ago",
        },
        {
          id: 2,
          title: "New Blog Post: 'Top 10 E-commerce Trends'",
          type: "blog",
          author: {
            name: "Sarah Miller",
            avatar: "/placeholder.svg?key=hxec8",
          },
          timestamp: "5 hours ago",
        },
        {
          id: 3,
          title: "Modified Order Confirmation Email Template",
          type: "email",
          author: {
            name: "David Chen",
            avatar: "/placeholder.svg?key=a75uo",
          },
          timestamp: "Yesterday",
        },
        {
          id: 4,
          title: "Updated FAQ Section in Help Center",
          type: "help-center",
          author: {
            name: "Emily Wilson",
            avatar: "/placeholder.svg?key=o5pv0",
          },
          timestamp: "2 days ago",
        },
        {
          id: 5,
          title: "New Dark Mode Theme Released",
          type: "theme",
          author: {
            name: "Michael Brown",
            avatar: "/placeholder.svg?key=83o0i",
          },
          timestamp: "3 days ago",
        },
      ])

      setScheduledContent([
        {
          id: 1,
          title: "Black Friday Landing Page",
          type: "landing-page",
          scheduledDate: "Nov 20, 2023",
          status: "scheduled",
        },
        {
          id: 2,
          title: "Holiday Season Email Campaign",
          type: "email",
          scheduledDate: "Dec 1, 2023",
          status: "scheduled",
        },
        {
          id: 3,
          title: "Year-End Review Blog Post",
          type: "blog",
          scheduledDate: "Dec 28, 2023",
          status: "draft",
        },
        {
          id: 4,
          title: "New Year Promotion Banner",
          type: "landing-page",
          scheduledDate: "Dec 30, 2023",
          status: "scheduled",
        },
      ])

      setContentPerformance([
        {
          id: 1,
          title: "How to Set Up Your Store",
          type: "help-center",
          views: 12450,
          engagement: "High",
          conversionRate: "8.2%",
        },
        {
          id: 2,
          title: "Summer Collection Landing Page",
          type: "landing-page",
          views: 8320,
          engagement: "Medium",
          conversionRate: "5.7%",
        },
        {
          id: 3,
          title: "10 Tips for Better Product Photos",
          type: "blog",
          views: 6540,
          engagement: "High",
          conversionRate: "4.3%",
        },
        {
          id: 4,
          title: "Welcome Email",
          type: "email",
          views: 15280,
          engagement: "Medium",
          conversionRate: "3.9%",
        },
      ])

      setIsLoading(false)
    }, 1000)
  }, [])

  const getTypeIcon = (type) => {
    switch (type) {
      case "landing-page":
        return <LayoutIcon className="h-4 w-4" />
      case "blog":
        return <FileTextIcon className="h-4 w-4" />
      case "email":
        return <MailIcon className="h-4 w-4" />
      case "help-center":
        return <HelpCircleIcon className="h-4 w-4" />
      case "theme":
        return <PaletteIcon className="h-4 w-4" />
      default:
        return <FileTextIcon className="h-4 w-4" />
    }
  }

  const getTypeColor = (type) => {
    switch (type) {
      case "landing-page":
        return "bg-blue-100 text-blue-800"
      case "blog":
        return "bg-green-100 text-green-800"
      case "email":
        return "bg-purple-100 text-purple-800"
      case "help-center":
        return "bg-yellow-100 text-yellow-800"
      case "theme":
        return "bg-pink-100 text-pink-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "scheduled":
        return "bg-green-100 text-green-800"
      case "draft":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (isLoading) {
    return (
      <div className="grid gap-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
          {[1, 2, 3, 4, 5].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 w-24 rounded bg-gray-200"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 rounded bg-gray-200"></div>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          {[1, 2].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-5 w-32 rounded bg-gray-200"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4].map((j) => (
                    <div key={j} className="flex items-center gap-4">
                      <div className="h-10 w-10 rounded-full bg-gray-200"></div>
                      <div className="space-y-2">
                        <div className="h-4 w-48 rounded bg-gray-200"></div>
                        <div className="h-3 w-24 rounded bg-gray-200"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="grid gap-6">
      {/* Content Stats */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Landing Pages</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <LayoutIcon className="h-5 w-5 text-muted-foreground" />
              <CardTitle>{contentStats.pages}</CardTitle>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Blog Articles</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <FileTextIcon className="h-5 w-5 text-muted-foreground" />
              <CardTitle>{contentStats.blogs}</CardTitle>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Email Templates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <MailIcon className="h-5 w-5 text-muted-foreground" />
              <CardTitle>{contentStats.emailTemplates}</CardTitle>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Help Articles</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <HelpCircleIcon className="h-5 w-5 text-muted-foreground" />
              <CardTitle>{contentStats.helpArticles}</CardTitle>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Themes</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <PaletteIcon className="h-5 w-5 text-muted-foreground" />
              <CardTitle>{contentStats.themes}</CardTitle>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different content views */}
      <Tabs defaultValue="recent" className="w-full">
        <TabsList className="grid w-full grid-cols-3 lg:w-[400px]">
          <TabsTrigger value="recent">Recent Changes</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        {/* Recent Changes Tab */}
        <TabsContent value="recent" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Content Changes</CardTitle>
              <CardDescription>Latest updates across all content types</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {recentChanges.map((item) => (
                  <div key={item.id} className="flex items-start gap-4">
                    <Avatar>
                      <AvatarImage src={item.author.avatar || "/placeholder.svg"} alt={item.author.name} />
                      <AvatarFallback>{item.author.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{item.title}</h4>
                        <Badge variant="outline" className={`${getTypeColor(item.type)} text-xs`}>
                          <span className="mr-1">{getTypeIcon(item.type)}</span>
                          {item.type.replace("-", " ")}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>By {item.author.name}</span>
                        <span>•</span>
                        <span>{item.timestamp}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scheduled Content Tab */}
        <TabsContent value="scheduled" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Content Releases</CardTitle>
              <CardDescription>Upcoming content scheduled for publication</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {scheduledContent.map((item) => (
                  <div key={item.id} className="flex items-start gap-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100">
                      {getTypeIcon(item.type)}
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{item.title}</h4>
                        <Badge variant="outline" className={getStatusColor(item.status)}>
                          {item.status}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <CalendarIcon className="h-4 w-4" />
                        <span>Scheduled for {item.scheduledDate}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Content Performance Metrics</CardTitle>
              <CardDescription>Analytics for top performing content</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative overflow-x-auto">
                <table className="w-full text-left text-sm">
                  <thead className="bg-gray-50 text-xs uppercase">
                    <tr>
                      <th scope="col" className="px-6 py-3">
                        Title
                      </th>
                      <th scope="col" className="px-6 py-3">
                        Type
                      </th>
                      <th scope="col" className="px-6 py-3">
                        Views
                      </th>
                      <th scope="col" className="px-6 py-3">
                        Engagement
                      </th>
                      <th scope="col" className="px-6 py-3">
                        Conversion
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {contentPerformance.map((item) => (
                      <tr key={item.id} className="border-b bg-white">
                        <td className="px-6 py-4 font-medium">{item.title}</td>
                        <td className="px-6 py-4">
                          <Badge variant="outline" className={getTypeColor(item.type)}>
                            <span className="mr-1">{getTypeIcon(item.type)}</span>
                            {item.type.replace("-", " ")}
                          </Badge>
                        </td>
                        <td className="px-6 py-4">{item.views.toLocaleString()}</td>
                        <td className="px-6 py-4">{item.engagement}</td>
                        <td className="px-6 py-4">{item.conversionRate}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
