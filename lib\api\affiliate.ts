import type {
  AffiliateProgram,
  AffiliateUser,
  AffiliateReferral,
  AffiliateCommission,
  AffiliatePayment,
} from "../models/affiliate"

// Mock data untuk development
const mockAffiliatePrograms: AffiliateProgram[] = [
  {
    id: "1",
    tenantId: "1",
    name: "Program Affiliate Standar",
    description: "Program affiliate standar dengan komisi 10% untuk setiap pembelian.",
    commissionRate: 10,
    cookieDuration: 30,
    minimumPayout: 100000,
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "2",
    tenantId: "1",
    name: "Program Affiliate Premium",
    description: "Program affiliate premium dengan komisi 15% untuk setiap pembelian.",
    commissionRate: 15,
    cookieDuration: 60,
    minimumPayout: 200000,
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

const mockAffiliateUser: AffiliateUser = {
  id: "1",
  userId: "1",
  programId: "1",
  referralCode: "USER123",
  balance: 250000,
  totalEarnings: 500000,
  status: "active",
  paymentMethod: {
    type: "bank_transfer",
    details: {
      bankName: "BCA",
      accountNumber: "**********",
      accountName: "John Doe",
    },
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}

const mockAffiliateReferrals: AffiliateReferral[] = [
  {
    id: "1",
    affiliateUserId: "1",
    referredUserId: "2",
    status: "registered",
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: "2",
    affiliateUserId: "1",
    referredUserId: "3",
    status: "purchased",
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: "3",
    affiliateUserId: "1",
    referredUserId: "4",
    status: "pending",
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
  },
]

const mockAffiliateCommissions: AffiliateCommission[] = [
  {
    id: "1",
    affiliateUserId: "1",
    orderId: "1",
    amount: 50000,
    status: "approved",
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: "2",
    affiliateUserId: "1",
    orderId: "2",
    amount: 75000,
    status: "pending",
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: "3",
    affiliateUserId: "1",
    orderId: "3",
    amount: 125000,
    status: "paid",
    createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000).toISOString(),
  },
]

const mockAffiliatePayments: AffiliatePayment[] = [
  {
    id: "1",
    affiliateUserId: "1",
    amount: 250000,
    paymentMethod: {
      type: "bank_transfer",
      details: {
        bankName: "BCA",
        accountNumber: "**********",
        accountName: "John Doe",
      },
    },
    status: "completed",
    transactionId: "TRX123456",
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 29 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: "2",
    affiliateUserId: "1",
    amount: 150000,
    paymentMethod: {
      type: "bank_transfer",
      details: {
        bankName: "BCA",
        accountNumber: "**********",
        accountName: "John Doe",
      },
    },
    status: "pending",
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
  },
]

export const affiliateAPI = {
  // Program Affiliate
  getAffiliatePrograms: async (): Promise<AffiliateProgram[]> => {
    // Dalam implementasi sebenarnya, ini akan memanggil API
    // return api.get('/affiliate/programs');
    return Promise.resolve(mockAffiliatePrograms)
  },

  getAffiliateProgram: async (id: string): Promise<AffiliateProgram> => {
    // return api.get(`/affiliate/programs/${id}`);
    const program = mockAffiliatePrograms.find((p) => p.id === id)
    if (!program) {
      throw new Error("Program affiliate tidak ditemukan")
    }
    return Promise.resolve(program)
  },

  // User Affiliate
  getAffiliateUser: async (): Promise<AffiliateUser> => {
    // return api.get('/affiliate/user');
    return Promise.resolve(mockAffiliateUser)
  },

  updateAffiliateUser: async (data: Partial<AffiliateUser>): Promise<AffiliateUser> => {
    // return api.put('/affiliate/user', data);
    const updated = { ...mockAffiliateUser, ...data, updatedAt: new Date().toISOString() }
    return Promise.resolve(updated)
  },

  // Referral
  getReferrals: async (): Promise<AffiliateReferral[]> => {
    // return api.get('/affiliate/referrals');
    return Promise.resolve(mockAffiliateReferrals)
  },

  generateReferralLink: async (baseUrl: string): Promise<string> => {
    // Dalam implementasi sebenarnya, ini akan memanggil API
    // const response = await api.post('/affiliate/referral-link');
    // return `${baseUrl}?ref=${response.referralCode}`;
    return Promise.resolve(`${baseUrl}?ref=${mockAffiliateUser.referralCode}`)
  },

  // Komisi
  getCommissions: async (): Promise<AffiliateCommission[]> => {
    // return api.get('/affiliate/commissions');
    return Promise.resolve(mockAffiliateCommissions)
  },

  // Pembayaran
  getPayments: async (): Promise<AffiliatePayment[]> => {
    // return api.get('/affiliate/payments');
    return Promise.resolve(mockAffiliatePayments)
  },

  requestPayment: async (amount: number): Promise<AffiliatePayment> => {
    // return api.post('/affiliate/payments', { amount });
    const newPayment: AffiliatePayment = {
      id: `${Date.now()}`,
      affiliateUserId: mockAffiliateUser.id,
      amount,
      paymentMethod: mockAffiliateUser.paymentMethod,
      status: "pending",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    return Promise.resolve(newPayment)
  },
}
