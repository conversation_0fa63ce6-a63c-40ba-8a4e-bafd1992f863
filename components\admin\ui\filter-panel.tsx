"use client"

import { useState } from "react"
import { Filter, Save, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"

interface FilterOption {
  id: string
  label: string
  type: "text" | "select" | "date" | "number" | "boolean"
  options?: { value: string; label: string }[]
}

interface SavedFilter {
  id: string
  name: string
  filters: Record<string, any>
}

interface FilterPanelProps {
  filterOptions: FilterOption[]
  onFilterChange: (filters: Record<string, any>) => void
  savedFilters?: SavedFilter[]
  onSaveFilter?: (name: string, filters: Record<string, any>) => void
  className?: string
}

export function FilterPanel({
  filterOptions,
  onFilterChange,
  savedFilters = [],
  onSaveFilter,
  className,
}: FilterPanelProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [filters, setFilters] = useState<Record<string, any>>({})
  const [filterName, setFilterName] = useState("")
  const [selectedSavedFilter, setSelectedSavedFilter] = useState<string | null>(null)

  const handleFilterChange = (id: string, value: any) => {
    const newFilters = { ...filters, [id]: value }
    setFilters(newFilters)
    onFilterChange(newFilters)
  }

  const handleClearFilters = () => {
    setFilters({})
    setSelectedSavedFilter(null)
    onFilterChange({})
  }

  const handleSaveFilter = () => {
    if (filterName && onSaveFilter) {
      onSaveFilter(filterName, filters)
      setFilterName("")
    }
  }

  const handleSelectSavedFilter = (id: string) => {
    const savedFilter = savedFilters.find((filter) => filter.id === id)
    if (savedFilter) {
      setFilters(savedFilter.filters)
      onFilterChange(savedFilter.filters)
      setSelectedSavedFilter(id)
    }
  }

  return (
    <div className={className}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <div className="flex items-center justify-between">
          <CollapsibleTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              <Filter className="h-4 w-4" />
              Advanced Filters
            </Button>
          </CollapsibleTrigger>
          {Object.keys(filters).length > 0 && (
            <Button variant="ghost" size="sm" onClick={handleClearFilters} className="gap-1">
              <X className="h-3 w-3" /> Clear
            </Button>
          )}
        </div>
        <CollapsibleContent className="mt-4">
          <div className="rounded-md border p-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filterOptions.map((option) => (
                <div key={option.id} className="space-y-2">
                  <Label htmlFor={option.id}>{option.label}</Label>
                  {option.type === "select" ? (
                    <Select
                      value={filters[option.id] || ""}
                      onValueChange={(value) => handleFilterChange(option.id, value)}
                    >
                      <SelectTrigger id={option.id}>
                        <SelectValue placeholder="Select..." />
                      </SelectTrigger>
                      <SelectContent>
                        {option.options?.map((opt) => (
                          <SelectItem key={opt.value} value={opt.value}>
                            {opt.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <Input
                      id={option.id}
                      type={option.type}
                      value={filters[option.id] || ""}
                      onChange={(e) => handleFilterChange(option.id, e.target.value)}
                    />
                  )}
                </div>
              ))}
            </div>
            {savedFilters.length > 0 && (
              <>
                <Separator className="my-4" />
                <div className="flex items-center gap-2">
                  <Label htmlFor="saved-filters">Saved Filters</Label>
                  <Select value={selectedSavedFilter || ""} onValueChange={handleSelectSavedFilter}>
                    <SelectTrigger id="saved-filters" className="w-[200px]">
                      <SelectValue placeholder="Select a saved filter" />
                    </SelectTrigger>
                    <SelectContent>
                      {savedFilters.map((filter) => (
                        <SelectItem key={filter.id} value={filter.id}>
                          {filter.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
            {onSaveFilter && (
              <>
                <Separator className="my-4" />
                <div className="flex items-center gap-2">
                  <Input
                    placeholder="Filter name"
                    value={filterName}
                    onChange={(e) => setFilterName(e.target.value)}
                    className="max-w-[200px]"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSaveFilter}
                    disabled={!filterName || Object.keys(filters).length === 0}
                    className="gap-1"
                  >
                    <Save className="h-4 w-4" /> Save Filter
                  </Button>
                </div>
              </>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
