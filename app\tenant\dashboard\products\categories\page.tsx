"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import {
  ArrowLeft,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Package,
  Grid3X3,
  TrendingUp,
  Settings,
  MoreVertical,
  Filter,
  Tag
} from "lucide-react"
import Link from "next/link"

import { useProductCategories, type ProductCategory } from "@/hooks/use-product-categories"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

function getStatusBadge(isActive: boolean) {
  return isActive ? (
    <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
  ) : (
    <Badge variant="secondary" className="bg-gray-100 text-gray-800">Inactive</Badge>
  )
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export default function ProductCategoriesPage() {
  const {
    categories,
    loading,
    createCategory,
    updateCategory,
    toggleCategoryStatus,
    deleteCategory,
    getCategoryStats
  } = useProductCategories()

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [stats, setStats] = useState<any>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showViewDialog, setShowViewDialog] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [editingCategory, setEditingCategory] = useState<ProductCategory | null>(null)
  const [viewingCategory, setViewingCategory] = useState<ProductCategory | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    description: "",
    parent_id: undefined as string | undefined,
    sort_order: 0,
    is_active: true,
    color: "#3B82F6",
    icon: ""
  })

  // Load stats and categories on mount
  useEffect(() => {
    const loadData = async () => {
      console.log('Loading category stats...')
      const categoryStats = await getCategoryStats()
      console.log('Category stats loaded:', categoryStats)
      setStats(categoryStats)
    }
    loadData()
  }, [getCategoryStats])

  // Debug log categories
  useEffect(() => {
    console.log('Categories updated:', categories)
  }, [categories])

  // Apply filters
  const filteredCategories = categories.filter(cat => {
    const matchesSearch = cat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (cat.description || '').toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" ||
                         (statusFilter === "active" && cat.is_active) ||
                         (statusFilter === "inactive" && !cat.is_active)
    return matchesSearch && matchesStatus
  })

  const handleToggleStatus = async (categoryId: string) => {
    await toggleCategoryStatus(categoryId)
    // Refresh stats
    const categoryStats = await getCategoryStats()
    setStats(categoryStats)
  }

  const handleDelete = async (categoryId: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus kategori ini?')) {
      await deleteCategory(categoryId)
      // Refresh stats
      const categoryStats = await getCategoryStats()
      setStats(categoryStats)
    }
  }

  const handleCreateCategory = async () => {
    console.log('handleCreateCategory called with formData:', formData)

    // Validasi form
    if (!formData.name.trim()) {
      alert('Nama kategori harus diisi')
      return
    }
    if (!formData.slug.trim()) {
      alert('Slug harus diisi')
      return
    }

    setIsCreating(true)
    try {
      // Prepare data for submission - handle parent_id properly
      const submitData = {
        ...formData,
        parent_id: formData.parent_id === "none" || !formData.parent_id ? undefined : formData.parent_id
      }

      // Remove undefined fields
      if (submitData.parent_id === undefined) {
        delete (submitData as any).parent_id
      }

      console.log('Submitting data:', submitData)
      const success = await createCategory(submitData)
      console.log('Create result:', success)

      if (success) {
        setShowCreateDialog(false)
        resetForm()
        // Refresh stats
        const categoryStats = await getCategoryStats()
        setStats(categoryStats)
      }
    } catch (error) {
      console.error('Error in handleCreateCategory:', error)
    } finally {
      setIsCreating(false)
    }
  }

  const handleEditCategory = async () => {
    if (!editingCategory) return

    // Validasi form
    if (!formData.name.trim()) {
      alert('Nama kategori harus diisi')
      return
    }
    if (!formData.slug.trim()) {
      alert('Slug harus diisi')
      return
    }

    setIsUpdating(true)
    try {
      // Prepare data for submission - handle parent_id properly
      const submitData = {
        ...formData,
        parent_id: formData.parent_id === "none" || !formData.parent_id ? undefined : formData.parent_id
      }

      // Remove undefined fields
      if (submitData.parent_id === undefined) {
        delete (submitData as any).parent_id
      }

      const success = await updateCategory(editingCategory.id, submitData)
      if (success) {
        setShowEditDialog(false)
        setEditingCategory(null)
        resetForm()
        // Refresh stats
        const categoryStats = await getCategoryStats()
        setStats(categoryStats)
      }
    } finally {
      setIsUpdating(false)
    }
  }

  const handleOpenEdit = (category: ProductCategory) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      slug: category.slug,
      description: category.description || "",
      parent_id: category.parent_id || "none",
      sort_order: category.sort_order,
      is_active: category.is_active,
      color: category.color,
      icon: category.icon || ""
    })
    setShowEditDialog(true)
  }

  const handleOpenView = (category: ProductCategory) => {
    setViewingCategory(category)
    setShowViewDialog(true)
  }

  const resetForm = () => {
    setFormData({
      name: "",
      slug: "",
      description: "",
      parent_id: undefined,
      sort_order: 0,
      is_active: true,
      color: "#3B82F6",
      icon: ""
    })
  }

  const handleNameChange = (name: string) => {
    const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    setFormData(prev => ({ ...prev, name, slug }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Product Categories</h1>
            <p className="text-muted-foreground">
              Kelola kategori produk dan sub-kategori
            </p>
          </div>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Tambah Kategori
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Kategori</CardTitle>
            <Grid3X3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{stats.total}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Kategori Aktif</CardTitle>
            <Grid3X3 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dengan Produk</CardTitle>
            <Package className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-blue-600">{stats.with_products}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Root Categories</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-orange-600">{stats.root_categories}</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari kategori produk..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Categories List */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="flex items-start gap-6">
                  <Skeleton className="w-20 h-20 rounded-lg" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-6 w-1/3" />
                    <Skeleton className="h-4 w-2/3" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredCategories.map((category) => (
            <Card key={category.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start gap-6">
                  {/* Category Image */}
                  <div className="flex-shrink-0">
                    <div
                      className="w-20 h-20 rounded-lg flex items-center justify-center text-white font-bold text-lg"
                      style={{ backgroundColor: category.color }}
                    >
                      {category.icon ? (
                        <span>{category.icon}</span>
                      ) : (
                        category.name.charAt(0).toUpperCase()
                      )}
                    </div>
                  </div>

                  {/* Category Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold">{category.name}</h3>
                          {getStatusBadge(category.is_active)}
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{category.description}</p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>Slug: /{category.slug}</span>
                          <span>Sort: {category.sort_order}</span>
                          {category.parent && <span>Parent: {category.parent.name}</span>}
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>

                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-muted-foreground">Total Produk</p>
                      <p className="text-lg font-bold text-blue-600">{category.product_count || 0}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Sub-kategori</p>
                      <p className="text-lg font-bold text-purple-600">{category.children?.length || 0}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Sort Order</p>
                      <p className="text-lg font-bold text-orange-600">{category.sort_order}</p>
                    </div>
                  </div>

                  {/* Children Categories */}
                  {category.children && category.children.length > 0 && (
                    <div className="mb-4">
                      <p className="text-xs text-muted-foreground mb-2">Sub-kategori</p>
                      <div className="flex flex-wrap gap-1">
                        {category.children.map((child) => (
                          <Badge key={child.id} variant="outline" className="text-xs">
                            {child.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* SEO Info */}
                  {(category.meta_title || category.meta_description) && (
                    <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                      {category.meta_title && (
                        <>
                          <p className="text-xs text-muted-foreground mb-1">SEO Title</p>
                          <p className="text-sm font-medium">{category.meta_title}</p>
                        </>
                      )}
                      {category.meta_description && (
                        <>
                          <p className="text-xs text-muted-foreground mb-1 mt-2">SEO Description</p>
                          <p className="text-xs text-gray-600">{category.meta_description}</p>
                        </>
                      )}
                    </div>
                  )}

                  {/* Dates & Actions */}
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-muted-foreground">
                      <p>Dibuat: {formatDate(category.created_at)}</p>
                      <p>Update: {formatDate(category.updated_at)}</p>
                    </div>

                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" onClick={() => handleOpenView(category)}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => handleOpenEdit(category)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleToggleStatus(category.id)}
                      >
                        {category.is_active ? 'Nonaktifkan' : 'Aktifkan'}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDelete(category.id)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Hapus
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        </div>
      )}

      {!loading && filteredCategories.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Grid3X3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Tidak ada kategori ditemukan</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || statusFilter !== "all"
                ? "Coba ubah filter atau kata kunci pencarian"
                : "Belum ada kategori produk yang ditambahkan"
              }
            </p>
            {!searchTerm && statusFilter === "all" && (
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Kategori Pertama
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Create Category Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={(open) => {
        setShowCreateDialog(open)
        if (!open) resetForm()
      }}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Tambah Kategori Baru</DialogTitle>
            <DialogDescription>
              Buat kategori produk baru untuk mengorganisir produk Anda.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nama Kategori</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="Masukkan nama kategori"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="slug-kategori"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Deskripsi</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Deskripsi kategori"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="parent">Parent Kategori</Label>
                <Select value={formData.parent_id || "none"} onValueChange={(value) => setFormData(prev => ({ ...prev, parent_id: value === "none" ? undefined : value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih parent kategori" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Tidak ada parent</SelectItem>
                    {categories.filter(cat => !cat.parent_id).map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="sort_order">Sort Order</Label>
                <Input
                  id="sort_order"
                  type="number"
                  value={formData.sort_order}
                  onChange={(e) => setFormData(prev => ({ ...prev, sort_order: Number(e.target.value) }))}
                  placeholder="0"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="color">Warna</Label>
                <Input
                  id="color"
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="icon">Icon (Emoji)</Label>
                <Input
                  id="icon"
                  value={formData.icon}
                  onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                  placeholder="📱"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Batal
            </Button>
            <Button onClick={handleCreateCategory} disabled={isCreating}>
              {isCreating ? "Menyimpan..." : "Simpan Kategori"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Category Dialog */}
      <Dialog open={showEditDialog} onOpenChange={(open) => {
        setShowEditDialog(open)
        if (!open) {
          setEditingCategory(null)
          resetForm()
        }
      }}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Kategori</DialogTitle>
            <DialogDescription>
              Ubah informasi kategori produk.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Nama Kategori</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="Masukkan nama kategori"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-slug">Slug</Label>
                <Input
                  id="edit-slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="slug-kategori"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Deskripsi</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Deskripsi kategori"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-parent">Parent Kategori</Label>
                <Select value={formData.parent_id || "none"} onValueChange={(value) => setFormData(prev => ({ ...prev, parent_id: value === "none" ? undefined : value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih parent kategori" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Tidak ada parent</SelectItem>
                    {categories.filter(cat => !cat.parent_id && cat.id !== editingCategory?.id).map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-sort_order">Sort Order</Label>
                <Input
                  id="edit-sort_order"
                  type="number"
                  value={formData.sort_order}
                  onChange={(e) => setFormData(prev => ({ ...prev, sort_order: Number(e.target.value) }))}
                  placeholder="0"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-color">Warna</Label>
                <Input
                  id="edit-color"
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-icon">Icon (Emoji)</Label>
                <Input
                  id="edit-icon"
                  value={formData.icon}
                  onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                  placeholder="📱"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Batal
            </Button>
            <Button onClick={handleEditCategory} disabled={isUpdating}>
              {isUpdating ? "Menyimpan..." : "Update Kategori"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Category Dialog */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detail Kategori</DialogTitle>
            <DialogDescription>
              Informasi lengkap kategori produk.
            </DialogDescription>
          </DialogHeader>
          {viewingCategory && (
            <div className="grid gap-4 py-4">
              <div className="flex items-center gap-4">
                <div
                  className="w-16 h-16 rounded-lg flex items-center justify-center text-white font-bold text-lg"
                  style={{ backgroundColor: viewingCategory.color }}
                >
                  {viewingCategory.icon ? (
                    <span>{viewingCategory.icon}</span>
                  ) : (
                    viewingCategory.name.charAt(0).toUpperCase()
                  )}
                </div>
                <div>
                  <h3 className="text-xl font-semibold">{viewingCategory.name}</h3>
                  <p className="text-sm text-muted-foreground">/{viewingCategory.slug}</p>
                  {getStatusBadge(viewingCategory.is_active)}
                </div>
              </div>

              {viewingCategory.description && (
                <div>
                  <Label>Deskripsi</Label>
                  <p className="text-sm mt-1">{viewingCategory.description}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Parent Kategori</Label>
                  <p className="text-sm mt-1">
                    {viewingCategory.parent ? viewingCategory.parent.name : "Tidak ada parent"}
                  </p>
                </div>
                <div>
                  <Label>Sort Order</Label>
                  <p className="text-sm mt-1">{viewingCategory.sort_order}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Warna</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <div
                      className="w-6 h-6 rounded border"
                      style={{ backgroundColor: viewingCategory.color }}
                    />
                    <span className="text-sm">{viewingCategory.color}</span>
                  </div>
                </div>
                <div>
                  <Label>Total Produk</Label>
                  <p className="text-sm mt-1">{viewingCategory.product_count || 0}</p>
                </div>
              </div>

              {viewingCategory.children && viewingCategory.children.length > 0 && (
                <div>
                  <Label>Sub-kategori</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {viewingCategory.children.map((child) => (
                      <Badge key={child.id} variant="outline" className="text-xs">
                        {child.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                <div>
                  <Label>Dibuat</Label>
                  <p className="mt-1">{formatDate(viewingCategory.created_at)}</p>
                </div>
                <div>
                  <Label>Terakhir Update</Label>
                  <p className="mt-1">{formatDate(viewingCategory.updated_at)}</p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowViewDialog(false)}>
              Tutup
            </Button>
            <Button onClick={() => {
              setShowViewDialog(false)
              if (viewingCategory) handleOpenEdit(viewingCategory)
            }}>
              Edit Kategori
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
