"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { storesAPI, type CreateStoreData, type UpdateStoreData } from "@/lib/api/stores"
import { FileUpload } from "@/components/ui/file-upload"

interface StoreFormProps {
  id?: string
}

export function StoreForm({ id }: StoreFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(id ? true : false)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const [formData, setFormData] = useState<CreateStoreData | UpdateStoreData>({
    name: "",
    description: "",
    slug: "",
    logo: "",
    banner: "",
  })

  useEffect(() => {
    if (id) {
      const fetchStore = async () => {
        try {
          const store = await storesAPI.getById(id)
          setFormData({
            name: store.name,
            description: store.description || "",
            slug: store.slug,
            logo: store.logo || "",
            banner: store.banner || "",
            tenantId: store.tenantId || "",
          })
          setLoading(false)
        } catch (err) {
          console.error("Error fetching store:", err)
          setError("Gagal memuat data toko. Silakan coba lagi.")
          setLoading(false)
        }
      }

      fetchStore()
    }
  }, [id])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleLogoChange = (url: string) => {
    setFormData((prev) => ({ ...prev, logo: url }))
  }

  const handleBannerChange = (url: string) => {
    setFormData((prev) => ({ ...prev, banner: url }))
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setSubmitting(true)
    setError(null)

    try {
      if (id) {
        await storesAPI.update(id, formData as UpdateStoreData)
      } else {
        await storesAPI.create(formData as CreateStoreData)
      }
      router.push("/dashboard/stores")
    } catch (err: any) {
      console.error("Error saving store:", err)
      setError(err.response?.data?.message || "Gagal menyimpan toko. Silakan coba lagi.")
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="space-y-4 pt-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-24 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </CardFooter>
      </Card>
    )
  }

  return (
    <Card>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4 pt-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="name">Nama Toko</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Masukkan nama toko"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Deskripsi</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description || ""}
              onChange={handleChange}
              placeholder="Masukkan deskripsi toko"
              rows={4}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="slug">Slug</Label>
            <Input
              id="slug"
              name="slug"
              value={formData.slug}
              onChange={handleChange}
              placeholder="toko-anda"
              required
              pattern="^[a-z0-9-]+$"
              title="Slug hanya boleh berisi huruf kecil, angka, dan tanda hubung"
            />
            <p className="text-sm text-muted-foreground">
              Slug akan digunakan untuk URL toko Anda: sellzio.com/store/{formData.slug || "toko-anda"}
            </p>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <FileUpload
              label="Logo Toko"
              folder="store-logos"
              value={formData.logo}
              onChange={handleLogoChange}
              onError={(err) => setError(err.message)}
            />

            <FileUpload
              label="Banner Toko"
              folder="store-banners"
              value={formData.banner}
              onChange={handleBannerChange}
              onError={(err) => setError(err.message)}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={() => router.push("/dashboard/stores")}>
            Batal
          </Button>
          <Button type="submit" disabled={submitting}>
            {submitting ? "Menyimpan..." : id ? "Simpan Perubahan" : "Buat Toko"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
