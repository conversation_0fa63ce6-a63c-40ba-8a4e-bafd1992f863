import axios from 'axios';

// API URL yang akan digunakan
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

// Instance axios untuk API calls
export const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor untuk menambahkan token auth
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Interceptor untuk handling response
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Fallback ke mock data jika terjadi network error
    if (error.message === 'Network Error') {
      console.warn('Network error detected, using mock data');
      return handleMockResponse(error.config);
    }
    return Promise.reject(error);
  }
);

// Fungsi untuk mengembalikan mock data berdasarkan request
function handleMockResponse(config: any) {
  const { url, method } = config;
  
  // Extract tenantId dari URL jika ada query param
  const tenantIdMatch = url.match(/tenantId=([^&]+)/);
  const tenantId = tenantIdMatch ? tenantIdMatch[1] : null;

  // Mock responses untuk tenant-domains
  if (url.includes('/tenant-domains') && tenantId) {
    return {
      data: [
        {
          id: '1',
          tenantId: tenantId,
          domain: 'example.com',
          status: 'verified',
          isPrimary: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          tenantId: tenantId,
          domain: 'store.example.com',
          status: 'pending',
          isPrimary: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      ]
    };
  }
  
  // Default empty response
  return { data: [] };
} 