"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ChevronLeft, Save, Info, Lock, Globe } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"

interface Collection {
  id: string
  name: string
  description: string
  isPublic: boolean
  items: string[]
  createdAt: string
  updatedAt: string
}

interface EditCollectionFormProps {
  collectionId: string
}

export function EditCollectionForm({ collectionId }: EditCollectionFormProps) {
  const [collection, setCollection] = useState<Collection | null>(null)
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [isPublic, setIsPublic] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const router = useRouter()

  // Fungsi untuk mengambil detail koleksi
  const fetchCollectionDetail = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/collections/${collectionId}`)
      if (!response.ok) {
        throw new Error('Gagal mengambil detail koleksi')
      }
      const data = await response.json()
      setCollection(data)
      
      // Set form values
      setName(data.name)
      setDescription(data.description || "")
      setIsPublic(data.isPublic)
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal memuat detail koleksi",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fungsi untuk menyimpan perubahan
  const saveChanges = async () => {
    if (!name.trim()) {
      toast({
        title: "Error",
        description: "Nama koleksi tidak boleh kosong",
        variant: "destructive",
      })
      return
    }

    setIsSaving(true)
    try {
      const response = await fetch(`/api/collections/${collectionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description,
          isPublic,
        }),
      })

      if (!response.ok) {
        throw new Error('Gagal menyimpan perubahan')
      }

      toast({
        title: "Berhasil",
        description: "Koleksi berhasil diperbarui",
      })

      // Redirect ke halaman detail koleksi
      router.push(`/buyer/dashboard/collections/${collectionId}`)
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal menyimpan perubahan",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Effect untuk mengambil data koleksi saat komponen dimuat
  useEffect(() => {
    fetchCollectionDetail()
  }, [collectionId])

  // Tampilkan loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="gap-1">
            <ChevronLeft className="h-4 w-4" />
            <Skeleton className="h-4 w-16" />
          </Button>
        </div>
        <div>
          <Skeleton className="h-8 w-48 mb-6" />
          <div className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-24 w-full" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5" />
              <Skeleton className="h-4 w-36" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Tampilkan pesan jika koleksi tidak ditemukan
  if (!collection) {
    return (
      <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">
        <div className="mb-4 rounded-full bg-muted p-4">
          <Info className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="mb-2 text-xl font-medium">Koleksi Tidak Ditemukan</h3>
        <p className="mb-6 text-muted-foreground">
          Koleksi yang Anda cari tidak ditemukan atau telah dihapus.
        </p>
        <Button onClick={() => router.push('/buyer/dashboard/collections')}>
          Kembali ke Daftar Koleksi
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button 
          variant="ghost" 
          size="sm" 
          className="gap-1"
          onClick={() => router.push(`/buyer/dashboard/collections/${collectionId}`)}
        >
          <ChevronLeft className="h-4 w-4" />
          <span>Kembali ke Detail Koleksi</span>
        </Button>
      </div>

      <div>
        <h1 className="text-2xl font-bold tracking-tight mb-6">Edit Koleksi</h1>
        <div className="space-y-4 max-w-2xl">
          <div className="space-y-2">
            <Label htmlFor="name">Nama Koleksi</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Masukkan nama koleksi"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Deskripsi (Opsional)</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Masukkan deskripsi koleksi"
              rows={4}
            />
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="public"
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
            <div className="grid gap-1.5">
              <Label htmlFor="public" className="flex items-center gap-2">
                Koleksi Publik
                {isPublic ? (
                  <Globe className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <Lock className="h-4 w-4 text-muted-foreground" />
                )}
              </Label>
              <p className="text-sm text-muted-foreground">
                {isPublic 
                  ? "Koleksi ini dapat dilihat oleh pengguna lain."
                  : "Hanya Anda yang dapat melihat koleksi ini."}
              </p>
            </div>
          </div>
          <div className="flex gap-2 pt-4">
            <Button 
              onClick={saveChanges} 
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <span className="animate-spin mr-2">◌</span>
                  Menyimpan...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Simpan Perubahan
                </>
              )}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => router.push(`/buyer/dashboard/collections/${collectionId}`)}
            >
              Batal
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 