"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Copy, Download, FileText, Instagram, Mail } from "lucide-react"
import Image from "next/image"
import { Label } from "@/components/ui/label"

export function AffiliateContentTemplates() {
  const [templateType, setTemplateType] = useState<"review" | "social" | "email" | "blog">("review")

  // Data dummy untuk template
  const templates = {
    review: [
      {
        id: "1",
        title: "Product Review Template 1",
        description: "Template untuk review produk fashion dengan fokus pada kualitas dan kenyamanan",
        imageUrl: "/placeholder.svg?height=200&width=400&query=Product%20Review%20Template",
        content:
          "Saya baru saja mencoba [NAMA PRODUK] dari [NAMA TOKO] dan saya sangat terkesan! Kualitasnya luar biasa dan harganya sangat terjangkau. Saya terutama menyukai [FITUR 1] dan [FITUR 2] yang membuatnya berbeda dari produk lain. Jika Anda mencari [JENIS PRODUK] yang [KEUNGGULAN], saya sangat merekomendasikan produk ini. Cek sendiri di sini: [LINK AFFILIATE]",
      },
      {
        id: "2",
        title: "Product Review Template 2",
        description: "Template untuk review produk elektronik dengan fokus pada fitur dan performa",
        imageUrl: "/placeholder.svg?height=200&width=400&query=Electronics%20Review%20Template",
        content:
          "Review [NAMA PRODUK]: Setelah menggunakan [NAMA PRODUK] selama [DURASI], saya dapat mengatakan bahwa ini adalah salah satu [JENIS PRODUK] terbaik yang pernah saya gunakan. Spesifikasi unggulannya meliputi [SPESIFIKASI 1], [SPESIFIKASI 2], dan [SPESIFIKASI 3]. Performa baterainya [DESKRIPSI BATERAI] dan kualitas [FITUR UTAMA] sangat [PENILAIAN]. Harganya [PENDAPAT TENTANG HARGA] untuk kualitas yang ditawarkan. Dapatkan dengan harga terbaik di sini: [LINK AFFILIATE]",
      },
    ],
    social: [
      {
        id: "3",
        title: "Instagram Post Template",
        description: "Template untuk posting Instagram dengan caption yang menarik",
        imageUrl: "/placeholder.svg?height=200&width=400&query=Instagram%20Post%20Template",
        content:
          "✨ Temukan [NAMA PRODUK] yang mengubah cara saya [AKTIVITAS]! 😍\n\nSaya sudah mencoba banyak [JENIS PRODUK], tapi yang ini benar-benar beda. [DESKRIPSI SINGKAT PENGALAMAN]\n\n🔥 Keunggulan:\n- [KEUNGGULAN 1]\n- [KEUNGGULAN 2]\n- [KEUNGGULAN 3]\n\nSekarang lagi PROMO! Cek link di bio atau swipe up untuk mendapatkannya dengan harga spesial! 🛍️\n\n#[HASHTAG1] #[HASHTAG2] #[HASHTAG3]",
      },
      {
        id: "4",
        title: "Facebook Post Template",
        description: "Template untuk posting Facebook dengan konten yang informatif",
        imageUrl: "/placeholder.svg?height=200&width=400&query=Facebook%20Post%20Template",
        content:
          "REVIEW: [NAMA PRODUK] - Apakah Layak Dibeli?\n\nHai teman-teman! Hari ini saya ingin berbagi pengalaman saya menggunakan [NAMA PRODUK] dari [NAMA TOKO/BRAND].\n\nSaya sudah menggunakannya selama [DURASI] dan inilah pendapat jujur saya:\n\n✅ KELEBIHAN:\n- [KELEBIHAN 1]\n- [KELEBIHAN 2]\n- [KELEBIHAN 3]\n\n⚠️ KEKURANGAN:\n- [KEKURANGAN 1]\n- [KEKURANGAN 2]\n\nKESIMPULAN:\n[KESIMPULAN SINGKAT 1-2 KALIMAT]\n\nJika Anda tertarik, produk ini sedang diskon [PERSENTASE DISKON]! Cek di sini: [LINK AFFILIATE]\n\nJangan lupa share dan tag teman yang mungkin tertarik! 😊",
      },
    ],
    email: [
      {
        id: "5",
        title: "Product Recommendation Email",
        description: "Template email untuk merekomendasikan produk ke subscriber",
        imageUrl: "/placeholder.svg?height=200&width=400&query=Email%20Template",
        content:
          "Subjek: Temukan [NAMA PRODUK] yang Mengubah [AKTIVITAS/PENGALAMAN]\n\nHai [NAMA],\n\nSemoga email ini menemukan Anda dalam keadaan baik.\n\nSaya ingin berbagi tentang produk yang baru-baru ini saya temukan dan sangat saya sukai - [NAMA PRODUK].\n\n[NAMA PRODUK] adalah [DESKRIPSI SINGKAT] yang membantu Anda [MANFAAT UTAMA]. Saya sudah menggunakannya selama [DURASI] dan hasilnya sangat [HASIL/PENGALAMAN].\n\nBeberapa fitur unggulannya:\n\n- [FITUR 1]\n- [FITUR 2]\n- [FITUR 3]\n\nSaat ini mereka sedang mengadakan [JENIS PROMO] dengan diskon hingga [PERSENTASE DISKON]. Saya pikir Anda mungkin tertarik untuk memeriksanya.\n\n[LINK AFFILIATE]\n\nJika Anda memiliki pertanyaan tentang produk ini, jangan ragu untuk membalas email ini.\n\nSalam hangat,\n[NAMA ANDA]",
      },
      {
        id: "6",
        title: "Special Offer Email",
        description: "Template email untuk mempromosikan penawaran khusus atau diskon",
        imageUrl: "/placeholder.svg?height=200&width=400&query=Special%20Offer%20Email",
        content:
          "Subjek: TERBATAS! Diskon [PERSENTASE]% untuk [NAMA PRODUK] - Berakhir [TANGGAL]\n\nHai [NAMA],\n\nKabar baik! Saya baru saja menemukan penawaran SPESIAL untuk [NAMA PRODUK] yang saya tahu Anda minati.\n\n🔥 PENAWARAN TERBATAS WAKTU 🔥\nDiskon [PERSENTASE]% untuk [NAMA PRODUK]\nHanya sampai [TANGGAL]\n\nKenapa [NAMA PRODUK] layak Anda pertimbangkan:\n\n✅ [ALASAN 1]\n✅ [ALASAN 2]\n✅ [ALASAN 3]\n\nJangan lewatkan kesempatan ini! Klik link di bawah untuk mendapatkan penawaran spesial:\n\n[LINK AFFILIATE]\n\nPenawaran ini hanya berlaku sampai [TANGGAL], jadi bertindaklah sekarang!\n\nSalam,\n[NAMA ANDA]",
      },
    ],
    blog: [
      {
        id: "7",
        title: "Product Review Blog Post",
        description: "Template artikel blog untuk review produk mendalam",
        imageUrl: "/placeholder.svg?height=200&width=400&query=Blog%20Review%20Template",
        content:
          "# Review Mendalam: [NAMA PRODUK] - Apakah Layak Dibeli?\n\n## Pendahuluan\n\nHari ini saya akan berbagi pengalaman saya menggunakan [NAMA PRODUK] dari [NAMA BRAND/TOKO]. Saya telah menggunakannya selama [DURASI] untuk [TUJUAN PENGGUNAAN], dan inilah review jujur saya.\n\n## Spesifikasi Produk\n\n- **Nama Produk**: [NAMA LENGKAP PRODUK]\n- **Brand**: [NAMA BRAND]\n- **Harga**: [HARGA]\n- **[SPESIFIKASI 1]**: [DETAIL]\n- **[SPESIFIKASI 2]**: [DETAIL]\n- **[SPESIFIKASI 3]**: [DETAIL]\n\n## Unboxing & First Impression\n\n[DESKRIPSI PENGALAMAN UNBOXING DAN KESAN PERTAMA, 2-3 PARAGRAF]\n\n## Fitur Utama\n\n### [FITUR 1]\n[DESKRIPSI DETAIL FITUR 1 DAN PENGALAMAN MENGGUNAKANNYA]\n\n### [FITUR 2]\n[DESKRIPSI DETAIL FITUR 2 DAN PENGALAMAN MENGGUNAKANNYA]\n\n### [FITUR 3]\n[DESKRIPSI DETAIL FITUR 3 DAN PENGALAMAN MENGGUNAKANNYA]\n\n## Kelebihan\n\n- [KELEBIHAN 1]\n- [KELEBIHAN 2]\n- [KELEBIHAN 3]\n- [KELEBIHAN 4]\n\n## Kekurangan\n\n- [KEKURANGAN 1]\n- [KEKURANGAN 2]\n\n## Perbandingan dengan Produk Serupa\n\n[BANDINGKAN DENGAN 1-2 PRODUK SERUPA DALAM 2-3 PARAGRAF]\n\n## Untuk Siapa Produk Ini Cocok?\n\n[JELASKAN TARGET PENGGUNA IDEAL DALAM 1-2 PARAGRAF]\n\n## Kesimpulan\n\n[KESIMPULAN AKHIR DALAM 2-3 PARAGRAF]\n\n**Rating**: [X/5]\n\n**Rekomendasi**: [YA/TIDAK/BERSYARAT]\n\n[LINK AFFILIATE DENGAN CALL TO ACTION]\n\n---\n\n*Disclaimer: Artikel ini mengandung link affiliate. Jika Anda membeli produk melalui link tersebut, saya akan menerima komisi kecil tanpa biaya tambahan untuk Anda.*",
      },
      {
        id: "8",
        title: "Top 10 Products Blog Post",
        description: "Template artikel blog untuk daftar produk terbaik",
        imageUrl: "/placeholder.svg?height=200&width=400&query=Top%2010%20Products%20Blog",
        content:
          "# 10 [JENIS PRODUK] Terbaik untuk [TUJUAN/AKTIVITAS] di 2023\n\n## Pendahuluan\n\n[PENGANTAR TENTANG PENTINGNYA MEMILIH PRODUK YANG TEPAT, 2-3 PARAGRAF]\n\n## Bagaimana Kami Memilih\n\nDalam memilih [JENIS PRODUK] terbaik, kami mempertimbangkan faktor-faktor berikut:\n\n- [KRITERIA 1]\n- [KRITERIA 2]\n- [KRITERIA 3]\n- [KRITERIA 4]\n- [KRITERIA 5]\n\n## 10 [JENIS PRODUK] Terbaik untuk [TUJUAN/AKTIVITAS]\n\n### 1. [NAMA PRODUK 1] - Pilihan Terbaik Secara Keseluruhan\n\n![NAMA PRODUK 1](URL_GAMBAR)\n\n**Harga**: [HARGA]\n\n**Kelebihan**:\n- [KELEBIHAN 1]\n- [KELEBIHAN 2]\n- [KELEBIHAN 3]\n\n**Kekurangan**:\n- [KEKURANGAN 1]\n- [KEKURANGAN 2]\n\n[DESKRIPSI DETAIL 2-3 PARAGRAF]\n\n[LINK AFFILIATE]\n\n### 2. [NAMA PRODUK 2] - Pilihan Terbaik untuk Anggaran Terbatas\n\n[LANJUTKAN FORMAT YANG SAMA UNTUK PRODUK 2-10]\n\n## Tips Memilih [JENIS PRODUK] yang Tepat\n\n[BERIKAN 3-5 TIPS DENGAN PENJELASAN SINGKAT]\n\n## Pertanyaan yang Sering Diajukan\n\n### [PERTANYAAN 1]?\n[JAWABAN 1]\n\n### [PERTANYAAN 2]?\n[JAWABAN 2]\n\n### [PERTANYAAN 3]?\n[JAWABAN 3]\n\n## Kesimpulan\n\n[KESIMPULAN DALAM 2-3 PARAGRAF]\n\n---\n\n*Disclaimer: Artikel ini mengandung link affiliate. Jika Anda membeli produk melalui link tersebut, saya akan menerima komisi kecil tanpa biaya tambahan untuk Anda.*",
      },
    ],
  }

  const currentTemplates = templates[templateType]

  const copyTemplate = (content: string) => {
    navigator.clipboard.writeText(content)
    // Bisa tambahkan toast notification di sini
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Label>Tipe Template</Label>
        <Tabs value={templateType} onValueChange={setTemplateType as any} className="w-auto">
          <TabsList>
            <TabsTrigger value="review" className="flex items-center">
              <FileText className="mr-2 h-4 w-4" />
              Review
            </TabsTrigger>
            <TabsTrigger value="social" className="flex items-center">
              <Instagram className="mr-2 h-4 w-4" />
              Social Media
            </TabsTrigger>
            <TabsTrigger value="email" className="flex items-center">
              <Mail className="mr-2 h-4 w-4" />
              Email
            </TabsTrigger>
            <TabsTrigger value="blog" className="flex items-center">
              <FileText className="mr-2 h-4 w-4" />
              Blog
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {currentTemplates.map((template) => (
          <Card key={template.id}>
            <CardContent className="p-4">
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">{template.title}</h3>
                  <p className="text-sm text-muted-foreground">{template.description}</p>
                </div>

                <div className="relative overflow-hidden rounded-md border">
                  <Image
                    src={template.imageUrl || "/placeholder.svg"}
                    alt={template.title}
                    width={400}
                    height={200}
                    className="w-full object-cover"
                  />
                </div>

                <div className="max-h-40 overflow-y-auto rounded-md border bg-muted p-3">
                  <pre className="text-xs">{template.content}</pre>
                </div>

                <div className="flex flex-wrap gap-2">
                  <Button size="sm" variant="outline" onClick={() => copyTemplate(template.content)}>
                    <Copy className="mr-2 h-4 w-4" />
                    Copy Template
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="rounded-md border p-4">
        <h3 className="font-medium">Cara Menggunakan Template</h3>
        <ol className="mt-2 space-y-2 text-sm">
          <li>1. Pilih template yang sesuai dengan kebutuhan Anda</li>
          <li>2. Salin template ke editor atau platform yang Anda gunakan</li>
          <li>3. Ganti semua teks dalam [KURUNG] dengan konten Anda sendiri</li>
          <li>4. Pastikan untuk menyertakan link affiliate Anda</li>
          <li>5. Sesuaikan konten agar terdengar natural dan sesuai dengan gaya Anda</li>
        </ol>
      </div>
    </div>
  )
}
