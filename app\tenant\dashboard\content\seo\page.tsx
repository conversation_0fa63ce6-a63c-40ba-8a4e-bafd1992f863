"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Plus,
  BarChart3,
  Eye,
  PencilIcon,
  Trash,
  MoreHorizontal,
  Globe,
  ArrowUpRight,
  Gauge,
  ArrowRight,
  Settings,
  RefreshCw,
  Check,
  X,
  ArrowRightLeft,
  FileText,
  Sparkles,
  Share2,
  ExternalLink
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk pengaturan SEO
const seoSettings = {
  general: {
    siteTitle: "Nama Marketplace Anda",
    siteTitleSeparator: "|",
    siteDescription: "Platform marketplace terbaik untuk semua kebutuhan belanja online Anda",
    homepageTitle: "Marketplace Terbaik untuk Belanja Online",
    homepageDescription: "Temukan berbagai produk berkualitas dengan harga terbaik di marketplace kami.",
    separator: "-",
    enableOpenGraph: true,
    enableTwitterCards: true,
    enableSchemaMarkup: true,
    enableSitemap: true,
    enableRobotsTxt: true,
    enableBreadcrumbs: true,
    verificationCodes: {
      googleVerification: "abc123def456",
      bingVerification: "xyz789",
      facebookAppId: "123456789",
    }
  },
  analytics: {
    googleAnalyticsId: "UA-12345678-1",
    googleTagManagerId: "GTM-ABC123",
    facebookPixelId: "987654321",
    enabledAnalytics: true
  }
};

// Data dummy untuk redirect
const redirects = [
  {
    id: "redirect-001",
    source: "/old-page",
    destination: "/new-page",
    isPermanent: true,
    isActive: true,
    createdAt: "2024-01-05T10:00:00"
  },
  {
    id: "redirect-002",
    source: "/old-products",
    destination: "/products",
    isPermanent: true,
    isActive: true,
    createdAt: "2024-01-08T14:30:00"
  },
  {
    id: "redirect-003",
    source: "/promo-april",
    destination: "/promotions/spring-sale",
    isPermanent: false,
    isActive: true,
    createdAt: "2024-01-15T09:45:00"
  },
  {
    id: "redirect-004",
    source: "/old-blog",
    destination: "/blog",
    isPermanent: true,
    isActive: true,
    createdAt: "2024-01-18T16:20:00"
  },
  {
    id: "redirect-005",
    source: "/special-offer",
    destination: "/promotions/special-deals",
    isPermanent: false,
    isActive: false,
    createdAt: "2024-01-20T11:30:00"
  }
];

// Data dummy untuk performa SEO
const seoPerformance = {
  seoScore: 85,
  issuesCount: 7,
  improvementSuggestions: 12,
  goodResults: 25,
  lastChecked: "2024-01-28T14:30:00",
  topKeywords: [
    { keyword: "marketplace online", position: 3 },
    { keyword: "belanja online terpercaya", position: 5 },
    { keyword: "toko online murah", position: 8 },
    { keyword: "jual beli online", position: 12 },
    { keyword: "e-commerce terbaik", position: 15 }
  ],
  topPages: [
    { url: "/", visits: 12500, position: 1 },
    { url: "/products", visits: 8200, position: 2 },
    { url: "/categories/electronics", visits: 4300, position: 3 },
    { url: "/blog/tips-belanja-online", visits: 3100, position: 4 },
    { url: "/flash-sale", visits: 2800, position: 5 }
  ]
};

// Data dummy untuk sitemap
const sitemapData = {
  totalUrls: 1240,
  lastGenerated: "2024-01-28T00:00:00",
  isAutoGenerated: true,
  generationFrequency: "daily",
  excludedUrls: [
    "/admin/*",
    "/checkout/*",
    "/user/profile",
    "/cart",
    "/search"
  ],
  priorityUrls: [
    { url: "/", priority: 1.0, changeFrequency: "daily" },
    { url: "/products", priority: 0.9, changeFrequency: "daily" },
    { url: "/categories", priority: 0.8, changeFrequency: "weekly" },
    { url: "/blog", priority: 0.7, changeFrequency: "weekly" },
    { url: "/about", priority: 0.5, changeFrequency: "monthly" }
  ]
};

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric'
  })
}

// Fungsi untuk format tanggal dan waktu
function formatDateTime(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function ContentSeoPage() {
  const [activeTab, setActiveTab] = useState<"general" | "redirects" | "sitemap" | "performance">("general")
  const [searchTerm, setSearchTerm] = useState("")
  
  // State untuk mengelola nilai input dalam formulir
  const [formState, setFormState] = useState({
    general: {
      siteTitle: seoSettings.general.siteTitle,
      siteTitleSeparator: seoSettings.general.siteTitleSeparator,
      siteDescription: seoSettings.general.siteDescription,
      homepageTitle: seoSettings.general.homepageTitle,
      homepageDescription: seoSettings.general.homepageDescription,
      enableOpenGraph: seoSettings.general.enableOpenGraph,
      enableTwitterCards: seoSettings.general.enableTwitterCards,
      enableSchemaMarkup: seoSettings.general.enableSchemaMarkup,
      enableBreadcrumbs: seoSettings.general.enableBreadcrumbs
    },
    analytics: {
      googleAnalyticsId: seoSettings.analytics.googleAnalyticsId,
      googleTagManagerId: seoSettings.analytics.googleTagManagerId,
      facebookPixelId: seoSettings.analytics.facebookPixelId,
      enabledAnalytics: seoSettings.analytics.enabledAnalytics
    }
  })
  
  // Handler untuk mengubah nilai form
  const handleInputChange = (section: 'general' | 'analytics', field: string, value: string | boolean) => {
    setFormState(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
  }

  // Filter redirects based on search term
  const filteredRedirects = redirects.filter(redirect => 
    redirect.source.toLowerCase().includes(searchTerm.toLowerCase()) ||
    redirect.destination.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/content">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">SEO</h1>
            <p className="text-muted-foreground">
              Kelola pengaturan SEO website
            </p>
          </div>
        </div>
        <Button variant="outline" asChild>
          <Link href="/tenant/dashboard/content/seo/scan">
            <Gauge className="h-4 w-4 mr-2" />
            Analisa SEO
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Skor SEO</CardTitle>
            <Gauge className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{seoPerformance.seoScore}/100</div>
            <p className="text-xs text-muted-foreground">
              Terakhir diperbarui: {formatDate(seoPerformance.lastChecked)}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Masalah</CardTitle>
            <X className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{seoPerformance.issuesCount}</div>
            <p className="text-xs text-muted-foreground">
              {seoPerformance.improvementSuggestions} saran perbaikan
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Redirects</CardTitle>
            <ArrowRightLeft className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{redirects.length}</div>
            <p className="text-xs text-muted-foreground">
              {redirects.filter(r => r.isActive).length} aktif
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">URLs di Sitemap</CardTitle>
            <FileText className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{sitemapData.totalUrls}</div>
            <p className="text-xs text-muted-foreground">
              Diperbarui: {formatDate(sitemapData.lastGenerated)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex border-b">
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === "general" 
              ? "border-b-2 border-primary text-primary" 
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("general")}
        >
          Pengaturan Umum
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === "redirects" 
              ? "border-b-2 border-primary text-primary" 
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("redirects")}
        >
          Redirects
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === "sitemap" 
              ? "border-b-2 border-primary text-primary" 
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("sitemap")}
        >
          Sitemap
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === "performance" 
              ? "border-b-2 border-primary text-primary" 
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("performance")}
        >
          Performa
        </button>
      </div>

      {/* Search (for redirects tab) */}
      {activeTab === "redirects" && (
        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari redirect..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Redirect
              </Button>
            </div>
          </CardHeader>
        </Card>
      )}

      {/* Tab Content */}
      {activeTab === "general" && (
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pengaturan SEO Dasar</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-1 block">Judul Website</label>
                    <Input 
                      value={formState.general.siteTitle} 
                      onChange={(e) => handleInputChange('general', 'siteTitle', e.target.value)}
                      className="mb-1" 
                    />
                    <p className="text-xs text-muted-foreground">Nama website yang akan ditampilkan di title</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-1 block">Pemisah Judul</label>
                    <Input 
                      value={formState.general.siteTitleSeparator} 
                      onChange={(e) => handleInputChange('general', 'siteTitleSeparator', e.target.value)}
                      className="mb-1" 
                    />
                    <p className="text-xs text-muted-foreground">Pemisah antara judul halaman dan nama website</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium mb-1 block">Deskripsi Website</label>
                  <textarea 
                    className="w-full min-h-24 p-2 border rounded-md bg-background" 
                    value={formState.general.siteDescription}
                    onChange={(e) => handleInputChange('general', 'siteDescription', e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground mt-1">Deskripsi umum website yang akan digunakan sebagai meta description</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-1 block">Judul Halaman Utama</label>
                    <Input 
                      value={formState.general.homepageTitle} 
                      onChange={(e) => handleInputChange('general', 'homepageTitle', e.target.value)}
                      className="mb-1" 
                    />
                    <p className="text-xs text-muted-foreground">Judul khusus untuk halaman utama website</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-1 block">Deskripsi Halaman Utama</label>
                    <Input 
                      value={formState.general.homepageDescription} 
                      onChange={(e) => handleInputChange('general', 'homepageDescription', e.target.value)}
                      className="mb-1" 
                    />
                    <p className="text-xs text-muted-foreground">Deskripsi khusus untuk halaman utama website</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Fitur SEO</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-2 border rounded-md">
                  <div>
                    <h3 className="font-medium">Meta Open Graph</h3>
                    <p className="text-sm text-muted-foreground">Aktifkan tag Open Graph untuk berbagi di sosial media</p>
                  </div>
                  <div className="flex items-center">
                    <Badge variant={formState.general.enableOpenGraph ? "outline" : "secondary"} 
                      className={formState.general.enableOpenGraph ? "bg-green-100 text-green-800" : ""}>
                      {formState.general.enableOpenGraph ? "Aktif" : "Nonaktif"}
                    </Badge>
                    <Button size="sm" variant="ghost" className="ml-2">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-2 border rounded-md">
                  <div>
                    <h3 className="font-medium">Twitter Cards</h3>
                    <p className="text-sm text-muted-foreground">Aktifkan Twitter Cards untuk tampilan yang lebih baik di Twitter</p>
                  </div>
                  <div className="flex items-center">
                    <Badge variant={formState.general.enableTwitterCards ? "outline" : "secondary"} 
                      className={formState.general.enableTwitterCards ? "bg-green-100 text-green-800" : ""}>
                      {formState.general.enableTwitterCards ? "Aktif" : "Nonaktif"}
                    </Badge>
                    <Button size="sm" variant="ghost" className="ml-2">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-2 border rounded-md">
                  <div>
                    <h3 className="font-medium">Schema Markup</h3>
                    <p className="text-sm text-muted-foreground">Aktifkan Schema.org markup untuk rich snippets di Google</p>
                  </div>
                  <div className="flex items-center">
                    <Badge variant={formState.general.enableSchemaMarkup ? "outline" : "secondary"} 
                      className={formState.general.enableSchemaMarkup ? "bg-green-100 text-green-800" : ""}>
                      {formState.general.enableSchemaMarkup ? "Aktif" : "Nonaktif"}
                    </Badge>
                    <Button size="sm" variant="ghost" className="ml-2">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-2 border rounded-md">
                  <div>
                    <h3 className="font-medium">Breadcrumbs</h3>
                    <p className="text-sm text-muted-foreground">Aktifkan breadcrumbs navigasi untuk SEO yang lebih baik</p>
                  </div>
                  <div className="flex items-center">
                    <Badge variant={formState.general.enableBreadcrumbs ? "outline" : "secondary"} 
                      className={formState.general.enableBreadcrumbs ? "bg-green-100 text-green-800" : ""}>
                      {formState.general.enableBreadcrumbs ? "Aktif" : "Nonaktif"}
                    </Badge>
                    <Button size="sm" variant="ghost" className="ml-2">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Integrasi Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-1 block">Google Analytics ID</label>
                    <Input 
                      value={formState.analytics.googleAnalyticsId} 
                      onChange={(e) => handleInputChange('analytics', 'googleAnalyticsId', e.target.value)}
                      className="mb-1" 
                    />
                    <p className="text-xs text-muted-foreground">ID Google Analytics (format: UA-XXXXXXXX-X)</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-1 block">Google Tag Manager ID</label>
                    <Input 
                      value={formState.analytics.googleTagManagerId} 
                      onChange={(e) => handleInputChange('analytics', 'googleTagManagerId', e.target.value)}
                      className="mb-1" 
                    />
                    <p className="text-xs text-muted-foreground">ID Google Tag Manager (format: GTM-XXXXXX)</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium mb-1 block">Facebook Pixel ID</label>
                  <Input 
                    value={formState.analytics.facebookPixelId} 
                    onChange={(e) => handleInputChange('analytics', 'facebookPixelId', e.target.value)}
                    className="mb-1" 
                  />
                  <p className="text-xs text-muted-foreground">ID Facebook Pixel untuk tracking konversi</p>
                </div>
                <div className="flex items-center justify-between p-2 border rounded-md mt-4">
                  <div>
                    <h3 className="font-medium">Status Analytics</h3>
                    <p className="text-sm text-muted-foreground">Aktifkan/nonaktifkan semua tracking analytics</p>
                  </div>
                  <div className="flex items-center">
                    <Badge variant={formState.analytics.enabledAnalytics ? "outline" : "secondary"} 
                      className={formState.analytics.enabledAnalytics ? "bg-green-100 text-green-800" : ""}>
                      {formState.analytics.enabledAnalytics ? "Aktif" : "Nonaktif"}
                    </Badge>
                    <Button size="sm" variant="ghost" className="ml-2">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button>
              <Check className="h-4 w-4 mr-2" />
              Simpan Pengaturan
            </Button>
          </div>
        </div>
      )}

      {activeTab === "redirects" && (
        <div className="space-y-4">
          {filteredRedirects.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <ArrowRightLeft className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada redirect ditemukan</h3>
                <p className="text-muted-foreground mb-4 text-center">
                  Tidak ada redirect yang cocok dengan pencarian Anda
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Redirect
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredRedirects.map(redirect => (
              <Card key={redirect.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className="text-muted-foreground">{redirect.source}</span>
                        <ArrowRight className="h-4 w-4 mx-2 text-muted-foreground" />
                        <span className="font-medium">{redirect.destination}</span>
                      </div>
                      <div className="flex items-center gap-3 mt-1 text-sm text-muted-foreground">
                        <Badge variant="outline" className={redirect.isPermanent ? "bg-blue-100 text-blue-800" : "bg-amber-100 text-amber-800"}>
                          {redirect.isPermanent ? "301 Permanent" : "302 Temporary"}
                        </Badge>
                        <span>Dibuat: {formatDate(redirect.createdAt)}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={redirect.isActive ? "outline" : "secondary"} className={redirect.isActive ? "bg-green-100 text-green-800" : ""}>
                        {redirect.isActive ? "Aktif" : "Nonaktif"}
                      </Badge>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline">
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="text-red-600">
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}

      {activeTab === "sitemap" && (
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pengaturan Sitemap</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-2 border rounded-md">
                  <div>
                    <h3 className="font-medium">Status Sitemap</h3>
                    <p className="text-sm text-muted-foreground">Aktifkan/nonaktifkan pembuatan sitemap XML otomatis</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={sitemapData.isAutoGenerated ? "outline" : "secondary"} 
                      className={sitemapData.isAutoGenerated ? "bg-green-100 text-green-800" : ""}>
                      {sitemapData.isAutoGenerated ? "Otomatis" : "Manual"}
                    </Badge>
                    <Button size="sm" variant="ghost">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex justify-between items-center p-2 border rounded-md">
                  <div>
                    <h3 className="font-medium">Frekuensi Pembaruan</h3>
                    <p className="text-sm text-muted-foreground">Seberapa sering sitemap diperbarui</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{sitemapData.generationFrequency}</span>
                    <Button size="sm" variant="ghost">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex justify-between items-center p-2 border rounded-md">
                  <div>
                    <h3 className="font-medium">URL Sitemap</h3>
                    <p className="text-sm text-muted-foreground">URL publik sitemap Anda</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline" asChild>
                      <a href="/sitemap.xml" target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        /sitemap.xml
                      </a>
                    </Button>
                    <Button size="sm" variant="outline">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Perbarui Sekarang
                    </Button>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium mb-2">URL yang Dikecualikan</h3>
                  <div className="max-h-40 overflow-y-auto p-2 border rounded-md">
                    <ul className="space-y-1">
                      {sitemapData.excludedUrls.map((url, index) => (
                        <li key={index} className="flex items-center justify-between">
                          <span className="text-sm">{url}</span>
                          <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                            <X className="h-4 w-4 text-red-600" />
                          </Button>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="flex mt-2">
                    <Input placeholder="Tambahkan URL untuk dikecualikan" className="flex-1" />
                    <Button className="ml-2">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium mb-2">URL Prioritas</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-3 text-sm font-medium">URL</th>
                          <th className="text-left py-2 px-3 text-sm font-medium">Prioritas</th>
                          <th className="text-left py-2 px-3 text-sm font-medium">Frekuensi Perubahan</th>
                          <th className="text-right py-2 px-3 text-sm font-medium">Aksi</th>
                        </tr>
                      </thead>
                      <tbody>
                        {sitemapData.priorityUrls.map((item, index) => (
                          <tr key={index} className="border-b">
                            <td className="py-2 px-3 text-sm">{item.url}</td>
                            <td className="py-2 px-3 text-sm">{item.priority}</td>
                            <td className="py-2 px-3 text-sm">{item.changeFrequency}</td>
                            <td className="py-2 px-3 text-sm text-right">
                              <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                                <PencilIcon className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="ghost" className="h-8 w-8 p-0 text-red-600">
                                <Trash className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <Button className="mt-3">
                    <Plus className="h-4 w-4 mr-2" />
                    Tambah URL Prioritas
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === "performance" && (
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Performa SEO</CardTitle>
                <Button variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Pindai Ulang
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center mb-4">
                <div className="w-32 h-32 rounded-full border-8 border-green-500 flex items-center justify-center">
                  <span className="text-3xl font-bold text-green-600">{seoPerformance.seoScore}</span>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center mb-4">
                <div className="p-3 bg-red-50 rounded-md">
                  <h3 className="text-sm font-medium text-red-800 mb-1">Masalah</h3>
                  <p className="text-2xl font-bold text-red-600">{seoPerformance.issuesCount}</p>
                </div>
                <div className="p-3 bg-amber-50 rounded-md">
                  <h3 className="text-sm font-medium text-amber-800 mb-1">Perbaikan</h3>
                  <p className="text-2xl font-bold text-amber-600">{seoPerformance.improvementSuggestions}</p>
                </div>
                <div className="p-3 bg-green-50 rounded-md">
                  <h3 className="text-sm font-medium text-green-800 mb-1">Baik</h3>
                  <p className="text-2xl font-bold text-green-600">{seoPerformance.goodResults}</p>
                </div>
              </div>
              <p className="text-sm text-center text-muted-foreground mb-4">
                Terakhir diperbarui: {formatDateTime(seoPerformance.lastChecked)}
              </p>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Keyword Teratas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {seoPerformance.topKeywords.map((keyword, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded-md">
                      <span className="font-medium">{keyword.keyword}</span>
                      <Badge variant={keyword.position <= 10 ? "outline" : "secondary"} 
                        className={keyword.position <= 10 ? "bg-green-100 text-green-800" : ""}>
                        Posisi {keyword.position}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Halaman Teratas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {seoPerformance.topPages.map((page, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded-md">
                      <div>
                        <div className="font-medium">{page.url}</div>
                        <div className="text-sm text-muted-foreground">{page.visits.toLocaleString('id-ID')} kunjungan</div>
                      </div>
                      <Badge variant="outline" className="bg-blue-100 text-blue-800">
                        #{page.position}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Button className="w-full">
            <Sparkles className="h-4 w-4 mr-2" />
            Lihat Laporan Lengkap
          </Button>
        </div>
      )}
    </div>
  )
} 