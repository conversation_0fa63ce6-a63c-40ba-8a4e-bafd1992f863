"use client"

import { useState, useEffect } from "react"
import { Bell, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

export function NotificationOptin() {
  const [isVisible, setIsVisible] = useState(false)
  const [hasOptedIn, setHasOptedIn] = useState(false)
  const [hasDismissed, setHasDismissed] = useState(false)
  const [notificationTypes, setNotificationTypes] = useState({
    orders: true,
    promotions: true,
    events: true,
    system: true,
  })

  useEffect(() => {
    // Check if user has already made a decision
    const notificationDecision = localStorage.getItem("notificationDecision")

    if (notificationDecision === "opted-in") {
      setHasOptedIn(true)
      return
    }

    if (notificationDecision === "dismissed") {
      setHasDismissed(true)
      return
    }

    // Show the opt-in banner after a delay
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, 3000)

    return () => clearTimeout(timer)
  }, [])

  const handleOptIn = async () => {
    try {
      // This would be replaced with actual notification permission logic
      const permission = await requestNotificationPermission()

      if (permission === "granted") {
        setHasOptedIn(true)
        localStorage.setItem("notificationDecision", "opted-in")
        setIsVisible(false)

        // Here you would register the device for push notifications
        // registerDeviceForPushNotifications(notificationTypes)
      }
    } catch (error) {
      console.error("Error requesting notification permission:", error)
    }
  }

  const handleDismiss = () => {
    setIsVisible(false)
    setHasDismissed(true)
    localStorage.setItem("notificationDecision", "dismissed")
  }

  const handleNotificationTypeChange = (type: keyof typeof notificationTypes) => {
    setNotificationTypes((prev) => ({
      ...prev,
      [type]: !prev[type],
    }))
  }

  // Mock function for notification permission
  const requestNotificationPermission = async () => {
    // In a real app, this would use the Notifications API
    // return await Notification.requestPermission()
    return new Promise<NotificationPermission>((resolve) => {
      setTimeout(() => resolve("granted"), 500)
    })
  }

  if (!isVisible || hasOptedIn || hasDismissed) {
    return null
  }

  return (
    <div className="fixed bottom-20 left-4 right-4 z-50 md:left-auto md:right-4 md:bottom-4 md:w-80">
      <Card>
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary mr-3">
                <Bell className="w-5 h-5" />
              </div>
              <h3 className="font-medium">Stay Updated</h3>
            </div>
            <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleDismiss}>
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>

          <p className="text-sm text-muted-foreground mb-4">
            Get notified about orders, promotions, and important updates.
          </p>

          <div className="space-y-3 mb-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="orders" className="text-sm">
                Order updates
              </Label>
              <Switch
                id="orders"
                checked={notificationTypes.orders}
                onCheckedChange={() => handleNotificationTypeChange("orders")}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="promotions" className="text-sm">
                Promotions & deals
              </Label>
              <Switch
                id="promotions"
                checked={notificationTypes.promotions}
                onCheckedChange={() => handleNotificationTypeChange("promotions")}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="events" className="text-sm">
                Events & livestreams
              </Label>
              <Switch
                id="events"
                checked={notificationTypes.events}
                onCheckedChange={() => handleNotificationTypeChange("events")}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="system" className="text-sm">
                System notifications
              </Label>
              <Switch
                id="system"
                checked={notificationTypes.system}
                onCheckedChange={() => handleNotificationTypeChange("system")}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleDismiss}>
              Later
            </Button>
            <Button onClick={handleOptIn}>Enable Notifications</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Ensure we have both default and named exports
export default NotificationOptin
