import { NextRequest, NextResponse } from 'next/server';
import { storeCategoryService } from '@/lib/services/store-categories';

// GET - Mendapatkan semua store categories atau difilter berdasarkan query
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const is_active = searchParams.get('is_active');
    
    const filters = {
      search: search || undefined,
      is_active: is_active !== null ? is_active === 'true' : undefined,
    };
    
    const categories = await storeCategoryService.getCategories(filters);
    
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST - Membuat store category baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newCategory = await storeCategoryService.createCategory(body);
    
    return NextResponse.json(newCategory, { status: 201 });
  } catch (error) {
    console.error('Error creating category:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to create category';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// PUT - Update multiple categories (untuk reordering)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;
    
    if (action === 'reorder') {
      await storeCategoryService.reorderCategories(data);
      return NextResponse.json({ message: 'Categories reordered successfully' });
    }
    
    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error updating categories:', error);
    return NextResponse.json(
      { error: 'Failed to update categories' },
      { status: 500 }
    );
  }
}
