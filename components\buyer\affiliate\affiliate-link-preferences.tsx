import { Card, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export function AffiliateLinkPreferences() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Link Preferences</CardTitle>
        <CardDescription>Customize your affiliate link settings and defaults</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs defaultValue="utm" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="utm">UTM Parameters</TabsTrigger>
            <TabsTrigger value="format">Link Format</TabsTrigger>
            <TabsTrigger value="advanced">Advanced Options</TabsTrigger>
          </TabsList>
          <TabsContent value="utm" className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="utm-source">Default UTM Source</Label>
              <Input id="utm-source" defaultValue="affiliate" />
              <p className="text-xs text-gray-500 mt-1">
                Identifies the source of your traffic (e.g., newsletter, twitter)
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="utm-medium">Default UTM Medium</Label>
              <Input id="utm-medium" defaultValue="referral" />
              <p className="text-xs text-gray-500 mt-1">Identifies the marketing medium (e.g., cpc, banner, email)</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="utm-campaign">Default UTM Campaign</Label>
              <Input id="utm-campaign" defaultValue="spring2023" />
              <p className="text-xs text-gray-500 mt-1">
                Identifies a specific product promotion or strategic campaign
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="utm-term">Default UTM Term</Label>
              <Input id="utm-term" placeholder="Optional" />
              <p className="text-xs text-gray-500 mt-1">Identifies paid search terms</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="utm-content">Default UTM Content</Label>
              <Input id="utm-content" placeholder="Optional" />
              <p className="text-xs text-gray-500 mt-1">
                Identifies what specifically was clicked to bring the user to the site
              </p>
            </div>
          </TabsContent>

          <TabsContent value="format" className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="link-format">Link Format</Label>
              <Select defaultValue="standard">
                <SelectTrigger id="link-format">
                  <SelectValue placeholder="Select link format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standard (with affiliate ID)</SelectItem>
                  <SelectItem value="short">Short URL</SelectItem>
                  <SelectItem value="pretty">Pretty URL</SelectItem>
                  <SelectItem value="custom">Custom Format</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="link-prefix">Custom Link Prefix</Label>
              <Input id="link-prefix" defaultValue="ref" />
              <p className="text-xs text-gray-500 mt-1">
                Custom prefix for your affiliate links (e.g., example.com/ref/product)
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="short-url">Always Generate Short URL</Label>
                <p className="text-xs text-gray-500">
                  Automatically create shortened URLs for all your affiliate links
                </p>
              </div>
              <Switch id="short-url" />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="qr-code">Generate QR Code</Label>
                <p className="text-xs text-gray-500">Automatically generate QR codes for all your affiliate links</p>
              </div>
              <Switch id="qr-code" />
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4 pt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="auto-tagging">Auto-Tagging</Label>
                <p className="text-xs text-gray-500">Automatically add UTM parameters to all your affiliate links</p>
              </div>
              <Switch id="auto-tagging" defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="session-tracking">Enhanced Session Tracking</Label>
                <p className="text-xs text-gray-500">Track user sessions for more accurate attribution</p>
              </div>
              <Switch id="session-tracking" defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="deep-linking">Deep Linking</Label>
                <p className="text-xs text-gray-500">Create links directly to specific product pages</p>
              </div>
              <Switch id="deep-linking" defaultChecked />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cookie-duration">Cookie Duration Display</Label>
              <Select defaultValue="show">
                <SelectTrigger id="cookie-duration">
                  <SelectValue placeholder="Select cookie duration display" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="show">Show in link generator</SelectItem>
                  <SelectItem value="hide">Hide in link generator</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">
                Control whether cookie duration information is displayed in the link generator
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="link-expiration">Link Expiration</Label>
              <Select defaultValue="never">
                <SelectTrigger id="link-expiration">
                  <SelectValue placeholder="Select link expiration" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="never">Never</SelectItem>
                  <SelectItem value="30days">30 days</SelectItem>
                  <SelectItem value="60days">60 days</SelectItem>
                  <SelectItem value="90days">90 days</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">Set an expiration date for your affiliate links</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Reset to Defaults</Button>
        <Button>Save Preferences</Button>
      </CardFooter>
    </Card>
  )
}
