"use client"

import { createContext, useContext, useState, type ReactNode } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface ConfirmationDialogProps {
  title: string
  description: string
  confirmLabel?: string
  cancelLabel?: string
  variant?: "default" | "destructive"
  onConfirm: () => void
  onCancel?: () => void
}

interface ConfirmationContextType {
  confirm: (props: ConfirmationDialogProps) => void
  confirmationDialog: ReactNode
}

const ConfirmationContext = createContext<ConfirmationContextType | undefined>(undefined)

export function ConfirmationProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false)
  const [dialogProps, setDialogProps] = useState<ConfirmationDialogProps>({
    title: "",
    description: "",
    onConfirm: () => {},
  })

  const confirm = (props: ConfirmationDialogProps) => {
    setDialogProps(props)
    setIsOpen(true)
  }

  const handleConfirm = () => {
    setIsOpen(false)
    dialogProps.onConfirm()
  }

  const handleCancel = () => {
    setIsOpen(false)
    if (dialogProps.onCancel) {
      dialogProps.onCancel()
    }
  }

  const confirmationDialog = (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{dialogProps.title}</AlertDialogTitle>
          <AlertDialogDescription>{dialogProps.description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel}>{dialogProps.cancelLabel || "Cancel"}</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className={dialogProps.variant === "destructive" ? "bg-red-600 hover:bg-red-700" : ""}
          >
            {dialogProps.confirmLabel || "Confirm"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )

  return (
    <ConfirmationContext.Provider value={{ confirm, confirmationDialog }}>
      {children}
      {confirmationDialog}
    </ConfirmationContext.Provider>
  )
}

export function useConfirmation() {
  const context = useContext(ConfirmationContext)
  if (context === undefined) {
    throw new Error("useConfirmation must be used within a ConfirmationProvider")
  }
  return context
}
