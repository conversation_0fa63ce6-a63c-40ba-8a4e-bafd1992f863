"use client"

import type React from "react"
import { useState, useRef } from "react"
import { Search, ShoppingCart, MessageSquare } from "lucide-react"

export function VelozioSearch() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [searchText, setSearchText] = useState("")
  const searchInputRef = useRef<HTMLInputElement>(null)

  const handleSearchClick = () => {
    setIsExpanded(true)
  }

  const handleBackClick = () => {
    setIsExpanded(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value)
  }

  const handleClearClick = () => {
    setSearchText("")
    if (searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }

  return (
    <div className="w-full">
      <header className="bg-red-500 p-4">
        <div className="container mx-auto flex items-center justify-between">
          <div className="text-white font-bold text-xl">Sellzio</div>
          <div className="flex items-center space-x-4">
            <Search className="h-6 w-6 text-white cursor-pointer" />
            <ShoppingCart className="h-6 w-6 text-white cursor-pointer" />
            <MessageSquare className="h-6 w-6 text-white cursor-pointer" />
          </div>
        </div>
      </header>
      <div className="container mx-auto mt-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search products..."
            className="w-full p-2 border rounded-md"
            value={searchText}
            onChange={handleInputChange}
            ref={searchInputRef}
          />
          {searchText && (
            <button
              className="absolute top-1/2 right-2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              onClick={handleClearClick}
            >
              Clear
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
