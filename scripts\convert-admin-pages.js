const fs = require("fs")
const path = require("path")
const glob = require("glob")

// Fungsi untuk membaca file
function readFile(filePath) {
  return fs.readFileSync(filePath, "utf8")
}

// Fungsi untuk menulis file
function writeFile(filePath, content) {
  fs.writeFileSync(filePath, content, "utf8")
}

// Fungsi untuk memeriksa apakah file menggunakan useNotifications
function usesNotifications(content) {
  return content.includes("useNotifications")
}

// Fungsi untuk memeriksa apakah file memiliki direktif 'use client'
function hasUseClientDirective(content) {
  return content.includes('"use client"') || content.includes("'use client'")
}

// Fungsi untuk membuat nama file client
function getClientFileName(filePath) {
  const dir = path.dirname(filePath)
  const baseName = path.basename(filePath, path.extname(filePath))
  const ext = path.extname(filePath)

  // Jika file adalah page.tsx, buat nama file client yang sesuai
  if (baseName === "page") {
    const folderName = path.basename(dir)
    return path.join(dir, `${folderName}-client${ext}`)
  }

  return path.join(dir, `${baseName}-client${ext}`)
}

// Fungsi untuk membuat konten file client
function createClientContent(content) {
  // Tambahkan direktif 'use client' jika belum ada
  if (!hasUseClientDirective(content)) {
    return `"use client"\n\n${content}`
  }
  return content
}

// Fungsi untuk membuat konten file server
function createServerContent(originalPath, clientPath) {
  const relativePath = path.relative(path.dirname(originalPath), clientPath).replace(/\\/g, "/")
  const importPath = relativePath.startsWith(".") ? relativePath : `./${relativePath}`
  const componentName = path
    .basename(clientPath, path.extname(clientPath))
    .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
    .replace(/^[a-z]/, (letter) => letter.toUpperCase())

  return `import { ${componentName} } from "${importPath.replace(/\.tsx$/, "")}"

export default function Page() {
  return <${componentName} />
}
`
}

// Fungsi utama
function main() {
  // Cari semua file page.tsx di direktori admin
  const files = glob.sync("app/admin/**/*page.tsx")

  let convertedCount = 0

  files.forEach((file) => {
    const content = readFile(file)

    // Jika file menggunakan useNotifications dan tidak memiliki direktif 'use client'
    if (usesNotifications(content) && !hasUseClientDirective(content)) {
      console.log(`Converting: ${file}`)

      // Buat file client
      const clientFile = getClientFileName(file)
      const clientContent = createClientContent(content)
      writeFile(clientFile, clientContent)

      // Update file server
      const serverContent = createServerContent(file, clientFile)
      writeFile(file, serverContent)

      convertedCount++
    }
  })

  console.log(`Converted ${convertedCount} files.`)
}

main()
