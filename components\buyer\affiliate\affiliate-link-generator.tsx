"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Copy, QrCode, Download, RefreshCw } from "lucide-react"
import { Separator } from "@/components/ui/separator"

export function AffiliateLinkGenerator() {
  const [linkType, setLinkType] = useState<"product" | "store" | "custom">("product")
  const [productId, setProductId] = useState("")
  const [storeId, setStoreId] = useState("")
  const [customUrl, setCustomUrl] = useState("")
  const [category, setCategory] = useState("")
  const [useShortLink, setUseShortLink] = useState(true)
  const [useUtm, setUseUtm] = useState(false)
  const [utmSource, setUtmSource] = useState("affiliate")
  const [utmMedium, setUtmMedium] = useState("referral")
  const [utmCampaign, setUtmCampaign] = useState("")
  const [generatedLink, setGeneratedLink] = useState("")
  const [shortLink, setShortLink] = useState("")
  const [copied, setCopied] = useState(false)

  const generateLink = () => {
    const baseUrl = "https://sellzio.com"
    const refCode = "USER123"

    let url = ""
    if (linkType === "product" && productId) {
      url = `${baseUrl}/product/${productId}`
    } else if (linkType === "store" && storeId) {
      url = `${baseUrl}/store/${storeId}`
    } else if (linkType === "custom" && customUrl) {
      try {
        const urlObj = new URL(customUrl)
        url = urlObj.toString()
      } catch (e) {
        url = `${baseUrl}/${customUrl}`
      }
    }

    // Tambahkan parameter referral
    url += (url.includes("?") ? "&" : "?") + `ref=${refCode}`

    // Tambahkan UTM parameters jika diaktifkan
    if (useUtm) {
      url += `&utm_source=${utmSource}`
      url += `&utm_medium=${utmMedium}`
      if (utmCampaign) {
        url += `&utm_campaign=${utmCampaign}`
      }
    }

    setGeneratedLink(url)

    // Generate short link
    if (useShortLink) {
      // Simulasi short link
      setShortLink(`https://szio.co/${Math.random().toString(36).substring(2, 8)}`)
    } else {
      setShortLink("")
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="product" onValueChange={(value) => setLinkType(value as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="product">Produk</TabsTrigger>
          <TabsTrigger value="store">Toko</TabsTrigger>
          <TabsTrigger value="custom">Custom URL</TabsTrigger>
        </TabsList>

        <TabsContent value="product" className="space-y-4 pt-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="product-id">ID atau Slug Produk</Label>
              <Input
                id="product-id"
                placeholder="Contoh: sepatu-running-123"
                value={productId}
                onChange={(e) => setProductId(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="product-category">Kategori (Opsional)</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger id="product-category">
                  <SelectValue placeholder="Pilih kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fashion">Fashion</SelectItem>
                  <SelectItem value="electronics">Elektronik</SelectItem>
                  <SelectItem value="home">Rumah & Dapur</SelectItem>
                  <SelectItem value="beauty">Kecantikan</SelectItem>
                  <SelectItem value="sports">Olahraga</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={() => setProductId("")} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset
            </Button>
            <Button onClick={() => {}} variant="outline" size="sm">
              Cari Produk
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="store" className="space-y-4 pt-4">
          <div className="space-y-2">
            <Label htmlFor="store-id">ID atau Slug Toko</Label>
            <Input
              id="store-id"
              placeholder="Contoh: toko-fashion"
              value={storeId}
              onChange={(e) => setStoreId(e.target.value)}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={() => setStoreId("")} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset
            </Button>
            <Button onClick={() => {}} variant="outline" size="sm">
              Cari Toko
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="custom" className="space-y-4 pt-4">
          <div className="space-y-2">
            <Label htmlFor="custom-url">URL Custom</Label>
            <Input
              id="custom-url"
              placeholder="Contoh: https://example.com/page"
              value={customUrl}
              onChange={(e) => setCustomUrl(e.target.value)}
            />
          </div>
        </TabsContent>
      </Tabs>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Opsi Link</h3>

        <div className="flex items-center space-x-2">
          <Switch id="short-link" checked={useShortLink} onCheckedChange={setUseShortLink} />
          <Label htmlFor="short-link">Gunakan Short Link</Label>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Switch id="utm-params" checked={useUtm} onCheckedChange={setUseUtm} />
            <Label htmlFor="utm-params">Tambahkan UTM Parameters</Label>
          </div>

          {useUtm && (
            <div className="grid gap-4 pl-6 pt-2 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="utm-source">UTM Source</Label>
                <Input
                  id="utm-source"
                  placeholder="affiliate"
                  value={utmSource}
                  onChange={(e) => setUtmSource(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="utm-medium">UTM Medium</Label>
                <Input
                  id="utm-medium"
                  placeholder="referral"
                  value={utmMedium}
                  onChange={(e) => setUtmMedium(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="utm-campaign">UTM Campaign (Opsional)</Label>
                <Input
                  id="utm-campaign"
                  placeholder="summer_sale"
                  value={utmCampaign}
                  onChange={(e) => setUtmCampaign(e.target.value)}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end">
        <Button onClick={generateLink}>Generate Link</Button>
      </div>

      {generatedLink && (
        <div className="space-y-4 rounded-md border p-4">
          <div className="space-y-2">
            <Label>Link Affiliate:</Label>
            <div className="flex items-center space-x-2">
              <Input value={generatedLink} readOnly className="flex-1 font-mono text-xs" />
              <Button size="icon" variant="outline" onClick={() => copyToClipboard(generatedLink)}>
                {copied ? <span className="text-xs">✓</span> : <Copy className="h-4 w-4" />}
              </Button>
              <Button size="icon" variant="outline">
                <QrCode className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {shortLink && (
            <div className="space-y-2">
              <Label>Short Link:</Label>
              <div className="flex items-center space-x-2">
                <Input value={shortLink} readOnly className="flex-1 font-mono text-xs" />
                <Button size="icon" variant="outline" onClick={() => copyToClipboard(shortLink)}>
                  {copied ? <span className="text-xs">✓</span> : <Copy className="h-4 w-4" />}
                </Button>
                <Button size="icon" variant="outline">
                  <QrCode className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <QrCode className="mr-2 h-4 w-4" />
              Download QR Code
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Simpan Link
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
