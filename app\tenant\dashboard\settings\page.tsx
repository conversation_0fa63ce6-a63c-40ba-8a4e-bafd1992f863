"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  User,
  Palette,
  Globe,
  Users,
  Plug,
  Bell,
  ShieldCheck,
  ArrowRight,
  Settings
} from "lucide-react"

const settingsSections = [
  {
    id: "profile",
    title: "Profil",
    description: "Kelola informasi profil dan bisnis Anda",
    icon: User,
    href: "/tenant/dashboard/settings/profile"
  },
  {
    id: "branding",
    title: "Branding",
    description: "Sesuaikan tampilan dan branding toko Anda",
    icon: Palette,
    href: "/tenant/dashboard/settings/branding"
  },
  {
    id: "domain",
    title: "Domain",
    description: "Kelola domain dan URL toko Anda",
    icon: Globe,
    href: "/tenant/dashboard/settings/domain"
  },
  {
    id: "users",
    title: "Pengguna",
    description: "Kelola akses pengguna dan tim",
    icon: Users,
    href: "/tenant/dashboard/settings/users"
  },
  {
    id: "integrations",
    title: "Integrasi",
    description: "Hubungkan dengan layanan dan aplikasi pihak ketiga",
    icon: Plug,
    href: "/tenant/dashboard/settings/integrations"
  },
  {
    id: "notifications",
    title: "Notifikasi",
    description: "Atur preferensi notifikasi email dan sistem",
    icon: Bell,
    href: "/tenant/dashboard/settings/notifications"
  },
  {
    id: "security",
    title: "Keamanan",
    description: "Kelola keamanan akun dan privasi",
    icon: ShieldCheck,
    href: "/tenant/dashboard/settings/security"
  }
]

export default function SettingsDashboardPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pengaturan</h1>
          <p className="text-muted-foreground">
            Kelola akun, toko, dan preferensi sistem Anda
          </p>
        </div>
      </div>

      {/* Settings Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {settingsSections.map((section) => (
          <Card key={section.id} className="flex flex-col">
            <CardHeader className="pb-2">
              <div className="flex items-center space-x-2">
                <div className="p-2 rounded-md bg-primary/10">
                  <section.icon className="h-5 w-5 text-primary" />
                </div>
                <CardTitle className="text-lg">{section.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="pb-2 flex-grow">
              <p className="text-sm text-muted-foreground">
                {section.description}
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href={section.href}>
                  Buka {section.title}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
      
      {/* Recently Updated Settings */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold tracking-tight">Pengaturan yang Baru Diperbarui</h2>
          <Button variant="ghost" size="sm">
            Lihat Semua
          </Button>
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between p-4 rounded-lg border">
            <div className="flex items-center space-x-4">
              <div className="p-2 rounded-md bg-primary/10">
                <User className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="font-medium">Informasi Profil</div>
                <div className="text-sm text-muted-foreground">Diperbarui 2 hari yang lalu</div>
              </div>
            </div>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/tenant/dashboard/settings/profile">
                <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
          <div className="flex items-center justify-between p-4 rounded-lg border">
            <div className="flex items-center space-x-4">
              <div className="p-2 rounded-md bg-primary/10">
                <Palette className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="font-medium">Pengaturan Branding</div>
                <div className="text-sm text-muted-foreground">Diperbarui 5 hari yang lalu</div>
              </div>
            </div>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/tenant/dashboard/settings/branding">
                <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
          <div className="flex items-center justify-between p-4 rounded-lg border">
            <div className="flex items-center space-x-4">
              <div className="p-2 rounded-md bg-primary/10">
                <ShieldCheck className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="font-medium">Pengaturan Keamanan</div>
                <div className="text-sm text-muted-foreground">Diperbarui 1 minggu yang lalu</div>
              </div>
            </div>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/tenant/dashboard/settings/security">
                <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
      
      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Tindakan Cepat</CardTitle>
          <CardDescription>Tindakan pengaturan yang sering digunakan</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Button variant="outline" className="h-auto flex flex-col items-center justify-center p-4" asChild>
              <Link href="/tenant/dashboard/settings/profile/business">
                <User className="h-5 w-5 mb-2" />
                <span>Perbarui Profil Bisnis</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto flex flex-col items-center justify-center p-4" asChild>
              <Link href="/tenant/dashboard/settings/users/invite">
                <Users className="h-5 w-5 mb-2" />
                <span>Undang Anggota Tim</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto flex flex-col items-center justify-center p-4" asChild>
              <Link href="/tenant/dashboard/settings/domain/connect">
                <Globe className="h-5 w-5 mb-2" />
                <span>Hubungkan Domain</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto flex flex-col items-center justify-center p-4" asChild>
              <Link href="/tenant/dashboard/settings/security/password">
                <ShieldCheck className="h-5 w-5 mb-2" />
                <span>Ubah Kata Sandi</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 