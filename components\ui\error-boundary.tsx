"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { <PERSON>ertCircle, RefreshCw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function ErrorBoundary({ children, fallback }: ErrorBoundaryProps) {
  const [hasError, setHasError] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const errorHandler = (event: ErrorEvent) => {
      console.error("Error caught by error boundary:", event.error)
      setHasError(true)
      setError(event.error || new Error(event.message))
      event.preventDefault()
    }

    window.addEventListener("error", errorHandler)
    return () => window.removeEventListener("error", errorHandler)
  }, [])

  if (hasError) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <Card className="mx-auto max-w-md border-destructive">
        <CardHeader className="bg-destructive/10">
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            Terjadi Kesalahan
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <p className="mb-4 text-muted-foreground">
            Aplikasi mengalami masalah. Ini mungkin disebabkan oleh redirect loop atau masalah dengan cookie.
          </p>
          <div className="rounded-md bg-destructive/10 p-4 text-sm text-destructive">
            <p>Error: {error?.message || "Unknown error occurred"}</p>
          </div>
          <ul className="mt-6 space-y-2 pl-6">
            <li>Hapus cookie browser Anda</li>
            <li>Gunakan mode penyamaran/incognito</li>
            <li>Muat ulang halaman</li>
            <li>Coba akses halaman utama terlebih dahulu</li>
          </ul>
        </CardContent>
        <CardFooter className="flex flex-col gap-2">
          <Button
            className="w-full"
            variant="default"
            onClick={() => {
              document.cookie.split(";").forEach((c) => {
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`)
              })
              window.location.reload()
            }}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Hapus Cookie & Coba Lagi
          </Button>
          <Button
            className="w-full"
            variant="outline"
            onClick={() => {
              window.location.href = "/"
            }}
          >
            Kembali ke Halaman Utama
          </Button>
        </CardFooter>
      </Card>
    )
  }

  return <>{children}</>
}
