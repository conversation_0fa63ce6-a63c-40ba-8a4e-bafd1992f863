"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { AffiliateProgressTracker } from "./affiliate-progress-tracker"
import { PersonalInformation } from "./personal-information"
import { PromotionChannels } from "./promotion-channels"
import { PaymentInformation } from "./payment-information"
import { TermsConditions } from "./terms-conditions"
import { ApplicationStatus } from "./application-status"
import { ArrowLeft, ArrowRight, Save } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export function AffiliateApplicationWizard() {
  const router = useRouter()
  const { toast } = useToast()
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [applicationData, setApplicationData] = useState({
    personalInfo: {
      background: "",
      expertise: [],
      experience: "",
      motivation: "",
    },
    promotionChannels: {
      channels: [],
      urls: {},
      audienceSize: {},
      categories: [],
      sampleLinks: [],
    },
    paymentInfo: {
      method: "",
      bankAccount: {
        name: "",
        number: "",
        bank: "",
      },
      digitalWallet: {
        type: "",
        email: "",
      },
      taxInfo: {
        taxId: "",
        taxName: "",
      },
      threshold: "",
      frequency: "",
    },
    termsAccepted: false,
  })

  // Generate a random application ID
  const applicationId = "AFF" + Math.floor(100000 + Math.random() * 900000)

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
      window.scrollTo({ top: 0, behavior: "smooth" })
    } else {
      handleSubmit()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
      window.scrollTo({ top: 0, behavior: "smooth" })
    }
  }

  const handleSaveProgress = () => {
    // In a real app, this would save to the database
    toast({
      title: "Progress Saved",
      description: "Your application progress has been saved. You can continue later.",
    })
  }

  const handleSubmit = () => {
    // Validate terms acceptance
    if (!applicationData.termsAccepted) {
      toast({
        title: "Terms & Conditions Required",
        description: "You must accept the terms and conditions to submit your application.",
        variant: "destructive",
      })
      return
    }

    // In a real app, this would submit to the API
    setIsSubmitted(true)
    toast({
      title: "Application Submitted",
      description: "Your affiliate application has been submitted successfully.",
    })
  }

  const updateApplicationData = (step, data) => {
    setApplicationData((prev) => {
      const newData = { ...prev }

      switch (step) {
        case 1:
          newData.personalInfo = { ...newData.personalInfo, ...data }
          break
        case 2:
          newData.promotionChannels = { ...newData.promotionChannels, ...data }
          break
        case 3:
          newData.paymentInfo = { ...newData.paymentInfo, ...data }
          break
        case 4:
          newData.termsAccepted = data.termsAccepted
          break
      }

      return newData
    })
  }

  // If application is submitted, show the status page
  if (isSubmitted) {
    return <ApplicationStatus applicationId={applicationId} />
  }

  return (
    <div id="affiliate-application-wizard" className="space-y-6">
      <AffiliateProgressTracker currentStep={currentStep} totalSteps={4} applicationId={applicationId} />

      <Card className="border-border/60">
        <CardHeader>
          <CardTitle>
            {currentStep === 1 && "Personal Information"}
            {currentStep === 2 && "Promotion Channels"}
            {currentStep === 3 && "Payment Information"}
            {currentStep === 4 && "Terms & Conditions"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {currentStep === 1 && (
            <PersonalInformation
              data={applicationData.personalInfo}
              onUpdate={(data) => updateApplicationData(1, data)}
            />
          )}
          {currentStep === 2 && (
            <PromotionChannels
              data={applicationData.promotionChannels}
              onUpdate={(data) => updateApplicationData(2, data)}
            />
          )}
          {currentStep === 3 && (
            <PaymentInformation
              data={applicationData.paymentInfo}
              onUpdate={(data) => updateApplicationData(3, data)}
            />
          )}
          {currentStep === 4 && (
            <TermsConditions
              data={{ termsAccepted: applicationData.termsAccepted }}
              onUpdate={(data) => updateApplicationData(4, data)}
            />
          )}
        </CardContent>
        <CardFooter className="flex justify-between border-t border-border/40 bg-muted/30 px-6 py-4">
          <div>
            <Button variant="outline" onClick={handleSaveProgress} className="mr-2">
              <Save className="mr-2 h-4 w-4" />
              Save Progress
            </Button>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handlePrevious} disabled={currentStep === 1}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            <Button onClick={handleNext}>
              {currentStep < 4 ? (
                <>
                  Next
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              ) : (
                "Submit Application"
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
