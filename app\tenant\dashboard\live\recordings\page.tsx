"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Calendar,
  Search,
  Filter,
  Play,
  Trash,
  Edit,
  MoreHorizontal,
  Download,
  Eye,
  Clock,
  Film,
  ThumbsUp,
  MessagesSquare,
  Share2,
  ClipboardEdit,
  CheckSquare,
  CalendarDays,
  SlidersHorizontal,
  AlertTriangle
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"

// Data dummy untuk rekaman
const recordings = [
  {
    id: "rec-001",
    title: "Webinar Strategi SEO 2024",
    recordedOn: "2024-05-15T15:00:00",
    duration: "01:12:35",
    views: 342,
    likes: 78,
    comments: 25,
    thumbnailUrl: "/images/thumbnails/seo-webinar.jpg",
    status: "published",
    visibility: "public",
    fileSize: "428 MB",
    quality: "HD",
    streamId: "stream-001"
  },
  {
    id: "rec-002",
    title: "Tutorial Penggunaan Platform Baru",
    recordedOn: "2024-05-10T11:30:00",
    duration: "00:45:22",
    views: 215,
    likes: 45,
    comments: 12,
    thumbnailUrl: "/images/thumbnails/platform-tutorial.jpg",
    status: "published",
    visibility: "public",
    fileSize: "256 MB",
    quality: "HD",
    streamId: "stream-002"
  },
  {
    id: "rec-003",
    title: "Diskusi Panel: Tren E-commerce 2024",
    recordedOn: "2024-05-05T14:00:00",
    duration: "01:35:48",
    views: 521,
    likes: 103,
    comments: 37,
    thumbnailUrl: "/images/thumbnails/ecommerce-panel.jpg",
    status: "published",
    visibility: "public",
    fileSize: "580 MB",
    quality: "HD",
    streamId: "stream-003"
  },
  {
    id: "rec-004",
    title: "Tips Meningkatkan Penjualan di Marketplace",
    recordedOn: "2024-04-28T13:15:00",
    duration: "00:52:18",
    views: 187,
    likes: 42,
    comments: 15,
    thumbnailUrl: "/images/thumbnails/sales-tips.jpg",
    status: "processing",
    visibility: "unlisted",
    fileSize: "310 MB",
    quality: "HD",
    streamId: "stream-004"
  },
  {
    id: "rec-005",
    title: "Persiapan Kampanye Flash Sale",
    recordedOn: "2024-04-20T10:00:00",
    duration: "01:05:32",
    views: 263,
    likes: 58,
    comments: 22,
    thumbnailUrl: "/images/thumbnails/flash-sale.jpg",
    status: "published",
    visibility: "public",
    fileSize: "390 MB",
    quality: "HD",
    streamId: "stream-005"
  },
  {
    id: "rec-006",
    title: "Wawancara dengan Pakar Pemasaran Digital",
    recordedOn: "2024-04-15T16:30:00",
    duration: "00:48:15",
    views: 145,
    likes: 35,
    comments: 18,
    thumbnailUrl: "/images/thumbnails/interview.jpg",
    status: "draft",
    visibility: "private",
    fileSize: "285 MB",
    quality: "HD",
    streamId: "stream-006"
  }
]

// Statistik rekaman
const recordingsStats = {
  totalRecordings: recordings.length,
  totalViews: recordings.reduce((acc, curr) => acc + curr.views, 0),
  totalWatchTime: 1250, // dalam menit
  publishedCount: recordings.filter(r => r.status === "published").length,
  processingCount: recordings.filter(r => r.status === "processing").length,
  draftCount: recordings.filter(r => r.status === "draft").length,
  totalStorage: "2.24 GB"
}

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Format waktu
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format tanggal dan waktu
function formatDateTime(dateString: string) {
  return `${formatDate(dateString)}, ${formatTime(dateString)}`
}

// Mendapatkan status badge
function getStatusBadge(status: string) {
  switch (status) {
    case "published":
      return <Badge className="bg-green-100 text-green-800">Dipublikasikan</Badge>
    case "processing":
      return <Badge className="bg-amber-100 text-amber-800">Diproses</Badge>
    case "draft":
      return <Badge variant="outline" className="text-muted-foreground">Draft</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

// Mendapatkan visibility badge
function getVisibilityBadge(visibility: string) {
  switch (visibility) {
    case "public":
      return <Badge className="bg-blue-100 text-blue-800">Publik</Badge>
    case "unlisted":
      return <Badge className="bg-purple-100 text-purple-800">Tidak Terdaftar</Badge>
    case "private":
      return <Badge className="bg-slate-100 text-slate-800">Privat</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

export default function LiveRecordingsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [visibilityFilter, setVisibilityFilter] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "table">("grid")
  const [selectedRecordings, setSelectedRecordings] = useState<string[]>([])
  
  // Fungsi untuk menangani select semua
  const handleSelectAll = () => {
    if (selectedRecordings.length === filteredRecordings.length) {
      setSelectedRecordings([])
    } else {
      setSelectedRecordings(filteredRecordings.map(rec => rec.id))
    }
  }
  
  // Fungsi untuk menangani select individu
  const handleSelectItem = (id: string) => {
    if (selectedRecordings.includes(id)) {
      setSelectedRecordings(selectedRecordings.filter(recId => recId !== id))
    } else {
      setSelectedRecordings([...selectedRecordings, id])
    }
  }

  // Filter recordings
  const filteredRecordings = recordings.filter(recording => {
    // Search filter
    const matchesSearch = recording.title.toLowerCase().includes(searchTerm.toLowerCase())
    
    // Status filter
    const matchesStatus = statusFilter === "all" || recording.status === statusFilter
    
    // Visibility filter
    const matchesVisibility = visibilityFilter === "all" || recording.visibility === visibilityFilter
    
    return matchesSearch && matchesStatus && matchesVisibility
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/live">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Rekaman</h1>
            <p className="text-muted-foreground">
              Kelola rekaman dari siaran langsung Anda
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {selectedRecordings.length > 0 && (
            <>
              <Button variant="outline" size="sm" className="text-red-600">
                <Trash className="h-4 w-4 mr-2" />
                Hapus ({selectedRecordings.length})
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Unduh ({selectedRecordings.length})
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Rekaman</CardTitle>
            <Film className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{recordingsStats.totalRecordings}</div>
            <p className="text-xs text-muted-foreground">
              {recordingsStats.publishedCount} dipublikasikan
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Penayangan</CardTitle>
            <Eye className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{recordingsStats.totalViews.toLocaleString('id-ID')}</div>
            <p className="text-xs text-muted-foreground">
              Seluruh rekaman
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Waktu Tonton</CardTitle>
            <Clock className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{recordingsStats.totalWatchTime} menit</div>
            <p className="text-xs text-muted-foreground">
              Dari seluruh penayangan
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Penyimpanan</CardTitle>
            <SlidersHorizontal className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{recordingsStats.totalStorage}</div>
            <p className="text-xs text-muted-foreground">
              {recordingsStats.processingCount} rekaman sedang diproses
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Cari rekaman..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex flex-wrap gap-2">
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
          >
            <SelectTrigger className="w-[160px]">
              <div className="flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                <span>Status</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Status</SelectItem>
              <SelectItem value="published">Dipublikasikan</SelectItem>
              <SelectItem value="processing">Diproses</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={visibilityFilter}
            onValueChange={setVisibilityFilter}
          >
            <SelectTrigger className="w-[160px]">
              <div className="flex items-center">
                <Eye className="h-4 w-4 mr-2" />
                <span>Visibilitas</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua</SelectItem>
              <SelectItem value="public">Publik</SelectItem>
              <SelectItem value="unlisted">Tidak Terdaftar</SelectItem>
              <SelectItem value="private">Privat</SelectItem>
            </SelectContent>
          </Select>
          <div className="flex border rounded-md overflow-hidden">
            <Button 
              variant={viewMode === "grid" ? "default" : "ghost"} 
              size="sm" 
              className="rounded-none"
              onClick={() => setViewMode("grid")}
            >
              Grid
            </Button>
            <Button 
              variant={viewMode === "table" ? "default" : "ghost"} 
              size="sm" 
              className="rounded-none"
              onClick={() => setViewMode("table")}
            >
              Tabel
            </Button>
          </div>
        </div>
      </div>

      {/* List of recordings */}
      {viewMode === "grid" ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredRecordings.length === 0 ? (
            <Card className="col-span-full">
              <CardContent className="flex flex-col items-center justify-center pt-6 pb-8">
                <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada rekaman ditemukan</h3>
                <p className="text-muted-foreground mb-4 text-center">
                  Coba ubah filter atau buat siaran langsung baru
                </p>
                <Button asChild>
                  <Link href="/tenant/dashboard/live/studio">
                    Mulai Siaran
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredRecordings.map(recording => (
              <Card key={recording.id} className="overflow-hidden hover:shadow-md transition-shadow group">
                <div className="relative">
                  <div className="absolute top-2 left-2 z-10">
                    <Checkbox
                      checked={selectedRecordings.includes(recording.id)}
                      onCheckedChange={() => handleSelectItem(recording.id)}
                    />
                  </div>
                  <div className="relative aspect-video bg-muted">
                    <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Play className="h-12 w-12 text-white opacity-80" />
                    </div>
                    <div className="absolute top-2 right-2">
                      {getStatusBadge(recording.status)}
                    </div>
                    <div className="absolute bottom-2 right-2">
                      <Badge className="bg-purple-600">
                        {recording.duration}
                      </Badge>
                    </div>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold line-clamp-2 mb-2">{recording.title}</h3>
                  <div className="flex items-center text-sm text-muted-foreground mb-2">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>{formatDate(recording.recordedOn)}</span>
                    <div className="mx-2">•</div>
                    {getVisibilityBadge(recording.visibility)}
                  </div>
                  
                  <div className="flex justify-between text-sm text-muted-foreground mt-3">
                    <div className="flex items-center">
                      <Eye className="h-4 w-4 mr-1" />
                      <span>{recording.views}</span>
                    </div>
                    <div className="flex items-center">
                      <ThumbsUp className="h-4 w-4 mr-1" />
                      <span>{recording.likes}</span>
                    </div>
                    <div className="flex items-center">
                      <MessagesSquare className="h-4 w-4 mr-1" />
                      <span>{recording.comments}</span>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/tenant/dashboard/live/recordings/${recording.id}`}>
                            Detail
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/tenant/dashboard/live/recordings/${recording.id}/edit`}>
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Download className="h-4 w-4 mr-2" />
                          Unduh
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Share2 className="h-4 w-4 mr-2" />
                          Bagikan
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <Trash className="h-4 w-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      ) : (
        <Card>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[40px]">
                    <Checkbox 
                      checked={
                        filteredRecordings.length > 0 && 
                        selectedRecordings.length === filteredRecordings.length
                      }
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Judul</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Visibilitas</TableHead>
                  <TableHead>Tanggal Rekaman</TableHead>
                  <TableHead>Durasi</TableHead>
                  <TableHead>Penayangan</TableHead>
                  <TableHead className="text-right">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRecordings.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center">
                        <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">Tidak ada rekaman ditemukan</h3>
                        <p className="text-muted-foreground mb-4">
                          Coba ubah filter atau buat siaran langsung baru
                        </p>
                        <Button asChild>
                          <Link href="/tenant/dashboard/live/studio">
                            Mulai Siaran
                          </Link>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRecordings.map(recording => (
                    <TableRow key={recording.id}>
                      <TableCell>
                        <Checkbox 
                          checked={selectedRecordings.includes(recording.id)}
                          onCheckedChange={() => handleSelectItem(recording.id)}
                        />
                      </TableCell>
                      <TableCell className="font-medium max-w-[200px] truncate">
                        <Link href={`/tenant/dashboard/live/recordings/${recording.id}`} className="hover:underline">
                          {recording.title}
                        </Link>
                      </TableCell>
                      <TableCell>{getStatusBadge(recording.status)}</TableCell>
                      <TableCell>{getVisibilityBadge(recording.visibility)}</TableCell>
                      <TableCell>{formatDateTime(recording.recordedOn)}</TableCell>
                      <TableCell>{recording.duration}</TableCell>
                      <TableCell>{recording.views.toLocaleString('id-ID')}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-1">
                          <Button size="sm" variant="ghost" asChild>
                            <Link href={`/tenant/dashboard/live/recordings/${recording.id}`}>
                              Detail
                            </Link>
                          </Button>
                          <Button size="sm" variant="ghost" asChild>
                            <Link href={`/tenant/dashboard/live/recordings/${recording.id}/edit`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-600">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </Card>
      )}
    </div>
  )
} 