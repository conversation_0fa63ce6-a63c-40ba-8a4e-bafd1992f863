"use client"

import { useState } from "react"
import Link from "next/link"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Download,
  Calendar,
  Clock,
  FileText,
  Filter,
  Refresh<PERSON>w,
  Search,
  Plus,
} from "lucide-react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Sample data for recently generated reports
const recentReports = [
  {
    id: "REP-001",
    name: "Monthly Sales Summary",
    type: "Sales",
    created: "2023-05-10T09:30:00",
    status: "Completed",
  },
  {
    id: "REP-002",
    name: "New User Acquisition Q2",
    type: "User",
    created: "2023-05-09T14:15:00",
    status: "Completed",
  },
  {
    id: "REP-003",
    name: "Tenant Performance Overview",
    type: "Tenant",
    created: "2023-05-08T11:45:00",
    status: "Completed",
  },
  {
    id: "REP-004",
    name: "Product Category Analysis",
    type: "Custom",
    created: "2023-05-07T16:20:00",
    status: "Completed",
  },
  {
    id: "REP-005",
    name: "Weekly Revenue Forecast",
    type: "Sales",
    created: "2023-05-06T10:00:00",
    status: "Scheduled",
  },
]

// Sample data for report templates
const reportTemplates = [
  {
    id: "TMPL-001",
    name: "Monthly Sales Summary",
    category: "Sales",
    lastUsed: "2023-05-01",
    popularity: "High",
  },
  {
    id: "TMPL-002",
    name: "User Growth Analysis",
    category: "User",
    lastUsed: "2023-04-28",
    popularity: "Medium",
  },
  {
    id: "TMPL-003",
    name: "Tenant Performance Comparison",
    category: "Tenant",
    lastUsed: "2023-04-25",
    popularity: "High",
  },
  {
    id: "TMPL-004",
    name: "Product Sales by Category",
    category: "Sales",
    lastUsed: "2023-04-22",
    popularity: "Medium",
  },
  {
    id: "TMPL-005",
    name: "User Retention Analysis",
    category: "User",
    lastUsed: "2023-04-20",
    popularity: "Low",
  },
]

// Sample data for scheduled reports
const scheduledReports = [
  {
    id: "SCH-001",
    name: "Weekly Sales Summary",
    frequency: "Weekly",
    nextRun: "2023-05-14T09:00:00",
    recipients: 5,
  },
  {
    id: "SCH-002",
    name: "Monthly Tenant Performance",
    frequency: "Monthly",
    nextRun: "2023-06-01T08:00:00",
    recipients: 8,
  },
  {
    id: "SCH-003",
    name: "Daily User Signups",
    frequency: "Daily",
    nextRun: "2023-05-11T07:00:00",
    recipients: 3,
  },
  {
    id: "SCH-004",
    name: "Quarterly Revenue Analysis",
    frequency: "Quarterly",
    nextRun: "2023-07-01T10:00:00",
    recipients: 12,
  },
]

export function ReportsDashboard() {
  const [searchQuery, setSearchQuery] = useState("")

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date)
  }

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    }).format(date)
  }

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
        return <Badge className="bg-green-500">Completed</Badge>
      case "Scheduled":
        return <Badge className="bg-blue-500">Scheduled</Badge>
      case "In Progress":
        return <Badge className="bg-yellow-500">In Progress</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Sales":
        return <BarChart className="h-4 w-4 text-blue-500" />
      case "User":
        return <LineChart className="h-4 w-4 text-green-500" />
      case "Tenant":
        return <PieChart className="h-4 w-4 text-purple-500" />
      case "Custom":
        return <FileText className="h-4 w-4 text-orange-500" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports Dashboard</h1>
          <p className="text-muted-foreground">View, create, and manage reports across your platform</p>
        </div>
        <div className="flex items-center gap-2">
          <Button>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="default">
            <Plus className="mr-2 h-4 w-4" />
            New Report
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Total Reports</CardTitle>
            <CardDescription>All reports generated</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">247</div>
            <p className="text-xs text-muted-foreground">+12% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Scheduled Reports</CardTitle>
            <CardDescription>Active scheduled reports</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">18</div>
            <p className="text-xs text-muted-foreground">+3 new this week</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Report Templates</CardTitle>
            <CardDescription>Available report templates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">32</div>
            <p className="text-xs text-muted-foreground">5 custom templates</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Recently Generated Reports</CardTitle>
                <CardDescription>View and manage your recent reports</CardDescription>
              </div>
              <div className="flex w-full max-w-sm items-center space-x-2">
                <Input
                  type="search"
                  placeholder="Search reports..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="h-8"
                />
                <Button type="submit" size="sm" className="h-8 px-2">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Report</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell className="font-medium">{report.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {getCategoryIcon(report.type)}
                        <span>{report.type}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-xs">{formatDate(report.created)}</span>
                        <span className="text-xs text-muted-foreground">{formatTime(report.created)}</span>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(report.status)}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" size="sm">
              View All Reports
            </Button>
            <div className="flex items-center text-sm text-muted-foreground">Showing 5 of 247 reports</div>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Report Categories</CardTitle>
            <CardDescription>Distribution by report type</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center">
            <div className="h-40 w-40 rounded-full border-8 border-primary/20 relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-2xl font-bold">247</div>
                  <div className="text-xs text-muted-foreground">Total Reports</div>
                </div>
              </div>
            </div>
            <div className="mt-6 grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="flex items-center justify-center gap-1">
                  <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                  <span className="text-sm font-medium">Sales</span>
                </div>
                <div className="text-2xl font-bold">112</div>
              </div>
              <div>
                <div className="flex items-center justify-center gap-1">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm font-medium">Users</span>
                </div>
                <div className="text-2xl font-bold">78</div>
              </div>
              <div>
                <div className="flex items-center justify-center gap-1">
                  <div className="h-3 w-3 rounded-full bg-purple-500"></div>
                  <span className="text-sm font-medium">Tenants</span>
                </div>
                <div className="text-2xl font-bold">42</div>
              </div>
              <div>
                <div className="flex items-center justify-center gap-1">
                  <div className="h-3 w-3 rounded-full bg-orange-500"></div>
                  <span className="text-sm font-medium">Custom</span>
                </div>
                <div className="text-2xl font-bold">15</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="templates">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="templates">Report Templates</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled Reports</TabsTrigger>
        </TabsList>
        <TabsContent value="templates">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Report Templates</CardTitle>
                  <CardDescription>Pre-configured report templates for quick generation</CardDescription>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  New Template
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Template Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Last Used</TableHead>
                    <TableHead>Popularity</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reportTemplates.map((template) => (
                    <TableRow key={template.id}>
                      <TableCell className="font-medium">{template.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {getCategoryIcon(template.category)}
                          <span>{template.category}</span>
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(template.lastUsed)}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={
                            template.popularity === "High"
                              ? "border-green-500 text-green-500"
                              : template.popularity === "Medium"
                                ? "border-yellow-500 text-yellow-500"
                                : "border-gray-500 text-gray-500"
                          }
                        >
                          {template.popularity}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm">
                          Generate
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" size="sm">
                View All Templates
              </Button>
              <div className="flex items-center text-sm text-muted-foreground">Showing 5 of 32 templates</div>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="scheduled">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Scheduled Reports</CardTitle>
                  <CardDescription>Reports scheduled for automatic generation</CardDescription>
                </div>
                <Button>
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule New
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Report Name</TableHead>
                    <TableHead>Frequency</TableHead>
                    <TableHead>Next Run</TableHead>
                    <TableHead>Recipients</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {scheduledReports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell className="font-medium">{report.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>{report.frequency}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-xs">{formatDate(report.nextRun)}</span>
                          <span className="text-xs text-muted-foreground">{formatTime(report.nextRun)}</span>
                        </div>
                      </TableCell>
                      <TableCell>{report.recipients} users</TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" size="sm">
                View All Scheduled
              </Button>
              <div className="flex items-center text-sm text-muted-foreground">Showing 4 of 18 scheduled reports</div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Dashboard Creation Tools</CardTitle>
            <CardDescription>Create custom dashboards from your reports</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <div className="flex items-center gap-4 rounded-lg border p-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                  <BarChart className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Sales Dashboard Builder</h3>
                  <p className="text-sm text-muted-foreground">Create custom sales performance dashboards</p>
                </div>
                <Button className="ml-auto" variant="outline" size="sm">
                  Open
                </Button>
              </div>
              <div className="flex items-center gap-4 rounded-lg border p-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                  <LineChart className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">User Analytics Dashboard</h3>
                  <p className="text-sm text-muted-foreground">Track user growth and engagement metrics</p>
                </div>
                <Button className="ml-auto" variant="outline" size="sm">
                  Open
                </Button>
              </div>
              <div className="flex items-center gap-4 rounded-lg border p-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                  <PieChart className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Tenant Performance Dashboard</h3>
                  <p className="text-sm text-muted-foreground">Monitor tenant health and activity</p>
                </div>
                <Button className="ml-auto" variant="outline" size="sm">
                  Open
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full">
              <Plus className="mr-2 h-4 w-4" />
              Create New Dashboard
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common report operations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <Link href="/admin/dashboard/reports/sales">
                <div className="flex flex-col items-center justify-center rounded-lg border p-4 text-center hover:bg-accent">
                  <BarChart className="mb-2 h-8 w-8 text-primary" />
                  <h3 className="font-medium">Sales Reports</h3>
                  <p className="text-xs text-muted-foreground">Generate sales reports</p>
                </div>
              </Link>
              <Link href="/admin/dashboard/reports/tenants">
                <div className="flex flex-col items-center justify-center rounded-lg border p-4 text-center hover:bg-accent">
                  <PieChart className="mb-2 h-8 w-8 text-primary" />
                  <h3 className="font-medium">Tenant Reports</h3>
                  <p className="text-xs text-muted-foreground">Analyze tenant performance</p>
                </div>
              </Link>
              <Link href="/admin/dashboard/reports/users">
                <div className="flex flex-col items-center justify-center rounded-lg border p-4 text-center hover:bg-accent">
                  <LineChart className="mb-2 h-8 w-8 text-primary" />
                  <h3 className="font-medium">User Reports</h3>
                  <p className="text-xs text-muted-foreground">Track user metrics</p>
                </div>
              </Link>
              <Link href="/admin/dashboard/reports/custom">
                <div className="flex flex-col items-center justify-center rounded-lg border p-4 text-center hover:bg-accent">
                  <FileText className="mb-2 h-8 w-8 text-primary" />
                  <h3 className="font-medium">Custom Reports</h3>
                  <p className="text-xs text-muted-foreground">Build custom reports</p>
                </div>
              </Link>
            </div>
          </CardContent>
          <CardFooter>
            <Separator />
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
