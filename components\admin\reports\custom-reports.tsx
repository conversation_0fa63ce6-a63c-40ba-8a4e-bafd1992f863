"use client"

import { useState } from "react"
import {
  Filter,
  Refresh<PERSON><PERSON>,
  Plus,
  FileText,
  Save,
  Settings,
  Database,
  BarChart,
  LineChart,
  PieChart,
  TableIcon,
  Layers,
  ChevronDown,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"

// Sample data for saved reports
const savedReports = [
  {
    id: "CR-001",
    name: "Cross-Platform Sales Analysis",
    description: "Comparison of sales across different platforms and stores",
    created: "2023-05-05T10:30:00",
    lastRun: "2023-05-10T14:15:00",
    type: "Custom",
  },
  {
    id: "CR-002",
    name: "User Acquisition by Marketing Channel",
    description: "Analysis of user acquisition sources and conversion rates",
    created: "2023-04-28T09:45:00",
    lastRun: "2023-05-09T11:20:00",
    type: "Custom",
  },
  {
    id: "CR-003",
    name: "Product Performance by Category",
    description: "Detailed breakdown of product performance across categories",
    created: "2023-04-20T15:10:00",
    lastRun: "2023-05-08T16:30:00",
    type: "Custom",
  },
]

// Sample data for data sources
const dataSources = [
  {
    id: "DS-001",
    name: "Sales Database",
    type: "PostgreSQL",
    tables: ["orders", "order_items", "products", "customers"],
    lastSync: "2023-05-10T08:00:00",
  },
  {
    id: "DS-002",
    name: "User Analytics",
    type: "Analytics API",
    tables: ["user_events", "sessions", "page_views", "conversions"],
    lastSync: "2023-05-10T08:15:00",
  },
  {
    id: "DS-003",
    name: "Marketing Data",
    type: "CSV Import",
    tables: ["campaigns", "ad_spend", "channel_performance"],
    lastSync: "2023-05-09T10:30:00",
  },
  {
    id: "DS-004",
    name: "Product Catalog",
    type: "MongoDB",
    tables: ["products", "categories", "attributes", "inventory"],
    lastSync: "2023-05-10T07:45:00",
  },
]

// Sample data for visualization templates
const visualizationTemplates = [
  {
    id: "VT-001",
    name: "Sales Dashboard",
    type: "Dashboard",
    charts: 5,
    preview: <BarChart className="h-10 w-10 text-primary" />,
  },
  {
    id: "VT-002",
    name: "User Growth Chart",
    type: "Line Chart",
    charts: 1,
    preview: <LineChart className="h-10 w-10 text-primary" />,
  },
  {
    id: "VT-003",
    name: "Category Distribution",
    type: "Pie Chart",
    charts: 1,
    preview: <PieChart className="h-10 w-10 text-primary" />,
  },
  {
    id: "VT-004",
    name: "Performance Comparison",
    type: "Bar Chart",
    charts: 1,
    preview: <BarChart className="h-10 w-10 text-primary" />,
  },
]

export function CustomReports() {
  const [activeTab, setActiveTab] = useState("builder")
  const [selectedDataSource, setSelectedDataSource] = useState<string | null>(null)
  const [selectedVisualization, setSelectedVisualization] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [progress, setProgress] = useState(0)

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date)
  }

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    }).format(date)
  }

  // Simulate report generation
  const generateReport = () => {
    setIsGenerating(true)
    setProgress(0)

    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsGenerating(false)
          return 100
        }
        return prev + 5
      })
    }, 300)
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Custom Reports</h1>
          <p className="text-muted-foreground">Build and manage custom reports with advanced data analysis</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="default">
            <Plus className="mr-2 h-4 w-4" />
            New Report
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="builder">Report Builder</TabsTrigger>
          <TabsTrigger value="saved">Saved Reports</TabsTrigger>
          <TabsTrigger value="data">Data Sources</TabsTrigger>
          <TabsTrigger value="visualizations">Visualizations</TabsTrigger>
        </TabsList>

        <TabsContent value="builder" className="mt-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle>Report Configuration</CardTitle>
                  <CardDescription>Define your custom report parameters</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="report-name">Report Name</Label>
                    <Input id="report-name" placeholder="Enter report name" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="report-description">Description</Label>
                    <Input id="report-description" placeholder="Enter report description" />
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <Label>Data Sources</Label>
                    <div className="space-y-2">
                      {dataSources.map((source) => (
                        <div
                          key={source.id}
                          className={`flex items-center space-x-2 rounded-md border p-3 cursor-pointer ${
                            selectedDataSource === source.id ? "border-primary bg-primary/5" : ""
                          }`}
                          onClick={() => setSelectedDataSource(source.id)}
                        >
                          <Checkbox id={`source-${source.id}`} checked={selectedDataSource === source.id} />
                          <div className="flex-1">
                            <label
                              htmlFor={`source-${source.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {source.name}
                            </label>
                            <p className="text-xs text-muted-foreground">{source.type}</p>
                          </div>
                          <Badge variant="outline">{source.tables.length} tables</Badge>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <Label>Report Type</Label>
                    <Select defaultValue="tabular">
                      <SelectTrigger>
                        <SelectValue placeholder="Select report type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="tabular">Tabular Report</SelectItem>
                        <SelectItem value="summary">Summary Report</SelectItem>
                        <SelectItem value="matrix">Matrix Report</SelectItem>
                        <SelectItem value="dashboard">Dashboard</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Date Range</Label>
                    <Select defaultValue="last30">
                      <SelectTrigger>
                        <SelectValue placeholder="Select date range" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="last7">Last 7 days</SelectItem>
                        <SelectItem value="last30">Last 30 days</SelectItem>
                        <SelectItem value="last90">Last 90 days</SelectItem>
                        <SelectItem value="custom">Custom range</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Schedule</Label>
                    <Select defaultValue="none">
                      <SelectTrigger>
                        <SelectValue placeholder="Select schedule" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No schedule (run manually)</SelectItem>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline">
                    <Save className="mr-2 h-4 w-4" />
                    Save Configuration
                  </Button>
                  <Button onClick={generateReport} disabled={isGenerating}>
                    {isGenerating ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <FileText className="mr-2 h-4 w-4" />
                        Generate Report
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </div>

            <div className="md:col-span-2">
              <Card className="h-full">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Report Builder</CardTitle>
                      <CardDescription>Design your custom report</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Settings className="mr-2 h-4 w-4" />
                        Settings
                      </Button>
                      <Button variant="outline" size="sm">
                        <Filter className="mr-2 h-4 w-4" />
                        Filters
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="border-b">
                    <div className="flex items-center gap-2 p-4">
                      <Button variant="outline" size="sm">
                        <Plus className="mr-2 h-4 w-4" />
                        Add Field
                      </Button>
                      <Button variant="outline" size="sm">
                        <TableIcon className="mr-2 h-4 w-4" />
                        Add Table
                      </Button>
                      <Button variant="outline" size="sm">
                        <BarChart className="mr-2 h-4 w-4" />
                        Add Chart
                      </Button>
                      <Button variant="outline" size="sm">
                        <Layers className="mr-2 h-4 w-4" />
                        Add Section
                      </Button>
                    </div>
                  </div>

                  {isGenerating ? (
                    <div className="flex flex-col items-center justify-center p-8">
                      <FileText className="h-16 w-16 text-primary/60 mb-4" />
                      <h3 className="text-lg font-medium mb-2">Generating Custom Report</h3>
                      <p className="text-sm text-muted-foreground mb-4">Please wait while we process your report</p>
                      <Progress value={progress} className="h-2 w-full max-w-md mb-2" />
                      <p className="text-sm text-muted-foreground">{progress}% Complete</p>
                    </div>
                  ) : selectedDataSource ? (
                    <div className="p-4">
                      <div className="mb-4">
                        <h3 className="text-lg font-medium mb-2">Available Fields</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Select fields from {dataSources.find((ds) => ds.id === selectedDataSource)?.name} to include
                          in your report
                        </p>

                        <div className="space-y-4">
                          {dataSources
                            .find((ds) => ds.id === selectedDataSource)
                            ?.tables.map((table) => (
                              <div key={table} className="rounded-md border">
                                <div className="flex items-center justify-between p-3 cursor-pointer">
                                  <div className="flex items-center">
                                    <Database className="h-4 w-4 mr-2 text-muted-foreground" />
                                    <span className="font-medium">{table}</span>
                                  </div>
                                  <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                </div>
                                <div className="border-t p-3">
                                  <div className="grid grid-cols-2 gap-2">
                                    {["id", "name", "created_at", "updated_at", "status"].map((field) => (
                                      <div key={`${table}-${field}`} className="flex items-center space-x-2">
                                        <Checkbox id={`${table}-${field}`} />
                                        <label
                                          htmlFor={`${table}-${field}`}
                                          className="text-sm font-medium leading-none"
                                        >
                                          {field}
                                        </label>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            ))}
                        </div>
                      </div>

                      <div className="mt-6">
                        <h3 className="text-lg font-medium mb-2">Report Preview</h3>
                        <div className="rounded-md border p-4 bg-muted/20 h-[300px] flex items-center justify-center">
                          <div className="text-center">
                            <TableIcon className="h-16 w-16 text-muted-foreground/60 mx-auto mb-4" />
                            <h4 className="text-lg font-medium">Select fields to preview report</h4>
                            <p className="text-sm text-muted-foreground mt-2">
                              Your report preview will appear here once you select fields and configure your report
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-8 h-[400px]">
                      <Database className="h-16 w-16 text-muted-foreground/60 mb-4" />
                      <h3 className="text-lg font-medium mb-2">Select a Data Source</h3>
                      <p className="text-sm text-muted-foreground text-center max-w-md">
                        Choose a data source from the left panel to start building your custom report
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="saved" className="mt-6">
          <div className="grid gap-6 md:grid-cols-3">
            {savedReports.map((report) => (
              <Card key={report.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">{report.type}</Badge>
                    <div className="text-xs text-muted-foreground">ID: {report.id}</div>
                  </div>
                  <CardTitle className="mt-2">{report.name}</CardTitle>
                  <CardDescription>{report.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Created:</span>
                      <span>{formatDate(report.created)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Last Run:</span>
                      <span>{formatDate(report.lastRun)}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="bg-muted/50 flex justify-between">
                  <Button variant="ghost" size="sm">
                    <FileText className="mr-2 h-4 w-4" />
                    View
                  </Button>
                  <Button variant="ghost" size="sm">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Run
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Settings className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                </CardFooter>
              </Card>
            ))}

            <Card className="flex flex-col items-center justify-center p-6 border-dashed">
              <Plus className="h-10 w-10 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Create New Report</h3>
              <p className="text-sm text-muted-foreground text-center mb-4">
                Start building a new custom report from scratch
              </p>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Report
              </Button>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="data" className="mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            {dataSources.map((source) => (
              <Card key={source.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">{source.type}</Badge>
                    <div className="text-xs text-muted-foreground">ID: {source.id}</div>
                  </div>
                  <CardTitle className="mt-2">{source.name}</CardTitle>
                  <CardDescription>
                    Last synced: {formatDate(source.lastSync)} at {formatTime(source.lastSync)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <h4 className="text-sm font-medium mb-2">Available Tables</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {source.tables.map((table) => (
                      <div key={table} className="flex items-center space-x-2">
                        <Database className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{table}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="bg-muted/50 flex justify-between">
                  <Button variant="ghost" size="sm">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Sync Now
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Settings className="mr-2 h-4 w-4" />
                    Configure
                  </Button>
                </CardFooter>
              </Card>
            ))}

            <Card className="flex flex-col items-center justify-center p-6 border-dashed">
              <Plus className="h-10 w-10 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Add Data Source</h3>
              <p className="text-sm text-muted-foreground text-center mb-4">
                Connect a new database or import data from external sources
              </p>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Source
              </Button>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="visualizations" className="mt-6">
          <div className="grid gap-6 md:grid-cols-4">
            {visualizationTemplates.map((template) => (
              <Card
                key={template.id}
                className={`overflow-hidden cursor-pointer transition-all ${
                  selectedVisualization === template.id ? "ring-2 ring-primary" : ""
                }`}
                onClick={() => setSelectedVisualization(template.id)}
              >
                <CardHeader className="p-4">
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">{template.type}</Badge>
                    <div className="text-xs text-muted-foreground">{template.charts} chart(s)</div>
                  </div>
                </CardHeader>
                <CardContent className="p-4 pt-0 flex justify-center">
                  <div className="bg-muted/50 rounded-md p-6 flex items-center justify-center">{template.preview}</div>
                </CardContent>
                <CardFooter className="p-4 pt-0 flex justify-between items-center">
                  <span className="font-medium">{template.name}</span>
                  <Button variant="ghost" size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            ))}

            <Card className="flex flex-col items-center justify-center p-6 border-dashed">
              <Plus className="h-10 w-10 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Custom Visualization</h3>
              <p className="text-sm text-muted-foreground text-center mb-4">Create a custom chart or visualization</p>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create New
              </Button>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
