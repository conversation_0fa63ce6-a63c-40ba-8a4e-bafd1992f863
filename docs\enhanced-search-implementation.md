# Enhanced Search Engine Implementation

## Overview
Implementasi mesin pencarian canggih di halaman Sellzio yang mampu mengenali typo, sinonim, related keywords, dan relevansi produk, serta mendukung pencarian melalui Enter key dan search icon.

## Features Implemented

### 1. Advanced Search Engine
- **Typo Correction**: Mengenali dan memperbaiki kesalahan ketik umum
- **Synonym Support**: Mendukung pencarian dengan kata sinonim
- **Related Keywords**: Mencari produk dengan kata kunci terkait
- **Fuzzy Matching**: Toleransi terhadap kesalahan ketik dengan algoritma similarity
- **Relevance Scoring**: Sistem scoring untuk mengurutkan hasil berdasarkan relevansi

### 2. Search Triggers
- **Enter Key**: Pencarian dijalankan saat menekan Enter
- **Search Icon**: Pencarian dijalankan saat klik icon search (mode expanded dan normal)
- **Auto Search**: Pencarian otomatis saat klik prediction/suggestion

### 3. Search Database
- **Typo Corrections**: 60+ koreksi typo umum
- **Synonyms**: Mapping sinonim untuk berbagai kategori produk
- **Related Keywords**: Kata kunci terkait untuk setiap kategori
- **Product Keywords**: Database keyword produk yang komprehensif

## Technical Implementation

### Enhanced Search Function
```typescript
const enhancedSearch = (query: string): Product[] => {
  const originalTerms = query.toLowerCase().trim().split(' ')
  const expandedTerms = new Set<string>()
  
  // Add original terms
  originalTerms.forEach(term => expandedTerms.add(term))
  
  // Expand search terms with typo corrections, synonyms, and related keywords
  originalTerms.forEach(term => {
    // 1. Check for typo corrections
    const correctedTerm = keywordPredictionDB.typoCorrections[term]
    if (correctedTerm) {
      expandedTerms.add(correctedTerm.toLowerCase())
    }
    
    // 2. Add synonyms
    const synonyms = keywordPredictionDB.synonyms[term]
    if (synonyms) {
      synonyms.forEach(synonym => expandedTerms.add(synonym.toLowerCase()))
    }
    
    // 3. Add related keywords
    const relatedKeywords = keywordPredictionDB.relatedKeywords[term]
    if (relatedKeywords) {
      relatedKeywords.forEach(related => expandedTerms.add(related.toLowerCase()))
    }
  })

  // Search and score products
  const results: Product[] = []
  sampleProducts.forEach(product => {
    let score = 0
    // Scoring logic...
  })
  
  return results.sort((a, b) => (b.searchScore || 0) - (a.searchScore || 0))
}
```

### Scoring System
```typescript
// Scoring weights
- Exact match in product name: +15 points
- Exact match in short name: +12 points  
- Exact match in category: +10 points
- Partial match (word starts with term): +8 points
- Fuzzy match (similarity > 0.7): +5 points
- Original term bonus: +5 points
```

### Typo Correction Database
```typescript
typoCorrections: {
  // Handphone/Smartphone typos
  'handpone': 'handphone',
  'smartpone': 'smartphone',
  'smarphone': 'smartphone',
  
  // Audio device typos
  'blutooth': 'bluetooth',
  'hadphone': 'headphone',
  'headpone': 'headphone',
  
  // Computer peripherals typos
  'keybord': 'keyboard',
  'keyborad': 'keyboard',
  'laptob': 'laptop',
  'labtop': 'laptop',
  
  // Fashion typos
  'sneaker': 'sneakers',
  'sneker': 'sneakers',
  'backpak': 'backpack',
  
  // Electronics typos
  'powerbank': 'power bank',
  'powerbang': 'power bank',
  'kamera': 'kamera',
  'camara': 'kamera',
  
  // Gaming typos
  'gamin': 'gaming',
  'gamming': 'gaming',
  
  // Wireless typos
  'wireles': 'wireless',
  'wirelless': 'wireless'
}
```

### Synonym Database
```typescript
synonyms: {
  'hp': ['handphone', 'smartphone', 'ponsel', 'telepon'],
  'handphone': ['hp', 'smartphone', 'ponsel', 'telepon'],
  'smartphone': ['hp', 'handphone', 'ponsel', 'telepon'],
  'laptop': ['notebook', 'komputer', 'pc portable'],
  'sepatu': ['shoes', 'sneakers', 'footwear'],
  'tas': ['bag', 'ransel', 'tote', 'backpack'],
  'murah': ['ekonomis', 'terjangkau', 'hemat', 'diskon'],
  'bagus': ['berkualitas', 'terbaik', 'premium']
}
```

### Related Keywords Database
```typescript
relatedKeywords: {
  'smartphone': ['android', 'iphone', 'samsung', 'xiaomi'],
  'laptop': ['gaming', 'asus', 'lenovo', 'acer'],
  'sepatu': ['sneakers', 'running', 'casual', 'sport'],
  'tas': ['selempang', 'ransel', 'laptop', 'sekolah']
}
```

### Search Triggers Implementation

#### Enter Key Handler
```typescript
const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.key === 'Enter') {
    if (inputValue.trim()) {
      onSearchExecute?.(inputValue.trim())
    }
  }
}
```

#### Search Icon Handler
```typescript
const handleSearchClick = () => {
  if (inputValue.trim()) {
    onSearchExecute?.(inputValue.trim())
  }
}
```

### Fuzzy Matching Algorithm
```typescript
const calculateSimilarity = (str1: string, str2: string): number => {
  // Levenshtein distance based similarity calculation
  const len1 = str1.length
  const len2 = str2.length
  
  if (len1 === 0) return len2 === 0 ? 1 : 0
  if (len2 === 0) return 0
  
  const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(null))
  
  // Calculate distance matrix
  for (let i = 0; i <= len1; i++) matrix[i][0] = i
  for (let j = 0; j <= len2; j++) matrix[0][j] = j
  
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      )
    }
  }
  
  const maxLen = Math.max(len1, len2)
  return (maxLen - matrix[len1][len2]) / maxLen
}
```

## Search Flow

### 1. Query Expansion
1. **Input Processing**: Split query into terms
2. **Typo Correction**: Check each term for common typos
3. **Synonym Expansion**: Add synonyms for each term
4. **Related Keywords**: Add related keywords
5. **Term Deduplication**: Remove duplicate expanded terms

### 2. Product Matching
1. **Exact Matching**: Check for exact matches in name, short name, category
2. **Partial Matching**: Check for partial matches at word boundaries
3. **Fuzzy Matching**: Use similarity algorithm for typo tolerance
4. **Scoring**: Calculate relevance score for each match

### 3. Result Ranking
1. **Score Calculation**: Sum all matching scores
2. **Original Term Bonus**: Extra points for original query terms
3. **Sorting**: Sort by score (highest first)
4. **Result Limiting**: Return top results

## Benefits

### 1. User Experience
- **Typo Tolerance**: Users don't need perfect spelling
- **Flexible Search**: Multiple ways to find the same product
- **Relevant Results**: Smart ranking shows most relevant products first
- **Multiple Triggers**: Search via Enter, icon click, or auto-search

### 2. Search Accuracy
- **Expanded Coverage**: Finds products even with different terminology
- **Smart Corrections**: Automatically fixes common typos
- **Context Awareness**: Related keywords provide context-based results
- **Fuzzy Matching**: Handles minor spelling variations

### 3. Performance
- **Client-side Processing**: Fast response without server calls
- **Efficient Algorithms**: Optimized similarity calculations
- **Smart Caching**: Reuse expanded terms for similar queries

## Testing Examples

### Typo Correction
- Input: `handpone` → Corrected to: `handphone`
- Input: `blutooth` → Corrected to: `bluetooth`
- Input: `keybord` → Corrected to: `keyboard`

### Synonym Search
- Input: `hp` → Finds: smartphone, handphone products
- Input: `bag` → Finds: tas, ransel products
- Input: `murah` → Finds: ekonomis, terjangkau products

### Related Keywords
- Input: `smartphone` → Also searches: android, iphone, samsung
- Input: `laptop` → Also searches: gaming, asus, lenovo
- Input: `sepatu` → Also searches: sneakers, running, casual

### Fuzzy Matching
- Input: `smartphon` → Matches: smartphone (similarity > 0.7)
- Input: `headphon` → Matches: headphone (similarity > 0.7)

## Future Enhancements

1. **Machine Learning**: Train model on user search patterns
2. **Contextual Search**: Consider user's previous searches
3. **Category Filtering**: Smart category detection from query
4. **Voice Search**: Speech-to-text integration
5. **Search Analytics**: Track search performance and user behavior
6. **Auto-complete**: Real-time search suggestions
7. **Search History**: Personal search history with smart suggestions

## Files Modified

- `app/sellzio/page.tsx`: Main search implementation
- `components/themes/sellzio/sellzio-header.tsx`: Search triggers
- `docs/enhanced-search-implementation.md`: Documentation

## Dependencies

- React hooks (useState, useEffect)
- Levenshtein distance algorithm
- localStorage API
- Existing prediction system
