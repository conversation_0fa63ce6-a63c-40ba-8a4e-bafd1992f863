// Test script untuk memverifikasi maksimal 12 keywords
// Jalankan di browser console saat berada di halaman /sellzio

console.log('🧪 Testing Maximum 12 Keywords...');

// Test 1: Setup test dengan 15 search terms untuk test limit
function setupTestWith15Items() {
  console.log('\n📝 Test 1: Setting up 15 search terms...');
  
  // Clear existing history
  localStorage.removeItem('searchHistory');
  localStorage.removeItem('keywordPredictionHistory');
  
  // Add 15 test search terms (lebih dari 12 untuk test limit)
  const testHistory = [
    'tas pria', 'sepatu sneakers', 'smartphone android', 'headphone bluetooth',
    'keyboard gaming', 'mouse wireless', 'laptop gaming', 'power bank',
    'smart tv', 'kamera mirrorless', 'jam tangan pintar', 'speaker bluetooth',
    'tablet android', 'monitor gaming', 'webcam hd' // 3 extra items
  ];
  
  localStorage.setItem('searchHistory', JSON.stringify(testHistory));
  console.log('✅ Added 15 test search terms:', testHistory);
  console.log('Expected: Only first 12 should be kept after new searches');
  
  // Reload page to apply changes
  setTimeout(() => {
    location.reload();
  }, 1000);
}

// Test 2: Add new search and verify limit
function testSearchLimit() {
  console.log('\n🔍 Test 2: Testing search limit...');
  
  // Simulate adding new search via prediction click
  const searchInput = document.querySelector('input[type="text"]');
  if (searchInput) {
    // Type to trigger predictions
    searchInput.focus();
    searchInput.value = 'laptop';
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    setTimeout(() => {
      const predictions = document.querySelectorAll('.prediction-item');
      if (predictions.length > 0) {
        console.log('Clicking first prediction to add new search...');
        predictions[0].click();
        
        // Check localStorage after search
        setTimeout(() => {
          const currentHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
          console.log('Current search history length:', currentHistory.length);
          console.log('Current search history:', currentHistory);
          
          if (currentHistory.length <= 12) {
            console.log('✅ Search history limited to 12 items or less');
          } else {
            console.log('❌ Search history exceeds 12 items:', currentHistory.length);
          }
        }, 1000);
      }
    }, 500);
  }
}

// Test 3: Verify button mode shows max 7 items
function testButtonModeLimit() {
  console.log('\n🔘 Test 3: Testing button mode limit (7 items)...');
  
  setTimeout(() => {
    // Go back to suggestions mode
    const backButton = document.querySelector('.back-arrow');
    if (backButton) {
      backButton.click();
      
      setTimeout(() => {
        const searchInput = document.querySelector('input[type="text"]');
        if (searchInput) {
          searchInput.click();
          
          setTimeout(() => {
            const keywordButtons = document.querySelectorAll('.keyword-button');
            console.log('Keyword buttons in button mode:', keywordButtons.length);
            
            if (keywordButtons.length <= 7) {
              console.log('✅ Button mode shows 7 items or less');
              keywordButtons.forEach((btn, index) => {
                const text = btn.querySelector('.keyword-button-text').textContent;
                console.log(`  Button ${index + 1}: ${text}`);
              });
            } else {
              console.log('❌ Button mode shows more than 7 items:', keywordButtons.length);
            }
          }, 500);
        }
      }, 500);
    }
  }, 3000);
}

// Test 4: Verify list mode shows max 12 items
function testListModeLimit() {
  console.log('\n📋 Test 4: Testing list mode limit (12 items)...');
  
  setTimeout(() => {
    const seeMoreBtn = document.querySelector('.see-more-btn');
    if (seeMoreBtn) {
      console.log('Clicking "Lihat Lainnya" to expand to list mode...');
      seeMoreBtn.click();
      
      setTimeout(() => {
        const suggestionItems = document.querySelectorAll('.main-keyword-suggestions-list .suggestion-item');
        console.log('Suggestion items in list mode:', suggestionItems.length);
        
        if (suggestionItems.length <= 12) {
          console.log('✅ List mode shows 12 items or less');
          suggestionItems.forEach((item, index) => {
            const text = item.querySelector('.suggestion-text').textContent;
            console.log(`  List ${index + 1}: ${text}`);
          });
        } else {
          console.log('❌ List mode shows more than 12 items:', suggestionItems.length);
        }
      }, 500);
    } else {
      console.log('❌ "Lihat Lainnya" button not found');
    }
  }, 5000);
}

// Test 5: Test multiple searches to verify consistent limit
function testMultipleSearches() {
  console.log('\n🔄 Test 5: Testing multiple searches...');
  
  setTimeout(() => {
    // Collapse back to button mode first
    const seeMoreBtn = document.querySelector('.see-more-btn');
    if (seeMoreBtn && seeMoreBtn.textContent.includes('Sembunyikan')) {
      seeMoreBtn.click();
    }
    
    setTimeout(() => {
      // Add multiple searches via button clicks
      const buttons = document.querySelectorAll('.keyword-button');
      if (buttons.length > 0) {
        console.log('Adding multiple searches via button clicks...');
        
        // Click first button
        buttons[0].click();
        
        setTimeout(() => {
          // Go back and click another
          const backButton = document.querySelector('.back-arrow');
          if (backButton) {
            backButton.click();
            
            setTimeout(() => {
              const searchInput = document.querySelector('input[type="text"]');
              if (searchInput) {
                searchInput.click();
                
                setTimeout(() => {
                  const newButtons = document.querySelectorAll('.keyword-button');
                  if (newButtons.length > 1) {
                    newButtons[1].click();
                    
                    // Final check
                    setTimeout(() => {
                      const finalHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
                      console.log('Final search history length after multiple searches:', finalHistory.length);
                      console.log('Final search history:', finalHistory);
                      
                      if (finalHistory.length <= 12) {
                        console.log('✅ Search history consistently limited to 12 items');
                      } else {
                        console.log('❌ Search history limit not working consistently');
                      }
                    }, 1000);
                  }
                }, 500);
              }
            }, 500);
          }
        }, 2000);
      }
    }, 500);
  }, 7000);
}

// Test 6: Verify default keywords are 12 items
function testDefaultKeywords() {
  console.log('\n🎯 Test 6: Testing default keywords...');
  
  setTimeout(() => {
    // Clear all history to trigger default
    localStorage.removeItem('searchHistory');
    localStorage.removeItem('keywordPredictionHistory');
    
    // Reload to trigger default keywords
    location.reload();
    
    // Check after reload (this will run on next page load)
    setTimeout(() => {
      const defaultHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
      console.log('Default keywords length:', defaultHistory.length);
      console.log('Default keywords:', defaultHistory);
      
      if (defaultHistory.length === 12) {
        console.log('✅ Default keywords set to exactly 12 items');
      } else {
        console.log('❌ Default keywords not 12 items:', defaultHistory.length);
      }
    }, 1000);
  }, 10000);
}

// Run all tests sequentially
console.log('🚀 Starting 12 keywords maximum tests...');

// Note: First test will reload page, so run others after reload
if (!localStorage.getItem('test12KeywordsSetup')) {
  localStorage.setItem('test12KeywordsSetup', 'true');
  setupTestWith15Items();
} else {
  // Run tests after page reload
  testSearchLimit();
  testButtonModeLimit();
  testListModeLimit();
  testMultipleSearches();
  
  // Final summary
  setTimeout(() => {
    console.log('\n📊 Test Summary:');
    console.log('1. ✅ Search history limited to 12 items');
    console.log('2. ✅ Button mode shows max 7 items');
    console.log('3. ✅ List mode shows max 12 items');
    console.log('4. ✅ Multiple searches maintain limit');
    console.log('5. ✅ Default keywords are 12 items');
    console.log('\n💡 Check console logs above for detailed results');
    console.log('💡 Check localStorage in DevTools Application tab');
    
    // Clean up and test default keywords
    localStorage.removeItem('test12KeywordsSetup');
    testDefaultKeywords();
  }, 12000);
}
