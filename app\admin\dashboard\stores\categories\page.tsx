import type { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"

import { StoreCategories } from "@/components/admin/stores/store-categories"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export const metadata: Metadata = {
  title: "Store Categories | Admin Dashboard",
  description: "Manage store categories and subcategories",
}

export default function StoreCategoriesPage() {
  return (
    <div className="flex flex-col gap-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Store Categories</h1>
        <p className="text-muted-foreground">Manage categories and subcategories for stores.</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Categories Management</CardTitle>
          <CardDescription>Create, edit, and organize store categories.</CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
            <StoreCategories />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
