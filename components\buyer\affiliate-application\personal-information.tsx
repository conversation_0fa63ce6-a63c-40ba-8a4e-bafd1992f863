"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useAuth } from "@/contexts/auth-context"

interface PersonalInformationProps {
  data: {
    background: string
    expertise: string[]
    experience: string
    motivation: string
  }
  onUpdate: (data: any) => void
}

export function PersonalInformation({ data, onUpdate }: PersonalInformationProps) {
  const { user } = useAuth()
  const [formData, setFormData] = useState(data)

  const expertiseOptions = [
    { id: "fashion", label: "Fashion & Apparel" },
    { id: "electronics", label: "Electronics & Gadgets" },
    { id: "beauty", label: "Beauty & Personal Care" },
    { id: "home", label: "Home & Furniture" },
    { id: "food", label: "Food & Beverages" },
    { id: "health", label: "Health & Wellness" },
    { id: "sports", label: "Sports & Outdoors" },
    { id: "toys", label: "Toys & Games" },
    { id: "books", label: "Books & Media" },
    { id: "other", label: "Other" },
  ]

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => {
      const newData = { ...prev, [name]: value }
      onUpdate(newData)
      return newData
    })
  }

  const handleExpertiseChange = (id: string, checked: boolean) => {
    setFormData((prev) => {
      const expertise = checked ? [...prev.expertise, id] : prev.expertise.filter((item) => item !== id)

      const newData = { ...prev, expertise }
      onUpdate(newData)
      return newData
    })
  }

  return (
    <div className="space-y-6">
      {/* Existing Account Information */}
      <div className="rounded-lg border border-border/60 bg-muted/30 p-4">
        <h3 className="mb-3 text-sm font-medium">Account Information</h3>
        <div className="grid gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <Label>Full Name</Label>
            <Input value={user?.name || "John Doe"} disabled />
          </div>
          <div className="space-y-2">
            <Label>Email Address</Label>
            <Input value={user?.email || "<EMAIL>"} disabled />
          </div>
        </div>
      </div>

      {/* Professional Background */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Professional Background</h3>
        <div className="space-y-2">
          <Label htmlFor="background">Briefly describe your professional background</Label>
          <Textarea
            id="background"
            name="background"
            value={formData.background}
            onChange={handleInputChange}
            placeholder="Tell us about your professional experience, education, and skills..."
            className="min-h-[100px]"
          />
        </div>
      </div>

      {/* Areas of Expertise */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Areas of Expertise</h3>
        <p className="text-xs text-muted-foreground">
          Select the product categories you have experience with or are interested in promoting.
        </p>
        <div className="grid gap-2 sm:grid-cols-2">
          {expertiseOptions.map((option) => (
            <div key={option.id} className="flex items-center space-x-2">
              <Checkbox
                id={`expertise-${option.id}`}
                checked={formData.expertise.includes(option.id)}
                onCheckedChange={(checked) => handleExpertiseChange(option.id, checked as boolean)}
              />
              <Label htmlFor={`expertise-${option.id}`} className="text-sm font-normal">
                {option.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Affiliate Experience */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Affiliate Experience</h3>
        <div className="space-y-2">
          <Label>Do you have previous experience with affiliate marketing?</Label>
          <RadioGroup
            name="experience"
            value={formData.experience}
            onValueChange={(value) => handleInputChange({ target: { name: "experience", value } })}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="none" id="experience-none" />
              <Label htmlFor="experience-none" className="text-sm font-normal">
                No experience
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="beginner" id="experience-beginner" />
              <Label htmlFor="experience-beginner" className="text-sm font-normal">
                Beginner (0-1 year)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="intermediate" id="experience-intermediate" />
              <Label htmlFor="experience-intermediate" className="text-sm font-normal">
                Intermediate (1-3 years)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="advanced" id="experience-advanced" />
              <Label htmlFor="experience-advanced" className="text-sm font-normal">
                Advanced (3+ years)
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      {/* Motivation */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Motivation for Joining</h3>
        <div className="space-y-2">
          <Label htmlFor="motivation">Why do you want to join our affiliate program?</Label>
          <Textarea
            id="motivation"
            name="motivation"
            value={formData.motivation}
            onChange={handleInputChange}
            placeholder="Explain your motivation and goals for joining our affiliate program..."
            className="min-h-[100px]"
          />
        </div>
      </div>
    </div>
  )
}
