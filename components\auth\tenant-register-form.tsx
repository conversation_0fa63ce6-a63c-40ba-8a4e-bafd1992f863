"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"

interface TenantRegisterFormProps {
  selectedPlan?: string | null
}

export function TenantRegisterForm({ selectedPlan }: TenantRegisterFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const { register } = useAuth()

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    companyName: "",
    companyDescription: "",
    industry: "",
    phone: "",
    website: "",
    plan: selectedPlan || "free",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData((prev) => ({ ...prev, [id]: value }))
  }

  const handleSelectChange = (value: string, field: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setIsLoading(true)
    setError(null)

    // Validasi password
    if (formData.password !== formData.confirmPassword) {
      setError("Password dan konfirmasi password tidak cocok")
      setIsLoading(false)
      return
    }

    try {
      await register(formData.name, formData.email, formData.password, "tenant", {
        companyName: formData.companyName,
        companyDescription: formData.companyDescription,
        industry: formData.industry,
        phone: formData.phone,
        website: formData.website,
        plan: formData.plan,
      })
      router.push("/tenant/dashboard")
    } catch (err) {
      console.error("Registration error:", err)
      setError("Pendaftaran gagal. Silakan coba lagi.")
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <form onSubmit={onSubmit}>
        <CardContent className="space-y-4 pt-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="name">Nama Admin</Label>
            <Input id="name" value={formData.name} onChange={handleChange} placeholder="Nama Lengkap" required />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Bisnis</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input id="password" type="password" value={formData.password} onChange={handleChange} required />
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Konfirmasi Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Nomor Telepon Bisnis</Label>
            <Input
              id="phone"
              type="tel"
              value={formData.phone}
              onChange={handleChange}
              placeholder="08123456789"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="companyName">Nama Perusahaan</Label>
            <Input
              id="companyName"
              value={formData.companyName}
              onChange={handleChange}
              placeholder="Nama Perusahaan Anda"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="website">Website (opsional)</Label>
            <Input
              id="website"
              type="url"
              value={formData.website}
              onChange={handleChange}
              placeholder="https://perusahaan-anda.com"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="industry">Industri</Label>
            <Select value={formData.industry} onValueChange={(value) => handleSelectChange(value, "industry")}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih industri" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="retail">Retail</SelectItem>
                <SelectItem value="fashion">Fashion</SelectItem>
                <SelectItem value="electronics">Elektronik</SelectItem>
                <SelectItem value="food">Makanan & Minuman</SelectItem>
                <SelectItem value="health">Kesehatan</SelectItem>
                <SelectItem value="education">Pendidikan</SelectItem>
                <SelectItem value="other">Lainnya</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="companyDescription">Deskripsi Perusahaan</Label>
            <Textarea
              id="companyDescription"
              value={formData.companyDescription}
              onChange={handleChange}
              placeholder="Deskripsi singkat tentang perusahaan Anda"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="plan">Paket</Label>
            <Select value={formData.plan} onValueChange={(value) => handleSelectChange(value, "plan")}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih paket" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="free">Gratis</SelectItem>
                <SelectItem value="starter">Starter</SelectItem>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="enterprise">Enterprise</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Mendaftar..." : "Daftar sebagai Tenant"}
          </Button>
        </CardContent>
      </form>
    </Card>
  )
}
