"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Filter,
  Download,
  Search,
  BarChart3,
  Percent,
  Tag,
  Clock,
  Calendar,
  Edit,
  Copy,
  Trash,
} from "lucide-react"

// Mock data for promotions
const promotionsData = [
  {
    id: 1,
    name: "Summer Sale 2023",
    type: "Percentage Discount",
    startDate: "2023-06-01",
    endDate: "2023-06-30",
    status: "Active",
    discount: "20%",
    usageCount: 1245,
    revenue: "$28,450",
    performance: "+18%",
  },
  {
    id: 2,
    name: "New User Welcome",
    type: "Fixed Amount",
    startDate: "Ongoing",
    endDate: "Ongoing",
    status: "Active",
    discount: "$10",
    usageCount: 3210,
    revenue: "$45,890",
    performance: "+24%",
  },
  {
    id: 3,
    name: "Flash Sale Weekend",
    type: "Buy One Get One",
    startDate: "2023-07-15",
    endDate: "2023-07-16",
    status: "Scheduled",
    discount: "BOGO",
    usageCount: 0,
    revenue: "$0",
    performance: "N/A",
  },
  {
    id: 4,
    name: "Holiday Special",
    type: "Percentage Discount",
    startDate: "2022-12-15",
    endDate: "2022-12-31",
    status: "Expired",
    discount: "25%",
    usageCount: 4567,
    revenue: "$78,920",
    performance: "+32%",
  },
  {
    id: 5,
    name: "Clearance Items",
    type: "Percentage Discount",
    startDate: "2023-05-01",
    endDate: "2023-05-15",
    status: "Expired",
    discount: "40%",
    usageCount: 2345,
    revenue: "$34,560",
    performance: "+15%",
  },
]

// Mock data for performance metrics
const metricData = [
  { title: "Active Promotions", value: "12", trend: "+2", trendDirection: "up", icon: <Tag className="h-4 w-4" /> },
  {
    title: "Avg. Discount",
    value: "22.5%",
    trend: "-1.5%",
    trendDirection: "down",
    icon: <Percent className="h-4 w-4" />,
  },
  {
    title: "Promotion Revenue",
    value: "$145,890",
    trend: "+12.4%",
    trendDirection: "up",
    icon: <BarChart3 className="h-4 w-4" />,
  },
  { title: "Upcoming Promotions", value: "8", trend: "+3", trendDirection: "up", icon: <Clock className="h-4 w-4" /> },
]

// Mock data for performance chart
const performanceData = [
  { month: "Jan", revenue: 12000, orders: 450, discount: 3600 },
  { month: "Feb", revenue: 15000, orders: 520, discount: 4500 },
  { month: "Mar", revenue: 18000, orders: 580, discount: 5400 },
  { month: "Apr", revenue: 21000, orders: 650, discount: 6300 },
  { month: "May", revenue: 24000, orders: 720, discount: 7200 },
  { month: "Jun", revenue: 27000, orders: 790, discount: 8100 },
]

// Revenue by promotion type data
const revenueByType = [
  { type: "Percentage Discount", revenue: "$141,930" },
  { type: "Fixed Amount", revenue: "$45,890" },
  { type: "Buy One Get One", revenue: "$0" },
  { type: "Free Shipping", revenue: "$23,450" },
  { type: "Bundle Discount", revenue: "$18,670" },
]

export default function PlatformPromotions() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("all")
  const [dateRange, setDateRange] = useState({ start: "", end: "" })

  // Filter promotions based on search term and status filter
  const getFilteredPromotions = (status = statusFilter) => {
    return promotionsData.filter((promotion) => {
      const matchesSearch = promotion.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = status === "all" || promotion.status === status
      return matchesSearch && matchesStatus
    })
  }

  const filteredPromotions = getFilteredPromotions()

  // Safely get top performing promotions
  const getTopPerformingPromotions = () => {
    return promotionsData
      .filter((p) => p.status === "Active" || p.status === "Expired")
      .filter((p) => p.performance !== "N/A")
      .sort((a, b) => {
        // Safely parse performance values
        const aVal =
          typeof a.performance === "string" ? Number.parseFloat(a.performance.replace("%", "").replace("+", "")) : 0
        const bVal =
          typeof b.performance === "string" ? Number.parseFloat(b.performance.replace("%", "").replace("+", "")) : 0
        return bVal - aVal
      })
      .slice(0, 3)
  }

  const topPerformingPromotions = getTopPerformingPromotions()

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Platform Promotions</h1>
          <p className="text-muted-foreground">Create and manage platform-wide promotional campaigns</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create Promotion
          </Button>
        </div>
      </div>

      {/* Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metricData.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between space-y-0 pb-2">
                <p className="text-sm font-medium leading-none">{metric.title}</p>
                <div className="rounded-full bg-muted p-1">{metric.icon}</div>
              </div>
              <div className="flex items-baseline">
                <h3 className="text-2xl font-bold">{metric.value}</h3>
                <span
                  className={`ml-2 text-xs ${
                    metric.trendDirection === "up"
                      ? "text-green-500"
                      : metric.trendDirection === "down"
                        ? "text-red-500"
                        : "text-muted-foreground"
                  }`}
                >
                  {metric.trend}
                </span>
              </div>
              <p className="text-xs text-muted-foreground pt-1">vs. previous period</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tabs */}
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="all">All Promotions</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="expired">Expired</TabsTrigger>
          <TabsTrigger value="builder">Promotion Builder</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        {/* All Promotions Tab */}
        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader className="pb-0">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search promotions..."
                      className="w-full md:w-[300px] pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Scheduled">Scheduled</SelectItem>
                      <SelectItem value="Expired">Expired</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Discount</TableHead>
                      <TableHead>Usage</TableHead>
                      <TableHead>Performance</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPromotions.length > 0 ? (
                      filteredPromotions.map((promotion) => (
                        <TableRow key={promotion.id}>
                          <TableCell className="font-medium">{promotion.name}</TableCell>
                          <TableCell>{promotion.type}</TableCell>
                          <TableCell>{promotion.startDate}</TableCell>
                          <TableCell>{promotion.endDate}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                promotion.status === "Active"
                                  ? "default"
                                  : promotion.status === "Scheduled"
                                    ? "secondary"
                                    : "outline"
                              }
                            >
                              {promotion.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{promotion.discount}</TableCell>
                          <TableCell>{promotion.usageCount.toLocaleString()}</TableCell>
                          <TableCell
                            className={
                              promotion.performance === "N/A"
                                ? ""
                                : promotion.performance.startsWith("+")
                                  ? "text-green-600"
                                  : "text-red-600"
                            }
                          >
                            {promotion.performance}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button variant="ghost" size="icon">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={9} className="h-24 text-center">
                          No promotions found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Active Tab */}
        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Promotions</CardTitle>
              <CardDescription>Currently running promotional campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Discount</TableHead>
                      <TableHead>Usage</TableHead>
                      <TableHead>Performance</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getFilteredPromotions("Active").length > 0 ? (
                      getFilteredPromotions("Active").map((promotion) => (
                        <TableRow key={promotion.id}>
                          <TableCell className="font-medium">{promotion.name}</TableCell>
                          <TableCell>{promotion.type}</TableCell>
                          <TableCell>{promotion.startDate}</TableCell>
                          <TableCell>{promotion.endDate}</TableCell>
                          <TableCell>{promotion.discount}</TableCell>
                          <TableCell>{promotion.usageCount.toLocaleString()}</TableCell>
                          <TableCell
                            className={
                              promotion.performance === "N/A"
                                ? ""
                                : promotion.performance.startsWith("+")
                                  ? "text-green-600"
                                  : "text-red-600"
                            }
                          >
                            {promotion.performance}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button variant="ghost" size="icon">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={8} className="h-24 text-center">
                          No active promotions found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scheduled Tab */}
        <TabsContent value="scheduled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Promotions</CardTitle>
              <CardDescription>Upcoming promotional campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Discount</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getFilteredPromotions("Scheduled").length > 0 ? (
                      getFilteredPromotions("Scheduled").map((promotion) => (
                        <TableRow key={promotion.id}>
                          <TableCell className="font-medium">{promotion.name}</TableCell>
                          <TableCell>{promotion.type}</TableCell>
                          <TableCell>{promotion.startDate}</TableCell>
                          <TableCell>{promotion.endDate}</TableCell>
                          <TableCell>{promotion.discount}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button variant="ghost" size="icon">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={6} className="h-24 text-center">
                          No scheduled promotions found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Expired Tab */}
        <TabsContent value="expired" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Expired Promotions</CardTitle>
              <CardDescription>Past promotional campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Discount</TableHead>
                      <TableHead>Usage</TableHead>
                      <TableHead>Revenue</TableHead>
                      <TableHead>Performance</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getFilteredPromotions("Expired").length > 0 ? (
                      getFilteredPromotions("Expired").map((promotion) => (
                        <TableRow key={promotion.id}>
                          <TableCell className="font-medium">{promotion.name}</TableCell>
                          <TableCell>{promotion.type}</TableCell>
                          <TableCell>{promotion.startDate}</TableCell>
                          <TableCell>{promotion.endDate}</TableCell>
                          <TableCell>{promotion.discount}</TableCell>
                          <TableCell>{promotion.usageCount.toLocaleString()}</TableCell>
                          <TableCell>{promotion.revenue}</TableCell>
                          <TableCell
                            className={
                              promotion.performance === "N/A"
                                ? ""
                                : promotion.performance.startsWith("+")
                                  ? "text-green-600"
                                  : "text-red-600"
                            }
                          >
                            {promotion.performance}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button variant="ghost" size="icon">
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={9} className="h-24 text-center">
                          No expired promotions found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Promotion Builder Tab */}
        <TabsContent value="builder" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Promotion Builder</CardTitle>
              <CardDescription>Create a new promotional campaign for the platform</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="promotion-name">Promotion Name</Label>
                    <Input id="promotion-name" placeholder="Enter promotion name" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="promotion-type">Promotion Type</Label>
                    <Select>
                      <SelectTrigger id="promotion-type">
                        <SelectValue placeholder="Select promotion type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="percentage">Percentage Discount</SelectItem>
                        <SelectItem value="fixed">Fixed Amount</SelectItem>
                        <SelectItem value="bogo">Buy One Get One</SelectItem>
                        <SelectItem value="free-shipping">Free Shipping</SelectItem>
                        <SelectItem value="bundle">Bundle Discount</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="discount-value">Discount Value</Label>
                    <Input id="discount-value" placeholder="Enter discount value" />
                  </div>

                  <div className="space-y-2">
                    <Label>Promotion Period</Label>
                    <div className="grid gap-2 grid-cols-2">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <Input
                          type="date"
                          placeholder="Start date"
                          value={dateRange.start}
                          onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
                        />
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <Input
                          type="date"
                          placeholder="End date"
                          value={dateRange.end}
                          onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Target Audience</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="all-users" />
                        <Label htmlFor="all-users">All Users</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="new-users" />
                        <Label htmlFor="new-users">New Users</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="returning-users" />
                        <Label htmlFor="returning-users">Returning Users</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="premium-users" />
                        <Label htmlFor="premium-users">Premium Users</Label>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Applicable To</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="all-products" />
                        <Label htmlFor="all-products">All Products</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="specific-categories" />
                        <Label htmlFor="specific-categories">Specific Categories</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="specific-products" />
                        <Label htmlFor="specific-products">Specific Products</Label>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="min-purchase">Minimum Purchase Amount</Label>
                    <Input id="min-purchase" placeholder="Enter minimum amount" />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end space-x-4">
              <Button variant="outline">Save as Draft</Button>
              <Button>Create Promotion</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Promotion Performance</CardTitle>
              <CardDescription>Track the performance of promotional campaigns over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center border rounded-md p-6">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">Performance Chart</h3>
                  <p className="text-sm text-muted-foreground mt-2">
                    This is a placeholder for the performance chart. In a real implementation, this would show a chart
                    of promotion performance over time.
                  </p>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2 mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Top Performing Promotions</CardTitle>
                    <CardDescription>Based on conversion rate</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {topPerformingPromotions.length > 0 ? (
                        topPerformingPromotions.map((promotion) => (
                          <div
                            key={promotion.id}
                            className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0"
                          >
                            <div>
                              <p className="font-medium">{promotion.name}</p>
                              <p className="text-sm text-muted-foreground">{promotion.type}</p>
                            </div>
                            <div className="text-right">
                              <p
                                className={`font-medium ${
                                  promotion.performance === "N/A"
                                    ? ""
                                    : promotion.performance.startsWith("+")
                                      ? "text-green-600"
                                      : "text-red-600"
                                }`}
                              >
                                {promotion.performance}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {promotion.usageCount.toLocaleString()} uses
                              </p>
                            </div>
                          </div>
                        ))
                      ) : (
                        <p className="text-center text-muted-foreground py-4">No performance data available.</p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Revenue by Promotion Type</CardTitle>
                    <CardDescription>Total revenue generated by promotion type</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {revenueByType.map((item, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <p>{item.type}</p>
                          <p className="font-medium">{item.revenue}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
