import type { TenantTheme } from "./tenant-theme"

// Tema default Velozio
export const velozioTheme: Partial<TenantTheme> = {
  name: "<PERSON>elo<PERSON>",
  colors: {
    primary: "#ee4d2d",
    secondary: "#f5a623",
    accent: "#10b981",
    background: "#ffffff",
    foreground: "#333333",
    muted: "#f2f2f2",
    mutedForeground: "#999999",
    border: "#e2e8f0",
    input: "#e2e8f0",
    card: "#ffffff",
    cardForeground: "#333333",
    destructive: "#ef4444",
    destructiveForeground: "#ffffff",
  },
  fonts: {
    heading: "Roboto, sans-serif",
    body: "Roboto, Arial, sans-serif",
  },
  layout: {
    header: "default",
    footer: "default",
    sidebar: "default",
  },
  customCSS: `
    /* Velozio Custom CSS */
    .velozio-search-container .search-input {
      border: 2px solid var(--primary);
      border-radius: 8px;
    }
    
    .velozio-search-container .search-icon {
      color: var(--primary);
    }
    
    .velozio-search-container .clear-search-icon {
      background-color: #ccc;
    }
    
    .velozio-search-container .suggestion-item:hover {
      background-color: #f9f9f9;
    }
    
    .velozio-search-container .product-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }
    
    .velozio-search-container .filter-tab.active {
      color: var(--primary);
    }
    
    .velozio-search-container .filter-tab.active:after {
      background-color: var(--primary);
    }
  `,
}
