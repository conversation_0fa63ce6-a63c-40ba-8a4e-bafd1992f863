import { NextRequest, NextResponse } from 'next/server';

// Sample data untuk keranjang (akan diganti dengan database nanti)
let cartItems = [
  {
    id: "cart-1",
    productId: "product-4",
    name: "Jam Tangan",
    price: 450000,
    image: "/wrist-watch-close-up.png",
    store: "Watch World",
    storeId: "store-4",
    quantity: 1,
    addedAt: "2025-05-15",
  }
];

// GET - Mendapatkan semua item di keranjang
export async function GET(request: NextRequest) {
  // Bisa ditambahkan parameter untuk memfilter berdasarkan user ID nanti
  return NextResponse.json(cartItems);
}

// POST - Menambahkan item baru ke keranjang
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validasi dasar
    if (!body.productId || !body.name || !body.price) {
      return NextResponse.json({ error: 'Data produk tidak lengkap' }, { status: 400 });
    }
    
    // Cek apakah produk sudah ada di keranjang
    const existingItemIndex = cartItems.findIndex(item => item.productId === body.productId);
    
    if (existingItemIndex !== -1) {
      // Jika sudah ada, update quantity
      cartItems[existingItemIndex].quantity += body.quantity || 1;
      
      return NextResponse.json({
        message: 'Jumlah item diperbarui',
        item: cartItems[existingItemIndex]
      });
    }
    
    // Buat ID baru
    const newCartId = `cart-${Math.floor(1000 + Math.random() * 9000)}`;
    
    // Buat item keranjang baru
    const newItem = {
      id: newCartId,
      productId: body.productId,
      name: body.name,
      price: body.price,
      image: body.image || "/placeholder.svg",
      store: body.store || "Unknown Store",
      storeId: body.storeId || "unknown",
      quantity: body.quantity || 1,
      addedAt: new Date().toISOString().split('T')[0],
    };
    
    // Tambahkan ke array keranjang
    cartItems.push(newItem);
    
    return NextResponse.json({
      message: 'Item berhasil ditambahkan ke keranjang',
      item: newItem
    }, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: 'Terjadi kesalahan server' }, { status: 500 });
  }
} 