"use client"

import { useState, useEffect } from "react"
import { Search, Grid3X3, List, ChevronDown, ShoppingCart, Trash2, FolderPlus, Share2, Eye, Folder } from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { toast } from "@/components/ui/use-toast"
import { Skeleton } from "@/components/ui/skeleton"
import { useRouter } from "next/navigation"
import { DeleteConfirmationDialog } from "@/components/buyer/delete-confirmation-dialog"
import { ProductQuickView } from "@/components/buyer/product-quick-view"
import { ShareProduct } from "@/components/buyer/share-product"
import { CollectionCreator } from "@/components/buyer/collection-creator"
import { CollectionSelector } from "@/components/buyer/collection-selector"

// Tipe data untuk item wishlist
interface WishlistItem {
  id: string
  name: string
  price: number
  originalPrice: number
  image: string
  inStock: boolean
  store: string
  storeId: string
  dateAdded: string
  priceDropped: boolean
  productId: string
}

export default function WishlistPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortBy, setSortBy] = useState("recently-added")
  const [filteredItems, setFilteredItems] = useState<WishlistItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([])
  const router = useRouter()

  // Fungsi untuk mengambil data wishlist
  const fetchWishlistItems = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/wishlist')
      if (!response.ok) {
        throw new Error('Gagal mengambil data wishlist')
      }
      const data = await response.json()
      setWishlistItems(data)
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal memuat data wishlist",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fungsi untuk menghapus item dari wishlist
  const removeFromWishlist = async (id: string) => {
    try {
      const response = await fetch(`/api/wishlist/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        throw new Error('Gagal menghapus item dari wishlist')
      }
      
      // Update state lokal setelah menghapus
      setWishlistItems(prev => prev.filter(item => item.id !== id))
      
      toast({
        title: "Berhasil",
        description: "Item berhasil dihapus dari wishlist",
      })
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal menghapus item dari wishlist",
        variant: "destructive",
      })
    }
  }

  // Fungsi untuk menambahkan item ke keranjang
  const addToCart = async (item: WishlistItem) => {
    try {
      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: item.productId,
          name: item.name,
          price: item.price,
          image: item.image,
          store: item.store,
          storeId: item.storeId,
          quantity: 1
        }),
      })
      
      if (!response.ok) {
        throw new Error('Gagal menambahkan ke keranjang')
      }
      
      const data = await response.json()
      
      toast({
        title: "Berhasil",
        description: `${item.name} ditambahkan ke keranjang`,
      })
      
      // Opsional: Hapus dari wishlist setelah ditambahkan ke keranjang
      // await removeFromWishlist(item.id)
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal menambahkan item ke keranjang",
        variant: "destructive",
      })
    }
  }

  // Fungsi untuk melihat detail produk
  const viewProductDetail = (productId: string) => {
    console.log("Navigasi ke produk:", productId)
    router.push(`/products/${productId}`)
  }

  // Effect untuk mengambil data wishlist saat komponen dimuat
  useEffect(() => {
    fetchWishlistItems()
  }, [])

  // Effect untuk memfilter dan mengurutkan items
  useEffect(() => {
    if (!wishlistItems.length) {
      setFilteredItems([])
      return
    }

    let results = [...wishlistItems]

    // Filter berdasarkan pencarian
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      results = results.filter(
        item => item.name.toLowerCase().includes(query) || item.store.toLowerCase().includes(query)
      )
    }

    // Urutkan berdasarkan pilihan
    switch (sortBy) {
      case 'recently-added':
        results.sort((a, b) => new Date(b.dateAdded).getTime() - new Date(a.dateAdded).getTime())
        break
      case 'price-low-high':
        results.sort((a, b) => a.price - b.price)
        break
      case 'price-high-low':
        results.sort((a, b) => b.price - a.price)
        break
      case 'name-a-z':
        results.sort((a, b) => a.name.localeCompare(b.name))
        break
    }

    setFilteredItems(results)
  }, [wishlistItems, searchQuery, sortBy])

  // Tampilkan loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-40" />
          <div className="flex gap-2">
            <Skeleton className="h-9 w-32" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
        <div className="flex flex-col gap-4 sm:flex-row">
          <Skeleton className="h-10 flex-1" />
          <div className="flex gap-2">
            <Skeleton className="h-10 w-[180px]" />
            <Skeleton className="h-10 w-20" />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
          {Array(10).fill(0).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <Skeleton className="aspect-square w-full" />
              <CardContent className="p-3">
                <Skeleton className="mb-1 h-4 w-16" />
                <Skeleton className="h-5 w-full" />
                <Skeleton className="mt-1 h-5 w-24" />
                <Skeleton className="mt-2 h-8 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">My Wishlist</h1>
        <div className="flex items-center gap-2">
          <CollectionCreator size="sm" />
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-1"
            onClick={() => router.push('/buyer/dashboard/collections')}
          >
            <Folder className="h-4 w-4" />
            <span className="hidden sm:inline">Lihat Koleksi</span>
          </Button>
        </div>
      </div>

      {/* Pencarian dan Pengurutan */}
      <div className="flex flex-col gap-4 sm:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Cari nama produk atau toko..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Select defaultValue={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Urutkan" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recently-added">Terbaru Ditambahkan</SelectItem>
              <SelectItem value="price-low-high">Harga: Rendah ke Tinggi</SelectItem>
              <SelectItem value="price-high-low">Harga: Tinggi ke Rendah</SelectItem>
              <SelectItem value="name-a-z">Nama: A ke Z</SelectItem>
            </SelectContent>
          </Select>
          
          <div className="flex rounded-md border">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="icon"
              className="rounded-none rounded-l-md"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="icon"
              className="rounded-none rounded-r-md"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Tampilkan pesan jika wishlist kosong */}
      {filteredItems.length === 0 ? (
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">
          <div className="mb-4 rounded-full bg-muted p-4">
            <FolderPlus className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="mb-2 text-xl font-medium">Wishlist Anda Kosong</h3>
          <p className="mb-6 text-muted-foreground">
            {searchQuery 
              ? "Tidak ada produk yang cocok dengan pencarian Anda." 
              : "Anda belum menambahkan produk ke wishlist."}
          </p>
          {searchQuery ? (
            <Button onClick={() => setSearchQuery("")}>Hapus Pencarian</Button>
          ) : (
            <Button onClick={() => router.push('/products')}>Jelajahi Produk</Button>
          )}
        </div>
      ) : viewMode === "grid" ? (
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
          {filteredItems.map((item) => (
            <Card key={item.id} className="overflow-hidden">
              <div className="relative">
                <div 
                  className="aspect-square w-full bg-gray-100 cursor-pointer" 
                  onClick={() => viewProductDetail(item.productId)}
                >
                  <img 
                    src={item.image || "/placeholder.svg"} 
                    alt={item.name} 
                    className="h-full w-full object-cover" 
                  />
                </div>
                {!item.inStock && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                    <Badge variant="outline" className="bg-black text-white">
                      Stok Habis
                    </Badge>
                  </div>
                )}
                {item.priceDropped && <Badge className="absolute right-2 top-2 bg-red-500">Turun Harga</Badge>}
                <div className="absolute bottom-2 right-2 flex gap-1">
                  <ProductQuickView
                    productId={item.productId}
                    productName={item.name}
                    productPrice={item.price}
                    productImage={item.image}
                    storeName={item.store}
                    storeId={item.storeId}
                    inStock={item.inStock}
                    originalPrice={item.originalPrice}
                    priceDropped={item.priceDropped}
                    description={`Informasi detail produk untuk ${item.name}. Kunjungi halaman produk untuk melihat lebih detail.`}
                  >
                    <Button 
                      size="icon" 
                      variant="secondary" 
                      className="h-8 w-8 rounded-full"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </ProductQuickView>
                  
                  <ShareProduct
                    productId={item.productId}
                    productName={item.name}
                  >
                    <Button 
                      size="icon" 
                      variant="secondary" 
                      className="h-8 w-8 rounded-full"
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </ShareProduct>
                  
                  <DeleteConfirmationDialog
                    title="Hapus dari Wishlist"
                    description={`Apakah Anda yakin ingin menghapus ${item.name} dari wishlist?`}
                    onConfirm={() => removeFromWishlist(item.id)}
                  >
                    <Button 
                      size="icon" 
                      variant="destructive" 
                      className="h-8 w-8 rounded-full"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </DeleteConfirmationDialog>
                </div>
              </div>
              <CardContent className="p-3">
                <div 
                  className="mb-1 text-xs text-muted-foreground cursor-pointer" 
                  onClick={() => router.push(`/store/${item.storeId}`)}
                >
                  {item.store}
                </div>
                <h3 
                  className="line-clamp-1 font-medium cursor-pointer" 
                  onClick={() => viewProductDetail(item.productId)}
                >
                  {item.name}
                </h3>
                <div className="mt-1 flex items-center gap-2">
                  <p className="font-bold">{formatCurrency(item.price)}</p>
                  {item.priceDropped && (
                    <p className="text-xs text-muted-foreground line-through">{formatCurrency(item.originalPrice)}</p>
                  )}
                </div>
                <div className="mt-2 flex gap-2">
                  <Button 
                    className="flex-1" 
                    size="sm" 
                    disabled={!item.inStock}
                    onClick={() => addToCart(item)}
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Tambah ke Keranjang
                  </Button>
                  <CollectionSelector
                    itemId={item.id}
                    size="sm"
                    variant="outline"
                  >
                    <Button 
                      size="sm" 
                      variant="outline"
                      className="px-2"
                    >
                      <FolderPlus className="h-4 w-4" />
                    </Button>
                  </CollectionSelector>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredItems.map((item) => (
            <Card key={item.id}>
              <CardContent className="flex gap-4 p-4">
                <div 
                  className="relative h-24 w-24 flex-shrink-0 cursor-pointer" 
                  onClick={() => viewProductDetail(item.productId)}
                >
                  <img
                    src={item.image || "/placeholder.svg"}
                    alt={item.name}
                    className="h-full w-full rounded-md object-cover"
                  />
                  {!item.inStock && (
                    <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black/60">
                      <Badge variant="outline" className="bg-black text-white">
                        Stok Habis
                      </Badge>
                    </div>
                  )}
                  {item.priceDropped && <Badge className="absolute -right-2 -top-2 bg-red-500">Turun Harga</Badge>}
                </div>
                <div className="flex flex-1 flex-col justify-between">
                  <div>
                    <div 
                      className="text-xs text-muted-foreground cursor-pointer" 
                      onClick={() => router.push(`/store/${item.storeId}`)}
                    >
                      {item.store}
                    </div>
                    <h3 
                      className="font-medium cursor-pointer" 
                      onClick={() => viewProductDetail(item.productId)}
                    >
                      {item.name}
                    </h3>
                    <div className="mt-1 flex items-center gap-2">
                      <p className="font-bold">{formatCurrency(item.price)}</p>
                      {item.priceDropped && (
                        <p className="text-xs text-muted-foreground line-through">
                          {formatCurrency(item.originalPrice)}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Ditambahkan pada {new Date(item.dateAdded).toLocaleDateString("id-ID")}
                  </div>
                </div>
                <div className="flex flex-col items-end justify-between">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <ProductQuickView
                        productId={item.productId}
                        productName={item.name}
                        productPrice={item.price}
                        productImage={item.image}
                        storeName={item.store}
                        storeId={item.storeId}
                        inStock={item.inStock}
                        originalPrice={item.originalPrice}
                        priceDropped={item.priceDropped}
                        description={`Informasi detail produk untuk ${item.name}. Kunjungi halaman produk untuk melihat lebih detail.`}
                      >
                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                          <Eye className="mr-2 h-4 w-4" />
                          Lihat Produk
                        </DropdownMenuItem>
                      </ProductQuickView>
                      
                      <CollectionSelector itemId={item.id}>
                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                          <FolderPlus className="mr-2 h-4 w-4" />
                          Tambah ke Koleksi
                        </DropdownMenuItem>
                      </CollectionSelector>
                      
                      <ShareProduct
                        productId={item.productId}
                        productName={item.name}
                      >
                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                          <Share2 className="mr-2 h-4 w-4" />
                          Bagikan
                        </DropdownMenuItem>
                      </ShareProduct>
                      
                      <DeleteConfirmationDialog
                        title="Hapus dari Wishlist"
                        description={`Apakah Anda yakin ingin menghapus ${item.name} dari wishlist?`}
                        onConfirm={() => removeFromWishlist(item.id)}
                      >
                        <DropdownMenuItem 
                          className="text-red-500"
                          onSelect={(e) => e.preventDefault()}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Hapus
                        </DropdownMenuItem>
                      </DeleteConfirmationDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <Button 
                    className="w-full" 
                    size="sm" 
                    disabled={!item.inStock}
                    onClick={() => addToCart(item)}
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Tambah ke Keranjang
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
