"use client"

import type React from "react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatCurrency } from "@/lib/utils"
import { format } from "date-fns"

export function AffiliateRecentActivity() {
  // Data dummy untuk aktivitas terbaru
  const activities = [
    {
      id: "1",
      type: "click",
      source: "Facebook",
      product: "Sepatu Running Nike Air Zoom",
      date: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 jam yang lalu
      ip: "114.122.xxx.xxx",
      location: "Jakarta, Indonesia",
    },
    {
      id: "2",
      type: "conversion",
      source: "Instagram",
      product: "T-Shirt Adidas Original",
      date: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 jam yang lalu
      amount: 350000,
      commission: 35000,
      status: "pending",
    },
    {
      id: "3",
      type: "commission",
      source: "Direct",
      product: "Jam <PERSON>an <PERSON>-Shock",
      date: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 jam yang lalu
      amount: 1250000,
      commission: 125000,
      status: "approved",
    },
    {
      id: "4",
      type: "payout",
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 hari yang lalu
      amount: 500000,
      method: "Bank Transfer",
      status: "completed",
    },
    {
      id: "5",
      type: "click",
      source: "Twitter",
      product: "Smartphone Samsung Galaxy S21",
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 hari yang lalu
      ip: "182.23.xxx.xxx",
      location: "Surabaya, Indonesia",
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Aktivitas Terbaru</CardTitle>
        <CardDescription>Aktivitas affiliate terbaru dalam 7 hari terakhir</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-4 rounded-md border p-3">
              <div
                className={`mt-0.5 h-9 w-9 rounded-full flex items-center justify-center ${getActivityBgColor(activity.type)}`}
              >
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium leading-none">{getActivityTitle(activity)}</p>
                  <Badge variant={getActivityBadgeVariant(activity)}>{getActivityBadgeText(activity)}</Badge>
                </div>
                <p className="text-sm text-muted-foreground">{getActivityDescription(activity)}</p>
                <p className="text-xs text-muted-foreground">{formatRelativeTime(activity.date)}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

function getActivityBgColor(type: string): string {
  switch (type) {
    case "click":
      return "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300"
    case "conversion":
      return "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300"
    case "commission":
      return "bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300"
    case "payout":
      return "bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300"
    default:
      return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
  }
}

function getActivityIcon(type: string): React.ReactNode {
  switch (type) {
    case "click":
      return "👆"
    case "conversion":
      return "🛒"
    case "commission":
      return "💰"
    case "payout":
      return "💸"
    default:
      return "📊"
  }
}

function getActivityTitle(activity: any): string {
  switch (activity.type) {
    case "click":
      return `Klik pada "${activity.product}"`
    case "conversion":
      return `Konversi untuk "${activity.product}"`
    case "commission":
      return `Komisi dari "${activity.product}"`
    case "payout":
      return `Pembayaran via ${activity.method}`
    default:
      return "Aktivitas"
  }
}

function getActivityDescription(activity: any): string {
  switch (activity.type) {
    case "click":
      return `Sumber: ${activity.source} | Lokasi: ${activity.location}`
    case "conversion":
      return `Nilai Order: ${formatCurrency(activity.amount)} | Komisi: ${formatCurrency(activity.commission)}`
    case "commission":
      return `Nilai Order: ${formatCurrency(activity.amount)} | Komisi: ${formatCurrency(activity.commission)}`
    case "payout":
      return `Jumlah: ${formatCurrency(activity.amount)}`
    default:
      return ""
  }
}

function getActivityBadgeVariant(activity: any): "default" | "secondary" | "destructive" | "outline" {
  if (activity.type === "click") return "secondary"
  if (activity.type === "payout" && activity.status === "completed") return "default"
  if (activity.status === "approved") return "default"
  if (activity.status === "pending") return "outline"
  if (activity.status === "rejected") return "destructive"
  return "secondary"
}

function getActivityBadgeText(activity: any): string {
  if (activity.type === "click") return "Klik"
  if (activity.type === "conversion") return capitalizeFirstLetter(activity.status)
  if (activity.type === "commission") return capitalizeFirstLetter(activity.status)
  if (activity.type === "payout") return capitalizeFirstLetter(activity.status)
  return ""
}

function capitalizeFirstLetter(string: string): string {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return `${diffInSeconds} detik yang lalu`
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes} menit yang lalu`
  }

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours} jam yang lalu`
  }

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) {
    return `${diffInDays} hari yang lalu`
  }

  return format(date, "dd MMM yyyy, HH:mm")
}
