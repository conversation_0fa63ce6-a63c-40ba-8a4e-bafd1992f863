# Filter Tabs Implementation

## Overview
Implementasi fitur tab filter seperti di `docs/facet.html` yang muncul saat melakukan pencarian dan hilang saat mengubah input pencarian (menambah/mengurangi huruf).

## Features Implemented

### 1. Filter Tab Visibility
- **Show on Search**: Tab filter muncul otomatis saat hasil pencarian ditampilkan
- **Hide on Input Change**: Tab filter hilang saat user mengetik atau menghapus huruf di kolom pencarian
- **Dynamic Display**: Tab filter hanya tampil ketika ada hasil pencarian

### 2. Filter Tab Types
- **Terkait**: Sort berdasarkan relevansi (search score) - default
- **Terlaris**: Sort berdasarkan jumlah penjualan (sold count)
- **Terbaru**: Sort berdasarkan ID produk (asumsi ID tinggi = produk baru)
- **Harga**: Sort berdasarkan harga dengan toggle asc/desc (↑/↓)

### 3. Interactive Features
- **Active State**: Tab aktif ditandai dengan warna orange dan border bawah
- **Hover Effect**: Efek hover dengan background color dan color change
- **Price Toggle**: Tab harga menampilkan arrow indicator untuk sort direction
- **Real-time Sorting**: Hasil pencarian diurutkan ulang secara real-time

## Technical Implementation

### State Management
```typescript
const [showFilterTabs, setShowFilterTabs] = useState(false)
const [activeFilterTab, setActiveFilterTab] = useState('terkait')
const [priceSortDirection, setPriceSortDirection] = useState<'asc' | 'desc'>('asc')
```

### Filter Tab Visibility Logic
```typescript
// Show filter tabs when search results are shown
const executeSearch = (searchText?: string) => {
  if (query.trim()) {
    // ... search logic
    setShowFilterTabs(true)
    setActiveFilterTab('terkait') // Reset to default tab
  } else {
    setShowFilterTabs(false) // Hide filter tabs
  }
}

// Hide filter tabs when input changes
const handleSearchChange = (value: string) => {
  if (showFilterTabs) {
    setShowFilterTabs(false)
  }
  // ... rest of search change logic
}
```

### Filter Tab Click Handler
```typescript
const handleFilterTabClick = (filterType: string) => {
  setActiveFilterTab(filterType)
  
  if (filterType === 'harga') {
    // Toggle price sort direction
    setPriceSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
  }
  
  // Apply sorting to current search results
  if (searchResults.length > 0) {
    const sortedResults = sortSearchResults(searchResults, filterType)
    setSearchResults(sortedResults)
  }
}
```

### Sorting Logic
```typescript
const sortSearchResults = (results: Product[], filterType: string): Product[] => {
  const sortedResults = [...results]
  
  switch (filterType) {
    case 'terkait':
      // Sort by relevance score (default)
      return sortedResults.sort((a, b) => (b.searchScore || 0) - (a.searchScore || 0))
    
    case 'terlaris':
      // Sort by sold count
      return sortedResults.sort((a, b) => {
        const aSold = parseInt(a.sold.replace(/\D/g, '')) || 0
        const bSold = parseInt(b.sold.replace(/\D/g, '')) || 0
        return bSold - aSold
      })
    
    case 'terbaru':
      // Sort by product ID (assuming higher ID = newer)
      return sortedResults.sort((a, b) => b.id - a.id)
    
    case 'harga':
      // Sort by price
      return sortedResults.sort((a, b) => {
        const aPrice = parseInt(a.price.replace(/\D/g, '')) || 0
        const bPrice = parseInt(b.price.replace(/\D/g, '')) || 0
        return priceSortDirection === 'asc' ? aPrice - bPrice : bPrice - aPrice
      })
    
    default:
      return sortedResults
  }
}
```

### UI Component
```tsx
{/* Filter Tabs - sesuai docs/facet.html */}
{showFilterTabs && (
  <div className="filter-tabs-container">
    <div className="filter-tabs">
      <button 
        className={`filter-tab ${activeFilterTab === 'terkait' ? 'active' : ''}`}
        onClick={() => handleFilterTabClick('terkait')}
      >
        Terkait
      </button>
      <button 
        className={`filter-tab ${activeFilterTab === 'terlaris' ? 'active' : ''}`}
        onClick={() => handleFilterTabClick('terlaris')}
      >
        Terlaris
      </button>
      <button 
        className={`filter-tab ${activeFilterTab === 'terbaru' ? 'active' : ''}`}
        onClick={() => handleFilterTabClick('terbaru')}
      >
        Terbaru
      </button>
      <button 
        className={`filter-tab ${activeFilterTab === 'harga' ? 'active' : ''}`}
        onClick={() => handleFilterTabClick('harga')}
      >
        Harga {priceSortDirection === 'asc' ? '↑' : '↓'}
      </button>
    </div>
  </div>
)}
```

## CSS Styling

### Filter Tabs Container
```css
.filter-tabs-container {
  margin: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  gap: 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.filter-tabs::-webkit-scrollbar {
  display: none;
}
```

### Filter Tab Styling
```css
.filter-tab {
  background: none;
  border: none;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
  position: relative;
}

.filter-tab:hover {
  color: #ff6b35;
  background-color: #fff5f2;
}

.filter-tab.active {
  color: #ff6b35;
  border-bottom-color: #ff6b35;
  font-weight: 600;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #ff6b35;
  border-radius: 1px 1px 0 0;
}
```

## User Flow

### 1. Search Trigger
1. User melakukan pencarian (via Enter, search icon, atau auto-search)
2. Hasil pencarian ditampilkan
3. Filter tabs muncul otomatis dengan tab "Terkait" aktif
4. Produk diurutkan berdasarkan relevansi (default)

### 2. Filter Selection
1. User klik salah satu tab filter
2. Tab yang diklik menjadi aktif (visual feedback)
3. Hasil pencarian diurutkan ulang sesuai filter yang dipilih
4. Untuk tab "Harga", direction toggle (asc/desc) dengan arrow indicator

### 3. Input Change
1. User mengetik atau menghapus huruf di kolom pencarian
2. Filter tabs hilang otomatis
3. Mode kembali ke prediction/suggestion
4. Filter tabs akan muncul lagi saat pencarian baru dilakukan

## Filter Types Detail

### 1. Terkait (Relevance)
- **Logic**: Sort berdasarkan `searchScore` dari enhanced search
- **Order**: Score tertinggi ke terendah
- **Use Case**: Menampilkan produk paling relevan dengan query pencarian

### 2. Terlaris (Best Selling)
- **Logic**: Parse angka dari field `sold` (contoh: "2rb+" → 2000)
- **Order**: Penjualan tertinggi ke terendah
- **Use Case**: Menampilkan produk dengan penjualan terbanyak

### 3. Terbaru (Newest)
- **Logic**: Sort berdasarkan `id` produk (asumsi ID tinggi = produk baru)
- **Order**: ID tertinggi ke terendah
- **Use Case**: Menampilkan produk terbaru yang ditambahkan

### 4. Harga (Price)
- **Logic**: Parse angka dari field `price` (contoh: "Rp 89.000" → 89000)
- **Order**: Toggle antara ascending (↑) dan descending (↓)
- **Use Case**: Mengurutkan produk berdasarkan harga (murah ke mahal atau sebaliknya)

## Benefits

### 1. User Experience
- **Intuitive**: Tab filter muncul saat dibutuhkan, hilang saat tidak relevan
- **Responsive**: Real-time sorting tanpa reload
- **Visual Feedback**: Clear active state dan hover effects
- **Flexible**: Multiple sorting options sesuai kebutuhan user

### 2. Search Enhancement
- **Contextual**: Filter hanya muncul saat ada hasil pencarian
- **Smart Reset**: Kembali ke default "Terkait" setiap pencarian baru
- **Dynamic**: Filter tabs hilang saat user mengubah query

### 3. Performance
- **Client-side**: Sorting dilakukan di client tanpa server call
- **Efficient**: Reuse existing search results untuk sorting
- **Smooth**: Transition animations untuk better UX

## Testing Scenarios

### 1. Filter Tab Visibility
- ✅ Tabs muncul saat melakukan pencarian
- ✅ Tabs hilang saat mengetik/menghapus huruf
- ✅ Tabs muncul lagi saat pencarian baru

### 2. Filter Functionality
- ✅ Tab "Terkait" mengurutkan berdasarkan relevance score
- ✅ Tab "Terlaris" mengurutkan berdasarkan sold count
- ✅ Tab "Terbaru" mengurutkan berdasarkan product ID
- ✅ Tab "Harga" toggle antara asc/desc dengan arrow indicator

### 3. Visual States
- ✅ Active tab memiliki styling berbeda (orange color, border)
- ✅ Hover effect pada semua tabs
- ✅ Price direction indicator (↑/↓) berfungsi

## Future Enhancements

1. **Advanced Filters**: Category, brand, price range filters
2. **Filter Persistence**: Remember user's preferred filter
3. **Filter Analytics**: Track most used filters
4. **Custom Sorting**: Allow user-defined sorting criteria
5. **Filter Combinations**: Multiple filters simultaneously
6. **Mobile Optimization**: Swipe gestures for filter tabs

## Files Modified

- `app/sellzio/page.tsx`: Filter tabs logic and state management
- `components/themes/sellzio/sellzio-styles.css`: Filter tabs styling
- `docs/filter-tabs-implementation.md`: Documentation

## Dependencies

- React hooks (useState)
- Existing search results system
- CSS transitions and animations
