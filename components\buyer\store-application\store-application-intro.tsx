"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Check, ArrowRight } from "lucide-react"
import Image from "next/image"

export function StoreApplicationIntro() {
  const [isStarted, setIsStarted] = useState(false)

  // Jika aplikasi sudah dimulai, komponen ini tidak perlu ditampilkan
  if (isStarted) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* Hero Banner */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-primary/90 to-primary">
        <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,#fff,rgba(255,255,255,0.6))] dark:bg-grid-white/5"></div>
        <div className="relative grid grid-cols-1 md:grid-cols-2 gap-6 p-8">
          <div className="space-y-4">
            <h2 className="text-3xl font-bold text-primary-foreground"><PERSON><PERSON></h2>
            <p className="text-primary-foreground/90 text-lg">
              Jadilah bagian dari ekosistem marketplace terpercaya dan mulai mendapatkan penghasilan tambahan
            </p>
            <div className="flex flex-wrap gap-2 pt-2">
              {["Gratis", "Mudah", "Cepat", "Aman"].map((tag) => (
                <span
                  key={tag}
                  className="bg-primary-foreground/20 text-primary-foreground px-3 py-1 rounded-full text-sm"
                >
                  {tag}
                </span>
              ))}
            </div>
            <Button
              size="lg"
              onClick={() => setIsStarted(true)}
              className="mt-4 bg-primary-foreground text-primary hover:bg-primary-foreground/90"
            >
              Mulai Aplikasi Sekarang <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
          <div className="hidden md:flex items-center justify-center">
            <div className="relative w-64 h-64">
              <Image
                src="/online-store.png"
                alt="Online Store"
                fill
                className="object-contain dark:brightness-110 dark:contrast-125"
                priority
              />
            </div>
          </div>
        </div>
      </div>

      {/* Benefits & Info Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Benefits Section */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold mb-4">Keuntungan Menjadi Penjual</h3>
            <ul className="space-y-3">
              {benefits.map((benefit, index) => (
                <li key={index} className="flex gap-2">
                  <Check className="h-5 w-5 text-green-500 dark:text-green-400 flex-shrink-0 mt-0.5" />
                  <span className="text-muted-foreground">{benefit}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Success Stories */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold mb-4">Kisah Sukses</h3>
            <div className="space-y-4">
              {successStories.map((story, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0 border border-border">
                    <Image
                      src={story.image || "/placeholder.svg"}
                      alt={story.name}
                      fill
                      className="object-cover dark:brightness-90"
                    />
                  </div>
                  <div>
                    <p className="font-medium">{story.name}</p>
                    <p className="text-sm text-muted-foreground">{story.story}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Earning Potential & CTA */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold mb-4">Potensi Penghasilan</h3>
            <div className="mb-4">
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">Fashion</span>
                <span className="text-sm font-medium">hingga 40jt/bulan</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div className="bg-green-500 dark:bg-green-400 h-2 rounded-full" style={{ width: "90%" }}></div>
              </div>
            </div>
            <div className="mb-4">
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">Elektronik</span>
                <span className="text-sm font-medium">hingga 75jt/bulan</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div className="bg-blue-500 dark:bg-blue-400 h-2 rounded-full" style={{ width: "95%" }}></div>
              </div>
            </div>
            <div className="mb-4">
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">Makanan</span>
                <span className="text-sm font-medium">hingga 25jt/bulan</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div className="bg-yellow-500 dark:bg-yellow-400 h-2 rounded-full" style={{ width: "70%" }}></div>
              </div>
            </div>
            <div className="mb-6 p-3 bg-muted/50 rounded-lg">
              <h4 className="font-medium mb-2">Komisi Platform</h4>
              <p className="text-sm text-muted-foreground mb-1">• 5% untuk transaksi hingga 10jt</p>
              <p className="text-sm text-muted-foreground mb-1">• 4% untuk transaksi 10jt - 50jt</p>
              <p className="text-sm text-muted-foreground">• 3% untuk transaksi di atas 50jt</p>
            </div>
            <Button className="w-full" size="lg" onClick={() => setIsStarted(true)}>
              Mulai Aplikasi Sekarang
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

const benefits = [
  "Akses ke jutaan pelanggan potensial",
  "Dashboard analitik lengkap",
  "Dukungan pembayaran online terintegrasi",
  "Alat marketing dan promosi gratis",
  "Dukungan layanan pelanggan 24/7",
  "Biaya komisi kompetitif",
  "Pembayaran cepat ke rekening Anda",
]

const successStories = [
  {
    name: "Toko Fashion Nusantara",
    image: "/brand-logo-1.png",
    story: "Penjualan meningkat 300% dalam 6 bulan pertama bergabung dengan platform.",
  },
  {
    name: "Elektronik Handal",
    image: "/brand-logo-2.png",
    story: "Dari toko kecil menjadi salah satu penjual elektronik terpercaya dengan 1,000+ transaksi per bulan.",
  },
  {
    name: "Kuliner Mama",
    image: "/brand-logo-3.png",
    story: "Berhasil memperluas pasar dari lokal ke nasional dengan sistem pengiriman terintegrasi.",
  },
]
