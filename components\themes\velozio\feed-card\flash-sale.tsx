"use client"

import { useEffect, useState } from "react"

interface FlashSaleProps {
  endTime: Date
  originalPrice: string
  currentPrice: string
  remaining: number
  total: number
}

export const FlashSale = ({ endTime, originalPrice, currentPrice, remaining = 0, total = 100 }: FlashSaleProps) => {
  const [timeLeft, setTimeLeft] = useState<{
    hours: number
    minutes: number
    seconds: number
  }>({
    hours: 0,
    minutes: 0,
    seconds: 0,
  })

  useEffect(() => {
    const calculateTimeLeft = () => {
      if (!endTime || !(endTime instanceof Date) || isNaN(endTime.getTime())) {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 })
        return
      }

      const difference = endTime.getTime() - new Date().getTime()

      if (difference > 0) {
        setTimeLeft({
          hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
          minutes: Math.floor((difference / 1000 / 60) % 60),
          seconds: Math.floor((difference / 1000) % 60),
        })
      } else {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 })
      }
    }

    calculateTimeLeft()
    const timer = setInterval(calculateTimeLeft, 1000)

    return () => clearInterval(timer)
  }, [endTime])

  // Ensure remaining and total are valid numbers
  const safeRemaining = typeof remaining === "number" && !isNaN(remaining) ? remaining : 0
  const safeTotal = typeof total === "number" && !isNaN(total) && total > 0 ? total : 100

  // Calculate percentage with safety checks
  const percentRemaining = (safeRemaining / safeTotal) * 100

  return (
    <div className="bg-[#fff0f0] border border-[#ffccc7] rounded-sm p-1 mb-2">
      <div className="flex justify-between items-center mb-1">
        <div className="flex flex-col">
          <div className="flex items-center text-[10px] font-bold text-[#ee4d2d]">
            <span className="mr-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
              </svg>
            </span>
            FLASH SALE
          </div>
          <div className="flex items-center text-[10px] mt-0.5">
            <span className="text-[#ee4d2d] font-bold">{currentPrice}</span>
            <span className="ml-1 text-gray-400 line-through text-[8px]">{originalPrice}</span>
          </div>
        </div>
        <div className="flex flex-col items-end">
          <div className="text-[10px] font-bold text-[#ee4d2d]">
            {String(timeLeft.hours).padStart(2, "0")}:{String(timeLeft.minutes).padStart(2, "0")}:
            {String(timeLeft.seconds).padStart(2, "0")}
          </div>
          <div className="text-[8px] text-[#ee4d2d] mt-0.5">Sisa {safeRemaining}</div>
        </div>
      </div>
      <div className="h-1 bg-[#ffe8e6] rounded-full overflow-hidden">
        <div
          className="h-full bg-gradient-to-r from-[#ee4d2d] to-[#ff7337] animate-shimmer"
          style={{ width: `${percentRemaining}%` }}
        ></div>
      </div>
    </div>
  )
}
