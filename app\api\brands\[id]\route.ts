import { NextRequest, NextResponse } from 'next/server';
import { brandService } from '@/lib/services/brands';

// GET - Mendapatkan brand berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    const brand = await brandService.getBrand(id);
    
    if (!brand) {
      return NextResponse.json(
        { error: 'Brand not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(brand);
  } catch (error) {
    console.error('Error fetching brand:', error);
    return NextResponse.json(
      { error: 'Failed to fetch brand' },
      { status: 500 }
    );
  }
}

// PUT - Update brand berdasarkan ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    
    const updatedBrand = await brandService.updateBrand(id, body);
    
    return NextResponse.json(updatedBrand);
  } catch (error) {
    console.error('Error updating brand:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update brand';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// DELETE - Hapus brand berdasarkan ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    await brandService.deleteBrand(id);
    
    return NextResponse.json({ 
      message: 'Brand deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting brand:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to delete brand';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// PATCH - Toggle status atau operasi khusus lainnya
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { action } = body;
    
    let updatedBrand;
    
    switch (action) {
      case 'toggle_status':
        updatedBrand = await brandService.toggleBrandStatus(id);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: toggle_status' },
          { status: 400 }
        );
    }
    
    return NextResponse.json(updatedBrand);
  } catch (error) {
    console.error('Error updating brand status:', error);
    return NextResponse.json(
      { error: 'Failed to update brand status' },
      { status: 500 }
    );
  }
}
