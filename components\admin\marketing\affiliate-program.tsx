"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DateRangePicker } from "@/components/analytics/date-range-picker"
import { AnalyticsPieChart } from "@/components/analytics/analytics-pie-chart"
import { AnalyticsTimeSeriesChart } from "@/components/analytics/analytics-time-series-chart"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Users,
  DollarSign,
  TrendingUp,
  BarChart3,
  ImageIcon,
  Mail,
  ChevronRight,
  RefreshCw,
  Plus,
  Edit,
  Eye,
  Download,
  Trash,
  FileText,
  Video,
  Link,
} from "lucide-react"

// Mock data for affiliate program
const affiliateData = {
  metrics: [
    {
      title: "Total Affiliates",
      value: "1,248",
      change: "+12%",
      trend: "up",
    },
    {
      title: "Conversion Rate",
      value: "3.8%",
      change: "+0.5%",
      trend: "up",
    },
    {
      title: "Revenue Generated",
      value: "$128,450",
      change: "+18%",
      trend: "up",
    },
    {
      title: "Avg. Commission",
      value: "$42.50",
      change: "+5%",
      trend: "up",
    },
  ],
  revenueData: [
    { date: "Jan", revenue: 8450 },
    { date: "Feb", revenue: 9200 },
    { date: "Mar", revenue: 10800 },
    { date: "Apr", revenue: 9600 },
    { date: "May", revenue: 12400 },
    { date: "Jun", revenue: 14200 },
    { date: "Jul", revenue: 15800 },
    { date: "Aug", revenue: 14500 },
    { date: "Sep", revenue: 16200 },
    { date: "Oct", revenue: 17500 },
    { date: "Nov", revenue: 0 },
    { date: "Dec", revenue: 0 },
  ],
  conversionData: [
    { date: "Jan", rate: 3.2 },
    { date: "Feb", rate: 3.3 },
    { date: "Mar", rate: 3.5 },
    { date: "Apr", rate: 3.4 },
    { date: "May", rate: 3.6 },
    { date: "Jun", rate: 3.7 },
    { date: "Jul", rate: 3.8 },
    { date: "Aug", rate: 3.7 },
    { date: "Sep", rate: 3.9 },
    { date: "Oct", rate: 4.0 },
    { date: "Nov", rate: 0 },
    { date: "Dec", rate: 0 },
  ],
  referralSourceData: [
    { name: "Social Media", value: 42 },
    { name: "Blogs", value: 28 },
    { name: "Email", value: 18 },
    { name: "Direct", value: 12 },
  ],
  topAffiliates: [
    { name: "Sarah Johnson", referrals: 128, revenue: 12450, commission: 3112.5, conversionRate: 4.2 },
    { name: "Michael Chen", referrals: 105, revenue: 9850, commission: 2462.5, conversionRate: 3.8 },
    { name: "Jessica Williams", referrals: 92, revenue: 8750, commission: 2187.5, conversionRate: 3.5 },
    { name: "David Rodriguez", referrals: 87, revenue: 7950, commission: 1987.5, conversionRate: 3.9 },
    { name: "Emma Thompson", referrals: 76, revenue: 6850, commission: 1712.5, conversionRate: 3.6 },
  ],
  programSettings: {
    enabled: true,
    commissionType: "percentage",
    defaultCommission: 25,
    cookieDuration: 30,
    minimumPayout: 50,
    payoutSchedule: "monthly",
    doubleCommission: true,
    requireApproval: true,
    allowedCountries: ["United States", "Canada", "United Kingdom", "Australia", "Germany", "France"],
  },
  commissionTiers: [
    { name: "Standard", minSales: 0, maxSales: 9, rate: 15 },
    { name: "Bronze", minSales: 10, maxSales: 24, rate: 20 },
    { name: "Silver", minSales: 25, maxSales: 49, rate: 25 },
    { name: "Gold", minSales: 50, maxSales: 99, rate: 30 },
    { name: "Platinum", minSales: 100, maxSales: null, rate: 35 },
  ],
  marketingMaterials: [
    { name: "Product Banner Set", type: "Image", size: "Multiple sizes", downloads: 458 },
    { name: "Email Templates", type: "HTML", size: "3 variations", downloads: 312 },
    { name: "Social Media Kit", type: "Mixed", size: "15 assets", downloads: 287 },
    { name: "Promotional Video", type: "Video", size: "1080p, 30s", downloads: 195 },
    { name: "Landing Page Templates", type: "HTML/CSS", size: "5 designs", downloads: 176 },
  ],
  emailTemplates: [
    { name: "Welcome Email", subject: "Welcome to Our Affiliate Program!" },
    { name: "Commission Notification", subject: "You've Earned a Commission!" },
    { name: "Monthly Report", subject: "Your Monthly Affiliate Performance Report" },
    { name: "Payout Confirmation", subject: "Your Affiliate Payout Has Been Processed" },
    { name: "New Promotion", subject: "New Promotion - Earn Double Commissions!" },
  ],
}

export function AffiliateProgram() {
  const [dateRange, setDateRange] = useState({ from: new Date(2023, 0, 1), to: new Date() })
  const [activeTab, setActiveTab] = useState("overview")
  const [commissionRate, setCommissionRate] = useState(affiliateData.programSettings.defaultCommission)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Affiliate Program</h2>
        <div className="flex items-center gap-2">
          <DateRangePicker dateRange={dateRange} onChange={setDateRange} />
          <Button>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Data
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="affiliates">Affiliates</TabsTrigger>
          <TabsTrigger value="settings">Program Settings</TabsTrigger>
          <TabsTrigger value="materials">Marketing Materials</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {affiliateData.metrics.map((metric, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
                  {index === 0 ? (
                    <Users className="h-4 w-4 text-muted-foreground" />
                  ) : index === 1 ? (
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  ) : index === 2 ? (
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  )}
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metric.value}</div>
                  <p className="text-xs text-muted-foreground">
                    <span className={metric.trend === "up" ? "text-green-500" : "text-red-500"}>{metric.change}</span>{" "}
                    from previous period
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Affiliate Revenue</CardTitle>
                <CardDescription>Monthly revenue generated through affiliates</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <AnalyticsTimeSeriesChart
                  data={affiliateData.revenueData}
                  categories={["revenue"]}
                  index="date"
                  valueFormatter={(value) => `$${value.toLocaleString()}`}
                  className="h-full"
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Referral Sources</CardTitle>
                <CardDescription>Where affiliate traffic is coming from</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <AnalyticsPieChart
                  data={affiliateData.referralSourceData}
                  category="value"
                  index="name"
                  valueFormatter={(value) => `${value}%`}
                  className="h-full"
                />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Top Performing Affiliates</CardTitle>
              <CardDescription>Affiliates with the highest revenue generation</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Affiliate</TableHead>
                    <TableHead>Referrals</TableHead>
                    <TableHead>Revenue Generated</TableHead>
                    <TableHead>Commission Earned</TableHead>
                    <TableHead>Conversion Rate</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {affiliateData.topAffiliates.map((affiliate, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">{affiliate.name}</TableCell>
                      <TableCell>{affiliate.referrals}</TableCell>
                      <TableCell>${affiliate.revenue.toLocaleString()}</TableCell>
                      <TableCell>${affiliate.commission.toLocaleString()}</TableCell>
                      <TableCell>{affiliate.conversionRate}%</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className="mt-4 flex justify-end">
                <Button variant="outline">
                  View All Affiliates
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common affiliate program tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <Users className="mr-2 h-4 w-4" />
                  Approve Pending Affiliates
                  <Badge className="ml-auto">12</Badge>
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <DollarSign className="mr-2 h-4 w-4" />
                  Process Pending Payouts
                  <Badge className="ml-auto">8</Badge>
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Mail className="mr-2 h-4 w-4" />
                  Send Newsletter to Affiliates
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <ImageIcon className="mr-2 h-4 w-4" />
                  Upload New Marketing Materials
                </Button>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Program Health</CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Active Affiliates</span>
                    <span className="text-sm font-medium">78%</span>
                  </div>
                  <div className="h-2 rounded-full bg-muted">
                    <div className="h-2 rounded-full bg-green-500" style={{ width: "78%" }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Conversion Rate</span>
                    <span className="text-sm font-medium">3.8%</span>
                  </div>
                  <div className="h-2 rounded-full bg-muted">
                    <div className="h-2 rounded-full bg-blue-500" style={{ width: "38%" }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Click-through Rate</span>
                    <span className="text-sm font-medium">5.2%</span>
                  </div>
                  <div className="h-2 rounded-full bg-muted">
                    <div className="h-2 rounded-full bg-amber-500" style={{ width: "52%" }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Affiliate Satisfaction</span>
                    <span className="text-sm font-medium">92%</span>
                  </div>
                  <div className="h-2 rounded-full bg-muted">
                    <div className="h-2 rounded-full bg-green-500" style={{ width: "92%" }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Affiliates Tab */}
        <TabsContent value="affiliates" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Affiliate Directory</CardTitle>
                  <CardDescription>Manage your affiliate partners</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Input placeholder="Search affiliates..." className="w-[250px]" />
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Affiliate
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Tier</TableHead>
                    <TableHead>Joined</TableHead>
                    <TableHead>Earnings</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Sarah Johnson</TableCell>
                    <TableCell><EMAIL></TableCell>
                    <TableCell>
                      <Badge variant="outline" className="border-green-500 text-green-500">
                        Active
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge>Gold</Badge>
                    </TableCell>
                    <TableCell>Jan 15, 2023</TableCell>
                    <TableCell>$3,112.50</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Michael Chen</TableCell>
                    <TableCell><EMAIL></TableCell>
                    <TableCell>
                      <Badge variant="outline" className="border-green-500 text-green-500">
                        Active
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge>Silver</Badge>
                    </TableCell>
                    <TableCell>Feb 3, 2023</TableCell>
                    <TableCell>$2,462.50</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Jessica Williams</TableCell>
                    <TableCell><EMAIL></TableCell>
                    <TableCell>
                      <Badge variant="outline" className="border-green-500 text-green-500">
                        Active
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge>Silver</Badge>
                    </TableCell>
                    <TableCell>Mar 12, 2023</TableCell>
                    <TableCell>$2,187.50</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">David Rodriguez</TableCell>
                    <TableCell><EMAIL></TableCell>
                    <TableCell>
                      <Badge variant="outline" className="border-amber-500 text-amber-500">
                        Pending
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge>Bronze</Badge>
                    </TableCell>
                    <TableCell>Apr 8, 2023</TableCell>
                    <TableCell>$1,987.50</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
              <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-muted-foreground">Showing 4 of 1,248 affiliates</div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" disabled>
                    Previous
                  </Button>
                  <Button variant="outline" size="sm">
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Program Settings Tab */}
        <TabsContent value="settings" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Program Settings</CardTitle>
                <CardDescription>Configure your affiliate program settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between space-y-2">
                  <div>
                    <Label htmlFor="program-status">Program Status</Label>
                    <p className="text-sm text-muted-foreground">Enable or disable the affiliate program</p>
                  </div>
                  <Switch id="program-status" checked={affiliateData.programSettings.enabled} />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="commission-type">Commission Type</Label>
                  <Select defaultValue={affiliateData.programSettings.commissionType}>
                    <SelectTrigger id="commission-type">
                      <SelectValue placeholder="Select commission type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="percentage">Percentage of Sale</SelectItem>
                      <SelectItem value="fixed">Fixed Amount</SelectItem>
                      <SelectItem value="hybrid">Hybrid (% + Fixed)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default-commission">Default Commission Rate (%)</Label>
                  <Input
                    id="default-commission"
                    type="number"
                    value={commissionRate}
                    onChange={(e) => setCommissionRate(Number(e.target.value))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cookie-duration">Cookie Duration (Days)</Label>
                  <Input
                    id="cookie-duration"
                    type="number"
                    defaultValue={affiliateData.programSettings.cookieDuration}
                  />
                  <p className="text-xs text-muted-foreground">
                    How long the affiliate cookie will last after a visitor clicks an affiliate link
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minimum-payout">Minimum Payout Amount ($)</Label>
                  <Input id="minimum-payout" type="number" defaultValue={affiliateData.programSettings.minimumPayout} />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="payout-schedule">Payout Schedule</Label>
                  <Select defaultValue={affiliateData.programSettings.payoutSchedule}>
                    <SelectTrigger id="payout-schedule">
                      <SelectValue placeholder="Select payout schedule" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="biweekly">Bi-weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="require-approval" checked={affiliateData.programSettings.requireApproval} />
                  <Label htmlFor="require-approval">Require manual approval for new affiliates</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="double-commission" checked={affiliateData.programSettings.doubleCommission} />
                  <Label htmlFor="double-commission">Allow double commission for returning customers</Label>
                </div>

                <Button className="w-full">Save Settings</Button>
              </CardContent>
            </Card>

            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Commission Tiers</CardTitle>
                  <CardDescription>Configure performance-based commission rates</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Tier Name</TableHead>
                        <TableHead>Min Sales</TableHead>
                        <TableHead>Max Sales</TableHead>
                        <TableHead>Commission Rate</TableHead>
                        <TableHead></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {affiliateData.commissionTiers.map((tier, i) => (
                        <TableRow key={i}>
                          <TableCell className="font-medium">{tier.name}</TableCell>
                          <TableCell>{tier.minSales}</TableCell>
                          <TableCell>{tier.maxSales === null ? "∞" : tier.maxSales}</TableCell>
                          <TableCell>{tier.rate}%</TableCell>
                          <TableCell>
                            <Button variant="ghost" size="icon">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  <div className="mt-4">
                    <Button variant="outline" size="sm">
                      <Plus className="mr-2 h-4 w-4" />
                      Add Tier
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Email Templates</CardTitle>
                  <CardDescription>Customize affiliate communication emails</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {affiliateData.emailTemplates.map((template, i) => (
                      <div key={i} className="flex items-center justify-between rounded-lg border p-3">
                        <div>
                          <h4 className="font-medium">{template.name}</h4>
                          <p className="text-sm text-muted-foreground">Subject: {template.subject}</p>
                        </div>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Marketing Materials Tab */}
        <TabsContent value="materials" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Marketing Materials</CardTitle>
                  <CardDescription>Resources for your affiliates to promote your platform</CardDescription>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Upload New Material
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Size/Format</TableHead>
                    <TableHead>Downloads</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {affiliateData.marketingMaterials.map((material, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">{material.name}</TableCell>
                      <TableCell>
                        {material.type === "Image" ? (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <ImageIcon className="h-3 w-3" />
                            Image
                          </Badge>
                        ) : material.type === "HTML" ? (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            HTML
                          </Badge>
                        ) : material.type === "Video" ? (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Video className="h-3 w-3" />
                            Video
                          </Badge>
                        ) : (
                          <Badge variant="outline">{material.type}</Badge>
                        )}
                      </TableCell>
                      <TableCell>{material.size}</TableCell>
                      <TableCell>{material.downloads}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Link className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Affiliate Links</CardTitle>
                <CardDescription>Configure and customize affiliate link formats</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="link-structure">Link Structure</Label>
                  <Select defaultValue="query">
                    <SelectTrigger id="link-structure">
                      <SelectValue placeholder="Select link structure" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="query">Query Parameter (?ref=)</SelectItem>
                      <SelectItem value="path">Path Based (/ref/)</SelectItem>
                      <SelectItem value="subdomain">Subdomain Based</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="parameter-name">Parameter Name</Label>
                  <Input id="parameter-name" defaultValue="ref" />
                  <p className="text-xs text-muted-foreground">
                    The URL parameter used to identify affiliates (e.g., ?ref=affiliate_id)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="example-link">Example Link</Label>
                  <div className="flex items-center rounded-md border bg-muted px-3 py-2 text-sm">
                    https://yourdomain.com/product?ref=affiliate123
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="utm-parameters" defaultChecked />
                  <Label htmlFor="utm-parameters">Add UTM parameters to affiliate links</Label>
                </div>

                <Button className="w-full">Save Link Settings</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tracking Pixel</CardTitle>
                <CardDescription>Provide tracking pixel for advanced affiliate tracking</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="tracking-code">Tracking Pixel Code</Label>
                  <Textarea
                    id="tracking-code"
                    className="font-mono text-xs"
                    rows={6}
                    defaultValue={`<script>
  (function(a,f,p){
    var s = document.createElement('script');
    s.src = 'https://track.yourdomain.com/pixel.js';
    s.async = true;
    document.head.appendChild(s);
  })(window,document,'affiliate');
</script>`}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Installation Instructions</Label>
                  <div className="rounded-md bg-muted p-3 text-sm">
                    <p>1. Copy the tracking pixel code above</p>
                    <p>2. Paste it before the closing &lt;/head&gt; tag on your website</p>
                    <p>3. The pixel will automatically track affiliate referrals</p>
                  </div>
                </div>

                <Button variant="outline" className="w-full">
                  <Download className="mr-2 h-4 w-4" />
                  Download Tracking Documentation
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trends</CardTitle>
                <CardDescription>Affiliate-generated revenue over time</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <AnalyticsTimeSeriesChart
                  data={affiliateData.revenueData}
                  categories={["revenue"]}
                  index="date"
                  valueFormatter={(value) => `$${value.toLocaleString()}`}
                  className="h-full"
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Conversion Rate</CardTitle>
                <CardDescription>Affiliate traffic conversion rate over time</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <AnalyticsTimeSeriesChart
                  data={affiliateData.conversionData}
                  categories={["rate"]}
                  index="date"
                  valueFormatter={(value) => `${value}%`}
                  className="h-full"
                />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Performance by Affiliate Tier</CardTitle>
              <CardDescription>Revenue and conversion metrics by affiliate tier</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tier</TableHead>
                    <TableHead>Affiliates</TableHead>
                    <TableHead>Clicks</TableHead>
                    <TableHead>Conversions</TableHead>
                    <TableHead>Conversion Rate</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>Commissions Paid</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Platinum</TableCell>
                    <TableCell>24</TableCell>
                    <TableCell>48,250</TableCell>
                    <TableCell>2,412</TableCell>
                    <TableCell>5.0%</TableCell>
                    <TableCell>$48,240</TableCell>
                    <TableCell>$16,884</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Gold</TableCell>
                    <TableCell>68</TableCell>
                    <TableCell>42,180</TableCell>
                    <TableCell>1,687</TableCell>
                    <TableCell>4.0%</TableCell>
                    <TableCell>$33,740</TableCell>
                    <TableCell>$10,122</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Silver</TableCell>
                    <TableCell>156</TableCell>
                    <TableCell>31,450</TableCell>
                    <TableCell>1,101</TableCell>
                    <TableCell>3.5%</TableCell>
                    <TableCell>$22,020</TableCell>
                    <TableCell>$5,505</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Bronze</TableCell>
                    <TableCell>342</TableCell>
                    <TableCell>25,680</TableCell>
                    <TableCell>770</TableCell>
                    <TableCell>3.0%</TableCell>
                    <TableCell>$15,400</TableCell>
                    <TableCell>$3,080</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Standard</TableCell>
                    <TableCell>658</TableCell>
                    <TableCell>19,240</TableCell>
                    <TableCell>577</TableCell>
                    <TableCell>3.0%</TableCell>
                    <TableCell>$9,050</TableCell>
                    <TableCell>$1,358</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Top Traffic Sources</CardTitle>
                <CardDescription>Where affiliate traffic is coming from</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">Social Media</div>
                      <div className="text-xs text-muted-foreground">42% of traffic</div>
                    </div>
                    <div className="font-medium">20,160 visits</div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">Blogs & Content</div>
                      <div className="text-xs text-muted-foreground">28% of traffic</div>
                    </div>
                    <div className="font-medium">13,440 visits</div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">Email Marketing</div>
                      <div className="text-xs text-muted-foreground">18% of traffic</div>
                    </div>
                    <div className="font-medium">8,640 visits</div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">Direct Links</div>
                      <div className="text-xs text-muted-foreground">12% of traffic</div>
                    </div>
                    <div className="font-medium">5,760 visits</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Performing Content</CardTitle>
                <CardDescription>Most effective affiliate content</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">Product Review Videos</div>
                      <div className="text-xs text-muted-foreground">6.2% conversion rate</div>
                    </div>
                    <div className="font-medium">$18,450</div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">Comparison Articles</div>
                      <div className="text-xs text-muted-foreground">5.8% conversion rate</div>
                    </div>
                    <div className="font-medium">$15,280</div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">Tutorial Content</div>
                      <div className="text-xs text-muted-foreground">4.9% conversion rate</div>
                    </div>
                    <div className="font-medium">$12,850</div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">Social Media Posts</div>
                      <div className="text-xs text-muted-foreground">3.5% conversion rate</div>
                    </div>
                    <div className="font-medium">$9,240</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Geographic Performance</CardTitle>
                <CardDescription>Top performing regions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">United States</div>
                      <div className="text-xs text-muted-foreground">42% of revenue</div>
                    </div>
                    <div className="font-medium">$53,950</div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">United Kingdom</div>
                      <div className="text-xs text-muted-foreground">18% of revenue</div>
                    </div>
                    <div className="font-medium">$23,120</div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">Canada</div>
                      <div className="text-xs text-muted-foreground">12% of revenue</div>
                    </div>
                    <div className="font-medium">$15,410</div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <div className="text-sm font-medium">Australia</div>
                      <div className="text-xs text-muted-foreground">10% of revenue</div>
                    </div>
                    <div className="font-medium">$12,845</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
