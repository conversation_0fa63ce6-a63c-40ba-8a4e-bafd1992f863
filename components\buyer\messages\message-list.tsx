"use client"

import { useState } from "react"
import { Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

// Tipe data untuk percakapan
interface Conversation {
  id: string
  name: string
  avatar: string
  lastMessage: string
  timestamp: string
  unread: number
  isOnline: boolean
  isStore: boolean
}

// Data dummy untuk percakapan
const dummyConversations: Conversation[] = [
  {
    id: "1",
    name: "Toko Elektronik Jaya",
    avatar: "/brand-logo-1.png",
    lastMessage: "Barang sudah dikirim, estimasi sampai besok",
    timestamp: "10:30",
    unread: 2,
    isOnline: true,
    isStore: true,
  },
  {
    id: "2",
    name: "Fashion Trendy",
    avatar: "/brand-logo-2.png",
    lastMessage: "Terima kasih atas pesanan Anda!",
    timestamp: "Kemarin",
    unread: 0,
    isOnline: false,
    isStore: true,
  },
  {
    id: "3",
    name: "<PERSON><PERSON>ll<PERSON>",
    avatar: "/your-logo.png",
    lastMessage: "Bagaimana pengalaman berbelanja Anda?",
    timestamp: "Kemarin",
    unread: 1,
    isOnline: true,
    isStore: false,
  },
  {
    id: "4",
    name: "Toko Furniture Modern",
    avatar: "/brand-logo-3.png",
    lastMessage: "Apakah ada pertanyaan tentang produk kami?",
    timestamp: "3 hari lalu",
    unread: 0,
    isOnline: false,
    isStore: true,
  },
  {
    id: "5",
    name: "Toko Buku Cerdas",
    avatar: "/brand-logo-4.png",
    lastMessage: "Buku yang Anda pesan sudah tersedia",
    timestamp: "5 hari lalu",
    unread: 0,
    isOnline: true,
    isStore: true,
  },
]

interface MessageListProps {
  activeConversation: string | null
  setActiveConversation: (id: string) => void
}

export function MessageList({ activeConversation, setActiveConversation }: MessageListProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [conversations, setConversations] = useState<Conversation[]>(dummyConversations)

  // Filter percakapan berdasarkan pencarian
  const filteredConversations = conversations.filter(
    (conversation) =>
      conversation.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conversation.lastMessage.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <div className="w-1/3 border-r border-border flex flex-col">
      <div className="p-4 border-b border-border">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Cari percakapan..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      <div className="flex-1 overflow-y-auto">
        {filteredConversations.length > 0 ? (
          filteredConversations.map((conversation) => (
            <div
              key={conversation.id}
              className={cn(
                "flex items-start gap-3 p-4 cursor-pointer hover:bg-accent/50 transition-colors",
                activeConversation === conversation.id && "bg-accent",
              )}
              onClick={() => setActiveConversation(conversation.id)}
            >
              <div className="relative">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={conversation.avatar || "/placeholder.svg"} alt={conversation.name} />
                  <AvatarFallback>
                    {conversation.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                      .toUpperCase()
                      .substring(0, 2)}
                  </AvatarFallback>
                </Avatar>
                {conversation.isOnline && (
                  <span className="absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 border-2 border-background"></span>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-sm truncate">{conversation.name}</h4>
                  <span className="text-xs text-muted-foreground">{conversation.timestamp}</span>
                </div>
                <p className="text-sm text-muted-foreground truncate">{conversation.lastMessage}</p>
                <div className="flex justify-between items-center mt-1">
                  {conversation.isStore && (
                    <Badge variant="outline" className="text-xs px-1 py-0 h-5">
                      Toko
                    </Badge>
                  )}
                  {conversation.unread > 0 && (
                    <Badge className="ml-auto bg-primary text-xs px-1.5 py-0 h-5 min-w-[20px] flex items-center justify-center">
                      {conversation.unread}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="p-4 text-center text-muted-foreground">Tidak ada percakapan yang ditemukan</div>
        )}
      </div>
    </div>
  )
}
