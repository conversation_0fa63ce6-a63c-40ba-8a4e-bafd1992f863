"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  MoreHorizontal, 
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  TrendingUp,
  Users,
  Mail,
  Megaphone,
  Target,
  Calendar,
  DollarSign,
  BarChart3
} from "lucide-react"
import Link from "next/link"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"

// Data dummy untuk campaigns
const campaignsData = [
  {
    id: "camp-1",
    name: "Flash Sale Akhir Tahun",
    type: "discount",
    status: "active",
    startDate: "2023-11-20",
    endDate: "2023-12-31",
    budget: 5000000,
    spent: 2800000,
    impressions: 125000,
    clicks: 8500,
    conversions: 340,
    revenue: 15000000,
    stores: ["Toko Fashion Kita", "Elektronik Murah"],
    description: "Diskon hingga 70% untuk semua kategori produk"
  },
  {
    id: "camp-2",
    name: "Email Newsletter Mingguan",
    type: "email",
    status: "active",
    startDate: "2023-11-01",
    endDate: "2023-12-31",
    budget: 2000000,
    spent: 800000,
    impressions: 45000,
    clicks: 3200,
    conversions: 180,
    revenue: 8500000,
    stores: ["Semua Store"],
    description: "Newsletter mingguan dengan produk terbaru dan promo"
  },
  {
    id: "camp-3",
    name: "Promosi Produk Elektronik",
    type: "promotion",
    status: "paused",
    startDate: "2023-11-15",
    endDate: "2023-11-30",
    budget: 3000000,
    spent: 1200000,
    impressions: 75000,
    clicks: 4200,
    conversions: 95,
    revenue: 4500000,
    stores: ["Elektronik Murah"],
    description: "Cashback 15% untuk pembelian smartphone dan laptop"
  },
  {
    id: "camp-4",
    name: "Banner Homepage Makanan",
    type: "banner",
    status: "completed",
    startDate: "2023-10-01",
    endDate: "2023-10-31",
    budget: 1500000,
    spent: 1500000,
    impressions: 200000,
    clicks: 12000,
    conversions: 450,
    revenue: 12000000,
    stores: ["Makanan Sehat"],
    description: "Banner promosi makanan organik di homepage"
  },
  {
    id: "camp-5",
    name: "Social Media Campaign",
    type: "social",
    status: "draft",
    startDate: "2023-12-01",
    endDate: "2023-12-25",
    budget: 4000000,
    spent: 0,
    impressions: 0,
    clicks: 0,
    conversions: 0,
    revenue: 0,
    stores: ["Olahraga & Fitness", "Buku & Alat Tulis"],
    description: "Kampanye media sosial untuk produk olahraga dan buku"
  }
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
    case "paused":
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Paused</Badge>
    case "completed":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">Completed</Badge>
    case "draft":
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const getTypeBadge = (type: string) => {
  switch (type) {
    case "discount":
      return <Badge variant="default" className="bg-purple-100 text-purple-800">Discount</Badge>
    case "email":
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Email</Badge>
    case "promotion":
      return <Badge variant="outline" className="bg-orange-100 text-orange-800">Promotion</Badge>
    case "banner":
      return <Badge variant="outline" className="bg-pink-100 text-pink-800">Banner</Badge>
    case "social":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Social Media</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const calculateCTR = (clicks: number, impressions: number) => {
  if (impressions === 0) return 0
  return ((clicks / impressions) * 100).toFixed(2)
}

const calculateConversionRate = (conversions: number, clicks: number) => {
  if (clicks === 0) return 0
  return ((conversions / clicks) * 100).toFixed(2)
}

const calculateROAS = (revenue: number, spent: number) => {
  if (spent === 0) return 0
  return (revenue / spent).toFixed(2)
}

export default function MarketingPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")

  const filteredCampaigns = campaignsData.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         campaign.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || campaign.status === statusFilter
    const matchesType = typeFilter === "all" || campaign.type === typeFilter
    return matchesSearch && matchesStatus && matchesType
  })

  const totalCampaigns = campaignsData.length
  const activeCampaigns = campaignsData.filter(c => c.status === "active").length
  const totalBudget = campaignsData.reduce((sum, campaign) => sum + campaign.budget, 0)
  const totalSpent = campaignsData.reduce((sum, campaign) => sum + campaign.spent, 0)
  const totalRevenue = campaignsData.reduce((sum, campaign) => sum + campaign.revenue, 0)
  const totalImpressions = campaignsData.reduce((sum, campaign) => sum + campaign.impressions, 0)
  const totalClicks = campaignsData.reduce((sum, campaign) => sum + campaign.clicks, 0)
  const totalConversions = campaignsData.reduce((sum, campaign) => sum + campaign.conversions, 0)

  const types = [...new Set(campaignsData.map(c => c.type))]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back</span>
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Marketing & Promosi</h1>
            <p className="text-muted-foreground">
              Kelola campaigns, promosi, dan strategi marketing
            </p>
          </div>
        </div>
        
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Buat Campaign Baru
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
            <Megaphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCampaigns}</div>
            <p className="text-xs text-muted-foreground">
              {activeCampaigns} aktif
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalBudget)}</div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(totalSpent)} terpakai
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              ROAS: {calculateROAS(totalRevenue, totalSpent)}x
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversions</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalConversions}</div>
            <p className="text-xs text-muted-foreground">
              Rate: {calculateConversionRate(totalConversions, totalClicks)}%
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Impressions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalImpressions.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total tayangan iklan</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Clicks</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalClicks.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              CTR: {calculateCTR(totalClicks, totalImpressions)}%
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Budget Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Terpakai</span>
                <span>{((totalSpent / totalBudget) * 100).toFixed(1)}%</span>
              </div>
              <Progress value={(totalSpent / totalBudget) * 100} className="h-2" />
              <p className="text-xs text-muted-foreground">
                {formatCurrency(totalSpent)} dari {formatCurrency(totalBudget)}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Campaigns Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Daftar Campaigns</CardTitle>
              <CardDescription>
                Kelola semua marketing campaigns dan promosi
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari campaigns..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-[250px]"
                />
              </div>
              
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Tipe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Tipe</SelectItem>
                  {types.map(type => (
                    <SelectItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="paused">Paused</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Campaign</TableHead>
                <TableHead>Tipe</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Periode</TableHead>
                <TableHead>Budget</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Revenue</TableHead>
                <TableHead>ROAS</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCampaigns.map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{campaign.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {campaign.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getTypeBadge(campaign.type)}</TableCell>
                  <TableCell>{getStatusBadge(campaign.status)}</TableCell>
                  <TableCell className="text-sm">
                    <div>{formatDate(campaign.startDate)}</div>
                    <div className="text-muted-foreground">
                      s/d {formatDate(campaign.endDate)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {formatCurrency(campaign.budget)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Spent: {formatCurrency(campaign.spent)}
                      </div>
                      <Progress 
                        value={(campaign.spent / campaign.budget) * 100} 
                        className="h-1" 
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1 text-sm">
                      <div>{campaign.impressions.toLocaleString()} impressions</div>
                      <div>{campaign.clicks.toLocaleString()} clicks</div>
                      <div>{campaign.conversions} conversions</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {formatCurrency(campaign.revenue)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {calculateROAS(campaign.revenue, campaign.spent)}x
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Campaign
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <BarChart3 className="h-4 w-4 mr-2" />
                          View Analytics
                        </DropdownMenuItem>
                        {campaign.status === "active" && (
                          <DropdownMenuItem className="text-yellow-600">
                            <Pause className="h-4 w-4 mr-2" />
                            Pause Campaign
                          </DropdownMenuItem>
                        )}
                        {campaign.status === "paused" && (
                          <DropdownMenuItem className="text-green-600">
                            <Play className="h-4 w-4 mr-2" />
                            Resume Campaign
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Campaign
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredCampaigns.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Tidak ada campaigns yang ditemukan</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 