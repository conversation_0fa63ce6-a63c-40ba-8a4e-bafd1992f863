"use client"

import { useState } from "react"
import Link from "next/link"
import { CreditCard, Plus, Edit, Trash2, X, Shield, AlertCircle, CreditCardIcon } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

// Sample payment methods data
const paymentMethods = [
  {
    id: "1",
    type: "credit_card",
    brand: "Visa",
    last4: "4242",
    expMonth: 12,
    expYear: 2025,
    name: "John Doe",
    isDefault: true,
    billingAddress: {
      id: "1",
      nickname: "Home",
    },
  },
  {
    id: "2",
    type: "credit_card",
    brand: "Mastercard",
    last4: "5678",
    expMonth: 8,
    expYear: 2024,
    name: "John Doe",
    isDefault: false,
    billingAddress: {
      id: "2",
      nickname: "Office",
    },
  },
]

export default function PaymentMethodsPage() {
  const [userPaymentMethods, setUserPaymentMethods] = useState(paymentMethods)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [paymentToDelete, setPaymentToDelete] = useState<string | null>(null)
  const [paymentType, setPaymentType] = useState("credit_card")

  const handleDeletePayment = (id: string) => {
    setPaymentToDelete(id)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (paymentToDelete) {
      setUserPaymentMethods(userPaymentMethods.filter((payment) => payment.id !== paymentToDelete))
      setIsDeleteDialogOpen(false)
      setPaymentToDelete(null)
    }
  }

  const getCardIcon = (brand: string) => {
    switch (brand.toLowerCase()) {
      case "visa":
        return <CreditCard className="h-6 w-6 text-blue-600" />
      case "mastercard":
        return <CreditCard className="h-6 w-6 text-red-600" />
      case "amex":
        return <CreditCard className="h-6 w-6 text-green-600" />
      default:
        return <CreditCard className="h-6 w-6" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Account Settings</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/buyer/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="payment" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6 lg:w-auto">
          <TabsTrigger value="profile" asChild>
            <Link href="/buyer/dashboard/account/profile">Profile</Link>
          </TabsTrigger>
          <TabsTrigger value="addresses" asChild>
            <Link href="/buyer/dashboard/account/addresses">Addresses</Link>
          </TabsTrigger>
          <TabsTrigger value="payment">Payment Methods</TabsTrigger>
          <TabsTrigger value="communication" asChild>
            <Link href="/buyer/dashboard/account/communication">Communication</Link>
          </TabsTrigger>
          <TabsTrigger value="security" asChild>
            <Link href="/buyer/dashboard/account/security">Security</Link>
          </TabsTrigger>
          <TabsTrigger value="connected" asChild>
            <Link href="/buyer/dashboard/account/connected">Connected Accounts</Link>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="payment" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Your Payment Methods</h2>
              <p className="text-sm text-muted-foreground">Manage your saved payment methods for faster checkout.</p>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="gap-1">
                  <Plus className="h-4 w-4" />
                  Add Payment Method
                </Button>
              </DialogTrigger>
              <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add New Payment Method</DialogTitle>
                  <DialogDescription>Fill in the details for your new payment method.</DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <RadioGroup defaultValue="credit_card" className="mb-4" onValueChange={setPaymentType}>
                    <div className="flex flex-col space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0">
                      <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-accent">
                        <RadioGroupItem value="credit_card" id="credit_card" />
                        <Label htmlFor="credit_card" className="flex cursor-pointer items-center gap-2">
                          <CreditCard className="h-5 w-5" />
                          Credit/Debit Card
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-accent">
                        <RadioGroupItem value="digital_wallet" id="digital_wallet" />
                        <Label htmlFor="digital_wallet" className="flex cursor-pointer items-center gap-2">
                          <Shield className="h-5 w-5" />
                          Digital Wallet
                        </Label>
                      </div>
                    </div>
                  </RadioGroup>

                  {paymentType === "credit_card" && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="cardName">Name on Card</Label>
                        <Input id="cardName" placeholder="Name as it appears on card" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="cardNumber">Card Number</Label>
                        <div className="relative">
                          <Input id="cardNumber" placeholder="•••• •••• •••• ••••" />
                          <CreditCardIcon className="absolute right-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="expiryDate">Expiry Date</Label>
                          <Input id="expiryDate" placeholder="MM/YY" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="cvv">CVV</Label>
                          <div className="relative">
                            <Input id="cvv" placeholder="•••" />
                            <AlertCircle className="absolute right-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="billingAddress">Billing Address</Label>
                        <Select defaultValue="1">
                          <SelectTrigger>
                            <SelectValue placeholder="Select billing address" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">Home - Jl. Sudirman No. 123</SelectItem>
                            <SelectItem value="2">Office - Jl. Gatot Subroto No. 456</SelectItem>
                            <SelectItem value="new">Add new address...</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center space-x-2 pt-2">
                        <Checkbox id="defaultPayment" />
                        <Label htmlFor="defaultPayment">Set as default payment method</Label>
                      </div>
                    </div>
                  )}

                  {paymentType === "digital_wallet" && (
                    <div className="space-y-4">
                      <div className="rounded-md border p-4">
                        <p className="text-center text-sm">
                          You will be redirected to the selected digital wallet provider to complete the setup.
                        </p>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="walletType">Select Wallet Type</Label>
                        <Select defaultValue="paypal">
                          <SelectTrigger>
                            <SelectValue placeholder="Select wallet type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="paypal">PayPal</SelectItem>
                            <SelectItem value="gopay">GoPay</SelectItem>
                            <SelectItem value="ovo">OVO</SelectItem>
                            <SelectItem value="dana">DANA</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}
                </div>
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" onClick={() => setIsAddDialogOpen(false)}>
                    Save Payment Method
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {userPaymentMethods.map((payment) => (
              <Card key={payment.id}>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getCardIcon(payment.brand)}
                      <CardTitle className="text-base">
                        {payment.brand} •••• {payment.last4}
                      </CardTitle>
                    </div>
                    {payment.isDefault && (
                      <Badge variant="outline" className="bg-primary/10 text-primary">
                        Default
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1 text-sm">
                    <p className="font-medium">{payment.name}</p>
                    <p>
                      Expires {payment.expMonth}/{payment.expYear}
                    </p>
                    <p className="pt-1 text-muted-foreground">Billing Address: {payment.billingAddress.nickname}</p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end gap-2">
                  <Button variant="outline" size="sm" className="gap-1">
                    <Edit className="h-4 w-4" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1 text-red-500 hover:bg-red-50 hover:text-red-600"
                    onClick={() => handleDeletePayment(payment.id)}
                    disabled={payment.isDefault}
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          <div className="mt-6 rounded-lg border p-4">
            <div className="flex items-start gap-3">
              <Shield className="mt-0.5 h-5 w-5 text-primary" />
              <div>
                <h3 className="font-medium">Your payment information is secure</h3>
                <p className="text-sm text-muted-foreground">
                  We use industry-standard encryption to protect your payment details. Your information is never shared
                  with sellers or third parties.
                </p>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this payment method? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4 flex gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Payment Method
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
