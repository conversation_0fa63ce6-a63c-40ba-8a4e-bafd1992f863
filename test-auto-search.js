// Test script untuk memverifikasi semua perbaikan auto search
// Jalankan di browser console saat berada di halaman /sellzio

console.log('🧪 Testing Auto Search Feature with All TypeScript Fixes...');

// Test TypeScript fixes
function testTypeScriptFixes() {
  console.log('\n🔧 Testing TypeScript fixes...');

  // Check if there are any console errors
  const originalError = console.error;
  let errorCount = 0;

  console.error = function(...args) {
    errorCount++;
    originalError.apply(console, args);
  };

  setTimeout(() => {
    console.error = originalError;
    if (errorCount === 0) {
      console.log('✅ No TypeScript/JavaScript errors detected');
    } else {
      console.warn(`⚠️ ${errorCount} errors detected in console`);
    }
  }, 2000);
}

// Function to test auto search functionality
function testAutoSearch() {
  const searchInput = document.querySelector('.search-input');
  
  if (!searchInput) {
    console.error('❌ Search input not found');
    return;
  }

  console.log('🔍 Step 1: Triggering predictions...');
  
  // Focus and type to trigger predictions
  searchInput.focus();
  searchInput.value = 'tas';
  searchInput.dispatchEvent(new Event('input', { bubbles: true }));

  setTimeout(() => {
    const predictionContainer = document.querySelector('.keyword-predictions');
    const predictions = document.querySelectorAll('.prediction-item');

    if (!predictionContainer || predictions.length === 0) {
      console.warn('⚠️ No predictions found to test');
      return;
    }

    console.log(`📊 Found ${predictions.length} predictions`);

    // Get the first prediction for testing
    const firstPrediction = predictions[0];
    const predictionText = firstPrediction.querySelector('.prediction-text')?.textContent?.trim();
    
    if (!predictionText) {
      console.error('❌ Could not get prediction text');
      return;
    }

    console.log(`🎯 Step 2: Testing click on prediction: "${predictionText}"`);

    // Simulate click on first prediction
    firstPrediction.click();

    // Wait for auto search to execute
    setTimeout(() => {
      console.log('📋 Step 3: Checking auto search results...');

      // Check if search input was filled
      const inputValue = searchInput.value;
      console.log(`  - Input value: "${inputValue}"`);
      console.log(`  - Expected: "${predictionText}"`);
      
      if (inputValue === predictionText) {
        console.log('✅ Input filled correctly');
      } else {
        console.warn(`⚠️ Input mismatch. Expected: "${predictionText}", Got: "${inputValue}"`);
      }

      // Check if predictions are hidden
      const predictionsVisible = predictionContainer && 
        window.getComputedStyle(predictionContainer).display !== 'none';
      console.log(`  - Predictions visible: ${predictionsVisible}`);
      
      if (!predictionsVisible) {
        console.log('✅ Predictions hidden correctly');
      } else {
        console.warn('⚠️ Predictions should be hidden after click');
      }

      // Check if search results are shown
      const searchResultsContainer = document.querySelector('.search-results-container');
      const searchResultsVisible = searchResultsContainer &&
        window.getComputedStyle(searchResultsContainer).display !== 'none';

      console.log(`  - Search results container visible: ${searchResultsVisible}`);

      if (searchResultsVisible) {
        console.log('✅ Search results container is visible');

        // Check search results content
        const resultsHeader = searchResultsContainer.querySelector('.search-results-header h3');
        const resultsCount = searchResultsContainer.querySelector('.results-count');
        const resultsGrid = searchResultsContainer.querySelector('.search-results-grid');
        const noResults = searchResultsContainer.querySelector('.no-results');

        if (resultsHeader) {
          console.log(`  - Results header: "${resultsHeader.textContent}"`);
        }

        if (resultsCount) {
          console.log(`  - Results count: "${resultsCount.textContent}"`);
        }

        if (resultsGrid && resultsGrid.children.length > 0) {
          console.log(`  - Product cards found: ${resultsGrid.children.length}`);
          console.log('✅ Search results with products displayed');

          // Log first few product names
          Array.from(resultsGrid.children).slice(0, 3).forEach((card, index) => {
            const productName = card.querySelector('.result-product-name')?.textContent?.trim();
            console.log(`    ${index + 1}. ${productName}`);
          });
        } else if (noResults) {
          console.log('📭 No results message displayed');
          console.log('✅ No results handling works correctly');
        } else {
          console.warn('⚠️ No product cards or no-results message found');
        }

        // NEW: Check if main content is hidden during search results
        const mainContent = document.querySelector('.main-content');
        const bodyHasHideClass = document.body.classList.contains('hide-main-content') ||
                                 document.querySelector('.hide-main-content');
        const mainContentHidden = mainContent &&
          window.getComputedStyle(mainContent).display === 'none';

        console.log(`  - Body has hide-main-content class: ${bodyHasHideClass}`);
        console.log(`  - Main content hidden: ${mainContentHidden}`);

        if (mainContentHidden || bodyHasHideClass) {
          console.log('✅ Main content is properly hidden during search results');
        } else {
          console.warn('⚠️ Main content should be hidden during search results');
        }
      } else {
        console.warn('⚠️ Search results container should be visible after auto search');
      }

      // Check if suggestions are hidden
      const suggestionsContainer = document.querySelector('.suggestions-container');
      const suggestionsVisible = suggestionsContainer && 
        window.getComputedStyle(suggestionsContainer).display !== 'none';
      
      console.log(`  - Suggestions visible: ${suggestionsVisible}`);
      
      if (!suggestionsVisible) {
        console.log('✅ Suggestions hidden correctly');
      } else {
        console.warn('⚠️ Suggestions should be hidden during search results');
      }

      // Final summary
      console.log('\n🏁 AUTO SEARCH TEST SUMMARY:');
      const inputCorrect = inputValue === predictionText;
      const predictionsHidden = !predictionsVisible;
      const resultsShown = searchResultsVisible;
      const suggestionsHidden = !suggestionsVisible;
      
      console.log(`  ✅ Input filled: ${inputCorrect}`);
      console.log(`  ✅ Predictions hidden: ${predictionsHidden}`);
      console.log(`  ✅ Results shown: ${resultsShown}`);
      console.log(`  ✅ Suggestions hidden: ${suggestionsHidden}`);
      
      const allPassed = inputCorrect && predictionsHidden && resultsShown && suggestionsHidden;
      
      if (allPassed) {
        console.log('🎉 ALL AUTO SEARCH TESTS PASSED!');
        console.log('✅ Auto search functionality works correctly like docs/facet.html');
      } else {
        console.log('❌ Some auto search tests failed. Check the issues above.');
      }
    }, 500);
  }, 300);
}

// Test localStorage integration
function testLocalStorageIntegration() {
  console.log('\n💾 Testing localStorage integration...');
  
  // Check if prediction history is saved
  try {
    const history = localStorage.getItem('keywordPredictionHistory');
    if (history) {
      const parsedHistory = JSON.parse(history);
      console.log(`📚 Prediction history found: ${parsedHistory.length} items`);
      console.log('✅ localStorage integration works');
      
      // Show first few items
      parsedHistory.slice(0, 3).forEach((item, index) => {
        console.log(`  ${index + 1}. "${item}"`);
      });
    } else {
      console.log('📭 No prediction history found in localStorage');
    }
  } catch (e) {
    console.error('❌ Error reading prediction history:', e);
  }
}

// Test search functionality with different keywords
function testSearchFunctionality() {
  console.log('\n🔍 Testing search functionality with different keywords...');
  
  const testKeywords = ['tas', 'smartphone', 'sepatu', 'laptop', 'xyz123'];
  let testIndex = 0;
  
  function runSearchTest() {
    if (testIndex >= testKeywords.length) {
      console.log('🏁 Search functionality tests completed!');
      return;
    }
    
    const keyword = testKeywords[testIndex];
    console.log(`\n🔍 Testing search for: "${keyword}"`);
    
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
      searchInput.value = keyword;
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      // Simulate manual search execution
      setTimeout(() => {
        // Check if executeSearch function exists and call it
        if (typeof window.executeSearch === 'function') {
          window.executeSearch(keyword);
        }
        
        setTimeout(() => {
          const resultsContainer = document.querySelector('.search-results-container');
          const resultsCount = document.querySelector('.results-count');
          
          if (resultsContainer && resultsCount) {
            console.log(`  Results: ${resultsCount.textContent}`);
          }
          
          testIndex++;
          setTimeout(runSearchTest, 1000);
        }, 300);
      }, 200);
    }
  }
  
  runSearchTest();
}

// Test behavior when adding/removing letters after search
function testInputChangeAfterSearch() {
  console.log('\n🔄 Testing input change behavior after search...');

  const searchInput = document.querySelector('.search-input');

  if (!searchInput) {
    console.error('❌ Search input not found');
    return;
  }

  // First, simulate a search
  console.log('🔍 Step 1: Performing initial search...');
  searchInput.focus();
  searchInput.value = 'tas';
  searchInput.dispatchEvent(new Event('input', { bubbles: true }));

  setTimeout(() => {
    // Click first prediction to trigger search
    const predictions = document.querySelectorAll('.prediction-item');
    if (predictions.length > 0) {
      predictions[0].click();

      setTimeout(() => {
        console.log('📝 Step 2: Modifying search input after search...');

        // Check initial state - should be in search results mode
        const initialSearchResults = document.querySelector('.search-results-container');
        const initialResultsVisible = initialSearchResults &&
          window.getComputedStyle(initialSearchResults).display !== 'none';

        console.log(`  - Initial search results visible: ${initialResultsVisible}`);

        // Now modify the input by adding a letter
        searchInput.focus();
        searchInput.value = 'tas ';
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));

        setTimeout(() => {
          // Check if predictions are back and search results are hidden
          const predictionsContainer = document.querySelector('.keyword-predictions');
          const predictionsVisible = predictionsContainer &&
            window.getComputedStyle(predictionsContainer).display !== 'none';

          const searchResultsAfter = document.querySelector('.search-results-container');
          const resultsVisibleAfter = searchResultsAfter &&
            window.getComputedStyle(searchResultsAfter).display !== 'none';

          const mainContent = document.querySelector('.main-content');
          const mainContentVisible = mainContent &&
            window.getComputedStyle(mainContent).display !== 'none';

          console.log(`  - Predictions visible after input change: ${predictionsVisible}`);
          console.log(`  - Search results visible after input change: ${resultsVisibleAfter}`);
          console.log(`  - Main content visible after input change: ${mainContentVisible}`);

          // Verify behavior
          if (predictionsVisible && !resultsVisibleAfter && mainContentVisible) {
            console.log('✅ Input change behavior works correctly!');
            console.log('✅ Predictions returned, search results hidden, main content shown');
          } else {
            console.warn('⚠️ Input change behavior needs fixing:');
            if (!predictionsVisible) console.warn('  - Predictions should be visible');
            if (resultsVisibleAfter) console.warn('  - Search results should be hidden');
            if (!mainContentVisible) console.warn('  - Main content should be visible');
          }

          // Test removing letters
          console.log('📝 Step 3: Testing letter removal...');
          searchInput.value = 'ta';
          searchInput.dispatchEvent(new Event('input', { bubbles: true }));

          setTimeout(() => {
            const predictionsAfterRemoval = document.querySelector('.keyword-predictions');
            const predictionsVisibleAfterRemoval = predictionsAfterRemoval &&
              window.getComputedStyle(predictionsAfterRemoval).display !== 'none';

            console.log(`  - Predictions visible after letter removal: ${predictionsVisibleAfterRemoval}`);

            if (predictionsVisibleAfterRemoval) {
              console.log('✅ Letter removal behavior works correctly!');
            } else {
              console.warn('⚠️ Predictions should be visible after letter removal');
            }
          }, 300);
        }, 500);
      }, 1000);
    } else {
      console.warn('⚠️ No predictions found for initial search test');
    }
  }, 500);
}

// Run all tests
console.log('🚀 Starting comprehensive auto search tests...');
testTypeScriptFixes();
setTimeout(() => {
  testLocalStorageIntegration();
  setTimeout(() => {
    testAutoSearch();
    setTimeout(() => {
      testInputChangeAfterSearch();
      setTimeout(() => {
        testSearchFunctionality();
      }, 4000);
    }, 3000);
  }, 2000);
}, 1000);
