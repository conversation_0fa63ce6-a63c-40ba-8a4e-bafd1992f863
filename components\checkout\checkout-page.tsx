"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { formatCurrency } from "@/lib/utils"
import { paymentAPI } from "@/lib/api/payment"
import type { PaymentGateway, PaymentRequest, PaymentResponse } from "@/lib/models/payment"
import { AlertCircle, CreditCard, <PERSON>ader2 } from "lucide-react"
import Image from "next/image"

export function CheckoutPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const orderId = searchParams.get("orderId") || "ORD-" + Math.floor(100000 + Math.random() * 900000).toString()

  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>([])
  const [selectedGateway, setSelectedGateway] = useState<string>("")
  const [selectedMethod, setSelectedMethod] = useState<string>("")
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [paymentResponse, setPaymentResponse] = useState<PaymentResponse | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Mock order data
  const orderData = {
    id: orderId,
    items: [
      { id: "1", name: "Sepatu Sneakers", price: 350000, quantity: 1 },
      { id: "2", name: "Kaos Polos", price: 120000, quantity: 2 },
    ],
    subtotal: 590000,
    shipping: 25000,
    discount: 50000,
    total: 565000,
  }

  // Customer data
  const [customerData, setCustomerData] = useState({
    name: "John Doe",
    email: "<EMAIL>",
    phone: "081234567890",
  })

  useEffect(() => {
    const fetchPaymentGateways = async () => {
      try {
        const gateways = await paymentAPI.getPaymentGateways()
        setPaymentGateways(gateways)
        if (gateways.length > 0) {
          setSelectedGateway(gateways[0].id)
        }
        setLoading(false)
      } catch (error) {
        console.error("Error fetching payment gateways:", error)
        setError("Gagal memuat metode pembayaran. Silakan coba lagi.")
        setLoading(false)
      }
    }

    fetchPaymentGateways()
  }, [])

  const handleGatewayChange = (gatewayId: string) => {
    setSelectedGateway(gatewayId)
    setSelectedMethod("")
  }

  const handleMethodChange = (methodId: string) => {
    setSelectedMethod(methodId)
  }

  const handleCustomerDataChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setCustomerData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async () => {
    if (!selectedMethod) {
      setError("Silakan pilih metode pembayaran")
      return
    }

    setProcessing(true)
    setError(null)

    try {
      const paymentRequest: PaymentRequest = {
        orderId: orderData.id,
        amount: orderData.total,
        paymentMethodId: selectedMethod,
        paymentGatewayId: selectedGateway,
        customerName: customerData.name,
        customerEmail: customerData.email,
        customerPhone: customerData.phone,
        callbackUrl: `${window.location.origin}/api/payment/callback`,
        returnUrl: `${window.location.origin}/checkout/status`,
      }

      const response = await paymentAPI.createPayment(paymentRequest)
      setPaymentResponse(response)

      // Redirect jika ada redirectUrl
      if (response.redirectUrl) {
        // Dalam implementasi sebenarnya, kita akan redirect ke URL ini
        // window.location.href = response.redirectUrl;

        // Untuk demo, kita hanya simulasi redirect
        setTimeout(() => {
          router.push(`/checkout/status?paymentId=${response.payment.id}`)
        }, 2000)
      }
    } catch (error) {
      console.error("Error creating payment:", error)
      setError("Gagal membuat pembayaran. Silakan coba lagi.")
    } finally {
      setProcessing(false)
    }
  }

  const selectedGatewayData = paymentGateways.find((g) => g.id === selectedGateway)
  const selectedMethodData = selectedGatewayData?.methods.find((m) => m.id === selectedMethod)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (paymentResponse) {
    return (
      <div className="container max-w-4xl mx-auto py-8 px-4">
        <Card>
          <CardHeader>
            <CardTitle>Instruksi Pembayaran</CardTitle>
            <CardDescription>Silakan selesaikan pembayaran Anda sesuai instruksi berikut</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-col md:flex-row justify-between gap-6">
              <div className="space-y-2">
                <p className="text-sm font-medium">Order ID</p>
                <p>{paymentResponse.payment.orderId}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium">Total Pembayaran</p>
                <p className="text-xl font-bold">{formatCurrency(paymentResponse.payment.amount)}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium">Metode Pembayaran</p>
                <p>{paymentResponse.payment.paymentMethod.name}</p>
              </div>
            </div>

            <Separator />

            {paymentResponse.payment.paymentMethod.type === "virtual_account" &&
              paymentResponse.virtualAccountNumber && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Virtual Account Number</h3>
                  <div className="flex items-center justify-between p-4 bg-muted rounded-md">
                    <span className="text-xl font-mono">{paymentResponse.virtualAccountNumber}</span>
                    <Button
                      variant="outline"
                      onClick={() => navigator.clipboard.writeText(paymentResponse.virtualAccountNumber!)}
                    >
                      Salin
                    </Button>
                  </div>
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Penting</AlertTitle>
                    <AlertDescription>
                      Pembayaran akan diverifikasi secara otomatis. Harap selesaikan pembayaran sebelum{" "}
                      {new Date(paymentResponse.payment.expiryTime!).toLocaleString()}
                    </AlertDescription>
                  </Alert>
                  {paymentResponse.payment.paymentInstructions && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Cara Pembayaran:</h4>
                      <ol className="list-decimal list-inside space-y-1">
                        {paymentResponse.payment.paymentInstructions.map((instruction, index) => (
                          <li key={index}>{instruction}</li>
                        ))}
                      </ol>
                    </div>
                  )}
                </div>
              )}

            {paymentResponse.payment.paymentMethod.type === "e_wallet" && paymentResponse.qrCode && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Scan QR Code</h3>
                <div className="flex flex-col items-center">
                  <div className="relative w-64 h-64 mb-4">
                    <Image
                      src={paymentResponse.qrCode || "/placeholder.svg"}
                      alt="QR Code"
                      fill
                      className="object-contain"
                    />
                  </div>
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Penting</AlertTitle>
                    <AlertDescription>
                      Buka aplikasi {paymentResponse.payment.paymentMethod.name} dan scan QR code di atas. Pembayaran
                      akan diverifikasi secara otomatis.
                    </AlertDescription>
                  </Alert>
                </div>
              </div>
            )}

            {paymentResponse.payment.paymentMethod.type === "retail_outlet" && paymentResponse.paymentCode && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Kode Pembayaran</h3>
                <div className="flex items-center justify-between p-4 bg-muted rounded-md">
                  <span className="text-xl font-mono">{paymentResponse.paymentCode}</span>
                  <Button variant="outline" onClick={() => navigator.clipboard.writeText(paymentResponse.paymentCode!)}>
                    Salin
                  </Button>
                </div>
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Penting</AlertTitle>
                  <AlertDescription>
                    Tunjukkan kode pembayaran ini ke kasir {paymentResponse.payment.paymentMethod.name}. Pembayaran akan
                    diverifikasi secara otomatis.
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {paymentResponse.redirectUrl && (
              <div className="text-center">
                <p className="mb-4">Anda akan dialihkan ke halaman pembayaran...</p>
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => router.push(`/checkout/status?paymentId=${paymentResponse.payment.id}`)}
            >
              Cek Status Pembayaran
            </Button>
            <Button variant="outline" onClick={() => router.push("/")}>
              Kembali ke Beranda
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      <div className="grid gap-8 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Metode Pembayaran</CardTitle>
              <CardDescription>Pilih metode pembayaran yang Anda inginkan</CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Tabs value={selectedGateway} onValueChange={handleGatewayChange}>
                <TabsList className="grid grid-cols-2">
                  {paymentGateways.map((gateway) => (
                    <TabsTrigger key={gateway.id} value={gateway.id}>
                      {gateway.name}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {paymentGateways.map((gateway) => (
                  <TabsContent key={gateway.id} value={gateway.id} className="space-y-4">
                    <RadioGroup value={selectedMethod} onValueChange={handleMethodChange}>
                      {gateway.methods.map((method) => (
                        <div key={method.id} className="flex items-center space-x-2">
                          <RadioGroupItem value={method.id} id={method.id} />
                          <Label htmlFor={method.id} className="flex items-center gap-2 cursor-pointer py-2">
                            {method.logo && (
                              <div className="relative w-12 h-8">
                                <Image
                                  src={method.logo || "/placeholder.svg"}
                                  alt={method.name}
                                  fill
                                  className="object-contain"
                                />
                              </div>
                            )}
                            <span>{method.name}</span>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </TabsContent>
                ))}
              </Tabs>

              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-medium">Informasi Pelanggan</h3>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="name">Nama Lengkap</Label>
                    <Input id="name" name="name" value={customerData.name} onChange={handleCustomerDataChange} />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={customerData.email}
                      onChange={handleCustomerDataChange}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="phone">Nomor Telepon</Label>
                    <Input id="phone" name="phone" value={customerData.phone} onChange={handleCustomerDataChange} />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Ringkasan Pesanan</CardTitle>
              <CardDescription>Order ID: {orderData.id}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                {orderData.items.map((item) => (
                  <div key={item.id} className="flex justify-between">
                    <span>
                      {item.name} x{item.quantity}
                    </span>
                    <span>{formatCurrency(item.price * item.quantity)}</span>
                  </div>
                ))}
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>{formatCurrency(orderData.subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Pengiriman</span>
                  <span>{formatCurrency(orderData.shipping)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Diskon</span>
                  <span>-{formatCurrency(orderData.discount)}</span>
                </div>
              </div>

              <Separator />

              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>{formatCurrency(orderData.total)}</span>
              </div>

              {selectedMethodData && (
                <div className="mt-4 p-3 bg-muted rounded-md flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  <span>Pembayaran dengan {selectedMethodData.name}</span>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button className="w-full" onClick={handleSubmit} disabled={!selectedMethod || processing}>
                {processing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Memproses
                  </>
                ) : (
                  "Bayar Sekarang"
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}
