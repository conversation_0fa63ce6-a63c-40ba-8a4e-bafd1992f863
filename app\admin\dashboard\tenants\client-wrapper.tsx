"use client"

import dynamic from "next/dynamic"
import { Suspense } from "react"
import { ErrorBoundary } from "@/components/error-boundary"
import { NotificationsProvider } from "@/components/providers/notifications-provider"

// Loading component
function TenantsLoading() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Tenants</h1>
      <p>Loading tenants list...</p>
    </div>
  )
}

// Error fallback component
function TenantsFallback() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Tenants</h1>
      <p className="text-red-500">Error loading tenants. Please try again.</p>
      <button
        onClick={() => window.location.reload()}
        className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md"
      >
        Reload Page
      </button>
    </div>
  )
}

// Dynamic import of client component
const TenantsClient = dynamic(() => import("./client"), {
  loading: () => <TenantsLoading />,
  ssr: false, // Disable SSR for client component
})

export default function ClientWrapper() {
  return (
    <ErrorBoundary fallback={<TenantsFallback />}>
      <Suspense fallback={<TenantsLoading />}>
        <NotificationsProvider>
          <TenantsClient />
        </NotificationsProvider>
      </Suspense>
    </ErrorBoundary>
  )
}
