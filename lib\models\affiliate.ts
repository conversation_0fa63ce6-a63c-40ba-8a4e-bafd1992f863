export interface AffiliateProgram {
  id: string
  tenantId: string
  name: string
  description: string
  commissionRate: number // Persentase komisi
  cookieDuration: number // Durasi cookie dalam hari
  minimumPayout: number
  status: "active" | "inactive"
  createdAt: string
  updatedAt: string
}

export interface AffiliateUser {
  id: string
  userId: string
  programId: string
  referralCode: string
  balance: number
  totalEarnings: number
  status: "active" | "pending" | "rejected" | "suspended"
  paymentMethod: {
    type: "bank_transfer" | "e_wallet"
    details: Record<string, string>
  }
  createdAt: string
  updatedAt: string
}

export interface AffiliateReferral {
  id: string
  affiliateUserId: string
  referredUserId: string
  status: "pending" | "registered" | "purchased"
  createdAt: string
  updatedAt: string
}

export interface AffiliateCommission {
  id: string
  affiliateUserId: string
  orderId: string
  amount: number
  status: "pending" | "approved" | "rejected" | "paid"
  createdAt: string
  updatedAt: string
}

export interface AffiliatePayment {
  id: string
  affiliateUserId: string
  amount: number
  paymentMethod: {
    type: "bank_transfer" | "e_wallet"
    details: Record<string, string>
  }
  status: "pending" | "processing" | "completed" | "failed"
  transactionId?: string
  createdAt: string
  updatedAt: string
}
