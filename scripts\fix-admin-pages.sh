#!/bin/bash

# Script untuk mengubah semua halaman admin menjadi client-safe
# Jalankan script ini dari root project

# Direktori admin dashboard
ADMIN_DIR="app/admin/dashboard"

# Fungsi untuk mengubah file page.tsx
convert_page() {
  local file=$1
  local dir=$(dirname "$file")
  local client_file="${dir}/client.tsx"
  
  # Jika client.tsx belum ada, buat file baru
  if [ ! -f "$client_file" ]; then
    echo "Creating client component for $file"
    
    # Buat file client.tsx
    cat > "$client_file" << EOF
"use client"

import { ErrorBoundary } from "@/components/error-boundary"

export default function ${dir##*/}Client() {
  return (
    <ErrorBoundary>
      {/* Content from original page */}
      <div>
        <h1>${dir##*/} Page</h1>
        {/* Add your content here */}
      </div>
    </ErrorBoundary>
  )
}
EOF
  fi
  
  # Ubah file page.tsx
  echo "Updating $file"
  cat > "$file" << EOF
import { Metadata } from "next"
import ${dir##*/}Client from "./client"

export const dynamic = "force-dynamic"

export const metadata: Metadata = {
  title: "${dir##*/} | Admin Dashboard",
  description: "${dir##*/} page for admin dashboard",
}

export default function ${dir##*/}Page() {
  return <${dir##*/}Client />
}
EOF
}

# Cari semua file page.tsx di direktori admin
find "$ADMIN_DIR" -name "page.tsx" | while read -r file; do
  convert_page "$file"
done

echo "All admin pages have been converted to client-safe format"
