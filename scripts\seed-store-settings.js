const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '../.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const storeSettings = [
  // General Settings
  {
    setting_key: 'store_name',
    setting_name: 'Nama Store',
    setting_value: 'My Store',
    setting_type: 'text',
    category: 'general',
    description: 'Nama resmi store Anda',
    is_public: true,
    is_required: true,
    sort_order: 1,
    validation_rules: {
      required: true,
      min_length: 2,
      max_length: 100
    }
  },
  {
    setting_key: 'store_description',
    setting_name: 'Deskripsi Store',
    setting_value: 'Toko online terpercaya dengan berbagai produk berkualitas',
    setting_type: 'textarea',
    category: 'general',
    description: 'Deskripsi singkat tentang store Anda',
    is_public: true,
    is_required: false,
    sort_order: 2,
    validation_rules: {
      max_length: 500
    }
  },
  {
    setting_key: 'store_email',
    setting_name: 'Email Store',
    setting_value: '<EMAIL>',
    setting_type: 'email',
    category: 'general',
    description: 'Email kontak untuk store',
    is_public: true,
    is_required: true,
    sort_order: 3,
    validation_rules: {
      required: true,
      email: true
    }
  },
  {
    setting_key: 'commission_rate',
    setting_name: 'Rate Komisi (%)',
    setting_value: '10',
    setting_type: 'number',
    category: 'commission',
    description: 'Persentase komisi untuk affiliate',
    is_public: false,
    is_required: true,
    sort_order: 1,
    validation_rules: {
      required: true,
      min: 0,
      max: 50,
      step: 0.1
    }
  },
  {
    setting_key: 'payment_methods',
    setting_name: 'Metode Pembayaran',
    setting_value: 'bank_transfer,credit_card,ewallet',
    setting_type: 'text',
    category: 'payment',
    description: 'Metode pembayaran yang diterima (dipisah koma)',
    is_public: true,
    is_required: true,
    sort_order: 1,
    validation_rules: {
      required: true
    }
  },
  {
    setting_key: 'free_shipping_threshold',
    setting_name: 'Minimum Free Shipping',
    setting_value: '100000',
    setting_type: 'number',
    category: 'shipping',
    description: 'Minimum pembelian untuk gratis ongkir (Rupiah)',
    is_public: true,
    is_required: false,
    sort_order: 1,
    validation_rules: {
      min: 0
    }
  },
  {
    setting_key: 'email_notifications',
    setting_name: 'Notifikasi Email',
    setting_value: 'true',
    setting_type: 'boolean',
    category: 'notification',
    description: 'Aktifkan notifikasi via email',
    is_public: false,
    is_required: false,
    sort_order: 1,
    validation_rules: {}
  },
  {
    setting_key: 'require_email_verification',
    setting_name: 'Verifikasi Email Wajib',
    setting_value: 'true',
    setting_type: 'boolean',
    category: 'security',
    description: 'Wajibkan verifikasi email untuk registrasi',
    is_public: false,
    is_required: false,
    sort_order: 1,
    validation_rules: {}
  },
  {
    setting_key: 'meta_title',
    setting_name: 'Meta Title',
    setting_value: 'My Store - Toko Online Terpercaya',
    setting_type: 'text',
    category: 'seo',
    description: 'Judul halaman untuk SEO',
    is_public: true,
    is_required: false,
    sort_order: 1,
    validation_rules: {
      max_length: 60
    }
  }
]

async function seedStoreSettings() {
  try {
    console.log('Seeding store settings...')
    
    // Check if settings already exist
    const { data: existing } = await supabase
      .from('store_settings')
      .select('setting_key')
    
    if (existing && existing.length > 0) {
      console.log('Store settings already exist, skipping seed')
      return
    }
    
    // Insert settings
    const { data, error } = await supabase
      .from('store_settings')
      .insert(storeSettings)
      .select()

    if (error) {
      console.error('Error seeding store settings:', error)
      return
    }

    console.log(`Successfully seeded ${data.length} store settings`)
    console.log('Store settings seeded successfully!')
  } catch (error) {
    console.error('Error:', error)
  }
}

// Run the seed function
seedStoreSettings().then(() => {
  console.log('Seed completed')
  process.exit(0)
}).catch(error => {
  console.error('Seed failed:', error)
  process.exit(1)
})
