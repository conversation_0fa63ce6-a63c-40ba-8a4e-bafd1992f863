// Test script untuk memverifikasi history functionality
// Jalankan di browser console saat berada di halaman /sellzio

console.log('🧪 Testing History Functionality...');

// Test 1: Clear existing history untuk test yang bersih
function clearAllHistory() {
  console.log('\n🧹 Test 1: Clearing all history for clean test...');
  
  // Clear localStorage
  localStorage.removeItem('searchHistory');
  localStorage.removeItem('keywordPredictionHistory');
  
  // Clear search history via UI
  const clearButton = document.querySelector('.clear-history');
  if (clearButton) {
    clearButton.click();
    console.log('✅ Clicked clear history button');
  }
  
  console.log('✅ All history cleared');
}

// Test 2: Simulate prediction click dan verifikasi history tersimpan
function testPredictionClickHistory() {
  console.log('\n🔍 Test 2: Testing prediction click history...');
  
  // Trigger search input untuk menampilkan predictions
  const searchInput = document.querySelector('input[type="text"]');
  if (searchInput) {
    searchInput.focus();
    searchInput.value = 'tas';
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    setTimeout(() => {
      // Check if predictions are shown
      const predictions = document.querySelectorAll('.prediction-item');
      console.log('Predictions found:', predictions.length);
      
      if (predictions.length > 0) {
        // Click first prediction
        const firstPrediction = predictions[0];
        const predictionText = firstPrediction.querySelector('.prediction-text').textContent;
        console.log('Clicking prediction:', predictionText);
        
        firstPrediction.click();
        
        // Check localStorage after click
        setTimeout(() => {
          const searchHistory = localStorage.getItem('searchHistory');
          const predictionHistory = localStorage.getItem('keywordPredictionHistory');
          
          console.log('Search history in localStorage:', searchHistory);
          console.log('Prediction history in localStorage:', predictionHistory);
          
          if (searchHistory && searchHistory.includes(predictionText)) {
            console.log('✅ Search history updated correctly');
          } else {
            console.log('❌ Search history NOT updated');
          }
          
          if (predictionHistory && predictionHistory.includes(predictionText)) {
            console.log('✅ Prediction history updated correctly');
          } else {
            console.log('❌ Prediction history NOT updated');
          }
        }, 1000);
      } else {
        console.log('❌ No predictions found');
      }
    }, 500);
  } else {
    console.log('❌ Search input not found');
  }
}

// Test 3: Test history muncul di predictions saat mengetik
function testHistoryInPredictions() {
  console.log('\n📝 Test 3: Testing history appears in predictions...');
  
  setTimeout(() => {
    // Clear search input dan ketik ulang untuk trigger predictions
    const searchInput = document.querySelector('input[type="text"]');
    if (searchInput) {
      // Clear first
      searchInput.value = '';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      setTimeout(() => {
        // Type again to trigger predictions
        searchInput.value = 'tas';
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));
        
        setTimeout(() => {
          const predictions = document.querySelectorAll('.prediction-item');
          const historyPredictions = Array.from(predictions).filter(pred => {
            const icon = pred.querySelector('.prediction-icon i');
            return icon && icon.classList.contains('fa-history');
          });
          
          console.log('Total predictions:', predictions.length);
          console.log('History predictions (with fa-history icon):', historyPredictions.length);
          
          if (historyPredictions.length > 0) {
            console.log('✅ History predictions found in predictions list');
            historyPredictions.forEach((pred, index) => {
              const text = pred.querySelector('.prediction-text').textContent;
              console.log(`  History ${index + 1}: ${text}`);
            });
          } else {
            console.log('❌ No history predictions found');
            
            // Debug: check localStorage content
            const predictionHistory = localStorage.getItem('keywordPredictionHistory');
            console.log('Debug - localStorage prediction history:', predictionHistory);
            
            // Debug: check if userInteractionHistory is loaded
            console.log('Debug - Check console for userInteractionHistory logs');
          }
        }, 1000);
      }, 500);
    }
  }, 3000);
}

// Test 4: Test search history muncul di suggestions
function testSearchHistoryInSuggestions() {
  console.log('\n📋 Test 4: Testing search history in suggestions...');
  
  setTimeout(() => {
    // Clear search input untuk menampilkan suggestions
    const searchInput = document.querySelector('input[type="text"]');
    if (searchInput) {
      searchInput.value = '';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      // Click search input untuk expand suggestions
      searchInput.click();
      
      setTimeout(() => {
        const keywordButtons = document.querySelectorAll('.keyword-button');
        const historyIcons = document.querySelectorAll('.suggestion-icon .fa-history');
        
        console.log('Keyword buttons found:', keywordButtons.length);
        console.log('History icons found:', historyIcons.length);
        
        if (keywordButtons.length > 0) {
          console.log('✅ Search history appears in suggestions');
          keywordButtons.forEach((btn, index) => {
            const text = btn.querySelector('.keyword-button-text').textContent;
            console.log(`  Suggestion ${index + 1}: ${text}`);
          });
        } else {
          console.log('❌ No search history in suggestions');
          
          // Debug localStorage
          const searchHistory = localStorage.getItem('searchHistory');
          console.log('Debug - localStorage search history:', searchHistory);
        }
      }, 1000);
    }
  }, 5000);
}

// Test 5: Comprehensive history flow test
function testCompleteHistoryFlow() {
  console.log('\n🔄 Test 5: Complete history flow test...');
  
  setTimeout(() => {
    console.log('Step 1: Type "smartphone" and click prediction');
    
    const searchInput = document.querySelector('input[type="text"]');
    if (searchInput) {
      searchInput.focus();
      searchInput.value = 'smartphone';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      setTimeout(() => {
        const predictions = document.querySelectorAll('.prediction-item');
        if (predictions.length > 0) {
          predictions[0].click();
          
          setTimeout(() => {
            console.log('Step 2: Clear search and type "smart" to see if history appears');
            
            // Go back to search mode
            const backButton = document.querySelector('.back-arrow');
            if (backButton) {
              backButton.click();
              
              setTimeout(() => {
                searchInput.value = 'smart';
                searchInput.dispatchEvent(new Event('input', { bubbles: true }));
                
                setTimeout(() => {
                  const newPredictions = document.querySelectorAll('.prediction-item');
                  const historyItems = Array.from(newPredictions).filter(pred => {
                    const icon = pred.querySelector('.prediction-icon i');
                    return icon && icon.classList.contains('fa-history');
                  });
                  
                  console.log('Predictions after typing "smart":', newPredictions.length);
                  console.log('History items found:', historyItems.length);
                  
                  if (historyItems.length > 0) {
                    console.log('✅ COMPLETE FLOW SUCCESS: History working correctly!');
                  } else {
                    console.log('❌ COMPLETE FLOW FAILED: History not appearing');
                  }
                }, 1000);
              }, 500);
            }
          }, 2000);
        }
      }, 500);
    }
  }, 7000);
}

// Run all tests sequentially
console.log('🚀 Starting history functionality tests...');

clearAllHistory();
setTimeout(() => {
  testPredictionClickHistory();
  testHistoryInPredictions();
  testSearchHistoryInSuggestions();
  testCompleteHistoryFlow();
}, 1000);

// Final summary
setTimeout(() => {
  console.log('\n📊 Test Summary:');
  console.log('1. ✅ Clear history');
  console.log('2. 🔍 Prediction click saves to history');
  console.log('3. 📝 History appears in predictions');
  console.log('4. 📋 Search history in suggestions');
  console.log('5. 🔄 Complete history flow');
  console.log('\n💡 Check console logs above for detailed results');
  console.log('💡 Check localStorage in DevTools Application tab');
}, 10000);
