"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Filter,
  Undo2,
  CircleDollarSign,
  ShoppingBag,
  ClipboardList,
  CheckCircle,
  X,
  Clock,
  AlertCircle,
  Package,
  User,
  Store,
  Eye,
  MessageSquare,
  MoreHorizontal,
  Calendar,
  Image as ImageIcon,
  Truck
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk pengembalian pesanan
const returns = [
  {
    id: "RET-20240105-001",
    orderId: "ORD-20231225-089",
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "081234567890",
      avatar: "/api/placeholder/32/32"
    },
    store: {
      name: "Fashion Zone",
      id: "STORE-002"
    },
    returnDate: "2024-01-05T09:30:00",
    returnReason: "Wrong size ordered",
    status: "pending_approval",
    refundStatus: "pending",
    refundAmount: 450000,
    returnMethod: "courier_pickup",
    pickupSchedule: "2024-01-07T10:00:00",
    items: [
      {
        id: "ITEM-101",
        name: "Dress Casual Wanita",
        sku: "FASH-DRESS-003",
        price: 450000,
        quantity: 1,
        returnReason: "Size too small",
        returnCondition: "unopened",
        image: "/api/placeholder/50/50"
      }
    ],
    timeline: [
      { time: "2024-01-05T09:30:00", status: "requested", note: "Permintaan pengembalian dibuat" },
      { time: "2024-01-05T11:45:00", status: "pending_approval", note: "Menunggu persetujuan dari store" }
    ],
    evidence: [
      { type: "image", url: "/api/placeholder/150/150", description: "Foto produk dalam kemasan" }
    ],
    notes: "Pelanggan ingin menukar dengan ukuran yang lebih besar",
    communicationHistory: [
      { time: "2024-01-05T10:00:00", from: "customer", message: "Saya ingin menukar ukuran karena terlalu kecil." },
      { time: "2024-01-05T10:30:00", from: "store", message: "Baik, kami akan proses permintaan penukaran Anda." }
    ]
  },
  {
    id: "RET-20240104-002",
    orderId: "ORD-20231223-077",
    customer: {
      name: "Budi Santoso",
      email: "<EMAIL>",
      phone: "081298765432",
      avatar: "/api/placeholder/32/32"
    },
    store: {
      name: "TechWorld Store",
      id: "STORE-001"
    },
    returnDate: "2024-01-04T14:20:00",
    returnReason: "Defective product",
    status: "approved",
    refundStatus: "processing",
    refundAmount: 2500000,
    returnMethod: "drop_off",
    returnLocation: "TechWorld Store - Mall Central Jakarta",
    items: [
      {
        id: "ITEM-102",
        name: "Headphone Bluetooth Premium",
        sku: "TECH-HP-005",
        price: 2500000,
        quantity: 1,
        returnReason: "Left speaker not working",
        returnCondition: "opened",
        image: "/api/placeholder/50/50"
      }
    ],
    timeline: [
      { time: "2024-01-04T14:20:00", status: "requested", note: "Permintaan pengembalian dibuat" },
      { time: "2024-01-04T15:30:00", status: "pending_approval", note: "Menunggu persetujuan dari store" },
      { time: "2024-01-05T09:00:00", status: "approved", note: "Pengembalian disetujui" }
    ],
    evidence: [
      { type: "image", url: "/api/placeholder/150/150", description: "Foto headphone rusak" },
      { type: "video", url: "#", description: "Video demonstrasi masalah audio" }
    ],
    notes: "Barang akan dibawa langsung ke toko oleh pelanggan",
    communicationHistory: [
      { time: "2024-01-04T14:30:00", from: "customer", message: "Speaker kiri headphone tidak berfungsi sama sekali." },
      { time: "2024-01-04T16:00:00", from: "store", message: "Mohon kirimkan video yang menunjukkan masalahnya." },
      { time: "2024-01-04T16:45:00", from: "customer", message: "Sudah saya kirimkan video demonstrasinya." }
    ]
  },
  {
    id: "RET-20240103-003",
    orderId: "ORD-20231220-065",
    customer: {
      name: "Clara Natalia",
      email: "<EMAIL>",
      phone: "081267890123",
      avatar: "/api/placeholder/32/32"
    },
    store: {
      name: "Beauty Corner",
      id: "STORE-003"
    },
    returnDate: "2024-01-03T16:10:00",
    returnReason: "Item different from description",
    status: "item_received",
    refundStatus: "approved",
    refundAmount: 350000,
    returnMethod: "courier_pickup",
    pickupSchedule: "2024-01-04T13:00:00",
    trackingNumber: "JNE-RET00123456",
    items: [
      {
        id: "ITEM-103",
        name: "Skincare Serum Anti-aging",
        sku: "BEAUTY-SERUM-002",
        price: 350000,
        quantity: 1,
        returnReason: "Received different variant",
        returnCondition: "unopened",
        image: "/api/placeholder/50/50"
      }
    ],
    timeline: [
      { time: "2024-01-03T16:10:00", status: "requested", note: "Permintaan pengembalian dibuat" },
      { time: "2024-01-03T17:00:00", status: "pending_approval", note: "Menunggu persetujuan dari store" },
      { time: "2024-01-03T18:30:00", status: "approved", note: "Pengembalian disetujui" },
      { time: "2024-01-04T13:15:00", status: "pickup_scheduled", note: "Pengambilan dijadwalkan" },
      { time: "2024-01-05T10:30:00", status: "item_received", note: "Item diterima oleh store" }
    ],
    evidence: [
      { type: "image", url: "/api/placeholder/150/150", description: "Foto produk yang diterima" },
      { type: "image", url: "/api/placeholder/150/150", description: "Foto produk di website" }
    ],
    notes: "Pelanggan mendapatkan varian yang berbeda dari yang dipesan",
    communicationHistory: [
      { time: "2024-01-03T16:30:00", from: "customer", message: "Saya menerima varian untuk kulit berminyak, padahal saya pesan untuk kulit kering." },
      { time: "2024-01-03T17:30:00", from: "store", message: "Mohon maaf atas ketidaknyamanannya, kami akan proses pengembalian." }
    ]
  },
  {
    id: "RET-20240102-004",
    orderId: "ORD-20231218-054",
    customer: {
      name: "Daniel Putra",
      email: "<EMAIL>",
      phone: "081278901234",
      avatar: "/api/placeholder/32/32"
    },
    store: {
      name: "Sports Hub",
      id: "STORE-004"
    },
    returnDate: "2024-01-02T11:25:00",
    returnReason: "Wrong item delivered",
    status: "completed",
    refundStatus: "refunded",
    refundAmount: 1200000,
    refundMethod: "original_payment",
    refundDate: "2024-01-05T09:00:00",
    returnMethod: "drop_off",
    returnLocation: "Sports Hub - Mall Kelapa Gading",
    items: [
      {
        id: "ITEM-104",
        name: "Sepatu Lari Premium",
        sku: "SPORT-SHOES-008",
        price: 1200000,
        quantity: 1,
        returnReason: "Received wrong color",
        returnCondition: "unopened",
        image: "/api/placeholder/50/50"
      }
    ],
    timeline: [
      { time: "2024-01-02T11:25:00", status: "requested", note: "Permintaan pengembalian dibuat" },
      { time: "2024-01-02T13:00:00", status: "pending_approval", note: "Menunggu persetujuan dari store" },
      { time: "2024-01-02T14:30:00", status: "approved", note: "Pengembalian disetujui" },
      { time: "2024-01-03T16:45:00", status: "item_received", note: "Item diterima oleh store" },
      { time: "2024-01-04T10:15:00", status: "refund_approved", note: "Refund disetujui" },
      { time: "2024-01-05T09:00:00", status: "completed", note: "Refund selesai, dana dikembalikan ke pelanggan" }
    ],
    evidence: [
      { type: "image", url: "/api/placeholder/150/150", description: "Foto sepatu yang diterima (warna merah)" }
    ],
    notes: "Pelanggan memesan warna hitam tapi menerima warna merah",
    communicationHistory: [
      { time: "2024-01-02T11:35:00", from: "customer", message: "Saya menerima sepatu warna merah, padahal pesan warna hitam." },
      { time: "2024-01-02T13:30:00", from: "store", message: "Mohon maaf atas kesalahan pengiriman. Kami akan proses pengembaliannya." }
    ]
  },
  {
    id: "RET-20240101-005",
    orderId: "ORD-20231215-042",
    customer: {
      name: "Elsa Mariani",
      email: "<EMAIL>",
      phone: "081289012345",
      avatar: "/api/placeholder/32/32"
    },
    store: {
      name: "Home Decor Store",
      id: "STORE-005"
    },
    returnDate: "2024-01-01T10:05:00",
    returnReason: "Item damaged during shipping",
    status: "rejected",
    refundStatus: "declined",
    returnMethod: "courier_pickup",
    items: [
      {
        id: "ITEM-105",
        name: "Vas Bunga Kristal",
        sku: "HOME-VASE-004",
        price: 750000,
        quantity: 1,
        returnReason: "Received broken",
        returnCondition: "damaged",
        image: "/api/placeholder/50/50"
      }
    ],
    timeline: [
      { time: "2024-01-01T10:05:00", status: "requested", note: "Permintaan pengembalian dibuat" },
      { time: "2024-01-01T11:30:00", status: "pending_approval", note: "Menunggu persetujuan dari store" },
      { time: "2024-01-02T09:45:00", status: "rejected", note: "Pengembalian ditolak - bukti tidak cukup" }
    ],
    evidence: [
      { type: "image", url: "/api/placeholder/150/150", description: "Foto vas bunga yang rusak" }
    ],
    rejectionReason: "Tidak ada bukti kerusakan saat pengiriman. Produk sangat rapuh dan ada indikasi penanganan yang tidak tepat setelah diterima.",
    notes: "Toko menolak permintaan pengembalian karena bukti tidak menunjukkan kerusakan saat pengiriman",
    communicationHistory: [
      { time: "2024-01-01T10:15:00", from: "customer", message: "Vas bunga datang dalam keadaan pecah." },
      { time: "2024-01-01T14:00:00", from: "store", message: "Bisakah Anda mengirimkan foto kotak pengiriman dan packing-nya?" },
      { time: "2024-01-01T15:30:00", from: "customer", message: "Saya sudah membuang kotaknya." },
      { time: "2024-01-02T09:45:00", from: "store", message: "Maaf, kami tidak dapat memproses pengembalian tanpa bukti kerusakan saat pengiriman." }
    ]
  }
]

// Fungsi untuk generate badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "requested":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800"><Clock className="h-3 w-3 mr-1" />Requested</Badge>
    case "pending_approval": 
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Pending Approval</Badge>
    case "approved":
      return <Badge variant="outline" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
    case "pickup_scheduled":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800"><Truck className="h-3 w-3 mr-1" />Pickup Scheduled</Badge>
    case "item_received":
      return <Badge variant="outline" className="bg-purple-100 text-purple-800"><Package className="h-3 w-3 mr-1" />Item Received</Badge>
    case "refund_approved":
      return <Badge variant="outline" className="bg-indigo-100 text-indigo-800"><CircleDollarSign className="h-3 w-3 mr-1" />Refund Approved</Badge>
    case "completed":
      return <Badge variant="outline" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Completed</Badge>
    case "rejected":
      return <Badge variant="destructive"><X className="h-3 w-3 mr-1" />Rejected</Badge>
    case "cancelled":
      return <Badge variant="secondary"><X className="h-3 w-3 mr-1" />Cancelled</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk generate badge refund status
function getRefundBadge(status: string) {
  switch (status) {
    case "pending":
      return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
    case "processing":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800"><Clock className="h-3 w-3 mr-1" />Processing</Badge>
    case "approved":
      return <Badge variant="outline" className="bg-indigo-100 text-indigo-800"><CircleDollarSign className="h-3 w-3 mr-1" />Approved</Badge>
    case "refunded":
      return <Badge variant="outline" className="bg-green-100 text-green-800"><CircleDollarSign className="h-3 w-3 mr-1" />Refunded</Badge>
    case "declined":
      return <Badge variant="destructive"><X className="h-3 w-3 mr-1" />Declined</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

// Fungsi untuk format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric'
  })
}

// Fungsi untuk format waktu
function formatDateTime(dateString: string) {
  return new Date(dateString).toLocaleString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function OrderReturnsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedReturn, setSelectedReturn] = useState<string | null>(null)

  // Filter returns berdasarkan status dan pencarian
  const filteredReturns = returns.filter(returnItem => {
    const matchesSearch = returnItem.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          returnItem.orderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          returnItem.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          returnItem.store.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || returnItem.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Menghitung statistik returns
  const stats = {
    total: returns.length,
    requested: returns.filter(r => r.status === "requested").length,
    pendingApproval: returns.filter(r => r.status === "pending_approval").length,
    approved: returns.filter(r => r.status === "approved").length,
    pickupScheduled: returns.filter(r => r.status === "pickup_scheduled").length,
    itemReceived: returns.filter(r => r.status === "item_received").length,
    completed: returns.filter(r => r.status === "completed").length,
    rejected: returns.filter(r => r.status === "rejected").length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/orders">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order Returns</h1>
            <p className="text-muted-foreground">
              Kelola permintaan pengembalian produk dan proses refund
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Returns</CardTitle>
            <Undo2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
            <ClipboardList className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pendingApproval}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
            <Package className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {stats.approved + stats.pickupScheduled + stats.itemReceived}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed/Rejected</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats.completed + stats.rejected}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari return ID, order ID, customer, atau store..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="requested">Requested</option>
              <option value="pending_approval">Pending Approval</option>
              <option value="approved">Approved</option>
              <option value="pickup_scheduled">Pickup Scheduled</option>
              <option value="item_received">Item Received</option>
              <option value="refund_approved">Refund Approved</option>
              <option value="completed">Completed</option>
              <option value="rejected">Rejected</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Returns List */}
      <div className="space-y-4">
        {filteredReturns.map((returnItem) => (
          <Card key={returnItem.id} className={`hover:shadow-md transition-shadow ${selectedReturn === returnItem.id ? 'ring-2 ring-primary' : ''}`}>
            <CardContent className="p-6">
              <div className="flex flex-col gap-4">
                {/* Return Header */}
                <div className="flex flex-col md:flex-row justify-between gap-4">
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="text-lg font-semibold">{returnItem.id}</h3>
                      <span className="text-sm text-muted-foreground">({returnItem.orderId})</span>
                    </div>
                    <div className="flex items-center gap-3 mt-1">
                      {getStatusBadge(returnItem.status)}
                      {returnItem.refundStatus && getRefundBadge(returnItem.refundStatus)}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-muted-foreground text-sm">Return Date: {formatDate(returnItem.returnDate)}</p>
                    {returnItem.refundAmount && (
                      <p className="font-semibold text-green-600 mt-1">Refund: {formatCurrency(returnItem.refundAmount)}</p>
                    )}
                  </div>
                </div>

                {/* Return Info */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                  {/* Customer */}
                  <div className="flex items-start gap-3">
                    <div className="h-8 w-8 rounded-full overflow-hidden bg-gray-100">
                      <img src={returnItem.customer.avatar} alt={returnItem.customer.name} className="h-full w-full object-cover" />
                    </div>
                    <div>
                      <h4 className="font-medium flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        {returnItem.customer.name}
                      </h4>
                      <p className="text-sm text-muted-foreground">{returnItem.customer.email}</p>
                      <p className="text-sm text-muted-foreground">{returnItem.customer.phone}</p>
                    </div>
                  </div>

                  {/* Store */}
                  <div>
                    <h4 className="font-medium flex items-center gap-2">
                      <Store className="h-4 w-4 text-muted-foreground" />
                      Store
                    </h4>
                    <p className="text-sm">{returnItem.store.name}</p>
                    <p className="text-sm text-muted-foreground">ID: {returnItem.store.id}</p>
                  </div>

                  {/* Return Method */}
                  <div>
                    <h4 className="font-medium flex items-center gap-2">
                      <Undo2 className="h-4 w-4 text-muted-foreground" />
                      Metode Return
                    </h4>
                    <p className="text-sm capitalize">{returnItem.returnMethod?.replace(/_/g, ' ')}</p>
                    {returnItem.pickupSchedule && (
                      <p className="text-sm text-muted-foreground">Pickup: {formatDate(returnItem.pickupSchedule)}</p>
                    )}
                    {returnItem.trackingNumber && (
                      <p className="text-sm font-medium">#{returnItem.trackingNumber}</p>
                    )}
                    {returnItem.returnLocation && (
                      <p className="text-sm text-muted-foreground">{returnItem.returnLocation}</p>
                    )}
                  </div>
                </div>

                {/* Return Reason */}
                <div className="p-3 bg-muted/50 rounded-md">
                  <h4 className="font-medium mb-2">Alasan Pengembalian</h4>
                  <p className="text-sm">{returnItem.returnReason}</p>
                </div>

                {/* Items */}
                <div className="mt-2">
                  <h4 className="font-medium mb-2">Items</h4>
                  <div className="space-y-3">
                    {returnItem.items.map((item) => (
                      <div key={item.id} className="flex items-center gap-3 bg-muted/50 p-3 rounded-md">
                        <div className="h-16 w-16 rounded overflow-hidden bg-muted">
                          <img src={item.image} alt={item.name} className="h-full w-full object-cover" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="font-medium truncate">{item.name}</p>
                          <div className="flex justify-between text-sm text-muted-foreground mb-1">
                            <span>SKU: {item.sku}</span>
                            <span>{item.quantity} x {formatCurrency(item.price)}</span>
                          </div>
                          <div className="text-sm">
                            <p><span className="text-muted-foreground">Alasan:</span> {item.returnReason}</p>
                            <p><span className="text-muted-foreground">Kondisi:</span> <span className="capitalize">{item.returnCondition}</span></p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Evidence */}
                {returnItem.evidence && returnItem.evidence.length > 0 && (
                  <div className="mt-2">
                    <h4 className="font-medium mb-2">Bukti</h4>
                    <div className="flex flex-wrap gap-3">
                      {returnItem.evidence.map((evidence, index) => (
                        <div key={index} className="w-[150px]">
                          {evidence.type === "image" ? (
                            <div className="relative h-[100px] w-full bg-muted rounded-md overflow-hidden">
                              <img src={evidence.url} alt={evidence.description || "Evidence"} className="h-full w-full object-cover" />
                            </div>
                          ) : (
                            <div className="relative h-[100px] w-full bg-muted rounded-md overflow-hidden flex items-center justify-center">
                              <ImageIcon className="h-8 w-8 text-muted-foreground" />
                            </div>
                          )}
                          <p className="text-xs text-muted-foreground mt-1 truncate">{evidence.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Timeline */}
                {returnItem.timeline.length > 0 && (
                  <div className="mt-2">
                    <h4 className="font-medium flex items-center gap-2 mb-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      Timeline
                    </h4>
                    <div className="space-y-3">
                      {returnItem.timeline.map((event, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <div className="h-5 w-5 rounded-full bg-primary flex items-center justify-center text-white text-xs">
                            {index + 1}
                          </div>
                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row sm:justify-between">
                              <p className="font-medium text-sm capitalize">
                                {event.status.replace(/_/g, ' ')}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {formatDateTime(event.time)}
                              </p>
                            </div>
                            <p className="text-sm text-muted-foreground">{event.note}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Communication */}
                {returnItem.communicationHistory && returnItem.communicationHistory.length > 0 && (
                  <div className="mt-2">
                    <h4 className="font-medium flex items-center gap-2 mb-2">
                      <MessageSquare className="h-4 w-4 text-muted-foreground" />
                      Komunikasi
                    </h4>
                    <div className="space-y-3">
                      {returnItem.communicationHistory.map((msg, index) => (
                        <div 
                          key={index} 
                          className={`p-3 rounded-md ${
                            msg.from === 'customer' 
                              ? 'bg-blue-50 border border-blue-100 ml-8' 
                              : 'bg-gray-50 border border-gray-100 mr-8'
                          }`}
                        >
                          <div className="flex justify-between mb-1">
                            <p className="text-sm font-medium capitalize">
                              {msg.from}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {formatDateTime(msg.time)}
                            </p>
                          </div>
                          <p className="text-sm">{msg.message}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Notes */}
                {returnItem.notes && (
                  <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <h4 className="text-sm font-medium text-yellow-800 mb-1">Notes</h4>
                    <p className="text-sm">{returnItem.notes}</p>
                  </div>
                )}

                {/* Rejection Reason */}
                {returnItem.rejectionReason && (
                  <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                    <h4 className="text-sm font-medium text-red-800 mb-1">Rejection Reason</h4>
                    <p className="text-sm">{returnItem.rejectionReason}</p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex flex-wrap gap-2 pt-4 border-t">
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>

                  {returnItem.status === "requested" || returnItem.status === "pending_approval" && (
                    <>
                      <Button size="sm" className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve Return
                      </Button>
                      <Button size="sm" variant="destructive">
                        <X className="h-4 w-4 mr-2" />
                        Reject Return
                      </Button>
                    </>
                  )}

                  {returnItem.status === "approved" && (
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      <Truck className="h-4 w-4 mr-2" />
                      Schedule Pickup
                    </Button>
                  )}

                  {returnItem.status === "item_received" && (
                    <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
                      <CircleDollarSign className="h-4 w-4 mr-2" />
                      Process Refund
                    </Button>
                  )}

                  <Button size="sm" variant="outline">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Add Note
                  </Button>

                  <Button size="sm" variant="ghost">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredReturns.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Undo2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Tidak ada permintaan pengembalian ditemukan</h3>
            <p className="text-muted-foreground mb-4">
              Tidak ada permintaan pengembalian yang cocok dengan filter Anda
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 