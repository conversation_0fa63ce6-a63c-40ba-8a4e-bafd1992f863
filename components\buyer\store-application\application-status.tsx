"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Bell, CheckCircle, Clock, ExternalLink, FileText, HelpCircle, Info, X } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Switch } from "@/components/ui/switch"
import Link from "next/link"

interface ApplicationStatusProps {
  applicationId: string
  status: "under_review" | "approved" | "needs_info" | "rejected"
  submittedDate?: string
  additionalInfo?: {
    message?: string
    requestedDocuments?: string[]
    rejectionReason?: string
    estimatedReviewTime?: string
  }
}

export function ApplicationStatus({
  applicationId,
  status,
  submittedDate = "2023-05-15",
  additionalInfo,
}: ApplicationStatusProps) {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    // Simulasi progress review
    if (status === "under_review") {
      const timer = setTimeout(() => {
        setProgress(Math.min(progress + 5, 90))
      }, 1000)
      return () => clearTimeout(timer)
    } else if (status === "approved") {
      setProgress(100)
    } else if (status === "needs_info") {
      setProgress(70)
    } else if (status === "rejected") {
      setProgress(100)
    }
  }, [progress, status])

  const getStatusBadge = () => {
    switch (status) {
      case "under_review":
        return <Badge className="bg-amber-500 dark:bg-amber-600">Sedang Ditinjau</Badge>
      case "approved":
        return <Badge className="bg-green-500 dark:bg-green-600">Disetujui</Badge>
      case "needs_info":
        return <Badge className="bg-blue-500 dark:bg-blue-600">Membutuhkan Informasi</Badge>
      case "rejected":
        return <Badge variant="destructive">Ditolak</Badge>
      default:
        return null
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case "under_review":
        return <Clock className="h-12 w-12 text-amber-500 dark:text-amber-400" />
      case "approved":
        return <CheckCircle className="h-12 w-12 text-green-500 dark:text-green-400" />
      case "needs_info":
        return <Info className="h-12 w-12 text-blue-500 dark:text-blue-400" />
      case "rejected":
        return <X className="h-12 w-12 text-red-500 dark:text-red-400" />
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Status Aplikasi Toko</CardTitle>
              <CardDescription>
                ID Aplikasi: {applicationId} • Diajukan pada: {submittedDate}
              </CardDescription>
            </div>
            <div className="flex items-center justify-center p-3 rounded-full bg-muted/30">{getStatusIcon()}</div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-medium">Status</h3>
                {getStatusBadge()}
              </div>
              {status === "under_review" && (
                <p className="text-sm text-muted-foreground">
                  Estimasi waktu review: {additionalInfo?.estimatedReviewTime || "2-3 hari kerja"}
                </p>
              )}
            </div>

            <Progress value={progress} className="h-2" />

            {status === "under_review" && (
              <p className="text-sm text-muted-foreground">
                Tim kami sedang meninjau aplikasi Anda. Kami akan memberi tahu Anda segera setelah proses selesai.
              </p>
            )}
          </div>

          {status === "needs_info" && additionalInfo?.message && (
            <Alert>
              <HelpCircle className="h-4 w-4" />
              <AlertTitle>Informasi Tambahan Diperlukan</AlertTitle>
              <AlertDescription>
                {additionalInfo.message}
                {additionalInfo.requestedDocuments && additionalInfo.requestedDocuments.length > 0 && (
                  <div className="mt-2">
                    <p className="font-medium">Dokumen yang diperlukan:</p>
                    <ul className="list-disc pl-5 mt-1">
                      {additionalInfo.requestedDocuments.map((doc, index) => (
                        <li key={index}>{doc}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </AlertDescription>
              <div className="mt-3">
                <Button size="sm">Kirim Informasi Tambahan</Button>
              </div>
            </Alert>
          )}

          {status === "rejected" && additionalInfo?.rejectionReason && (
            <Alert variant="destructive">
              <X className="h-4 w-4" />
              <AlertTitle>Aplikasi Ditolak</AlertTitle>
              <AlertDescription>{additionalInfo.rejectionReason}</AlertDescription>
              <div className="mt-3">
                <Button size="sm" variant="outline">
                  Ajukan Banding
                </Button>
                <Button size="sm" className="ml-2">
                  Ajukan Aplikasi Baru
                </Button>
              </div>
            </Alert>
          )}

          {status === "approved" && (
            <Alert className="bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-900">
              <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
              <AlertTitle className="text-green-800 dark:text-green-400">Selamat! Aplikasi Anda Disetujui</AlertTitle>
              <AlertDescription className="text-green-700 dark:text-green-400/80">
                Toko Anda telah disetujui dan siap untuk mulai menjual. Klik tombol di bawah untuk mengakses dashboard
                toko Anda.
              </AlertDescription>
              <div className="mt-3">
                <Button size="sm" className="bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800">
                  <Link href="/store/dashboard" className="flex items-center gap-1">
                    Ke Dashboard Toko
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </Alert>
          )}

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                <h3 className="font-medium">Pengaturan Notifikasi</h3>
              </div>
              <Switch checked={notificationsEnabled} onCheckedChange={setNotificationsEnabled} />
            </div>
            <p className="text-sm text-muted-foreground">
              {notificationsEnabled
                ? "Anda akan menerima notifikasi tentang perubahan status aplikasi Anda."
                : "Anda tidak akan menerima notifikasi tentang perubahan status aplikasi Anda."}
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col items-start gap-2">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">Anda dapat melihat detail lengkap aplikasi Anda kapan saja.</p>
          </div>
          <Button variant="outline" size="sm" className="mt-2">
            Lihat Detail Aplikasi
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
