"use client"

import { useState, useEffect } from "react"
import { ShoppingCart, MessageSquare, Search } from "lucide-react"

export default function PreviewVelozioHeader() {
  const [placeholderIndex, setPlaceholderIndex] = useState(0)
  const placeholders = [
    "Handphone Samsung",
    "Sepatu Pria",
    "Tas Wanita",
    "Promo Elektronik",
    "Laptop Gaming",
    "Kamera Mirrorless",
    "Smart TV",
    "Headphone Bluetooth",
    "Mainan Anak",
    "Perabotan Rumah",
    "Kosmetik",
    "Buku Terlaris",
    "Alat Olahraga",
    "Aksesoris Fashion",
    "Voucher Game",
  ]

  // Animation for placeholder text - with longer duration
  useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prevIndex) => (prevIndex + 1) % placeholders.length)
    }, 3000) // 3 seconds per item for demo purposes
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="min-h-screen bg-gray-100 pt-16">
      <header className="bg-[#ee4d2d] py-3 px-4 font-['Roboto',Arial,sans-serif] fixed top-0 left-0 w-full z-50">
        <div className="max-w-7xl mx-auto flex items-center">
          {/* Logo */}
          <a href="#" className="text-white font-bold text-xl md:text-xl mr-3">
            Velozio
          </a>

          {/* Search Bar */}
          <div className="relative flex-grow max-w-3xl mx-auto">
            <div className="relative">
              <input
                type="text"
                className="w-full py-2 px-4 pr-12 rounded-lg border-2 border-[#ee4d2d] focus:ring-2 focus:ring-white/30 placeholder-transparent"
                placeholder=" "
              />
              {/* Animated placeholder */}
              <div className="absolute left-4 top-1/2 -translate-y-1/2 pointer-events-none text-gray-400">
                {placeholders[placeholderIndex]}
              </div>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <Search className="h-5 w-5 text-[#ee4d2d]" />
              </div>
            </div>
          </div>

          {/* Icons */}
          <div className="flex items-center ml-4 space-x-5">
            <div className="relative">
              <ShoppingCart className="h-6 w-6 text-white cursor-pointer" strokeWidth={2.5} />
              <span className="absolute -top-2 -right-2 bg-white text-[#ee4d2d] text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                5
              </span>
            </div>
            <div className="relative">
              <div className="relative">
                <MessageSquare className="h-6 w-6 text-white cursor-pointer" strokeWidth={2.5} />
                {/* Custom chat dots */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex justify-between w-2.5 pointer-events-none">
                  <div className="w-0.5 h-0.5 bg-white rounded-full"></div>
                  <div className="w-0.5 h-0.5 bg-white rounded-full"></div>
                  <div className="w-0.5 h-0.5 bg-white rounded-full"></div>
                </div>
              </div>
              <span className="absolute -top-2 -right-2 bg-white text-[#ee4d2d] text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                3
              </span>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto p-4 md:p-6">
        <div className="bg-white rounded-lg p-6 mt-6 shadow-sm">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Velozio E-commerce Theme</h1>
          <p className="text-gray-600">
            Preview header komponen Velozio dengan placeholder text beranimasi, badge notifikasi, dan desain responsif.
          </p>
        </div>
      </main>
    </div>
  )
}
