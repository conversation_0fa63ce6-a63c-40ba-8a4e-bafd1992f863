const fs = require("fs")
const path = require("path")
const { execSync } = require("child_process")

// Cari semua file yang menggunakan useNotifications
try {
  console.log("Mencari file yang menggunakan useNotifications...")
  const result = execSync('grep -r "useNotifications" --include="*.tsx" --include="*.ts" .').toString()

  const lines = result.split("\n").filter((line) => line.includes("useNotifications"))

  console.log(`\nDitemukan ${lines.length} penggunaan useNotifications:`)
  lines.forEach((line) => {
    console.log(line)
  })

  console.log("\nPastikan semua komponen yang menggunakan useNotifications:")
  console.log('1. Memiliki "use client" directive')
  console.log("2. Dibungkus dengan NotificationsProvider")
  console.log("3. Atau dipisahkan menjadi server dan client component")
} catch (error) {
  console.error("Error:", error.message)
}
