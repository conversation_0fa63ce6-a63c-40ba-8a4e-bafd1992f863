"use client"

import type React from "react"
import { RefreshCw, Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { FilterPanel } from "./filter-panel"
import { ViewToggle } from "./view-toggle"
import { BulkActions } from "./bulk-actions"
import { ColumnVisibilityToggle } from "./column-visibility-toggle"
import { QuickActions } from "./quick-actions"

interface FilterOption {
  id: string
  label: string
  type: "text" | "select" | "date" | "number" | "boolean"
  options?: { value: string; label: string }[]
}

interface Column {
  id: string
  label: string
}

interface BulkAction {
  id: string
  label: string
  icon?: React.ReactNode
  destructive?: boolean
  onClick: (selectedIds: string[]) => void
}

interface QuickAction {
  id: string
  label: string
  icon?: React.ReactNode
  onClick: () => void
}

interface DataTableToolbarProps {
  searchValue: string
  onSearchChange: (value: string) => void
  filterOptions?: FilterOption[]
  onFilterChange?: (filters: Record<string, any>) => void
  viewMode?: "list" | "grid" | "card"
  onViewModeChange?: (mode: "list" | "grid" | "card") => void
  selectedIds?: string[]
  bulkActions?: BulkAction[]
  onExport?: (format: "csv" | "excel" | "pdf") => void
  columns?: Column[]
  visibleColumns?: string[]
  onColumnVisibilityChange?: (columnId: string, isVisible: boolean) => void
  primaryAction?: QuickAction
  quickActions?: QuickAction[]
  onRefresh?: () => void
  className?: string
}

export function DataTableToolbar({
  searchValue,
  onSearchChange,
  filterOptions,
  onFilterChange,
  viewMode = "list",
  onViewModeChange,
  selectedIds = [],
  bulkActions = [],
  onExport,
  columns = [],
  visibleColumns = [],
  onColumnVisibilityChange,
  primaryAction,
  quickActions = [],
  onRefresh,
  className,
}: DataTableToolbarProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center gap-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search..."
              value={searchValue}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-8"
            />
          </div>
          {filterOptions && onFilterChange && (
            <FilterPanel filterOptions={filterOptions} onFilterChange={onFilterChange} />
          )}
          {onRefresh && (
            <Button variant="outline" size="icon" onClick={onRefresh} title="Refresh data">
              <RefreshCw className="h-4 w-4" />
            </Button>
          )}
        </div>
        <div className="flex flex-wrap items-center gap-2">
          {selectedIds.length > 0 && bulkActions.length > 0 && (
            <BulkActions selectedIds={selectedIds} actions={bulkActions} onExport={onExport} />
          )}
          {columns.length > 0 && visibleColumns && onColumnVisibilityChange && (
            <ColumnVisibilityToggle
              columns={columns}
              visibleColumns={visibleColumns}
              onColumnVisibilityChange={onColumnVisibilityChange}
            />
          )}
          {onViewModeChange && <ViewToggle value={viewMode} onValueChange={onViewModeChange} />}
          <QuickActions primaryAction={primaryAction} actions={quickActions} />
        </div>
      </div>
    </div>
  )
}
