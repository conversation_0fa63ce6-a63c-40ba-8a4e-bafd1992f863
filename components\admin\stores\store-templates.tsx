"use client"

import { useState } from "react"
import { Search, Plus, Eye, Settings, Check } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Mock data for store templates
const mockTemplates = [
  {
    id: "template-1",
    name: "Modern Boutique",
    description: "A clean, modern template for fashion and lifestyle stores.",
    thumbnail: "/modern-boutique-store.png",
    category: "Fashion",
    components: ["Hero Banner", "Featured Products", "Category Grid", "Newsletter", "Instagram Feed"],
    layouts: ["Single Column", "Two Column", "Full Width"],
    availableFor: ["Free", "Basic", "Premium"],
    popularity: 85,
  },
  {
    id: "template-2",
    name: "Tech Store",
    description: "Optimized for electronics and tech product stores with comparison features.",
    thumbnail: "/tech-store-template.png",
    category: "Electronics",
    components: ["Product Comparison", "Spec Sheets", "Featured Products", "Reviews", "FAQ Section"],
    layouts: ["Grid Layout", "Feature Focus", "Spec Comparison"],
    availableFor: ["Basic", "Premium"],
    popularity: 72,
  },
  {
    id: "template-3",
    name: "Food & Grocery",
    description: "Perfect for food stores with recipe integration and nutrition information.",
    thumbnail: "/food-grocery-store-template.png",
    category: "Food & Beverage",
    components: ["Recipe Cards", "Nutrition Info", "Meal Planner", "Shopping List", "Seasonal Specials"],
    layouts: ["Recipe Grid", "Product Showcase", "Nutrition Focus"],
    availableFor: ["Premium"],
    popularity: 68,
  },
  {
    id: "template-4",
    name: "Handmade Crafts",
    description: "Showcase artisanal products with story-focused layouts.",
    thumbnail: "/handmade-crafts-store.png",
    category: "Arts & Crafts",
    components: ["Artisan Stories", "Process Showcase", "Custom Orders", "Workshop Calendar", "Material Sourcing"],
    layouts: ["Story Focus", "Gallery Grid", "Process Timeline"],
    availableFor: ["Free", "Basic", "Premium"],
    popularity: 76,
  },
  {
    id: "template-5",
    name: "Minimalist",
    description: "A clean, minimalist design that puts your products front and center.",
    thumbnail: "/minimalist-store-template.png",
    category: "General",
    components: ["Product Focus", "Minimal Navigation", "Quick View", "Simple Checkout", "Clean Footer"],
    layouts: ["Minimal Grid", "White Space Focus", "Product Spotlight"],
    availableFor: ["Free", "Basic", "Premium"],
    popularity: 92,
  },
  {
    id: "template-6",
    name: "Vintage Shop",
    description: "Retro-inspired design perfect for antiques and vintage items.",
    thumbnail: "/vintage-shop-template-retro.png",
    category: "Antiques & Vintage",
    components: ["Era Categories", "Item History", "Condition Ratings", "Authentication", "Era Timeline"],
    layouts: ["Vintage Gallery", "Timeline View", "Collection Showcase"],
    availableFor: ["Basic", "Premium"],
    popularity: 65,
  },
]

// Subscription plan options
const subscriptionPlans = ["Free", "Basic", "Premium"]

// Template card component
const TemplateCard = ({
  template,
  onSelect,
}: {
  template: (typeof mockTemplates)[0]
  onSelect: (template: (typeof mockTemplates)[0]) => void
}) => {
  return (
    <Card className="overflow-hidden">
      <div className="aspect-video w-full overflow-hidden">
        <img
          src={template.thumbnail || "/placeholder.svg"}
          alt={template.name}
          className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
        />
      </div>
      <CardHeader className="p-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{template.name}</CardTitle>
          <Badge variant="outline">{template.category}</Badge>
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <p className="line-clamp-2 text-sm text-muted-foreground">{template.description}</p>
        <div className="mt-2 flex flex-wrap gap-1">
          {template.availableFor.map((plan) => (
            <Badge key={plan} variant="secondary" className="text-xs">
              {plan}
            </Badge>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between p-4 pt-0">
        <Button variant="ghost" size="sm" className="gap-1" onClick={() => onSelect(template)}>
          <Settings className="h-4 w-4" />
          Configure
        </Button>
        <Button variant="outline" size="sm" className="gap-1">
          <Eye className="h-4 w-4" />
          Preview
        </Button>
      </CardFooter>
    </Card>
  )
}

// Template settings component
const TemplateSettings = ({
  template,
  onClose,
}: {
  template: (typeof mockTemplates)[0]
  onClose: () => void
}) => {
  const [selectedPlans, setSelectedPlans] = useState<string[]>(template.availableFor)

  const togglePlan = (plan: string) => {
    if (selectedPlans.includes(plan)) {
      setSelectedPlans(selectedPlans.filter((p) => p !== plan))
    } else {
      setSelectedPlans([...selectedPlans, plan])
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <img
          src={template.thumbnail || "/placeholder.svg"}
          alt={template.name}
          className="h-20 w-32 rounded-md object-cover"
        />
        <div>
          <h3 className="text-xl font-medium">{template.name}</h3>
          <p className="text-sm text-muted-foreground">{template.category}</p>
        </div>
      </div>

      <Tabs defaultValue="availability">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="availability">Availability</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="layouts">Layouts</TabsTrigger>
        </TabsList>

        <TabsContent value="availability" className="space-y-4">
          <div>
            <h4 className="mb-2 text-sm font-medium">Available for Plans</h4>
            <div className="space-y-2">
              {subscriptionPlans.map((plan) => (
                <div key={plan} className="flex items-center space-x-2">
                  <Checkbox
                    id={`plan-${plan}`}
                    checked={selectedPlans.includes(plan)}
                    onCheckedChange={() => togglePlan(plan)}
                  />
                  <Label htmlFor={`plan-${plan}`}>{plan} Plan</Label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h4 className="mb-2 text-sm font-medium">Default Template</h4>
            <div className="space-y-2">
              {subscriptionPlans.map((plan) => (
                <div key={`default-${plan}`} className="flex items-center space-x-2">
                  <Checkbox id={`default-${plan}`} />
                  <Label htmlFor={`default-${plan}`}>Default for {plan} Plan</Label>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="components" className="space-y-4">
          <div>
            <h4 className="mb-2 text-sm font-medium">Included Components</h4>
            <div className="space-y-2">
              {template.components.map((component, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Checkbox id={`component-${index}`} defaultChecked />
                  <Label htmlFor={`component-${index}`}>{component}</Label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h4 className="mb-2 text-sm font-medium">Add Component</h4>
            <div className="flex gap-2">
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select component" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="product-grid">Product Grid</SelectItem>
                  <SelectItem value="testimonials">Testimonials</SelectItem>
                  <SelectItem value="blog-section">Blog Section</SelectItem>
                  <SelectItem value="contact-form">Contact Form</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="layouts" className="space-y-4">
          <div>
            <h4 className="mb-2 text-sm font-medium">Available Layouts</h4>
            <div className="space-y-2">
              {template.layouts.map((layout, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Checkbox id={`layout-${index}`} defaultChecked />
                  <Label htmlFor={`layout-${index}`}>{layout}</Label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h4 className="mb-2 text-sm font-medium">Default Layout</h4>
            <Select defaultValue={template.layouts[0]}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {template.layouts.map((layout, index) => (
                  <SelectItem key={index} value={layout}>
                    {layout}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </TabsContent>
      </Tabs>

      <Separator />

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button className="gap-1">
          <Check className="h-4 w-4" />
          Save Changes
        </Button>
      </div>
    </div>
  )
}

export function StoreTemplates() {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [selectedTemplate, setSelectedTemplate] = useState<(typeof mockTemplates)[0] | null>(null)

  // Get unique categories for filter
  const categories = Array.from(new Set(mockTemplates.map((template) => template.category)))

  // Filter templates based on search term and category filter
  const filteredTemplates = mockTemplates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = categoryFilter === "all" || template.category === categoryFilter

    return matchesSearch && matchesCategory
  })

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search templates..."
              className="w-full pl-8 md:w-[300px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Button className="gap-1">
          <Plus className="h-4 w-4" />
          Create Template
        </Button>
      </div>

      {selectedTemplate ? (
        <Card>
          <CardContent className="p-6">
            <TemplateSettings template={selectedTemplate} onClose={() => setSelectedTemplate(null)} />
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {filteredTemplates.map((template) => (
            <TemplateCard key={template.id} template={template} onSelect={setSelectedTemplate} />
          ))}
        </div>
      )}
    </div>
  )
}
