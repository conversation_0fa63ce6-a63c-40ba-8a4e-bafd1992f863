// Test script untuk fitur prediksi keyword di Sellzio
// Jalankan di browser console saat berada di halaman /sellzio

console.log('🧪 Testing Keyword Prediction Feature...');

// Test 1: Check if prediction container exists
const predictionContainer = document.querySelector('.keyword-predictions');
console.log('✅ Test 1 - Prediction container exists:', !!predictionContainer);

// Test 2: Check if search input exists
const searchInput = document.querySelector('.search-input');
console.log('✅ Test 2 - Search input exists:', !!searchInput);

// Test 3: Check if suggestions container exists
const suggestionsContainer = document.querySelector('.suggestions-container');
console.log('✅ Test 3 - Suggestions container exists:', !!suggestionsContainer);

// Test 4: Simulate typing in search input
if (searchInput) {
  console.log('🔍 Test 4 - Simulating typing "tas"...');
  
  // Focus on search input first
  searchInput.focus();
  
  // Simulate typing
  searchInput.value = 'tas';
  searchInput.dispatchEvent(new Event('input', { bubbles: true }));
  
  setTimeout(() => {
    const predictions = document.querySelectorAll('.prediction-item');
    console.log('✅ Test 4 - Predictions found:', predictions.length);
    
    if (predictions.length > 0) {
      console.log('📝 Prediction examples:');
      predictions.forEach((pred, index) => {
        const text = pred.querySelector('.prediction-text')?.textContent;
        const icon = pred.querySelector('.prediction-icon i')?.className;
        console.log(`  ${index + 1}. "${text}" (${icon})`);
      });
    }
  }, 100);
}

// Test 5: Check CSS classes
setTimeout(() => {
  console.log('🎨 Test 5 - CSS Classes check:');
  console.log('  - .keyword-predictions:', !!document.querySelector('.keyword-predictions'));
  console.log('  - .prediction-item:', !!document.querySelector('.prediction-item'));
  console.log('  - .prediction-icon:', !!document.querySelector('.prediction-icon'));
  console.log('  - .prediction-text:', !!document.querySelector('.prediction-text'));
  console.log('  - .highlighted:', !!document.querySelector('.highlighted'));
}, 200);

// Test 6: Check if suggestions are hidden when predictions show
setTimeout(() => {
  const suggestionsVisible = suggestionsContainer && 
    window.getComputedStyle(suggestionsContainer).display !== 'none';
  const predictionsVisible = predictionContainer && 
    window.getComputedStyle(predictionContainer).display !== 'none';
  
  console.log('👁️ Test 6 - Visibility check:');
  console.log('  - Suggestions visible:', suggestionsVisible);
  console.log('  - Predictions visible:', predictionsVisible);
  console.log('  - Correct behavior:', !suggestionsVisible && predictionsVisible);
}, 300);

// Test 7: Test clear input
setTimeout(() => {
  if (searchInput) {
    console.log('🧹 Test 7 - Testing clear input...');
    searchInput.value = '';
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    setTimeout(() => {
      const suggestionsVisible = suggestionsContainer && 
        window.getComputedStyle(suggestionsContainer).display !== 'none';
      const predictionsVisible = predictionContainer && 
        window.getComputedStyle(predictionContainer).display !== 'none';
      
      console.log('✅ Test 7 - After clear:');
      console.log('  - Suggestions visible:', suggestionsVisible);
      console.log('  - Predictions visible:', predictionsVisible);
      console.log('  - Correct behavior:', suggestionsVisible && !predictionsVisible);
    }, 100);
  }
}, 500);

console.log('🏁 Test completed! Check results above.');
