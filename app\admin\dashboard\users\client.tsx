"use client"

import { useState, useEffect } from "react"
import { UserList } from "@/components/admin/users/user-list"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { useRouter } from "next/navigation"
import { ErrorBoundary } from "@/components/error-boundary"

export default function UsersClient() {
  const router = useRouter()
  const [isLoaded, setIsLoaded] = useState(false)

  // Simulasi loading state untuk memastikan komponen dirender dengan benar
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  if (!isLoaded) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Users</h1>
        <p>Loading users list...</p>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Users</h1>
          <Button onClick={() => router.push("/admin/dashboard/users/create")}>
            <Plus className="mr-2 h-4 w-4" /> Add User
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Users</CardTitle>
          </CardHeader>
          <CardContent>
            <UserListWrapper />
          </CardContent>
        </Card>
      </div>
    </ErrorBoundary>
  )
}

// Wrapper component untuk UserList untuk mengisolasi error
function UserListWrapper() {
  return (
    <ErrorBoundary
      fallback={
        <div className="p-4 border border-red-200 rounded bg-red-50 text-red-700">
          <h3 className="font-bold mb-2">Error loading user list</h3>
          <p>There was a problem loading the user list. Please try refreshing the page.</p>
          <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
            Refresh Page
          </Button>
        </div>
      }
    >
      <UserList />
    </ErrorBoundary>
  )
}
