"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Store,
  ShoppingBag,
  FileText,
  Users,
  UserPlus,
  BarChart,
  Settings,
  Shield,
  Server,
  MessageSquare,
  CreditCard,
} from "lucide-react"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"
import { ModeToggle } from "@/components/mode-toggle"
import { UserMenu } from "@/components/dashboard/user-menu"

const items = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Toko",
    href: "/dashboard/stores",
    icon: Store,
  },
  {
    title: "Produk",
    href: "/dashboard/products",
    icon: ShoppingBag,
  },
  {
    title: "<PERSON><PERSON><PERSON>",
    href: "/dashboard/orders",
    icon: FileText,
  },
  {
    title: "<PERSON><PERSON><PERSON><PERSON>",
    href: "/dashboard/customers",
    icon: Users,
  },
  {
    title: "Affiliate",
    href: "/dashboard/affiliates",
    icon: UserPlus,
  },
  {
    title: "Analitik",
    href: "/dashboard/analytics",
    icon: BarChart,
  },
  {
    title: "Pembayaran",
    href: "/dashboard/payments",
    icon: CreditCard,
  },
  {
    title: "Chat",
    href: "/dashboard/chat",
    icon: MessageSquare,
  },
  {
    title: "Keamanan",
    href: "/dashboard/security",
    icon: Shield,
  },
  {
    title: "Server",
    href: "/dashboard/server",
    icon: Server,
  },
  {
    title: "Pengaturan",
    href: "/dashboard/settings",
    icon: Settings,
  },
]

export function DashboardSidebar() {
  const pathname = usePathname()
  const { state } = useSidebar()
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)

  return (
    <Sidebar className="border-r border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <SidebarHeader className="flex h-14 items-center border-b border-border/40 px-4">
        <Link href="/dashboard" className="flex items-center gap-2">
          <div className="flex h-7 w-7 items-center justify-center rounded-full bg-primary">
            <span className="text-xs font-bold text-primary-foreground">SZ</span>
          </div>
          <span className={cn("font-bold transition-opacity", state === "collapsed" ? "opacity-0" : "opacity-100")}>
            Sellzio
          </span>
        </Link>
        <div className="ml-auto flex items-center gap-1">
          <SidebarTrigger />
        </div>
      </SidebarHeader>
      <SidebarContent className="px-2 py-2">
        <SidebarMenu>
          {items.map((item) => (
            <SidebarMenuItem
              key={item.href}
              onMouseEnter={() => setHoveredItem(item.href)}
              onMouseLeave={() => setHoveredItem(null)}
            >
              <SidebarMenuButton
                asChild
                isActive={pathname === item.href}
                className={cn(
                  "transition-all duration-200 hover:bg-accent/50",
                  pathname === item.href ? "bg-accent text-accent-foreground" : "text-muted-foreground",
                  hoveredItem === item.href && pathname !== item.href ? "text-foreground" : "",
                )}
              >
                <Link href={item.href} className="flex items-center gap-2">
                  <item.icon
                    className={cn(
                      "h-4 w-4",
                      pathname === item.href ? "text-primary" : "text-muted-foreground",
                      hoveredItem === item.href && pathname !== item.href ? "text-foreground" : "",
                    )}
                  />
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className="border-t border-border/40 p-2">
        <div className="flex items-center justify-between p-2">
          <UserMenu />
          <ModeToggle />
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
