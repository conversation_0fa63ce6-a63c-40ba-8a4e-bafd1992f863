"use client"

import { useState } from "react"
import { Globe, Lock, Save, Settings, ToggleLeft, Upload } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export function PlatformSettings() {
  const [logoUrl, setLogoUrl] = useState("/your-logo.png")
  const [faviconUrl, setFaviconUrl] = useState("/favicon.ico")

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Platform Settings</h2>
        <p className="text-muted-foreground">Configure general platform settings and preferences</p>
      </div>

      <Tabs defaultValue="general">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="regional">Regional</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="limits">System Limits</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Platform Information</CardTitle>
              <CardDescription>Basic information about your platform</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="platform-name">Platform Name</Label>
                  <Input id="platform-name" defaultValue="Sellzio" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="platform-domain">Primary Domain</Label>
                  <Input id="platform-domain" defaultValue="sellzio.com" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="platform-description">Platform Description</Label>
                <Textarea
                  id="platform-description"
                  defaultValue="Sellzio is a multi-tenant SaaS platform for creating and managing online stores."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="support-email">Support Email</Label>
                  <Input id="support-email" defaultValue="<EMAIL>" type="email" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-phone">Contact Phone</Label>
                  <Input id="contact-phone" defaultValue="+62 812 3456 7890" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Branding</CardTitle>
              <CardDescription>Customize your platform's branding</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Platform Logo</Label>
                <div className="flex items-center gap-4">
                  <Avatar className="h-20 w-20 rounded-md">
                    <AvatarImage src={logoUrl || "/placeholder.svg"} alt="Platform logo" />
                    <AvatarFallback className="rounded-md">SZ</AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm">
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Logo
                    </Button>
                    <p className="text-xs text-muted-foreground">Recommended size: 512x512px. Max file size: 2MB.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Favicon</Label>
                <div className="flex items-center gap-4">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={faviconUrl || "/placeholder.svg"} alt="Favicon" />
                    <AvatarFallback>SZ</AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm">
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Favicon
                    </Button>
                    <p className="text-xs text-muted-foreground">Recommended size: 32x32px. Max file size: 1MB.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="primary-color">Primary Color</Label>
                <div className="flex items-center gap-2">
                  <Input id="primary-color" defaultValue="#3B82F6" className="w-32" />
                  <input type="color" defaultValue="#3B82F6" className="h-10 w-10 cursor-pointer rounded-md border-0" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="secondary-color">Secondary Color</Label>
                <div className="flex items-center gap-2">
                  <Input id="secondary-color" defaultValue="#10B981" className="w-32" />
                  <input type="color" defaultValue="#10B981" className="h-10 w-10 cursor-pointer rounded-md border-0" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Social Media</CardTitle>
              <CardDescription>Connect your platform to social media</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="facebook-url">Facebook URL</Label>
                  <Input id="facebook-url" defaultValue="https://facebook.com/sellzio" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="twitter-url">Twitter URL</Label>
                  <Input id="twitter-url" defaultValue="https://twitter.com/sellzio" />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="instagram-url">Instagram URL</Label>
                  <Input id="instagram-url" defaultValue="https://instagram.com/sellzio" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="linkedin-url">LinkedIn URL</Label>
                  <Input id="linkedin-url" defaultValue="https://linkedin.com/company/sellzio" />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              <Button variant="outline">Cancel</Button>
              <Button>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="regional" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Regional Settings</CardTitle>
              <CardDescription>Configure timezone, currency, and language settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="default-timezone">Default Timezone</Label>
                <Select defaultValue="Asia/Jakarta">
                  <SelectTrigger id="default-timezone">
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Asia/Jakarta">Asia/Jakarta (GMT+7)</SelectItem>
                    <SelectItem value="Asia/Singapore">Asia/Singapore (GMT+8)</SelectItem>
                    <SelectItem value="Asia/Tokyo">Asia/Tokyo (GMT+9)</SelectItem>
                    <SelectItem value="Australia/Sydney">Australia/Sydney (GMT+10)</SelectItem>
                    <SelectItem value="Europe/London">Europe/London (GMT+0)</SelectItem>
                    <SelectItem value="America/New_York">America/New_York (GMT-5)</SelectItem>
                    <SelectItem value="America/Los_Angeles">America/Los_Angeles (GMT-8)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  This will be the default timezone for new tenants and users
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="default-currency">Default Currency</Label>
                <Select defaultValue="IDR">
                  <SelectTrigger id="default-currency">
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="IDR">Indonesian Rupiah (IDR)</SelectItem>
                    <SelectItem value="USD">US Dollar (USD)</SelectItem>
                    <SelectItem value="EUR">Euro (EUR)</SelectItem>
                    <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                    <SelectItem value="JPY">Japanese Yen (JPY)</SelectItem>
                    <SelectItem value="SGD">Singapore Dollar (SGD)</SelectItem>
                    <SelectItem value="AUD">Australian Dollar (AUD)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="default-language">Default Language</Label>
                <Select defaultValue="id">
                  <SelectTrigger id="default-language">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="id">Bahasa Indonesia</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="zh">Chinese (Simplified)</SelectItem>
                    <SelectItem value="ja">Japanese</SelectItem>
                    <SelectItem value="ko">Korean</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="date-format">Date Format</Label>
                <Select defaultValue="dd/MM/yyyy">
                  <SelectTrigger id="date-format">
                    <SelectValue placeholder="Select date format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="dd/MM/yyyy">DD/MM/YYYY (31/12/2025)</SelectItem>
                    <SelectItem value="MM/dd/yyyy">MM/DD/YYYY (12/31/2025)</SelectItem>
                    <SelectItem value="yyyy-MM-dd">YYYY-MM-DD (2025-12-31)</SelectItem>
                    <SelectItem value="dd-MMM-yyyy">DD-MMM-YYYY (31-Dec-2025)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="time-format">Time Format</Label>
                <Select defaultValue="HH:mm">
                  <SelectTrigger id="time-format">
                    <SelectValue placeholder="Select time format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="HH:mm">24-hour (14:30)</SelectItem>
                    <SelectItem value="hh:mm a">12-hour (02:30 PM)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="first-day-of-week">First Day of Week</Label>
                <Select defaultValue="1">
                  <SelectTrigger id="first-day-of-week">
                    <SelectValue placeholder="Select first day of week" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Sunday</SelectItem>
                    <SelectItem value="1">Monday</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              <Button variant="outline">Cancel</Button>
              <Button>
                <Globe className="mr-2 h-4 w-4" />
                Save Regional Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>Configure platform security settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Authentication</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Two-Factor Authentication</Label>
                    <p className="text-sm text-muted-foreground">Require 2FA for admin accounts</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Social Login</Label>
                    <p className="text-sm text-muted-foreground">Allow users to sign in with social accounts</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password-policy">Password Policy</Label>
                  <Select defaultValue="strong">
                    <SelectTrigger id="password-policy">
                      <SelectValue placeholder="Select password policy" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="basic">Basic (min. 8 characters)</SelectItem>
                      <SelectItem value="medium">Medium (min. 10 chars, 1 number, 1 special)</SelectItem>
                      <SelectItem value="strong">
                        Strong (min. 12 chars, uppercase, lowercase, number, special)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="session-timeout">Session Timeout</Label>
                  <Select defaultValue="60">
                    <SelectTrigger id="session-timeout">
                      <SelectValue placeholder="Select session timeout" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="60">1 hour</SelectItem>
                      <SelectItem value="120">2 hours</SelectItem>
                      <SelectItem value="240">4 hours</SelectItem>
                      <SelectItem value="480">8 hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Data Protection</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Data Encryption</Label>
                    <p className="text-sm text-muted-foreground">Encrypt sensitive data at rest</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>GDPR Compliance</Label>
                    <p className="text-sm text-muted-foreground">Enable GDPR compliance features</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Data Retention Policy</Label>
                    <p className="text-sm text-muted-foreground">Automatically delete inactive user data</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Select defaultValue="365">
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select retention period" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="90">90 days</SelectItem>
                        <SelectItem value="180">180 days</SelectItem>
                        <SelectItem value="365">1 year</SelectItem>
                        <SelectItem value="730">2 years</SelectItem>
                        <SelectItem value="never">Never delete</SelectItem>
                      </SelectContent>
                    </Select>
                    <Switch defaultChecked />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Access Control</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>IP Restriction</Label>
                    <p className="text-sm text-muted-foreground">Restrict admin access to specific IP addresses</p>
                  </div>
                  <Switch />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Login Attempt Limits</Label>
                    <p className="text-sm text-muted-foreground">Lock accounts after failed login attempts</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Select defaultValue="5">
                      <SelectTrigger className="w-[100px]">
                        <SelectValue placeholder="Attempts" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="3">3 attempts</SelectItem>
                        <SelectItem value="5">5 attempts</SelectItem>
                        <SelectItem value="10">10 attempts</SelectItem>
                      </SelectContent>
                    </Select>
                    <Switch defaultChecked />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              <Button variant="outline">Cancel</Button>
              <Button>
                <Lock className="mr-2 h-4 w-4" />
                Save Security Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Feature Toggles</CardTitle>
              <CardDescription>Enable or disable platform features</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Core Features</h3>

                <div className="grid gap-4">
                  {[
                    { name: "Multi-tenancy", description: "Allow multiple tenants on the platform", enabled: true },
                    {
                      name: "Marketplace",
                      description: "Enable the central marketplace for all stores",
                      enabled: true,
                    },
                    { name: "Custom Domains", description: "Allow tenants to use custom domains", enabled: true },
                    { name: "White Labeling", description: "Allow tenants to fully customize branding", enabled: true },
                  ].map((feature, i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-base">{feature.name}</Label>
                        <p className="text-sm text-muted-foreground">{feature.description}</p>
                      </div>
                      <Switch defaultChecked={feature.enabled} />
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Payment Features</h3>

                <div className="grid gap-4">
                  {[
                    { name: "Credit Card Payments", description: "Process credit card payments", enabled: true },
                    { name: "Bank Transfers", description: "Allow bank transfer payments", enabled: true },
                    { name: "Digital Wallets", description: "Support digital wallet payments", enabled: true },
                    {
                      name: "Cryptocurrency",
                      description: "Accept cryptocurrency payments",
                      enabled: false,
                      beta: true,
                    },
                    { name: "Subscription Billing", description: "Recurring subscription payments", enabled: true },
                    {
                      name: "Split Payments",
                      description: "Split payments between multiple recipients",
                      enabled: false,
                      beta: true,
                    },
                  ].map((feature, i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <Label className="text-base">{feature.name}</Label>
                          {feature.beta && (
                            <Badge variant="secondary" className="text-xs">
                              BETA
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{feature.description}</p>
                      </div>
                      <Switch defaultChecked={feature.enabled} />
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Advanced Features</h3>

                <div className="grid gap-4">
                  {[
                    {
                      name: "AI Product Recommendations",
                      description: "AI-powered product recommendations",
                      enabled: true,
                    },
                    { name: "Advanced Analytics", description: "Detailed analytics and reporting", enabled: true },
                    { name: "Fraud Detection", description: "Automated fraud detection system", enabled: true },
                    {
                      name: "Inventory Forecasting",
                      description: "AI-powered inventory forecasting",
                      enabled: false,
                      beta: true,
                    },
                    { name: "Abandoned Cart Recovery", description: "Automated abandoned cart emails", enabled: true },
                    {
                      name: "Dynamic Pricing",
                      description: "Automated price adjustments based on demand",
                      enabled: false,
                      beta: true,
                    },
                  ].map((feature, i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <Label className="text-base">{feature.name}</Label>
                          {feature.beta && (
                            <Badge variant="secondary" className="text-xs">
                              BETA
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{feature.description}</p>
                      </div>
                      <Switch defaultChecked={feature.enabled} />
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              <Button variant="outline">Cancel</Button>
              <Button>
                <ToggleLeft className="mr-2 h-4 w-4" />
                Save Feature Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="limits" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>System Limits Configuration</CardTitle>
              <CardDescription>Configure system-wide limits and quotas</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Tenant Limits</h3>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="max-tenants">Maximum Tenants</Label>
                      <Input id="max-tenants" type="number" defaultValue="1000" />
                      <p className="text-xs text-muted-foreground">Maximum number of tenants allowed on the platform</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max-stores-per-tenant">Maximum Stores per Tenant</Label>
                      <Input id="max-stores-per-tenant" type="number" defaultValue="100" />
                      <p className="text-xs text-muted-foreground">Maximum number of stores a tenant can create</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="max-products-per-store">Maximum Products per Store</Label>
                      <Input id="max-products-per-store" type="number" defaultValue="10000" />
                      <p className="text-xs text-muted-foreground">Maximum number of products a store can have</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max-categories-per-store">Maximum Categories per Store</Label>
                      <Input id="max-categories-per-store" type="number" defaultValue="100" />
                      <p className="text-xs text-muted-foreground">Maximum number of categories a store can have</p>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Resource Limits</h3>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="storage-per-tenant">Storage per Tenant (GB)</Label>
                      <Input id="storage-per-tenant" type="number" defaultValue="50" />
                      <p className="text-xs text-muted-foreground">Maximum storage space per tenant in GB</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="bandwidth-per-tenant">Bandwidth per Tenant (GB/month)</Label>
                      <Input id="bandwidth-per-tenant" type="number" defaultValue="500" />
                      <p className="text-xs text-muted-foreground">Maximum bandwidth per tenant in GB per month</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="max-file-size">Maximum File Size (MB)</Label>
                      <Input id="max-file-size" type="number" defaultValue="10" />
                      <p className="text-xs text-muted-foreground">Maximum size for uploaded files in MB</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max-image-resolution">Maximum Image Resolution</Label>
                      <Input id="max-image-resolution" defaultValue="4000x4000" />
                      <p className="text-xs text-muted-foreground">Maximum resolution for uploaded images</p>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">API Limits</h3>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="api-rate-limit">API Rate Limit (requests/minute)</Label>
                      <Input id="api-rate-limit" type="number" defaultValue="1000" />
                      <p className="text-xs text-muted-foreground">Maximum API requests per minute per tenant</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="api-daily-limit">API Daily Limit (requests/day)</Label>
                      <Input id="api-daily-limit" type="number" defaultValue="100000" />
                      <p className="text-xs text-muted-foreground">Maximum API requests per day per tenant</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="webhook-rate-limit">Webhook Rate Limit (requests/minute)</Label>
                      <Input id="webhook-rate-limit" type="number" defaultValue="100" />
                      <p className="text-xs text-muted-foreground">Maximum webhook requests per minute per tenant</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max-concurrent-connections">Maximum Concurrent Connections</Label>
                      <Input id="max-concurrent-connections" type="number" defaultValue="500" />
                      <p className="text-xs text-muted-foreground">Maximum concurrent connections per tenant</p>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Performance Limits</h3>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="max-background-jobs">Maximum Background Jobs</Label>
                      <Input id="max-background-jobs" type="number" defaultValue="50" />
                      <p className="text-xs text-muted-foreground">Maximum concurrent background jobs per tenant</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="job-timeout">Job Timeout (seconds)</Label>
                      <Input id="job-timeout" type="number" defaultValue="300" />
                      <p className="text-xs text-muted-foreground">Maximum time a background job can run</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="max-report-rows">Maximum Report Rows</Label>
                      <Input id="max-report-rows" type="number" defaultValue="100000" />
                      <p className="text-xs text-muted-foreground">Maximum rows in generated reports</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max-export-size">Maximum Export Size (MB)</Label>
                      <Input id="max-export-size" type="number" defaultValue="100" />
                      <p className="text-xs text-muted-foreground">Maximum size for exported files in MB</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              <Button variant="outline">Reset to Defaults</Button>
              <Button>
                <Settings className="mr-2 h-4 w-4" />
                Save Limit Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
