"use client"

import type React from "react"

import { useState } from "react"
import { ChevronRight, ChevronDown, Plus, Trash, <PERSON><PERSON>hart, Save, X } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "@/components/ui/use-toast"

// Mock data for categories
const mockCategories = [
  {
    id: "cat-1",
    name: "Electronics",
    description: "Electronic devices and accessories",
    storeCount: 120,
    children: [
      {
        id: "cat-1-1",
        name: "Smartphones",
        description: "Mobile phones and accessories",
        storeCount: 45,
        children: [],
      },
      {
        id: "cat-1-2",
        name: "Computers",
        description: "Laptops, desktops, and accessories",
        storeCount: 38,
        children: [
          {
            id: "cat-1-2-1",
            name: "Laptops",
            description: "Portable computers",
            storeCount: 22,
            children: [],
          },
          {
            id: "cat-1-2-2",
            name: "Desktops",
            description: "Desktop computers",
            storeCount: 16,
            children: [],
          },
        ],
      },
      {
        id: "cat-1-3",
        name: "Audio",
        description: "Headphones, speakers, and audio equipment",
        storeCount: 37,
        children: [],
      },
    ],
  },
  {
    id: "cat-2",
    name: "Fashion",
    description: "Clothing, shoes, and accessories",
    storeCount: 180,
    children: [
      {
        id: "cat-2-1",
        name: "Men's Clothing",
        description: "Clothing for men",
        storeCount: 65,
        children: [],
      },
      {
        id: "cat-2-2",
        name: "Women's Clothing",
        description: "Clothing for women",
        storeCount: 85,
        children: [],
      },
      {
        id: "cat-2-3",
        name: "Accessories",
        description: "Fashion accessories",
        storeCount: 30,
        children: [],
      },
    ],
  },
  {
    id: "cat-3",
    name: "Home & Garden",
    description: "Products for home and garden",
    storeCount: 150,
    children: [
      {
        id: "cat-3-1",
        name: "Furniture",
        description: "Home furniture",
        storeCount: 55,
        children: [],
      },
      {
        id: "cat-3-2",
        name: "Kitchen",
        description: "Kitchen appliances and accessories",
        storeCount: 48,
        children: [],
      },
      {
        id: "cat-3-3",
        name: "Garden",
        description: "Garden tools and accessories",
        storeCount: 47,
        children: [],
      },
    ],
  },
]

// Category tree component
const CategoryTree = ({
  categories,
  level = 0,
  onSelect,
}: {
  categories: any[]
  level?: number
  onSelect: (category: any) => void
}) => {
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({})

  const toggleExpand = (categoryId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setExpandedCategories((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId],
    }))
  }

  return (
    <div className="space-y-1">
      {categories.map((category) => (
        <div key={category.id}>
          <div
            className={`flex items-center rounded-md p-2 hover:bg-muted ${level > 0 ? "pl-" + (level * 4 + 2) + "px" : ""}`}
            style={{ paddingLeft: level > 0 ? `${level * 16 + 8}px` : undefined }}
            onClick={() => onSelect(category)}
          >
            {category.children.length > 0 ? (
              <Button variant="ghost" size="icon" className="h-5 w-5 p-0" onClick={(e) => toggleExpand(category.id, e)}>
                {expandedCategories[category.id] ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            ) : (
              <div className="w-5" />
            )}
            <div className="ml-1 flex-1 cursor-pointer">{category.name}</div>
            <Badge variant="outline" className="ml-2">
              {category.storeCount}
            </Badge>
          </div>
          {expandedCategories[category.id] && category.children.length > 0 && (
            <CategoryTree categories={category.children} level={level + 1} onSelect={onSelect} />
          )}
        </div>
      ))}
    </div>
  )
}

// Category statistics component
const CategoryStatistics = ({ categories }: { categories: any[] }) => {
  // Calculate total store count
  const totalStores = categories.reduce((total, category) => total + category.storeCount, 0)

  // Sort categories by store count (descending)
  const sortedCategories = [...categories].sort((a, b) => b.storeCount - a.storeCount)

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Category Distribution</h3>
      <div className="space-y-3">
        {sortedCategories.map((category) => (
          <div key={category.id} className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{category.name}</span>
              <span className="text-sm text-muted-foreground">
                {category.storeCount} stores ({Math.round((category.storeCount / totalStores) * 100)}%)
              </span>
            </div>
            <Progress value={(category.storeCount / totalStores) * 100} />
          </div>
        ))}
      </div>
    </div>
  )
}

export function StoreCategories() {
  const [selectedCategory, setSelectedCategory] = useState<any | null>(null)
  const [categoryName, setCategoryName] = useState("")
  const [categoryDescription, setCategoryDescription] = useState("")
  const [parentCategory, setParentCategory] = useState("")
  const [isEditing, setIsEditing] = useState(false)
  const [activeTab, setActiveTab] = useState<"edit" | "stats">("edit")
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [categories, setCategories] = useState(mockCategories)

  const handleSelectCategory = (category: any) => {
    setSelectedCategory(category)
    setCategoryName(category.name)
    setCategoryDescription(category.description)
    setParentCategory("") // Reset parent category
    setIsEditing(true)
    setActiveTab("edit")
  }

  const handleNewCategory = () => {
    setSelectedCategory(null)
    setCategoryName("")
    setCategoryDescription("")
    setParentCategory("")
    setIsEditing(true)
    setActiveTab("edit")
  }

  const handleSaveCategory = () => {
    if (!categoryName.trim()) {
      toast({
        title: "Error",
        description: "Category name is required",
        variant: "destructive",
      })
      return
    }

    if (selectedCategory) {
      // Update existing category
      toast({
        title: "Category Updated",
        description: `${categoryName} has been updated successfully.`,
      })
    } else {
      // Create new category
      const newCategory = {
        id: `cat-${Date.now()}`,
        name: categoryName,
        description: categoryDescription,
        storeCount: 0,
        children: [],
      }

      if (parentCategory) {
        // Add as child to parent category
        // This is simplified - in a real app you'd need to traverse the category tree
        toast({
          title: "Category Created",
          description: `${categoryName} has been created as a subcategory.`,
        })
      } else {
        // Add as top-level category
        setCategories([...categories, newCategory])
        toast({
          title: "Category Created",
          description: `${categoryName} has been created successfully.`,
        })
      }
    }

    // Reset form
    setIsEditing(false)
  }

  const handleDeleteCategory = () => {
    if (selectedCategory) {
      setShowDeleteDialog(true)
    }
  }

  const confirmDeleteCategory = () => {
    if (selectedCategory) {
      // Here you would call your API to delete the category
      // This is simplified - in a real app you'd need to handle children, etc.
      setCategories(categories.filter((cat) => cat.id !== selectedCategory.id))

      toast({
        title: "Category Deleted",
        description: `${selectedCategory.name} has been deleted.`,
        variant: "destructive",
      })

      setSelectedCategory(null)
      setIsEditing(false)
      setShowDeleteDialog(false)
    }
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    if (!selectedCategory) {
      setCategoryName("")
      setCategoryDescription("")
      setParentCategory("")
    }
  }

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
      <div className="md:col-span-1">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-medium">Category Tree</h3>
          <Button size="sm" variant="outline" className="gap-1" onClick={handleNewCategory}>
            <Plus className="h-4 w-4" />
            New
          </Button>
        </div>
        <Card>
          <CardContent className="p-3">
            <CategoryTree categories={categories} onSelect={handleSelectCategory} />
          </CardContent>
        </Card>
      </div>

      <div className="md:col-span-2">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "edit" | "stats")}>
          <div className="mb-4 flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="edit">{selectedCategory ? "Edit Category" : "New Category"}</TabsTrigger>
              <TabsTrigger value="stats">
                <BarChart className="mr-2 h-4 w-4" />
                Statistics
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="edit">
            <Card>
              <CardContent className="p-6">
                {isEditing ? (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="category-name">Category Name</Label>
                      <Input
                        id="category-name"
                        value={categoryName}
                        onChange={(e) => setCategoryName(e.target.value)}
                        placeholder="Enter category name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category-description">Description</Label>
                      <Textarea
                        id="category-description"
                        value={categoryDescription}
                        onChange={(e) => setCategoryDescription(e.target.value)}
                        placeholder="Enter category description"
                        rows={3}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="parent-category">Parent Category (Optional)</Label>
                      <select
                        id="parent-category"
                        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        value={parentCategory}
                        onChange={(e) => setParentCategory(e.target.value)}
                      >
                        <option value="">None (Top Level)</option>
                        {categories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="flex justify-end gap-2 pt-4">
                      {selectedCategory && (
                        <Button variant="destructive" className="gap-1" onClick={handleDeleteCategory}>
                          <Trash className="h-4 w-4" />
                          Delete
                        </Button>
                      )}
                      <div className="flex-1" />
                      <Button variant="outline" onClick={handleCancelEdit}>
                        <X className="mr-2 h-4 w-4" />
                        Cancel
                      </Button>
                      <Button onClick={handleSaveCategory}>
                        <Save className="mr-2 h-4 w-4" />
                        {selectedCategory ? "Update" : "Create"} Category
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex h-[300px] items-center justify-center">
                    <div className="text-center">
                      <h3 className="text-lg font-medium">No Category Selected</h3>
                      <p className="text-sm text-muted-foreground">Select a category to edit or create a new one.</p>
                      <Button className="mt-4" onClick={handleNewCategory}>
                        Create New Category
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stats">
            <Card>
              <CardContent className="p-6">
                <CategoryStatistics categories={categories} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Delete Category Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Category</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedCategory?.name}? This will also delete all subcategories and
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteCategory} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
