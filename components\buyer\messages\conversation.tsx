"use client"

import { useState, useRef, useEffect } from "react"
import { MessageHeader } from "./message-header"
import { MessageInput } from "./message-input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { id } from "date-fns/locale"

// Tipe data untuk pesan
interface Message {
  id: string
  content: string
  timestamp: Date
  sender: "user" | "other"
  read: boolean
  image?: string
}

// Data dummy untuk percakapan
const dummyConversations: Record<
  string,
  {
    id: string
    name: string
    avatar: string
    isOnline: boolean
    isStore: boolean
    messages: Message[]
  }
> = {
  "1": {
    id: "1",
    name: "Toko Elektronik Jaya",
    avatar: "/brand-logo-1.png",
    isOnline: true,
    isStore: true,
    messages: [
      {
        id: "m1",
        content: "<PERSON><PERSON>, saya tertarik dengan produk laptop yang Anda jual",
        timestamp: new Date(2023, 5, 10, 10, 30),
        sender: "user",
        read: true,
      },
      {
        id: "m2",
        content: "Halo, terima kasih telah menghubungi kami. Laptop apa yang Anda minati?",
        timestamp: new Date(2023, 5, 10, 10, 35),
        sender: "other",
        read: true,
      },
      {
        id: "m3",
        content: "Saya tertarik dengan laptop gaming yang ada di halaman depan",
        timestamp: new Date(2023, 5, 10, 10, 40),
        sender: "user",
        read: true,
      },
      {
        id: "m4",
        content: "Baik, laptop gaming tersebut masih tersedia. Apakah Anda ingin mengetahui spesifikasi lebih detail?",
        timestamp: new Date(2023, 5, 10, 10, 45),
        sender: "other",
        read: true,
      },
      {
        id: "m5",
        content: "Ya, tolong berikan detail spesifikasinya",
        timestamp: new Date(2023, 5, 10, 11, 0),
        sender: "user",
        read: true,
      },
      {
        id: "m6",
        content:
          "Berikut spesifikasi laptop gaming tersebut: Processor Intel Core i7 gen 12, RAM 16GB, SSD 1TB, VGA RTX 3060 6GB, Layar 15.6 inch 144Hz. Harga Rp 15.500.000",
        timestamp: new Date(2023, 5, 10, 11, 5),
        sender: "other",
        read: true,
      },
      {
        id: "m7",
        content: "Apakah tersedia warna lain selain hitam?",
        timestamp: new Date(2023, 5, 10, 11, 10),
        sender: "user",
        read: true,
      },
      {
        id: "m8",
        content: "Saat ini tersedia warna hitam dan silver. Warna apa yang Anda inginkan?",
        timestamp: new Date(2023, 5, 10, 11, 15),
        sender: "other",
        read: true,
      },
      {
        id: "m9",
        content: "Saya tertarik dengan warna silver. Apakah bisa dikirim hari ini?",
        timestamp: new Date(2023, 5, 10, 11, 20),
        sender: "user",
        read: true,
      },
      {
        id: "m10",
        content:
          "Tentu, kami bisa memproses pesanan Anda hari ini dan mengirimkannya. Estimasi sampai besok atau lusa tergantung lokasi Anda.",
        timestamp: new Date(2023, 5, 10, 11, 25),
        sender: "other",
        read: true,
      },
      {
        id: "m11",
        content: "Baik, saya akan melakukan pemesanan sekarang. Terima kasih informasinya!",
        timestamp: new Date(2023, 5, 10, 11, 30),
        sender: "user",
        read: true,
      },
      {
        id: "m12",
        content: "Sama-sama! Kami tunggu pesanan Anda. Jika ada pertanyaan lain, silakan tanyakan kepada kami.",
        timestamp: new Date(2023, 5, 10, 11, 35),
        sender: "other",
        read: true,
      },
      {
        id: "m13",
        content: "Pesanan saya sudah dibuat dengan nomor #12345. Mohon segera diproses ya",
        timestamp: new Date(2023, 5, 10, 14, 0),
        sender: "user",
        read: true,
      },
      {
        id: "m14",
        content: "Baik, kami sudah menerima pesanan Anda. Pesanan sedang diproses dan akan segera dikirim.",
        timestamp: new Date(2023, 5, 10, 14, 10),
        sender: "other",
        read: true,
      },
      {
        id: "m15",
        content: "Pesanan Anda sudah dikirim dengan nomor resi JNE: 1234567890. Estimasi sampai besok.",
        timestamp: new Date(2023, 5, 11, 9, 0),
        sender: "other",
        read: false,
      },
      {
        id: "m16",
        content: "Barang sudah dikirim, estimasi sampai besok",
        timestamp: new Date(2023, 5, 11, 10, 30),
        sender: "other",
        read: false,
        image: "/product-image-1.png",
      },
    ],
  },
  "2": {
    id: "2",
    name: "Fashion Trendy",
    avatar: "/brand-logo-2.png",
    isOnline: false,
    isStore: true,
    messages: [
      {
        id: "m1",
        content: "Halo, saya ingin menanyakan tentang ukuran baju yang tersedia",
        timestamp: new Date(2023, 5, 9, 15, 0),
        sender: "user",
        read: true,
      },
      {
        id: "m2",
        content: "Halo, untuk produk tersebut tersedia ukuran S, M, L, dan XL",
        timestamp: new Date(2023, 5, 9, 15, 10),
        sender: "other",
        read: true,
      },
      {
        id: "m3",
        content: "Baik, saya akan pesan ukuran L. Terima kasih!",
        timestamp: new Date(2023, 5, 9, 15, 15),
        sender: "user",
        read: true,
      },
      {
        id: "m4",
        content: "Terima kasih atas pesanan Anda!",
        timestamp: new Date(2023, 5, 9, 15, 20),
        sender: "other",
        read: true,
      },
    ],
  },
  "3": {
    id: "3",
    name: "Admin SellZio",
    avatar: "/your-logo.png",
    isOnline: true,
    isStore: false,
    messages: [
      {
        id: "m1",
        content: "Selamat datang di SellZio! Kami senang Anda bergabung dengan platform kami.",
        timestamp: new Date(2023, 5, 8, 9, 0),
        sender: "other",
        read: true,
      },
      {
        id: "m2",
        content: "Terima kasih! Saya senang bisa bergabung.",
        timestamp: new Date(2023, 5, 8, 9, 5),
        sender: "user",
        read: true,
      },
      {
        id: "m3",
        content: "Jika Anda memiliki pertanyaan atau membutuhkan bantuan, jangan ragu untuk menghubungi kami.",
        timestamp: new Date(2023, 5, 8, 9, 10),
        sender: "other",
        read: true,
      },
      {
        id: "m4",
        content: "Bagaimana pengalaman berbelanja Anda?",
        timestamp: new Date(2023, 5, 9, 14, 0),
        sender: "other",
        read: false,
      },
    ],
  },
  "4": {
    id: "4",
    name: "Toko Furniture Modern",
    avatar: "/brand-logo-3.png",
    isOnline: false,
    isStore: true,
    messages: [
      {
        id: "m1",
        content: "Halo, saya tertarik dengan meja kerja yang Anda jual",
        timestamp: new Date(2023, 5, 5, 13, 0),
        sender: "user",
        read: true,
      },
      {
        id: "m2",
        content: "Halo, terima kasih telah menghubungi kami. Meja kerja yang mana yang Anda minati?",
        timestamp: new Date(2023, 5, 5, 13, 10),
        sender: "other",
        read: true,
      },
      {
        id: "m3",
        content: "Meja kerja model minimalis dengan laci",
        timestamp: new Date(2023, 5, 5, 13, 15),
        sender: "user",
        read: true,
      },
      {
        id: "m4",
        content: "Apakah ada pertanyaan tentang produk kami?",
        timestamp: new Date(2023, 5, 5, 13, 20),
        sender: "other",
        read: true,
      },
    ],
  },
  "5": {
    id: "5",
    name: "Toko Buku Cerdas",
    avatar: "/brand-logo-4.png",
    isOnline: true,
    isStore: true,
    messages: [
      {
        id: "m1",
        content: "Halo, saya ingin menanyakan ketersediaan buku 'Atomic Habits'",
        timestamp: new Date(2023, 5, 3, 10, 0),
        sender: "user",
        read: true,
      },
      {
        id: "m2",
        content: "Halo, saat ini buku tersebut sedang kosong. Kami akan memberitahu Anda jika sudah tersedia kembali.",
        timestamp: new Date(2023, 5, 3, 10, 10),
        sender: "other",
        read: true,
      },
      {
        id: "m3",
        content: "Baik, terima kasih informasinya",
        timestamp: new Date(2023, 5, 3, 10, 15),
        sender: "user",
        read: true,
      },
      {
        id: "m4",
        content: "Buku yang Anda pesan sudah tersedia",
        timestamp: new Date(2023, 5, 3, 15, 0),
        sender: "other",
        read: true,
      },
    ],
  },
}

interface ConversationProps {
  conversationId: string
}

export function Conversation({ conversationId }: ConversationProps) {
  const [conversation, setConversation] = useState(dummyConversations[conversationId])
  const [newMessage, setNewMessage] = useState("")
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Scroll ke pesan terbaru saat percakapan berubah
  useEffect(() => {
    setConversation(dummyConversations[conversationId])
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [conversationId])

  // Fungsi untuk mengirim pesan baru
  const handleSendMessage = (content: string, image?: string) => {
    if (!content.trim() && !image) return

    const newMsg: Message = {
      id: `new-${Date.now()}`,
      content,
      timestamp: new Date(),
      sender: "user",
      read: false,
      image,
    }

    const updatedConversation = {
      ...conversation,
      messages: [...conversation.messages, newMsg],
    }

    setConversation(updatedConversation)
    dummyConversations[conversationId] = updatedConversation
    setNewMessage("")

    // Scroll ke pesan terbaru
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
    }, 100)
  }

  // Fungsi untuk mengelompokkan pesan berdasarkan tanggal
  const groupMessagesByDate = () => {
    const groups: { date: string; messages: Message[] }[] = []
    let currentDate = ""
    let currentGroup: Message[] = []

    conversation.messages.forEach((message) => {
      const messageDate = format(message.timestamp, "dd MMMM yyyy", { locale: id })

      if (messageDate !== currentDate) {
        if (currentGroup.length > 0) {
          groups.push({ date: currentDate, messages: currentGroup })
        }
        currentDate = messageDate
        currentGroup = [message]
      } else {
        currentGroup.push(message)
      }
    })

    if (currentGroup.length > 0) {
      groups.push({ date: currentDate, messages: currentGroup })
    }

    return groups
  }

  return (
    <div className="flex-1 flex flex-col">
      <MessageHeader
        name={conversation.name}
        avatar={conversation.avatar}
        isOnline={conversation.isOnline}
        isStore={conversation.isStore}
      />
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {groupMessagesByDate().map((group, groupIndex) => (
          <div key={groupIndex} className="space-y-4">
            <div className="flex justify-center">
              <div className="bg-muted px-3 py-1 rounded-full text-xs text-muted-foreground">{group.date}</div>
            </div>
            {group.messages.map((message, index) => {
              // Cek apakah pesan sebelumnya dari pengirim yang sama
              const prevMessage = index > 0 ? group.messages[index - 1] : null
              const showAvatar = !prevMessage || prevMessage.sender !== message.sender
              const isSameTime =
                prevMessage &&
                prevMessage.sender === message.sender &&
                Math.abs(message.timestamp.getTime() - prevMessage.timestamp.getTime()) < 60000 // 1 menit

              return (
                <div
                  key={message.id}
                  className={cn(
                    "flex items-end gap-2",
                    message.sender === "user" ? "justify-end" : "justify-start",
                    !isSameTime ? "mt-2" : "mt-1",
                  )}
                >
                  {message.sender === "other" && showAvatar && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={conversation.avatar || "/placeholder.svg"} alt={conversation.name} />
                      <AvatarFallback>
                        {conversation.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()
                          .substring(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                  )}
                  {message.sender === "other" && !showAvatar && <div className="w-8" />}
                  <div
                    className={cn(
                      "max-w-[70%] rounded-lg px-4 py-2 text-sm",
                      message.sender === "user"
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted text-muted-foreground",
                    )}
                  >
                    {message.image && (
                      <div className="mb-2">
                        <img
                          src={message.image || "/placeholder.svg"}
                          alt="Attachment"
                          className="rounded-md max-h-60 object-cover"
                        />
                      </div>
                    )}
                    <p>{message.content}</p>
                    <div
                      className={cn(
                        "text-xs mt-1 flex justify-end",
                        message.sender === "user" ? "text-primary-foreground/70" : "text-muted-foreground/70",
                      )}
                    >
                      {format(message.timestamp, "HH:mm")}
                      {message.sender === "user" && <span className="ml-1">{message.read ? "✓✓" : "✓"}</span>}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      <MessageInput value={newMessage} onChange={setNewMessage} onSend={handleSendMessage} />
    </div>
  )
}
