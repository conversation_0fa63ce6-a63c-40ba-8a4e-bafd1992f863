"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Plus,
  Image as ImageIcon,
  FileVideo,
  File,
  Eye,
  PencilIcon,
  Trash,
  MoreHorizontal,
  Copy,
  Filter,
  ChevronDown,
  Calendar,
  Upload,
  LayoutGrid,
  LayoutList,
  Settings,
  Download,
  Link as LinkIcon,
  FolderOpen,
  Folder,
  FilePlus,
  HardDrive
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk media
const mediaFiles = [
  {
    id: "media-001",
    name: "product-banner.jpg",
    type: "image",
    mimeType: "image/jpeg",
    size: 1240000,
    dimensions: {
      width: 1920,
      height: 1080
    },
    url: "/assets/media/product-banner.jpg",
    thumbnailUrl: "/assets/media/thumbnails/product-banner.jpg",
    folder: "Banner",
    uploadedBy: "Admin",
    createdAt: "2024-01-15T10:30:00",
    updatedAt: "2024-01-15T10:30:00",
    usageCount: 5
  },
  {
    id: "media-002",
    name: "company-logo.png",
    type: "image",
    mimeType: "image/png",
    size: 560000,
    dimensions: {
      width: 500,
      height: 500
    },
    url: "/assets/media/company-logo.png",
    thumbnailUrl: "/assets/media/thumbnails/company-logo.png",
    folder: "Logo",
    uploadedBy: "Admin",
    createdAt: "2024-01-10T09:15:00",
    updatedAt: "2024-01-10T09:15:00",
    usageCount: 12
  },
  {
    id: "media-003",
    name: "product-catalog-2024.pdf",
    type: "document",
    mimeType: "application/pdf",
    size: 4500000,
    url: "/assets/media/product-catalog-2024.pdf",
    thumbnailUrl: "/assets/media/thumbnails/pdf-thumbnail.png",
    folder: "Documents",
    uploadedBy: "Marketing",
    createdAt: "2024-01-20T13:45:00",
    updatedAt: "2024-01-20T13:45:00",
    usageCount: 3
  },
  {
    id: "media-004",
    name: "promotional-video.mp4",
    type: "video",
    mimeType: "video/mp4",
    size: 15800000,
    dimensions: {
      width: 1920,
      height: 1080
    },
    duration: 45,
    url: "/assets/media/promotional-video.mp4",
    thumbnailUrl: "/assets/media/thumbnails/promotional-video.jpg",
    folder: "Videos",
    uploadedBy: "Marketing",
    createdAt: "2024-01-25T11:20:00",
    updatedAt: "2024-01-25T11:20:00",
    usageCount: 2
  },
  {
    id: "media-005",
    name: "product-photo-01.jpg",
    type: "image",
    mimeType: "image/jpeg",
    size: 980000,
    dimensions: {
      width: 1200,
      height: 800
    },
    url: "/assets/media/product-photo-01.jpg",
    thumbnailUrl: "/assets/media/thumbnails/product-photo-01.jpg",
    folder: "Products",
    uploadedBy: "Content",
    createdAt: "2024-01-18T15:30:00",
    updatedAt: "2024-01-18T15:30:00",
    usageCount: 8
  },
  {
    id: "media-006",
    name: "product-photo-02.jpg",
    type: "image",
    mimeType: "image/jpeg",
    size: 1050000,
    dimensions: {
      width: 1200,
      height: 800
    },
    url: "/assets/media/product-photo-02.jpg",
    thumbnailUrl: "/assets/media/thumbnails/product-photo-02.jpg",
    folder: "Products",
    uploadedBy: "Content",
    createdAt: "2024-01-18T15:35:00",
    updatedAt: "2024-01-18T15:35:00",
    usageCount: 6
  },
  {
    id: "media-007",
    name: "terms-and-conditions.docx",
    type: "document",
    mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    size: 320000,
    url: "/assets/media/terms-and-conditions.docx",
    thumbnailUrl: "/assets/media/thumbnails/doc-thumbnail.png",
    folder: "Documents",
    uploadedBy: "Legal",
    createdAt: "2024-01-12T16:20:00",
    updatedAt: "2024-01-12T16:20:00",
    usageCount: 1
  },
  {
    id: "media-008",
    name: "product-tour.mp4",
    type: "video",
    mimeType: "video/mp4",
    size: 24500000,
    dimensions: {
      width: 1920,
      height: 1080
    },
    duration: 120,
    url: "/assets/media/product-tour.mp4",
    thumbnailUrl: "/assets/media/thumbnails/product-tour.jpg",
    folder: "Videos",
    uploadedBy: "Marketing",
    createdAt: "2024-01-28T10:15:00",
    updatedAt: "2024-01-28T10:15:00",
    usageCount: 4
  }
];

// Data folder
const mediaFolders = [
  { id: "folder-001", name: "Banner", filesCount: 3 },
  { id: "folder-002", name: "Logo", filesCount: 2 },
  { id: "folder-003", name: "Documents", filesCount: 5 },
  { id: "folder-004", name: "Videos", filesCount: 4 },
  { id: "folder-005", name: "Products", filesCount: 15 }
];

// Fungsi untuk menampilkan ikon file berdasarkan tipe
function getFileIcon(fileType: string) {
  switch (fileType) {
    case "image":
      return <ImageIcon className="h-10 w-10 text-blue-500" />
    case "video":
      return <FileVideo className="h-10 w-10 text-purple-500" />
    case "document":
      return <File className="h-10 w-10 text-orange-500" />
    default:
      return <File className="h-10 w-10 text-gray-500" />
  }
}

// Fungsi untuk format ukuran file
function formatFileSize(bytes: number) {
  if (bytes < 1024) {
    return `${bytes} B`
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`
  } else if (bytes < 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  } else {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`
  }
}

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function ContentMediaPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [folderFilter, setFolderFilter] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [currentView, setCurrentView] = useState<"files" | "folders">("files")
  
  // Filter media files
  const filteredMedia = mediaFiles.filter(file => {
    const matchesSearch = 
      file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      file.folder.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = typeFilter === "all" || file.type === typeFilter
    const matchesFolder = folderFilter === "all" || file.folder === folderFilter
    
    return matchesSearch && matchesType && matchesFolder
  })
  
  // Statistik
  const stats = {
    totalFiles: mediaFiles.length,
    totalImages: mediaFiles.filter(f => f.type === "image").length,
    totalVideos: mediaFiles.filter(f => f.type === "video").length,
    totalDocuments: mediaFiles.filter(f => f.type === "document").length,
    totalStorage: mediaFiles.reduce((sum, f) => sum + f.size, 0),
    totalFolders: mediaFolders.length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/content">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Media</h1>
            <p className="text-muted-foreground">
              Kelola gambar, video, dan file
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button>
            <Upload className="h-4 w-4 mr-2" />
            Upload Media
          </Button>
          <Button variant="outline">
            <FilePlus className="h-4 w-4 mr-2" />
            Buat Folder
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total File</CardTitle>
            <File className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalFiles}</div>
            <p className="text-xs text-muted-foreground">
              Dalam {stats.totalFolders} folder
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gambar</CardTitle>
            <ImageIcon className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.totalImages}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Video</CardTitle>
            <FileVideo className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.totalVideos}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Penyimpanan</CardTitle>
            <HardDrive className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatFileSize(stats.totalStorage)}</div>
            <p className="text-xs text-muted-foreground">
              Dari kuota 2 GB
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex border-b">
        <button
          className={`px-4 py-2 font-medium ${
            currentView === "files" 
              ? "border-b-2 border-primary text-primary" 
              : "text-muted-foreground"
          }`}
          onClick={() => setCurrentView("files")}
        >
          File
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            currentView === "folders" 
              ? "border-b-2 border-primary text-primary" 
              : "text-muted-foreground"
          }`}
          onClick={() => setCurrentView("folders")}
        >
          Folder
        </button>
      </div>

      {/* Search & Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={currentView === "files" ? "Cari file..." : "Cari folder..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 flex-wrap">
              {currentView === "files" && (
                <>
                  <select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    className="px-3 py-2 border rounded-md bg-background"
                  >
                    <option value="all">Semua Tipe</option>
                    <option value="image">Gambar</option>
                    <option value="video">Video</option>
                    <option value="document">Dokumen</option>
                  </select>
                  <select
                    value={folderFilter}
                    onChange={(e) => setFolderFilter(e.target.value)}
                    className="px-3 py-2 border rounded-md bg-background"
                  >
                    <option value="all">Semua Folder</option>
                    {mediaFolders.map(folder => (
                      <option key={folder.id} value={folder.name}>
                        {folder.name}
                      </option>
                    ))}
                  </select>
                </>
              )}
              <div className="flex border rounded-md overflow-hidden">
                <Button 
                  variant={viewMode === "list" ? "default" : "ghost"} 
                  size="sm"
                  className="rounded-none"
                  onClick={() => setViewMode("list")}
                >
                  <LayoutList className="h-4 w-4" />
                </Button>
                <Button 
                  variant={viewMode === "grid" ? "default" : "ghost"} 
                  size="sm"
                  className="rounded-none"
                  onClick={() => setViewMode("grid")}
                >
                  <LayoutGrid className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Files/Folders View */}
      {currentView === "files" ? (
        // Files View
        viewMode === "list" ? (
          <div className="space-y-4">
            {filteredMedia.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <File className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">Tidak ada file ditemukan</h3>
                  <p className="text-muted-foreground mb-4 text-center">
                    Tidak ada file yang cocok dengan filter atau pencarian Anda
                  </p>
                  <Button>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Media
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredMedia.map((file) => (
                <Card key={file.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <div className="flex-shrink-0">
                        {file.type === "image" ? (
                          <div className="w-16 h-16 rounded bg-muted flex items-center justify-center overflow-hidden">
                            <ImageIcon className="h-8 w-8 text-blue-500" />
                          </div>
                        ) : (
                          <div className="w-16 h-16 rounded bg-muted flex items-center justify-center">
                            {getFileIcon(file.type)}
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium truncate">{file.name}</h3>
                          <Badge variant="outline">{file.type}</Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2 text-sm text-muted-foreground">
                          <div>
                            <span className="block text-xs">Ukuran</span>
                            <span>{formatFileSize(file.size)}</span>
                          </div>
                          <div>
                            <span className="block text-xs">Folder</span>
                            <span>{file.folder}</span>
                          </div>
                          <div>
                            <span className="block text-xs">Diupload</span>
                            <span>{formatDate(file.createdAt)}</span>
                          </div>
                          <div>
                            <span className="block text-xs">Digunakan</span>
                            <span>{file.usageCount} kali</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-2" />
                          Lihat
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                        <Button size="sm" variant="outline">
                          <LinkIcon className="h-4 w-4 mr-2" />
                          Salin URL
                        </Button>
                        <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        ) : (
          // Grid View for Files
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {filteredMedia.length === 0 ? (
              <Card className="col-span-full">
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <File className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">Tidak ada file ditemukan</h3>
                  <p className="text-muted-foreground mb-4 text-center">
                    Tidak ada file yang cocok dengan filter atau pencarian Anda
                  </p>
                  <Button>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Media
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredMedia.map((file) => (
                <Card key={file.id} className="hover:shadow-md transition-shadow overflow-hidden">
                  <div className="aspect-square bg-muted flex items-center justify-center">
                    <div className="w-full h-full flex items-center justify-center">
                      {getFileIcon(file.type)}
                    </div>
                  </div>
                  <CardContent className="p-3">
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-sm truncate" title={file.name}>
                          {file.name}
                        </h3>
                        <Badge variant="outline" className="text-xs px-1 h-5">
                          {file.type}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{formatFileSize(file.size)}</span>
                        <span>{file.folder}</span>
                      </div>
                      <div className="flex mt-2 pt-2 border-t gap-1">
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                          <LinkIcon className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0 ml-auto text-red-600 hover:text-red-700">
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )
      ) : (
        // Folders View
        viewMode === "list" ? (
          <div className="space-y-4">
            {mediaFolders.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <FolderOpen className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">Tidak ada folder ditemukan</h3>
                  <p className="text-muted-foreground mb-4 text-center">
                    Tidak ada folder yang cocok dengan pencarian Anda
                  </p>
                  <Button>
                    <FilePlus className="h-4 w-4 mr-2" />
                    Buat Folder
                  </Button>
                </CardContent>
              </Card>
            ) : (
              mediaFolders
                .filter(folder => folder.name.toLowerCase().includes(searchTerm.toLowerCase()))
                .map((folder) => (
                  <Card key={folder.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-4">
                        <div className="flex-shrink-0">
                          <div className="w-16 h-16 rounded bg-muted flex items-center justify-center">
                            <Folder className="h-10 w-10 text-amber-500" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h3 className="font-medium truncate">{folder.name}</h3>
                            <Badge variant="outline" className="bg-amber-100 text-amber-800">
                              {folder.filesCount} files
                            </Badge>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <FolderOpen className="h-4 w-4 mr-2" />
                            Buka
                          </Button>
                          <Button size="sm" variant="outline">
                            <PencilIcon className="h-4 w-4 mr-2" />
                            Ubah Nama
                          </Button>
                          <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
            )}
          </div>
        ) : (
          // Grid View for Folders
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {mediaFolders.length === 0 ? (
              <Card className="col-span-full">
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <FolderOpen className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">Tidak ada folder ditemukan</h3>
                  <p className="text-muted-foreground mb-4 text-center">
                    Tidak ada folder yang cocok dengan pencarian Anda
                  </p>
                  <Button>
                    <FilePlus className="h-4 w-4 mr-2" />
                    Buat Folder
                  </Button>
                </CardContent>
              </Card>
            ) : (
              mediaFolders
                .filter(folder => folder.name.toLowerCase().includes(searchTerm.toLowerCase()))
                .map((folder) => (
                  <Card key={folder.id} className="hover:shadow-md transition-shadow overflow-hidden">
                    <div className="aspect-square bg-muted flex items-center justify-center">
                      <Folder className="h-20 w-20 text-amber-500" />
                    </div>
                    <CardContent className="p-3">
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-sm truncate" title={folder.name}>
                            {folder.name}
                          </h3>
                          <Badge variant="outline" className="text-xs px-1 h-5 bg-amber-100 text-amber-800">
                            {folder.filesCount}
                          </Badge>
                        </div>
                        <div className="flex mt-2 pt-2 border-t gap-1">
                          <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <FolderOpen className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <PencilIcon className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="h-8 w-8 p-0 ml-auto text-red-600 hover:text-red-700">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
            )}
          </div>
        )
      )}
      
      {(currentView === "files" && filteredMedia.length > 0) || 
       (currentView === "folders" && mediaFolders.filter(folder => 
         folder.name.toLowerCase().includes(searchTerm.toLowerCase())).length > 0) ? (
        <div className="flex justify-center mt-4">
          <Button variant="outline" className="mr-2" size="sm">
            Sebelumnya
          </Button>
          <Button variant="outline" size="sm">
            Selanjutnya
          </Button>
        </div>
      ) : null}
    </div>
  )
} 