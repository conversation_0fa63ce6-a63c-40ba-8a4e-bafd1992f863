"use client"

import { useState } from "react"
import { AlertCircle, ArrowUpFromLine, CheckCircle, Database, Download, HardDrive, UploadCloud } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { DateRangePicker } from "@/components/analytics/date-range-picker"

export function BackupRestore() {
  const [activeTab, setActiveTab] = useState("backup")
  const [backupInProgress, setBackupInProgress] = useState(false)
  const [restoreInProgress, setRestoreInProgress] = useState(false)
  const [backupProgress, setBackupProgress] = useState(0)
  const [restoreProgress, setRestoreProgress] = useState(0)
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date(),
  })

  // Simulasi proses backup
  const startBackup = () => {
    setBackupInProgress(true)
    setBackupProgress(0)

    const interval = setInterval(() => {
      setBackupProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setBackupInProgress(false)
          return 100
        }
        return prev + 5
      })
    }, 300)
  }

  // Simulasi proses restore
  const startRestore = () => {
    setRestoreInProgress(true)
    setRestoreProgress(0)

    const interval = setInterval(() => {
      setRestoreProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setRestoreInProgress(false)
          return 100
        }
        return prev + 4
      })
    }, 300)
  }

  // Data backup terakhir
  const recentBackups = [
    {
      id: "BKP-20250512-001",
      type: "Full",
      date: "2025-05-12T08:30:00",
      size: "4.2 GB",
      status: "Completed",
      duration: "15m 42s",
    },
    {
      id: "BKP-20250505-002",
      type: "Incremental",
      date: "2025-05-05T09:15:00",
      size: "1.8 GB",
      status: "Completed",
      duration: "8m 15s",
    },
    {
      id: "BKP-20250428-003",
      type: "Full",
      date: "2025-04-28T07:45:00",
      size: "4.1 GB",
      status: "Completed",
      duration: "14m 55s",
    },
    {
      id: "BKP-20250421-004",
      type: "Incremental",
      date: "2025-04-21T10:20:00",
      size: "1.5 GB",
      status: "Completed",
      duration: "7m 30s",
    },
    {
      id: "BKP-20250414-005",
      type: "Full",
      date: "2025-04-14T08:00:00",
      size: "4.0 GB",
      status: "Completed",
      duration: "14m 20s",
    },
  ]

  // Format tanggal untuk tampilan
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("id-ID", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Backup & Restore</h2>
          <p className="text-muted-foreground">Kelola backup dan restore data platform Anda</p>
        </div>
        <div className="flex items-center gap-2">
          <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
        </div>
      </div>

      <Tabs defaultValue="backup" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="backup">Backup</TabsTrigger>
          <TabsTrigger value="restore">Restore</TabsTrigger>
          <TabsTrigger value="schedule">Jadwal Backup</TabsTrigger>
        </TabsList>

        <TabsContent value="backup" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Backup</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">42</div>
                <p className="text-xs text-muted-foreground">12 backup dalam 30 hari terakhir</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Backup Terakhir</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12 Mei 2025</div>
                <p className="text-xs text-muted-foreground">08:30 WIB</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Ukuran</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">128.5 GB</div>
                <p className="text-xs text-muted-foreground">Semua backup</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Status Penyimpanan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">68%</div>
                <p className="text-xs text-muted-foreground">Dari 200 GB tersedia</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-3">
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Backup Terbaru</CardTitle>
                <CardDescription>Riwayat backup 30 hari terakhir</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Tipe</TableHead>
                      <TableHead>Tanggal</TableHead>
                      <TableHead>Ukuran</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentBackups.map((backup) => (
                      <TableRow key={backup.id}>
                        <TableCell className="font-medium">{backup.id}</TableCell>
                        <TableCell>
                          <Badge variant={backup.type === "Full" ? "default" : "outline"}>{backup.type}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>{formatDate(backup.date)}</span>
                            <span className="text-xs text-muted-foreground">Durasi: {backup.duration}</span>
                          </div>
                        </TableCell>
                        <TableCell>{backup.size}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span>{backup.status}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm">
                  Lihat Semua Backup
                </Button>
                <div className="text-sm text-muted-foreground">Menampilkan 5 dari 42 backup</div>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Buat Backup Baru</CardTitle>
                <CardDescription>Backup data platform Anda</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="backup-type">Tipe Backup</Label>
                  <Select defaultValue="full">
                    <SelectTrigger id="backup-type">
                      <SelectValue placeholder="Pilih tipe backup" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full">Full Backup</SelectItem>
                      <SelectItem value="incremental">Incremental Backup</SelectItem>
                      <SelectItem value="differential">Differential Backup</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Pilih Data</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="database" defaultChecked />
                      <label
                        htmlFor="database"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Database
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="files" defaultChecked />
                      <label
                        htmlFor="files"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        File & Media
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="settings" defaultChecked />
                      <label
                        htmlFor="settings"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Pengaturan Sistem
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="logs" />
                      <label
                        htmlFor="logs"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Log Sistem
                      </label>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="backup-description">Deskripsi (Opsional)</Label>
                  <Input id="backup-description" placeholder="Deskripsi backup ini" />
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full" onClick={startBackup} disabled={backupInProgress}>
                  {backupInProgress ? (
                    <>
                      <ArrowUpFromLine className="mr-2 h-4 w-4 animate-pulse" />
                      Backup Sedang Berjalan...
                    </>
                  ) : (
                    <>
                      <ArrowUpFromLine className="mr-2 h-4 w-4" />
                      Mulai Backup
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>

          {backupInProgress && (
            <Card>
              <CardHeader>
                <CardTitle>Backup Sedang Berjalan</CardTitle>
                <CardDescription>Mohon tunggu hingga proses selesai</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Progress value={backupProgress} className="h-2" />
                <div className="flex justify-between text-sm">
                  <span>{backupProgress}% Selesai</span>
                  <span>Estimasi: {Math.ceil((100 - backupProgress) / 5) * 0.3} menit lagi</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Database</span>
                    <span>{backupProgress > 30 ? "Selesai" : "Sedang Diproses..."}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>File & Media</span>
                    <span>
                      {backupProgress > 60 ? "Selesai" : backupProgress > 30 ? "Sedang Diproses..." : "Menunggu..."}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Pengaturan Sistem</span>
                    <span>
                      {backupProgress > 90 ? "Selesai" : backupProgress > 60 ? "Sedang Diproses..." : "Menunggu..."}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="restore" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Restore Data</CardTitle>
              <CardDescription>Pulihkan data dari backup yang tersedia</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="restore-source">Pilih Backup</Label>
                <Select>
                  <SelectTrigger id="restore-source">
                    <SelectValue placeholder="Pilih backup untuk restore" />
                  </SelectTrigger>
                  <SelectContent>
                    {recentBackups.map((backup) => (
                      <SelectItem key={backup.id} value={backup.id}>
                        {backup.id} - {formatDate(backup.date)} ({backup.size})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Pilih Data untuk Restore</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="restore-database" defaultChecked />
                    <label
                      htmlFor="restore-database"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Database
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="restore-files" defaultChecked />
                    <label
                      htmlFor="restore-files"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      File & Media
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="restore-settings" defaultChecked />
                    <label
                      htmlFor="restore-settings"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Pengaturan Sistem
                    </label>
                  </div>
                </div>
              </div>

              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Perhatian</AlertTitle>
                <AlertDescription>
                  Proses restore akan menimpa data yang ada saat ini. Pastikan Anda telah membuat backup terbaru sebelum
                  melanjutkan.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="confirm-restore" />
                  <label
                    htmlFor="confirm-restore"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Saya mengerti bahwa proses ini akan menimpa data yang ada saat ini
                  </label>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full" variant="destructive" onClick={startRestore} disabled={restoreInProgress}>
                {restoreInProgress ? (
                  <>
                    <HardDrive className="mr-2 h-4 w-4 animate-pulse" />
                    Restore Sedang Berjalan...
                  </>
                ) : (
                  <>
                    <HardDrive className="mr-2 h-4 w-4" />
                    Mulai Restore
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>

          {restoreInProgress && (
            <Card>
              <CardHeader>
                <CardTitle>Restore Sedang Berjalan</CardTitle>
                <CardDescription>Mohon tunggu hingga proses selesai</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Progress value={restoreProgress} className="h-2" />
                <div className="flex justify-between text-sm">
                  <span>{restoreProgress}% Selesai</span>
                  <span>Estimasi: {Math.ceil((100 - restoreProgress) / 4) * 0.3} menit lagi</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Database</span>
                    <span>{restoreProgress > 40 ? "Selesai" : "Sedang Diproses..."}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>File & Media</span>
                    <span>
                      {restoreProgress > 80 ? "Selesai" : restoreProgress > 40 ? "Sedang Diproses..." : "Menunggu..."}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Pengaturan Sistem</span>
                    <span>
                      {restoreProgress > 95 ? "Selesai" : restoreProgress > 80 ? "Sedang Diproses..." : "Menunggu..."}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Jadwal Backup Otomatis</CardTitle>
              <CardDescription>Atur jadwal backup otomatis untuk data platform Anda</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="schedule-frequency">Frekuensi</Label>
                <Select defaultValue="daily">
                  <SelectTrigger id="schedule-frequency">
                    <SelectValue placeholder="Pilih frekuensi backup" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hourly">Setiap Jam</SelectItem>
                    <SelectItem value="daily">Harian</SelectItem>
                    <SelectItem value="weekly">Mingguan</SelectItem>
                    <SelectItem value="monthly">Bulanan</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="schedule-time">Waktu</Label>
                <Select defaultValue="02:00">
                  <SelectTrigger id="schedule-time">
                    <SelectValue placeholder="Pilih waktu backup" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="00:00">00:00 WIB</SelectItem>
                    <SelectItem value="01:00">01:00 WIB</SelectItem>
                    <SelectItem value="02:00">02:00 WIB</SelectItem>
                    <SelectItem value="03:00">03:00 WIB</SelectItem>
                    <SelectItem value="04:00">04:00 WIB</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="retention-period">Periode Retensi</Label>
                <Select defaultValue="30">
                  <SelectTrigger id="retention-period">
                    <SelectValue placeholder="Pilih periode retensi" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">7 hari</SelectItem>
                    <SelectItem value="14">14 hari</SelectItem>
                    <SelectItem value="30">30 hari</SelectItem>
                    <SelectItem value="60">60 hari</SelectItem>
                    <SelectItem value="90">90 hari</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Tipe Backup</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="schedule-full" defaultChecked />
                    <label
                      htmlFor="schedule-full"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Full Backup (Setiap Minggu)
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="schedule-incremental" defaultChecked />
                    <label
                      htmlFor="schedule-incremental"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Incremental Backup (Harian)
                    </label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Notifikasi</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="notify-success" defaultChecked />
                    <label
                      htmlFor="notify-success"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Kirim notifikasi saat backup berhasil
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="notify-failure" defaultChecked />
                    <label
                      htmlFor="notify-failure"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Kirim notifikasi saat backup gagal
                    </label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Batal</Button>
              <Button>Simpan Jadwal</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Jadwal Backup Aktif</CardTitle>
              <CardDescription>Jadwal backup yang sedang berjalan</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nama</TableHead>
                    <TableHead>Frekuensi</TableHead>
                    <TableHead>Waktu</TableHead>
                    <TableHead>Tipe</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Backup Harian</TableCell>
                    <TableCell>Harian</TableCell>
                    <TableCell>02:00 WIB</TableCell>
                    <TableCell>Incremental</TableCell>
                    <TableCell>
                      <Badge className="bg-green-500">Aktif</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Backup Mingguan</TableCell>
                    <TableCell>Mingguan (Minggu)</TableCell>
                    <TableCell>01:00 WIB</TableCell>
                    <TableCell>Full</TableCell>
                    <TableCell>
                      <Badge className="bg-green-500">Aktif</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Backup Bulanan</TableCell>
                    <TableCell>Bulanan (Tanggal 1)</TableCell>
                    <TableCell>00:00 WIB</TableCell>
                    <TableCell>Full</TableCell>
                    <TableCell>
                      <Badge className="bg-green-500">Aktif</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Statistik Backup</CardTitle>
            <CardDescription>Statistik backup 30 hari terakhir</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Backup Berhasil</span>
                  <span className="text-sm font-medium text-green-500">28</span>
                </div>
                <Progress value={93} className="h-2 bg-muted" />
                <div className="text-xs text-muted-foreground text-right">93% dari total backup</div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Backup Gagal</span>
                  <span className="text-sm font-medium text-red-500">2</span>
                </div>
                <Progress value={7} className="h-2 bg-muted" />
                <div className="text-xs text-muted-foreground text-right">7% dari total backup</div>
              </div>
              <Separator />
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-muted-foreground">Rata-rata Ukuran</div>
                  <div className="text-lg font-medium">2.8 GB</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Rata-rata Durasi</div>
                  <div className="text-lg font-medium">12m 24s</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Total Backup</div>
                  <div className="text-lg font-medium">30</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Backup Otomatis</div>
                  <div className="text-lg font-medium">28</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Lokasi Penyimpanan</CardTitle>
            <CardDescription>Konfigurasi lokasi penyimpanan backup</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4 rounded-lg border p-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <Database className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium">Penyimpanan Utama</h3>
                <p className="text-sm text-muted-foreground">Supabase Storage</p>
              </div>
              <Badge className="bg-green-500">Aktif</Badge>
            </div>
            <div className="flex items-center space-x-4 rounded-lg border p-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <HardDrive className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium">Penyimpanan Sekunder</h3>
                <p className="text-sm text-muted-foreground">Amazon S3</p>
              </div>
              <Badge variant="outline">Tidak Aktif</Badge>
            </div>
            <div className="flex items-center space-x-4 rounded-lg border p-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <UploadCloud className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium">Tambah Penyimpanan</h3>
                <p className="text-sm text-muted-foreground">Konfigurasi penyimpanan tambahan</p>
              </div>
              <Button variant="outline" size="sm">
                Tambah
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
