import { NextRequest, NextResponse } from 'next/server';
import { productImportExportService } from '@/lib/services/product-import-export';
import { getServerClient } from '@/lib/supabase';

// POST - Seed sample import/export jobs
export async function POST(request: NextRequest) {
  try {
    const supabase = getServerClient()

    // Clear existing data first
    await supabase.from('product_import_export_jobs').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    // Sample import/export jobs
    const sampleJobs = [
      {
        type: 'import' as const,
        file_name: 'products_import_2024-01-15.csv',
        file_url: '/uploads/products_import_2024-01-15.csv',
        file_size: 1024 * 25, // 25KB
        mapping_config: {
          name: 'Product Name',
          sku: 'SKU',
          price: 'Price',
          description: 'Description',
          category: 'Category'
        },
        options: {
          skip_duplicates: true,
          update_existing: false,
          validate_data: true
        },
        created_by: 'admin-user-1'
      },
      {
        type: 'export' as const,
        file_name: 'products_export_2024-01-14.xlsx',
        file_url: '/exports/products_export_2024-01-14.xlsx',
        file_size: 1024 * 150, // 150KB
        filters: {
          status: 'active',
          category: 'electronics'
        },
        options: {
          format: 'xlsx',
          include_images: true,
          include_variants: true
        },
        created_by: 'admin-user-1'
      },
      {
        type: 'import' as const,
        file_name: 'inventory_update_2024-01-13.csv',
        file_url: '/uploads/inventory_update_2024-01-13.csv',
        file_size: 1024 * 8, // 8KB
        mapping_config: {
          sku: 'SKU',
          quantity: 'Stock Quantity'
        },
        options: {
          skip_duplicates: false,
          update_existing: true,
          validate_data: true
        },
        created_by: 'store-manager-1'
      },
      {
        type: 'export' as const,
        file_name: 'low_stock_report_2024-01-12.csv',
        file_url: '/exports/low_stock_report_2024-01-12.csv',
        file_size: 1024 * 12, // 12KB
        filters: {
          inventory_quantity: { lte: 10 },
          status: 'active'
        },
        options: {
          format: 'csv',
          include_inventory: true
        },
        created_by: 'inventory-manager-1'
      },
      {
        type: 'import' as const,
        file_name: 'new_products_batch_2024-01-10.xlsx',
        file_url: '/uploads/new_products_batch_2024-01-10.xlsx',
        file_size: 1024 * 45, // 45KB
        mapping_config: {
          name: 'Product Name',
          sku: 'Product Code',
          price: 'Selling Price',
          cost: 'Cost Price',
          description: 'Product Description',
          category: 'Product Category',
          brand: 'Brand Name'
        },
        options: {
          skip_duplicates: true,
          update_existing: false,
          validate_data: true
        },
        created_by: 'product-manager-1'
      }
    ]

    const createdJobs = []
    const errors = []

    for (const jobData of sampleJobs) {
      try {
        const job = await productImportExportService.createJob(jobData)

        // Simulate different job statuses and progress
        let status: 'pending' | 'processing' | 'completed' | 'failed' = 'completed'
        let progress = 100
        let additionalData: any = {}

        // Randomize some job statuses for variety
        const random = Math.random()
        if (random < 0.1) {
          status = 'pending'
          progress = 0
        } else if (random < 0.2) {
          status = 'processing'
          progress = Math.floor(Math.random() * 80) + 10
        } else if (random < 0.05) {
          status = 'failed'
          progress = Math.floor(Math.random() * 50)
          additionalData.error_log = ['File format validation failed', 'Invalid SKU format in row 15']
        } else {
          // Completed job
          if (jobData.type === 'import') {
            additionalData = {
              processed_count: Math.floor(Math.random() * 100) + 50,
              success_count: Math.floor(Math.random() * 90) + 45,
              error_count: Math.floor(Math.random() * 5),
              result_summary: {
                total_rows: Math.floor(Math.random() * 100) + 50,
                imported: Math.floor(Math.random() * 90) + 45,
                skipped: Math.floor(Math.random() * 3),
                errors: Math.floor(Math.random() * 5),
                warnings: Math.floor(Math.random() * 2)
              }
            }
          } else {
            additionalData = {
              processed_count: Math.floor(Math.random() * 200) + 100,
              success_count: Math.floor(Math.random() * 200) + 100,
              error_count: 0,
              result_summary: {
                total_products: Math.floor(Math.random() * 200) + 100,
                exported: Math.floor(Math.random() * 200) + 100,
                file_size: `${Math.floor(Math.random() * 500) + 50}KB`,
                format: jobData.options.format?.toUpperCase() || 'CSV'
              }
            }
          }
        }

        // Update job with status and additional data
        const updatedJob = await productImportExportService.updateJob(job.id, {
          status,
          progress,
          started_at: status !== 'pending' ? new Date(Date.now() - Math.random() * 86400000).toISOString() : undefined,
          completed_at: ['completed', 'failed'].includes(status) ? new Date().toISOString() : undefined,
          ...additionalData
        })

        createdJobs.push(updatedJob)
      } catch (error) {
        console.error(`Error creating job:`, error)
        errors.push({
          job_type: jobData.type,
          file_name: jobData.file_name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      message: 'Import/Export jobs seeded successfully',
      created: createdJobs.length,
      errors: errors.length,
      errorDetails: errors,
      jobs: createdJobs
    }, { status: 201 })

  } catch (error) {
    console.error('Error seeding import/export jobs:', error)
    const errorMessage = error instanceof Error ? error.message : 'Failed to seed import/export jobs'
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}
