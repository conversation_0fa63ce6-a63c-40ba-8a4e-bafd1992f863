"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"

interface SwipeGalleryProps {
  images: {
    src: string
    alt: string
  }[]
  aspectRatio?: "square" | "video" | "wide"
  className?: string
}

export function SwipeGallery({ images, aspectRatio = "square", className }: SwipeGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const [isSwiping, setIsSwiping] = useState(false)
  const [translateX, setTranslateX] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  // Minimum swipe distance in pixels
  const minSwipeDistance = 50

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  const handleNext = () => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX)
    setIsSwiping(true)
    setTranslateX(0)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return

    const currentTouch = e.targetTouches[0].clientX
    setTouchEnd(currentTouch)

    // Calculate distance moved as a percentage of container width
    if (containerRef.current) {
      const containerWidth = containerRef.current.offsetWidth
      const touchDiff = currentTouch - touchStart

      // Limit the drag to the next/previous image only
      const maxTranslate = containerWidth * 0.5
      const limitedTranslate = Math.max(Math.min(touchDiff, maxTranslate), -maxTranslate)

      setTranslateX(limitedTranslate)
    }
  }

  const handleTouchEnd = () => {
    setIsSwiping(false)

    if (!touchStart || !touchEnd) return

    const distance = touchEnd - touchStart
    const isLeftSwipe = distance < -minSwipeDistance
    const isRightSwipe = distance > minSwipeDistance

    if (isLeftSwipe && currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }

    if (isRightSwipe && currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }

    // Reset
    setTouchStart(null)
    setTouchEnd(null)
    setTranslateX(0)
  }

  // Reset translateX when not swiping
  useEffect(() => {
    if (!isSwiping) {
      setTranslateX(0)
    }
  }, [isSwiping])

  // Map aspect ratio to Tailwind classes
  const aspectRatioClasses = {
    square: "aspect-square",
    video: "aspect-video",
    wide: "aspect-[16/9]",
  }

  return (
    <div className={cn("relative overflow-hidden rounded-lg", className)}>
      <div
        ref={containerRef}
        className={cn("relative", aspectRatioClasses[aspectRatio])}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div
          className="flex transition-transform duration-300 ease-out h-full"
          style={{
            transform: isSwiping ? `translateX(${translateX}px)` : `translateX(-${currentIndex * 100}%)`,
          }}
        >
          {images.map((image, index) => (
            <div key={index} className="relative w-full h-full flex-shrink-0" aria-hidden={index !== currentIndex}>
              <Image
                src={image.src || "/placeholder.svg"}
                alt={image.alt}
                fill
                className="object-cover"
                priority={index === 0}
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Navigation arrows */}
      {images.length > 1 && (
        <>
          <button
            onClick={handlePrevious}
            disabled={currentIndex === 0}
            className={cn(
              "absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 flex items-center justify-center rounded-full bg-white/70 text-gray-800 shadow-sm transition-opacity",
              currentIndex === 0 ? "opacity-40 cursor-not-allowed" : "opacity-70 hover:opacity-100",
            )}
            aria-label="Previous image"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>

          <button
            onClick={handleNext}
            disabled={currentIndex === images.length - 1}
            className={cn(
              "absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 flex items-center justify-center rounded-full bg-white/70 text-gray-800 shadow-sm transition-opacity",
              currentIndex === images.length - 1 ? "opacity-40 cursor-not-allowed" : "opacity-70 hover:opacity-100",
            )}
            aria-label="Next image"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </>
      )}

      {/* Dots indicator */}
      {images.length > 1 && (
        <div className="absolute bottom-2 left-0 right-0 flex justify-center space-x-1.5">
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={cn(
                "w-2 h-2 rounded-full transition-all",
                index === currentIndex ? "bg-white w-4" : "bg-white/50 hover:bg-white/80",
              )}
              aria-label={`Go to image ${index + 1}`}
              aria-current={index === currentIndex}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Ensure we have both default and named exports
export default SwipeGallery
