"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { eventCategories } from "./mock-data"

interface EventCategoriesProps {
  selectedCategories: string[]
  onCategoryChange: (category: string) => void
}

export function EventCategories({ selectedCategories, onCategoryChange }: EventCategoriesProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>Kategori Event</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="pt-4">
        <div className="space-y-4">
          {eventCategories.map((category) => (
            <div key={category.id} className="flex items-center space-x-2">
              <Checkbox
                id={`category-${category.id}`}
                checked={selectedCategories.includes(category.name)}
                onChe<PERSON><PERSON>hange={() => onCategoryChange(category.name)}
              />
              <Label
                htmlFor={`category-${category.id}`}
                className="flex flex-1 items-center justify-between text-sm font-medium"
              >
                {category.name}
                <span className="rounded-full bg-muted px-2 py-0.5 text-xs text-muted-foreground">
                  {category.count}
                </span>
              </Label>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
