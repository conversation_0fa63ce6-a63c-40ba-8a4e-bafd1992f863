"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { PlusIcon, PencilIcon, TrashIcon, EyeIcon, MailIcon, CodeIcon, SendIcon, CopyIcon } from "lucide-react"

export function EmailTemplates() {
  const [templates, setTemplates] = useState([])
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [activeTab, setActiveTab] = useState("templates")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulasi pengambilan data
    setTimeout(() => {
      const mockTemplates = [
        {
          id: 1,
          name: "Welcome Email",
          description: "Sent to new users after registration",
          category: "Onboarding",
          lastUpdated: "2023-10-05",
          status: "active",
          subject: "Welcome to SellZio - Get Started with Your Account",
          preview: "/placeholder.svg?key=ip4uo",
        },
        {
          id: 2,
          name: "Order Confirmation",
          description: "Sent after a successful purchase",
          category: "Transactional",
          lastUpdated: "2023-09-18",
          status: "active",
          subject: "Your Order #{{order_id}} Confirmation",
          preview: "/placeholder.svg?key=f0xw5",
        },
        {
          id: 3,
          name: "Shipping Notification",
          description: "Sent when an order ships",
          category: "Transactional",
          lastUpdated: "2023-09-20",
          status: "active",
          subject: "Your Order #{{order_id}} Has Shipped",
          preview: "/placeholder.svg?key=fhq58",
        },
        {
          id: 4,
          name: "Password Reset",
          description: "Sent when a user requests a password reset",
          category: "Account",
          lastUpdated: "2023-08-15",
          status: "active",
          subject: "Reset Your SellZio Password",
          preview: "/password-reset-email.png",
        },
        {
          id: 5,
          name: "Abandoned Cart",
          description: "Sent to users who left items in their cart",
          category: "Marketing",
          lastUpdated: "2023-10-10",
          status: "active",
          subject: "Complete Your Purchase - Items Waiting in Your Cart",
          preview: "/abandoned-cart-email.png",
        },
        {
          id: 6,
          name: "Black Friday Promotion",
          description: "Special promotion for Black Friday",
          category: "Marketing",
          lastUpdated: "2023-10-22",
          status: "draft",
          subject: "Black Friday Deals - Save Up to 70%",
          preview: "/black-friday-email.png",
        },
      ]

      setTemplates(mockTemplates)
      setIsLoading(false)
    }, 1000)
  }, [])

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template)
    setActiveTab("editor")
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "draft":
        return "bg-yellow-100 text-yellow-800"
      case "inactive":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (isLoading) {
    return (
      <div className="grid gap-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <div className="aspect-video w-full bg-gray-200"></div>
              <CardHeader>
                <div className="h-5 w-32 rounded bg-gray-200"></div>
                <div className="h-4 w-48 rounded bg-gray-200"></div>
              </CardHeader>
              <CardFooter>
                <div className="h-9 w-full rounded bg-gray-200"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-3 lg:w-[400px]">
        <TabsTrigger value="templates">All Templates</TabsTrigger>
        <TabsTrigger value="editor" disabled={!selectedTemplate}>
          Template Editor
        </TabsTrigger>
        <TabsTrigger value="variables">Variables</TabsTrigger>
      </TabsList>

      {/* All Templates Tab */}
      <TabsContent value="templates" className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-bold">Email Templates</h2>
            <p className="text-sm text-muted-foreground">Manage your platform email templates</p>
          </div>
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" />
            Create New Template
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="onboarding">Onboarding</SelectItem>
              <SelectItem value="transactional">Transactional</SelectItem>
              <SelectItem value="account">Account</SelectItem>
              <SelectItem value="marketing">Marketing</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="active">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {templates.map((template) => (
            <Card key={template.id} className="overflow-hidden">
              <div className="aspect-video w-full overflow-hidden">
                <img
                  src={template.preview || "/placeholder.svg"}
                  alt={template.name}
                  className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                />
              </div>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <Badge variant="outline">{template.category}</Badge>
                  <Badge variant="outline" className={getStatusColor(template.status)}>
                    {template.status}
                  </Badge>
                </div>
                <CardTitle>{template.name}</CardTitle>
                <CardDescription>{template.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground">
                  <span>Subject: </span>
                  <span className="font-medium">{template.subject}</span>
                </div>
                <div className="mt-1 text-sm text-muted-foreground">
                  <span>Last updated: {template.lastUpdated}</span>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" onClick={() => handleTemplateSelect(template)}>
                  <PencilIcon className="mr-2 h-4 w-4" />
                  Edit
                </Button>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <EyeIcon className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                  <Button variant="outline" size="sm" className="text-red-500 hover:text-red-700">
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </TabsContent>

      {/* Template Editor Tab */}
      <TabsContent value="editor" className="space-y-6">
        {selectedTemplate && (
          <>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h2 className="text-2xl font-bold">Editing: {selectedTemplate.name}</h2>
                <p className="text-sm text-muted-foreground">Last saved: 5 minutes ago</p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline">
                  <EyeIcon className="mr-2 h-4 w-4" />
                  Preview
                </Button>
                <Button variant="outline">
                  <SendIcon className="mr-2 h-4 w-4" />
                  Test Send
                </Button>
                <Select defaultValue={selectedTemplate.status}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                <Button>Save Changes</Button>
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-[1fr_3fr]">
              {/* Template Settings Panel */}
              <Card>
                <CardHeader>
                  <CardTitle>Template Settings</CardTitle>
                  <CardDescription>Configure email template properties</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="template-name">Template Name</Label>
                    <Input id="template-name" defaultValue={selectedTemplate.name} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="template-description">Description</Label>
                    <Textarea
                      id="template-description"
                      defaultValue={selectedTemplate.description}
                      placeholder="Describe the purpose of this email template..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="template-category">Category</Label>
                    <Select defaultValue={selectedTemplate.category.toLowerCase()}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="onboarding">Onboarding</SelectItem>
                        <SelectItem value="transactional">Transactional</SelectItem>
                        <SelectItem value="account">Account</SelectItem>
                        <SelectItem value="marketing">Marketing</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <Label htmlFor="template-subject">Email Subject</Label>
                    <Input id="template-subject" defaultValue={selectedTemplate.subject} />
                    <p className="text-xs text-muted-foreground">
                      You can use variables like {{ user_name }} in the subject line.
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="template-sender">Sender Name</Label>
                    <Input id="template-sender" defaultValue="SellZio Team" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="template-reply-to">Reply-To Email</Label>
                    <Input id="template-reply-to" defaultValue="<EMAIL>" />
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <Label>Template Variables</Label>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline" className="cursor-pointer">
                        <CopyIcon className="mr-1 h-3 w-3" />
                        {`{{ user_name }}`}
                      </Badge>
                      <Badge variant="outline" className="cursor-pointer">
                        <CopyIcon className="mr-1 h-3 w-3" />
                        {`{{ order_id }}`}
                      </Badge>
                      <Badge variant="outline" className="cursor-pointer">
                        <CopyIcon className="mr-1 h-3 w-3" />
                        {`{{ store_name }}`}
                      </Badge>
                      <Badge variant="outline" className="cursor-pointer">
                        <CopyIcon className="mr-1 h-3 w-3" />
                        {`{{ reset_link }}`}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">Click on a variable to copy it to clipboard.</p>
                  </div>
                </CardContent>
              </Card>

              {/* Email Template Editor */}
              <Card>
                <CardHeader>
                  <Tabs defaultValue="visual" className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="visual">
                        <MailIcon className="mr-2 h-4 w-4" />
                        Visual Editor
                      </TabsTrigger>
                      <TabsTrigger value="code">
                        <CodeIcon className="mr-2 h-4 w-4" />
                        HTML Editor
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="visual" className="mt-4 space-y-4">
                      <div className="flex flex-wrap gap-2 border-b pb-4">
                        <Button variant="outline" size="sm">
                          Header
                        </Button>
                        <Button variant="outline" size="sm">
                          Text
                        </Button>
                        <Button variant="outline" size="sm">
                          Button
                        </Button>
                        <Button variant="outline" size="sm">
                          Image
                        </Button>
                        <Button variant="outline" size="sm">
                          Divider
                        </Button>
                        <Button variant="outline" size="sm">
                          Spacer
                        </Button>
                        <Button variant="outline" size="sm">
                          Social
                        </Button>
                        <Button variant="outline" size="sm">
                          Footer
                        </Button>
                      </div>
                      <div className="border rounded-md p-4 min-h-[500px]">
                        <div className="bg-gray-50 p-4 rounded-md mb-4">
                          <div className="bg-white p-4 rounded-md border">
                            <img src="/your-logo.png" alt="Logo" className="h-8 mx-auto mb-4" />
                            <h1 className="text-xl font-bold text-center mb-4">Welcome to SellZio!</h1>
                            <p className="text-center mb-6">
                              Thank you for joining our platform. We're excited to have you on board!
                            </p>
                            <div className="text-center mb-6">
                              <button className="bg-blue-600 text-white px-6 py-2 rounded-md">Get Started</button>
                            </div>
                            <p className="text-sm text-gray-600 mb-4">
                              Here are a few things you can do with your new account:
                            </p>
                            <ul className="text-sm text-gray-600 list-disc pl-5 mb-6">
                              <li>Set up your store profile</li>
                              <li>Add your first products</li>
                              <li>Customize your storefront</li>
                              <li>Connect payment methods</li>
                            </ul>
                            <p className="text-sm text-gray-600 mb-6">
                              If you have any questions, please don't hesitate to contact our support team.
                            </p>
                            <div className="text-center text-xs text-gray-500 pt-4 border-t">
                              <p>© 2023 SellZio. All rights reserved.</p>
                              <p className="mt-2">
                                <a href="#" className="text-blue-600">
                                  Unsubscribe
                                </a>{" "}
                                |
                                <a href="#" className="text-blue-600">
                                  {" "}
                                  Privacy Policy
                                </a>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="code" className="mt-4 space-y-4">
                      <Textarea
                        className="font-mono h-[500px] text-sm"
                        defaultValue={`<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Welcome to SellZio</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 0; }
    .container { max-width: 600px; margin: 0 auto; }
    .header { text-align: center; padding: 20px; }
    .content { padding: 20px; background-color: #ffffff; }
    .button { display: inline-block; padding: 10px 20px; background-color: #2563eb; color: #ffffff; text-decoration: none; border-radius: 4px; }
    .footer { text-align: center; padding: 20px; font-size: 12px; color: #6b7280; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://yourdomain.com/logo.png" alt="SellZio Logo" height="32">
    </div>
    <div class="content">
      <h1>Welcome to SellZio!</h1>
      <p>Hello {{user_name}},</p>
      <p>Thank you for joining our platform. We're excited to have you on board!</p>
      <p style="text-align: center; margin: 30px 0;">
        <a href="{{dashboard_url}}" class="button">Get Started</a>
      </p>
      <p>Here are a few things you can do with your new account:</p>
      <ul>
        <li>Set up your store profile</li>
        <li>Add your first products</li>
        <li>Customize your storefront</li>
        <li>Connect payment methods</li>
      </ul>
      <p>If you have any questions, please don't hesitate to contact our support team.</p>
    </div>
    <div class="footer">
      <p>&copy; 2023 SellZio. All rights reserved.</p>
      <p>
        <a href="{{unsubscribe_url}}">Unsubscribe</a> | 
        <a href="{{privacy_url}}">Privacy Policy</a>
      </p>
    </div>
  </div>
</body>
</html>`}
                      />
                    </TabsContent>
                  </Tabs>
                </CardHeader>
                <CardFooter className="flex justify-between">
                  <Button variant="outline">Test Send</Button>
                  <Button>Save Template</Button>
                </CardFooter>
              </Card>
            </div>
          </>
        )}
      </TabsContent>

      {/* Variables Tab */}
      <TabsContent value="variables" className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-bold">Template Variables</h2>
            <p className="text-sm text-muted-foreground">Manage dynamic content placeholders for email templates</p>
          </div>
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" />
            Add New Variable
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Available Variables</CardTitle>
            <CardDescription>Variables that can be used in email templates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative overflow-x-auto">
              <table className="w-full text-left text-sm">
                <thead className="bg-gray-50 text-xs uppercase">
                  <tr>
                    <th scope="col" className="px-6 py-3">
                      Variable Name
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Description
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Example Value
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Usage
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">user_name</td>
                    <td className="px-6 py-4">User's full name</td>
                    <td className="px-6 py-4">John Doe</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="font-mono">
                        {`{{ user_name }}`}
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">order_id</td>
                    <td className="px-6 py-4">Order identifier</td>
                    <td className="px-6 py-4">ORD-12345</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="font-mono">
                        {`{{ order_id }}`}
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">store_name</td>
                    <td className="px-6 py-4">Store name</td>
                    <td className="px-6 py-4">Fashion Boutique</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="font-mono">
                        {`{{ store_name }}`}
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">reset_link</td>
                    <td className="px-6 py-4">Password reset URL</td>
                    <td className="px-6 py-4">https://sellzio.com/reset/token123</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="font-mono">
                        {`{{ reset_link }}`}
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">dashboard_url</td>
                    <td className="px-6 py-4">User dashboard URL</td>
                    <td className="px-6 py-4">https://sellzio.com/dashboard</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="font-mono">
                        {`{{ dashboard_url }}`}
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">unsubscribe_url</td>
                    <td className="px-6 py-4">Unsubscribe URL</td>
                    <td className="px-6 py-4">https://sellzio.com/unsubscribe/user123</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="font-mono">
                        {`{{ unsubscribe_url }}`}
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="bg-white">
                    <td className="px-6 py-4 font-medium">privacy_url</td>
                    <td className="px-6 py-4">Privacy policy URL</td>
                    <td className="px-6 py-4">https://sellzio.com/privacy</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="font-mono">
                        {`{{ privacy_url }}`}
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Template Assignments</CardTitle>
            <CardDescription>Manage which templates are used for different events</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative overflow-x-auto">
              <table className="w-full text-left text-sm">
                <thead className="bg-gray-50 text-xs uppercase">
                  <tr>
                    <th scope="col" className="px-6 py-3">
                      Event
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Template
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">User Registration</td>
                    <td className="px-6 py-4">Welcome Email</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="bg-green-100 text-green-800">
                        Active
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">Order Placed</td>
                    <td className="px-6 py-4">Order Confirmation</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="bg-green-100 text-green-800">
                        Active
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">Order Shipped</td>
                    <td className="px-6 py-4">Shipping Notification</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="bg-green-100 text-green-800">
                        Active
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">Password Reset Request</td>
                    <td className="px-6 py-4">Password Reset</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="bg-green-100 text-green-800">
                        Active
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">Cart Abandoned (24h)</td>
                    <td className="px-6 py-4">Abandoned Cart</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="bg-green-100 text-green-800">
                        Active
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="bg-white">
                    <td className="px-6 py-4 font-medium">Black Friday Campaign</td>
                    <td className="px-6 py-4">Black Friday Promotion</td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                        Draft
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
