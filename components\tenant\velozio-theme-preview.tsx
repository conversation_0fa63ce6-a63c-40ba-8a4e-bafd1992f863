"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Monitor,
  Smartphone,
  Tablet,
  Check,
  Loader2,
  Search,
  ShoppingCart,
  Heart,
  User,
  Menu,
  ChevronDown,
} from "lucide-react"

export function VelozioThemePreview() {
  const router = useRouter()
  const [viewMode, setViewMode] = useState<"desktop" | "tablet" | "mobile">("desktop")
  const [activating, setActivating] = useState(false)

  const handleActivateTheme = async () => {
    setActivating(true)
    // Simulasi aktivasi tema
    setTimeout(() => {
      setActivating(false)
      router.push("/tenant/dashboard/themes")
    }, 1500)
  }

  const getPreviewWidth = () => {
    switch (viewMode) {
      case "mobile":
        return "max-w-[375px]"
      case "tablet":
        return "max-w-[768px]"
      case "desktop":
      default:
        return "max-w-full"
    }
  }

  // Tema Velozio memiliki warna-warna khas
  const velozioTheme = {
    colors: {
      primary: "#FF6B35",
      secondary: "#2EC4B6",
      accent: "#E71D36",
      background: "#FFFFFF",
      foreground: "#011627",
      muted: "#F6F8FA",
      mutedForeground: "#546E7A",
      border: "#E2E8F0",
      input: "#F1F5F9",
      card: "#FFFFFF",
      cardForeground: "#011627",
      destructive: "#E71D36",
      destructiveForeground: "#FFFFFF",
    },
    fonts: {
      heading: "'Poppins', sans-serif",
      body: "'Inter', sans-serif",
    },
    layout: {
      header: "fixed",
      footer: "standard",
      sidebar: "floating",
    },
    logo: {
      main: "/your-logo.png",
    },
  }

  // Styling untuk preview
  const previewStyles = {
    "--primary": velozioTheme.colors.primary,
    "--secondary": velozioTheme.colors.secondary,
    "--accent": velozioTheme.colors.accent,
    "--background": velozioTheme.colors.background,
    "--foreground": velozioTheme.colors.foreground,
    "--muted": velozioTheme.colors.muted,
    "--muted-foreground": velozioTheme.colors.mutedForeground,
    "--border": velozioTheme.colors.border,
    "--input": velozioTheme.colors.input,
    "--card": velozioTheme.colors.card,
    "--card-foreground": velozioTheme.colors.cardForeground,
    "--destructive": velozioTheme.colors.destructive,
    "--destructive-foreground": velozioTheme.colors.destructiveForeground,
    "--font-heading": velozioTheme.fonts.heading,
    "--font-body": velozioTheme.fonts.body,
  } as React.CSSProperties

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Themes
        </Button>
        <div className="flex items-center gap-2">
          <TabsList>
            <TabsTrigger value="desktop" onClick={() => setViewMode("desktop")}>
              <Monitor className="h-4 w-4" />
              <span className="ml-2 hidden sm:inline">Desktop</span>
            </TabsTrigger>
            <TabsTrigger value="tablet" onClick={() => setViewMode("tablet")}>
              <Tablet className="h-4 w-4" />
              <span className="ml-2 hidden sm:inline">Tablet</span>
            </TabsTrigger>
            <TabsTrigger value="mobile" onClick={() => setViewMode("mobile")}>
              <Smartphone className="h-4 w-4" />
              <span className="ml-2 hidden sm:inline">Mobile</span>
            </TabsTrigger>
          </TabsList>
          <Button onClick={handleActivateTheme} disabled={activating}>
            {activating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Activating...
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                Activate Theme
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <div className="bg-gray-100 border-b p-2 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-500" />
            <div className="w-3 h-3 rounded-full bg-yellow-500" />
            <div className="w-3 h-3 rounded-full bg-green-500" />
          </div>
          <div className="bg-white rounded px-2 py-1 text-xs text-gray-500 flex-1 mx-4 text-center">
            Velozio Theme Preview
          </div>
          <div className="w-6" />
        </div>
        <div className="bg-gray-50 p-4 flex justify-center overflow-auto" style={{ minHeight: "600px" }}>
          <div className={`transition-all duration-300 ${getPreviewWidth()}`}>
            <div className="bg-white rounded shadow-lg overflow-hidden" style={previewStyles}>
              {/* Header Velozio */}
              <header
                className="sticky top-0 z-50 border-b"
                style={{
                  backgroundColor: velozioTheme.colors.background,
                  borderColor: velozioTheme.colors.border,
                }}
              >
                {/* Top Bar */}
                <div className="bg-primary text-white py-1 px-4 text-center text-sm">
                  <p>Free shipping on all orders over $50! Limited time offer</p>
                </div>

                {/* Main Header */}
                <div className="container mx-auto py-4 px-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <button className="p-2 mr-2 lg:hidden">
                        <Menu className="h-6 w-6" />
                      </button>
                      <img src="/your-logo.png" alt="Velozio" className="h-8" />
                    </div>

                    {/* Search Bar - Khas Velozio */}
                    <div className="hidden md:flex flex-1 max-w-xl mx-4">
                      <div className="relative w-full">
                        <input
                          type="text"
                          placeholder="Search for products..."
                          className="w-full py-2 pl-4 pr-10 rounded-lg border"
                          style={{
                            borderColor: velozioTheme.colors.border,
                            backgroundColor: velozioTheme.colors.input,
                          }}
                        />
                        <button
                          className="absolute right-0 top-0 h-full px-3 text-gray-500 flex items-center"
                          style={{ color: velozioTheme.colors.mutedForeground }}
                        >
                          <Search className="h-5 w-5" />
                        </button>
                      </div>
                    </div>

                    {/* Action Icons */}
                    <div className="flex items-center space-x-4">
                      <button className="p-2 relative">
                        <Heart className="h-6 w-6" />
                        <span className="absolute -top-1 -right-1 bg-accent text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                          3
                        </span>
                      </button>
                      <button className="p-2 relative">
                        <ShoppingCart className="h-6 w-6" />
                        <span className="absolute -top-1 -right-1 bg-accent text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                          2
                        </span>
                      </button>
                      <button className="p-2">
                        <User className="h-6 w-6" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Navigation */}
                <nav className="border-t hidden lg:block" style={{ borderColor: velozioTheme.colors.border }}>
                  <div className="container mx-auto px-4">
                    <ul className="flex space-x-8">
                      <li>
                        <a href="#" className="py-3 inline-block font-medium">
                          Home
                        </a>
                      </li>
                      <li className="relative group">
                        <a href="#" className="py-3 inline-flex items-center font-medium">
                          Categories <ChevronDown className="ml-1 h-4 w-4" />
                        </a>
                      </li>
                      <li>
                        <a href="#" className="py-3 inline-block font-medium">
                          New Arrivals
                        </a>
                      </li>
                      <li>
                        <a href="#" className="py-3 inline-block font-medium">
                          Best Sellers
                        </a>
                      </li>
                      <li>
                        <a href="#" className="py-3 inline-block font-medium">
                          Deals
                        </a>
                      </li>
                      <li>
                        <a href="#" className="py-3 inline-block font-medium">
                          Brands
                        </a>
                      </li>
                    </ul>
                  </div>
                </nav>
              </header>

              {/* Hero Section - Khas Velozio */}
              <section className="relative">
                <div className="bg-gradient-to-r from-secondary to-primary text-white py-12 px-4">
                  <div className="container mx-auto max-w-6xl">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                      <div>
                        <h1 className="text-4xl font-bold mb-4" style={{ fontFamily: velozioTheme.fonts.heading }}>
                          Summer Collection 2023
                        </h1>
                        <p
                          className="text-lg mb-6 opacity-90"
                          style={{
                            fontFamily: velozioTheme.fonts.body,
                          }}
                        >
                          Discover the latest trends and styles for the summer season. Shop now and get up to 40% off on
                          selected items.
                        </p>
                        <div className="flex flex-wrap gap-4">
                          <button
                            className="px-6 py-3 rounded-md font-medium"
                            style={{
                              backgroundColor: "#FFFFFF",
                              color: velozioTheme.colors.primary,
                            }}
                          >
                            Shop Now
                          </button>
                          <button
                            className="px-6 py-3 rounded-md font-medium"
                            style={{
                              backgroundColor: "rgba(255,255,255,0.2)",
                              color: "#FFFFFF",
                            }}
                          >
                            View Lookbook
                          </button>
                        </div>
                      </div>
                      <div>
                        <img
                          src="/diverse-sneaker-collection.png"
                          alt="Summer Collection"
                          className="rounded-lg shadow-lg"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Category Navigation - Khas Velozio */}
              <section className="py-8 px-4" style={{ backgroundColor: velozioTheme.colors.muted }}>
                <div className="container mx-auto">
                  <div className="flex overflow-x-auto pb-2 gap-4 hide-scrollbar">
                    {["All", "Shoes", "Clothing", "Accessories", "Electronics", "Home", "Beauty", "Sports"].map(
                      (category) => (
                        <button
                          key={category}
                          className={`px-6 py-2 rounded-full whitespace-nowrap ${
                            category === "All" ? "bg-primary text-white" : "bg-white"
                          }`}
                          style={{
                            borderColor: velozioTheme.colors.border,
                            borderWidth: "1px",
                          }}
                        >
                          {category}
                        </button>
                      ),
                    )}
                  </div>
                </div>
              </section>

              {/* Featured Products - Khas Velozio */}
              <section className="py-12 px-4">
                <div className="container mx-auto max-w-6xl">
                  <div className="flex items-center justify-between mb-8">
                    <h2 className="text-3xl font-bold" style={{ fontFamily: velozioTheme.fonts.heading }}>
                      Featured Products
                    </h2>
                    <a href="#" className="text-primary font-medium" style={{ color: velozioTheme.colors.primary }}>
                      View All
                    </a>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {[
                      {
                        name: "Premium Running Shoes",
                        price: 129.99,
                        image: "/running-shoes-on-track.png",
                        discount: 20,
                        rating: 4.8,
                      },
                      {
                        name: "Wireless Headphones",
                        price: 89.99,
                        image: "/diverse-people-listening-headphones.png",
                        discount: 0,
                        rating: 4.5,
                      },
                      {
                        name: "Designer Handbag",
                        price: 199.99,
                        image: "/stylish-leather-handbag.png",
                        discount: 15,
                        rating: 4.9,
                      },
                      {
                        name: "Mechanical Keyboard",
                        price: 149.99,
                        image: "/mechanical-keyboard.png",
                        discount: 0,
                        rating: 4.7,
                      },
                    ].map((product, index) => (
                      <div
                        key={index}
                        className="rounded-lg overflow-hidden group"
                        style={{
                          backgroundColor: velozioTheme.colors.card,
                          borderColor: velozioTheme.colors.border,
                          borderWidth: "1px",
                        }}
                      >
                        <div className="relative">
                          <img
                            src={product.image || "/placeholder.svg"}
                            alt={product.name}
                            className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
                          />
                          {product.discount > 0 && (
                            <div
                              className="absolute top-2 left-2 bg-accent text-white text-xs font-bold px-2 py-1 rounded"
                              style={{ backgroundColor: velozioTheme.colors.accent }}
                            >
                              {product.discount}% OFF
                            </div>
                          )}
                          <div className="absolute top-2 right-2 flex flex-col gap-2">
                            <button className="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors">
                              <Heart className="h-4 w-4" />
                            </button>
                            <button className="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors">
                              <ShoppingCart className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center mb-1">
                            <div className="flex text-yellow-400">
                              {"★".repeat(Math.floor(product.rating))}
                              {"☆".repeat(5 - Math.floor(product.rating))}
                            </div>
                            <span className="text-xs ml-1 text-gray-500">({product.rating})</span>
                          </div>
                          <h3 className="font-semibold mb-1" style={{ fontFamily: velozioTheme.fonts.heading }}>
                            {product.name}
                          </h3>
                          <div className="flex items-center">
                            <span className="font-bold" style={{ color: velozioTheme.colors.primary }}>
                              $
                              {product.discount > 0
                                ? (product.price * (1 - product.discount / 100)).toFixed(2)
                                : product.price.toFixed(2)}
                            </span>
                            {product.discount > 0 && (
                              <span className="ml-2 text-sm line-through text-gray-500">
                                ${product.price.toFixed(2)}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Feature Banner - Khas Velozio */}
              <section className="py-10 px-4" style={{ backgroundColor: velozioTheme.colors.muted }}>
                <div className="container mx-auto max-w-6xl">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="flex items-center p-6 bg-white rounded-lg shadow-sm">
                      <div
                        className="rounded-full p-3 mr-4"
                        style={{ backgroundColor: `${velozioTheme.colors.primary}20` }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke={velozioTheme.colors.primary}
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M5 12h14" />
                          <path d="M12 5v14" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold" style={{ fontFamily: velozioTheme.fonts.heading }}>
                          Free Shipping
                        </h3>
                        <p className="text-sm text-gray-600">On orders over $50</p>
                      </div>
                    </div>
                    <div className="flex items-center p-6 bg-white rounded-lg shadow-sm">
                      <div
                        className="rounded-full p-3 mr-4"
                        style={{ backgroundColor: `${velozioTheme.colors.secondary}20` }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke={velozioTheme.colors.secondary}
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold" style={{ fontFamily: velozioTheme.fonts.heading }}>
                          Secure Payment
                        </h3>
                        <p className="text-sm text-gray-600">100% secure checkout</p>
                      </div>
                    </div>
                    <div className="flex items-center p-6 bg-white rounded-lg shadow-sm">
                      <div
                        className="rounded-full p-3 mr-4"
                        style={{ backgroundColor: `${velozioTheme.colors.accent}20` }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke={velozioTheme.colors.accent}
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M21 10H3" />
                          <path d="M21 6H3" />
                          <path d="M21 14H3" />
                          <path d="M21 18H3" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold" style={{ fontFamily: velozioTheme.fonts.heading }}>
                          Easy Returns
                        </h3>
                        <p className="text-sm text-gray-600">30 days return policy</p>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Flash Deals - Khas Velozio */}
              <section className="py-12 px-4">
                <div className="container mx-auto max-w-6xl">
                  <div className="bg-gradient-to-r from-accent to-primary text-white p-6 rounded-lg mb-8">
                    <div className="flex items-center justify-between">
                      <div>
                        <h2 className="text-2xl font-bold mb-2" style={{ fontFamily: velozioTheme.fonts.heading }}>
                          Flash Deals
                        </h2>
                        <p className="opacity-90">Limited time offers. Grab them before they're gone!</p>
                      </div>
                      <div className="bg-white text-primary px-4 py-2 rounded-lg font-mono font-bold">23:59:59</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    {[
                      {
                        name: "Smartphone Pro",
                        price: 899.99,
                        discountPrice: 699.99,
                        image: "/modern-smartphone.png",
                      },
                      {
                        name: "Portable Power Bank",
                        price: 49.99,
                        discountPrice: 29.99,
                        image: "/portable-power-bank.png",
                      },
                      {
                        name: "Cozy Hoodie",
                        price: 59.99,
                        discountPrice: 39.99,
                        image: "/cozy-hoodie.png",
                      },
                      {
                        name: "Stylish Sunglasses",
                        price: 129.99,
                        discountPrice: 79.99,
                        image: "/stylish-sunglasses.png",
                      },
                    ].map((deal, index) => (
                      <div
                        key={index}
                        className="border rounded-lg overflow-hidden flex flex-col"
                        style={{ borderColor: velozioTheme.colors.border }}
                      >
                        <div className="relative pt-[100%]">
                          <img
                            src={deal.image || "/placeholder.svg"}
                            alt={deal.name}
                            className="absolute inset-0 w-full h-full object-cover"
                          />
                          <div
                            className="absolute top-2 left-2 text-xs font-bold px-2 py-1 rounded"
                            style={{
                              backgroundColor: velozioTheme.colors.accent,
                              color: "#FFFFFF",
                            }}
                          >
                            SAVE ${(deal.price - deal.discountPrice).toFixed(2)}
                          </div>
                        </div>
                        <div className="p-4 flex-grow flex flex-col">
                          <h3 className="font-semibold mb-2" style={{ fontFamily: velozioTheme.fonts.heading }}>
                            {deal.name}
                          </h3>
                          <div className="flex items-center mb-3">
                            <span className="font-bold text-lg" style={{ color: velozioTheme.colors.primary }}>
                              ${deal.discountPrice.toFixed(2)}
                            </span>
                            <span className="ml-2 text-sm line-through text-gray-500">${deal.price.toFixed(2)}</span>
                          </div>
                          <div className="mt-auto">
                            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                              <div
                                className="h-2 rounded-full"
                                style={{
                                  width: `${Math.floor(Math.random() * 70) + 30}%`,
                                  backgroundColor: velozioTheme.colors.accent,
                                }}
                              ></div>
                            </div>
                            <p className="text-xs text-gray-500">Selling fast! Limited stock available</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Footer - Khas Velozio */}
              <footer
                className="pt-12 pb-6 px-4"
                style={{
                  backgroundColor: velozioTheme.colors.foreground,
                  color: "#FFFFFF",
                }}
              >
                <div className="container mx-auto max-w-6xl">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
                    <div>
                      <img src="/your-logo.png" alt="Velozio" className="h-8 mb-4 bg-white p-1 rounded" />
                      <p className="text-sm opacity-80 mb-4">
                        Velozio is your one-stop marketplace for all your shopping needs. We offer a wide range of
                        products at competitive prices.
                      </p>
                      <div className="flex space-x-4">
                        <a href="#" className="text-white hover:text-primary transition-colors">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                          </svg>
                        </a>
                        <a href="#" className="text-white hover:text-primary transition-colors">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                            <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                            <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                          </svg>
                        </a>
                        <a href="#" className="text-white hover:text-primary transition-colors">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                          </svg>
                        </a>
                        <a href="#" className="text-white hover:text-primary transition-colors">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                            <rect width="4" height="12" x="2" y="9" />
                            <circle cx="4" cy="4" r="2" />
                          </svg>
                        </a>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-4" style={{ fontFamily: velozioTheme.fonts.heading }}>
                        Shop
                      </h4>
                      <ul className="space-y-2 text-sm opacity-80">
                        <li>
                          <a href="#" className="hover:text-primary transition-colors">
                            New Arrivals
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary transition-colors">
                            Best Sellers
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary transition-colors">
                            Trending Now
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary transition-colors">
                            Sale Items
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary transition-colors">
                            All Collections
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-4" style={{ fontFamily: velozioTheme.fonts.heading }}>
                        Customer Service
                      </h4>
                      <ul className="space-y-2 text-sm opacity-80">
                        <li>
                          <a href="#" className="hover:text-primary transition-colors">
                            Contact Us
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary transition-colors">
                            FAQs
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary transition-colors">
                            Shipping Policy
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary transition-colors">
                            Returns & Exchanges
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary transition-colors">
                            Size Guide
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-4" style={{ fontFamily: velozioTheme.fonts.heading }}>
                        Join Our Newsletter
                      </h4>
                      <p className="text-sm opacity-80 mb-4">
                        Subscribe to get special offers, free giveaways, and once-in-a-lifetime deals.
                      </p>
                      <div className="flex">
                        <input
                          type="email"
                          placeholder="Your email"
                          className="px-3 py-2 rounded-l-md w-full text-sm bg-white bg-opacity-10 border-0"
                        />
                        <button
                          className="px-4 py-2 rounded-r-md font-medium"
                          style={{
                            backgroundColor: velozioTheme.colors.primary,
                            color: "#FFFFFF",
                          }}
                        >
                          Subscribe
                        </button>
                      </div>
                    </div>
                  </div>
                  <div
                    className="pt-6 text-center text-sm opacity-60 border-t"
                    style={{
                      borderColor: "rgba(255,255,255,0.1)",
                    }}
                  >
                    <p>© {new Date().getFullYear()} Velozio. All rights reserved.</p>
                    <div className="flex justify-center mt-4 space-x-4">
                      <a href="#" className="hover:text-primary transition-colors">
                        Privacy Policy
                      </a>
                      <a href="#" className="hover:text-primary transition-colors">
                        Terms of Service
                      </a>
                      <a href="#" className="hover:text-primary transition-colors">
                        Cookie Policy
                      </a>
                    </div>
                  </div>
                </div>
              </footer>
            </div>
          </div>
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-bold mb-4">Velozio Theme Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">Colors</h3>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(velozioTheme.colors).map(([key, value]) => (
                  <div key={key} className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full border" style={{ backgroundColor: value }} />
                    <span className="text-sm capitalize">{key}: </span>
                    <span className="text-sm text-muted-foreground">{value}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Typography</h3>
              <div className="space-y-2">
                <div>
                  <span className="text-sm">Heading Font: </span>
                  <span className="text-sm font-medium">{velozioTheme.fonts.heading}</span>
                </div>
                <div>
                  <span className="text-sm">Body Font: </span>
                  <span className="text-sm font-medium">{velozioTheme.fonts.body}</span>
                </div>
              </div>

              <h3 className="font-semibold mt-4 mb-2">Layout</h3>
              <div className="space-y-2">
                <div>
                  <span className="text-sm">Header: </span>
                  <span className="text-sm font-medium capitalize">{velozioTheme.layout.header}</span>
                </div>
                <div>
                  <span className="text-sm">Footer: </span>
                  <span className="text-sm font-medium capitalize">{velozioTheme.layout.footer}</span>
                </div>
                <div>
                  <span className="text-sm">Sidebar: </span>
                  <span className="text-sm font-medium capitalize">{velozioTheme.layout.sidebar}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
