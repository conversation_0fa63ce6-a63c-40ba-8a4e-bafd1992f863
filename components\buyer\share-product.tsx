"use client"

import { useState } from "react"
import { 
  Facebook, 
  Twitter, 
  Linkedin, 
  <PERSON>py, 
  Share2, 
  MessageCircle,
  Check
} from "lucide-react"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"

interface ShareProductProps {
  productId: string
  productName: string
  variant?: "icon" | "default"
  size?: "sm" | "default" | "icon"
  className?: string
  children?: React.ReactNode
}

export function ShareProduct({
  productId,
  productName,
  variant = "default",
  size = "default",
  className = "",
  children
}: ShareProductProps) {
  const [copied, setCopied] = useState(false)
  
  const productUrl = `${window.location.origin}/products/${productId}`
  const productTitle = encodeURIComponent(`Lihat ${productName} di Velozio!`)
  
  const shareLinks = [
    {
      name: "WhatsApp",
      icon: MessageCircle,
      color: "bg-green-500 hover:bg-green-600",
      url: `https://wa.me/?text=${productTitle}%20${encodeURIComponent(productUrl)}`
    },
    {
      name: "Facebook",
      icon: Facebook,
      color: "bg-blue-600 hover:bg-blue-700",
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(productUrl)}`
    },
    {
      name: "Twitter",
      icon: Twitter,
      color: "bg-sky-500 hover:bg-sky-600",
      url: `https://twitter.com/intent/tweet?text=${productTitle}&url=${encodeURIComponent(productUrl)}`
    },
    {
      name: "LinkedIn",
      icon: Linkedin,
      color: "bg-blue-700 hover:bg-blue-800",
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(productUrl)}`
    }
  ]
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(productUrl)
    setCopied(true)
    
    toast({
      title: "Tautan disalin!",
      description: "Tautan produk telah disalin ke clipboard",
    })
    
    setTimeout(() => setCopied(false), 2000)
  }
  
  const shareProduct = (shareLink: string) => {
    window.open(shareLink, "_blank", "width=600,height=400")
  }

  const triggerButton = children ? (
    children
  ) : variant === "icon" ? (
    <Button variant="ghost" size="icon" className={className}>
      <Share2 className="h-4 w-4" />
    </Button>
  ) : (
    <Button 
      variant="outline" 
      size={size} 
      className={`gap-1 ${className}`}
    >
      <Share2 className="h-4 w-4" />
      {size !== "icon" && <span>Bagikan</span>}
    </Button>
  )

  return (
    <Popover>
      <PopoverTrigger asChild>
        {triggerButton}
      </PopoverTrigger>
      <PopoverContent className="w-72">
        <div className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">Bagikan produk ini</h4>
            <p className="text-xs text-muted-foreground">
              Bagikan {productName} dengan teman-teman Anda
            </p>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {shareLinks.map((link) => (
              <Button 
                key={link.name}
                size="sm"
                className={`${link.color} text-white px-3`}
                onClick={() => shareProduct(link.url)}
              >
                <link.icon className="mr-1 h-4 w-4" />
                {link.name}
              </Button>
            ))}
          </div>
          
          <div className="flex gap-2">
            <div className="flex-1 rounded-md border bg-muted px-3 py-2 text-sm">
              <span className="line-clamp-1">{productUrl}</span>
            </div>
            <Button variant="secondary" size="icon" onClick={copyToClipboard}>
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
} 