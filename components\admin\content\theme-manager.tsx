"use client"

import { useRout<PERSON> } from "next/navigation"
import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { PlusIcon, PencilIcon, EyeIcon, CodeIcon, PaletteIcon, LayoutIcon, CheckIcon } from "lucide-react"

export function ThemeManager() {
  const router = useRouter()
  const [themes, setThemes] = useState([])
  const [selectedTheme, setSelectedTheme] = useState(null)
  const [activeTab, setActiveTab] = useState("gallery")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulasi pengambilan data
    setTimeout(() => {
      const mockThemes = [
        {
          id: 1,
          name: "Modern Boutique",
          description: "A clean, modern theme perfect for fashion and boutique stores",
          thumbnail: "/modern-boutique-store.png",
          category: "Fashion",
          popularity: "High",
          status: "active",
          customizable: true,
        },
        {
          id: 2,
          name: "Tech Store",
          description: "Sleek and minimalist theme designed for electronics and tech products",
          thumbnail: "/tech-store-template.png",
          category: "Electronics",
          popularity: "Medium",
          status: "active",
          customizable: true,
        },
        {
          id: 3,
          name: "Food & Grocery",
          description: "Vibrant and appetizing theme for food and grocery stores",
          thumbnail: "/food-grocery-store-template.png",
          category: "Food",
          popularity: "High",
          status: "active",
          customizable: true,
        },
        {
          id: 4,
          name: "Handmade Crafts",
          description: "Rustic and artistic theme for handmade and craft stores",
          thumbnail: "/handmade-crafts-store.png",
          category: "Crafts",
          popularity: "Medium",
          status: "active",
          customizable: true,
        },
        {
          id: 5,
          name: "Minimalist",
          description: "Ultra-clean minimalist design that puts products in focus",
          thumbnail: "/minimalist-store-template.png",
          category: "General",
          popularity: "High",
          status: "active",
          customizable: true,
        },
        {
          id: 6,
          name: "Vintage Shop",
          description: "Retro-inspired theme with a nostalgic feel",
          thumbnail: "/vintage-shop-template-retro.png",
          category: "Vintage",
          popularity: "Low",
          status: "active",
          customizable: true,
        },
        {
          id: 7,
          name: "Velozio",
          description: "Modern marketplace theme with vibrant accents and optimized for conversion",
          thumbnail: "/assorted-shoes.png",
          category: "Marketplace",
          popularity: "High",
          status: "active",
          customizable: true,
        },
      ]

      setThemes(mockThemes)
      setSelectedTheme(mockThemes[0])
      setIsLoading(false)
    }, 1000)
  }, [])

  const handleThemeSelect = (theme) => {
    setSelectedTheme(theme)
    setActiveTab("editor")
  }

  const handlePreviewTheme = (id) => {
    router.push(`/admin/dashboard/content/themes/preview/${id}`)
  }

  if (isLoading) {
    return (
      <div className="grid gap-6">
        <div className="grid gap-6 md:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <div className="aspect-video w-full bg-gray-200"></div>
              <CardHeader>
                <div className="h-5 w-32 rounded bg-gray-200"></div>
                <div className="h-4 w-48 rounded bg-gray-200"></div>
              </CardHeader>
              <CardFooter>
                <div className="h-9 w-full rounded bg-gray-200"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-3 lg:w-[400px]">
        <TabsTrigger value="gallery">Theme Gallery</TabsTrigger>
        <TabsTrigger value="editor" disabled={!selectedTheme}>
          Theme Editor
        </TabsTrigger>
        <TabsTrigger value="assignments">Assignments</TabsTrigger>
      </TabsList>

      {/* Theme Gallery Tab */}
      <TabsContent value="gallery" className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-bold">Theme Gallery</h2>
            <p className="text-sm text-muted-foreground">Browse and select themes to customize</p>
          </div>
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" />
            Add New Theme
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {themes.map((theme) => (
            <Card key={theme.id} className="overflow-hidden">
              <div className="aspect-video w-full overflow-hidden">
                <img
                  src={theme.thumbnail || "/placeholder.svg"}
                  alt={theme.name}
                  className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                />
              </div>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>{theme.name}</CardTitle>
                  <Badge variant={theme.status === "active" ? "default" : "secondary"}>{theme.status}</Badge>
                </div>
                <CardDescription>{theme.description}</CardDescription>
              </CardHeader>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" onClick={() => handleThemeSelect(theme)}>
                  <PencilIcon className="mr-2 h-4 w-4" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" onClick={() => handlePreviewTheme(theme.id)}>
                  <EyeIcon className="mr-2 h-4 w-4" />
                  Preview
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </TabsContent>

      {/* Theme Editor Tab */}
      <TabsContent value="editor" className="space-y-6">
        {selectedTheme && (
          <>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h2 className="text-2xl font-bold">Editing: {selectedTheme.name}</h2>
                <p className="text-sm text-muted-foreground">Customize theme appearance and settings</p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline">
                  <EyeIcon className="mr-2 h-4 w-4" />
                  Preview
                </Button>
                <Button>
                  <CheckIcon className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-[1fr_2fr]">
              {/* Theme Settings Panel */}
              <Card>
                <CardHeader>
                  <CardTitle>Theme Settings</CardTitle>
                  <CardDescription>Configure basic theme properties</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="theme-name">Theme Name</Label>
                    <Input id="theme-name" defaultValue={selectedTheme.name} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="theme-description">Description</Label>
                    <Textarea id="theme-description" defaultValue={selectedTheme.description} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="theme-category">Category</Label>
                    <Select defaultValue={selectedTheme.category}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Fashion">Fashion</SelectItem>
                        <SelectItem value="Electronics">Electronics</SelectItem>
                        <SelectItem value="Food">Food</SelectItem>
                        <SelectItem value="Crafts">Crafts</SelectItem>
                        <SelectItem value="General">General</SelectItem>
                        <SelectItem value="Vintage">Vintage</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="theme-status">Status</Label>
                    <Select defaultValue={selectedTheme.status}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Theme Editor Tabs */}
              <Card>
                <CardHeader>
                  <Tabs defaultValue="visual" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="visual">
                        <PaletteIcon className="mr-2 h-4 w-4" />
                        Visual Editor
                      </TabsTrigger>
                      <TabsTrigger value="layout">
                        <LayoutIcon className="mr-2 h-4 w-4" />
                        Layout
                      </TabsTrigger>
                      <TabsTrigger value="code">
                        <CodeIcon className="mr-2 h-4 w-4" />
                        Custom CSS/JS
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="visual" className="mt-4 space-y-4">
                      <div className="space-y-2">
                        <Label>Primary Color</Label>
                        <div className="flex items-center gap-2">
                          <div className="h-10 w-10 rounded-full bg-blue-600"></div>
                          <Input type="text" defaultValue="#2563EB" className="w-32" />
                        </div>
                      </div>
                      <Separator />
                      <div className="space-y-2">
                        <Label>Secondary Color</Label>
                        <div className="flex items-center gap-2">
                          <div className="h-10 w-10 rounded-full bg-gray-800"></div>
                          <Input type="text" defaultValue="#1F2937" className="w-32" />
                        </div>
                      </div>
                      <Separator />
                      <div className="space-y-2">
                        <Label>Accent Color</Label>
                        <div className="flex items-center gap-2">
                          <div className="h-10 w-10 rounded-full bg-pink-500"></div>
                          <Input type="text" defaultValue="#EC4899" className="w-32" />
                        </div>
                      </div>
                      <Separator />
                      <div className="space-y-2">
                        <Label>Font Family</Label>
                        <Select defaultValue="inter">
                          <SelectTrigger>
                            <SelectValue placeholder="Select font" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="inter">Inter</SelectItem>
                            <SelectItem value="roboto">Roboto</SelectItem>
                            <SelectItem value="opensans">Open Sans</SelectItem>
                            <SelectItem value="montserrat">Montserrat</SelectItem>
                            <SelectItem value="poppins">Poppins</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </TabsContent>

                    <TabsContent value="layout" className="mt-4 space-y-4">
                      <div className="space-y-2">
                        <Label>Header Style</Label>
                        <Select defaultValue="centered">
                          <SelectTrigger>
                            <SelectValue placeholder="Select header style" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="centered">Centered</SelectItem>
                            <SelectItem value="left-aligned">Left Aligned</SelectItem>
                            <SelectItem value="minimal">Minimal</SelectItem>
                            <SelectItem value="expanded">Expanded</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <Separator />
                      <div className="space-y-2">
                        <Label>Product Grid</Label>
                        <Select defaultValue="3-column">
                          <SelectTrigger>
                            <SelectValue placeholder="Select grid layout" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="2-column">2 Columns</SelectItem>
                            <SelectItem value="3-column">3 Columns</SelectItem>
                            <SelectItem value="4-column">4 Columns</SelectItem>
                            <SelectItem value="masonry">Masonry</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <Separator />
                      <div className="space-y-2">
                        <Label>Footer Style</Label>
                        <Select defaultValue="standard">
                          <SelectTrigger>
                            <SelectValue placeholder="Select footer style" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="standard">Standard</SelectItem>
                            <SelectItem value="minimal">Minimal</SelectItem>
                            <SelectItem value="expanded">Expanded</SelectItem>
                            <SelectItem value="multi-column">Multi-Column</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </TabsContent>

                    <TabsContent value="code" className="mt-4 space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="custom-css">Custom CSS</Label>
                        <Textarea
                          id="custom-css"
                          className="font-mono h-40"
                          placeholder="/* Add your custom CSS here */"
                        />
                      </div>
                      <Separator />
                      <div className="space-y-2">
                        <Label htmlFor="custom-js">Custom JavaScript</Label>
                        <Textarea
                          id="custom-js"
                          className="font-mono h-40"
                          placeholder="// Add your custom JavaScript here"
                        />
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardHeader>
                <CardFooter className="flex justify-end">
                  <Button>Save Changes</Button>
                </CardFooter>
              </Card>
            </div>
          </>
        )}
      </TabsContent>

      {/* Theme Assignments Tab */}
      <TabsContent value="assignments" className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-bold">Theme Assignments</h2>
            <p className="text-sm text-muted-foreground">Assign themes to tenant plans</p>
          </div>
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" />
            New Assignment
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Current Theme Assignments</CardTitle>
            <CardDescription>Manage which themes are available for each tenant plan</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative overflow-x-auto">
              <table className="w-full text-left text-sm">
                <thead className="bg-gray-50 text-xs uppercase">
                  <tr>
                    <th scope="col" className="px-6 py-3">
                      Plan Name
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Default Theme
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Available Themes
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Custom CSS
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">Free Plan</td>
                    <td className="px-6 py-4">Minimalist</td>
                    <td className="px-6 py-4">
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline">Minimalist</Badge>
                        <Badge variant="outline">Tech Store</Badge>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="bg-red-100 text-red-800">
                        Not Allowed
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">Basic Plan</td>
                    <td className="px-6 py-4">Modern Boutique</td>
                    <td className="px-6 py-4">
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline">Modern Boutique</Badge>
                        <Badge variant="outline">Minimalist</Badge>
                        <Badge variant="outline">Tech Store</Badge>
                        <Badge variant="outline">Food & Grocery</Badge>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                        Limited
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b bg-white">
                    <td className="px-6 py-4 font-medium">Premium Plan</td>
                    <td className="px-6 py-4">Any</td>
                    <td className="px-6 py-4">
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline">All Themes</Badge>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="bg-green-100 text-green-800">
                        Full Access
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                  <tr className="bg-white">
                    <td className="px-6 py-4 font-medium">Enterprise Plan</td>
                    <td className="px-6 py-4">Any</td>
                    <td className="px-6 py-4">
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline">All Themes</Badge>
                        <Badge variant="outline">Custom Themes</Badge>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <Badge variant="outline" className="bg-green-100 text-green-800">
                        Full Access
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
