"use client"

import { useState } from "react"
import Image from "next/image"
import { ImageSlider } from "./themes/velozio/feed-card/image-slider"
import { TermurahDiTokoBadge, TerlarisBadge, KomisiXtraBadge } from "./themes/velozio/feed-card/badges"

export interface ProductCardProps {
  id: number
  name: string
  price: string
  originalPrice?: string
  discount?: string
  image: string
  images?: { src: string; alt: string }[]
  isMall?: boolean
  rating: number
  sold: number
  shipping?: string
  cod?: boolean
  isImageSlider?: boolean
  isTerlaris?: boolean
  isTerlarisBadge?: boolean
  isKomisiXtra?: boolean
}

export const VelozioProductCard = ({
  id,
  name,
  price,
  originalPrice,
  discount,
  image,
  images,
  isMall = false,
  rating,
  sold,
  shipping = "Pengiriman Reguler",
  cod = false,
  isImageSlider = false,
  isTerlaris = false,
  isTerlarisBadge = false,
  isKomisiXtra = false,
}: ProductCardProps) => {
  const [isLoaded, setIsLoaded] = useState(false)

  // Render image slider card if specified
  if (isImageSlider && images && images.length > 0) {
    return (
      <div className="bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 cursor-pointer w-full">
        <div className="relative">
          <ImageSlider images={images} />
        </div>
        <div className="p-3">
          <h3 className="text-xs text-gray-800 line-clamp-2 mb-1.5">
            {isMall && (
              <span className="inline-flex items-center bg-[#d0011b] text-white text-[10px] font-semibold px-1 py-[1px] rounded-sm mr-1 mb-0.5">
                Mall
              </span>
            )}
            {name}
          </h3>

          {isTerlaris && <TermurahDiTokoBadge />}
          {isTerlarisBadge && <TerlarisBadge />}
          {isKomisiXtra && <KomisiXtraBadge />}

          <div className="flex items-center text-xs text-gray-500 mb-1.5">
            <div className="flex items-center">
              <span className="text-[#ee4d2d] mr-1">{rating}</span>
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className={`w-3 h-3 ${i < Math.floor(rating) ? "text-[#ee4d2d]" : "text-gray-300"}`}
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                ))}
              </div>
            </div>
            <div className="mx-1 w-[1px] h-2 bg-gray-300"></div>
            <span>Terjual {sold}</span>
          </div>

          <div className="flex items-center text-[10px] text-[#666] mb-1.5">
            <i className="fa fa-truck text-[#00bfa5] mr-0.5 text-[10px]"></i>
            {shipping}
          </div>

          <div className="flex items-center">
            <div className="text-sm font-bold text-[#ee4d2d]">{price}</div>
            {originalPrice && (
              <div className="ml-1 text-xs text-gray-400 line-through overflow-hidden text-ellipsis max-w-[40%]">
                {originalPrice}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Render standard card
  return (
    <div className="bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 cursor-pointer w-full">
      <div className="relative pt-[100%] overflow-hidden bg-white border-b border-[#f2f2f2]">
        <Image
          src={image || "/placeholder.svg"}
          alt={name}
          fill
          className={`object-contain p-0.5 transition-opacity duration-300 ${isLoaded ? "opacity-100" : "opacity-0"}`}
          onLoad={() => setIsLoaded(true)}
          sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 16vw"
        />
        {!isLoaded && <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>}
        {discount && (
          <div className="absolute top-0 right-0 bg-[#ee4d2d] text-white text-xs font-medium px-1 py-0.5 z-10">
            {discount}
          </div>
        )}
        {cod && (
          <div className="absolute bottom-0 left-0 bg-[#fcd511] text-[#ee4d2d] text-[10px] font-medium px-1 py-0.5 z-10">
            COD
          </div>
        )}
      </div>

      <div className="p-3">
        <h3 className="text-xs text-gray-800 line-clamp-2 mb-1.5">
          {isMall && (
            <span className="inline-flex items-center bg-[#d0011b] text-white text-[10px] font-semibold px-1 py-[1px] rounded-sm mr-1 mb-0.5">
              Mall
            </span>
          )}
          {name}
        </h3>

        {isTerlaris && <TermurahDiTokoBadge />}
        {isTerlarisBadge && <TerlarisBadge />}
        {isKomisiXtra && <KomisiXtraBadge />}

        <div className="flex items-center text-xs text-gray-500 mb-1.5">
          <div className="flex items-center">
            <span className="text-[#ee4d2d] mr-1">{rating}</span>
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  className={`w-3 h-3 ${i < Math.floor(rating) ? "text-[#ee4d2d]" : "text-gray-300"}`}
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                </svg>
              ))}
            </div>
          </div>
          <div className="mx-1 w-[1px] h-2 bg-gray-300"></div>
          <span>Terjual {sold}</span>
        </div>

        <div className="flex items-center text-[10px] text-[#666] mb-1.5">
          <i className="fa fa-truck text-[#00bfa5] mr-0.5 text-[10px]"></i>
          {shipping}
        </div>

        <div className="flex items-center">
          <div className="text-sm font-bold text-[#ee4d2d]">{price}</div>
          {originalPrice && (
            <div className="ml-1 text-xs text-gray-400 line-through overflow-hidden text-ellipsis max-w-[40%]">
              {originalPrice}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
