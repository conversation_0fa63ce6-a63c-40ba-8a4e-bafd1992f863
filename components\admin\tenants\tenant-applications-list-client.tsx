"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useNotifications } from "@/components/providers/notifications-provider"

// Dummy data untuk contoh
const applications = [
  {
    id: "app-001",
    tenantName: "Fashion Marketplace",
    ownerName: "<PERSON>",
    email: "<EMAIL>",
    status: "pending",
    date: "2023-05-15",
    description: "A marketplace for fashion designers and retailers",
  },
  {
    id: "app-002",
    tenantName: "Tech Gadgets Hub",
    ownerName: "<PERSON>",
    email: "<EMAIL>",
    status: "pending",
    date: "2023-05-14",
    description: "Online store for the latest tech gadgets and accessories",
  },
  {
    id: "app-003",
    tenantName: "Organic Food Co-op",
    ownerName: "Michael Brown",
    email: "<EMAIL>",
    status: "pending",
    date: "2023-05-13",
    description: "Connecting organic farmers with health-conscious consumers",
  },
]

export function TenantApplicationsListClient() {
  const [applicationList, setApplicationList] = useState(applications)
  const { showNotification } = useNotifications()

  const handleApprove = (id: string) => {
    setApplicationList(applicationList.map((app) => (app.id === id ? { ...app, status: "approved" } : app)))
    showNotification("Tenant application approved successfully", "success")
  }

  const handleReject = (id: string) => {
    setApplicationList(applicationList.map((app) => (app.id === id ? { ...app, status: "rejected" } : app)))
    showNotification("Tenant application rejected", "info")
  }

  return (
    <div className="space-y-4">
      {applicationList.map((application) => (
        <Card key={application.id} className="overflow-hidden">
          <CardContent className="p-0">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold">{application.tenantName}</h3>
                  <p className="text-sm text-muted-foreground">
                    Submitted by {application.ownerName} on {new Date(application.date).toLocaleDateString()}
                  </p>
                </div>
                <Badge
                  className={
                    application.status === "approved"
                      ? "bg-green-100 text-green-800"
                      : application.status === "rejected"
                        ? "bg-red-100 text-red-800"
                        : "bg-yellow-100 text-yellow-800"
                  }
                >
                  {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                </Badge>
              </div>
              <p className="mb-4">{application.description}</p>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm font-medium">Contact Email</p>
                  <p className="text-sm">{application.email}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Application ID</p>
                  <p className="text-sm">{application.id}</p>
                </div>
              </div>
              {application.status === "pending" && (
                <div className="flex gap-2 mt-4">
                  <Button onClick={() => handleApprove(application.id)} className="bg-green-600 hover:bg-green-700">
                    Approve
                  </Button>
                  <Button
                    onClick={() => handleReject(application.id)}
                    variant="outline"
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    Reject
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
