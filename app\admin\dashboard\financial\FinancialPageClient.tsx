"use client"

import { useState } from "react"
import { PageHeader } from "@/components/admin/ui/page-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { RevenueOverview } from "@/components/admin/financial/revenue-overview"
import { DateRangePicker } from "@/components/analytics/date-range-picker"
import { Button } from "@/components/ui/button"
import { Download, RefreshCw } from "lucide-react"
import { useNotifications } from "@/components/admin/ui/notifications"

export default function FinancialPageClient() {
  const { addNotification } = useNotifications()
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date(),
  })

  const handleRefresh = () => {
    addNotification({
      title: "Data Refreshed",
      message: "Financial data has been refreshed.",
      type: "info",
    })
  }

  const handleExport = (format: string) => {
    addNotification({
      title: "Export Started",
      message: `Financial data export as ${format.toUpperCase()} has been initiated.`,
      type: "info",
    })
  }

  return (
    <>
      <PageHeader
        title="Financial Dashboard"
        description="Overview of platform financial performance"
        breadcrumbs={[
          { title: "Dashboard", href: "/admin/dashboard" },
          { title: "Financial", href: "/admin/dashboard/financial" },
        ]}
        actions={
          <>
            <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleExport("csv")}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </>
        }
      />

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="commissions">Commissions</TabsTrigger>
          <TabsTrigger value="payouts">Payouts</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="tax">Tax</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$1,245,389.00</div>
                <p className="text-xs text-muted-foreground">+20.1% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Subscription Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$567,890.00</div>
                <p className="text-xs text-muted-foreground">+10.5% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Commission Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$456,789.00</div>
                <p className="text-xs text-muted-foreground">+15.2% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Other Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$220,710.00</div>
                <p className="text-xs text-muted-foreground">+8.1% from last month</p>
              </CardContent>
            </Card>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Revenue Overview</CardTitle>
                <CardDescription>Monthly revenue breakdown</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <RevenueOverview />
              </CardContent>
            </Card>
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Revenue by Source</CardTitle>
                <CardDescription>Distribution of revenue sources</CardDescription>
              </CardHeader>
              <CardContent>
                {/* Placeholder for pie chart */}
                <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                  Revenue by Source Chart
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="commissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Commission Reports</CardTitle>
              <CardDescription>Overview of platform commissions</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Placeholder for commission reports */}
              <div className="h-[400px] flex items-center justify-center bg-muted/20 rounded-md">
                Commission Reports Content
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="payouts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payout Management</CardTitle>
              <CardDescription>Manage payouts to stores and affiliates</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Placeholder for payout management */}
              <div className="h-[400px] flex items-center justify-center bg-muted/20 rounded-md">
                Payout Management Content
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="invoices" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Invoices & Billing</CardTitle>
              <CardDescription>Manage platform invoices and billing</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Placeholder for invoices & billing */}
              <div className="h-[400px] flex items-center justify-center bg-muted/20 rounded-md">
                Invoices & Billing Content
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="tax" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tax Management</CardTitle>
              <CardDescription>Manage tax settings and reports</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Placeholder for tax management */}
              <div className="h-[400px] flex items-center justify-center bg-muted/20 rounded-md">
                Tax Management Content
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </>
  )
}
