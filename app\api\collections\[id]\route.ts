import { NextRequest, NextResponse } from 'next/server';

// Referensi ke data koleksi (akan diganti dengan database)
let collections = [
  {
    id: "col-1",
    name: "Pakaian Favorit",
    description: "Koleksi pakaian yang saya ingin beli di masa depan",
    imageUrl: "/placeholder.svg",
    createdAt: "2025-05-10",
    itemCount: 2,
    isPublic: true,
    items: [
      "1", // ID item wishlist
      "5"
    ]
  },
  {
    id: "col-2",
    name: "Gadget",
    description: "Gadget keren yang ingin saya miliki",
    imageUrl: "/placeholder.svg",
    createdAt: "2025-05-05",
    itemCount: 1,
    isPublic: false,
    items: [
      "4"
    ]
  }
];

// GET - Mendapatkan koleksi berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Di implementasi nyata, ambil data dari database
    const id = params.id
    
    // Simulasi delay untuk mendemonstrasikan loading state
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Cek apakah koleksi dengan ID tersebut ada
    const mockCollections = await getMockCollections()
    const collection = mockCollections.find(c => c.id === id)
    
    if (!collection) {
      return NextResponse.json(
        { error: "Koleksi tidak ditemukan" },
        { status: 404 }
      )
    }
    
    return NextResponse.json(collection)
  } catch (error) {
    console.error(error)
    return NextResponse.json(
      { error: "Terjadi kesalahan saat mengambil detail koleksi" },
      { status: 500 }
    )
  }
}

// PUT - Memperbarui koleksi berdasarkan ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const { name, description, isPublic } = await request.json()
    
    // Simulasi delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Cek apakah koleksi dengan ID tersebut ada
    let mockCollections = await getMockCollections()
    const collectionIndex = mockCollections.findIndex(c => c.id === id)
    
    if (collectionIndex === -1) {
      return NextResponse.json(
        { error: "Koleksi tidak ditemukan" },
        { status: 404 }
      )
    }
    
    // Update koleksi
    const updatedCollection = {
      ...mockCollections[collectionIndex],
      name: name || mockCollections[collectionIndex].name,
      description: description !== undefined ? description : mockCollections[collectionIndex].description,
      isPublic: isPublic !== undefined ? isPublic : mockCollections[collectionIndex].isPublic,
      updatedAt: new Date().toISOString()
    }
    
    mockCollections[collectionIndex] = updatedCollection
    
    // Simpan perubahan
    await saveMockCollections(mockCollections)
    
    return NextResponse.json(updatedCollection)
  } catch (error) {
    console.error(error)
    return NextResponse.json(
      { error: "Terjadi kesalahan saat mengupdate koleksi" },
      { status: 500 }
    )
  }
}

// DELETE - Menghapus koleksi berdasarkan ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    
    // Simulasi delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Cek apakah koleksi dengan ID tersebut ada
    let mockCollections = await getMockCollections()
    const collectionIndex = mockCollections.findIndex(c => c.id === id)
    
    if (collectionIndex === -1) {
      return NextResponse.json(
        { error: "Koleksi tidak ditemukan" },
        { status: 404 }
      )
    }
    
    // Hapus koleksi dari array
    mockCollections = mockCollections.filter(c => c.id !== id)
    
    // Simpan perubahan
    await saveMockCollections(mockCollections)
    
    return NextResponse.json({ success: true, message: "Koleksi berhasil dihapus" })
  } catch (error) {
    console.error(error)
    return NextResponse.json(
      { error: "Terjadi kesalahan saat menghapus koleksi" },
      { status: 500 }
    )
  }
}

// PATCH - Menambahkan item ke koleksi
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const { action, itemId } = await request.json()
    
    if (!action || !itemId) {
      return NextResponse.json(
        { error: "Parameter tidak lengkap" },
        { status: 400 }
      )
    }
    
    // Simulasi delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Cek apakah koleksi dengan ID tersebut ada
    let mockCollections = await getMockCollections()
    const collectionIndex = mockCollections.findIndex(c => c.id === id)
    
    if (collectionIndex === -1) {
      return NextResponse.json(
        { error: "Koleksi tidak ditemukan" },
        { status: 404 }
      )
    }
    
    const collection = mockCollections[collectionIndex]
    
    // Operasi tambah item ke koleksi
    if (action === 'addItem') {
      // Cek apakah item sudah ada dalam koleksi
      if (!collection.items.includes(itemId)) {
        collection.items.push(itemId)
      }
    } 
    // Operasi hapus item dari koleksi
    else if (action === 'removeItem') {
      collection.items = collection.items.filter(item => item !== itemId)
    }
    else {
      return NextResponse.json(
        { error: "Operasi tidak valid" },
        { status: 400 }
      )
    }
    
    // Update timestamp
    collection.updatedAt = new Date().toISOString()
    
    // Update koleksi dalam array
    mockCollections[collectionIndex] = collection
    
    // Simpan perubahan
    await saveMockCollections(mockCollections)
    
    return NextResponse.json(collection)
  } catch (error) {
    console.error(error)
    return NextResponse.json(
      { error: "Terjadi kesalahan saat memperbarui item dalam koleksi" },
      { status: 500 }
    )
  }
}

// Fungsi helper untuk mendapatkan koleksi (mock data)
async function getMockCollections() {
  // Di implementasi nyata, ini akan mengambil data dari database
  return [
    {
      id: "1",
      name: "Barang Favorit",
      description: "Koleksi barang favorit saya",
      isPublic: true,
      items: ["item1", "item2"],
      createdAt: "2023-10-15T10:00:00Z",
      updatedAt: "2023-10-15T10:00:00Z"
    },
    {
      id: "2",
      name: "Wishlist Pakaian",
      description: "Pakaian yang ingin dibeli",
      isPublic: false,
      items: ["item3"],
      createdAt: "2023-10-16T14:30:00Z",
      updatedAt: "2023-10-16T14:30:00Z"
    },
    {
      id: "3",
      name: "Peralatan Masak",
      description: "",
      isPublic: true,
      items: [],
      createdAt: "2023-10-17T09:15:00Z",
      updatedAt: "2023-10-17T09:15:00Z"
    }
  ]
}

// Fungsi helper untuk menyimpan koleksi (mock function)
async function saveMockCollections(collections: any[]) {
  // Di implementasi nyata, ini akan menyimpan data ke database
  console.log("Collections saved:", collections)
  return true
} 