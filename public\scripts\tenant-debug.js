// Debug script untuk tenant
console.log("Tenant Debug Script loaded")

// Fungsi untuk melihat tenant di localStorage
function viewTenants() {
  try {
    const tenants = localStorage.getItem("tenants")
    console.log("Tenants in localStorage:", tenants ? JSON.parse(tenants) : "None")
    return tenants ? JSON.parse(tenants) : null
  } catch (error) {
    console.error("Error viewing tenants:", error)
    return null
  }
}

// Fungsi untuk menambahkan tenant dummy
function addDummyTenant() {
  try {
    const tenants = viewTenants() || []
    const newId = tenants.length > 0 ? Math.max(...tenants.map((t) => Number.parseInt(t.id))) + 1 : 1

    const dummyTenant = {
      id: newId.toString(),
      name: `Dummy Tenant ${newId}`,
      domain: `dummy${newId}.sellzio.com`,
      plan: ["Basic", "Professional", "Enterprise"][Math.floor(Math.random() * 3)],
      status: ["active", "suspended", "pending"][Math.floor(Math.random() * 3)],
      storeCount: Math.floor(Math.random() * 10),
      userCount: Math.floor(Math.random() * 20),
      revenue: `$${Math.floor(Math.random() * 5000)}`,
      createdAt: new Date().toISOString().split("T")[0],
    }

    tenants.push(dummyTenant)
    localStorage.setItem("tenants", JSON.stringify(tenants))
    console.log("Added dummy tenant:", dummyTenant)
    return dummyTenant
  } catch (error) {
    console.error("Error adding dummy tenant:", error)
    return null
  }
}

// Fungsi untuk menghapus semua tenant
function clearTenants() {
  try {
    localStorage.removeItem("tenants")
    console.log("Cleared all tenants from localStorage")
    return true
  } catch (error) {
    console.error("Error clearing tenants:", error)
    return false
  }
}

// Fungsi untuk reset tenant ke default
function resetTenants() {
  try {
    const defaultTenants = [
      {
        id: "1",
        name: "Acme Corporation",
        domain: "acme.sellzio.com",
        plan: "Enterprise",
        status: "active",
        storeCount: 12,
        userCount: 45,
        revenue: "$5,240",
        createdAt: "2023-01-15",
      },
      {
        id: "2",
        name: "TechStart Inc",
        domain: "techstart.sellzio.com",
        plan: "Professional",
        status: "active",
        storeCount: 5,
        userCount: 18,
        revenue: "$1,890",
        createdAt: "2023-02-22",
      },
      {
        id: "3",
        name: "Global Retail",
        domain: "globalretail.sellzio.com",
        plan: "Enterprise",
        status: "active",
        storeCount: 28,
        userCount: 120,
        revenue: "$12,450",
        createdAt: "2022-11-05",
      },
      {
        id: "4",
        name: "Fashion Forward",
        domain: "fashionforward.sellzio.com",
        plan: "Basic",
        status: "suspended",
        storeCount: 1,
        userCount: 3,
        revenue: "$240",
        createdAt: "2023-03-10",
      },
      {
        id: "5",
        name: "Digital Solutions",
        domain: "digitalsolutions.sellzio.com",
        plan: "Professional",
        status: "active",
        storeCount: 7,
        userCount: 22,
        revenue: "$3,120",
        createdAt: "2023-01-30",
      },
    ]

    localStorage.setItem("tenants", JSON.stringify(defaultTenants))
    console.log("Reset tenants to default")
    return defaultTenants
  } catch (error) {
    console.error("Error resetting tenants:", error)
    return null
  }
}

// Fungsi untuk menghapus tenant berdasarkan ID
function deleteTenant(id) {
  try {
    const tenants = viewTenants()
    if (!tenants) return false

    const filteredTenants = tenants.filter((t) => t.id !== id.toString())
    localStorage.setItem("tenants", JSON.stringify(filteredTenants))
    console.log(`Deleted tenant with ID ${id}`)
    return true
  } catch (error) {
    console.error("Error deleting tenant:", error)
    return false
  }
}

// Fungsi untuk memeriksa status localStorage
function checkLocalStorage() {
  try {
    const testKey = "__test_storage__"
    localStorage.setItem(testKey, "test")
    const testValue = localStorage.getItem(testKey)
    localStorage.removeItem(testKey)

    if (testValue === "test") {
      console.log("localStorage is working correctly")
      return true
    } else {
      console.error("localStorage test failed")
      return false
    }
  } catch (error) {
    console.error("localStorage is not available:", error)
    return false
  }
}

// Expose functions to window for debugging
window.tenantDebug = {
  viewTenants,
  addDummyTenant,
  clearTenants,
  resetTenants,
  deleteTenant,
  checkLocalStorage,
}

// Initialize localStorage with default data if empty
function initializeLocalStorage() {
  try {
    const tenants = localStorage.getItem("tenants")
    if (!tenants) {
      resetTenants()
    }
  } catch (error) {
    console.error("Error initializing localStorage:", error)
  }
}

// Run initialization
initializeLocalStorage()

console.log("Tenant debug functions available at window.tenantDebug")
