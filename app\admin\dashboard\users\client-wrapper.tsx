"use client"

import { Suspense } from "react"
import dynamic from "next/dynamic"

// Komponen loading sederhana
function UsersLoading() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Users</h1>
      <p>Loading users list...</p>
    </div>
  )
}

// Import client component dengan dynamic import
const UsersClient = dynamic(() => import("./client"), {
  loading: () => <UsersLoading />,
  ssr: false, // Disable SSR untuk komponen client
})

export default function ClientWrapper() {
  return (
    <Suspense fallback={<UsersLoading />}>
      <UsersClient />
    </Suspense>
  )
}
