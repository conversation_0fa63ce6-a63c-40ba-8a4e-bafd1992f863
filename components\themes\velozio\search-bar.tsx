"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { Search, X, ShoppingCart, MessageCircle, ArrowLeft, Filter, Plus, Minus } from "lucide-react"
import { useTenantTheme } from "@/components/tenant/tenant-theme-provider"
import { cn } from "@/lib/utils"

interface SearchBarProps {
  onSearch?: (query: string) => void
  className?: string
}

// Definisi untuk handler global
interface WindowWithHandler extends Window {
  __preventSearchClickHandler?: (e: MouseEvent) => void;
}

// Pastikan TypeScript memahami properti custom pada window
declare global {
  interface Window {
    __preventSearchClickHandler?: (e: MouseEvent) => void;
  }
}

export function VelozioSearchBar({ onSearch, className }: SearchBarProps) {
  const { theme } = useTenantTheme()
  const [searchValue, setSearchValue] = useState("")
  const [isExpanded, setIsExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const expandedSearchInputRef = useRef<HTMLInputElement>(null)
  // Ref untuk menyimpan event handler
  const preventClickHandlerRef = useRef<((e: MouseEvent) => void) | null>(null)
  
  // Mengelola CSS global dan event handling untuk expanded search
  useEffect(() => {
    if (typeof document !== 'undefined') {
      if (isExpanded) {
        // Tambahkan style untuk menyembunyikan Categories dan VelozioPage
        const style = document.createElement('style');
        style.id = 'expanded-search-style';
        style.innerHTML = `
          [data-component-name="Categories"] { display: none !important; }
          main[data-component-name="VelozioPage"] { display: none !important; }
          body { overflow: hidden; }
          /* Mencegah interaksi dengan elemen lain di halaman */
          body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9000;
            /* Transparan tapi dapat menerima klik */
            background-color: transparent;
            pointer-events: none;
          }
        `;
        document.head.appendChild(style);
        
        // Mencegah event click pada document sampai expanded search ditutup
        const preventClickHandler = (e: MouseEvent) => {
          // Izinkan event hanya pada elemen di dalam expanded search container
          if (!(e.target as Element).closest('.expanded-search-container')) {
            e.preventDefault();
            e.stopPropagation();
          }
        };
        
        // Simpan handler ke ref untuk dibersihkan nanti
        preventClickHandlerRef.current = preventClickHandler;
        
        // Gunakan event capturing untuk menangkap event klik sebelum sampai ke elemen target
        document.addEventListener('click', preventClickHandler, true);
        document.addEventListener('mousedown', preventClickHandler, true);
      } else {
        // Hapus style saat expanded search ditutup
        const style = document.getElementById('expanded-search-style');
        if (style) {
          style.remove();
        }
        
        // Hapus event handler global
        if (preventClickHandlerRef.current) {
          document.removeEventListener('click', preventClickHandlerRef.current, true);
          document.removeEventListener('mousedown', preventClickHandlerRef.current, true);
          preventClickHandlerRef.current = null;
        }
      }
    }
    
    return () => {
      if (typeof document !== 'undefined') {
        // Bersihkan style
        const style = document.getElementById('expanded-search-style');
        if (style) {
          style.remove();
        }
        
        // Bersihkan event handler
        if (preventClickHandlerRef.current) {
          document.removeEventListener('click', preventClickHandlerRef.current, true);
          document.removeEventListener('mousedown', preventClickHandlerRef.current, true);
          preventClickHandlerRef.current = null;
        }
      }
    };
  }, [isExpanded]);

  // Fungsi untuk mencegah event klik di komponen terteruskan ke parent
  const preventDefaultAndPropagation = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // Placeholder suggestions
  const placeholderSuggestions = ["Handphone Samsung", "Sepatu Pria", "Tas Wanita", "Promo Elektronik"]

  // Sample search history
  const searchHistory = [
    "Smartphone Android",
    "Sepatu Sneakers",
    "Tas Selempang",
    "Headphone Bluetooth",
    "Keyboard Gaming",
  ]

  // Toggle expanded search view
  const toggleExpandedSearch = () => {
    setIsExpanded(true)
    setTimeout(() => {
      expandedSearchInputRef.current?.focus()
    }, 100)
  }

  // Close expanded search view dengan penanganan khusus
  const closeExpandedSearch = (e?: React.MouseEvent) => {
    // Mencegah event propagation jika event tersedia
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    // Tutup expanded search dan reset state
    setIsExpanded(false);
    setSearchValue("");
    setShowSuggestions(false);
  }

  // Clear search input
  const clearSearch = () => {
    setSearchValue("")
    expandedSearchInputRef.current?.focus()
  }

  // Handle search submission
  const handleSearch = () => {
    if (searchValue.trim()) {
      onSearch?.(searchValue)
      setShowSuggestions(false)
    }
  }

  // Handle suggestion click - mengarahkan ke produk sesuai facet.html
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)
    // Tidak menutup suggestion container saat click
    // Simulasi mengarahkan ke produk seperti facet.html
    onSearch?.(suggestion)
  }

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch()
    }
  }

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
    setShowSuggestions(e.target.value.length > 0)
  }

  // Close suggestions and expanded search when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Untuk mencegah search bar ditutup saat klik di dalam expanded search atau suggestions
      const isInsideExpandedSearch = (
        isExpanded && 
        (expandedSearchInputRef.current?.contains(event.target as Node) ||
        (event.target as Element).closest(".expanded-search-container") ||
        (event.target as Element).closest(".suggestions-container"))
      );

      // Jika klik di luar expanded search dan suggestions, tutup suggestions
      if (
        expandedSearchInputRef.current &&
        !expandedSearchInputRef.current.contains(event.target as Node) &&
        !(event.target as Element).closest(".suggestions-container") &&
        !isInsideExpandedSearch
      ) {
        setShowSuggestions(false);
      }

      // Tutup expanded search hanya jika tombol close yang diklik, tidak pada klik di luar
      // Ini mencegah menutup expanded search saat klik di luar yang bisa menampilkan kategori
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isExpanded])

  return (
    <div className={cn("velozio-search-container", className)}>
      {/* Normal search bar */}
      {!isExpanded && (
        <div className="relative w-full max-w-3xl mx-auto">
          <div className="flex items-center relative bg-white rounded-lg shadow-sm">
            <input
              ref={searchInputRef}
              type="text"
              className="flex-grow py-2 px-4 pr-10 border-2 border-primary rounded-lg text-sm outline-none"
              placeholder=" "
              onClick={toggleExpandedSearch}
            />
            <div className="absolute inset-y-0 left-4 flex items-center pointer-events-none">
              {/* Animated placeholder */}
              <div className="search-placeholder text-primary opacity-100 pointer-events-none">
                <div className="placeholder-dynamic">
                  {placeholderSuggestions.map((suggestion, index) => (
                    <div key={index} className="placeholder-text" style={{ animationDelay: `${index * 3}s` }}>
                      {suggestion}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="absolute inset-y-0 right-4 flex items-center">
              <Search className="h-4 w-4 text-primary" />
            </div>

            {/* Cart and chat icons */}
            <div className="absolute right-12 flex items-center space-x-4">
              <div className="relative">
                <ShoppingCart className="h-5 w-5 text-white stroke-[1.5px]" />
                <span className="absolute -top-2 -right-2 bg-white text-primary text-xs font-bold w-4 h-4 rounded-full flex items-center justify-center">
                  5
                </span>
              </div>
              <div className="relative">
                <MessageCircle className="h-5 w-5 text-white stroke-[1.5px]" />
                <span className="absolute -top-2 -right-2 bg-white text-primary text-xs font-bold w-4 h-4 rounded-full flex items-center justify-center">
                  3
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Expanded search view dengan solusi CSS yang lebih kuat untuk menutupi elemen lain */}
      {isExpanded && (
        <div 
          className="fixed inset-0 bg-white overflow-y-auto w-full h-full expanded-search-container"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          style={{ 
            position: 'fixed', 
            top: 0, 
            left: 0, 
            right: 0,
            bottom: 0,
            width: '100vw', 
            height: '100vh',
            display: 'block',
            visibility: 'visible',
            opacity: 1,
            zIndex: 99999,
            backgroundColor: '#ffffff',
            isolation: 'isolate',
            transform: 'translateZ(0)',
            overscrollBehavior: 'contain'
          }}
        >
          <div className="p-4 border-b">
            <div className="flex items-center relative">
              <button 
                onClick={(e) => closeExpandedSearch(e)} 
                className="p-2 mr-2"
                onMouseDown={(e) => e.preventDefault()}
              >
                <ArrowLeft className="h-5 w-5 text-primary" />
              </button>

              <div className="relative flex-grow">
                <input
                  ref={expandedSearchInputRef}
                  type="text"
                  className="w-full py-2 px-4 pr-16 border-2 border-primary rounded-lg text-sm outline-none"
                  placeholder=" "
                  value={searchValue}
                  onChange={handleInputChange}
                  onKeyPress={handleKeyPress}
                />

                {/* Animated placeholder */}
                {!searchValue && (
                  <div className="absolute inset-y-0 left-4 flex items-center pointer-events-none">
                    <div className="search-placeholder text-primary opacity-100">
                      <div className="placeholder-dynamic">
                        {placeholderSuggestions.map((suggestion, index) => (
                          <div key={index} className="placeholder-text" style={{ animationDelay: `${index * 3}s` }}>
                            {suggestion}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Clear button */}
                {searchValue && (
                  <button
                    className="absolute inset-y-0 right-12 flex items-center justify-center w-6 h-6 rounded-full bg-gray-400 my-auto"
                    onClick={clearSearch}
                  >
                    <X className="h-3 w-3 text-white" />
                  </button>
                )}
              </div>

              {/* Search button */}
              <button className="ml-2 bg-primary text-white p-2 rounded-lg" onClick={handleSearch}>
                <Search className="h-5 w-5" />
              </button>

              {/* Filter button - only show when search has results */}
              {searchValue && (
                <button className="ml-2 bg-primary text-white p-2 rounded-lg">
                  <Filter className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>

          {/* Suggestions */}
          {showSuggestions && (
            <div 
              className="suggestions-container max-w-3xl mx-auto bg-white shadow-md rounded-b-lg mt-1 overflow-hidden"
              onClick={(e) => e.stopPropagation()} // Mencegah click container menutup suggestions
            >
              <div 
                className="text-center py-3 text-sm text-gray-500 border-b"
                onClick={() => console.log('Hapus riwayat pencarian')} // Fungsi untuk menghapus riwayat
              >
                Hapus riwayat pencarian
              </div>

              {/* Search history */}
              <div className="p-3 flex flex-wrap gap-2">
                {searchHistory.map((item, index) => (
                  <button
                    key={index}
                    className="flex items-center bg-white border border-gray-200 rounded-full px-3 py-1.5 text-sm"
                    onClick={() => handleSuggestionClick(item)}
                  >
                    <span className="text-gray-500 mr-2">
                      <Search className="h-3 w-3" />
                    </span>
                    {item}
                  </button>
                ))}
              </div>

              {/* See more button */}
              <div className="text-center py-3 border-t border-gray-100">
                <button 
                  className="text-primary flex items-center justify-center mx-auto"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Lihat lainnya clicked');
                  }}
                >
                  <span className="mr-1">+</span> Lihat Lainnya
                </button>
              </div>

              {/* Trending */}
              <div className="px-4 pt-2">
                <div className="inline-flex items-center bg-red-50 text-primary rounded-full px-4 py-1">
                  Sedang Trend
                  <span className="ml-2 bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                    5
                  </span>
                </div>
              </div>

              {/* Trending items */}
              <div className="p-3">
                {[
                  "Smartphone Android",
                  "Sepatu Sneakers",
                  "Tas Selempang",
                  "Headphone Bluetooth",
                  "Keyboard Gaming",
                ].map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center py-3 border-b border-gray-100 cursor-pointer"
                    onClick={() => handleSuggestionClick(item)}
                  >
                    <span className="text-primary mr-3">
                      <Search className="h-4 w-4" />
                    </span>
                    <span>{item}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
