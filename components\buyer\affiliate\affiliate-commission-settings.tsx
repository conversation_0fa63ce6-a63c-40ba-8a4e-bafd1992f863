import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"

export function AffiliateCommissionSettings() {
  // Sample commission tiers data
  const commissionTiers = [
    {
      id: 1,
      tier: "Bronze",
      rate: 5,
      requirements: "Up to $5,000 in monthly sales",
      current: true,
    },
    {
      id: 2,
      tier: "Silver",
      rate: 7.5,
      requirements: "$5,000 - $15,000 in monthly sales",
      current: false,
    },
    {
      id: 3,
      tier: "Gold",
      rate: 10,
      requirements: "$15,000 - $30,000 in monthly sales",
      current: false,
    },
    {
      id: 4,
      tier: "Platinum",
      rate: 12.5,
      requirements: "Over $30,000 in monthly sales",
      current: false,
    },
  ]

  // Sample category commission rates
  const categoryRates = [
    {
      id: 1,
      category: "Electronics",
      baseRate: 5,
      yourRate: 5,
      notes: "Standard rate",
    },
    {
      id: 2,
      category: "Fashion",
      baseRate: 7,
      yourRate: 7,
      notes: "Standard rate",
    },
    {
      id: 3,
      category: "Home & Kitchen",
      baseRate: 6,
      yourRate: 6,
      notes: "Standard rate",
    },
    {
      id: 4,
      category: "Beauty & Personal Care",
      baseRate: 8,
      yourRate: 8,
      notes: "Standard rate",
    },
    {
      id: 5,
      category: "Sports & Outdoors",
      baseRate: 6,
      yourRate: 7,
      notes: "Special rate for category specialists",
    },
  ]

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Commission Settings</CardTitle>
        <CardDescription>View your commission structure and performance tiers</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Current Commission Structure</h3>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Your Current Tier</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-2">
                <div>
                  <span className="text-2xl font-bold">Bronze</span>
                  <span className="text-sm text-gray-500 ml-2">5% Base Commission Rate</span>
                </div>
                <Badge>Current</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">$3,245 of $5,000 to Silver Tier</span>
                  <span className="text-sm font-medium">64.9%</span>
                </div>
                <Progress value={64.9} className="h-2" />
                <p className="text-xs text-gray-500">You need $1,755 more in monthly sales to reach Silver Tier</p>
              </div>
            </CardContent>
          </Card>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tier</TableHead>
                <TableHead>Commission Rate</TableHead>
                <TableHead>Requirements</TableHead>
                <TableHead className="text-right">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {commissionTiers.map((tier) => (
                <TableRow key={tier.id}>
                  <TableCell className="font-medium">{tier.tier}</TableCell>
                  <TableCell>{tier.rate}%</TableCell>
                  <TableCell>{tier.requirements}</TableCell>
                  <TableCell className="text-right">
                    {tier.current ? <Badge>Current</Badge> : <Badge variant="outline">Upcoming</Badge>}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Category-Specific Rates</h3>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Category</TableHead>
                <TableHead>Base Rate</TableHead>
                <TableHead>Your Rate</TableHead>
                <TableHead>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {categoryRates.map((rate) => (
                <TableRow key={rate.id}>
                  <TableCell className="font-medium">{rate.category}</TableCell>
                  <TableCell>{rate.baseRate}%</TableCell>
                  <TableCell>
                    {rate.yourRate > rate.baseRate ? (
                      <span className="text-green-600 font-medium">{rate.yourRate}%</span>
                    ) : (
                      <span>{rate.yourRate}%</span>
                    )}
                  </TableCell>
                  <TableCell>{rate.notes}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Special Promotions</h3>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <h4 className="font-medium text-blue-800">Summer Sale Boost</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Earn an additional 2% commission on all summer collection items from June 1 - August 31.
                  </p>
                </div>

                <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                  <h4 className="font-medium text-green-800">New Product Launch Bonus</h4>
                  <p className="text-sm text-green-700 mt-1">
                    Earn 15% commission on all new product launches for the first 30 days after release.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Commission History</h3>

          <Card>
            <CardContent className="pt-6">
              <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                <p className="text-sm text-gray-500 dark:text-gray-400">Commission rate history chart</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  )
}
