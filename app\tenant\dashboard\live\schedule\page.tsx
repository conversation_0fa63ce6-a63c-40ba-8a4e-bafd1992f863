"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Calendar,
  CalendarDays,
  Clock,
  Search,
  Plus,
  MoreHorizontal,
  Trash,
  PencilIcon,
  Users,
  Eye,
  PlayCircle,
  Clock10,
  Filter,
  ChevronDown
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Data dummy untuk siaran
const streams = [
  {
    id: "stream-001",
    title: "Peluncuran Produk Terbaru 2024",
    scheduledFor: "2024-06-02T14:00:00",
    status: "scheduled",
    estimatedDuration: 60,
    participants: 3,
    thumbnailUrl: "/images/thumbnails/product-launch.jpg",
    description: "Siaran langsung peluncuran produk unggulan terbaru tahun 2024",
    remindersSent: 150,
    expectedViewers: 500,
  },
  {
    id: "stream-002",
    title: "Workshop: Tips Pemasaran Digital",
    scheduledFor: "2024-06-05T10:30:00",
    status: "scheduled",
    estimatedDuration: 90,
    participants: 2,
    thumbnailUrl: "/images/thumbnails/marketing-workshop.jpg",
    description: "Workshop interaktif tentang strategi pemasaran digital terkini",
    remindersSent: 230,
    expectedViewers: 300,
  },
  {
    id: "stream-003",
    title: "Tanya Jawab dengan Founder",
    scheduledFor: "2024-06-08T16:00:00",
    status: "scheduled",
    estimatedDuration: 45,
    participants: 1,
    thumbnailUrl: "/images/thumbnails/qa-session.jpg",
    description: "Sesi tanya jawab langsung dengan pendiri perusahaan",
    remindersSent: 180,
    expectedViewers: 250,
  },
  {
    id: "stream-004",
    title: "Demo Fitur Aplikasi Baru",
    scheduledFor: "2024-06-10T11:00:00",
    status: "scheduled",
    estimatedDuration: 30,
    participants: 2,
    thumbnailUrl: "/images/thumbnails/app-demo.jpg",
    description: "Demonstrasi fitur-fitur terbaru aplikasi kami",
    remindersSent: 120,
    expectedViewers: 200,
  },
  {
    id: "stream-005",
    title: "Webinar Strategi Konten untuk E-commerce",
    scheduledFor: "2024-06-15T13:00:00",
    status: "draft",
    estimatedDuration: 60,
    participants: 3,
    thumbnailUrl: "/images/thumbnails/content-strategy.jpg",
    description: "Webinar tentang strategi konten yang efektif untuk meningkatkan penjualan e-commerce",
    remindersSent: 0,
    expectedViewers: 350,
  },
  {
    id: "stream-006",
    title: "Diskusi: Tren E-commerce 2024",
    scheduledFor: "2024-06-20T15:00:00",
    status: "draft",
    estimatedDuration: 75,
    participants: 4,
    thumbnailUrl: "/images/thumbnails/ecommerce-trends.jpg",
    description: "Diskusi panel tentang tren e-commerce terbaru yang perlu diperhatikan di tahun 2024",
    remindersSent: 0,
    expectedViewers: 400,
  }
]

// Statistik jadwal
const scheduleStats = {
  totalStreams: streams.length,
  scheduledStreams: streams.filter(s => s.status === "scheduled").length,
  draftStreams: streams.filter(s => s.status === "draft").length,
  upcomingNextWeek: 3,
  totalScheduledDuration: streams.filter(s => s.status === "scheduled").reduce((acc, curr) => acc + curr.estimatedDuration, 0),
  totalExpectedViewers: streams.filter(s => s.status === "scheduled").reduce((acc, curr) => acc + curr.expectedViewers, 0)
}

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Format waktu
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format tanggal dan waktu
function formatDateTime(dateString: string) {
  return `${formatDate(dateString)}, ${formatTime(dateString)}`
}

// Format durasi ke format jam:menit
function formatDuration(minutes: number) {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours > 0 ? hours + ' jam ' : ''}${mins > 0 ? mins + ' menit' : ''}`
}

// Mendapatkan status label
function getStatusBadge(status: string) {
  switch (status) {
    case "scheduled":
      return <Badge className="bg-blue-100 text-blue-800">Terjadwal</Badge>
    case "draft":
      return <Badge variant="outline" className="text-muted-foreground">Draft</Badge>
    case "live":
      return <Badge className="bg-red-100 text-red-800 animate-pulse">Live</Badge>
    case "completed":
      return <Badge className="bg-green-100 text-green-800">Selesai</Badge>
    case "cancelled":
      return <Badge className="bg-red-100 text-red-800">Dibatalkan</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

export default function LiveSchedulePage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "table">("grid")

  // Filter streams
  const filteredStreams = streams.filter(stream => {
    // Search filter
    const matchesSearch = stream.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          stream.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    // Status filter
    const matchesStatus = statusFilter === "all" || stream.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/live">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Jadwal Siaran</h1>
            <p className="text-muted-foreground">
              Jadwalkan dan kelola sesi siaran langsung
            </p>
          </div>
        </div>
        <Button asChild>
          <Link href="/tenant/dashboard/live/schedule/create">
            <Plus className="h-4 w-4 mr-2" />
            Jadwalkan Siaran
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Siaran</CardTitle>
            <CalendarDays className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{scheduleStats.totalStreams}</div>
            <p className="text-xs text-muted-foreground">
              {scheduleStats.upcomingNextWeek} jadwal minggu depan
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Terjadwal</CardTitle>
            <Calendar className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{scheduleStats.scheduledStreams}</div>
            <p className="text-xs text-muted-foreground">
              {scheduleStats.draftStreams} dalam draft
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Durasi Total</CardTitle>
            <Clock className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{scheduleStats.totalScheduledDuration} menit</div>
            <p className="text-xs text-muted-foreground">
              Untuk siaran terjadwal
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Perkiraan Penonton</CardTitle>
            <Eye className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{scheduleStats.totalExpectedViewers}</div>
            <p className="text-xs text-muted-foreground">
              Total dari semua siaran terjadwal
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Cari siaran..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
          >
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                <span>Status</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Status</SelectItem>
              <SelectItem value="scheduled">Terjadwal</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="completed">Selesai</SelectItem>
              <SelectItem value="cancelled">Dibatalkan</SelectItem>
            </SelectContent>
          </Select>
          <div className="flex border rounded-md overflow-hidden">
            <Button 
              variant={viewMode === "grid" ? "default" : "ghost"} 
              size="sm" 
              className="rounded-none"
              onClick={() => setViewMode("grid")}
            >
              Grid
            </Button>
            <Button 
              variant={viewMode === "table" ? "default" : "ghost"} 
              size="sm" 
              className="rounded-none"
              onClick={() => setViewMode("table")}
            >
              Tabel
            </Button>
          </div>
        </div>
      </div>

      {/* List of streams */}
      {viewMode === "grid" ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredStreams.length === 0 ? (
            <Card className="col-span-full">
              <CardContent className="flex flex-col items-center justify-center pt-6 pb-8">
                <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada siaran ditemukan</h3>
                <p className="text-muted-foreground mb-4 text-center">
                  Coba ubah filter atau jadwalkan siaran baru
                </p>
                <Button asChild>
                  <Link href="/tenant/dashboard/live/schedule/create">
                    <Plus className="h-4 w-4 mr-2" />
                    Jadwalkan Siaran
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredStreams.map(stream => (
              <Card key={stream.id} className="overflow-hidden hover:shadow-md transition-shadow">
                <div className="relative aspect-video bg-muted">
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                    <PlayCircle className="h-12 w-12 text-white opacity-80" />
                  </div>
                  <div className="absolute top-2 left-2">
                    {getStatusBadge(stream.status)}
                  </div>
                  <div className="absolute bottom-2 right-2">
                    <Badge className="bg-blue-600">
                      <Clock10 className="h-3 w-3 mr-1" />
                      {formatDuration(stream.estimatedDuration)}
                    </Badge>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold line-clamp-2 mb-2">{stream.title}</h3>
                  <div className="flex items-center text-sm text-muted-foreground mb-2">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>{formatDateTime(stream.scheduledFor)}</span>
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                    {stream.description}
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span className="text-sm">{stream.participants}</span>
                      <Eye className="h-4 w-4 ml-2" />
                      <span className="text-sm">{stream.expectedViewers}</span>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/tenant/dashboard/live/schedule/${stream.id}`}>
                            Detail
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/tenant/dashboard/live/schedule/${stream.id}/edit`}>
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">Hapus</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      ) : (
        <Card>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Judul</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Jadwal</TableHead>
                  <TableHead>Durasi</TableHead>
                  <TableHead>Peserta</TableHead>
                  <TableHead>Target Penonton</TableHead>
                  <TableHead className="text-right">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStreams.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center">
                        <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">Tidak ada siaran ditemukan</h3>
                        <p className="text-muted-foreground mb-4">
                          Coba ubah filter atau jadwalkan siaran baru
                        </p>
                        <Button asChild>
                          <Link href="/tenant/dashboard/live/schedule/create">
                            <Plus className="h-4 w-4 mr-2" />
                            Jadwalkan Siaran
                          </Link>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredStreams.map(stream => (
                    <TableRow key={stream.id}>
                      <TableCell className="font-medium max-w-[200px] truncate">
                        <Link href={`/tenant/dashboard/live/schedule/${stream.id}`} className="hover:underline">
                          {stream.title}
                        </Link>
                      </TableCell>
                      <TableCell>{getStatusBadge(stream.status)}</TableCell>
                      <TableCell>{formatDateTime(stream.scheduledFor)}</TableCell>
                      <TableCell>{formatDuration(stream.estimatedDuration)}</TableCell>
                      <TableCell>{stream.participants}</TableCell>
                      <TableCell>{stream.expectedViewers}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-1">
                          <Button size="sm" variant="ghost" asChild>
                            <Link href={`/tenant/dashboard/live/schedule/${stream.id}`}>
                              Detail
                            </Link>
                          </Button>
                          <Button size="sm" variant="ghost" asChild>
                            <Link href={`/tenant/dashboard/live/schedule/${stream.id}/edit`}>
                              <PencilIcon className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-600">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </Card>
      )}
    </div>
  )
} 