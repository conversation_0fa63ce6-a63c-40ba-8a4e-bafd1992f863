"use client"

import { useState } from "react"
import { Activity, Alert<PERSON><PERSON>gle, CheckCircle, Clock, Database, HardDrive, RefreshCw, Server, Zap } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { AnalyticsTimeSeriesChart } from "@/components/analytics/analytics-time-series-chart"
import { DateRangePicker } from "@/components/analytics/date-range-picker"

// Mock data for system health
const systemHealthData = [
  { name: "API Server", status: "online", uptime: "99.98%", load: 42 },
  { name: "Database", status: "online", uptime: "99.95%", load: 38 },
  { name: "Storage", status: "online", uptime: "99.99%", load: 25 },
  { name: "<PERSON><PERSON>", status: "online", uptime: "99.97%", load: 30 },
  { name: "Authentication", status: "online", uptime: "99.99%", load: 15 },
  { name: "Payment Gateway", status: "warning", uptime: "99.85%", load: 65 },
  { name: "Search Service", status: "online", uptime: "99.92%", load: 45 },
  { name: "Email Service", status: "online", uptime: "99.90%", load: 20 },
]

// Mock data for resource utilization
const cpuUtilizationData = [
  { name: "00:00", value: 35 },
  { name: "03:00", value: 28 },
  { name: "06:00", value: 45 },
  { name: "09:00", value: 65 },
  { name: "12:00", value: 75 },
  { name: "15:00", value: 82 },
  { name: "18:00", value: 55 },
  { name: "21:00", value: 40 },
]

const memoryUtilizationData = [
  { name: "00:00", value: 45 },
  { name: "03:00", value: 48 },
  { name: "06:00", value: 52 },
  { name: "09:00", value: 70 },
  { name: "12:00", value: 85 },
  { name: "15:00", value: 75 },
  { name: "18:00", value: 65 },
  { name: "21:00", value: 50 },
]

const diskUtilizationData = [
  { name: "00:00", value: 60 },
  { name: "03:00", value: 61 },
  { name: "06:00", value: 62 },
  { name: "09:00", value: 63 },
  { name: "12:00", value: 64 },
  { name: "15:00", value: 65 },
  { name: "18:00", value: 66 },
  { name: "21:00", value: 67 },
]

// Mock data for error rates
const errorRateData = [
  { name: "00:00", value: 0.5 },
  { name: "03:00", value: 0.3 },
  { name: "06:00", value: 0.2 },
  { name: "09:00", value: 0.8 },
  { name: "12:00", value: 1.2 },
  { name: "15:00", value: 0.7 },
  { name: "18:00", value: 0.4 },
  { name: "21:00", value: 0.3 },
]

// Mock data for recent incidents
const recentIncidents = [
  {
    id: "1",
    service: "Payment Gateway",
    status: "resolved",
    description: "Intermittent timeouts on payment processing",
    startTime: "2025-05-12T08:30:00",
    endTime: "2025-05-12T09:45:00",
    duration: "1h 15m",
  },
  {
    id: "2",
    service: "Database",
    status: "resolved",
    description: "High latency on read operations",
    startTime: "2025-05-10T14:20:00",
    endTime: "2025-05-10T15:10:00",
    duration: "50m",
  },
  {
    id: "3",
    service: "API Server",
    status: "resolved",
    description: "Increased error rate on authentication endpoints",
    startTime: "2025-05-08T11:05:00",
    endTime: "2025-05-08T11:35:00",
    duration: "30m",
  },
]

export function SystemsDashboard() {
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    to: new Date(),
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "text-green-500"
      case "warning":
        return "text-yellow-500"
      case "offline":
        return "text-red-500"
      default:
        return "text-gray-500"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "online":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case "offline":
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      default:
        return <Activity className="h-5 w-5 text-gray-500" />
    }
  }

  const getLoadColor = (load: number) => {
    if (load < 50) return "bg-green-500"
    if (load < 80) return "bg-yellow-500"
    return "bg-red-500"
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Systems Dashboard</h2>
          <p className="text-muted-foreground">Monitor system health, resource utilization, and performance metrics</p>
        </div>
        <div className="flex items-center gap-2">
          <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
          <Button size="sm" variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Operational</div>
            <p className="text-xs text-muted-foreground">All systems normal</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Uptime</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">99.97%</div>
            <p className="text-xs text-muted-foreground">Last 30 days</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0.05%</div>
            <p className="text-xs text-muted-foreground">Last 24 hours</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Response Time</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">187ms</div>
            <p className="text-xs text-muted-foreground">Average</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="health">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="health">System Health</TabsTrigger>
          <TabsTrigger value="resources">Resource Utilization</TabsTrigger>
          <TabsTrigger value="errors">Error Monitoring</TabsTrigger>
          <TabsTrigger value="incidents">Incidents</TabsTrigger>
        </TabsList>

        <TabsContent value="health" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Service Status</CardTitle>
              <CardDescription>Current status of all system services</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {systemHealthData.map((service) => (
                  <div key={service.name} className="grid grid-cols-4 items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Server className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{service.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(service.status)}
                      <span className={getStatusColor(service.status)}>
                        {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{service.uptime}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">{service.load}%</span>
                      <Progress value={service.load} className={getLoadColor(service.load)} />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="resources" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>CPU Utilization</CardTitle>
                <CardDescription>Average CPU usage across all servers</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <AnalyticsTimeSeriesChart
                  data={cpuUtilizationData}
                  index="name"
                  categories={["value"]}
                  colors={["#3b82f6"]}
                  valueFormatter={(value) => `${value}%`}
                  showLegend={false}
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Memory Utilization</CardTitle>
                <CardDescription>Average memory usage across all servers</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <AnalyticsTimeSeriesChart
                  data={memoryUtilizationData}
                  index="name"
                  categories={["value"]}
                  colors={["#10b981"]}
                  valueFormatter={(value) => `${value}%`}
                  showLegend={false}
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Disk Utilization</CardTitle>
                <CardDescription>Average disk usage across all servers</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <AnalyticsTimeSeriesChart
                  data={diskUtilizationData}
                  index="name"
                  categories={["value"]}
                  colors={["#f59e0b"]}
                  valueFormatter={(value) => `${value}%`}
                  showLegend={false}
                />
              </CardContent>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Resource Allocation</CardTitle>
              <CardDescription>Current resource allocation by service</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { name: "API Servers", cpu: 45, memory: 60, disk: 25 },
                  { name: "Database Cluster", cpu: 65, memory: 75, disk: 80 },
                  { name: "Cache Servers", cpu: 30, memory: 50, disk: 15 },
                  { name: "Storage Servers", cpu: 20, memory: 30, disk: 90 },
                  { name: "Background Workers", cpu: 55, memory: 40, disk: 30 },
                ].map((service) => (
                  <div key={service.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{service.name}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">CPU</span>
                          <span>{service.cpu}%</span>
                        </div>
                        <Progress
                          value={service.cpu}
                          className={
                            service.cpu > 80 ? "bg-red-500" : service.cpu > 60 ? "bg-yellow-500" : "bg-green-500"
                          }
                        />
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Memory</span>
                          <span>{service.memory}%</span>
                        </div>
                        <Progress
                          value={service.memory}
                          className={
                            service.memory > 80 ? "bg-red-500" : service.memory > 60 ? "bg-yellow-500" : "bg-green-500"
                          }
                        />
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Disk</span>
                          <span>{service.disk}%</span>
                        </div>
                        <Progress
                          value={service.disk}
                          className={
                            service.disk > 80 ? "bg-red-500" : service.disk > 60 ? "bg-yellow-500" : "bg-green-500"
                          }
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Error Rate</CardTitle>
              <CardDescription>System-wide error rate over time</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <AnalyticsTimeSeriesChart
                data={errorRateData}
                index="name"
                categories={["value"]}
                colors={["#ef4444"]}
                valueFormatter={(value) => `${value}%`}
                showLegend={false}
              />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Top Errors</CardTitle>
              <CardDescription>Most frequent errors in the last 24 hours</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { type: "Database Connection Timeout", count: 24, service: "Database", status: "investigating" },
                  { type: "Payment Gateway Timeout", count: 18, service: "Payment", status: "resolved" },
                  { type: "API Rate Limit Exceeded", count: 15, service: "API", status: "resolved" },
                  { type: "Authentication Failed", count: 12, service: "Auth", status: "resolved" },
                  { type: "File Upload Error", count: 8, service: "Storage", status: "resolved" },
                ].map((error, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                      </div>
                      <div>
                        <p className="font-medium">{error.type}</p>
                        <p className="text-sm text-muted-foreground">{error.service}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge variant={error.status === "investigating" ? "destructive" : "outline"}>
                        {error.status}
                      </Badge>
                      <span className="font-medium">{error.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="incidents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Incidents</CardTitle>
              <CardDescription>System incidents in the last 7 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {recentIncidents.map((incident) => (
                  <div key={incident.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          Resolved
                        </Badge>
                        <h4 className="font-semibold">{incident.service}</h4>
                      </div>
                      <span className="text-sm text-muted-foreground">{incident.duration}</span>
                    </div>
                    <p className="text-sm">{incident.description}</p>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>Started: {new Date(incident.startTime).toLocaleString()}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <CheckCircle className="h-3 w-3 text-green-500" />
                        <span>Resolved: {new Date(incident.endTime).toLocaleString()}</span>
                      </div>
                    </div>
                    <Separator />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Incident History</CardTitle>
              <CardDescription>Monthly incident statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { month: "May 2025", count: 3, avgResolution: "45m", availability: "99.97%" },
                  { month: "April 2025", count: 5, avgResolution: "38m", availability: "99.95%" },
                  { month: "March 2025", count: 2, avgResolution: "27m", availability: "99.98%" },
                  { month: "February 2025", count: 4, avgResolution: "52m", availability: "99.94%" },
                  { month: "January 2025", count: 6, avgResolution: "41m", availability: "99.93%" },
                ].map((month, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <div className="font-medium">{month.month}</div>
                    <div className="flex items-center gap-8">
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Incidents</div>
                        <div>{month.count}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Avg. Resolution</div>
                        <div>{month.avgResolution}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Availability</div>
                        <div className="text-green-500">{month.availability}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Infrastructure Overview</CardTitle>
            <CardDescription>Current infrastructure status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: "Production Environment", servers: 12, status: "Healthy", region: "Asia Southeast" },
                { name: "Staging Environment", servers: 4, status: "Healthy", region: "Asia Southeast" },
                { name: "Development Environment", servers: 2, status: "Maintenance", region: "Asia Southeast" },
                { name: "Database Cluster", servers: 5, status: "Healthy", region: "Asia Southeast" },
                { name: "Cache Cluster", servers: 3, status: "Healthy", region: "Asia Southeast" },
              ].map((env, i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                      {env.name.includes("Database") ? (
                        <Database className="h-5 w-5 text-primary" />
                      ) : env.name.includes("Cache") ? (
                        <HardDrive className="h-5 w-5 text-primary" />
                      ) : (
                        <Server className="h-5 w-5 text-primary" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{env.name}</p>
                      <p className="text-sm text-muted-foreground">{env.region}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-sm ${env.status === "Healthy" ? "text-green-500" : "text-yellow-500"}`}>
                      {env.status}
                    </p>
                    <p className="text-sm text-muted-foreground">{env.servers} servers</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>System Notifications</CardTitle>
            <CardDescription>Recent system alerts and notifications</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  title: "Scheduled Maintenance",
                  description: "Database optimization scheduled for May 15, 2025, 02:00 - 04:00 UTC",
                  time: "2 hours ago",
                  type: "info",
                },
                {
                  title: "High CPU Usage Alert",
                  description: "API Server cluster experiencing high CPU usage",
                  time: "5 hours ago",
                  type: "warning",
                },
                {
                  title: "Payment Gateway Issue Resolved",
                  description: "Intermittent timeouts on payment processing have been resolved",
                  time: "1 day ago",
                  type: "success",
                },
                {
                  title: "New Region Deployed",
                  description: "New server region deployed in Europe West",
                  time: "2 days ago",
                  type: "info",
                },
                {
                  title: "Security Patch Applied",
                  description: "Critical security patches applied to all servers",
                  time: "3 days ago",
                  type: "success",
                },
              ].map((notification, i) => (
                <div key={i} className="flex gap-4">
                  <div
                    className={`mt-0.5 h-2 w-2 rounded-full ${
                      notification.type === "warning"
                        ? "bg-yellow-500"
                        : notification.type === "success"
                          ? "bg-green-500"
                          : "bg-blue-500"
                    }`}
                  />
                  <div className="space-y-1">
                    <p className="font-medium">{notification.title}</p>
                    <p className="text-sm text-muted-foreground">{notification.description}</p>
                    <p className="text-xs text-muted-foreground">{notification.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
