import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

export function AffiliateProductPerformance() {
  // Sample data for product performance
  const products = [
    {
      id: 1,
      name: "Premium Wireless Headphones",
      category: "Electronics",
      clicks: 245,
      conversions: 32,
      conversionRate: 13.1,
      revenue: 3840,
      commission: 384,
      trend: "up",
    },
    {
      id: 2,
      name: "Organic Cotton T-Shirt",
      category: "Fashion",
      clicks: 189,
      conversions: 27,
      conversionRate: 14.3,
      revenue: 1080,
      commission: 108,
      trend: "up",
    },
    {
      id: 3,
      name: "Smart Home Security Camera",
      category: "Electronics",
      clicks: 156,
      conversions: 18,
      conversionRate: 11.5,
      revenue: 2700,
      commission: 270,
      trend: "down",
    },
    {
      id: 4,
      name: "Stainless Steel Water Bottle",
      category: "Home & Kitchen",
      clicks: 132,
      conversions: 15,
      conversionRate: 11.4,
      revenue: 450,
      commission: 45,
      trend: "stable",
    },
    {
      id: 5,
      name: "Fitness Tracker Watch",
      category: "Sports & Outdoors",
      clicks: 128,
      conversions: 14,
      conversionRate: 10.9,
      revenue: 1400,
      commission: 140,
      trend: "up",
    },
  ]

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Product Performance</CardTitle>
        <CardDescription>Analysis of your top performing products and categories</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Top Converting Products</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                <p className="text-sm text-gray-500 dark:text-gray-400">Top converting products chart</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Category Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                <p className="text-sm text-gray-500 dark:text-gray-400">Category performance chart</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Product</TableHead>
              <TableHead>Category</TableHead>
              <TableHead className="text-right">Clicks</TableHead>
              <TableHead className="text-right">Conv.</TableHead>
              <TableHead className="text-right">Rate</TableHead>
              <TableHead className="text-right">Revenue</TableHead>
              <TableHead className="text-right">Commission</TableHead>
              <TableHead className="text-right">Trend</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.map((product) => (
              <TableRow key={product.id}>
                <TableCell className="font-medium">{product.name}</TableCell>
                <TableCell>{product.category}</TableCell>
                <TableCell className="text-right">{product.clicks}</TableCell>
                <TableCell className="text-right">{product.conversions}</TableCell>
                <TableCell className="text-right">{product.conversionRate}%</TableCell>
                <TableCell className="text-right">${product.revenue}</TableCell>
                <TableCell className="text-right">${product.commission}</TableCell>
                <TableCell className="text-right">
                  <Badge
                    variant={product.trend === "up" ? "success" : product.trend === "down" ? "destructive" : "outline"}
                  >
                    {product.trend === "up" ? "↑" : product.trend === "down" ? "↓" : "→"}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
