"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { RefreshCw } from "lucide-react"

interface StorePoliciesProps {
  formData: any
  onChange: (field: string, value: any) => void
}

export function StorePolicies({ formData, onChange }: StorePoliciesProps) {
  const [activeTab, setActiveTab] = useState("return")

  // Template kebijakan
  const returnPolicyTemplate = `Kami menerima pengembalian produk dalam jangka waktu 14 hari setelah barang diterima. Produk harus dalam kondisi asli, belum digunakan, dan dengan semua label masih terpasang. Pembeli bertanggung jawab atas biaya pengiriman pengembalian, kecuali jika pengembalian disebabkan oleh kesalahan kami. Setelah produk diterima dan diperiksa, kami akan memproses pengembalian dana dalam waktu 7 hari kerja.`

  const shippingPolicyTemplate = `Kami memproses dan mengirimkan pesanan dalam waktu 1-3 hari kerja. Waktu pengiriman tergantung pada lokasi dan metode pengiriman yang dipilih. Kami menawarkan pengiriman reguler (3-7 hari kerja) dan pengiriman ekspres (1-2 hari kerja). Biaya pengiriman dihitung berdasarkan berat, dimensi, dan jarak. Kami tidak bertanggung jawab atas keterlambatan yang disebabkan oleh pihak kurir atau kejadian di luar kendali kami.`

  const privacyPolicyTemplate = `Kami mengumpulkan informasi pribadi seperti nama, alamat, email, dan nomor telepon untuk memproses pesanan dan memberikan layanan pelanggan. Kami tidak akan menjual atau membagikan informasi pribadi Anda kepada pihak ketiga tanpa izin Anda. Kami menggunakan cookies untuk meningkatkan pengalaman berbelanja Anda. Anda dapat meminta akses, koreksi, atau penghapusan data pribadi Anda kapan saja dengan menghubungi kami.`

  const termsOfServiceTemplate = `Dengan menggunakan toko online kami, Anda menyetujui syarat dan ketentuan ini. Kami berhak mengubah harga dan ketersediaan produk tanpa pemberitahuan. Semua konten di toko kami dilindungi hak cipta. Kami tidak bertanggung jawab atas kesalahan tipografi atau fotografi. Kami berhak menolak layanan kepada siapa pun dengan alasan apa pun. Perselisihan akan diselesaikan melalui arbitrase sesuai dengan hukum yang berlaku.`

  // Handler untuk opsi pengiriman
  const handleShippingOptionChange = (option: string, checked: boolean) => {
    const currentOptions = [...(formData.shippingOptions || [])]

    if (checked) {
      if (!currentOptions.includes(option)) {
        onChange("shippingOptions", [...currentOptions, option])
      }
    } else {
      onChange(
        "shippingOptions",
        currentOptions.filter((o) => o !== option),
      )
    }
  }

  // Handler untuk menerapkan template
  const applyTemplate = (field: string, template: string) => {
    onChange(field, template)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Kebijakan Toko</h2>
        <p className="text-gray-500">Tetapkan kebijakan toko yang jelas untuk membangun kepercayaan dengan pembeli.</p>
      </div>

      <Tabs defaultValue="return" className="w-full" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="return">Pengembalian</TabsTrigger>
          <TabsTrigger value="shipping">Pengiriman</TabsTrigger>
          <TabsTrigger value="privacy">Privasi</TabsTrigger>
          <TabsTrigger value="terms">Syarat Layanan</TabsTrigger>
        </TabsList>

        <TabsContent value="return" className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-base">
              Kebijakan Pengembalian <span className="text-red-500">*</span>
            </Label>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => applyTemplate("returnPolicy", returnPolicyTemplate)}
            >
              <RefreshCw className="h-3 w-3" />
              Gunakan Template
            </Button>
          </div>
          <Textarea
            value={formData.returnPolicy || ""}
            onChange={(e) => onChange("returnPolicy", e.target.value)}
            placeholder="Jelaskan kebijakan pengembalian produk Anda..."
            rows={10}
          />
          <div className="flex gap-2 items-start p-3 bg-amber-50 border border-amber-200 rounded-md">
            <div className="text-amber-600 mt-1">ℹ️</div>
            <div className="text-sm text-amber-700">
              <p className="font-medium mb-1">Tips Kebijakan Pengembalian yang Baik:</p>
              <ul className="list-disc pl-4 space-y-1">
                <li>Tetapkan jangka waktu pengembalian yang jelas (misal: 7 hari, 14 hari)</li>
                <li>Jelaskan kondisi produk yang dapat dikembalikan</li>
                <li>Tentukan siapa yang menanggung biaya pengiriman untuk pengembalian</li>
                <li>Jelaskan proses refund (metode pembayaran, waktu pemrosesan)</li>
              </ul>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="shipping" className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-base">
              Kebijakan Pengiriman <span className="text-red-500">*</span>
            </Label>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => applyTemplate("shippingPolicy", shippingPolicyTemplate)}
            >
              <RefreshCw className="h-3 w-3" />
              Gunakan Template
            </Button>
          </div>
          <Textarea
            value={formData.shippingPolicy || ""}
            onChange={(e) => onChange("shippingPolicy", e.target.value)}
            placeholder="Jelaskan kebijakan pengiriman Anda..."
            rows={8}
          />

          <div className="space-y-3 mt-4">
            <Label className="text-base">Opsi Pengiriman yang Ditawarkan</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {[
                { id: "regular", label: "Reguler (3-7 hari)" },
                { id: "express", label: "Ekspres (1-2 hari)" },
                { id: "sameday", label: "Same Day (dalam kota)" },
                { id: "pickup", label: "Self Pickup" },
              ].map((option) => (
                <div key={option.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`shipping-${option.id}`}
                    checked={(formData.shippingOptions || []).includes(option.id)}
                    onCheckedChange={(checked) => handleShippingOptionChange(option.id, checked as boolean)}
                  />
                  <label
                    htmlFor={`shipping-${option.id}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {option.label}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-3 mt-4">
            <Label className="text-base">Waktu Pemrosesan Pesanan</Label>
            <div className="grid grid-cols-1 gap-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="processing-1-2"
                  checked={formData.processingTime === "1-2"}
                  onCheckedChange={(checked) => {
                    if (checked) onChange("processingTime", "1-2")
                  }}
                />
                <label
                  htmlFor="processing-1-2"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  1-2 hari kerja
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="processing-3-5"
                  checked={formData.processingTime === "3-5"}
                  onCheckedChange={(checked) => {
                    if (checked) onChange("processingTime", "3-5")
                  }}
                />
                <label
                  htmlFor="processing-3-5"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  3-5 hari kerja
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="processing-5-7"
                  checked={formData.processingTime === "5-7"}
                  onCheckedChange={(checked) => {
                    if (checked) onChange("processingTime", "5-7")
                  }}
                />
                <label
                  htmlFor="processing-5-7"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  5-7 hari kerja
                </label>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="privacy" className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-base">
              Kebijakan Privasi <span className="text-red-500">*</span>
            </Label>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => applyTemplate("privacyPolicy", privacyPolicyTemplate)}
            >
              <RefreshCw className="h-3 w-3" />
              Gunakan Template
            </Button>
          </div>
          <Textarea
            value={formData.privacyPolicy || ""}
            onChange={(e) => onChange("privacyPolicy", e.target.value)}
            placeholder="Jelaskan kebijakan privasi Anda..."
            rows={10}
          />
          <div className="flex gap-2 items-start p-3 bg-amber-50 border border-amber-200 rounded-md">
            <div className="text-amber-600 mt-1">ℹ️</div>
            <div className="text-sm text-amber-700">
              <p className="font-medium mb-1">Poin Penting untuk Kebijakan Privasi:</p>
              <ul className="list-disc pl-4 space-y-1">
                <li>Informasi apa yang Anda kumpulkan dari pelanggan</li>
                <li>Bagaimana Anda menggunakan informasi tersebut</li>
                <li>Apakah Anda membagikan informasi dengan pihak ketiga</li>
                <li>Bagaimana pelanggan dapat mengakses atau menghapus data mereka</li>
                <li>Penggunaan cookies dan teknologi pelacakan</li>
              </ul>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="terms" className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-base">
              Syarat Layanan <span className="text-red-500">*</span>
            </Label>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => applyTemplate("termsOfService", termsOfServiceTemplate)}
            >
              <RefreshCw className="h-3 w-3" />
              Gunakan Template
            </Button>
          </div>
          <Textarea
            value={formData.termsOfService || ""}
            onChange={(e) => onChange("termsOfService", e.target.value)}
            placeholder="Jelaskan syarat layanan Anda..."
            rows={10}
          />
          <div className="flex gap-2 items-start p-3 bg-amber-50 border border-amber-200 rounded-md">
            <div className="text-amber-600 mt-1">ℹ️</div>
            <div className="text-sm text-amber-700">
              <p className="font-medium mb-1">Elemen Penting Syarat Layanan:</p>
              <ul className="list-disc pl-4 space-y-1">
                <li>Hak dan kewajiban penjual dan pembeli</li>
                <li>Kebijakan harga dan pembayaran</li>
                <li>Hak kekayaan intelektual</li>
                <li>Batasan tanggung jawab</li>
                <li>Proses penyelesaian sengketa</li>
                <li>Hukum yang berlaku</li>
              </ul>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex justify-between pt-4">
        <Button
          variant="outline"
          onClick={() => {
            // Navigasi ke tab sebelumnya
            if (activeTab === "shipping") setActiveTab("return")
            else if (activeTab === "privacy") setActiveTab("shipping")
            else if (activeTab === "terms") setActiveTab("privacy")
          }}
          disabled={activeTab === "return"}
        >
          Sebelumnya
        </Button>
        <Button
          onClick={() => {
            // Navigasi ke tab berikutnya
            if (activeTab === "return") setActiveTab("shipping")
            else if (activeTab === "shipping") setActiveTab("privacy")
            else if (activeTab === "privacy") setActiveTab("terms")
          }}
          disabled={activeTab === "terms"}
        >
          Selanjutnya
        </Button>
      </div>
    </div>
  )
}
