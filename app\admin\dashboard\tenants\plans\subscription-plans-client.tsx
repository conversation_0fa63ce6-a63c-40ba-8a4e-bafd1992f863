"use client"

import { SubscriptionPlansList } from "@/components/admin/tenants/subscription-plans-list"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Plus, ArrowLeft } from "lucide-react"
import { PageHeader } from "@/components/admin/ui/page-header"
import Link from "next/link"

export function SubscriptionPlansClient() {
  return (
    <div className="flex flex-col gap-6">
      <PageHeader
        title="Subscription Plans"
        description="Manage subscription plans for tenants"
        breadcrumbs={[
          { title: "Dashboard", href: "/admin/dashboard" },
          { title: "Tenants", href: "/admin/dashboard/tenants" },
          { title: "Plans", href: "/admin/dashboard/tenants/plans" },
        ]}
        actions={
          <div className="flex gap-2">
            <Link href="/admin/dashboard/tenants">
              <Button variant="outline">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Tenants
              </Button>
            </Link>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              <span>Add Plan</span>
            </Button>
          </div>
        }
      />

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Total Plans</CardTitle>
            <CardDescription>Active subscription plans</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">4</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Total Subscribers</CardTitle>
            <CardDescription>Across all plans</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">247</div>
            <p className="text-xs text-muted-foreground">+12% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Monthly Revenue</CardTitle>
            <CardDescription>From all subscription plans</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">$24,389</div>
            <p className="text-xs text-muted-foreground">+18% from last month</p>
          </CardContent>
        </Card>
      </div>

      <SubscriptionPlansList />
    </div>
  )
}
