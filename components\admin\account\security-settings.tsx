"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { AlertCircle, ArrowLeft, Check, Copy, Eye, EyeOff, Smartphone } from "lucide-react"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

const passwordFormSchema = z
  .object({
    currentPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
    newPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
    confirmPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match.",
    path: ["confirmPassword"],
  })

type PasswordFormValues = z.infer<typeof passwordFormSchema>

// Dummy login history data
const loginHistory = [
  {
    id: 1,
    device: "Chrome on Windows",
    location: "Jakarta, Indonesia",
    ip: "103.28.xx.xx",
    time: "Today, 10:30 AM",
    status: "success",
    current: true,
  },
  {
    id: 2,
    device: "Safari on macOS",
    location: "Jakarta, Indonesia",
    ip: "103.28.xx.xx",
    time: "Yesterday, 3:45 PM",
    status: "success",
    current: false,
  },
  {
    id: 3,
    device: "Firefox on Windows",
    location: "Singapore",
    ip: "128.53.xx.xx",
    time: "3 days ago, 11:20 AM",
    status: "success",
    current: false,
  },
  {
    id: 4,
    device: "Chrome on Android",
    location: "Unknown",
    ip: "185.73.xx.xx",
    time: "1 week ago, 2:15 PM",
    status: "failed",
    current: false,
  },
]

export function SecuritySettings() {
  const router = useRouter()
  const { toast } = useToast()
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(true)
  const [showRecoveryCodes, setShowRecoveryCodes] = useState(false)
  const [sessionToRevoke, setSessionToRevoke] = useState<number | null>(null)
  const [isRevokeDialogOpen, setIsRevokeDialogOpen] = useState(false)

  // Recovery codes (dummy data)
  const recoveryCodes = [
    "ABCD-EFGH-IJKL-MNOP",
    "QRST-UVWX-YZ12-3456",
    "7890-ABCD-EFGH-IJKL",
    "MNOP-QRST-UVWX-YZ12",
    "3456-7890-ABCD-EFGH",
  ]

  const form = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
    mode: "onChange",
  })

  function onSubmit(data: PasswordFormValues) {
    toast({
      title: "Password updated",
      description: "Your password has been updated successfully.",
    })
    form.reset()
  }

  const copyRecoveryCodes = () => {
    navigator.clipboard.writeText(recoveryCodes.join("\n"))
    toast({
      title: "Recovery codes copied",
      description: "Recovery codes have been copied to your clipboard.",
    })
  }

  const downloadRecoveryCodes = () => {
    const element = document.createElement("a")
    const file = new Blob([recoveryCodes.join("\n")], { type: "text/plain" })
    element.href = URL.createObjectURL(file)
    element.download = "sellzio-recovery-codes.txt"
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
    toast({
      title: "Recovery codes downloaded",
      description: "Recovery codes have been downloaded to your device.",
    })
  }

  const regenerateRecoveryCodes = () => {
    toast({
      title: "Recovery codes regenerated",
      description: "New recovery codes have been generated. Please save them securely.",
    })
  }

  const revokeSession = (id: number) => {
    setSessionToRevoke(null)
    setIsRevokeDialogOpen(false)
    toast({
      title: "Session revoked",
      description: "The selected session has been terminated.",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Security</h2>
            <p className="text-muted-foreground">Manage your account security and authentication settings</p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="password" className="space-y-4">
        <TabsList>
          <TabsTrigger value="password">Password</TabsTrigger>
          <TabsTrigger value="2fa">Two-Factor Authentication</TabsTrigger>
          <TabsTrigger value="sessions">Active Sessions</TabsTrigger>
          <TabsTrigger value="recovery">Recovery Options</TabsTrigger>
        </TabsList>

        <TabsContent value="password" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>Update your password to keep your account secure</CardDescription>
            </CardHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="currentPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Current Password</FormLabel>
                        <div className="relative">
                          <FormControl>
                            <Input
                              placeholder="Enter your current password"
                              type={showCurrentPassword ? "text" : "password"}
                              {...field}
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          >
                            {showCurrentPassword ? (
                              <EyeOff className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <Eye className="h-4 w-4 text-muted-foreground" />
                            )}
                            <span className="sr-only">{showCurrentPassword ? "Hide password" : "Show password"}</span>
                          </Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="newPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>New Password</FormLabel>
                        <div className="relative">
                          <FormControl>
                            <Input
                              placeholder="Enter your new password"
                              type={showNewPassword ? "text" : "password"}
                              {...field}
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                          >
                            {showNewPassword ? (
                              <EyeOff className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <Eye className="h-4 w-4 text-muted-foreground" />
                            )}
                            <span className="sr-only">{showNewPassword ? "Hide password" : "Show password"}</span>
                          </Button>
                        </div>
                        <FormDescription>
                          Password must be at least 8 characters and include a mix of letters, numbers, and symbols.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirm New Password</FormLabel>
                        <div className="relative">
                          <FormControl>
                            <Input
                              placeholder="Confirm your new password"
                              type={showConfirmPassword ? "text" : "password"}
                              {...field}
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          >
                            {showConfirmPassword ? (
                              <EyeOff className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <Eye className="h-4 w-4 text-muted-foreground" />
                            )}
                            <span className="sr-only">{showConfirmPassword ? "Hide password" : "Show password"}</span>
                          </Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter>
                  <Button type="submit">Update Password</Button>
                </CardFooter>
              </form>
            </Form>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Password Requirements</CardTitle>
              <CardDescription>Ensure your password meets these security requirements</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-green-500" />
                <p className="text-sm">At least 8 characters long</p>
              </div>
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-green-500" />
                <p className="text-sm">Contains at least one uppercase letter</p>
              </div>
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-green-500" />
                <p className="text-sm">Contains at least one number</p>
              </div>
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-green-500" />
                <p className="text-sm">Contains at least one special character</p>
              </div>
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-green-500" />
                <p className="text-sm">Not similar to previous passwords</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="2fa" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Two-Factor Authentication (2FA)</CardTitle>
              <CardDescription>Add an extra layer of security to your account</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="font-medium">Two-Factor Authentication</div>
                  <div className="text-sm text-muted-foreground">
                    {twoFactorEnabled
                      ? "2FA is currently enabled for your account"
                      : "Enable 2FA to add an extra layer of security"}
                  </div>
                </div>
                <Switch checked={twoFactorEnabled} onCheckedChange={setTwoFactorEnabled} />
              </div>
              <Separator />
              {twoFactorEnabled ? (
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium">Current Method</h3>
                    <div className="mt-2 flex items-center space-x-4 rounded-lg border p-4">
                      <div className="rounded-full bg-primary/10 p-2">
                        <Smartphone className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">Authenticator App</p>
                        <p className="text-sm text-muted-foreground">Using Google Authenticator or similar app</p>
                      </div>
                      <Button variant="outline" size="sm">
                        Change
                      </Button>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">Recovery Codes</h3>
                    <p className="text-sm text-muted-foreground mt-1 mb-2">
                      Save these recovery codes in a secure location. They can be used to recover access to your account
                      if you lose your 2FA device.
                    </p>
                    <div className="rounded-lg border p-4">
                      {showRecoveryCodes ? (
                        <div className="space-y-2">
                          <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                            {recoveryCodes.map((code, index) => (
                              <div key={index} className="rounded bg-muted px-3 py-2 font-mono text-sm">
                                {code}
                              </div>
                            ))}
                          </div>
                          <div className="flex flex-wrap gap-2 pt-2">
                            <Button variant="outline" size="sm" onClick={copyRecoveryCodes}>
                              <Copy className="mr-2 h-4 w-4" />
                              Copy
                            </Button>
                            <Button variant="outline" size="sm" onClick={downloadRecoveryCodes}>
                              Download
                            </Button>
                            <Button variant="outline" size="sm" onClick={regenerateRecoveryCodes}>
                              Regenerate
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => setShowRecoveryCodes(false)}>
                              Hide Codes
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center justify-between">
                          <div className="text-sm">Recovery codes are hidden for security</div>
                          <Button variant="outline" size="sm" onClick={() => setShowRecoveryCodes(true)}>
                            Show Recovery Codes
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="rounded-lg border p-4">
                    <h3 className="font-medium">Set up Two-Factor Authentication</h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      Choose a method to add an extra layer of security to your account
                    </p>
                    <div className="mt-4 space-y-2">
                      <div className="flex items-center space-x-4 rounded-lg border p-3">
                        <div className="rounded-full bg-primary/10 p-2">
                          <Smartphone className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">Authenticator App</p>
                          <p className="text-sm text-muted-foreground">
                            Use Google Authenticator, Authy, or similar app
                          </p>
                        </div>
                        <Button size="sm">Set up</Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Sessions</CardTitle>
              <CardDescription>Manage your active sessions across different devices</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Device</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>IP Address</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loginHistory.map((session) => (
                    <TableRow key={session.id}>
                      <TableCell className="font-medium">
                        {session.device}
                        {session.current && <span className="ml-2 text-xs text-green-500">(Current)</span>}
                      </TableCell>
                      <TableCell>{session.location}</TableCell>
                      <TableCell>{session.ip}</TableCell>
                      <TableCell>{session.time}</TableCell>
                      <TableCell>
                        <span
                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                            session.status === "success"
                              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                              : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                          }`}
                        >
                          {session.status === "success" ? "Success" : "Failed"}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        {!session.current && (
                          <Dialog
                            open={isRevokeDialogOpen && sessionToRevoke === session.id}
                            onOpenChange={(open) => {
                              setIsRevokeDialogOpen(open)
                              if (!open) setSessionToRevoke(null)
                            }}
                          >
                            <DialogTrigger asChild>
                              <Button variant="outline" size="sm" onClick={() => setSessionToRevoke(session.id)}>
                                Revoke
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Revoke Session</DialogTitle>
                                <DialogDescription>
                                  Are you sure you want to revoke this session? The user will be logged out immediately.
                                </DialogDescription>
                              </DialogHeader>
                              <DialogFooter>
                                <Button
                                  variant="outline"
                                  onClick={() => {
                                    setIsRevokeDialogOpen(false)
                                    setSessionToRevoke(null)
                                  }}
                                >
                                  Cancel
                                </Button>
                                <Button variant="destructive" onClick={() => revokeSession(session.id)}>
                                  Revoke
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                Revoke All Other Sessions
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="recovery" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Account Recovery Options</CardTitle>
              <CardDescription>Configure additional recovery methods for your account</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Recovery Email</h3>
                <div className="flex items-center justify-between rounded-lg border p-3">
                  <div className="flex-1">
                    <p className="font-medium"><EMAIL></p>
                    <p className="text-sm text-muted-foreground">Verified on Jan 15, 2023</p>
                  </div>
                  <Button variant="outline" size="sm">
                    Change
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Recovery Phone</h3>
                <div className="flex items-center justify-between rounded-lg border p-3">
                  <div className="flex-1">
                    <p className="font-medium">+62 812 **** 5678</p>
                    <p className="text-sm text-muted-foreground">Verified on Jan 15, 2023</p>
                  </div>
                  <Button variant="outline" size="sm">
                    Change
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Security Questions</h3>
                <div className="rounded-lg border p-3">
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium">Question 1</p>
                      <p className="text-sm text-muted-foreground">What was the name of your first pet?</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Question 2</p>
                      <p className="text-sm text-muted-foreground">In what city were you born?</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Question 3</p>
                      <p className="text-sm text-muted-foreground">What was the model of your first car?</p>
                    </div>
                    <Button variant="outline" size="sm">
                      Change Security Questions
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Important</AlertTitle>
            <AlertDescription>
              Keep your recovery options up to date. If you lose access to your account, these will be your only way to
              recover it.
            </AlertDescription>
          </Alert>
        </TabsContent>
      </Tabs>
    </div>
  )
}
