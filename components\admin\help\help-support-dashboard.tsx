"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Search, Play, FileText, Bell, HelpCircle, ExternalLink, Clock } from "lucide-react"

export function HelpSupportDashboard() {
  const [searchQuery, setSearchQuery] = useState("")

  // Dummy data for recent tickets
  const recentTickets = [
    {
      id: "T-1234",
      title: "API Integration Issue",
      status: "open",
      priority: "high",
      updated: "2 hours ago",
    },
    {
      id: "T-1233",
      title: "Payment Gateway Error",
      status: "in-progress",
      priority: "medium",
      updated: "1 day ago",
    },
    {
      id: "T-1232",
      title: "Custom Domain Setup",
      status: "resolved",
      priority: "low",
      updated: "3 days ago",
    },
  ]

  // Dummy data for popular docs
  const popularDocs = [
    {
      title: "Getting Started with SellZio",
      views: 1245,
      category: "Basics",
      url: "/admin/dashboard/help/documentation",
    },
    {
      title: "Setting Up Your First Store",
      views: 982,
      category: "Stores",
      url: "/admin/dashboard/help/documentation",
    },
    {
      title: "Advanced API Usage",
      views: 756,
      category: "Development",
      url: "/admin/dashboard/help/documentation",
    },
  ]

  // Dummy data for video tutorials
  const videoTutorials = [
    {
      title: "Platform Overview",
      duration: "5:32",
      thumbnail: "/placeholder.svg?key=rmkpp",
    },
    {
      title: "Store Configuration",
      duration: "8:15",
      thumbnail: "/placeholder.svg?key=7b8cn",
    },
    {
      title: "Product Management",
      duration: "6:47",
      thumbnail: "/placeholder.svg?key=qihpp",
    },
  ]

  // Status badge color mapping
  const statusColors = {
    open: "bg-yellow-500 hover:bg-yellow-600",
    "in-progress": "bg-blue-500 hover:bg-blue-600",
    resolved: "bg-green-500 hover:bg-green-600",
  }

  // Priority badge color mapping
  const priorityColors = {
    high: "bg-red-500 hover:bg-red-600",
    medium: "bg-orange-500 hover:bg-orange-600",
    low: "bg-blue-500 hover:bg-blue-600",
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Help & Support</h1>
        <p className="text-muted-foreground">
          Access support resources, documentation, and help for the SellZio platform.
        </p>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search for help articles, tutorials, or FAQs..."
          className="pl-10 h-12"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-lg">
              <Bell className="mr-2 h-5 w-5 text-primary" />
              Recent Support Tickets
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentTickets.map((ticket) => (
              <div key={ticket.id} className="flex items-start justify-between">
                <div className="space-y-1">
                  <div className="flex items-center">
                    <span className="font-medium">{ticket.title}</span>
                    <span className="ml-2 text-xs text-muted-foreground">#{ticket.id}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-xs">
                    <Badge className={statusColors[ticket.status]}>{ticket.status.replace("-", " ")}</Badge>
                    <Badge className={priorityColors[ticket.priority]}>{ticket.priority}</Badge>
                    <span className="text-muted-foreground flex items-center">
                      <Clock className="mr-1 h-3 w-3" />
                      {ticket.updated}
                    </span>
                  </div>
                </div>
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/admin/dashboard/help/tickets">View</Link>
                </Button>
              </div>
            ))}
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/admin/dashboard/help/tickets">View All Tickets</Link>
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-lg">
              <FileText className="mr-2 h-5 w-5 text-primary" />
              Popular Documentation
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {popularDocs.map((doc, index) => (
              <div key={index} className="flex items-start justify-between">
                <div className="space-y-1">
                  <div className="font-medium">{doc.title}</div>
                  <div className="flex items-center space-x-2 text-xs">
                    <Badge variant="outline">{doc.category}</Badge>
                    <span className="text-muted-foreground">{doc.views} views</span>
                  </div>
                </div>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={doc.url}>
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            ))}
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/admin/dashboard/help/documentation">Browse Documentation</Link>
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-lg">
              <Play className="mr-2 h-5 w-5 text-primary" />
              Video Tutorials
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {videoTutorials.map((video, index) => (
              <div key={index} className="space-y-2">
                <div className="relative aspect-video rounded-md overflow-hidden">
                  <Image src={video.thumbnail || "/placeholder.svg"} alt={video.title} fill className="object-cover" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-black/60 rounded-full p-2">
                      <Play className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                    {video.duration}
                  </div>
                </div>
                <div className="font-medium">{video.title}</div>
              </div>
            ))}
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">
              View All Tutorials
            </Button>
          </CardFooter>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quick Help</CardTitle>
          <CardDescription>Common questions and resources to help you get started</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="faq">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="faq">FAQs</TabsTrigger>
              <TabsTrigger value="guides">Quick Guides</TabsTrigger>
              <TabsTrigger value="contact">Contact Support</TabsTrigger>
            </TabsList>
            <TabsContent value="faq" className="space-y-4 pt-4">
              <div className="space-y-2">
                <h3 className="font-semibold">How do I set up a new tenant?</h3>
                <p className="text-sm text-muted-foreground">
                  Navigate to Tenants → All Tenants and click on "Add New Tenant". Follow the setup wizard to complete
                  the process.
                </p>
              </div>
              <Separator />
              <div className="space-y-2">
                <h3 className="font-semibold">How do I manage subscription plans?</h3>
                <p className="text-sm text-muted-foreground">
                  Go to Tenants → Subscription Plans to view, create, or modify subscription plans for your tenants.
                </p>
              </div>
              <Separator />
              <div className="space-y-2">
                <h3 className="font-semibold">How do I verify a store?</h3>
                <p className="text-sm text-muted-foreground">
                  Access Stores → Verification Queue to review and approve pending store verification requests.
                </p>
              </div>
              <Button variant="link" className="p-0" asChild>
                <Link href="/admin/dashboard/help/documentation">View all FAQs</Link>
              </Button>
            </TabsContent>
            <TabsContent value="guides" className="space-y-4 pt-4">
              <div className="space-y-2">
                <h3 className="font-semibold">Setting up your first store</h3>
                <p className="text-sm text-muted-foreground">
                  A step-by-step guide to creating and configuring your first store on the platform.
                </p>
                <Button variant="link" className="p-0" size="sm">
                  Read guide
                </Button>
              </div>
              <Separator />
              <div className="space-y-2">
                <h3 className="font-semibold">Managing product inventory</h3>
                <p className="text-sm text-muted-foreground">
                  Learn how to add, edit, and organize products in your store's inventory.
                </p>
                <Button variant="link" className="p-0" size="sm">
                  Read guide
                </Button>
              </div>
              <Separator />
              <div className="space-y-2">
                <h3 className="font-semibold">Processing orders and payments</h3>
                <p className="text-sm text-muted-foreground">
                  A comprehensive guide to handling customer orders and payment processing.
                </p>
                <Button variant="link" className="p-0" size="sm">
                  Read guide
                </Button>
              </div>
            </TabsContent>
            <TabsContent value="contact" className="pt-4">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-primary/10 p-2 rounded-full">
                      <HelpCircle className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Live Chat Support</h3>
                      <p className="text-sm text-muted-foreground">Available 24/7 for urgent issues</p>
                    </div>
                  </div>
                  <Button className="w-full">Start Chat</Button>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-primary/10 p-2 rounded-full">
                      <Bell className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Create Support Ticket</h3>
                      <p className="text-sm text-muted-foreground">For non-urgent issues and requests</p>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/admin/dashboard/help/tickets">Create Ticket</Link>
                  </Button>
                </div>
              </div>
              <div className="mt-6 p-4 bg-muted rounded-lg">
                <h3 className="font-semibold mb-2">Support Hours</h3>
                <p className="text-sm text-muted-foreground">
                  Our technical support team is available Monday through Friday, 9 AM to 6 PM EST. Live chat support is
                  available 24/7 for urgent issues.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
