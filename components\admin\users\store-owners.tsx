"use client"

import { useState } from "react"
import { DataTable } from "@/components/ui/data-table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Eye, Mail, UserX, Store, BarChart3, MessageSquare } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"

// Mock data for store owners
const storeOwners = [
  {
    id: "so1",
    name: "<PERSON>",
    email: "<EMAIL>",
    store: "Fashion Boutique",
    tenant: "Fashion Hub",
    registrationDate: "2023-03-05",
    status: "Verified",
    storePerformance: {
      orders: 245,
      revenue: 12500,
      products: 78,
      rating: 4.7,
    },
    verificationStatus: "Verified",
    communicationHistory: [
      { date: "2023-05-01", type: "Email", subject: "Monthly Report" },
      { date: "2023-04-15", type: "Support", subject: "Payment Issue" },
    ],
  },
  {
    id: "so2",
    name: "Sarah Brown",
    email: "<EMAIL>",
    store: "Tech Gadgets",
    tenant: "SaaS Corp",
    registrationDate: "2023-03-20",
    status: "Active",
    storePerformance: {
      orders: 189,
      revenue: 9800,
      products: 45,
      rating: 4.5,
    },
    verificationStatus: "Pending",
    communicationHistory: [
      { date: "2023-05-02", type: "Email", subject: "Verification Request" },
      { date: "2023-04-20", type: "Support", subject: "Product Upload Issue" },
    ],
  },
  {
    id: "so3",
    name: "Patricia Thomas",
    email: "<EMAIL>",
    store: "Organic Foods",
    tenant: "Food Delivery",
    registrationDate: "2023-04-10",
    status: "Pending",
    storePerformance: {
      orders: 56,
      revenue: 2300,
      products: 22,
      rating: 4.2,
    },
    verificationStatus: "In Review",
    communicationHistory: [
      { date: "2023-05-05", type: "Email", subject: "Verification Documents" },
      { date: "2023-04-25", type: "Support", subject: "Store Setup Help" },
    ],
  },
  {
    id: "so4",
    name: "Michael Wilson",
    email: "<EMAIL>",
    store: "Vintage Collectibles",
    tenant: "Fashion Hub",
    registrationDate: "2023-03-15",
    status: "Active",
    storePerformance: {
      orders: 112,
      revenue: 5600,
      products: 34,
      rating: 4.3,
    },
    verificationStatus: "Verified",
    communicationHistory: [
      { date: "2023-05-03", type: "Email", subject: "Promotion Opportunity" },
      { date: "2023-04-18", type: "Support", subject: "Shipping Integration" },
    ],
  },
  {
    id: "so5",
    name: "Emily Davis",
    email: "<EMAIL>",
    store: "Handmade Crafts",
    tenant: "Tech Solutions",
    registrationDate: "2023-03-10",
    status: "Active",
    storePerformance: {
      orders: 78,
      revenue: 3200,
      products: 42,
      rating: 4.8,
    },
    verificationStatus: "Verified",
    communicationHistory: [
      { date: "2023-05-04", type: "Email", subject: "Featured Store Opportunity" },
      { date: "2023-04-22", type: "Support", subject: "Custom Order Question" },
    ],
  },
  {
    id: "so6",
    name: "David Miller",
    email: "<EMAIL>",
    store: "Sports Equipment",
    tenant: "Food Delivery",
    registrationDate: "2023-03-25",
    status: "Suspended",
    storePerformance: {
      orders: 34,
      revenue: 1800,
      products: 28,
      rating: 3.5,
    },
    verificationStatus: "Rejected",
    communicationHistory: [
      { date: "2023-05-06", type: "Email", subject: "Account Suspension Notice" },
      { date: "2023-04-28", type: "Support", subject: "Customer Complaint" },
    ],
  },
]

export default function StoreOwners() {
  const [searchTerm, setSearchTerm] = useState("")
  const [tenantFilter, setTenantFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [verificationFilter, setVerificationFilter] = useState("all")
  const [selectedOwner, setSelectedOwner] = useState<(typeof storeOwners)[0] | null>(null)
  const [activeTab, setActiveTab] = useState("overview")

  // Get unique values for filters
  const tenants = [...new Set(storeOwners.map((owner) => owner.tenant))]
  const statuses = [...new Set(storeOwners.map((owner) => owner.status))]
  const verificationStatuses = [...new Set(storeOwners.map((owner) => owner.verificationStatus))]

  // Filter store owners based on search term and filters
  const filteredOwners = storeOwners.filter((owner) => {
    const matchesSearch =
      owner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      owner.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      owner.store.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesTenant = tenantFilter === "all" || owner.tenant === tenantFilter
    const matchesStatus = statusFilter === "all" || owner.status === statusFilter
    const matchesVerification = verificationFilter === "all" || owner.verificationStatus === verificationFilter

    return matchesSearch && matchesTenant && matchesStatus && matchesVerification
  })

  const columns = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "store",
      header: "Store",
    },
    {
      accessorKey: "tenant",
      header: "Tenant",
    },
    {
      accessorKey: "registrationDate",
      header: "Registration Date",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <Badge
            variant={
              status === "Active"
                ? "success"
                : status === "Verified"
                  ? "default"
                  : status === "Pending"
                    ? "warning"
                    : "destructive"
            }
          >
            {status}
          </Badge>
        )
      },
    },
    {
      accessorKey: "verificationStatus",
      header: "Verification",
      cell: ({ row }) => {
        const status = row.getValue("verificationStatus") as string
        return (
          <Badge
            variant={
              status === "Verified"
                ? "success"
                : status === "In Review"
                  ? "warning"
                  : status === "Pending"
                    ? "secondary"
                    : "destructive"
            }
          >
            {status}
          </Badge>
        )
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const owner = row.original as (typeof storeOwners)[0]
        return (
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" title="View Store Owner" onClick={() => setSelectedOwner(owner)}>
              <Eye className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Contact Owner">
              <Mail className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="View Store">
              <Store className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Suspend Owner">
              <UserX className="h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]

  return (
    <div>
      {selectedOwner ? (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle>{selectedOwner.name}</CardTitle>
                <CardDescription>{selectedOwner.email}</CardDescription>
              </div>
              <Button variant="outline" onClick={() => setSelectedOwner(null)}>
                Back to List
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="store">Store Performance</TabsTrigger>
                <TabsTrigger value="verification">Verification</TabsTrigger>
                <TabsTrigger value="communication">Communication</TabsTrigger>
              </TabsList>

              <TabsContent value="overview">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Store Owner Details</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Store:</div>
                        <div className="col-span-2">{selectedOwner.store}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Tenant:</div>
                        <div className="col-span-2">{selectedOwner.tenant}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Registration:</div>
                        <div className="col-span-2">{selectedOwner.registrationDate}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Status:</div>
                        <div className="col-span-2">
                          <Badge
                            variant={
                              selectedOwner.status === "Active"
                                ? "success"
                                : selectedOwner.status === "Verified"
                                  ? "default"
                                  : selectedOwner.status === "Pending"
                                    ? "warning"
                                    : "destructive"
                            }
                          >
                            {selectedOwner.status}
                          </Badge>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Verification:</div>
                        <div className="col-span-2">
                          <Badge
                            variant={
                              selectedOwner.verificationStatus === "Verified"
                                ? "success"
                                : selectedOwner.verificationStatus === "In Review"
                                  ? "warning"
                                  : selectedOwner.verificationStatus === "Pending"
                                    ? "secondary"
                                    : "destructive"
                            }
                          >
                            {selectedOwner.verificationStatus}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium mb-4">Store Performance</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Orders</span>
                          <span className="text-sm font-medium">{selectedOwner.storePerformance.orders}</span>
                        </div>
                        <Progress value={Math.min(selectedOwner.storePerformance.orders / 3, 100)} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Revenue</span>
                          <span className="text-sm font-medium">${selectedOwner.storePerformance.revenue}</span>
                        </div>
                        <Progress value={Math.min(selectedOwner.storePerformance.revenue / 150, 100)} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Products</span>
                          <span className="text-sm font-medium">{selectedOwner.storePerformance.products}</span>
                        </div>
                        <Progress value={Math.min(selectedOwner.storePerformance.products / 1, 100)} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Rating</span>
                          <span className="text-sm font-medium">{selectedOwner.storePerformance.rating}/5.0</span>
                        </div>
                        <Progress value={(selectedOwner.storePerformance.rating / 5) * 100} className="h-2" />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-6 flex justify-end space-x-2">
                  <Button variant="outline">Contact Owner</Button>
                  <Button variant="outline">View Store</Button>
                  <Button variant="destructive">Suspend Owner</Button>
                </div>
              </TabsContent>

              <TabsContent value="store">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Store Performance</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="border rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold">{selectedOwner.storePerformance.orders}</div>
                          <div className="text-sm text-muted-foreground">Total Orders</div>
                        </div>
                        <div className="border rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold">${selectedOwner.storePerformance.revenue}</div>
                          <div className="text-sm text-muted-foreground">Total Revenue</div>
                        </div>
                        <div className="border rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold">{selectedOwner.storePerformance.products}</div>
                          <div className="text-sm text-muted-foreground">Active Products</div>
                        </div>
                        <div className="border rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold">{selectedOwner.storePerformance.rating}/5.0</div>
                          <div className="text-sm text-muted-foreground">Average Rating</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle>Store Analytics</CardTitle>
                    </CardHeader>
                    <CardContent className="flex items-center justify-center h-40">
                      <BarChart3 className="h-24 w-24 text-muted-foreground" />
                      <div className="text-center ml-4">
                        <p className="text-muted-foreground">Detailed analytics available</p>
                        <Button variant="outline" className="mt-2">
                          View Analytics
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="verification">
                <Card>
                  <CardHeader>
                    <CardTitle>Verification Status</CardTitle>
                    <CardDescription>
                      Current status:
                      <Badge
                        className="ml-2"
                        variant={
                          selectedOwner.verificationStatus === "Verified"
                            ? "success"
                            : selectedOwner.verificationStatus === "In Review"
                              ? "warning"
                              : selectedOwner.verificationStatus === "Pending"
                                ? "secondary"
                                : "destructive"
                        }
                      >
                        {selectedOwner.verificationStatus}
                      </Badge>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="border rounded-lg p-4">
                        <h4 className="font-medium mb-2">Identity Verification</h4>
                        <div className="flex items-center">
                          <div className="w-full">
                            <div className="flex justify-between mb-1">
                              <span className="text-sm">Status</span>
                              <Badge
                                variant={selectedOwner.verificationStatus === "Verified" ? "success" : "secondary"}
                              >
                                {selectedOwner.verificationStatus === "Verified" ? "Verified" : "Pending"}
                              </Badge>
                            </div>
                            <Progress
                              value={selectedOwner.verificationStatus === "Verified" ? 100 : 50}
                              className="h-2"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="border rounded-lg p-4">
                        <h4 className="font-medium mb-2">Business Verification</h4>
                        <div className="flex items-center">
                          <div className="w-full">
                            <div className="flex justify-between mb-1">
                              <span className="text-sm">Status</span>
                              <Badge
                                variant={selectedOwner.verificationStatus === "Verified" ? "success" : "secondary"}
                              >
                                {selectedOwner.verificationStatus === "Verified" ? "Verified" : "Pending"}
                              </Badge>
                            </div>
                            <Progress
                              value={selectedOwner.verificationStatus === "Verified" ? 100 : 30}
                              className="h-2"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="border rounded-lg p-4">
                        <h4 className="font-medium mb-2">Address Verification</h4>
                        <div className="flex items-center">
                          <div className="w-full">
                            <div className="flex justify-between mb-1">
                              <span className="text-sm">Status</span>
                              <Badge
                                variant={selectedOwner.verificationStatus === "Verified" ? "success" : "secondary"}
                              >
                                {selectedOwner.verificationStatus === "Verified" ? "Verified" : "Pending"}
                              </Badge>
                            </div>
                            <Progress
                              value={selectedOwner.verificationStatus === "Verified" ? 100 : 70}
                              className="h-2"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-6 flex justify-end space-x-2">
                      <Button variant="outline">Request Documents</Button>
                      <Button variant="default">Approve Verification</Button>
                      <Button variant="destructive">Reject Verification</Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="communication">
                <Card>
                  <CardHeader>
                    <CardTitle>Communication History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {selectedOwner.communicationHistory.map((comm, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex justify-between items-center">
                            <div>
                              <h4 className="font-medium">{comm.subject}</h4>
                              <p className="text-sm text-muted-foreground">
                                {comm.type} • {comm.date}
                              </p>
                            </div>
                            <Badge variant={comm.type === "Email" ? "default" : "secondary"}>{comm.type}</Badge>
                          </div>
                        </div>
                      ))}
                      {selectedOwner.communicationHistory.length === 0 && (
                        <div className="text-center py-8">
                          <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground" />
                          <p className="mt-2 text-muted-foreground">No communication history found</p>
                        </div>
                      )}
                    </div>
                    <div className="mt-6 flex justify-end space-x-2">
                      <Button variant="outline">Send Email</Button>
                      <Button variant="default">Create Support Ticket</Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Store Owners</CardTitle>
            <CardDescription>Manage users who own stores on the platform</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <Input
                  placeholder="Search by name, email or store..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Select value={tenantFilter} onValueChange={setTenantFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by Tenant" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Tenants</SelectItem>
                    {tenants.map((tenant) => (
                      <SelectItem key={tenant} value={tenant}>
                        {tenant}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {statuses.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={verificationFilter} onValueChange={setVerificationFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by Verification" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Verification</SelectItem>
                    {verificationStatuses.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DataTable
              columns={columns}
              data={filteredOwners}
              searchColumn="name"
              searchPlaceholder="Search store owners..."
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
