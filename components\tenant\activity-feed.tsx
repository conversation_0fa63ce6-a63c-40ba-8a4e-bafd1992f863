"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Store,
  Package,
  ShoppingCart,
  Settings,
  CreditCard,
  Users,
  Star,
  MessageSquare,
  TrendingUp,
  AlertCircle,
  ExternalLink,
  MoreHorizontal
} from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface ActivityItem {
  id: string
  user: {
    name: string
    email: string
    avatar?: string
  }
  action: string
  target: string
  description?: string
  type: "success" | "error" | "info" | "warning"
  icon: React.ElementType
  timestamp: string
  href?: string
  metadata?: {
    amount?: number
    rating?: number
    status?: string
  }
}

const activityData: ActivityItem[] = [
  {
    id: "1",
    user: { 
      name: "Toko <PERSON> Kita", 
      email: "<EMAIL>",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=fashion"
    },
    action: "bergabung dengan",
    target: "marketplace",
    description: "Store baru telah disetujui dan aktif",
    type: "success",
    icon: Store,
    timestamp: "Baru saja",
    href: "/tenant/dashboard/stores",
    metadata: { status: "approved" }
  },
  {
    id: "2",
    user: { 
      name: "Toko Elektronik", 
      email: "<EMAIL>",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=elektronik"
    },
    action: "menambahkan produk baru",
    target: "Smartphone XYZ Pro Max",
    description: "Produk dengan harga Rp 15.499.000",
    type: "info",
    icon: Package,
    timestamp: "5 menit yang lalu",
    href: "/tenant/dashboard/products",
    metadata: { amount: 15499000 }
  },
  {
    id: "3",
    user: { 
      name: "Customer Ahmad", 
      email: "<EMAIL>",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=ahmad"
    },
    action: "menerima pesanan",
    target: "#ORD-12345",
    description: "Pesanan senilai Rp 750.000 telah dikonfirmasi",
    type: "success",
    icon: ShoppingCart,
    timestamp: "15 menit yang lalu",
    href: "/tenant/dashboard/orders",
    metadata: { amount: 750000 }
  },
  {
    id: "4",
    user: { 
      name: "Toko Buku", 
      email: "<EMAIL>",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=buku"
    },
    action: "mengubah pengaturan",
    target: "kebijakan pengembalian",
    description: "Kebijakan pengembalian diperbarui",
    type: "info",
    icon: Settings,
    timestamp: "30 menit yang lalu",
    href: "/tenant/dashboard/stores/settings"
  },
  {
    id: "5",
    user: { 
      name: "Customer Sari", 
      email: "<EMAIL>",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=sari"
    },
    action: "memberikan review",
    target: "Produk Skincare Set",
    description: "Rating 5 bintang dengan komentar positif",
    type: "success",
    icon: Star,
    timestamp: "45 menit yang lalu",
    href: "/tenant/dashboard/customers/reviews",
    metadata: { rating: 5 }
  },
  {
    id: "6",
    user: { 
      name: "Toko Olahraga", 
      email: "<EMAIL>",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=olahraga"
    },
    action: "gagal melakukan pembayaran",
    target: "biaya langganan premium",
    description: "Pembayaran Rp 299.000 gagal diproses",
    type: "error",
    icon: CreditCard,
    timestamp: "1 jam yang lalu",
    href: "/tenant/dashboard/financial/transactions",
    metadata: { amount: 299000 }
  },
  {
    id: "7",
    user: { 
      name: "System", 
      email: "<EMAIL>",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=system"
    },
    action: "mendeteksi kenaikan",
    target: "traffic website",
    description: "Traffic naik 25% dibanding minggu lalu",
    type: "success",
    icon: TrendingUp,
    timestamp: "2 jam yang lalu",
    href: "/tenant/dashboard/analytics"
  },
  {
    id: "8",
    user: { 
      name: "Customer Budi", 
      email: "<EMAIL>",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=budi"
    },
    action: "mengirim komplain",
    target: "pesanan #ORD-12340",
    description: "Komplain tentang keterlambatan pengiriman",
    type: "warning",
    icon: MessageSquare,
    timestamp: "3 jam yang lalu",
    href: "/tenant/dashboard/customers"
  }
]

function getActivityStyle(type: ActivityItem["type"]) {
  switch (type) {
    case "success":
      return {
        dotColor: "bg-green-500",
        iconBg: "bg-green-50",
        iconColor: "text-green-600"
      }
    case "error":
      return {
        dotColor: "bg-red-500",
        iconBg: "bg-red-50",
        iconColor: "text-red-600"
      }
    case "warning":
      return {
        dotColor: "bg-yellow-500",
        iconBg: "bg-yellow-50",
        iconColor: "text-yellow-600"
      }
    case "info":
      return {
        dotColor: "bg-blue-500",
        iconBg: "bg-blue-50",
        iconColor: "text-blue-600"
      }
    default:
      return {
        dotColor: "bg-gray-500",
        iconBg: "bg-gray-50",
        iconColor: "text-gray-600"
      }
  }
}

function ActivityItemCard({ activity, isLast }: { activity: ActivityItem; isLast: boolean }) {
  const style = getActivityStyle(activity.type)
  
  return (
    <div className="relative flex gap-3">
      {/* Timeline Line */}
      {!isLast && (
        <div className="absolute left-6 top-8 w-px h-full bg-border" />
      )}
      
      {/* Avatar & Icon */}
      <div className="relative flex-shrink-0">
        <Avatar className="w-12 h-12 border-2 border-background">
          <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
          <AvatarFallback>
            {activity.user.name.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        
        {/* Activity Icon */}
        <div className={cn(
          "absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-2 border-background flex items-center justify-center",
          style.iconBg
        )}>
          <activity.icon className={cn("w-3 h-3", style.iconColor)} />
        </div>
      </div>
      
      {/* Content */}
      <div className="flex-1 min-w-0 pb-4">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1">
            <p className="text-sm text-foreground">
              <span className="font-medium">{activity.user.name}</span>
              {" "}
              <span className="text-muted-foreground">{activity.action}</span>
              {" "}
              <span className="font-medium">{activity.target}</span>
            </p>
            
            {activity.description && (
              <p className="text-xs text-muted-foreground mt-1">
                {activity.description}
              </p>
            )}
            
            {/* Metadata */}
            {activity.metadata && (
              <div className="flex items-center gap-2 mt-2">
                {activity.metadata.amount && (
                  <Badge variant="outline" className="text-xs">
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                      minimumFractionDigits: 0,
                    }).format(activity.metadata.amount)}
                  </Badge>
                )}
                {activity.metadata.rating && (
                  <Badge variant="outline" className="text-xs">
                    ⭐ {activity.metadata.rating}/5
                  </Badge>
                )}
                {activity.metadata.status && (
                  <Badge 
                    variant={activity.metadata.status === "approved" ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {activity.metadata.status}
                  </Badge>
                )}
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">
              {activity.timestamp}
            </span>
            <div className={cn("w-2 h-2 rounded-full", style.dotColor)} />
          </div>
        </div>
        
        {activity.href && (
          <div className="mt-2">
            <Button variant="ghost" size="sm" className="h-8 px-2 text-xs" asChild>
              <Link href={activity.href}>
                Lihat Detail
                <ExternalLink className="ml-1 h-3 w-3" />
              </Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

export function ActivityFeed() {
  const recentActivities = activityData.slice(0, 6)
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Aktivitas Terbaru</CardTitle>
            <CardDescription>
              Aktivitas terbaru di marketplace Anda
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" asChild>
            <Link href="/tenant/dashboard/activities">
              Lihat Semua
              <ExternalLink className="ml-1 h-3 w-3" />
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {recentActivities.length === 0 ? (
          <div className="text-center py-8">
            <div className="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="h-6 w-6 text-gray-500" />
            </div>
            <h3 className="text-sm font-medium text-foreground mb-1">
              Belum Ada Aktivitas
            </h3>
            <p className="text-xs text-muted-foreground">
              Aktivitas akan muncul di sini setelah ada interaksi
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {recentActivities.map((activity, index) => (
              <ActivityItemCard 
                key={activity.id} 
                activity={activity} 
                isLast={index === recentActivities.length - 1}
              />
            ))}
            
            {activityData.length > 6 && (
              <div className="pt-4 border-t">
                <Button variant="ghost" className="w-full" asChild>
                  <Link href="/tenant/dashboard/activities">
                    Lihat {activityData.length - 6} aktivitas lainnya
                    <MoreHorizontal className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
} 