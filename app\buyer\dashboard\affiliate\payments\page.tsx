import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { AffiliatePaymentDashboard } from "@/components/buyer/affiliate/affiliate-payment-dashboard"
import { AffiliatePaymentHistory } from "@/components/buyer/affiliate/affiliate-payment-history"
import { AffiliatePaymentMethods } from "@/components/buyer/affiliate/affiliate-payment-methods"
import { AffiliateTaxDocuments } from "@/components/buyer/affiliate/affiliate-tax-documents"

export default function AffiliatePaymentsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Payments</h1>
        <p className="text-muted-foreground">Manage your earnings, payment methods, and tax documents</p>
      </div>

      <Tabs defaultValue="dashboard" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="methods">Payment Methods</TabsTrigger>
          <TabsTrigger value="tax">Tax Documents</TabsTrigger>
        </TabsList>
        <TabsContent value="dashboard">
          <AffiliatePaymentDashboard />
        </TabsContent>
        <TabsContent value="history">
          <AffiliatePaymentHistory />
        </TabsContent>
        <TabsContent value="methods">
          <AffiliatePaymentMethods />
        </TabsContent>
        <TabsContent value="tax">
          <AffiliateTaxDocuments />
        </TabsContent>
      </Tabs>
    </div>
  )
}
