import { NextRequest, NextResponse } from 'next/server';
import { productService } from '@/lib/services/products';

// GET - Mendapatkan statistik products
export async function GET(request: NextRequest) {
  try {
    const stats = await productService.getProductStats();
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching product stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product stats' },
      { status: 500 }
    );
  }
}
