"use client"

import { useState, useEffect } from "react"
import { useR<PERSON>er, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { formatCurrency } from "@/lib/utils"
import { paymentAPI } from "@/lib/api/payment"
import type { Payment } from "@/lib/models/payment"
import { AlertCircle, CheckCircle, Clock, XCircle, Loader2, RefreshCw } from "lucide-react"

export function PaymentStatus() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const paymentId = searchParams.get("paymentId")

  const [payment, setPayment] = useState<Payment | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    if (!paymentId) {
      setError("ID Pembayaran tidak ditemukan")
      setLoading(false)
      return
    }

    const fetchPayment = async () => {
      try {
        const paymentData = await paymentAPI.getPayment(paymentId)
        setPayment(paymentData)
        setLoading(false)
      } catch (error) {
        console.error("Error fetching payment:", error)
        setError("Gagal memuat data pembayaran")
        setLoading(false)
      }
    }

    fetchPayment()
  }, [paymentId])

  const handleRefresh = async () => {
    if (!paymentId) return

    setRefreshing(true)
    try {
      const paymentData = await paymentAPI.checkPaymentStatus(paymentId)
      setPayment(paymentData)
    } catch (error) {
      console.error("Error checking payment status:", error)
      setError("Gagal memeriksa status pembayaran")
    } finally {
      setRefreshing(false)
    }
  }

  const handleCancel = async () => {
    if (!paymentId || !payment || payment.status !== "pending") return

    setRefreshing(true)
    try {
      const paymentData = await paymentAPI.cancelPayment(paymentId)
      setPayment(paymentData)
    } catch (error) {
      console.error("Error canceling payment:", error)
      setError("Gagal membatalkan pembayaran")
    } finally {
      setRefreshing(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="container max-w-md mx-auto py-8 px-4">
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>Terjadi kesalahan</CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Button className="w-full" onClick={() => router.push("/")}>
              Kembali ke Beranda
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  if (!payment) {
    return (
      <div className="container max-w-md mx-auto py-8 px-4">
        <Card>
          <CardHeader>
            <CardTitle>Pembayaran Tidak Ditemukan</CardTitle>
            <CardDescription>Data pembayaran tidak ditemukan</CardDescription>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Informasi</AlertTitle>
              <AlertDescription>
                Pembayaran dengan ID {paymentId} tidak ditemukan. Silakan periksa kembali ID pembayaran Anda.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Button className="w-full" onClick={() => router.push("/")}>
              Kembali ke Beranda
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="container max-w-md mx-auto py-8 px-4">
      <Card>
        <CardHeader>
          <CardTitle>Status Pembayaran</CardTitle>
          <CardDescription>Order ID: {payment.orderId}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex justify-center">
            {payment.status === "completed" && (
              <div className="flex flex-col items-center text-center">
                <CheckCircle className="h-16 w-16 text-green-500 mb-2" />
                <h3 className="text-xl font-bold">Pembayaran Berhasil</h3>
                <p className="text-muted-foreground">Pembayaran Anda telah berhasil diverifikasi</p>
              </div>
            )}

            {payment.status === "pending" && (
              <div className="flex flex-col items-center text-center">
                <Clock className="h-16 w-16 text-yellow-500 mb-2" />
                <h3 className="text-xl font-bold">Menunggu Pembayaran</h3>
                <p className="text-muted-foreground">Silakan selesaikan pembayaran Anda</p>
              </div>
            )}

            {payment.status === "processing" && (
              <div className="flex flex-col items-center text-center">
                <Loader2 className="h-16 w-16 text-blue-500 mb-2 animate-spin" />
                <h3 className="text-xl font-bold">Pembayaran Diproses</h3>
                <p className="text-muted-foreground">Pembayaran Anda sedang diproses</p>
              </div>
            )}

            {(payment.status === "failed" || payment.status === "expired") && (
              <div className="flex flex-col items-center text-center">
                <XCircle className="h-16 w-16 text-red-500 mb-2" />
                <h3 className="text-xl font-bold">
                  {payment.status === "failed" ? "Pembayaran Gagal" : "Pembayaran Kedaluwarsa"}
                </h3>
                <p className="text-muted-foreground">
                  {payment.status === "failed"
                    ? "Pembayaran Anda gagal diproses"
                    : "Batas waktu pembayaran telah berakhir"}
                </p>
              </div>
            )}
          </div>

          <div className="space-y-2 p-4 bg-muted rounded-md">
            <div className="flex justify-between">
              <span className="font-medium">Metode Pembayaran</span>
              <span>{payment.paymentMethod.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Jumlah</span>
              <span>{formatCurrency(payment.amount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Waktu</span>
              <span>{new Date(payment.createdAt).toLocaleString()}</span>
            </div>
            {payment.expiryTime && payment.status === "pending" && (
              <div className="flex justify-between">
                <span className="font-medium">Batas Waktu</span>
                <span>{new Date(payment.expiryTime).toLocaleString()}</span>
              </div>
            )}
          </div>

          {payment.status === "pending" && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Informasi</AlertTitle>
              <AlertDescription>
                Silakan selesaikan pembayaran Anda sesuai instruksi yang telah diberikan. Status akan diperbarui secara
                otomatis setelah pembayaran berhasil.
              </AlertDescription>
            </Alert>
          )}

          {payment.status === "completed" && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertTitle>Sukses</AlertTitle>
              <AlertDescription>
                Terima kasih atas pembayaran Anda. Pesanan Anda sedang diproses dan akan segera dikirim.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          {payment.status === "pending" && (
            <div className="flex w-full space-x-2">
              <Button className="flex-1" variant="outline" onClick={handleRefresh} disabled={refreshing}>
                {refreshing ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh Status
              </Button>
              <Button className="flex-1" variant="outline" onClick={handleCancel} disabled={refreshing}>
                Batalkan
              </Button>
            </div>
          )}
          <Button
            className="w-full"
            variant={payment.status === "completed" ? "default" : "outline"}
            onClick={() => router.push("/")}
          >
            {payment.status === "completed" ? "Lihat Pesanan Saya" : "Kembali ke Beranda"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
