import { Card, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Download, Eye, Upload } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export function AffiliateTaxDocuments() {
  // Sample tax documents data
  const taxDocuments = [
    {
      id: "TAX-2023",
      year: "2023",
      type: "Form 1099-MISC",
      status: "pending",
      dateIssued: "Not issued yet",
      dateSubmitted: "N/A",
    },
    {
      id: "TAX-2022",
      year: "2022",
      type: "Form 1099-MISC",
      status: "completed",
      dateIssued: "Jan 31, 2023",
      dateSubmitted: "Feb 15, 2023",
    },
    {
      id: "TAX-2021",
      year: "2021",
      type: "Form 1099-MISC",
      status: "completed",
      dateIssued: "Jan 31, 2022",
      dateSubmitted: "Feb 10, 2022",
    },
    {
      id: "W9-2023",
      year: "2023",
      type: "Form W-9",
      status: "completed",
      dateIssued: "N/A",
      dateSubmitted: "Jan 05, 2023",
    },
  ]

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Tax Documents</CardTitle>
        <CardDescription>View and download your tax forms and documents</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-md">
          <h3 className="text-sm font-medium text-amber-800">Tax Information</h3>
          <p className="text-sm text-amber-700 mt-1">
            Please ensure your tax information is up to date. You can update your W-9 or tax details at any time.
          </p>
          <Button variant="outline" size="sm" className="mt-2">
            <Upload className="mr-2 h-4 w-4" />
            Update Tax Information
          </Button>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Year</TableHead>
              <TableHead>Document Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date Issued</TableHead>
              <TableHead>Date Submitted</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {taxDocuments.map((doc) => (
              <TableRow key={doc.id}>
                <TableCell>{doc.year}</TableCell>
                <TableCell>{doc.type}</TableCell>
                <TableCell>
                  <Badge variant={doc.status === "completed" ? "success" : "outline"}>
                    {doc.status === "completed" ? "Completed" : "Pending"}
                  </Badge>
                </TableCell>
                <TableCell>{doc.dateIssued}</TableCell>
                <TableCell>{doc.dateSubmitted}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button variant="ghost" size="icon" disabled={doc.status !== "completed"}>
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" disabled={doc.status !== "completed"}>
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
