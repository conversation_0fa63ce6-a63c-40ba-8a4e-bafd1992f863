// Debug script untuk tenant
window.tenantDebug = {
  // Lihat tenant di localStorage
  viewTenants: () => {
    try {
      const tenants = localStorage.getItem("tenants")
      if (tenants) {
        console.log("Tenants in localStorage:", JSON.parse(tenants))
        return JSON.parse(tenants)
      } else {
        console.log("No tenants found in localStorage")
        return null
      }
    } catch (error) {
      console.error("Error viewing tenants:", error)
      return null
    }
  },

  // Tambahkan tenant dummy
  addDummyTenant: () => {
    try {
      const tenants = localStorage.getItem("tenants")
      let tenantsArray = []

      if (tenants) {
        tenantsArray = JSON.parse(tenants)
      }

      const newId = tenantsArray.length > 0 ? Math.max(...tenantsArray.map((t) => Number.parseInt(t.id))) + 1 : 1

      const dummyTenant = {
        id: newId.toString(),
        name: `Dummy Tenant ${newId}`,
        domain: `dummy${newId}.sellzio.com`,
        plan: ["Basic", "Professional", "Enterprise"][Math.floor(Math.random() * 3)],
        status: ["active", "suspended", "pending"][Math.floor(Math.random() * 3)],
        storeCount: Math.floor(Math.random() * 10),
        userCount: Math.floor(Math.random() * 20),
        revenue: `$${Math.floor(Math.random() * 5000)}`,
        createdAt: new Date().toISOString().split("T")[0],
      }

      tenantsArray.push(dummyTenant)
      localStorage.setItem("tenants", JSON.stringify(tenantsArray))

      console.log("Dummy tenant added:", dummyTenant)
      return dummyTenant
    } catch (error) {
      console.error("Error adding dummy tenant:", error)
      return null
    }
  },

  // Hapus tenant berdasarkan ID
  deleteTenant: (id) => {
    try {
      const tenants = localStorage.getItem("tenants")
      if (!tenants) {
        console.log("No tenants found in localStorage")
        return false
      }

      const tenantsArray = JSON.parse(tenants)
      const filteredTenants = tenantsArray.filter((t) => t.id !== id.toString())

      if (filteredTenants.length === tenantsArray.length) {
        console.log(`Tenant with ID ${id} not found`)
        return false
      }

      localStorage.setItem("tenants", JSON.stringify(filteredTenants))
      console.log(`Tenant with ID ${id} deleted`)
      return true
    } catch (error) {
      console.error("Error deleting tenant:", error)
      return false
    }
  },

  // Hapus semua tenant
  clearTenants: () => {
    try {
      localStorage.removeItem("tenants")
      console.log("All tenants cleared from localStorage")
      return true
    } catch (error) {
      console.error("Error clearing tenants:", error)
      return false
    }
  },

  // Reset tenant ke default
  resetTenants: () => {
    try {
      // Import default tenants
      const defaultTenants = [
        {
          id: "1",
          name: "Acme Corporation",
          domain: "acme.sellzio.com",
          plan: "Enterprise",
          status: "active",
          storeCount: 12,
          userCount: 45,
          revenue: "$5,240",
          createdAt: "2023-01-15",
        },
        {
          id: "2",
          name: "TechStart Inc",
          domain: "techstart.sellzio.com",
          plan: "Professional",
          status: "active",
          storeCount: 5,
          userCount: 18,
          revenue: "$1,890",
          createdAt: "2023-02-22",
        },
        {
          id: "3",
          name: "Global Retail",
          domain: "globalretail.sellzio.com",
          plan: "Enterprise",
          status: "active",
          storeCount: 28,
          userCount: 120,
          revenue: "$12,450",
          createdAt: "2022-11-05",
        },
        {
          id: "4",
          name: "Fashion Forward",
          domain: "fashionforward.sellzio.com",
          plan: "Basic",
          status: "suspended",
          storeCount: 1,
          userCount: 3,
          revenue: "$240",
          createdAt: "2023-03-10",
        },
        {
          id: "5",
          name: "Digital Solutions",
          domain: "digitalsolutions.sellzio.com",
          plan: "Professional",
          status: "active",
          storeCount: 7,
          userCount: 22,
          revenue: "$3,120",
          createdAt: "2023-01-30",
        },
        {
          id: "6",
          name: "Organic Foods",
          domain: "organicfoods.sellzio.com",
          plan: "Basic",
          status: "pending",
          storeCount: 0,
          userCount: 2,
          revenue: "$0",
          createdAt: "2023-04-05",
        },
        {
          id: "7",
          name: "Tech Gadgets",
          domain: "techgadgets.sellzio.com",
          plan: "Professional",
          status: "active",
          storeCount: 4,
          userCount: 9,
          revenue: "$1,540",
          createdAt: "2023-02-15",
        },
      ]

      localStorage.setItem("tenants", JSON.stringify(defaultTenants))
      console.log("Tenants reset to default")
      return defaultTenants
    } catch (error) {
      console.error("Error resetting tenants:", error)
      return null
    }
  },

  // Cek status localStorage
  checkLocalStorage: () => {
    try {
      const testKey = "test_storage"
      localStorage.setItem(testKey, "test")
      localStorage.removeItem(testKey)
      console.log("localStorage is working properly")
      return true
    } catch (error) {
      console.error("localStorage is not available:", error)
      return false
    }
  },
}

// Log pesan saat script dimuat
console.log("Tenant debug tools loaded. Use window.tenantDebug to access the tools.")
