"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  DollarSign,
  Users,
  CreditCard,
  Calendar,
  Eye,
  Download,
  Filter,
  CheckCircle2,
  Clock,
  XCircle,
  MoreHorizontal,
  FileText
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk komisi afiliasi
const commissionData = [
  {
    id: "com-001",
    affiliateName: "Budi Santoso",
    affiliateId: "aff-001",
    amount: 1250000,
    salesCount: 8,
    totalSales: ********,
    dateEarned: "2024-01-01T00:00:00",
    paymentDate: "2024-01-15T10:30:00",
    status: "paid",
    paymentMethod: "Bank Transfer",
    referenceNumber: "TRX-1234567",
    invoice: "INV-001-2024"
  },
  {
    id: "com-002",
    affiliateName: "<PERSON><PERSON>ni",
    affiliateId: "aff-002",
    amount: 850000,
    salesCount: 5,
    totalSales: 5666667,
    dateEarned: "2024-01-02T00:00:00",
    paymentDate: null,
    status: "pending",
    paymentMethod: null,
    referenceNumber: null,
    invoice: "INV-002-2024"
  },
  {
    id: "com-003",
    affiliateName: "Ahmad Rizki",
    affiliateId: "aff-003",
    amount: 375000,
    salesCount: 3,
    totalSales: 3750000,
    dateEarned: "2024-01-03T00:00:00",
    paymentDate: null,
    status: "processing",
    paymentMethod: "Bank Transfer",
    referenceNumber: null,
    invoice: "INV-003-2024"
  },
  {
    id: "com-004",
    affiliateName: "Maya Putri",
    affiliateId: "aff-004",
    amount: 2100000,
    salesCount: 12,
    totalSales: ********,
    dateEarned: "2023-12-01T00:00:00",
    paymentDate: "2023-12-15T11:45:00",
    status: "paid",
    paymentMethod: "PayPal",
    referenceNumber: "PP-9876543",
    invoice: "INV-004-2023"
  },
  {
    id: "com-005",
    affiliateName: "Deni Hermawan",
    affiliateId: "aff-005",
    amount: 550000,
    salesCount: 4,
    totalSales: 5500000,
    dateEarned: "2023-12-15T00:00:00",
    paymentDate: null,
    status: "rejected",
    paymentMethod: null,
    referenceNumber: null,
    invoice: "INV-005-2023",
    rejectionReason: "Invalid bank account information"
  }
]

// Fungsi untuk badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "paid":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Paid</Badge>
    case "pending": 
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>
    case "processing":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">Processing</Badge>
    case "rejected":
      return <Badge variant="outline" className="bg-red-100 text-red-800">Rejected</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Fungsi untuk format tanggal
function formatDate(dateString: string | null) {
  if (!dateString) return "-"
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric'
  })
}

export default function AffiliateCommissionPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Filter commission data
  const filteredCommissions = commissionData.filter(commission => {
    const matchesSearch = commission.affiliateName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          commission.affiliateId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          (commission.invoice && commission.invoice.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesStatus = statusFilter === "all" || commission.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Statistik
  const stats = {
    totalCommission: commissionData.reduce((sum, c) => sum + c.amount, 0),
    paidCommission: commissionData.filter(c => c.status === "paid").reduce((sum, c) => sum + c.amount, 0),
    pendingCommission: commissionData.filter(c => c.status === "pending" || c.status === "processing").reduce((sum, c) => sum + c.amount, 0),
    totalSales: commissionData.reduce((sum, c) => sum + c.totalSales, 0),
    totalAffiliates: new Set(commissionData.map(c => c.affiliateId)).size
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/affiliates">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Affiliate Commissions</h1>
            <p className="text-muted-foreground">
              Kelola komisi, pembayaran, dan invoice afiliasi
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            Laporan
          </Button>
          <Button>
            <DollarSign className="h-4 w-4 mr-2" />
            Proses Pembayaran
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Commission</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalCommission)}</div>
            <p className="text-xs text-muted-foreground">
              Dari {stats.totalAffiliates} afiliasi
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid Commission</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.paidCommission)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Commission</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{formatCurrency(stats.pendingCommission)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales Generated</CardTitle>
            <CreditCard className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatCurrency(stats.totalSales)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari nama afiliasi, ID, atau invoice..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="paid">Paid</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Commission Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="h-12 px-4 text-left align-middle text-sm font-medium">Affiliate</th>
                  <th className="h-12 px-4 text-left align-middle text-sm font-medium">Amount</th>
                  <th className="h-12 px-4 text-left align-middle text-sm font-medium">Sales</th>
                  <th className="h-12 px-4 text-left align-middle text-sm font-medium">Date Earned</th>
                  <th className="h-12 px-4 text-left align-middle text-sm font-medium">Payment Date</th>
                  <th className="h-12 px-4 text-left align-middle text-sm font-medium">Status</th>
                  <th className="h-12 px-4 text-left align-middle text-sm font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredCommissions.map((commission) => (
                  <tr key={commission.id} className="border-b hover:bg-muted/50">
                    <td className="px-4 py-3">
                      <div className="font-medium">{commission.affiliateName}</div>
                      <div className="text-xs text-muted-foreground">{commission.affiliateId}</div>
                    </td>
                    <td className="px-4 py-3 font-medium">
                      {formatCurrency(commission.amount)}
                    </td>
                    <td className="px-4 py-3">
                      <div>{commission.salesCount} orders</div>
                      <div className="text-xs text-muted-foreground">{formatCurrency(commission.totalSales)}</div>
                    </td>
                    <td className="px-4 py-3">
                      {formatDate(commission.dateEarned)}
                    </td>
                    <td className="px-4 py-3">
                      {formatDate(commission.paymentDate)}
                    </td>
                    <td className="px-4 py-3">
                      {getStatusBadge(commission.status)}
                      {commission.status === "paid" && commission.paymentMethod && (
                        <div className="text-xs text-muted-foreground mt-1">{commission.paymentMethod}</div>
                      )}
                      {commission.status === "rejected" && commission.rejectionReason && (
                        <div className="text-xs text-red-600 mt-1">{commission.rejectionReason}</div>
                      )}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="h-4 w-4" />
                        </Button>
                        {commission.status === "pending" && (
                          <Button size="sm" variant="outline" className="text-green-600 hover:text-green-700">
                            <CreditCard className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
                {filteredCommissions.length === 0 && (
                  <tr>
                    <td colSpan={7} className="p-8 text-center">
                      <div className="mx-auto max-w-md">
                        <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">Tidak ada data komisi ditemukan</h3>
                        <p className="text-muted-foreground mb-4">
                          Tidak ada data yang cocok dengan filter Anda
                        </p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 