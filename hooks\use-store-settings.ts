import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface StoreSetting {
  id: string;
  setting_key: string;
  setting_name: string;
  setting_value: string | null;
  setting_type: 'text' | 'textarea' | 'number' | 'boolean' | 'select' | 'file' | 'email' | 'tel' | 'json';
  category: string;
  description?: string;
  options?: Array<{ value: string; label: string }> | null;
  validation_rules: {
    required?: boolean;
    min?: number;
    max?: number;
    min_length?: number;
    max_length?: number;
    pattern?: string;
    email?: boolean;
    file_types?: string[];
    max_size?: number;
    step?: number;
    [key: string]: any;
  };
  is_public: boolean;
  is_required: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface StoreSettingFilters {
  category?: string;
  is_public?: boolean;
  search?: string;
}

export interface StoreSettingCreate {
  setting_key: string;
  setting_name: string;
  setting_value?: string;
  setting_type: StoreSetting['setting_type'];
  category: string;
  description?: string;
  options?: Array<{ value: string; label: string }>;
  validation_rules?: object;
  is_public?: boolean;
  is_required?: boolean;
  sort_order?: number;
}

export function useStoreSettings() {
  const [settings, setSettings] = useState<StoreSetting[]>([]);
  const [settingsByCategory, setSettingsByCategory] = useState<Record<string, StoreSetting[]>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch settings with optional filters
  const fetchSettings = useCallback(async (filters?: StoreSettingFilters, grouped = false) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      
      if (filters?.category) {
        params.append('category', filters.category);
      }
      
      if (filters?.is_public !== undefined) {
        params.append('is_public', filters.is_public.toString());
      }
      
      if (filters?.search) {
        params.append('search', filters.search);
      }
      
      if (grouped) {
        params.append('grouped', 'true');
      }
      
      const response = await fetch(`/api/store-settings?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }
      
      const data = await response.json();
      
      if (grouped) {
        setSettingsByCategory(data);
      } else {
        setSettings(data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Get single setting by key
  const getSetting = useCallback(async (key: string): Promise<StoreSetting | null> => {
    try {
      const response = await fetch(`/api/store-settings/key/${key}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch setting');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Get single setting by ID
  const getSettingById = useCallback(async (id: string): Promise<StoreSetting | null> => {
    try {
      const response = await fetch(`/api/store-settings/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch setting');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Create setting
  const createSetting = useCallback(async (settingData: StoreSettingCreate): Promise<boolean> => {
    try {
      const response = await fetch('/api/store-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create setting');
      }
      
      const newSetting = await response.json();
      
      // Update local state
      setSettings(prev => [...prev, newSetting].sort((a, b) => {
        if (a.category !== b.category) {
          return a.category.localeCompare(b.category);
        }
        return a.sort_order - b.sort_order;
      }));
      
      toast.success('Setting berhasil dibuat');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Update setting by ID
  const updateSetting = useCallback(async (id: string, updates: Partial<StoreSetting>): Promise<boolean> => {
    try {
      const response = await fetch(`/api/store-settings/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update setting');
      }
      
      const updatedSetting = await response.json();
      
      // Update local state
      setSettings(prev => 
        prev.map(setting => setting.id === id ? updatedSetting : setting)
      );
      
      toast.success('Setting berhasil diperbarui');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Update setting value by key
  const updateSettingValue = useCallback(async (key: string, value: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/store-settings/key/${key}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ setting_value: value }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update setting');
      }
      
      const updatedSetting = await response.json();
      
      // Update local state
      setSettings(prev => 
        prev.map(setting => setting.setting_key === key ? updatedSetting : setting)
      );
      
      toast.success('Setting berhasil diperbarui');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Update multiple settings at once
  const updateMultipleSettings = useCallback(async (settingsData: Record<string, string>): Promise<boolean> => {
    try {
      const response = await fetch('/api/store-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings: settingsData }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update settings');
      }
      
      const result = await response.json();
      
      // Update local state
      setSettings(prev => 
        prev.map(setting => {
          const newValue = settingsData[setting.setting_key];
          return newValue !== undefined ? { ...setting, setting_value: newValue } : setting;
        })
      );
      
      toast.success('Settings berhasil diperbarui');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Delete setting
  const deleteSetting = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/store-settings/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete setting');
      }
      
      // Update local state
      setSettings(prev => prev.filter(setting => setting.id !== id));
      
      toast.success('Setting berhasil dihapus');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Refresh settings (re-fetch with current filters)
  const refreshSettings = useCallback(async () => {
    await fetchSettings();
  }, [fetchSettings]);

  // Get settings grouped by category
  const fetchSettingsByCategory = useCallback(async (filters?: StoreSettingFilters) => {
    await fetchSettings(filters, true);
  }, [fetchSettings]);

  // Initial fetch on mount
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  return {
    settings,
    settingsByCategory,
    loading,
    error,
    fetchSettings,
    fetchSettingsByCategory,
    getSetting,
    getSettingById,
    createSetting,
    updateSetting,
    updateSettingValue,
    updateMultipleSettings,
    deleteSetting,
    refreshSettings,
  };
}
