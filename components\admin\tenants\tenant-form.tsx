"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Define form schema with Zod
const tenantFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  domain: z.string().min(3, "Domain must be at least 3 characters"),
  plan: z.string().min(1, "Please select a plan"),
  description: z.string().optional(),
})

type TenantFormValues = z.infer<typeof tenantFormSchema>

interface TenantFormProps {
  onSubmit: (data: TenantFormValues) => void
  initialData?: Partial<TenantFormValues>
  isSubmitting?: boolean
}

export function TenantForm({ onSubmit, initialData, isSubmitting = false }: TenantFormProps) {
  // Initialize form with react-hook-form
  const form = useForm<TenantFormValues>({
    resolver: zodResolver(tenantFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      domain: initialData?.domain || "",
      plan: initialData?.plan || "",
      description: initialData?.description || "",
    },
  })

  // Handle form submission
  const handleSubmit = async (data: TenantFormValues) => {
    await onSubmit(data)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tenant Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter tenant name" {...field} />
              </FormControl>
              <FormDescription>The name of the tenant as it will appear in the platform.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="domain"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Domain</FormLabel>
              <FormControl>
                <div className="flex items-center">
                  <Input placeholder="example" {...field} />
                  <span className="ml-2 text-muted-foreground">.sellzio.com</span>
                </div>
              </FormControl>
              <FormDescription>The subdomain for the tenant (e.g., example.sellzio.com).</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="plan"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Subscription Plan</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a plan" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="free">Free</SelectItem>
                  <SelectItem value="starter">Starter</SelectItem>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="enterprise">Enterprise</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>The subscription plan for this tenant.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter a description for this tenant" className="resize-none" {...field} />
              </FormControl>
              <FormDescription>A brief description of the tenant and its purpose.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Tenant"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
