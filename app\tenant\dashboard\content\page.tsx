"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  FileText,
  FileEdit,
  LayoutDashboard,
  Plus,
  Settings,
  Eye,
  PencilIcon,
  MessageSquare,
  ImageIcon
} from "lucide-react"
import Link from "next/link"

export default function ContentDashboardPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Konten</h1>
        <p className="text-muted-foreground">
          <PERSON><PERSON><PERSON> semua konten website, blog, dan halaman
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-start justify-between space-y-0">
            <div>
              <CardTitle className="text-xl"><PERSON><PERSON></CardTitle>
              <CardDescription>
                Ke<PERSON>la halaman statis website
              </CardDescription>
            </div>
            <div className="bg-blue-100 text-blue-800 p-3 rounded-full">
              <FileText className="h-6 w-6" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Total Halaman</span>
                <span className="font-medium">8</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Terakhir Diperbarui</span>
                <span className="font-medium">3 jam yang lalu</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex gap-2 justify-between">
            <Button variant="outline" size="sm" asChild>
              <Link href="/tenant/dashboard/content/pages">
                <Eye className="h-4 w-4 mr-2" />
                Lihat Semua
              </Link>
            </Button>
            <Button size="sm" asChild>
              <Link href="/tenant/dashboard/content/pages/new">
                <Plus className="h-4 w-4 mr-2" />
                Halaman Baru
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-start justify-between space-y-0">
            <div>
              <CardTitle className="text-xl">Blog</CardTitle>
              <CardDescription>
                Kelola artikel blog dan kategori
              </CardDescription>
            </div>
            <div className="bg-purple-100 text-purple-800 p-3 rounded-full">
              <FileEdit className="h-6 w-6" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Total Artikel</span>
                <span className="font-medium">6</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Komentar</span>
                <span className="font-medium">82</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex gap-2 justify-between">
            <Button variant="outline" size="sm" asChild>
              <Link href="/tenant/dashboard/content/blog">
                <Eye className="h-4 w-4 mr-2" />
                Lihat Semua
              </Link>
            </Button>
            <Button size="sm" asChild>
              <Link href="/tenant/dashboard/content/blog/new">
                <Plus className="h-4 w-4 mr-2" />
                Tulis Artikel
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-start justify-between space-y-0">
            <div>
              <CardTitle className="text-xl">Media</CardTitle>
              <CardDescription>
                Kelola gambar, video, dan file
              </CardDescription>
            </div>
            <div className="bg-green-100 text-green-800 p-3 rounded-full">
              <ImageIcon className="h-6 w-6" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Total Media</span>
                <span className="font-medium">124</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Penyimpanan</span>
                <span className="font-medium">512 MB / 2 GB</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex gap-2 justify-between">
            <Button variant="outline" size="sm" asChild>
              <Link href="/tenant/dashboard/content/media">
                <Eye className="h-4 w-4 mr-2" />
                Lihat Semua
              </Link>
            </Button>
            <Button size="sm" asChild>
              <Link href="/tenant/dashboard/content/media/upload">
                <Plus className="h-4 w-4 mr-2" />
                Upload Media
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      <h2 className="text-xl font-semibold mt-8 mb-4">Aktivitas Terbaru</h2>
      
      <div className="space-y-4">
        <Card>
          <CardHeader className="p-4">
            <div className="flex items-center gap-4">
              <div className="bg-blue-100 text-blue-800 p-2 rounded-full">
                <FileText className="h-4 w-4" />
              </div>
              <div>
                <h3 className="font-medium">Halaman "Kebijakan Privasi" diperbarui</h3>
                <p className="text-sm text-muted-foreground">Oleh Admin, 3 jam yang lalu</p>
              </div>
            </div>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader className="p-4">
            <div className="flex items-center gap-4">
              <div className="bg-purple-100 text-purple-800 p-2 rounded-full">
                <MessageSquare className="h-4 w-4" />
              </div>
              <div>
                <h3 className="font-medium">5 komentar baru pada artikel "10 Tips Memilih Produk Berkualitas"</h3>
                <p className="text-sm text-muted-foreground">Belum dimoderasi, 5 jam yang lalu</p>
              </div>
            </div>
          </CardHeader>
        </Card>

        <Card>
          <CardHeader className="p-4">
            <div className="flex items-center gap-4">
              <div className="bg-green-100 text-green-800 p-2 rounded-full">
                <ImageIcon className="h-4 w-4" />
              </div>
              <div>
                <h3 className="font-medium">12 gambar baru diunggah</h3>
                <p className="text-sm text-muted-foreground">Oleh Marketing, 1 hari yang lalu</p>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      <div className="mt-8">
        <Button variant="outline" asChild>
          <Link href="/tenant/dashboard/content/settings">
            <Settings className="h-4 w-4 mr-2" />
            Pengaturan Konten
          </Link>
        </Button>
      </div>
    </div>
  )
} 