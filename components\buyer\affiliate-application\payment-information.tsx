"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface PaymentInformationProps {
  data: {
    method: string
    bankAccount: {
      name: string
      number: string
      bank: string
    }
    digitalWallet: {
      type: string
      email: string
    }
    taxInfo: {
      taxId: string
      taxName: string
    }
    threshold: string
    frequency: string
  }
  onUpdate: (data: any) => void
}

export function PaymentInformation({ data, onUpdate }: PaymentInformationProps) {
  const [formData, setFormData] = useState(data)

  const handleMethodChange = (value: string) => {
    setFormData((prev) => {
      const newData = { ...prev, method: value }
      onUpdate(newData)
      return newData
    })
  }

  const handleBankAccountChange = (field: string, value: string) => {
    setFormData((prev) => {
      const bankAccount = { ...prev.bankAccount, [field]: value }
      const newData = { ...prev, bankAccount }
      onUpdate(newData)
      return newData
    })
  }

  const handleDigitalWalletChange = (field: string, value: string) => {
    setFormData((prev) => {
      const digitalWallet = { ...prev.digitalWallet, [field]: value }
      const newData = { ...prev, digitalWallet }
      onUpdate(newData)
      return newData
    })
  }

  const handleTaxInfoChange = (field: string, value: string) => {
    setFormData((prev) => {
      const taxInfo = { ...prev.taxInfo, [field]: value }
      const newData = { ...prev, taxInfo }
      onUpdate(newData)
      return newData
    })
  }

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => {
      const newData = { ...prev, [field]: value }
      onUpdate(newData)
      return newData
    })
  }

  return (
    <div className="space-y-6">
      {/* Payment Method */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Payment Method</h3>
        <p className="text-xs text-muted-foreground">
          Select your preferred payment method for receiving affiliate commissions.
        </p>
        <RadioGroup value={formData.method} onValueChange={handleMethodChange} className="space-y-3">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="bank" id="method-bank" />
            <Label htmlFor="method-bank" className="text-sm font-normal">
              Bank Transfer
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="paypal" id="method-paypal" />
            <Label htmlFor="method-paypal" className="text-sm font-normal">
              PayPal
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="ewallet" id="method-ewallet" />
            <Label htmlFor="method-ewallet" className="text-sm font-normal">
              E-Wallet (GoPay, OVO, Dana)
            </Label>
          </div>
        </RadioGroup>
      </div>

      {/* Bank Account Details */}
      {formData.method === "bank" && (
        <div className="rounded-md border border-border/60 bg-muted/30 p-4 space-y-4">
          <h3 className="text-sm font-medium">Bank Account Details</h3>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bank-name" className="text-sm">
                Account Holder Name
              </Label>
              <Input
                id="bank-name"
                value={formData.bankAccount.name}
                onChange={(e) => handleBankAccountChange("name", e.target.value)}
                placeholder="Enter account holder name"
                className="text-sm"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="bank-number" className="text-sm">
                Account Number
              </Label>
              <Input
                id="bank-number"
                value={formData.bankAccount.number}
                onChange={(e) => handleBankAccountChange("number", e.target.value)}
                placeholder="Enter account number"
                className="text-sm"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="bank-name" className="text-sm">
                Bank Name
              </Label>
              <Select
                value={formData.bankAccount.bank}
                onValueChange={(value) => handleBankAccountChange("bank", value)}
              >
                <SelectTrigger id="bank-name" className="text-sm">
                  <SelectValue placeholder="Select bank" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bca">BCA</SelectItem>
                  <SelectItem value="bni">BNI</SelectItem>
                  <SelectItem value="bri">BRI</SelectItem>
                  <SelectItem value="mandiri">Mandiri</SelectItem>
                  <SelectItem value="cimb">CIMB Niaga</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}

      {/* Digital Wallet */}
      {(formData.method === "paypal" || formData.method === "ewallet") && (
        <div className="rounded-md border border-border/60 bg-muted/30 p-4 space-y-4">
          <h3 className="text-sm font-medium">
            {formData.method === "paypal" ? "PayPal Details" : "E-Wallet Details"}
          </h3>
          <div className="space-y-4">
            {formData.method === "ewallet" && (
              <div className="space-y-2">
                <Label htmlFor="wallet-type" className="text-sm">
                  E-Wallet Type
                </Label>
                <Select
                  value={formData.digitalWallet.type}
                  onValueChange={(value) => handleDigitalWalletChange("type", value)}
                >
                  <SelectTrigger id="wallet-type" className="text-sm">
                    <SelectValue placeholder="Select e-wallet type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gopay">GoPay</SelectItem>
                    <SelectItem value="ovo">OVO</SelectItem>
                    <SelectItem value="dana">DANA</SelectItem>
                    <SelectItem value="linkaja">LinkAja</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="wallet-email" className="text-sm">
                {formData.method === "paypal" ? "PayPal Email" : "E-Wallet Phone/Email"}
              </Label>
              <Input
                id="wallet-email"
                value={formData.digitalWallet.email}
                onChange={(e) => handleDigitalWalletChange("email", e.target.value)}
                placeholder={formData.method === "paypal" ? "Enter PayPal email" : "Enter phone number or email"}
                className="text-sm"
              />
            </div>
          </div>
        </div>
      )}

      {/* Tax Information */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Tax Information</h3>
        <p className="text-xs text-muted-foreground">Please provide your tax information for reporting purposes.</p>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="tax-id" className="text-sm">
              Tax ID Number (NPWP)
            </Label>
            <Input
              id="tax-id"
              value={formData.taxInfo.taxId}
              onChange={(e) => handleTaxInfoChange("taxId", e.target.value)}
              placeholder="Enter your NPWP number"
              className="text-sm"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="tax-name" className="text-sm">
              Registered Tax Name
            </Label>
            <Input
              id="tax-name"
              value={formData.taxInfo.taxName}
              onChange={(e) => handleTaxInfoChange("taxName", e.target.value)}
              placeholder="Enter name as registered with tax authority"
              className="text-sm"
            />
          </div>
        </div>
      </div>

      {/* Payment Preferences */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Payment Preferences</h3>
        <div className="grid gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="threshold" className="text-sm">
              Payment Threshold
            </Label>
            <Select value={formData.threshold} onValueChange={(value) => handleChange("threshold", value)}>
              <SelectTrigger id="threshold" className="text-sm">
                <SelectValue placeholder="Select threshold" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="500000">Rp 500,000</SelectItem>
                <SelectItem value="1000000">Rp 1,000,000</SelectItem>
                <SelectItem value="2000000">Rp 2,000,000</SelectItem>
                <SelectItem value="5000000">Rp 5,000,000</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="frequency" className="text-sm">
              Payment Frequency
            </Label>
            <Select value={formData.frequency} onValueChange={(value) => handleChange("frequency", value)}>
              <SelectTrigger id="frequency" className="text-sm">
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="bimonthly">Bi-monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  )
}
