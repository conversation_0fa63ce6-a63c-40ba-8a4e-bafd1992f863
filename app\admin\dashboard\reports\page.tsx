import type { Metada<PERSON> } from "next"
import { ReportsDashboard } from "@/components/admin/reports/reports-dashboard"
import { PageHeader } from "@/components/admin/ui/page-header"

export const metadata: Metadata = {
  title: "Reports Dashboard | Sellzio Admin",
  description: "Lihat dan kelola laporan platform Anda",
}

export default function ReportsPage() {
  return (
    <>
      <PageHeader
        title="Reports Dashboard"
        description="Lihat dan kelola laporan platform Anda"
        actions={[
          {
            label: "Buat Laporan",
            href: "/admin/dashboard/reports/create",
            variant: "default",
          },
          {
            label: "Ekspor Data",
            href: "#",
            variant: "outline",
          },
        ]}
      />
      <ReportsDashboard />
    </>
  )
}
