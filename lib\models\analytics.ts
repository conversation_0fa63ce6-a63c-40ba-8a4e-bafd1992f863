export interface AnalyticsMetric {
  id: string
  name: string
  value: number
  previousValue?: number
  change?: number
  changeType?: "increase" | "decrease" | "neutral"
  format?: "number" | "currency" | "percentage"
}

export interface AnalyticsTimeSeriesData {
  id: string
  name: string
  data: {
    date: string
    value: number
  }[]
  previousPeriodData?: {
    date: string
    value: number
  }[]
  total: number
  previousTotal?: number
  change?: number
  changeType?: "increase" | "decrease" | "neutral"
}

export interface AnalyticsPieData {
  id: string
  name: string
  data: {
    name: string
    value: number
    percentage: number
  }[]
  total: number
}

export interface AnalyticsTableData {
  id: string
  name: string
  columns: {
    id: string
    name: string
    format?: "number" | "currency" | "percentage" | "date" | "text"
  }[]
  rows: Record<string, any>[]
  total?: number
}

export interface AnalyticsDashboard {
  id: string
  name: string
  metrics: AnalyticsMetric[]
  timeSeriesData: AnalyticsTimeSeriesData[]
  pieData: AnalyticsPieData[]
  tableData: AnalyticsTableData[]
}

export interface AnalyticsFilter {
  dateRange?: {
    start: string
    end: string
  }
  comparison?: "previous_period" | "previous_year" | "custom"
  customComparisonRange?: {
    start: string
    end: string
  }
  granularity?: "day" | "week" | "month" | "quarter" | "year"
  dimensions?: string[]
  metrics?: string[]
}

export interface AnalyticsExportOptions {
  format: "csv" | "excel" | "pdf"
  data: "current_view" | "all_data"
  includeCharts?: boolean
  fileName?: string
}

export interface AnalyticsEvent {
  id: string
  type: string
  userId?: string
  tenantId?: string
  storeId?: string
  productId?: string
  orderId?: string
  properties?: Record<string, any>
  timestamp: string
}
