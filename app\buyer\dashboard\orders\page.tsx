"use client"

import { useState, use<PERSON>ffect } from "react"
import Link from "next/link"
import {
  Package,
  Search,
  Filter,
  ChevronDown,
  Truck,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Eye,
  ShoppingBag,
  Calendar,
  Plus,
  Edit2,
  Trash2
} from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"

// Type untuk Order
interface OrderItem {
  name: string;
  quantity: number;
  price: number;
  image: string;
}

interface Shipping {
  method: string;
  status: string;
  trackingNumber: string | null;
  eta: string | null;
  deliveredAt?: string;
}

interface Order {
  id: string;
  date: string;
  status: string;
  items: OrderItem[];
  total: number;
  shipping: Shipping;
  cancellationReason?: string;
}

// Schema validasi untuk form pembaruan order
const updateOrderSchema = z.object({
  status: z.string(),
  shipping: z.object({
    method: z.string(),
    status: z.string(),
    trackingNumber: z.string().nullable(),
    eta: z.string().nullable(),
  }),
  cancellationReason: z.string().optional(),
});

// Schema validasi untuk form pembuatan order
const createOrderSchema = z.object({
  items: z.array(
    z.object({
      name: z.string().min(1, "Nama item diperlukan"),
      quantity: z.number().min(1, "Kuantitas minimal 1"),
      price: z.number().min(0, "Harga tidak boleh negatif"),
      image: z.string().optional(),
    })
  ).min(1, "Minimal satu item diperlukan"),
  shipping: z.object({
    method: z.string(),
    trackingNumber: z.string().optional().nullable(),
    eta: z.string().optional().nullable(),
  }),
});

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [activeTab, setActiveTab] = useState("all")
  const [timePeriod, setTimePeriod] = useState("all-time")
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  
  // Forms
  const updateForm = useForm<z.infer<typeof updateOrderSchema>>({
    resolver: zodResolver(updateOrderSchema),
  });
  
  const createForm = useForm<z.infer<typeof createOrderSchema>>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      items: [{ name: "", quantity: 1, price: 0, image: "/placeholder.png" }],
      shipping: {
        method: "JNE Regular",
        trackingNumber: null,
        eta: null,
      },
    },
  });

  // Fetch orders dari API
  const fetchOrders = async () => {
    setLoading(true);
    try {
      let url = '/api/orders';
      if (activeTab !== 'all') {
        url += `?status=${activeTab}`;
      }
      if (searchQuery) {
        url += `${activeTab !== 'all' ? '&' : '?'}query=${searchQuery}`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Gagal memuat data');
      }
      const data = await response.json();
      setOrders(data);
      setFilteredOrders(data);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast({
        title: "Error",
        description: "Gagal memuat pesanan",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk membuat order baru
  const createOrder = async (data: z.infer<typeof createOrderSchema>) => {
    try {
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error('Gagal membuat pesanan');
      }
      
      const newOrder = await response.json();
      setOrders([newOrder, ...orders]);
      setFilteredOrders([newOrder, ...filteredOrders]);
      setCreateDialogOpen(false);
      createForm.reset();
      
      toast({
        title: "Sukses",
        description: "Pesanan berhasil dibuat",
      });
    } catch (error) {
      console.error('Error creating order:', error);
      toast({
        title: "Error",
        description: "Gagal membuat pesanan",
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk update order
  const updateOrder = async (data: z.infer<typeof updateOrderSchema>) => {
    if (!selectedOrder) return;
    
    try {
      const response = await fetch(`/api/orders/${selectedOrder.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error('Gagal memperbarui pesanan');
      }
      
      const updatedOrder = await response.json();
      
      // Update orders array dengan order yang diperbarui
      const updatedOrders = orders.map(order => 
        order.id === selectedOrder.id ? updatedOrder : order
      );
      
      setOrders(updatedOrders);
      setFilteredOrders(updatedOrders);
      setUpdateDialogOpen(false);
      
      toast({
        title: "Sukses",
        description: "Pesanan berhasil diperbarui",
      });
    } catch (error) {
      console.error('Error updating order:', error);
      toast({
        title: "Error",
        description: "Gagal memperbarui pesanan",
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk menghapus order
  const deleteOrder = async () => {
    if (!selectedOrder) return;
    
    try {
      const response = await fetch(`/api/orders/${selectedOrder.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Gagal menghapus pesanan');
      }
      
      // Hapus order dari array
      const updatedOrders = orders.filter(order => order.id !== selectedOrder.id);
      setOrders(updatedOrders);
      setFilteredOrders(updatedOrders);
      setDeleteDialogOpen(false);
      
      toast({
        title: "Sukses",
        description: "Pesanan berhasil dihapus",
      });
    } catch (error) {
      console.error('Error deleting order:', error);
      toast({
        title: "Error",
        description: "Gagal menghapus pesanan",
        variant: "destructive",
      });
    }
  };

  // Load data saat komponen dimount
  useEffect(() => {
    fetchOrders();
  }, [activeTab, searchQuery]);

  // Status badge renderer
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "processing":
        return (
          <Badge variant="default" className="capitalize">
            Processing
          </Badge>
        )
      case "shipped":
        return (
          <Badge variant="secondary" className="capitalize">
            Shipped
          </Badge>
        )
      case "delivered":
        return (
          <Badge variant="outline" className="border-green-500 text-green-500 capitalize">
            Delivered
          </Badge>
        )
      case "cancelled":
        return (
          <Badge variant="destructive" className="capitalize">
            Cancelled
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="capitalize">
            {status}
          </Badge>
        )
    }
  }

  // Shipping status renderer
  const renderShippingStatus = (shipping: Shipping) => {
    switch (shipping.status) {
      case "preparing":
        return (
          <div className="flex items-center gap-1 text-sm">
            <Package className="h-4 w-4 text-primary" />
            <span>Preparing for shipment</span>
          </div>
        )
      case "in_transit":
        return (
          <div className="flex items-center gap-1 text-sm">
            <Truck className="h-4 w-4 text-blue-500" />
            <span>In transit</span>
          </div>
        )
      case "delivered":
        return (
          <div className="flex items-center gap-1 text-sm">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span>Delivered on {shipping.deliveredAt ? new Date(shipping.deliveredAt).toLocaleDateString("id-ID") : 'N/A'}</span>
          </div>
        )
      case "cancelled":
        return (
          <div className="flex items-center gap-1 text-sm">
            <XCircle className="h-4 w-4 text-red-500" />
            <span>Cancelled</span>
          </div>
        )
      default:
        return (
          <div className="flex items-center gap-1 text-sm">
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
            <span>{shipping.status}</span>
          </div>
        )
    }
  }

  // Action buttons renderer
  const renderActionButtons = (order: Order) => {
    return (
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          asChild
        >
        <Link href={`/buyer/dashboard/orders/${order.id}`}>
          <Eye className="mr-1 h-4 w-4" />
            Detail
        </Link>
        </Button>
        
        {order.shipping.trackingNumber && (
          <Button
            variant="outline"
            size="sm"
            asChild
          >
            <Link href={`/buyer/dashboard/orders/tracking?tracking=${order.shipping.trackingNumber}`}>
            <Truck className="mr-1 h-4 w-4" />
              Lacak
          </Link>
          </Button>
        )}
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            setSelectedOrder(order);
            updateForm.reset({
              status: order.status,
              shipping: {
                method: order.shipping.method,
                status: order.shipping.status,
                trackingNumber: order.shipping.trackingNumber,
                eta: order.shipping.eta,
              },
              cancellationReason: order.cancellationReason,
            });
            setUpdateDialogOpen(true);
          }}
        >
          <Edit2 className="mr-1 h-4 w-4" />
          Edit
        </Button>
        
        <Button
          variant="destructive"
          size="sm"
          onClick={() => {
            setSelectedOrder(order);
            setDeleteDialogOpen(true);
          }}
        >
          <Trash2 className="mr-1 h-4 w-4" />
          Hapus
        </Button>
      </div>
    )
  }

  // Helper untuk menambahkan item pada form pembuatan
  const addItem = () => {
    const items = createForm.getValues().items;
    createForm.setValue('items', [...items, { name: "", quantity: 1, price: 0, image: "/placeholder.png" }]);
  };

  // Helper untuk menghapus item pada form pembuatan
  const removeItem = (index: number) => {
    const items = createForm.getValues().items;
    if (items.length > 1) {
      createForm.setValue('items', items.filter((_, i) => i !== index));
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Pesanan Saya</h2>
          <p className="text-muted-foreground">Lihat dan kelola semua pesanan Anda</p>
        </div>
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Pesanan Baru
            </Button>
      </div>

      <div className="space-y-4">
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
              placeholder="Cari pesanan..."
              className="pl-8"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
          <div className="flex items-center gap-2">
            <Select value={timePeriod} onValueChange={setTimePeriod}>
            <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Periode Waktu" />
            </SelectTrigger>
            <SelectContent>
                <SelectItem value="all-time">Semua Waktu</SelectItem>
                <SelectItem value="this-month">Bulan Ini</SelectItem>
                <SelectItem value="last-month">Bulan Lalu</SelectItem>
                <SelectItem value="last-3-months">3 Bulan Terakhir</SelectItem>
                <SelectItem value="last-6-months">6 Bulan Terakhir</SelectItem>
                <SelectItem value="this-year">Tahun Ini</SelectItem>
            </SelectContent>
          </Select>
            <Button variant="outline" size="icon" onClick={() => fetchOrders()}>
              <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="w-full justify-start overflow-x-auto">
            <TabsTrigger value="all">Semua</TabsTrigger>
            <TabsTrigger value="processing">Diproses</TabsTrigger>
            <TabsTrigger value="shipped">Dikirim</TabsTrigger>
            <TabsTrigger value="delivered">Diterima</TabsTrigger>
            <TabsTrigger value="cancelled">Dibatalkan</TabsTrigger>
        </TabsList>
          <TabsContent value="all" className="space-y-4">
            {loading ? (
              <div className="flex justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <ShoppingBag className="h-12 w-12 text-muted-foreground" />
                <h3 className="mt-2 text-lg font-medium">Tidak ada pesanan</h3>
                <p className="text-sm text-muted-foreground">Anda belum memiliki pesanan apa pun.</p>
              </div>
            ) : (
          <div className="space-y-4">
                {filteredOrders.map((order) => (
                <Card key={order.id} className="overflow-hidden">
                  <CardContent className="p-0">
                      <div className="flex flex-col sm:flex-row">
                        <div className="flex-1 p-6">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div className="mb-2 sm:mb-0">
                        <div className="flex items-center gap-2">
                                <h3 className="text-lg font-medium">{order.id}</h3>
                          {renderStatusBadge(order.status)}
                        </div>
                              <div className="mt-1 flex items-center gap-2 text-sm text-muted-foreground">
                                <Calendar className="h-4 w-4" />
                                <span>{new Date(order.date).toLocaleDateString("id-ID")}</span>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-muted-foreground">Total</div>
                              <div className="font-medium">{formatCurrency(order.total)}</div>
                            </div>
                        </div>
                          <div className="mt-4 border-t pt-4">
                            <div className="mb-2 font-medium">Items</div>
                            <div className="space-y-2">
                              {order.items.map((item, i) => (
                                <div key={i} className="flex items-center gap-2">
                                  <div className="h-10 w-10 overflow-hidden rounded-md bg-muted">
                                    <img
                                      src={item.image}
                                alt={item.name}
                                      className="h-full w-full object-cover"
                              />
                                  </div>
                                  <div className="flex-1">
                                <div className="font-medium">{item.name}</div>
                                <div className="text-sm text-muted-foreground">
                                      {item.quantity} x {formatCurrency(item.price)}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                          </div>
                          <div className="mt-4 border-t pt-4">
                          {renderShippingStatus(order.shipping)}
                            <div className="mt-2 text-sm text-muted-foreground">
                              {order.shipping.trackingNumber ? (
                                <div>
                                  Tracking: <span className="font-medium">{order.shipping.trackingNumber}</span>
                            </div>
                              ) : null}
                              {order.shipping.eta ? (
                              <div>
                                  Estimasi tiba: {new Date(order.shipping.eta).toLocaleDateString("id-ID")}
                                </div>
                              ) : null}
                            </div>
                            </div>
                        </div>
                      </div>
                      <div className="border-t bg-muted/50 p-4">
                        {renderActionButtons(order)}
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
            )}
          </TabsContent>
          <TabsContent value="processing" className="space-y-4">
            {/* Sama seperti 'all' tapi difilter berdasarkan status */}
          </TabsContent>
          <TabsContent value="shipped" className="space-y-4">
            {/* Sama seperti 'all' tapi difilter berdasarkan status */}
          </TabsContent>
          <TabsContent value="delivered" className="space-y-4">
            {/* Sama seperti 'all' tapi difilter berdasarkan status */}
          </TabsContent>
          <TabsContent value="cancelled" className="space-y-4">
            {/* Sama seperti 'all' tapi difilter berdasarkan status */}
        </TabsContent>
        </Tabs>
                    </div>

      {/* Dialog untuk membuat order baru */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Buat Pesanan Baru</DialogTitle>
          </DialogHeader>
          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit(createOrder)} className="space-y-6">
              <div>
                <h3 className="mb-4 text-lg font-medium">Item Pesanan</h3>
                {createForm.watch("items").map((_, index) => (
                  <div key={index} className="mb-4 grid grid-cols-12 gap-3">
                    <FormField
                      control={createForm.control}
                      name={`items.${index}.name`}
                      render={({ field }) => (
                        <FormItem className="col-span-5">
                          <FormLabel>Nama Produk</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Nama produk" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={createForm.control}
                      name={`items.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Jumlah</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              {...field} 
                              onChange={e => field.onChange(parseInt(e.target.value) || 1)} 
                              min={1} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={createForm.control}
                      name={`items.${index}.price`}
                      render={({ field }) => (
                        <FormItem className="col-span-3">
                          <FormLabel>Harga</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              {...field} 
                              onChange={e => field.onChange(parseInt(e.target.value) || 0)} 
                              min={0} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={createForm.control}
                      name={`items.${index}.image`}
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Gambar</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="URL gambar" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {index > 0 && (
                      <Button 
                        type="button" 
                        variant="destructive" 
                        size="sm" 
                        className="mt-8" 
                        onClick={() => removeItem(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                            </div>
                          ))}
                <Button type="button" variant="outline" onClick={addItem}>
                  <Plus className="mr-1 h-4 w-4" />
                  Tambah Item
                </Button>
                        </div>

              <div>
                <h3 className="mb-4 text-lg font-medium">Informasi Pengiriman</h3>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={createForm.control}
                    name="shipping.method"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Metode Pengiriman</FormLabel>
                        <Select 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih metode pengiriman" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="JNE Regular">JNE Regular</SelectItem>
                            <SelectItem value="J&T Express">J&T Express</SelectItem>
                            <SelectItem value="SiCepat">SiCepat</SelectItem>
                            <SelectItem value="AnterAja">AnterAja</SelectItem>
                            <SelectItem value="Ninja Express">Ninja Express</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={createForm.control}
                    name="shipping.trackingNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nomor Resi (Opsional)</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Nomor resi" value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={createForm.control}
                    name="shipping.eta"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estimasi Tiba (Opsional)</FormLabel>
                        <FormControl>
                          <Input 
                            type="date" 
                            {...field} 
                            value={field.value || ''} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                        </div>
                      </div>

              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setCreateDialogOpen(false)}>
                  Batal
                </Button>
                <Button type="submit">Buat Pesanan</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk update order */}
      <Dialog open={updateDialogOpen} onOpenChange={setUpdateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Pesanan {selectedOrder?.id}</DialogTitle>
          </DialogHeader>
          <Form {...updateForm}>
            <form onSubmit={updateForm.handleSubmit(updateOrder)} className="space-y-6">
          <div className="space-y-4">
                <FormField
                  control={updateForm.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status Pesanan</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="processing">Diproses</SelectItem>
                          <SelectItem value="shipped">Dikirim</SelectItem>
                          <SelectItem value="delivered">Diterima</SelectItem>
                          <SelectItem value="cancelled">Dibatalkan</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={updateForm.control}
                  name="shipping.status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status Pengiriman</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih status pengiriman" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="preparing">Sedang Disiapkan</SelectItem>
                          <SelectItem value="in_transit">Dalam Perjalanan</SelectItem>
                          <SelectItem value="delivered">Terkirim</SelectItem>
                          <SelectItem value="cancelled">Dibatalkan</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={updateForm.control}
                  name="shipping.trackingNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nomor Resi</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nomor resi" value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={updateForm.control}
                  name="shipping.eta"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estimasi Tiba</FormLabel>
                      <FormControl>
                        <Input 
                          type="date" 
                          {...field} 
                          value={field.value || ''} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {updateForm.watch("status") === "cancelled" && (
                  <FormField
                    control={updateForm.control}
                    name="cancellationReason"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Alasan Pembatalan</FormLabel>
                        <FormControl>
                          <Textarea 
                            {...field} 
                            placeholder="Alasan pembatalan pesanan" 
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                      </div>

              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setUpdateDialogOpen(false)}>
                  Batal
                </Button>
                <Button type="submit">Simpan Perubahan</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Dialog konfirmasi hapus */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Pesanan</DialogTitle>
          </DialogHeader>
          <p>Apakah Anda yakin ingin menghapus pesanan {selectedOrder?.id}? Tindakan ini tidak dapat dibatalkan.</p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Batal
            </Button>
            <Button variant="destructive" onClick={deleteOrder}>
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
