"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { formatCurrency } from "@/lib/utils"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Minus, Plus, ShoppingCart } from "lucide-react"
import { productsAPI, type Product } from "@/lib/api/products"

interface TenantProductPageProps {
  tenantSlug: string
  storeSlug: string
  productId: string
}

export function TenantProductPage({ tenantSlug, storeSlug, productId }: TenantProductPageProps) {
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [quantity, setQuantity] = useState(1)
  const [selectedImage, setSelectedImage] = useState(0)

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const productData = await productsAPI.getById(productId)
        setProduct(productData)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching product data:", err)
        setError("Gagal memuat data produk. Silakan coba lagi.")
        setLoading(false)
      }
    }

    fetchProduct()
  }, [productId])

  const increaseQuantity = () => {
    setQuantity((prev) => prev + 1)
  }

  const decreaseQuantity = () => {
    setQuantity((prev) => (prev > 1 ? prev - 1 : 1))
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center mb-4">
            <Skeleton className="h-4 w-20" />
            <span className="mx-2">/</span>
            <Skeleton className="h-4 w-20" />
            <span className="mx-2">/</span>
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="grid gap-8 md:grid-cols-2">
            <Skeleton className="h-96 w-full" />
            <div className="space-y-6">
              <Skeleton className="h-10 w-3/4" />
              <Skeleton className="h-6 w-1/3" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-12 w-full" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="max-w-md w-full">
          <CardContent className="pt-6 text-center">
            <h1 className="text-2xl font-bold mb-4">Produk Tidak Ditemukan</h1>
            <p className="text-muted-foreground mb-6">Produk yang Anda cari tidak ditemukan atau telah dihapus.</p>
            <Button asChild>
              <Link href={`/tenant/${tenantSlug}/store/${storeSlug}`}>Kembali ke Toko</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center mb-4">
          <Link href={`/tenant/${tenantSlug}`} className="text-sm text-primary hover:underline">
            {tenantSlug}
          </Link>
          <span className="mx-2">/</span>
          <Link href={`/tenant/${tenantSlug}/store/${storeSlug}`} className="text-sm text-primary hover:underline">
            {storeSlug}
          </Link>
          <span className="mx-2">/</span>
          <span className="text-sm">{product.name}</span>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
          <div className="space-y-4">
            <div className="bg-muted rounded-lg flex items-center justify-center h-96">
              {product.images && product.images.length > 0 ? (
                <img
                  src={product.images[selectedImage] || "/placeholder.svg"}
                  alt={product.name}
                  className="w-full h-full object-contain rounded-lg"
                />
              ) : (
                <div className="text-6xl font-bold text-muted-foreground">{product.name.charAt(0)}</div>
              )}
            </div>

            {product.images && product.images.length > 1 && (
              <div className="flex space-x-2 overflow-x-auto">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    className={`h-20 w-20 rounded-md overflow-hidden flex-shrink-0 ${
                      selectedImage === index ? "ring-2 ring-primary" : ""
                    }`}
                    onClick={() => setSelectedImage(index)}
                  >
                    <img
                      src={image || "/placeholder.svg"}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          <div className="space-y-6">
            <h1 className="text-3xl font-bold">{product.name}</h1>
            <p className="text-2xl font-bold">{formatCurrency(product.price)}</p>

            <div>
              <h3 className="font-medium mb-2">Deskripsi</h3>
              <p className="text-muted-foreground">{product.description || "Tidak ada deskripsi"}</p>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium">Jumlah</h3>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="icon" onClick={decreaseQuantity} disabled={quantity <= 1}>
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="w-12 text-center">{quantity}</span>
                <Button variant="outline" size="icon" onClick={increaseQuantity}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <Button className="w-full">
              <ShoppingCart className="mr-2 h-4 w-4" />
              Tambah ke Keranjang
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
