"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>hart, <PERSON><PERSON>hart, <PERSON>hart } from "@/components/ui/charts"

export function RevenueOverview() {
  const [timeRange, setTimeRange] = useState("year")
  const [isLoading, setIsLoading] = useState(true)
  const [data, setData] = useState({
    financialMetrics: [],
    monthlyRevenue: [],
    revenueBreakdown: [],
    yearlyComparison: [],
    forecastProjections: [],
  })

  useEffect(() => {
    // Simulasi loading data
    const timer = setTimeout(() => {
      try {
        setData({
          financialMetrics: [
            {
              title: "Gross Revenue",
              value: "$1,245,890",
              change: "+12.5%",
              trend: "up",
            },
            {
              title: "Net Revenue",
              value: "$987,650",
              change: "+8.3%",
              trend: "up",
            },
            {
              title: "Transaction Fees",
              value: "$124,589",
              change: "+15.2%",
              trend: "up",
            },
            {
              title: "Subscription Fees",
              value: "$133,651",
              change: "+5.7%",
              trend: "up",
            },
          ],
          monthlyRevenue: [
            { name: "Jan", revenue: 65000 },
            { name: "Feb", revenue: 72000 },
            { name: "Mar", revenue: 84000 },
            { name: "Apr", revenue: 78000 },
            { name: "May", revenue: 92000 },
            { name: "Jun", revenue: 105000 },
            { name: "Jul", revenue: 114000 },
            { name: "Aug", revenue: 108000 },
            { name: "Sep", revenue: 120000 },
            { name: "Oct", revenue: 132000 },
            { name: "Nov", revenue: 125000 },
            { name: "Dec", revenue: 145000 },
          ],
          revenueBreakdown: [
            { name: "Transaction Fees", value: 35 },
            { name: "Subscription Fees", value: 30 },
            { name: "Commission", value: 20 },
            { name: "Premium Features", value: 10 },
            { name: "Other", value: 5 },
          ],
          yearlyComparison: [
            { name: "Jan", current: 65000, previous: 45000 },
            { name: "Feb", current: 72000, previous: 52000 },
            { name: "Mar", current: 84000, previous: 61000 },
            { name: "Apr", current: 78000, previous: 58000 },
            { name: "May", current: 92000, previous: 71000 },
            { name: "Jun", current: 105000, previous: 82000 },
            { name: "Jul", current: 114000, previous: 91000 },
            { name: "Aug", current: 108000, previous: 84000 },
            { name: "Sep", current: 120000, previous: 95000 },
            { name: "Oct", current: 132000, previous: 102000 },
            { name: "Nov", current: 125000, previous: 98000 },
            { name: "Dec", current: 145000, previous: 112000 },
          ],
          forecastProjections: [
            { name: "Jan", actual: 65000, forecast: 65000 },
            { name: "Feb", actual: 72000, forecast: 70000 },
            { name: "Mar", actual: 84000, forecast: 80000 },
            { name: "Apr", actual: 78000, forecast: 85000 },
            { name: "May", actual: 92000, forecast: 90000 },
            { name: "Jun", actual: 105000, forecast: 100000 },
            { name: "Jul", actual: 114000, forecast: 110000 },
            { name: "Aug", actual: 108000, forecast: 115000 },
            { name: "Sep", actual: 120000, forecast: 125000 },
            { name: "Oct", actual: 132000, forecast: 135000 },
            { name: "Nov", actual: 0, forecast: 140000 },
            { name: "Dec", actual: 0, forecast: 150000 },
          ],
        })
        setIsLoading(false)
      } catch (error) {
        console.error("Error loading data:", error)
        setIsLoading(false)
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return <LoadingState />
  }

  const { financialMetrics, monthlyRevenue, revenueBreakdown, yearlyComparison, forecastProjections } = data

  return (
    <div className="grid gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Revenue Overview</h2>
        <Select defaultValue={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="quarter">This Quarter</SelectItem>
            <SelectItem value="year">This Year</SelectItem>
            <SelectItem value="all">All Time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array.isArray(financialMetrics) && financialMetrics.length > 0 ? (
          financialMetrics.map((metric, index) => (
            <Card key={index}>
              <CardHeader className="pb-2">
                <CardDescription>{metric.title}</CardDescription>
                <CardTitle className="text-3xl">{metric.value}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <span className={`mr-1 text-sm ${metric.trend === "up" ? "text-green-500" : "text-red-500"}`}>
                    {metric.change}
                  </span>
                  <span className="text-xs text-muted-foreground">vs previous period</span>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card className="col-span-4">
            <CardContent className="p-6">
              <p className="text-center text-muted-foreground">Tidak ada data metrik tersedia</p>
            </CardContent>
          </Card>
        )}
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3 lg:w-[400px]">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="comparison">Yearly Comparison</TabsTrigger>
          <TabsTrigger value="forecast">Forecast</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
                <CardDescription>Monthly revenue for the current year</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {Array.isArray(monthlyRevenue) && monthlyRevenue.length > 0 ? (
                    <LineChart
                      data={monthlyRevenue}
                      index="name"
                      categories={["revenue"]}
                      colors={["blue"]}
                      valueFormatter={(value) => `$${value.toLocaleString()}`}
                      className="h-full"
                    />
                  ) : (
                    <div className="flex h-full items-center justify-center">
                      <p className="text-muted-foreground">Tidak ada data revenue tersedia</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Revenue Breakdown</CardTitle>
                <CardDescription>Revenue by source</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {Array.isArray(revenueBreakdown) && revenueBreakdown.length > 0 ? (
                    <PieChart
                      data={revenueBreakdown}
                      index="name"
                      category="value"
                      valueFormatter={(value) => `${value}%`}
                      className="h-full"
                    />
                  ) : (
                    <div className="flex h-full items-center justify-center">
                      <p className="text-muted-foreground">Tidak ada data breakdown tersedia</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="comparison" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Year-over-Year Comparison</CardTitle>
              <CardDescription>Current year vs previous year</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                {Array.isArray(yearlyComparison) && yearlyComparison.length > 0 ? (
                  <BarChart
                    data={yearlyComparison}
                    index="name"
                    categories={["current", "previous"]}
                    colors={["blue", "gray"]}
                    valueFormatter={(value) => `$${value.toLocaleString()}`}
                    className="h-full"
                  />
                ) : (
                  <div className="flex h-full items-center justify-center">
                    <p className="text-muted-foreground">Tidak ada data perbandingan tersedia</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="forecast" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Forecast</CardTitle>
              <CardDescription>Actual vs projected revenue</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                {Array.isArray(forecastProjections) && forecastProjections.length > 0 ? (
                  <LineChart
                    data={forecastProjections}
                    index="name"
                    categories={["actual", "forecast"]}
                    colors={["blue", "green"]}
                    valueFormatter={(value) => `$${value.toLocaleString()}`}
                    className="h-full"
                  />
                ) : (
                  <div className="flex h-full items-center justify-center">
                    <p className="text-muted-foreground">Tidak ada data forecast tersedia</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

function LoadingState() {
  return (
    <div className="grid gap-6">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-10 w-[180px]" />
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array(4)
          .fill(null)
          .map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-32" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-36" />
              </CardContent>
            </Card>
          ))}
      </div>

      <Skeleton className="h-10 w-[400px]" />

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px] w-full" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px] w-full" />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
