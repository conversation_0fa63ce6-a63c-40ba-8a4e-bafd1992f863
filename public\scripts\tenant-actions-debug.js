// Debug script untuk tenant actions
console.log("Tenant Actions Debug Script loaded")

// Fungsi untuk menangani action tenant
function handleTenantAction(action, tenantId) {
  console.log(`Debug: Handling action ${action} for tenant ${tenantId}`)

  switch (action) {
    case "view":
      console.log(`Debug: Viewing tenant ${tenantId}`)
      window.location.href = `/admin/dashboard/tenants/${tenantId}`
      break
    case "edit":
      console.log(`Debug: Editing tenant ${tenantId}`)
      window.location.href = `/admin/dashboard/tenants/${tenantId}/edit`
      break
    case "login":
      console.log(`Debug: Logging in as tenant ${tenantId}`)
      window.location.href = `/tenant/dashboard?tenant=${tenantId}`
      break
    case "suspend":
      console.log(`Debug: Suspending tenant ${tenantId}`)
      alert(`Tenant ${tenantId} has been suspended`)
      break
    case "activate":
      console.log(`Debug: Activating tenant ${tenantId}`)
      alert(`Tenant ${tenantId} has been activated`)
      break
    case "delete":
      console.log(`Debug: Deleting tenant ${tenantId}`)
      if (confirm(`Are you sure you want to delete tenant ${tenantId}?`)) {
        alert(`Tenant ${tenantId} has been deleted`)
      }
      break
    default:
      console.log(`Debug: Unknown action ${action}`)
      break
  }
}

// Expose functions to window for debugging
window.tenantActionsDebug = {
  handleTenantAction,
}

console.log("Tenant actions debug functions available at window.tenantActionsDebug")
