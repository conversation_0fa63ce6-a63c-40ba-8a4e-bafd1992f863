"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useNotifications } from "@/components/admin/ui/notifications"
import { MoreHorizontal, Edit, Trash2, Check, X, BarChart, DollarSign, Users, Loader2 } from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Mock data for subscription plans
const plans = [
  {
    id: "1",
    name: "Basic",
    price: "$29",
    billingCycle: "monthly",
    tenantCount: 85,
    revenue: "$2,465",
    features: [
      { name: "Up to 3 stores", included: true },
      { name: "Basic analytics", included: true },
      { name: "Standard support", included: true },
      { name: "Custom domain", included: false },
      { name: "API access", included: false },
      { name: "White labeling", included: false },
      { name: "Priority support", included: false },
    ],
  },
  {
    id: "2",
    name: "Professional",
    price: "$99",
    billingCycle: "monthly",
    tenantCount: 124,
    revenue: "$12,276",
    features: [
      { name: "Up to 10 stores", included: true },
      { name: "Advanced analytics", included: true },
      { name: "Priority support", included: true },
      { name: "Custom domain", included: true },
      { name: "API access", included: true },
      { name: "White labeling", included: false },
      { name: "Dedicated account manager", included: false },
    ],
  },
  {
    id: "3",
    name: "Enterprise",
    price: "$299",
    billingCycle: "monthly",
    tenantCount: 38,
    revenue: "$11,362",
    features: [
      { name: "Unlimited stores", included: true },
      { name: "Enterprise analytics", included: true },
      { name: "24/7 support", included: true },
      { name: "Custom domain", included: true },
      { name: "API access", included: true },
      { name: "White labeling", included: true },
      { name: "Dedicated account manager", included: true },
    ],
  },
  {
    id: "4",
    name: "Free Trial",
    price: "$0",
    billingCycle: "14 days",
    tenantCount: 156,
    revenue: "$0",
    features: [
      { name: "1 store", included: true },
      { name: "Basic analytics", included: true },
      { name: "Community support", included: true },
      { name: "Custom domain", included: false },
      { name: "API access", included: false },
      { name: "White labeling", included: false },
      { name: "Priority support", included: false },
    ],
  },
]

export function SubscriptionPlansList() {
  const [selectedPlan, setSelectedPlan] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { addNotification } = useNotifications()

  const handleRowClick = (plan: any) => {
    setSelectedPlan(plan)
  }

  const handleEditPlan = () => {
    if (!selectedPlan) return

    setIsLoading(true)
    // Simulasi API call
    setTimeout(() => {
      setIsLoading(false)
      addNotification({
        title: "Plan Updated",
        message: `${selectedPlan.name} plan has been updated.`,
        type: "success",
      })
    }, 1000)
  }

  const handleDeletePlan = () => {
    if (!selectedPlan) return

    setIsLoading(true)
    // Simulasi API call
    setTimeout(() => {
      setIsLoading(false)
      addNotification({
        title: "Plan Deleted",
        message: `${selectedPlan.name} plan has been deleted.`,
        type: "info",
      })
      setSelectedPlan(null)
    }, 1000)
  }

  return (
    <div className="grid gap-6 md:grid-cols-3">
      <div className="md:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>Subscription Plans</CardTitle>
            <CardDescription>Manage your platform's subscription plans</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">Name</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Billing Cycle</TableHead>
                  <TableHead className="text-center">Tenant Count</TableHead>
                  <TableHead className="text-right">Revenue</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {plans.map((plan) => (
                  <TableRow
                    key={plan.id}
                    className={`cursor-pointer ${selectedPlan?.id === plan.id ? "bg-accent/20" : ""}`}
                    onClick={() => handleRowClick(plan)}
                  >
                    <TableCell className="font-medium">{plan.name}</TableCell>
                    <TableCell>{plan.price}</TableCell>
                    <TableCell>{plan.billingCycle}</TableCell>
                    <TableCell className="text-center">{plan.tenantCount}</TableCell>
                    <TableCell className="text-right">{plan.revenue}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem className="flex items-center gap-2" onClick={handleEditPlan}>
                            <Edit className="h-4 w-4" /> Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem className="flex items-center gap-2 text-red-600" onClick={handleDeletePlan}>
                            <Trash2 className="h-4 w-4" /> Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <div>
        {selectedPlan ? (
          <Card>
            <CardHeader>
              <CardTitle>{selectedPlan.name}</CardTitle>
              <CardDescription>Plan Details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Tabs defaultValue="details">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="features">Features</TabsTrigger>
                </TabsList>
                <TabsContent value="details" className="space-y-4 pt-4">
                  <div className="grid grid-cols-[20px_1fr] items-start gap-2">
                    <DollarSign className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Price</p>
                      <p className="text-sm text-muted-foreground">
                        {selectedPlan.price} / {selectedPlan.billingCycle}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-[20px_1fr] items-start gap-2">
                    <Users className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Subscribers</p>
                      <p className="text-sm text-muted-foreground">{selectedPlan.tenantCount} tenants</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-[20px_1fr] items-start gap-2">
                    <BarChart className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Revenue</p>
                      <p className="text-sm text-muted-foreground">{selectedPlan.revenue} monthly</p>
                    </div>
                  </div>

                  <div className="pt-2">
                    <Button variant="outline" className="w-full" onClick={handleEditPlan} disabled={isLoading}>
                      {isLoading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Edit className="mr-2 h-4 w-4" />
                      )}
                      Edit Plan
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="features" className="pt-4">
                  <div className="space-y-2">
                    {selectedPlan.features.map((feature: any, index: number) => (
                      <div
                        key={index}
                        className="flex items-center justify-between py-2 border-b border-border/40 last:border-0"
                      >
                        <span className="text-sm">{feature.name}</span>
                        {feature.included ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <X className="h-4 w-4 text-red-600" />
                        )}
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Plan Details</CardTitle>
              <CardDescription>Select a plan to view details</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] flex items-center justify-center text-muted-foreground">
              <p>No plan selected</p>
            </CardContent>
          </Card>
        )}

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Feature Matrix</CardTitle>
            <CardDescription>Compare features across plans</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Feature</TableHead>
                    {plans.map((plan) => (
                      <TableHead key={plan.id} className="text-center">
                        {plan.name}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {plans[0].features.map((feature, featureIndex) => (
                    <TableRow key={featureIndex}>
                      <TableCell>{feature.name}</TableCell>
                      {plans.map((plan) => (
                        <TableCell key={plan.id} className="text-center">
                          {plan.features[featureIndex].included ? (
                            <Check className="h-4 w-4 text-green-600 mx-auto" />
                          ) : (
                            <X className="h-4 w-4 text-red-600 mx-auto" />
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
