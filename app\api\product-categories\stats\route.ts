import { NextRequest, NextResponse } from 'next/server';
import { productCategoryService } from '@/lib/services/product-categories';

// GET - Mendapatkan statistik product categories
export async function GET(request: NextRequest) {
  try {
    const stats = await productCategoryService.getCategoryStats();
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching category stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch category stats' },
      { status: 500 }
    );
  }
}
