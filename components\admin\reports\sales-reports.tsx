"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Download, Calendar, Filter, RefreshCw, Plus, FileText, Mail, Share2 } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"

// Sample data for report templates
const reportTemplates = [
  {
    id: "TMPL-001",
    name: "Monthly Sales Summary",
    description: "Overview of sales performance for the month",
    lastGenerated: "2023-05-01",
  },
  {
    id: "TMPL-002",
    name: "Product Category Sales",
    description: "Sales breakdown by product category",
    lastGenerated: "2023-04-28",
  },
  {
    id: "TMPL-003",
    name: "Sales by Store",
    description: "Comparison of sales across different stores",
    lastGenerated: "2023-04-25",
  },
  {
    id: "TMPL-004",
    name: "Revenue Trends",
    description: "Analysis of revenue trends over time",
    lastGenerated: "2023-04-22",
  },
  {
    id: "TMPL-005",
    name: "Top Selling Products",
    description: "Ranking of best-selling products",
    lastGenerated: "2023-04-20",
  },
]

// Sample data for recent reports
const recentReports = [
  {
    id: "REP-001",
    name: "April 2023 Sales Summary",
    generated: "2023-05-02T09:30:00",
    format: "PDF",
    size: "2.4 MB",
  },
  {
    id: "REP-002",
    name: "Q1 2023 Revenue Analysis",
    generated: "2023-04-15T14:15:00",
    format: "Excel",
    size: "4.8 MB",
  },
  {
    id: "REP-003",
    name: "March 2023 Product Sales",
    generated: "2023-04-05T11:45:00",
    format: "PDF",
    size: "3.1 MB",
  },
  {
    id: "REP-004",
    name: "Store Performance Q1 2023",
    generated: "2023-04-02T16:20:00",
    format: "CSV",
    size: "1.7 MB",
  },
]

export function SalesReports() {
  const [activeTab, setActiveTab] = useState("builder")
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [reportProgress, setReportProgress] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date)
  }

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    }).format(date)
  }

  // Simulate report generation
  const generateReport = () => {
    setIsGenerating(true)
    setReportProgress(0)

    const interval = setInterval(() => {
      setReportProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsGenerating(false)
          return 100
        }
        return prev + 10
      })
    }, 500)
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Sales Reports</h1>
          <p className="text-muted-foreground">Generate and manage sales performance reports</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="default">
            <Plus className="mr-2 h-4 w-4" />
            New Report
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="builder">Report Builder</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="recent">Recent Reports</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
        </TabsList>

        <TabsContent value="builder" className="mt-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Sales Report Builder</CardTitle>
                  <CardDescription>Configure parameters to generate a custom sales report</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="report-name">Report Name</Label>
                      <Input id="report-name" placeholder="Enter report name" />
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="date-range">Date Range</Label>
                        <Select defaultValue="last30">
                          <SelectTrigger id="date-range">
                            <SelectValue placeholder="Select date range" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="last7">Last 7 days</SelectItem>
                            <SelectItem value="last30">Last 30 days</SelectItem>
                            <SelectItem value="last90">Last 90 days</SelectItem>
                            <SelectItem value="lastYear">Last year</SelectItem>
                            <SelectItem value="custom">Custom range</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="report-format">Report Format</Label>
                        <Select defaultValue="pdf">
                          <SelectTrigger id="report-format">
                            <SelectValue placeholder="Select format" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pdf">PDF Document</SelectItem>
                            <SelectItem value="excel">Excel Spreadsheet</SelectItem>
                            <SelectItem value="csv">CSV File</SelectItem>
                            <SelectItem value="json">JSON Data</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="data-grouping">Data Grouping</Label>
                      <Select defaultValue="daily">
                        <SelectTrigger id="data-grouping">
                          <SelectValue placeholder="Select grouping" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="hourly">Hourly</SelectItem>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="quarterly">Quarterly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Data to Include</Label>
                      <div className="mt-2 grid gap-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="revenue" defaultChecked />
                          <label
                            htmlFor="revenue"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Revenue
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="orders" defaultChecked />
                          <label
                            htmlFor="orders"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Order Count
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="avg-order" defaultChecked />
                          <label
                            htmlFor="avg-order"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Average Order Value
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="products" defaultChecked />
                          <label
                            htmlFor="products"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Product Breakdown
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="categories" />
                          <label
                            htmlFor="categories"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Category Breakdown
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="stores" />
                          <label
                            htmlFor="stores"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Store Comparison
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="tenants" />
                          <label
                            htmlFor="tenants"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Tenant Comparison
                          </label>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label>Visualization Options</Label>
                      <div className="mt-2 grid gap-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="bar-charts" defaultChecked />
                          <label
                            htmlFor="bar-charts"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Bar Charts
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="line-charts" defaultChecked />
                          <label
                            htmlFor="line-charts"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Line Charts
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="pie-charts" />
                          <label
                            htmlFor="pie-charts"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Pie Charts
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="data-tables" defaultChecked />
                          <label
                            htmlFor="data-tables"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Data Tables
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline">Save as Template</Button>
                  <Button onClick={generateReport} disabled={isGenerating}>
                    {isGenerating ? "Generating..." : "Generate Report"}
                  </Button>
                </CardFooter>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Report Preview</CardTitle>
                  <CardDescription>Preview of your report configuration</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isGenerating ? (
                    <div className="space-y-4">
                      <div className="text-center">
                        <FileText className="mx-auto h-16 w-16 text-primary/60" />
                        <h3 className="mt-2 font-medium">Generating Report</h3>
                        <p className="text-sm text-muted-foreground">Please wait while we process your report</p>
                      </div>
                      <Progress value={reportProgress} className="h-2 w-full" />
                      <p className="text-center text-sm text-muted-foreground">{reportProgress}% Complete</p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center space-y-4 py-8">
                      <div className="flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
                        <BarChart className="h-10 w-10 text-primary" />
                      </div>
                      <div className="text-center">
                        <h3 className="font-medium">Sales Report</h3>
                        <p className="text-sm text-muted-foreground">Configure and generate your report</p>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <h3 className="font-medium">Report Summary</h3>
                    <div className="rounded-md bg-muted p-4">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="text-muted-foreground">Date Range:</div>
                        <div>Last 30 days</div>
                        <div className="text-muted-foreground">Format:</div>
                        <div>PDF Document</div>
                        <div className="text-muted-foreground">Grouping:</div>
                        <div>Daily</div>
                        <div className="text-muted-foreground">Data Points:</div>
                        <div>4 selected</div>
                        <div className="text-muted-foreground">Visualizations:</div>
                        <div>3 selected</div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Delivery Options</h3>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="email-delivery" />
                        <label
                          htmlFor="email-delivery"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Email report when complete
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="schedule-recurring" />
                        <label
                          htmlFor="schedule-recurring"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Schedule as recurring report
                        </label>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" size="sm" className="w-full">
                    <Mail className="mr-2 h-4 w-4" />
                    Email Options
                  </Button>
                  <Button variant="outline" size="sm" className="w-full">
                    <Calendar className="mr-2 h-4 w-4" />
                    Schedule
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="templates" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Report Templates</CardTitle>
                  <CardDescription>Pre-configured sales report templates</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Input placeholder="Search templates..." className="h-8 w-[200px]" />
                  <Button size="sm">
                    <Plus className="mr-2 h-4 w-4" />
                    New Template
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {reportTemplates.map((template) => (
                  <Card
                    key={template.id}
                    className={`cursor-pointer transition-all hover:border-primary ${
                      selectedTemplate === template.id ? "border-2 border-primary" : ""
                    }`}
                    onClick={() => setSelectedTemplate(template.id)}
                  >
                    <CardHeader className="p-4">
                      <CardTitle className="text-base">{template.name}</CardTitle>
                      <CardDescription className="text-xs">{template.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      <div className="text-xs text-muted-foreground">
                        Last generated: {formatDate(template.lastGenerated)}
                      </div>
                    </CardContent>
                    <CardFooter className="p-4 pt-0">
                      <Button size="sm" variant="outline" className="w-full">
                        Use Template
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Import Template</Button>
              <Button disabled={!selectedTemplate}>Generate from Template</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Sales Reports</CardTitle>
                  <CardDescription>Previously generated sales reports</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Input placeholder="Search reports..." className="h-8 w-[200px]" />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[300px]">Report Name</TableHead>
                    <TableHead>Generated</TableHead>
                    <TableHead>Format</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentReports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell className="font-medium">{report.name}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-xs">{formatDate(report.generated)}</span>
                          <span className="text-xs text-muted-foreground">{formatTime(report.generated)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{report.format}</Badge>
                      </TableCell>
                      <TableCell>{report.size}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Share2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">Showing 4 of 24 reports</div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  Previous
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Scheduled Sales Reports</CardTitle>
                  <CardDescription>Automatically generated reports on schedule</CardDescription>
                </div>
                <Button>
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule New Report
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="p-6 text-center">
                  <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-lg font-medium">No Scheduled Reports</h3>
                  <p className="mt-1 text-sm text-muted-foreground">You haven't scheduled any sales reports yet.</p>
                  <Button className="mt-4">Schedule Your First Report</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Export Tools</CardTitle>
            <CardDescription>Export your sales data in various formats</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <div className="flex items-center gap-4 rounded-lg border p-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                  <FileText className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">PDF Export</h3>
                  <p className="text-sm text-muted-foreground">Export reports as PDF documents</p>
                </div>
                <Button className="ml-auto" variant="outline" size="sm">
                  Export
                </Button>
              </div>
              <div className="flex items-center gap-4 rounded-lg border p-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                  <BarChart className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Excel Export</h3>
                  <p className="text-sm text-muted-foreground">Export data as Excel spreadsheets</p>
                </div>
                <Button className="ml-auto" variant="outline" size="sm">
                  Export
                </Button>
              </div>
              <div className="flex items-center gap-4 rounded-lg border p-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                  <LineChart className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">CSV Export</h3>
                  <p className="text-sm text-muted-foreground">Export raw data as CSV files</p>
                </div>
                <Button className="ml-auto" variant="outline" size="sm">
                  Export
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Visualization Options</CardTitle>
            <CardDescription>Customize how your data is visualized</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="chart-type">Chart Type</Label>
                <Select defaultValue="bar">
                  <SelectTrigger id="chart-type">
                    <SelectValue placeholder="Select chart type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bar">Bar Chart</SelectItem>
                    <SelectItem value="line">Line Chart</SelectItem>
                    <SelectItem value="pie">Pie Chart</SelectItem>
                    <SelectItem value="area">Area Chart</SelectItem>
                    <SelectItem value="scatter">Scatter Plot</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="color-scheme">Color Scheme</Label>
                <Select defaultValue="default">
                  <SelectTrigger id="color-scheme">
                    <SelectValue placeholder="Select color scheme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default</SelectItem>
                    <SelectItem value="monochrome">Monochrome</SelectItem>
                    <SelectItem value="colorful">Colorful</SelectItem>
                    <SelectItem value="pastel">Pastel</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Additional Options</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="show-legend" defaultChecked />
                    <label
                      htmlFor="show-legend"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Show legend
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="show-grid" defaultChecked />
                    <label
                      htmlFor="show-grid"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Show grid lines
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="show-labels" defaultChecked />
                    <label
                      htmlFor="show-labels"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Show data labels
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="interactive" defaultChecked />
                    <label
                      htmlFor="interactive"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Interactive charts
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full">Apply Visualization Settings</Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
