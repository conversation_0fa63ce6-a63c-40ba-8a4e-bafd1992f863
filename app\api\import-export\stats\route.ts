import { NextRequest, NextResponse } from 'next/server';
import { productImportExportService } from '@/lib/services/product-import-export';

// GET - Mendapatkan statistik import/export jobs
export async function GET(request: NextRequest) {
  try {
    const stats = await productImportExportService.getJobStats();
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching import/export stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch import/export stats' },
      { status: 500 }
    );
  }
}
