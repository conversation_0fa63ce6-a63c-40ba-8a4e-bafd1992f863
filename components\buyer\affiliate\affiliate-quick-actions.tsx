"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Link, Copy, QrCode, ExternalLink, HelpCircle } from "lucide-react"
import { formatCurrency } from "@/lib/utils"

export function AffiliateQuickActions() {
  const [url, setUrl] = useState("")
  const [generatedLink, setGeneratedLink] = useState("")
  const [copied, setCopied] = useState(false)
  const [linkType, setLinkType] = useState<"product" | "store" | "custom">("product")

  const generateLink = () => {
    // Logika untuk generate link berdasarkan tipe
    const baseUrl = "https://sellzio.com"
    const refCode = "USER123"

    let generatedUrl = ""
    if (linkType === "product" && url) {
      generatedUrl = `${baseUrl}/product/${url}?ref=${refCode}`
    } else if (linkType === "store" && url) {
      generatedUrl = `${baseUrl}/store/${url}?ref=${refCode}`
    } else if (linkType === "custom" && url) {
      // Validasi URL
      try {
        const urlObj = new URL(url)
        generatedUrl = `${urlObj.toString()}${urlObj.search ? "&" : "?"}ref=${refCode}`
      } catch (e) {
        generatedUrl = `${baseUrl}/${url}?ref=${refCode}`
      }
    }

    setGeneratedLink(generatedUrl)
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedLink)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <Card className="col-span-3">
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
        <CardDescription>Generate link affiliate, akses materi marketing, dan lainnya</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Tabs defaultValue="generate-link" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="generate-link">Generate Link</TabsTrigger>
            <TabsTrigger value="marketing">Marketing</TabsTrigger>
            <TabsTrigger value="support">Support</TabsTrigger>
          </TabsList>

          <TabsContent value="generate-link" className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="link-type">Tipe Link</Label>
              <div className="flex space-x-2">
                <Button
                  variant={linkType === "product" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setLinkType("product")}
                >
                  Produk
                </Button>
                <Button
                  variant={linkType === "store" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setLinkType("store")}
                >
                  Toko
                </Button>
                <Button
                  variant={linkType === "custom" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setLinkType("custom")}
                >
                  Custom URL
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="url-input">
                {linkType === "product"
                  ? "ID atau Slug Produk"
                  : linkType === "store"
                    ? "ID atau Slug Toko"
                    : "URL Custom"}
              </Label>
              <div className="flex space-x-2">
                <Input
                  id="url-input"
                  placeholder={
                    linkType === "product"
                      ? "Contoh: sepatu-running-123"
                      : linkType === "store"
                        ? "Contoh: toko-fashion"
                        : "Contoh: https://example.com/page"
                  }
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                />
                <Button onClick={generateLink}>Generate</Button>
              </div>
            </div>

            {generatedLink && (
              <div className="space-y-2 rounded-md border p-3">
                <Label>Link Affiliate Anda:</Label>
                <div className="flex items-center space-x-2">
                  <Input value={generatedLink} readOnly className="flex-1" />
                  <Button size="icon" variant="outline" onClick={copyToClipboard}>
                    {copied ? <span className="text-xs">✓</span> : <Copy className="h-4 w-4" />}
                  </Button>
                  <Button size="icon" variant="outline">
                    <QrCode className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="marketing" className="space-y-4 pt-4">
            <div className="grid gap-2">
              <Button variant="outline" className="justify-start">
                <Link className="mr-2 h-4 w-4" />
                Akses Banner & Materi Marketing
              </Button>
              <Button variant="outline" className="justify-start">
                <ExternalLink className="mr-2 h-4 w-4" />
                Panduan Promosi Efektif
              </Button>
              <Button variant="outline" className="justify-start">
                <QrCode className="mr-2 h-4 w-4" />
                Generate QR Code Produk
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="support" className="space-y-4 pt-4">
            <div className="grid gap-2">
              <Button variant="outline" className="justify-start">
                <HelpCircle className="mr-2 h-4 w-4" />
                FAQ Affiliate Program
              </Button>
              <Button variant="outline" className="justify-start">
                <ExternalLink className="mr-2 h-4 w-4" />
                Hubungi Tim Support
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter>
        <div className="w-full rounded-md bg-muted p-3 text-sm">
          <p className="font-medium">Status Pembayaran</p>
          <div className="mt-2 flex items-center justify-between">
            <span>Saldo saat ini:</span>
            <span className="font-medium">{formatCurrency(450000)}</span>
          </div>
          <div className="mt-1 flex items-center justify-between">
            <span>Minimum payout:</span>
            <span className="font-medium">{formatCurrency(100000)}</span>
          </div>
          <Button className="mt-3 w-full">Request Payout</Button>
        </div>
      </CardFooter>
    </Card>
  )
}
