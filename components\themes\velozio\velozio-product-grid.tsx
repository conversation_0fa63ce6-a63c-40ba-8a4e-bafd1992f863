"use client"

import { useState, useEffect } from "react"
import { VelozioProductCard, type ProductCardProps } from "./velozio-product-card"
import { VelozioProductSkeleton } from "./velozio-product-skeleton"
import { VelozioPagination } from "./velozio-pagination"

interface ProductGridProps {
  products: ProductCardProps[]
  loading?: boolean
  itemsPerPage?: number
}

export function VelozioProductGrid({ products, loading = false, itemsPerPage = 8 }: ProductGridProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [displayedProducts, setDisplayedProducts] = useState<ProductCardProps[]>([])
  const totalPages = Math.ceil(products.length / itemsPerPage)

  useEffect(() => {
    if (!loading && products.length > 0) {
      const startIndex = (currentPage - 1) * itemsPerPage
      const endIndex = startIndex + itemsPerPage
      setDisplayedProducts(products.slice(startIndex, endIndex))
    }
  }, [currentPage, products, loading, itemsPerPage])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top of grid
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  return (
    <div className="bg-[#f2f2f2] py-4 px-2 md:py-6 md:px-4">
      <div className="max-w-[1200px] mx-auto">
        {/* Product Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 md:gap-3">
          {loading
            ? // Skeleton loading state
              Array.from({ length: itemsPerPage }).map((_, index) => <VelozioProductSkeleton key={index} />)
            : // Product cards
              displayedProducts.map((product) => <VelozioProductCard key={product.id} {...product} />)}
        </div>

        {/* Pagination */}
        {!loading && totalPages > 1 && (
          <div className="mt-6 flex justify-center">
            <VelozioPagination currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />
          </div>
        )}
      </div>
    </div>
  )
}
