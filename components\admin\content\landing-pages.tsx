"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  LayoutIcon,
  SearchIcon,
  ArrowUpRightIcon,
  BarChart3Icon,
} from "lucide-react"

export function LandingPages() {
  const [landingPages, setLandingPages] = useState([])
  const [selectedPage, setSelectedPage] = useState(null)
  const [activeTab, setActiveTab] = useState("pages")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulasi pengambilan data
    setTimeout(() => {
      const mockLandingPages = [
        {
          id: 1,
          title: "Main Homepage",
          slug: "home",
          thumbnail: "/placeholder.svg?key=wybsz",
          status: "published",
          lastUpdated: "2023-10-15",
          views: 24560,
          conversionRate: "4.2%",
          seoScore: 92,
        },
        {
          id: 2,
          title: "Black Friday Sale",
          slug: "black-friday-2023",
          thumbnail: "/placeholder.svg?key=22i8t",
          status: "scheduled",
          lastUpdated: "2023-10-20",
          views: 0,
          conversionRate: "0%",
          seoScore: 88,
        },
        {
          id: 3,
          title: "Seller Registration",
          slug: "become-a-seller",
          thumbnail: "/placeholder.svg?key=j3yit",
          status: "published",
          lastUpdated: "2023-09-05",
          views: 8750,
          conversionRate: "6.8%",
          seoScore: 85,
        },
        {
          id: 4,
          title: "Summer Collection",
          slug: "summer-collection-2023",
          thumbnail: "/placeholder.svg?key=sqqnl",
          status: "published",
          lastUpdated: "2023-06-01",
          views: 15320,
          conversionRate: "3.5%",
          seoScore: 90,
        },
        {
          id: 5,
          title: "Holiday Gift Guide",
          slug: "holiday-gifts-2023",
          thumbnail: "/placeholder.svg?key=hz1x9",
          status: "draft",
          lastUpdated: "2023-10-18",
          views: 0,
          conversionRate: "0%",
          seoScore: 78,
        },
        {
          id: 6,
          title: "About Us",
          slug: "about",
          thumbnail: "/placeholder.svg?key=8h3sl",
          status: "published",
          lastUpdated: "2023-08-12",
          views: 5680,
          conversionRate: "1.2%",
          seoScore: 95,
        },
      ]

      setLandingPages(mockLandingPages)
      setIsLoading(false)
    }, 1000)
  }, [])

  const handlePageSelect = (page) => {
    setSelectedPage(page)
    setActiveTab("editor")
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800"
      case "draft":
        return "bg-yellow-100 text-yellow-800"
      case "scheduled":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (isLoading) {
    return (
      <div className="grid gap-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <div className="aspect-video w-full bg-gray-200"></div>
              <CardHeader>
                <div className="h-5 w-32 rounded bg-gray-200"></div>
                <div className="h-4 w-48 rounded bg-gray-200"></div>
              </CardHeader>
              <CardFooter>
                <div className="h-9 w-full rounded bg-gray-200"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-3 lg:w-[400px]">
        <TabsTrigger value="pages">All Pages</TabsTrigger>
        <TabsTrigger value="editor" disabled={!selectedPage}>
          Page Editor
        </TabsTrigger>
        <TabsTrigger value="analytics">Analytics</TabsTrigger>
      </TabsList>

      {/* All Pages Tab */}
      <TabsContent value="pages" className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-bold">Landing Pages</h2>
            <p className="text-sm text-muted-foreground">Manage your platform landing pages</p>
          </div>
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" />
            Create New Page
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search pages..." className="pl-8" />
          </div>
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="scheduled">Scheduled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {landingPages.map((page) => (
            <Card key={page.id} className="overflow-hidden">
              <div className="aspect-video w-full overflow-hidden">
                <img
                  src={page.thumbnail || "/placeholder.svg"}
                  alt={page.title}
                  className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                />
              </div>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>{page.title}</CardTitle>
                  <Badge variant="outline" className={getStatusColor(page.status)}>
                    {page.status}
                  </Badge>
                </div>
                <CardDescription>/{page.slug}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between text-sm">
                  <span>Last updated: {page.lastUpdated}</span>
                  <span>SEO Score: {page.seoScore}/100</span>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" onClick={() => handlePageSelect(page)}>
                  <PencilIcon className="mr-2 h-4 w-4" />
                  Edit
                </Button>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <EyeIcon className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                  <Button variant="outline" size="sm" className="text-red-500 hover:text-red-700">
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </TabsContent>

      {/* Page Editor Tab */}
      <TabsContent value="editor" className="space-y-6">
        {selectedPage && (
          <>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h2 className="text-2xl font-bold">Editing: {selectedPage.title}</h2>
                <p className="text-sm text-muted-foreground">/{selectedPage.slug}</p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline">
                  <EyeIcon className="mr-2 h-4 w-4" />
                  Preview
                </Button>
                <Select defaultValue={selectedPage.status}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                  </SelectContent>
                </Select>
                <Button>Save Changes</Button>
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-[1fr_3fr]">
              {/* Page Settings Panel */}
              <Card>
                <CardHeader>
                  <CardTitle>Page Settings</CardTitle>
                  <CardDescription>Configure page properties</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="page-title">Page Title</Label>
                    <Input id="page-title" defaultValue={selectedPage.title} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="page-slug">URL Slug</Label>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">/</span>
                      <Input id="page-slug" defaultValue={selectedPage.slug} />
                    </div>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <Label>SEO Settings</Label>
                    <div className="space-y-4 rounded-md border p-4">
                      <div className="space-y-2">
                        <Label htmlFor="meta-title">Meta Title</Label>
                        <Input id="meta-title" defaultValue={selectedPage.title} />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="meta-description">Meta Description</Label>
                        <Textarea id="meta-description" placeholder="Enter a description for search engines..." />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="meta-keywords">Meta Keywords</Label>
                        <Input id="meta-keywords" placeholder="keyword1, keyword2, keyword3" />
                      </div>
                    </div>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <Label>A/B Testing</Label>
                    <div className="rounded-md border p-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Enable A/B Testing</span>
                        <div className="relative h-4 w-8 cursor-pointer rounded-full bg-gray-200">
                          <div className="absolute left-0 h-4 w-4 rounded-full bg-gray-400"></div>
                        </div>
                      </div>
                      <p className="mt-2 text-xs text-muted-foreground">
                        Create variations of this page to test different designs and content.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Visual Page Builder */}
              <Card>
                <CardHeader>
                  <CardTitle>Visual Page Builder</CardTitle>
                  <CardDescription>Drag and drop elements to build your page</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex h-[600px] flex-col items-center justify-center rounded-md border-2 border-dashed p-12 text-center">
                    <LayoutIcon className="mb-4 h-12 w-12 text-muted-foreground" />
                    <h3 className="mb-2 text-xl font-medium">Visual Page Builder</h3>
                    <p className="mb-4 text-sm text-muted-foreground">
                      Drag and drop components to create your landing page.
                    </p>
                    <Button>Open Page Builder</Button>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline">Discard Changes</Button>
                  <Button>Save Page</Button>
                </CardFooter>
              </Card>
            </div>
          </>
        )}
      </TabsContent>

      {/* Analytics Tab */}
      <TabsContent value="analytics" className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-bold">Page Analytics</h2>
            <p className="text-sm text-muted-foreground">Performance metrics for your landing pages</p>
          </div>
          <Select defaultValue="30days">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="year">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Analytics Overview Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardDescription>Total Page Views</CardDescription>
              <CardTitle className="text-3xl">54,280</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                <span className="text-green-500">↑ 12.5%</span> vs previous period
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardDescription>Average Time on Page</CardDescription>
              <CardTitle className="text-3xl">2:45</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                <span className="text-green-500">↑ 8.3%</span> vs previous period
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardDescription>Conversion Rate</CardDescription>
              <CardTitle className="text-3xl">4.8%</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                <span className="text-red-500">↓ 1.2%</span> vs previous period
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardDescription>Bounce Rate</CardDescription>
              <CardTitle className="text-3xl">38.2%</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                <span className="text-green-500">↑ 3.5%</span> vs previous period
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Page Performance Table */}
        <Card>
          <CardHeader>
            <CardTitle>Page Performance</CardTitle>
            <CardDescription>Analytics for individual landing pages</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative overflow-x-auto">
              <table className="w-full text-left text-sm">
                <thead className="bg-gray-50 text-xs uppercase">
                  <tr>
                    <th scope="col" className="px-6 py-3">
                      Page Title
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Views
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Avg. Time
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Conversion Rate
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Bounce Rate
                    </th>
                    <th scope="col" className="px-6 py-3">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {landingPages
                    .filter((page) => page.status === "published")
                    .sort((a, b) => b.views - a.views)
                    .map((page) => (
                      <tr key={page.id} className="border-b bg-white">
                        <td className="px-6 py-4 font-medium">{page.title}</td>
                        <td className="px-6 py-4">{page.views.toLocaleString()}</td>
                        <td className="px-6 py-4">2:18</td>
                        <td className="px-6 py-4">{page.conversionRate}</td>
                        <td className="px-6 py-4">42.3%</td>
                        <td className="px-6 py-4">
                          <div className="flex gap-2">
                            <Button variant="ghost" size="sm">
                              <BarChart3Icon className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <ArrowUpRightIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* A/B Testing Results */}
        <Card>
          <CardHeader>
            <CardTitle>A/B Testing Results</CardTitle>
            <CardDescription>Performance comparison of page variations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex h-40 items-center justify-center rounded-md border-2 border-dashed">
              <p className="text-center text-muted-foreground">No active A/B tests at the moment</p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
