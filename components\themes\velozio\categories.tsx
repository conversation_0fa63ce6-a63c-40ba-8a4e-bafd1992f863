"use client"

import { useState, useEffect, useRef, ReactNode } from "react"
import { motion, AnimatePresence } from "framer-motion"
import React from "react"
import { Search, X } from 'lucide-react'
import SubcategoryView from "./SubcategoryView"
import type { Category } from "./types"

// Fungsi helper untuk menghasilkan ID unik yang konsisten
function generateId(prefix: string, name: string): string {
  // Gunakan hash sederhana untuk membuat ID yang konsisten
  const str = `${prefix}-${name}`.toLowerCase().replace(/\s+/g, '-')
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32bit integer
  }
  return `${str}-${Math.abs(hash).toString(36).substr(0, 8)}`
}

// Menggunakan tipe Category dari types.ts

// Fungsi untuk mendapatkan ikon berdasarkan nama ikon
const getIcon = (iconName: string): React.ReactNode => {
  return iconComponents[iconName] || iconComponents['more-horizontal'];
};

// SVG Icons mapping
const iconComponents: Record<string, React.ReactNode> = {
  'list': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 10h18M3 6h18M3 14h18M3 18h18"></path></svg>,
  'device-mobile': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><path d="M8 6h.01M16 6h.01M12 6h.01M8 10h.01M16 10h.01M12 10h.01M8 14h.01M16 14h.01M12 14h.01M8 18h.01M16 18h.01M12 18h.01"></path></svg>,
  'monitor': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect><line x1="8" y1="21" x2="16" y2="21"></line><line x1="12" y1="17" x2="12" y2="21"></line></svg>,
  'tool': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path></svg>,
  'shirt': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z"></path></svg>,
  'home': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>,
  'smile': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M8 14s1.5 2 4 2 4-2 4-2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line></svg>,
  'headphones': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 18v-6a9 9 0 0 1 18 0v6"></path><path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"></path></svg>,
  'gamepad': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="2" y="6" width="20" height="12" rx="2"></rect><path d="M12 12h.01"></path><path d="M17 10V8"></path><path d="M19 12h.01"></path><path d="M17 14v2"></path><path d="M7 12h.01"></path><path d="M5 10v2"></path><path d="M5 14v-2"></path></svg>,
  'book': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path></svg>,
  'kesehatan': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"></path></svg>,
  'heart': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg>,
  'gift': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M20 12v10H4V12"></path><path d="M22 7H2v5h20V7z"></path><path d="M12 22V7"></path><path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"></path><path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"></path></svg>,
  'camera': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path><circle cx="12" cy="13" r="3"></circle></svg>,
  'car': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="1" y="6" width="22" height="12" rx="6"></rect><circle cx="7" cy="12" r="3"></circle><circle cx="17" cy="12" r="3"></circle></svg>,
  'shopping-cart': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>,
  'shopping-bag': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path></svg>,
  'tag': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path><line x1="7" y1="7" x2="7.01" y2="7"></line></svg>,
  'more-horizontal': <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>,
  'map-pin': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>,
  'utensils': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path><path d="M7 2v20"></path><path d="M21 15V2"></path><path d="M18 15v2"></path><path d="M15 15v4"></path><path d="M21 15a3 3 0 1 1-6 0"></path></svg>,
  'activity': <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline></svg>,
  'makanan': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
  <path d="M6 12h12c2.2 0 4-1.8 4-4s-1.8-4-4-4H6c-2.2 0-4 1.8-4 4s1.8 4 4 4z"></path>
  <path d="M6 12v8a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-8"></path>
</svg>,
  'smartphone': <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line></svg>,
  'see-all': <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 40 40">
    <rect x="2" y="2" width="36" height="36" rx="10" fill="#FFF5F0" stroke="#FFDFD1" strokeWidth="2"/>
    <rect x="6" y="6" width="12" height="12" rx="3" fill="#FF5722"/>
    <rect x="22" y="6" width="12" height="12" rx="3" fill="#FF5722"/>
    <rect x="6" y="22" width="12" height="12" rx="3" fill="#FF5722"/>
    <rect x="22" y="22" width="12" height="12" rx="3" fill="#FF5722"/>
  </svg>,
  'close-all': <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 40 40">
    <rect x="2" y="2" width="36" height="36" rx="10" fill="#F5F5F5" stroke="#E0E0E0" strokeWidth="2"/>
    <path d="M13 13L27 27M13 27L27 13" stroke="#555" strokeWidth="3" strokeLinecap="round"/>
  </svg>,
  'chevron-up': <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="18 15 12 9 6 15"></polyline></svg>
}

const allCategories: Category[] = [
  { id: generateId('kategori', 'elektronik'), name: "Elektronik", icon: 'device-mobile', color: "#1BA0E2" },
  { id: generateId('kategori', 'fashion'), name: "Fashion", icon: 'shirt', color: "#F1C40F" },
  { id: generateId('kategori', 'makanan'), name: "Makanan", icon: 'utensils', color: "#F25C05" },
  { id: generateId('kategori', 'belanja'), name: "Belanja", icon: 'shopping-cart', color: "#E74C3C" },
  { id: generateId('kategori', 'game'), name: "Game", icon: 'gamepad', color: "#3498DB" },
  { id: generateId('kategori', 'lokal'), name: "Lokal", icon: 'map-pin', color: "#9B59B6" },
  { id: generateId('kategori', 'kecantikan'), name: "Kecantikan", icon: 'heart', color: "#E84393" },
  { id: generateId('kategori', 'otomotif'), name: "Otomotif", icon: 'car', color: "#2C3E50" },
  { id: generateId('kategori', 'komputer'), name: "Komputer", icon: 'monitor', color: "#5D6D7E" },
  { id: generateId('kategori', 'hobi'), name: "Hobi", icon: 'camera', color: "#1ABC9C" },
  { id: generateId('kategori', 'rumah'), name: "Rumah", icon: 'home', color: "#8E44AD" },
  { id: generateId('kategori', 'kesehatan'), name: "Kesehatan", icon: 'kesehatan', color: "#2ECC71" },
  { id: generateId('kategori', 'olahraga'), name: "Olahraga", icon: 'activity', color: "#E67E22" },
  { id: generateId('kategori', 'mainan'), name: "Mainan", icon: 'gift', color: "#FF6B81" },
  { id: generateId('kategori', 'bayi'), name: "Bayi", icon: 'smile', color: "#FDA7DF" },
  { id: generateId('kategori', 'pendidikan'), name: "Pendidikan", icon: 'book', color: "#4834DF" }
]

const CategoryItem = ({ category, onClick, isExpandedView = false }: { 
  category: Category, 
  onClick?: (e: React.MouseEvent<Element>) => void, 
  isExpandedView?: boolean 
}) => (
  <motion.div
    className={`
      flex flex-col items-center justify-center rounded-lg transition-all duration-200
      border border-gray-200 bg-white hover:bg-gray-50 shadow-sm
      ${isExpandedView ? 'p-2 w-[100px] h-[100px]' : 'p-1.5 w-[80px] h-[80px] mx-0.5'}
      overflow-hidden
    `}
    initial={{ opacity: 0, y: 5 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={(e: React.MouseEvent) => {
      e.preventDefault()
      onClick?.(e)
    }}
  >
    <div className={`flex items-center justify-center ${isExpandedView ? 'mb-2' : 'mb-1'}`} style={{ height: '32px', width: '100%' }}>
      <div 
        className="flex items-center justify-center w-8 h-8" 
        style={{ color: category.color, minWidth: '32px' }}
      >
        {getIcon(category.icon)}
      </div>
    </div>
    <div className="w-full px-1 flex justify-center">
      <span className={`
        text-center text-gray-800 leading-tight break-words
        ${isExpandedView ? 'text-xs' : 'text-[10px] line-clamp-2'}
        w-full
      `}>
        {category.name}
      </span>
    </div>
  </motion.div>
)

// Special "See All" card component
const SeeAllItem = ({ 
  onClick, 
  isExpanded 
}: { 
  onClick: (e: React.MouseEvent<Element>) => void, 
  isExpanded: boolean 
}) => {
  return (
    <motion.div
      className={`see-all-card flex flex-col items-center justify-center rounded-lg transition-all duration-200 p-2 w-[80px] h-[80px] mx-0.5
      ${isExpanded ? 'bg-[#F5F5F5] border border-[#f0f0f0]' : 'bg-[#FFF5F0] border border-[#f0f0f0]'}
      overflow-hidden`}
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02, backgroundColor: isExpanded ? '#E0E0E0' : '#FFE8E0' }}
      whileTap={{ scale: 0.98 }}
      onClick={(e: React.MouseEvent<HTMLDivElement>) => {
        e.preventDefault()
        onClick(e)
      }}
    >
      <div className="flex items-center justify-center w-8 h-8 mb-2">
        <div className="flex items-center justify-center w-full h-full">
          {iconComponents[isExpanded ? 'close-all' : 'see-all']}
        </div>
      </div>
      <div className="w-full px-1">
        <span className="text-center text-xs text-gray-800 leading-tight block w-full">
          {isExpanded ? "Close" : "View All"}
        </span>
      </div>
    </motion.div>
  )
}


// Definisi sub-kategori dengan tipe yang benar
const subCategories: Record<string, Category[]> = {
  "Lokal": [
    { id: generateId('subkategori', 'produk-umkm'), name: "Produk UMKM", icon: 'home', color: "#9B59B6" },
    { id: generateId('subkategori', 'kerajinan-lokal'), name: "Kerajinan Lokal", icon: 'tool', color: "#9B59B6" },
    { id: generateId('subkategori', 'makanan-khas'), name: "Makanan Khas", icon: 'utensils', color: "#9B59B6" },
  ],
  "Kecantikan": [
    { id: generateId('subkategori', 'skincare'), name: "Skincare", icon: 'droplet', color: "#E84393" },
    { id: generateId('subkategori', 'makeup'), name: "Makeup", icon: 'smile', color: "#E84393" },
    { id: generateId('subkategori', 'perawatan-rambut'), name: "Perawatan Rambut", icon: 'scissors', color: "#E84393" },
  ],
  "Otomotif": [
    { id: generateId('subkategori', 'aksesoris-mobil'), name: "Aksesoris Mobil", icon: 'car', color: "#2C3E50" },
    { id: generateId('subkategori', 'aksesoris-motor'), name: "Aksesoris Motor", icon: 'activity', color: "#2C3E50" },
    { id: generateId('subkategori', 'perawatan-kendaraan'), name: "Perawatan Kendaraan", icon: 'tool', color: "#2C3E50" },
  ],
  "Komputer": [
    { id: generateId('subkategori', 'laptop'), name: "Laptop", icon: 'laptop', color: "#5D6D7E" },
    { id: generateId('subkategori', 'pc-desktop'), name: "PC Desktop", icon: 'monitor', color: "#5D6D7E" },
    { id: generateId('subkategori', 'aksesoris-komputer'), name: "Aksesoris Komputer", icon: 'keyboard', color: "#5D6D7E" },
  ],
  "Hobi": [
    { id: generateId('subkategori', 'fotografi'), name: "Fotografi", icon: 'camera', color: "#1ABC9C" },
    { id: generateId('subkategori', 'musik'), name: "Musik", icon: 'music', color: "#1ABC9C" },
    { id: generateId('subkategori', 'koleksi'), name: "Koleksi", icon: 'archive', color: "#1ABC9C" },
  ],
  "Rumah": [
    { id: generateId('subkategori', 'furniture'), name: "Furniture", icon: 'inbox', color: "#8E44AD" },
    { id: generateId('subkategori', 'dekorasi'), name: "Dekorasi", icon: 'image', color: "#8E44AD" },
    { id: generateId('subkategori', 'perlengkapan-dapur'), name: "Perlengkapan Dapur", icon: 'coffee', color: "#8E44AD" },
  ],
  "Kesehatan": [
    { id: generateId('subkategori', 'obat-obatan'), name: "Obat-obatan", icon: 'thermometer', color: "#2ECC71" },
    { id: generateId('subkategori', 'suplemen'), name: "Suplemen", icon: 'package', color: "#2ECC71" },
    { id: generateId('subkategori', 'alat-kesehatan'), name: "Alat Kesehatan", icon: 'activity', color: "#2ECC71" },
  ],
  "Olahraga": [
    { id: generateId('subkategori', 'pakaian-olahraga'), name: "Pakaian Olahraga", icon: 'shirt', color: "#E67E22" },
    { id: generateId('subkategori', 'alat-olahraga'), name: "Alat Olahraga", icon: 'dumbbell', color: "#E67E22" },
    { id: generateId('subkategori', 'sepatu-olahraga'), name: "Sepatu Olahraga", icon: 'boot', color: "#E67E22" },
  ],
  "Mainan": [
    { id: generateId('subkategori', 'mainan-anak'), name: "Mainan Anak", icon: 'truck', color: "#FF6B81" },
    { id: generateId('subkategori', 'mainan-edukatif'), name: "Mainan Edukatif", icon: 'book', color: "#FF6B81" },
    { id: generateId('subkategori', 'action-figure'), name: "Action Figure", icon: 'star', color: "#FF6B81" },
  ],
  "Bayi": [
    { id: generateId('subkategori', 'pakaian-bayi'), name: "Pakaian Bayi", icon: 'shirt', color: "#FDA7DF" },
    { id: generateId('subkategori', 'perlengkapan-bayi'), name: "Perlengkapan Bayi", icon: 'package', color: "#FDA7DF" },
    { id: generateId('subkategori', 'makanan-bayi'), name: "Makanan Bayi", icon: 'coffee', color: "#FDA7DF" },
  ],
  "Pendidikan": [
    { id: generateId('subkategori', 'buku'), name: "Buku", icon: 'book', color: "#4834DF" },
    { id: generateId('subkategori', 'alat-tulis'), name: "Alat Tulis", icon: 'edit', color: "#4834DF" },
    { id: generateId('subkategori', 'kursus-online'), name: "Kursus Online", icon: 'monitor', color: "#4834DF" },
  ],
  "Elektronik": [
    { id: generateId('elektronik', 'handphone'), name: "Handphone", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'laptop'), name: "Laptop", icon: 'monitor', color: "#1BA0E2" },
    { id: generateId('elektronik', 'tablet'), name: "Tablet", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'kamera'), name: "Kamera", icon: 'camera', color: "#1BA0E2" },
    { id: generateId('elektronik', 'audio'), name: "Audio", icon: 'headphones', color: "#1BA0E2" },
    { id: generateId('elektronik', 'aksesoris'), name: "Aksesoris", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'smart-home'), name: "Smart Home", icon: 'home', color: "#1BA0E2" },
    { id: generateId('elektronik', 'gadget'), name: "Gadget", icon: 'device-mobile', color: "#1BA0E2" },
  ],
  "Fashion": [
    { id: generateId('fashion', 'pria'), name: "Pria", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('fashion', 'wanita'), name: "Wanita", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('fashion', 'anak'), name: "Anak", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('fashion', 'muslim'), name: "Muslim", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('fashion', 'tas'), name: "Tas", icon: 'shopping-cart', color: "#F1C40F" },
    { id: generateId('fashion', 'sepatu'), name: "Sepatu", icon: 'shopping-cart', color: "#F1C40F" },
    { id: generateId('fashion', 'jam-tangan'), name: "Jam Tangan", icon: 'shopping-cart', color: "#F1C40F" },
    { id: generateId('fashion', 'aksesoris'), name: "Aksesoris", icon: 'shopping-cart', color: "#F1C40F" },
  ],
  "Makanan": [
    { id: generateId('makanan', 'makanan-instan'), name: "Makanan Instan", icon: 'makanan', color: "#F25C05" },
    { id: generateId('makanan', 'minuman'), name: "Minuman", icon: 'makanan', color: "#F25C05" },
    { id: generateId('makanan', 'bumbu-dapur'), name: "Bumbu Dapur", icon: 'makanan', color: "#F25C05" },
    { id: generateId('makanan', 'kue-camilan'), name: "Kue & Camilan", icon: 'makanan', color: "#F25C05" },
    { id: generateId('makanan', 'bahan-pokok'), name: "Bahan Pokok", icon: 'makanan', color: "#F25C05" },
    { id: generateId('makanan', 'makanan-kaleng'), name: "Makanan Kaleng", icon: 'makanan', color: "#F25C05" },
  ],
  "Belanja": [
    { id: generateId('belanja', 'bulanan'), name: "Belanja Bulanan", icon: 'shopping-bag', color: "#E74C3C" },
    { id: generateId('belanja', 'harian'), name: "Kebutuhan Harian", icon: 'shopping-cart', color: "#E74C3C" },
    { id: generateId('belanja', 'promo'), name: "Promo Spesial", icon: 'tag', color: "#E74C3C" }
  ],
  "Game": [
    { id: generateId('game', 'mobile'), name: "Game Mobile", icon: 'smartphone', color: "#3498DB" },
    { id: generateId('game', 'pc'), name: "Game PC", icon: 'monitor', color: "#3498DB" },
    { id: generateId('game', 'aksesori'), name: "Aksesori Gaming", icon: 'headphones', color: "#3498DB" }
  ],
}

export default function Categories() {
  const [isMobile, setIsMobile] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [bannerKey, setBannerKey] = useState(0); // Key untuk memaksa update banner
  const [showScrollHint, setShowScrollHint] = useState(false)
  const [isScrolling, setIsScrolling] = useState(false)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const initialShowTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const showIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const mobileContainerRef = useRef<HTMLDivElement>(null)
  const expandedRef = useRef<HTMLDivElement>(null)
  const [isScrollAtStart, setIsScrollAtStart] = useState(true)
  const [isScrollAtEnd, setIsScrollAtEnd] = useState(false)
  const [visibleCategories, setVisibleCategories] = useState<Category[]>(allCategories);
  const [isShowingAllOnDesktop, setIsShowingAllOnDesktop] = useState(false)
  const [showSubcategoryView, setShowSubcategoryView] = useState(false)

  // State untuk kontrol tombol navigasi
  const [showLeftNav, setShowLeftNav] = useState(false)
  const [showRightNav, setShowRightNav] = useState(false)
  const desktopContainerRef = useRef<HTMLDivElement>(null)
  
  // State untuk menyimpan daftar kategori yang sudah diurutkan
  const [sortedCategories, setSortedCategories] = useState<Category[]>([])
  
  // Inisialisasi sortedCategories dengan referensi yang konsisten
  useEffect(() => {
    setSortedCategories([...allCategories])
  }, [])

  // Initialize categories based on screen size
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
      
      // Reset expanded state on resize
      if (!mobile && isExpanded) {
        setIsExpanded(false)
      }
      
      // Untuk tampilan desktop, tampilkan lebih banyak kategori
      const newVisibleCategories = mobile ? allCategories : allCategories
      setVisibleCategories(newVisibleCategories)
      
      // Hanya update sortedCategories jika belum diinisialisasi
      setSortedCategories(prev => prev.length ? prev : [...newVisibleCategories])
    }

    // Add scroll event listener for mobile container
    const mobileContainer = mobileContainerRef.current;
    if (mobileContainer) {
      mobileContainer.addEventListener('scroll', handleScroll);
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', checkMobile);
      if (mobileContainer) {
        mobileContainer.removeEventListener('scroll', handleScroll);
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [isExpanded])
  
  // Cek apakah perlu menampilkan tombol navigasi
  useEffect(() => {
    if (isMobile || !desktopContainerRef.current) return
    
    const checkScroll = () => {
      const container = desktopContainerRef.current
      if (!container) return
      
      // Cek apakah ada konten di sebelah kiri yang tidak terlihat
      setShowLeftNav(container.scrollLeft > 10)
      
      // Cek apakah ada konten di sebelah kanan yang tidak terlihat
      setShowRightNav(container.scrollLeft < (container.scrollWidth - container.clientWidth - 10))
    }
    
    const container = desktopContainerRef.current
    container.addEventListener('scroll', checkScroll)
    // Cek setelah render
    setTimeout(checkScroll, 100)
    
    return () => container.removeEventListener('scroll', checkScroll)
  }, [isMobile, visibleCategories])
  
  // Fungsi untuk scroll ke kanan dengan perhitungan card yang terlihat
  const scrollRight = () => {
    const container = desktopContainerRef.current;
    if (container) {
      const cardWidth = 100; // Lebar card
      const gapWidth = 3; // Lebar gap
      const totalCardWidth = cardWidth + gapWidth;
      const visibleCards = Math.floor(container.clientWidth / totalCardWidth);
      const scrollAmount = visibleCards * totalCardWidth;
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  }
  
  // Fungsi untuk scroll ke kiri dengan perhitungan card yang terlihat
  const scrollLeft = () => {
    const container = desktopContainerRef.current;
    if (container) {
      const cardWidth = 100; // Lebar card
      const gapWidth = 3; // Lebar gap
      const totalCardWidth = cardWidth + gapWidth;
      const visibleCards = Math.floor(container.clientWidth / totalCardWidth);
      const scrollAmount = visibleCards * totalCardWidth;
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    }
  }

  // State untuk melacak apakah card View All terlihat di layar
  const [isViewAllVisible, setIsViewAllVisible] = useState(false);
  const rafId = useRef<number | undefined>(undefined);

  // Effect untuk memeriksa visibilitas card View All
  useEffect(() => {
    if (!isMobile) return;
    
    let isMounted = true;
    
    const checkViewAllVisibility = () => {
      if (!isMounted) return;
      
      const container = mobileContainerRef.current;
      if (!container) return;
      
      const viewAllCard = container.querySelector('.see-all-card') as HTMLElement;
      if (!viewAllCard) {
        setIsViewAllVisible(false);
        return;
      }
      
      const containerRect = container.getBoundingClientRect();
      const cardRect = viewAllCard.getBoundingClientRect();
      
      // Hitung area yang terlihat (dalam piksel)
      const visibleWidth = Math.min(cardRect.right, containerRect.right) - Math.max(cardRect.left, containerRect.left);
      const visibleHeight = Math.min(cardRect.bottom, containerRect.bottom) - Math.max(cardRect.top, containerRect.top);
      
      // Setidaknya 1px yang terlihat di kedua sumbu
      const isVisible = visibleWidth > 0 && visibleHeight > 0;
      
      if (isMounted) {
        setIsViewAllVisible(isVisible);
        
        // Jika card terlihat, pastikan tombol View All disembunyikan
        if (isVisible) {
          setShowScrollHint(false);
        }
      }
    };
    
    // Gunakan requestAnimationFrame untuk performa yang lebih baik
    const animate = () => {
      checkViewAllVisibility();
      rafId.current = requestAnimationFrame(animate);
    };
    
    // Mulai animasi
    rafId.current = requestAnimationFrame(animate);
    
    // Juga periksa saat terjadi scroll atau resize
    const handleScroll = () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      rafId.current = requestAnimationFrame(checkViewAllVisibility);
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleScroll, { passive: true });
    
    // Pemeriksaan awal
    const initialCheck = setTimeout(checkViewAllVisibility, 100);
    
    // Bersihkan
    return () => {
      isMounted = false;
      clearTimeout(initialCheck);
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [isMobile, isExpanded, visibleCategories]);

  // Fungsi untuk menampilkan tombol View All
  const showViewAllButton = () => {
    // Jangan tampilkan tombol jika dalam mode expanded atau card View All terlihat
    if (isExpanded || isViewAllVisible) {
      setShowScrollHint(false);
      return;
    }
    
    // Hapus timeout yang ada
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);
    
    // Tampilkan tombol
    setShowScrollHint(true);
    
    // Sembunyikan setelah 5 detik
    hideTimeoutRef.current = setTimeout(() => {
      setShowScrollHint(false);
    }, 5000);
  };

  // Handle scroll untuk mobile
  const handleScroll = () => {
    if (!mobileContainerRef.current || isExpanded) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = mobileContainerRef.current;
    const isAtStart = scrollLeft <= 0;
    const isAtEnd = scrollLeft + clientWidth >= scrollWidth - 5; // Sedikit toleransi
    
    // Update state posisi scroll
    setIsScrollAtStart(isAtStart);
    setIsScrollAtEnd(isAtEnd);
    
    // Sembunyikan tombol View All saat scroll
    setShowScrollHint(false);
    setIsScrolling(true);
    
    // Hapus semua timeout yang ada
    if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);
    
    // Set timeout untuk menampilkan tombol View All setelah 15 detik tidak ada scroll
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
      if (!isAtEnd && !isExpanded) { // Hanya tampilkan jika tidak di ujung kanan dan tidak expanded
        showViewAllButton();
      }
    }, 15000);
  };

  // Set up initial show timeout dan interval
  useEffect(() => {
    if (isMobile) {
      // Tampilkan tombol pertama kali setelah 7 detik
      initialShowTimeoutRef.current = setTimeout(() => {
        if (!isExpanded) {
          showViewAllButton();
        }
        
        // Set interval untuk menampilkan tombol setiap 15 detik
        showIntervalRef.current = setInterval(() => {
          if (!isScrolling && !isExpanded) {
            showViewAllButton();
          }
        }, 15000);
      }, 7000);
      
      // Add scroll event listener
      const container = mobileContainerRef.current;
      if (container) {
        container.addEventListener('scroll', handleScroll);
      }
      
      // Cleanup function
      return () => {
        if (initialShowTimeoutRef.current) clearTimeout(initialShowTimeoutRef.current);
        if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
        if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);
        if (showIntervalRef.current) clearInterval(showIntervalRef.current);
        if (container) container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [isMobile, isScrolling, isExpanded]);
  
  // Animasi scroll hint untuk mobile
  const mobileScrollHintAnimation = {
    x: [0, 3, 0],
    transition: {
      repeat: Infinity,
      duration: 1.5,
      repeatType: "loop" as const,
      ease: "easeInOut"
    }
  };

  // Fungsi untuk menangani klik kategori
  const handleCategoryClick = (e: React.MouseEvent, category: Category) => {
    e.preventDefault();
    if (!category) return;
    
    // Untuk tampilan mobile, buka subcategory view
    if (isMobile) {
      // Update urutan kategori dengan yang dipilih di paling atas
      const newSortedCategories = [
        category,
        ...allCategories.filter((cat: Category) => cat.id !== category.id)
      ];
      setSortedCategories(newSortedCategories);
      
      setSelectedCategory(category);
      setShowSubcategoryView(true);
      // Disable scrolling pada body saat subcategory view terbuka
      document.body.style.overflow = 'hidden';
      return;
    }
    
    const hasSubcategories = category.name in subCategories && 
      subCategories[category.name]?.length > 0;
    
    if (hasSubcategories) {
      setSelectedCategory(category);
    } else {
      // Jika tidak ada subkategori, lakukan aksi default
      console.log('Kategori dipilih:', category.name);
      // Tambahkan logika navigasi atau tindakan lain yang diperlukan
    }
  };

  // Fungsi untuk menutup subcategory view
  const handleCloseSubcategoryView = () => {
    setShowSubcategoryView(false);
    // Enable scrolling kembali pada body
    document.body.style.overflow = '';
  };
  
  // Mengambil daftar subkategori
  const getSubcategories = (categoryName: string) => {
    if (!categoryName || !(categoryName in subCategories)) {
      console.log('Kategori tidak ditemukan atau tidak valid:', categoryName);
      return [];
    }
    return subCategories[categoryName];
  };

  // Fungsi untuk toggle expanded view
  const toggleExpand = (e: React.MouseEvent) => {
    e.preventDefault();
    
    if (isMobile) {
      // Jika di mobile, langsung arahkan ke subcategory view dengan kategori pertama
      if (allCategories.length > 0) {
        const firstCategory = allCategories[0];
        const newSortedCategories = [
          firstCategory,
          ...allCategories.filter((cat: Category) => cat.id !== firstCategory.id)
        ];
        setSortedCategories(newSortedCategories);
        setSelectedCategory(firstCategory);
        setShowSubcategoryView(true);
        document.body.style.overflow = 'hidden';
      }
      return;
    }
    
    // Untuk desktop/tablet, tetap gunakan logika expand biasa
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);
    
    if (newExpandedState && mobileContainerRef.current) {
      mobileContainerRef.current.scrollLeft = 0;
    }
    
    setVisibleCategories(newExpandedState ? allCategories : allCategories.slice(0, 9));
  };

  // Fungsi untuk menangani klik tombol kembali
  const handleBackClick = () => {
    setSelectedCategory(null);
  };

  // Fungsi untuk menangani klik subkategori
  const handleSubcategoryClick = (subcategory: Category) => {
    if (!subcategory) return;
    
    console.log('Subkategori dipilih:', subcategory.name);
    // Tambahkan logika navigasi atau tindakan lain yang diperlukan
  };

  // Selalu tampilkan semua kategori di desktop/tablet
  const isShowingAllCategories = !isMobile;

  return (
    <div className="bg-white py-3 px-4 mb-4 rounded-lg border border-gray-100 shadow-sm overflow-visible">
      {/* Subcategory View untuk mobile */}
      {showSubcategoryView && selectedCategory && (
        <div className="subcategory-view">
          <div className="subcategory-header">
            <div className="flex items-center w-full">
              <button 
                onClick={handleCloseSubcategoryView}
                className="back-button"
                aria-label="Kembali ke kategori"
              >
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="24" 
                  height="24" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                >
                  <line x1="19" y1="12" x2="5" y2="12"></line>
                  <polyline points="12 19 5 12 12 5"></polyline>
                </svg>
              </button>
              <h1 className="text-lg font-medium text-gray-800 ml-2">Kategori {selectedCategory.name}</h1>
            </div>
          </div>
          
          <div className="subcategory-container">
            <div className="category-sidebar">
              {isMobile ? (
                // Tampilan mobile dengan pengurutan dinamis
                sortedCategories.map((cat: Category) => (
                  <div 
                    key={cat.id}
                    className={`sidebar-item ${cat.id === selectedCategory?.id ? 'active' : ''}`}
                    onClick={() => {
                      // Update urutan kategori dengan yang dipilih di paling atas
                      const newSortedCategories = [
                        cat,
                        ...sortedCategories.filter((c: Category) => c.id !== cat.id)
                      ];
                      setSortedCategories(newSortedCategories);
                      setSelectedCategory(cat);
                    }}
                  >
                    <div className="sidebar-icon" style={{ color: cat.id === selectedCategory?.id ? (cat.color || '#FF5722') : '#666' }}>
                      {getIcon(cat.icon)}
                    </div>
                    <div className="sidebar-text">{cat.name}</div>
                  </div>
                ))
              ) : (
                // Tampilan desktop tanpa pengurutan dinamis
                allCategories.map((cat: Category) => (
                  <div 
                    key={cat.id}
                    className={`sidebar-item ${cat.id === selectedCategory?.id ? 'active' : ''}`}
                    onClick={() => setSelectedCategory(cat)}
                  >
                    <div className="sidebar-icon" style={{ color: cat.id === selectedCategory?.id ? (cat.color || '#FF5722') : '#666' }}>
                      {getIcon(cat.icon)}
                    </div>
                    <div className="sidebar-text">{cat.name}</div>
                  </div>
                ))
              )}
            </div>
            
            <div className="subcategory-content">
              <div className="subcategory-banner" key={`banner-${selectedCategory?.id}-${bannerKey}`}>
                {selectedCategory && (
                  <img 
                    src={selectedCategory.banner || '/api/placeholder/800/200'} 
                    alt={`${selectedCategory.name} Banner`} 
                    onError={(e) => {
                      // Fallback jika gambar tidak dapat dimuat
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentNode as HTMLElement;
                      if (parent) {
                        parent.innerHTML = `
                          <div style="width:100%;height:140px;background:${selectedCategory.color}20;display:flex;align-items:center;justify-content:center;color:${selectedCategory.color};border:1px solid ${selectedCategory.color}30;border-radius:8px;">
                            <h3 style="margin:0;font-size:1.2rem;">Promo Spesial ${selectedCategory.name}!</h3>
                          </div>
                        `;
                      }
                    }}
                    style={{
                      width: '100%', 
                      height: 'auto', 
                      display: 'block',
                      borderRadius: '8px',
                      border: `1px solid ${selectedCategory?.color || '#FF5722'}30`
                    }}
                  />
                )}
              </div>
              
              <div className="subcategory-grid">
                {subCategories[selectedCategory.name]?.map((subCategory: Category, idx: number) => (
                  <div 
                    key={idx} 
                    className="subcategory-item"
                    onClick={() => handleSubcategoryClick(subCategory)}
                  >
                    <div className="subcategory-icon" style={{ color: selectedCategory.color }}>
                      {getIcon(subCategory.icon)}
                    </div>
                    <div className="subcategory-title">
                      {subCategory.name}
                    </div>
                  </div>
                )) || (
                  <div className="col-span-3 text-center py-8 text-gray-500">
                    Belum ada subkategori untuk kategori ini.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div className="container mx-auto max-w-6xl px-0 overflow-visible">
        {/* Kategori navigation buttons untuk desktop/tablet tidak ditampilkan di sini, tapi di dalam container */}
        
        {/* Mobile View */}
        {isMobile ? (
          <>
            <div className={`relative categories-container-wrapper ${!isExpanded ? 'has-blur' : ''}`}>
              {!isExpanded && (
                <div 
                  ref={mobileContainerRef}
                  className="flex overflow-x-auto pb-3 hide-scrollbar categories-container"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                    WebkitOverflowScrolling: 'touch',
                    paddingRight: '5px' // Padding kanan 10px
                  }}
                >
                  <div className="flex gap-2 relative pr-4">
                    <AnimatePresence>
                      {visibleCategories.map((category, index) => (
                        <CategoryItem 
                          key={index} 
                          category={category}
                          onClick={(e) => handleCategoryClick(e, category)}
                        />
                      ))}
                      {!isExpanded && (
                        <div className="pr-2">
                          <SeeAllItem 
                            onClick={(e: React.MouseEvent) => toggleExpand(e)}
                            isExpanded={isExpanded}
                          />
                        </div>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  {/* Animasi panah untuk scroll hint di mobile berdasarkan beranda-feed.html */}
                  {showScrollHint && !isScrolling && (
                    <div 
                      className="absolute right-2 top-[45%] -translate-y-1/2 z-10 h-8 bg-[rgba(255,245,240,0.95)] border border-[#FFDFD1] rounded-[16px] shadow-md flex items-center px-2 py-0 cursor-pointer"
                      onClick={(e: React.MouseEvent) => toggleExpand(e)}
                    >
                      <span className="text-[11px] font-semibold text-[#FF5722] mr-1">View All</span>
                      <motion.div 
                        className="flex items-center justify-center text-[#FF5722]"
                        animate={mobileScrollHintAnimation}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                      </motion.div>
                    </div>
                  )}
                </div>
              )}
              {isExpanded && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="overflow-hidden no-blur">
                    {/* Expanded categories grid */}
                    <div className="grid grid-cols-4 gap-1 pt-1 pb-2">
                      <AnimatePresence>
                        {allCategories.map((category, index) => (
                          <CategoryItem 
                            key={index} 
                            category={category}
                            isExpandedView={true}
                            onClick={(e) => handleCategoryClick(e, category)}
                          />
                        ))}
                      </AnimatePresence>
                    </div>
                    
                    <div className="flex justify-center mt-4 mb-2">
                      <button 
                        onClick={(e: React.MouseEvent) => toggleExpand(e)}
                        className="bg-[#ff5722] rounded-full w-8 h-8 flex items-center justify-center shadow-sm transition-transform hover:scale-110 text-white"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                      </button>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </>
        ) : (
          // Desktop/Tablet View
          <div className="relative">

            <div className="w-full px-4 lg:px-8 flex items-center">
              {/* Tombol navigasi kiri */}
              {showLeftNav && (
                <button 
                  onClick={scrollLeft}
                  className="z-10 w-8 h-8 rounded-full bg-white border border-gray-200 flex items-center justify-center shadow-md hover:bg-gray-50 transition-colors mr-2"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="15 18 9 12 15 6"></polyline>
                  </svg>
                </button>
              )}
              
              <div 
                ref={desktopContainerRef}
                className="flex-1 max-w-[1200px] mx-auto py-2.5 overflow-x-auto overflow-y-hidden hide-scrollbar"
                style={{
                  paddingLeft: 'calc((100% - min(1200px, 100% - 2rem)) / 2)',
                  paddingRight: 'calc((100% - min(1200px, 100% - 2rem)) / 2)'
                }}
              >
                <div 
                  className="grid grid-flow-col auto-cols-[100px] gap-3 justify-start w-max mx-auto"
                  style={{
                    gridAutoFlow: 'column',
                    gridTemplateRows: 'repeat(2, 100px)',
                    padding: '0.5rem 1rem',
                    margin: '0 auto',
                    width: 'auto',
                    minWidth: '100%',
                    display: 'inline-grid'
                  } as React.CSSProperties}
                >
                  <AnimatePresence>
                    {showSubcategoryView && selectedCategory ? (
                      <SubcategoryView 
                        category={selectedCategory} 
                        subcategories={getSubcategories(selectedCategory.name)}
                        onBack={handleCloseSubcategoryView}
                        onSubcategoryClick={handleSubcategoryClick}
                      />
                    ) : (
                      <>
                        {allCategories.map((category, index) => (
                          <CategoryItem 
                            key={index} 
                            category={category}
                            isExpandedView={true}
                            onClick={(e) => handleCategoryClick(e, category)}
                          />
                        ))}
                        
                        {/* View All card for desktop */}
                        {!isShowingAllCategories && (
                          <motion.div
                            className="flex flex-col items-center justify-center rounded-lg transition-all duration-200 p-3 w-[100px] h-[100px] bg-[#FFF5F0] border border-[#f0f0f0]"
                            initial={{ opacity: 0, y: 5 }}
                            animate={{ opacity: 1, y: 0 }}
                            whileHover={{ scale: 1.02, backgroundColor: '#FFE8E0' }}
                            whileTap={{ scale: 0.98 }}
                            onClick={toggleExpand}
                          >
                            <div className="flex items-center justify-center mb-3 w-8 h-8">
                              {iconComponents['see-all']}
                            </div>
                            <span className="text-center text-xs text-gray-800 leading-tight">
                              View All
                            </span>
                          </motion.div>
                        )}
                      </>
                    )}
                  </AnimatePresence>
                </div>
              </div>
              
              {/* Tombol navigasi kanan */}
              {showRightNav && (
                <button 
                  onClick={scrollRight}
                  className="z-10 w-8 h-8 rounded-full bg-white border border-gray-200 flex items-center justify-center shadow-md hover:bg-gray-50 transition-colors ml-2"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </button>
              )}
            </div>
            
            {/* Sub-categories section for desktop */}
            {selectedCategory && selectedCategory.name in subCategories && (
              <motion.div 
                ref={expandedRef} 
                className="mt-4 mb-2"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="border-t border-gray-200 pt-3 pb-2">
                  <div className="flex items-center justify-between mb-3 px-1">
                    <h3 className="text-[14px] font-semibold text-gray-900">
                      Kategori {selectedCategory.name}
                    </h3>
                    <button 
                      onClick={() => setSelectedCategory(null)}
                      className="text-[12px] text-[#FF5722] hover:text-[#E64A19] transition-colors flex items-center"
                    >
                      <span>Close</span>
                      <svg 
                        className="w-4 h-4 ml-1" 
                        xmlns="http://www.w3.org/2000/svg" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                      >
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>
                  <div className="expanded-categories active">
                    <div className="expanded-grid">
                      {getSubcategories(selectedCategory.name).length > 0 ? (
                        getSubcategories(selectedCategory.name).map((subCategory: Category, idx: number) => (
                          <motion.div
                            key={idx}
                            className="expanded-item bg-white hover:bg-gray-50"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: idx * 0.05 }}
                            onClick={() => handleSubcategoryClick(subCategory)}
                          >
                            <div className="expanded-icon" style={{ color: subCategory.color }}>
                              {iconComponents[subCategory.icon] || iconComponents['more-horizontal']}
                            </div>
                            <span className="expanded-name">{subCategory.name}</span>
                          </motion.div>
                        ))
                      ) : (
                        <div className="col-span-full text-center py-8 text-gray-500">
                          Tidak ada subkategori yang tersedia
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        )}
      </div>
      
      <style jsx global>{`
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        /* Menghilangkan efek blur di expanded view mobile */
        @media (max-width: 768px) {
          /* Efek blur hanya untuk tampilan kategori biasa (bukan expanded) */
          .categories-container-wrapper {
            position: relative;
            overflow: hidden;
          }
          .categories-container-wrapper.has-blur::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 24px; /* Mengurangi lebar efek blur */
            background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,1) 100%);
            pointer-events: none;
            z-index: 10;
          }
          /* Hapus blur saat expanded */
          .categories-container-wrapper:not(.has-blur)::after {
            display: none;
          }
          .expanded-categories {
            -webkit-backdrop-filter: none !important;
            backdrop-filter: none !important;
            overflow: visible !important;
          }
          .expanded-categories::before,
          .expanded-categories::after {
            display: none !important;
          }
          .expanded-grid {
            display: grid !important;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)) !important;
            gap: 0.5rem !important;
            padding: 0.5rem !important;
            overflow: visible !important;
          }
          .no-blur {
            -webkit-backdrop-filter: none !important;
            backdrop-filter: none !important;
            overflow: visible !important;
          }
          .no-blur::before,
          .no-blur::after {
            display: none !important;
          }
        }
        
        /* Styling untuk subcategory view */
        .subcategory-view {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: white;
          z-index: 1000;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
          left: 0;
          right: 0;
        }
        
        .subcategory-header {
          background-color: white;
          padding: 15px;
          display: flex;
          align-items: center;
          box-shadow: none;
          z-index: 10;
        }
        
        .back-button {
          color: #ee4d2d;
          font-size: 20px;
          margin-right: 15px;
          text-decoration: none;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          cursor: pointer;
        }
        
        .back-button svg {
          transform: scaleX(1.3);
        }
        
        .subcategory-container {
          display: flex;
          height: calc(100% - 70px);
          overflow: hidden;
          position: relative;
        }
        
        .category-sidebar {
          border-top: 1px solid #eee;
          width: 100px;
          background-color: #f9f9f9;
          overflow-y: auto;
          flex-shrink: 0;
          box-shadow: 3px 0 5px -2px rgba(0, 0, 0, 0.1);
          height: 100%;
          position: relative;
          z-index: 1;
        }
        
        .category-sidebar::-webkit-scrollbar {
          display: none;
        }
        
        .sidebar-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 12px 8px;
          border-bottom: 1px solid #eee;
          cursor: pointer;
          text-align: center;
        }
        
        .sidebar-item.active {
          background-color: white;
        }
        
        .sidebar-icon {
          color: #888;
          margin-bottom: 6px;
        }
        
        .sidebar-item.active .sidebar-icon {
          color: #FF5722;
        }
        
        .sidebar-text {
          font-size: 11px;
          color: #555;
          text-align: center;
        }
        
        .sidebar-item.active .sidebar-text {
          color: #333;
          font-weight: 500;
        }
        
        .subcategory-content {
          flex-grow: 1;
          padding: 15px;
          background-color: white;
          overflow-y: auto;
          margin-left: 1px;
        }
        
        .subcategory-content::-webkit-scrollbar {
          display: none;
        }
        
        .subcategory-banner {
          width: 100%;
          margin-bottom: 15px;
          border-radius: 8px;
          overflow: hidden;
        }
        
        .subcategory-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 12px;
        }
        
        .subcategory-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 10px;
          background-color: white;
          box-shadow: 0 1px 3px rgba(0,0,0,0.05);
          height: 100px;
          width: 100%;
        }
        
        .subcategory-icon {
          width: 40px;
          height: 40px;
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .subcategory-title {
          font-size: 12px;
          font-weight: normal;
          color: #333;
          text-align: center;
          line-height: 1.3;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        
        /* Media query untuk subcategory view */
        @media (max-width: 480px) {
          .subcategory-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }
        
        @media (min-width: 768px) {
          .category-sidebar {
            width: 150px;
          }
          
          .sidebar-item {
            padding: 15px 10px;
          }
          
          .sidebar-text {
            font-size: 12px;
          }
          
          .subcategory-grid {
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
          }
        }
        
        @media (min-width: 768px) {
          .subcategory-view {
            max-width: 900px;
          }
        }
        
        @media (min-width: 1200px) {
          .subcategory-view {
            max-width: 700px;
          }
        }
      `}</style>
    </div>
  );
}