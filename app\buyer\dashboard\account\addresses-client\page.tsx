"use client"

import { useState } from "react"
import Link from "next/link"
import { MapPin, Home, Briefcase, Plus, Edit, Trash2, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

// Sample address data
const addresses = [
  {
    id: "1",
    nickname: "Home",
    fullName: "<PERSON>",
    street: "Jl. Sudirman No. 123",
    detail: "Apartment 4B, Tower C",
    city: "Jakarta",
    province: "DKI Jakarta",
    postalCode: "12930",
    country: "Indonesia",
    phone: "+62812345678",
    type: "home",
    isDefaultShipping: true,
    isDefaultBilling: true,
  },
  {
    id: "2",
    nickname: "Office",
    fullName: "John Doe",
    street: "Jl. Gatot Subroto No. 456",
    detail: "12th Floor, XYZ Building",
    city: "Jakarta",
    province: "DKI Jakarta",
    postalCode: "12950",
    country: "Indonesia",
    phone: "+62812345678",
    type: "work",
    isDefaultShipping: false,
    isDefaultBilling: false,
  },
]

export default function AddressesClientPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [userAddresses, setUserAddresses] = useState(addresses)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null)

  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login")
    }
  }, [loading, user, router])

  const handleDeleteAddress = (id: string) => {
    setAddressToDelete(id)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (addressToDelete) {
      setUserAddresses(userAddresses.filter((address) => address.id !== addressToDelete))
      setIsDeleteDialogOpen(false)
      setAddressToDelete(null)
    }
  }

  const renderAddressTypeIcon = (type: string) => {
    switch (type) {
      case "home":
        return <Home className="h-4 w-4" />
      case "work":
        return <Briefcase className="h-4 w-4" />
      default:
        return <MapPin className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
          <p className="text-gray-500">Please wait while we load your addresses.</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Account Settings</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/buyer/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="addresses" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 lg:w-auto">
          <TabsTrigger value="profile" asChild>
            <Link href="/buyer/dashboard/account/profile">Profile</Link>
          </TabsTrigger>
          <TabsTrigger value="addresses">Addresses</TabsTrigger>
          <TabsTrigger value="payment" asChild>
            <Link href="/buyer/dashboard/account/payment">Payment Methods</Link>
          </TabsTrigger>
          <TabsTrigger value="security" asChild>
            <Link href="/buyer/dashboard/account/security">Security</Link>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="addresses" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Your Addresses</h2>
              <p className="text-sm text-muted-foreground">Manage your shipping and billing addresses.</p>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="gap-1">
                  <Plus className="h-4 w-4" />
                  Add New Address
                </Button>
              </DialogTrigger>
              <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add New Address</DialogTitle>
                  <DialogDescription>Fill in the details for your new address.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="nickname">Address Nickname</Label>
                      <Input id="nickname" placeholder="e.g., Home, Office" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="type">Address Type</Label>
                      <Select defaultValue="home">
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="home">Home</SelectItem>
                          <SelectItem value="work">Work</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input id="fullName" placeholder="Full Name" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="street">Street Address</Label>
                    <Input id="street" placeholder="Street address or P.O. Box" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="detail">Apartment, Suite, Unit, etc.</Label>
                    <Input id="detail" placeholder="Apt, Suite, Unit, Building, Floor, etc." />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input id="city" placeholder="City" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="province">Province/State</Label>
                      <Input id="province" placeholder="Province or State" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="postalCode">Postal Code</Label>
                      <Input id="postalCode" placeholder="Postal Code" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Select defaultValue="indonesia">
                        <SelectTrigger>
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="indonesia">Indonesia</SelectItem>
                          <SelectItem value="malaysia">Malaysia</SelectItem>
                          <SelectItem value="singapore">Singapore</SelectItem>
                          <SelectItem value="thailand">Thailand</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" placeholder="Phone Number" />
                  </div>
                  <div className="space-y-4 pt-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="defaultShipping" />
                      <Label htmlFor="defaultShipping">Set as default shipping address</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="defaultBilling" />
                      <Label htmlFor="defaultBilling">Set as default billing address</Label>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" onClick={() => setIsAddDialogOpen(false)}>
                    Save Address
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {userAddresses.map((address) => (
              <Card key={address.id}>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary">
                        {renderAddressTypeIcon(address.type)}
                      </div>
                      <CardTitle className="text-base">{address.nickname}</CardTitle>
                    </div>
                    <div className="flex gap-1">
                      {address.isDefaultShipping && (
                        <Badge variant="outline" className="bg-primary/10 text-primary">
                          Default Shipping
                        </Badge>
                      )}
                      {address.isDefaultBilling && (
                        <Badge variant="outline" className="bg-primary/10 text-primary">
                          Default Billing
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1 text-sm">
                    <p className="font-medium">{address.fullName}</p>
                    <p>{address.street}</p>
                    {address.detail && <p>{address.detail}</p>}
                    <p>
                      {address.city}, {address.province} {address.postalCode}
                    </p>
                    <p>{address.country}</p>
                    <p className="pt-1">{address.phone}</p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end gap-2">
                  <Button variant="outline" size="sm" className="gap-1">
                    <Edit className="h-4 w-4" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1 text-red-500 hover:bg-red-50 hover:text-red-600"
                    onClick={() => handleDeleteAddress(address.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this address? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4 flex gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Address
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
