import { getClient } from '@/lib/supabase'

export interface StoreCategory {
  id: string
  name: string
  slug: string
  description?: string
  icon?: string
  color: string
  image_url?: string
  is_active: boolean
  sort_order: number
  meta_title?: string
  meta_description?: string
  commission_rate: number
  min_commission: number
  max_commission?: number
  requirements: {
    min_products?: number
    required_docs?: string[]
    [key: string]: any
  }
  created_at: string
  updated_at: string
}

export interface StoreCategoryFilters {
  search?: string
  is_active?: boolean
}

export interface StoreCategoryCreate {
  name: string
  slug: string
  description?: string
  icon?: string
  color?: string
  image_url?: string
  is_active?: boolean
  sort_order?: number
  meta_title?: string
  meta_description?: string
  commission_rate?: number
  min_commission?: number
  max_commission?: number
  requirements?: object
}

export interface StoreCategoryUpdate {
  name?: string
  slug?: string
  description?: string
  icon?: string
  color?: string
  image_url?: string
  is_active?: boolean
  sort_order?: number
  meta_title?: string
  meta_description?: string
  commission_rate?: number
  min_commission?: number
  max_commission?: number
  requirements?: object
}

class StoreCategoryService {
  private supabase = getClient()

  // Get all store categories with optional filters
  async getCategories(filters?: StoreCategoryFilters): Promise<StoreCategory[]> {
    let query = this.supabase
      .from('store_categories')
      .select('*')
      .order('sort_order', { ascending: true })

    // Apply active filter
    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active)
    }

    // Apply search filter
    if (filters?.search) {
      const searchTerm = `%${filters.search}%`
      query = query.or(`name.ilike.${searchTerm},description.ilike.${searchTerm},slug.ilike.${searchTerm}`)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch categories: ${error.message}`)
    }

    return data || []
  }

  // Get single category by ID
  async getCategory(id: string): Promise<StoreCategory | null> {
    const { data, error } = await this.supabase
      .from('store_categories')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch category: ${error.message}`)
    }

    return data
  }

  // Get category by slug
  async getCategoryBySlug(slug: string): Promise<StoreCategory | null> {
    const { data, error } = await this.supabase
      .from('store_categories')
      .select('*')
      .eq('slug', slug)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch category: ${error.message}`)
    }

    return data
  }

  // Create new category
  async createCategory(category: StoreCategoryCreate): Promise<StoreCategory> {
    // Check if slug already exists
    const existingCategory = await this.getCategoryBySlug(category.slug)
    if (existingCategory) {
      throw new Error('Slug already exists')
    }

    const { data, error } = await this.supabase
      .from('store_categories')
      .insert([{
        ...category,
        color: category.color || '#4F46E5',
        is_active: category.is_active !== undefined ? category.is_active : true,
        sort_order: category.sort_order || 0,
        commission_rate: category.commission_rate || 0,
        min_commission: category.min_commission || 0,
        requirements: category.requirements || {}
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create category: ${error.message}`)
    }

    return data
  }

  // Update category
  async updateCategory(id: string, updates: StoreCategoryUpdate): Promise<StoreCategory> {
    // If slug is being updated, check if it already exists
    if (updates.slug) {
      const existingCategory = await this.getCategoryBySlug(updates.slug)
      if (existingCategory && existingCategory.id !== id) {
        throw new Error('Slug already exists')
      }
    }

    const { data, error } = await this.supabase
      .from('store_categories')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update category: ${error.message}`)
    }

    return data
  }

  // Delete category
  async deleteCategory(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('store_categories')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete category: ${error.message}`)
    }
  }

  // Toggle category active status
  async toggleCategoryStatus(id: string): Promise<StoreCategory> {
    const category = await this.getCategory(id)
    if (!category) {
      throw new Error('Category not found')
    }

    return this.updateCategory(id, { is_active: !category.is_active })
  }

  // Reorder categories
  async reorderCategories(categoryOrders: { id: string; sort_order: number }[]): Promise<void> {
    const updates = categoryOrders.map(({ id, sort_order }) => 
      this.supabase
        .from('store_categories')
        .update({ sort_order })
        .eq('id', id)
    )

    const results = await Promise.all(updates)
    
    for (const result of results) {
      if (result.error) {
        throw new Error(`Failed to reorder categories: ${result.error.message}`)
      }
    }
  }

  // Get category statistics
  async getCategoryStats(): Promise<{
    total: number
    active: number
    inactive: number
  }> {
    const { data, error } = await this.supabase
      .from('store_categories')
      .select('is_active')

    if (error) {
      throw new Error(`Failed to fetch category stats: ${error.message}`)
    }

    const stats = {
      total: data.length,
      active: 0,
      inactive: 0,
    }

    data.forEach((category) => {
      if (category.is_active) {
        stats.active++
      } else {
        stats.inactive++
      }
    })

    return stats
  }
}

export const storeCategoryService = new StoreCategoryService()
