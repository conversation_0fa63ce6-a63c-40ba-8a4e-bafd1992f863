"use client"

import type React from "react"

import { CalendarIcon } from "lucide-react"
import { forwardRef } from "react"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface DatePickerProps extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: boolean
}

const DatePicker = forwardRef<HTMLInputElement, DatePickerProps>(({ className, icon = true, ...props }, ref) => {
  return (
    <div className="relative">
      {icon && (
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <CalendarIcon className="w-4 h-4 text-muted-foreground" />
        </div>
      )}
      <Input type="date" className={cn(icon ? "pl-10" : "", className)} ref={ref} {...props} />
    </div>
  )
})

DatePicker.displayName = "DatePicker"

export { DatePicker }
