import type { Metadata } from "next"
import { AffiliateApplicationIntro } from "@/components/buyer/affiliate-application/affiliate-application-intro"
import { AffiliateApplicationWizard } from "@/components/buyer/affiliate-application/affiliate-application-wizard"

export const metadata: Metadata = {
  title: "Affiliate Application - SellZio Marketplace",
  description: "Join our affiliate program and earn commissions by promoting products",
}

export default function AffiliateApplicationPage() {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Affiliate Application</h1>
          <p className="text-muted-foreground">Join our affiliate program and earn commissions by promoting products</p>
        </div>
      </div>

      <div className="space-y-10">
        {/* Introduction Banner */}
        <AffiliateApplicationIntro />

        {/* Application Wizard */}
        <div id="affiliate-application-wizard">
          <AffiliateApplicationWizard />
        </div>
      </div>
    </div>
  )
}
