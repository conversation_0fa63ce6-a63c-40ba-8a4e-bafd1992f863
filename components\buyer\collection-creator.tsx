"use client"

import { useState } from "react"
import { Plus, Loader2 } from "lucide-react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/use-toast"

interface Collection {
  id: string
  name: string
  description: string
  imageUrl: string
  createdAt: string
  itemCount: number
  isPublic: boolean
  items: string[]
}

export interface CollectionCreatorProps {
  children?: React.ReactNode
  size?: "default" | "sm"
  buttonLabel?: string
  buttonIcon?: React.ReactNode
  onSuccess?: (collection: Collection) => void
}

export function CollectionCreator({ 
  children, 
  size = "default",
  buttonLabel = "Buat Koleksi",
  buttonIcon,
  onSuccess
}: CollectionCreatorProps) {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [isPublic, setIsPublic] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!name) return

    setIsLoading(true)
    try {
      const response = await fetch('/api/collections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description,
          isPublic,
        }),
      })

      if (!response.ok) {
        throw new Error('Gagal membuat koleksi')
      }

      const collection = await response.json()
      
      toast({
        title: "Berhasil",
        description: "Koleksi baru berhasil dibuat",
      })
      
      // Reset form
      setName("")
      setDescription("")
      setIsPublic(false)
      
      // Tutup dialog
      setOpen(false)
      
      // Panggil callback onSuccess jika disediakan
      if (onSuccess) {
        onSuccess(collection)
      }
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal membuat koleksi baru",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const triggerButton = children ? children : (
    <Button 
      variant="default" 
      size={size}
    >
      {buttonIcon || <Plus className="mr-2 h-4 w-4" />}
      {buttonLabel}
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {triggerButton}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Buat Koleksi Baru</DialogTitle>
          <DialogDescription>
            Buat koleksi untuk menyimpan dan mengorganisir produk favorit Anda.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Koleksi</Label>
              <Input
                id="name"
                placeholder="Masukkan nama koleksi"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Deskripsi (Opsional)</Label>
              <Textarea
                id="description"
                placeholder="Jelaskan tentang koleksi ini"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="public">Koleksi Publik</Label>
                <div className="text-sm text-muted-foreground">
                  Koleksi publik dapat dilihat oleh pengguna lain
                </div>
              </div>
              <Switch
                id="public"
                checked={isPublic}
                onCheckedChange={setIsPublic}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setOpen(false)}
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button type="submit" disabled={!name || isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                "Buat Koleksi"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 