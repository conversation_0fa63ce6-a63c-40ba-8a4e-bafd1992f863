"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format, subDays, startOfMonth, endOfMonth, startOfYear, endOfYear } from "date-fns"
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Store, 
  Users, 
  Package, 
  TrendingUp,
  Eye,
  Edit,
  Trash2,
  Settings,
  X,
  Check,
  Loader2,
  Columns,
  Calendar as CalendarIcon,
  Download
} from "lucide-react"
import Link from "next/link"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

// Import fungsi-fungsi Supabase
import {
  getStores,
  createStore,
  updateStore,
  deleteStore as deleteStoreService,
  updateStoreStatus as updateStoreStatusService,
  updateStoreSettings,
  Store as StoreType
} from "@/lib/services/stores"

// Schema untuk validasi form
const storeFormSchema = z.object({
  name: z.string().min(3, "Nama toko harus minimal 3 karakter"),
  owner: z.string().min(3, "Nama pemilik harus minimal 3 karakter"),
  email: z.string().email("Email tidak valid"),
  category: z.string().min(1, "Kategori harus dipilih"),
  location: z.string().min(3, "Lokasi harus minimal 3 karakter"),
  plan: z.string().min(1, "Paket harus dipilih"),
  status: z.string().min(1, "Status harus dipilih"),
});

// Data dummy untuk stores (akan diganti dengan data dari Supabase)
const initialStoresData = [
  {
    id: "store-1",
    name: "Toko Fashion Kita",
    owner: "Ahmad Rizki",
    email: "<EMAIL>",
    status: "active",
    plan: "Premium",
    products: 245,
    orders: 1250,
    revenue: 45000000,
    rating: 4.8,
    joinDate: "2023-01-15",
    lastActive: "2 menit lalu",
    category: "Fashion",
    location: "Jakarta"
  },
  {
    id: "store-2",
    name: "Elektronik Murah",
    owner: "Siti Nurhaliza",
    email: "<EMAIL>",
    status: "active",
    plan: "Business",
    products: 180,
    orders: 890,
    revenue: 32000000,
    rating: 4.6,
    joinDate: "2023-02-20",
    lastActive: "15 menit lalu",
    category: "Elektronik",
    location: "Bandung"
  },
  {
    id: "store-3",
    name: "Makanan Sehat",
    owner: "Budi Santoso",
    email: "<EMAIL>",
    status: "pending",
    plan: "Starter",
    products: 65,
    orders: 320,
    revenue: 8500000,
    rating: 4.3,
    joinDate: "2023-11-10",
    lastActive: "1 jam lalu",
    category: "Makanan",
    location: "Surabaya"
  },
  {
    id: "store-4",
    name: "Buku & Alat Tulis",
    owner: "Maya Sari",
    email: "<EMAIL>",
    status: "suspended",
    plan: "Business",
    products: 420,
    orders: 650,
    revenue: 15000000,
    rating: 4.1,
    joinDate: "2023-05-08",
    lastActive: "3 hari lalu",
    category: "Buku",
    location: "Yogyakarta"
  },
  {
    id: "store-5",
    name: "Olahraga & Fitness",
    owner: "Andi Pratama",
    email: "<EMAIL>",
    status: "active",
    plan: "Premium",
    products: 310,
    orders: 1100,
    revenue: 38000000,
    rating: 4.7,
    joinDate: "2023-03-12",
    lastActive: "30 menit lalu",
    category: "Olahraga",
    location: "Medan"
  }
]

// Kategori toko
const storeCategories = [
  "Fashion", 
  "Elektronik", 
  "Makanan", 
  "Buku", 
  "Olahraga", 
  "Kesehatan", 
  "Kecantikan", 
  "Rumah Tangga",
  "Otomotif",
  "Lainnya"
]

// Paket langganan
const storePlans = [
  "Starter",
  "Business",
  "Premium"
]

// Status toko
const storeStatuses = [
  "active",
  "pending",
  "suspended"
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge variant="default" className="bg-green-100 text-green-800">Aktif</Badge>
    case "pending":
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
    case "suspended":
      return <Badge variant="destructive" className="bg-red-100 text-red-800">Suspended</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const getPlanBadge = (plan: string) => {
  switch (plan) {
    case "Premium":
      return <Badge variant="default" className="bg-purple-100 text-purple-800">Premium</Badge>
    case "Business":
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Business</Badge>
    case "Starter":
      return <Badge variant="outline">Starter</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount)
}

export default function StoresPage() {
  // State untuk stores
  const [storesData, setStoresData] = useState<StoreType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // State untuk filter dan search
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  
  // State untuk date filter
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  
  // State untuk bulk actions
  const [selectedStores, setSelectedStores] = useState<string[]>([])
  const [isAllSelected, setIsAllSelected] = useState(false)
  
  // State untuk kustomisasi kolom
  const [visibleColumns, setVisibleColumns] = useState<{[key: string]: boolean}>({
    store: true,
    owner: true,
    status: true,
    plan: true,
    products: true,
    orders: true,
    revenue: true,
    rating: true,
    lastActive: true,
    actions: true,
  })
  
  // State untuk dialog dan form
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false)
  const [selectedStore, setSelectedStore] = useState<StoreType | null>(null)
  
  // State untuk pengaturan store
  const [emailNotificationsEnabled, setEmailNotificationsEnabled] = useState(false)
  const [productVerificationEnabled, setProductVerificationEnabled] = useState(false)
  const [isUpdatingSettings, setIsUpdatingSettings] = useState(false)
  
  // Fungsi untuk mengirim reset password email
  const sendResetPasswordEmail = async (email: string) => {
    try {
      // Menampilkan loading state
      const loadingMsg = `Mengirim email reset password ke ${email}...`;
      console.log(loadingMsg);
      
      // Simulasi API call dengan timeout
      // Dalam implementasi nyata, ini akan memanggil API endpoint Supabase Auth
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Dalam aplikasi produksi, gunakan kode seperti ini:
      // const { error } = await supabase.auth.resetPasswordForEmail(email, {
      //   redirectTo: `${window.location.origin}/reset-password`,
      // });
      // 
      // if (error) throw error;
      
      // Menampilkan sukses message
      alert(`Email reset password telah dikirim ke ${email}`);
    } catch (error) {
      console.error('Error sending reset password email:', error);
      alert('Gagal mengirim email reset password. Silakan coba lagi.');
    }
  }
  
  // Fungsi untuk mengekspor data store sebagai CSV
  const exportStoreData = (store: StoreType) => {
    try {
      // Membuat header CSV
      const headers = [
        'ID', 'Nama Store', 'Pemilik', 'Email', 'Kategori', 'Lokasi',
        'Status', 'Paket', 'Produk', 'Order', 'Revenue', 'Rating',
        'Tanggal Bergabung', 'Terakhir Aktif'
      ];
      
      // Membuat baris data
      const data = [
        store.id, 
        store.name, 
        store.owner_name || store.owner, 
        store.email, 
        store.category, 
        store.location,
        store.status, 
        store.plan, 
        store.products, 
        store.orders, 
        store.revenue, 
        store.rating,
        store.join_date, 
        store.last_active
      ];
      
      // Menggabungkan header dan data
      const csvContent = [
        headers.join(','),
        data.join(',')
      ].join('\n');
      
      // Membuat Blob dan link untuk download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      // Membuat elemen anchor untuk download
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `store_${store.name.replace(/\s+/g, '_')}_data.csv`);
      link.style.visibility = 'hidden';
      
      // Menambahkan ke DOM, memicu download, dan membersihkan
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      alert(`Data store ${store.name} telah diekspor sebagai CSV`);
    } catch (error) {
      console.error('Error exporting store data:', error);
      alert('Gagal mengekspor data store. Silakan coba lagi.');
    }
  }
  
  // Fungsi untuk mengaktifkan/menonaktifkan tampilan marketplace
  const toggleMarketplaceVisibility = async (store: StoreType) => {
    const newStatus = store.status === "active" ? "pending" : "active";
    await changeStoreStatus(store.id, newStatus as any);
  }
  
  // Fungsi untuk bulk actions
  const handleBulkAction = async (action: string) => {
    if (selectedStores.length === 0) {
      alert("Pilih minimal satu store untuk melakukan bulk action")
      return
    }
    
    // Konfirmasi dari user
    const actionText = action === "activate" ? "mengaktifkan" : 
                       action === "suspend" ? "menangguhkan" : "menghapus"
    const isConfirmed = confirm(`Apakah Anda yakin ingin ${actionText} ${selectedStores.length} store terpilih?`)
    
    if (!isConfirmed) return
    
    try {
      if (action === "delete") {
        // Hapus store terpilih
        for (const storeId of selectedStores) {
          await deleteStoreService(storeId)
        }
        
        // Update state
        setStoresData(prevStores => prevStores.filter(store => !selectedStores.includes(store.id)))
        alert(`${selectedStores.length} store berhasil dihapus`)
      } else {
        // Update status store terpilih
        const newStatus = action === "activate" ? "active" : "suspended"
        
        for (const storeId of selectedStores) {
          await updateStoreStatusService(storeId, newStatus)
        }
        
        // Update state
        setStoresData(prevStores => 
          prevStores.map(store => 
            selectedStores.includes(store.id) 
              ? {...store, status: newStatus} 
              : store
          )
        )
        
        const statusText = newStatus === "active" ? "aktif" : "ditangguhkan"
        alert(`Status ${selectedStores.length} store berhasil diubah menjadi ${statusText}`)
      }
      
      // Reset selected stores
      setSelectedStores([])
      setIsAllSelected(false)
    } catch (err) {
      console.error("Error processing bulk action:", err)
      alert("Gagal melakukan bulk action. Silakan coba lagi.")
    }
  }
  
  // Fungsi untuk filter berdasarkan date range
  const applyDateFilter = (stores: StoreType[]) => {
    if (!dateRange.from) return stores;
    
    return stores.filter(store => {
      const joinDate = new Date(store.join_date);
      if (dateRange.from && dateRange.to) {
        return joinDate >= dateRange.from && joinDate <= dateRange.to;
      }
      return joinDate >= (dateRange.from as Date);
    });
  }
  
  // Fungsi untuk mengatur preset date filter
  const setDateFilterPreset = (preset: string) => {
    const today = new Date();
    
    switch (preset) {
      case "today":
        setDateRange({ from: today, to: today });
        break;
        
      case "yesterday":
        const yesterday = subDays(today, 1);
        setDateRange({ from: yesterday, to: yesterday });
        break;
        
      case "thisMonth":
        setDateRange({ 
          from: startOfMonth(today), 
          to: endOfMonth(today) 
        });
        break;
        
      case "thisYear":
        setDateRange({ 
          from: startOfYear(today), 
          to: endOfYear(today) 
        });
        break;
        
      case "clear":
        setDateRange({ from: undefined, to: undefined });
        break;
    }
  }
  
  // Fungsi untuk toggle pilihan store untuk bulk action
  const toggleStoreSelection = (storeId: string) => {
    setSelectedStores(prev => {
      const newSelection = prev.includes(storeId) 
        ? prev.filter(id => id !== storeId) 
        : [...prev, storeId];
      
      // Update isAllSelected berdasarkan apakah semua item telah dipilih
      setIsAllSelected(newSelection.length === filteredStores.length);
      return newSelection;
    });
  }
  
  // Fungsi untuk select/deselect semua store
  const toggleSelectAll = () => {
    if (isAllSelected) {
      // Deselect semua
      setSelectedStores([]);
      setIsAllSelected(false);
    } else {
      // Select semua store yang terfilter saat ini
      setSelectedStores(filteredStores.map(store => store.id));
      setIsAllSelected(true);
    }
  }
  
  // Fungsi untuk toggle email notifications
  const toggleEmailNotifications = async (storeId: string, enabled: boolean) => {
    setIsUpdatingSettings(true);
    try {
      const { success, error } = await updateStoreSettings(storeId, {
        email_notifications: enabled
      });
      
      if (error) throw error;
      
      setEmailNotificationsEnabled(enabled);
      alert(enabled ? "Notifikasi email telah diaktifkan" : "Notifikasi email telah dinonaktifkan");
    } catch (error) {
      console.error("Error updating email notifications setting:", error);
      alert("Gagal mengubah pengaturan notifikasi email. Silakan coba lagi.");
      // Kembalikan state ke nilai sebelumnya jika gagal
      setEmailNotificationsEnabled(!enabled);
    } finally {
      setIsUpdatingSettings(false);
    }
  }
  
  // Fungsi untuk toggle product verification
  const toggleProductVerification = async (storeId: string, enabled: boolean) => {
    setIsUpdatingSettings(true);
    try {
      const { success, error } = await updateStoreSettings(storeId, {
        product_verification: enabled
      });
      
      if (error) throw error;
      
      setProductVerificationEnabled(enabled);
      alert(enabled ? "Verifikasi produk telah diaktifkan" : "Verifikasi produk telah dinonaktifkan");
    } catch (error) {
      console.error("Error updating product verification setting:", error);
      alert("Gagal mengubah pengaturan verifikasi produk. Silakan coba lagi.");
      // Kembalikan state ke nilai sebelumnya jika gagal
      setProductVerificationEnabled(!enabled);
    } finally {
      setIsUpdatingSettings(false);
    }
  }
  
  // Setup form dengan react-hook-form dan zod validation
  const form = useForm<z.infer<typeof storeFormSchema>>({
    resolver: zodResolver(storeFormSchema),
    defaultValues: {
      name: "",
      owner: "",
      email: "",
      category: "",
      location: "",
      plan: "Starter",
      status: "pending",
    },
  });

  // Fetch data stores dari Supabase saat komponen dimuat
  useEffect(() => {
    async function fetchStores() {
      setIsLoading(true);
      try {
        const { data, error } = await getStores();
        
        if (error) {
          throw error;
        }
        
        if (data) {
          setStoresData(data as unknown as StoreType[]);
        }
      } catch (err) {
        console.error("Error fetching stores:", err);
        setError("Gagal memuat data stores. Silakan coba lagi nanti.");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchStores();
  }, []);

  // Filter stores berdasarkan pencarian, status, dan date range
  const filteredStores = storesData.filter(store => {
    // Filter pencarian
    const matchesSearch = 
      store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (store.owner_name || store.owner).toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Filter status
    const matchesStatus = statusFilter === "all" || store.status === statusFilter;
    
    // Filter date range
    const matchesDateRange = !dateRange.from ? true : (() => {
      const joinDate = new Date(store.join_date);
      if (dateRange.from && dateRange.to) {
        return joinDate >= dateRange.from && joinDate <= dateRange.to;
      }
      return joinDate >= dateRange.from;
    })();
    
    return matchesSearch && matchesStatus && matchesDateRange;
  })

  // Menghitung statistik
  const totalStores = storesData.length
  const activeStores = storesData.filter(s => s.status === "active").length
  const pendingStores = storesData.filter(s => s.status === "pending").length
  const totalRevenue = storesData.reduce((sum, store) => sum + store.revenue, 0)
  const totalProducts = storesData.reduce((sum, store) => sum + store.products, 0)

  // Fungsi untuk membuka dialog tambah store
  const openAddDialog = () => {
    form.reset({
      name: "",
      owner: "",
      email: "",
      category: "",
      location: "",
      plan: "Starter",
      status: "pending",
    });
    setIsAddDialogOpen(true);
  };

  // Fungsi untuk membuka dialog edit store
  const openEditDialog = (store: StoreType) => {
    setSelectedStore(store);
    form.reset({
      name: store.name,
      owner: store.owner_name || store.owner, // Gunakan owner_name jika tersedia, jika tidak gunakan owner
      email: store.email,
      category: store.category,
      location: store.location,
      plan: store.plan,
      status: store.status,
    });
    setIsEditDialogOpen(true);
  };

  // Fungsi untuk membuka dialog detail store
  const openDetailDialog = (store: StoreType) => {
    setSelectedStore(store);
    setIsDetailDialogOpen(true);
  };

  // Fungsi untuk membuka dialog pengaturan store
  const openSettingsDialog = (store: StoreType) => {
    setSelectedStore(store);
    setIsSettingsDialogOpen(true);
  };

  // Fungsi untuk membuka dialog konfirmasi hapus
  const openDeleteDialog = (store: StoreType) => {
    setSelectedStore(store);
    setIsDeleteDialogOpen(true);
  };

  // Fungsi untuk menambah store baru dengan Supabase
  const addStore = async (data: z.infer<typeof storeFormSchema>) => {
    try {
      const newStore = {
        name: data.name,
        owner: data.owner,
        email: data.email,
        status: data.status as any,
        plan: data.plan,
        products: 0,
        orders: 0,
        revenue: 0,
        rating: 0,
        join_date: new Date().toISOString(), // Menggunakan join_date bukan joinDate
        last_active: new Date().toISOString(), // Menggunakan last_active bukan lastActive
        category: data.category,
        location: data.location
      };
      
      console.log('Mengirim data ke createStore:', newStore);
      
      const { data: createdStore, error } = await createStore(newStore);
      
      if (error) {
        console.error('Error dari createStore:', error);
        throw error;
      }
      
      if (createdStore) {
        // Update state dengan store baru
        setStoresData(prevStores => [...prevStores, createdStore as StoreType]);
        setIsAddDialogOpen(false);
        alert("Store berhasil ditambahkan");
      }
    } catch (err) {
      console.error("Error creating store:", err);
      alert("Gagal menambahkan store. Silakan coba lagi.");
    }
  };

  // Fungsi untuk mengupdate store dengan Supabase
  const updateStoreData = async (data: z.infer<typeof storeFormSchema>) => {
    if (!selectedStore) return;
    
    try {
      const updates = {
        name: data.name,
        owner_name: data.owner, // Simpan nama pemilik di field owner_name
        email: data.email,
        status: data.status as any,
        plan: data.plan,
        category: data.category,
        location: data.location
      };
      
      const { data: updatedStore, error } = await updateStore(selectedStore.id, updates);
      
      if (error) {
        throw error;
      }
      
      if (updatedStore) {
        // Update state dengan store yang diperbarui
        const updatedStores = storesData.map(store => 
          store.id === selectedStore.id ? (updatedStore as unknown as StoreType) : store
        );
        
        setStoresData(updatedStores as StoreType[]);
        setIsEditDialogOpen(false);
        alert("Store berhasil diperbarui");
      }
    } catch (err) {
      console.error("Error updating store:", err);
      alert("Gagal memperbarui store. Silakan coba lagi.");
    }
  };

  // Fungsi untuk menghapus store dengan Supabase
  const deleteStoreData = async () => {
    if (!selectedStore) return;
    
    try {
      const { success, error } = await deleteStoreService(selectedStore.id);
      
      if (error) {
        throw error;
      }
      
      if (success) {
        // Update state dengan menghapus store
        const updatedStores = storesData.filter(store => store.id !== selectedStore.id);
        setStoresData(updatedStores);
        setIsDeleteDialogOpen(false);
        alert("Store berhasil dihapus");
      }
    } catch (err) {
      console.error("Error deleting store:", err);
      alert("Gagal menghapus store. Silakan coba lagi.");
    }
  };

  // Fungsi untuk mengubah status store dengan Supabase
  const changeStoreStatus = async (storeId: string, newStatus: 'active' | 'pending' | 'suspended') => {
    try {
      const { data: updatedStore, error } = await updateStoreStatusService(storeId, newStatus);
      
      if (error) {
        throw error;
      }
      
      if (updatedStore) {
        // Update state dengan status store yang diperbarui
        const updatedStores = storesData.map(store => 
          store.id === storeId ? (updatedStore as unknown as StoreType) : store
        );
        
        setStoresData(updatedStores as StoreType[]);
        alert(`Status store berhasil diubah menjadi ${newStatus}`);
      }
    } catch (err) {
      console.error("Error updating store status:", err);
      alert("Gagal mengubah status store. Silakan coba lagi.");
    }
  };

  // Render komponen
  return (
    <div className="space-y-6">
  try {
    const { data: updatedStore, error } = await updateStoreStatusService(storeId, newStatus);
    
    if (error) {
      throw error;
    }
    
    if (updatedStore) {
      // Update state dengan status store yang diperbarui
      const updatedStores = storesData.map(store => 
        store.id === storeId ? (updatedStore as unknown as StoreType) : store
      );
      
      setStoresData(updatedStores as StoreType[]);
      alert(`Status store berhasil diubah menjadi ${newStatus}`);
    }
  } catch (err) {
    console.error("Error updating store status:", err);
    alert("Gagal mengubah status store. Silakan coba lagi.");
  }
};

return (
  <div className="space-y-6">
    {/* Header */}
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Link
          href="/tenant/dashboard"
          className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10"
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">Back</span>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Kelola Stores</h1>
          <p className="text-muted-foreground">
            Manage semua stores dalam marketplace Anda
          </p>
        </div>
      </div>
      
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <CalendarIcon className="h-4 w-4" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "dd/MM/yyyy")} - {format(dateRange.to, "dd/MM/yyyy")}
                    </>
                  ) : (
                    format(dateRange.from, "dd/MM/yyyy")
                  )
                ) : (
                  "Filter Tanggal"
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent align="end" className="w-auto p-0" sideOffset={5}>
              <div className="p-3 border-b">
                <div className="space-y-2">
                  <h4 className="font-medium">Filter Berdasarkan Tanggal</h4>
                  <div className="flex flex-wrap gap-2">
                    <Button variant="outline" size="sm" onClick={() => setDateFilterPreset("today")}>Hari Ini</Button>
                    <Button variant="outline" size="sm" onClick={() => setDateFilterPreset("yesterday")}>Kemarin</Button>
                    <Button variant="outline" size="sm" onClick={() => setDateFilterPreset("thisMonth")}>Bulan Ini</Button>
                    <Button variant="outline" size="sm" onClick={() => setDateFilterPreset("thisYear")}>Tahun Ini</Button>
                    <Button variant="outline" size="sm" onClick={() => setDateFilterPreset("clear")}>Reset</Button>
                  </div>
                </div>
              </div>
              <Calendar
                mode="range"
                selected={{
                  from: dateRange.from,
                  to: dateRange.to,
                }}
                onSelect={(value: any) => setDateRange(value)}
                numberOfMonths={2}
                className="p-3"
              />
            </PopoverContent>
          </Popover>
        </div>
        
        <Button onClick={openAddDialog}>
          <Plus className="h-4 w-4 mr-2" />
          Tambah Store Baru
        </Button>
      </div>
    </div>

    {/* Stats Cards */}
    <div className="grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Stores</CardTitle>
          <Store className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
            <div className="text-2xl font-bold">{totalStores}</div>
            <p className="text-xs text-muted-foreground">
              +2 dari bulan lalu
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stores Aktif</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeStores}</div>
            <p className="text-xs text-muted-foreground">
              {pendingStores} pending approval
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Produk</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              Across all stores
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              +12% dari bulan lalu
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Daftar Stores</CardTitle>
              <div className="text-sm text-muted-foreground">
                <span>Manage dan monitor semua stores dalam marketplace</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari stores..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-[300px]"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter Status
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setStatusFilter("all")}>
                    Semua Status
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("active")}>
                    Aktif
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("pending")}>
                    Pending
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("suspended")}>
                    Suspended
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 text-primary animate-spin" />
              <span className="ml-2">Memuat data...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              <div className="mb-2">{error}</div>
              <Button variant="outline" onClick={() => window.location.reload()}>
                Coba Lagi
              </Button>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40px]">
                      <Checkbox 
                        checked={isAllSelected} 
                        onCheckedChange={toggleSelectAll} 
                      />
                    </TableHead>
                    {visibleColumns.store && <TableHead>Store</TableHead>}
                    {visibleColumns.owner && <TableHead>Owner</TableHead>}
                    {visibleColumns.status && <TableHead>Status</TableHead>}
                    {visibleColumns.plan && <TableHead>Plan</TableHead>}
                    {visibleColumns.products && <TableHead>Produk</TableHead>}
                    {visibleColumns.orders && <TableHead>Orders</TableHead>}
                    {visibleColumns.revenue && <TableHead>Revenue</TableHead>}
                    {visibleColumns.rating && <TableHead>Rating</TableHead>}
                    {visibleColumns.lastActive && <TableHead>Last Active</TableHead>}
                    {visibleColumns.actions && <TableHead className="w-[50px]"></TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStores.map((store) => (
                    <TableRow key={store.id}>
                      <TableCell>
                        <Checkbox 
                          checked={selectedStores.includes(store.id)} 
                          onCheckedChange={() => toggleStoreSelection(store.id)} 
                        />
                      </TableCell>
                      {visibleColumns.store && (
                        <TableCell>
                          <div>
                            <div className="font-medium">{store.name}</div>
                            <div className="text-sm text-muted-foreground">{store.category}</div>
                          </div>
                        </TableCell>
                      )}
                      {visibleColumns.owner && (
                        <TableCell>
                          <div>
                            <div className="font-medium" data-component-name="StoresPage">{store.owner_name || store.owner}</div>
                            <div className="text-sm text-muted-foreground">{store.email}</div>
                          </div>
                        </TableCell>
                      )}
                      {visibleColumns.status && (
                        <TableCell>{getStatusBadge(store.status)}</TableCell>
                      )}
                      {visibleColumns.plan && (
                        <TableCell>{getPlanBadge(store.plan)}</TableCell>
                      )}
                      {visibleColumns.products && (
                        <TableCell>{store.products}</TableCell>
                      )}
                      {visibleColumns.orders && (
                        <TableCell>{store.orders}</TableCell>
                      )}
                      {visibleColumns.revenue && (
                        <TableCell>{formatCurrency(store.revenue)}</TableCell>
                      )}
                      {visibleColumns.rating && (
                        <TableCell>
                          <div className="flex items-center">
                            <span className="text-yellow-500">★</span>
                            <span className="ml-1">{store.rating}</span>
                          </div>
                        </TableCell>
                      )}
                      {visibleColumns.lastActive && (
                        <TableCell className="text-sm text-muted-foreground">
                          {store.last_active}
                        </TableCell>
                      )}
                      {visibleColumns.actions && (
                        <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openDetailDialog(store)}>
                              <Eye className="h-4 w-4 mr-2" />
                              Lihat Detail
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openEditDialog(store)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Store
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openSettingsDialog(store)}>
                              <Settings className="h-4 w-4 mr-2" />
                              Pengaturan
                            </DropdownMenuItem>
                            {store.status !== "active" && (
                              <DropdownMenuItem onClick={() => changeStoreStatus(store.id, "active")}>
                                <Check className="h-4 w-4 mr-2 text-green-600" />
                                <span className="text-green-600">Aktifkan Store</span>
                              </DropdownMenuItem>
                            )}
                            {store.status !== "suspended" && (
                              <DropdownMenuItem onClick={() => changeStoreStatus(store.id, "suspended")}>
                                <X className="h-4 w-4 mr-2 text-red-600" />
                                <span className="text-red-600">Suspend Store</span>
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={() => openDeleteDialog(store)} className="text-red-600">
                              <Trash2 className="h-4 w-4 mr-2" />
                              Hapus Store
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {filteredStores.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Tidak ada stores yang ditemukan</p>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Dialog Tambah Store */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Tambah Store Baru</DialogTitle>
            <div className="text-sm text-muted-foreground">
              Isi form berikut untuk menambahkan store baru ke marketplace.
            </div>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(addStore)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Store</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan nama store" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kategori</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih kategori" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {storeCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="owner"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Pemilik</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan nama pemilik" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lokasi</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan lokasi" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="plan"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Paket</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih paket" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {storePlans.map((plan) => (
                            <SelectItem key={plan} value={plan}>
                              {plan}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Aktif</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Batal
                </Button>
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Tambah Store
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Dialog Edit Store */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Store</DialogTitle>
            <div className="text-sm text-muted-foreground">
              Edit informasi store berikut.
            </div>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(updateStoreData)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Store</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan nama store" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kategori</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih kategori" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {storeCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="owner"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Pemilik</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan nama pemilik" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lokasi</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan lokasi" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="plan"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Paket</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih paket" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {storePlans.map((plan) => (
                            <SelectItem key={plan} value={plan}>
                              {plan}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Aktif</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Batal
                </Button>
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Simpan Perubahan
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Dialog Konfirmasi Hapus Store */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Apakah Anda yakin?</AlertDialogTitle>
            <AlertDialogDescription>
              Tindakan ini akan menghapus store "{selectedStore?.name}" secara permanen dan tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={deleteStoreData} className="bg-red-600 hover:bg-red-700">
              Hapus Store
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog Detail Store */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detail Store</DialogTitle>
            <div className="text-sm text-muted-foreground">
              Informasi lengkap tentang store {selectedStore?.name}
            </div>
          </DialogHeader>
          {selectedStore && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-sm">Nama Store</h3>
                  <div>{selectedStore.name}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Kategori</h3>
                  <div>{selectedStore.category}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Pemilik</h3>
                  <div>{selectedStore.owner_name || selectedStore.owner}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Email</h3>
                  <div>{selectedStore.email}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Lokasi</h3>
                  <div>{selectedStore.location}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Status</h3>
                  <div>{getStatusBadge(selectedStore.status)}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Paket</h3>
                  <div>{getPlanBadge(selectedStore.plan)}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Tanggal Bergabung</h3>
                  <div>{selectedStore.join_date}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Total Produk</h3>
                  <div>{selectedStore.products}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Total Order</h3>
                  <div>{selectedStore.orders}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Revenue</h3>
                  <div>{formatCurrency(selectedStore.revenue)}</div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm">Rating</h3>
                  <div className="flex items-center">
                    <span className="text-yellow-500">★</span>
                    <span className="ml-1">{selectedStore.rating}</span>
                  </div>
                </div>
              </div>
              
              <DialogFooter>
                <Button onClick={() => setIsDetailDialogOpen(false)}>Tutup</Button>
                <Button variant="outline" onClick={() => {
                  setIsDetailDialogOpen(false);
                  openEditDialog(selectedStore);
                }}>
                  Edit Store
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Dialog Pengaturan Store */}
      <Dialog open={isSettingsDialogOpen} onOpenChange={setIsSettingsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Pengaturan Store</DialogTitle>
            <div className="text-sm text-muted-foreground">
              Konfigurasi pengaturan untuk store {selectedStore?.name}
            </div>
          </DialogHeader>
          {selectedStore && (
            <div className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Pengaturan Umum</h3>
                <div className="flex items-center justify-between border-b pb-3">
                  <div>
                    <h4 className="font-medium">Notifikasi Email</h4>
                    <div className="text-sm text-muted-foreground">Kirim notifikasi ke pemilik store</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button 
                      variant={emailNotificationsEnabled ? "default" : "outline"}
                      size="sm"
                      disabled={isUpdatingSettings}
                      onClick={() => toggleEmailNotifications(selectedStore.id, !emailNotificationsEnabled)}
                    >
                      {isUpdatingSettings ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        emailNotificationsEnabled ? "Aktif" : "Aktifkan"
                      )}
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between border-b pb-3">
                  <div>
                    <h4 className="font-medium">Verifikasi Produk</h4>
                    <div className="text-sm text-muted-foreground">Wajibkan verifikasi produk baru</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button 
                      variant={productVerificationEnabled ? "default" : "outline"}
                      size="sm"
                      disabled={isUpdatingSettings}
                      onClick={() => toggleProductVerification(selectedStore.id, !productVerificationEnabled)}
                    >
                      {isUpdatingSettings ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        productVerificationEnabled ? "Aktif" : "Aktifkan"
                      )}
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between border-b pb-3">
                  <div>
                    <h4 className="font-medium">Tampilkan di Marketplace</h4>
                    <div className="text-sm text-muted-foreground">Tampilkan store di halaman utama</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button 
                      variant={selectedStore.status === "active" ? "default" : "outline"}
                      size="sm"
                      disabled={isUpdatingSettings}
                      onClick={async () => {
                        setIsUpdatingSettings(true);
                        try {
                          const newStatus = selectedStore.status === "active" ? "pending" : "active";
                          const { success, error } = await updateStoreStatusService(selectedStore.id, newStatus as any);
                          
                          if (error) throw error;
                          
                          // Update local state jika operasi berhasil
                          selectedStore.status = newStatus as any;
                          setStoresData(prevStores => 
                            prevStores.map(store => 
                              store.id === selectedStore.id ? { ...store, status: newStatus } : store
                            )
                          );
                          
                          alert(selectedStore.status === "active" ? 
                            "Store sekarang ditampilkan di marketplace" : 
                            "Store tidak lagi ditampilkan di marketplace");
                        } catch (error) {
                          console.error("Error updating store visibility:", error);
                          alert("Gagal mengubah visibilitas store. Silakan coba lagi.");
                        } finally {
                          setIsUpdatingSettings(false);
                        }
                      }}
                    >
                      {isUpdatingSettings ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        selectedStore.status === "active" ? "Aktif" : "Nonaktif"
                      )}
                    </Button>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Tindakan</h3>
                <div className="flex items-center justify-between border-b pb-3">
                  <div>
                    <h4 className="font-medium">Reset Password</h4>
                    <div className="text-sm text-muted-foreground">Kirim email reset password ke pemilik store</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        sendResetPasswordEmail(selectedStore.email);
                      }}
                    >
                      Kirim
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between border-b pb-3">
                  <div>
                    <h4 className="font-medium">Ekspor Data</h4>
                    <div className="text-sm text-muted-foreground">Ekspor data store dalam format CSV</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        exportStoreData(selectedStore);
                      }}
                    >
                      Ekspor
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-red-500">Bekukan Store</h4>
                    <div className="text-sm text-muted-foreground">Bekukan store ini sementara</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button 
                      variant="destructive" 
                      size="sm" 
                      onClick={() => {
                        changeStoreStatus(selectedStore.id, "suspended");
                        setIsSettingsDialogOpen(false);
                        alert("Store telah dibekukan");
                      }}
                    >
                      Bekukan
                    </Button>
                  </div>
                </div>
              </div>
              
              <DialogFooter>
                <Button onClick={() => setIsSettingsDialogOpen(false)}>Tutup</Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
} 