"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Star,
  Users,
  ShoppingBag,
  Store,
  CheckCircle2,
  XCircle,
  AlertCircle,
  MessageSquare,
  MoreHorizontal,
  Calendar,
  ThumbsUp,
  ThumbsDown
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk ulasan
const reviews = [
  {
    id: "rev-001",
    customer: {
      name: "<PERSON><PERSON>",
      avatar: "/api/placeholder/32/32"
    },
    product: "Headphone Wireless X200",
    store: "TechWorld Store",
    rating: 5,
    comment: "Produk sangat bagus, suara jernih dan baterai tahan lama!",
    date: "2024-01-10T15:30:00",
    status: "approved",
    helpful: 12,
    notHelpful: 1
  },
  {
    id: "rev-002",
    customer: {
      name: "<PERSON><PERSON>",
      avatar: "/api/placeholder/32/32"
    },
    product: "Lipstick Matte Red",
    store: "Beauty Corner",
    rating: 4,
    comment: "Warnanya cantik, tapi agak cepat pudar.",
    date: "2024-01-08T11:20:00",
    status: "approved",
    helpful: 7,
    notHelpful: 0
  },
  {
    id: "rev-003",
    customer: {
      name: "Ahmad Rizki",
      avatar: "/api/placeholder/32/32"
    },
    product: "Kaos Polos Pria",
    store: "Fashion Zone",
    rating: 3,
    comment: "Bahan lumayan, pengiriman agak lama.",
    date: "2024-01-12T09:45:00",
    status: "pending",
    helpful: 2,
    notHelpful: 1
  },
  {
    id: "rev-004",
    customer: {
      name: "Maya Putri",
      avatar: "/api/placeholder/32/32"
    },
    product: "Vas Bunga Keramik",
    store: "Home Decor Store",
    rating: 5,
    comment: "Sangat cantik, sesuai foto dan aman saat pengiriman.",
    date: "2024-01-15T13:40:00",
    status: "approved",
    helpful: 5,
    notHelpful: 0
  },
  {
    id: "rev-005",
    customer: {
      name: "Deni Hermawan",
      avatar: "/api/placeholder/32/32"
    },
    product: "Smartwatch FitPro",
    store: "Electronic Hub",
    rating: 2,
    comment: "Kurang akurat untuk fitur detak jantung.",
    date: "2023-09-20T10:15:00",
    status: "rejected",
    helpful: 0,
    notHelpful: 3
  }
]

// Fungsi untuk badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "approved":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Approved</Badge>
    case "pending":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>
    case "rejected":
      return <Badge variant="outline" className="bg-red-100 text-red-800">Rejected</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export default function ReviewsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Filter reviews
  const filteredReviews = reviews.filter(review => {
    const matchesSearch = review.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.product.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.store.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || review.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Statistik
  const stats = {
    total: reviews.length,
    approved: reviews.filter(r => r.status === "approved").length,
    pending: reviews.filter(r => r.status === "pending").length,
    rejected: reviews.filter(r => r.status === "rejected").length,
    avgRating: (reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length).toFixed(1)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/customers">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Reviews</h1>
            <p className="text-muted-foreground">
              Lihat, moderasi, dan kelola ulasan pelanggan
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Users className="h-4 w-4 mr-2" />
            Customers
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Avg. Rating: {stats.avgRating}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <AlertCircle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari nama pelanggan, produk, atau toko..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="approved">Approved</option>
              <option value="pending">Pending</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Reviews List */}
      <div className="space-y-4">
        {filteredReviews.map((review) => (
          <Card key={review.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex flex-col gap-2">
                <div className="flex flex-col md:flex-row justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-100">
                      <img src={review.customer.avatar} alt={review.customer.name} className="h-full w-full object-cover" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-semibold">{review.customer.name}</span>
                        {getStatusBadge(review.status)}
                      </div>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Calendar className="h-3.5 w-3.5" />
                        <span>{formatDate(review.date)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <div className="flex items-center gap-1">
                      <Store className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">{review.store}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <ShoppingBag className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">{review.product}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1 mt-2">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className={`h-4 w-4 ${i < review.rating ? 'text-yellow-500' : 'text-gray-300'}`} fill={i < review.rating ? '#facc15' : 'none'} />
                  ))}
                  <span className="ml-2 text-xs text-muted-foreground">{review.rating} / 5</span>
                </div>
                <div className="my-2 text-sm">{review.comment}</div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <ThumbsUp className="h-4 w-4" /> {review.helpful} Helpful
                  <ThumbsDown className="h-4 w-4 ml-4" /> {review.notHelpful} Not Helpful
                </div>
                <div className="flex gap-2 pt-2 border-t mt-2">
                  {review.status === "pending" && (
                    <Button size="sm" variant="outline" className="text-green-600 hover:text-green-700">
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Approve
                    </Button>
                  )}
                  {review.status !== "rejected" && (
                    <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject
                    </Button>
                  )}
                  <Button size="sm" variant="ghost">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        {filteredReviews.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Tidak ada ulasan ditemukan</h3>
              <p className="text-muted-foreground mb-4">
                Tidak ada ulasan yang cocok dengan filter Anda
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
} 