import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { AffiliatePerformanceDashboard } from "@/components/buyer/affiliate/affiliate-performance-dashboard"
import { AffiliateProductPerformance } from "@/components/buyer/affiliate/affiliate-product-performance"
import { AffiliateCustomerInsights } from "@/components/buyer/affiliate/affiliate-customer-insights"
import { AffiliateReportGenerator } from "@/components/buyer/affiliate/affiliate-report-generator"

export default function AffiliateAnalyticsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
        <p className="text-muted-foreground">Detailed analytics and insights for your affiliate performance</p>
      </div>

      <Tabs defaultValue="performance" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>
        <TabsContent value="performance">
          <AffiliatePerformanceDashboard />
        </TabsContent>
        <TabsContent value="products">
          <AffiliateProductPerformance />
        </TabsContent>
        <TabsContent value="customers">
          <AffiliateCustomerInsights />
        </TabsContent>
        <TabsContent value="reports">
          <AffiliateReportGenerator />
        </TabsContent>
      </Tabs>
    </div>
  )
}
