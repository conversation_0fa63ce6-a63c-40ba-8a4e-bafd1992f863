"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Edit, ExternalLink, Package, ShoppingBag, PlusCircle, Trash2 } from "lucide-react"
import { ProductList } from "@/components/dashboard/products/product-list"
import { storesAPI, type Store } from "@/lib/api/stores"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useRouter } from "next/navigation"

interface StoreDetailProps {
  id: string
}

export function StoreDetail({ id }: StoreDetailProps) {
  const router = useRouter()
  const [store, setStore] = useState<Store | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  useEffect(() => {
    const fetchStore = async () => {
      try {
        const data = await storesAPI.getById(id)
        setStore(data)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching store:", err)
        setError("Gagal memuat data toko. Silakan coba lagi.")
        setLoading(false)
      }
    }

    fetchStore()
  }, [id])

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await storesAPI.delete(id)
      router.push("/dashboard/stores")
    } catch (err) {
      console.error("Error deleting store:", err)
      setError("Gagal menghapus toko. Silakan coba lagi.")
      setIsDeleting(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </CardContent>
        </Card>
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!store) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold mb-2">Toko tidak ditemukan</h3>
        <p className="text-muted-foreground mb-4">Toko yang Anda cari tidak ditemukan atau telah dihapus.</p>
        <Button asChild>
          <Link href="/dashboard/stores">Kembali ke Daftar Toko</Link>
        </Button>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">{store.name}</h1>
        <div className="flex space-x-2">
          <Button asChild>
            <Link href={`/dashboard/stores/${id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Toko
            </Link>
          </Button>
          <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Hapus Toko
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Hapus Toko</AlertDialogTitle>
                <AlertDialogDescription>
                  Apakah Anda yakin ingin menghapus toko "{store.name}"? Tindakan ini tidak dapat dibatalkan.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Batal</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isDeleting ? "Menghapus..." : "Hapus"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informasi Toko</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium">Deskripsi</h3>
            <p className="text-muted-foreground">{store.description || "Tidak ada deskripsi"}</p>
          </div>
          <div>
            <h3 className="font-medium">Slug</h3>
            <p className="text-muted-foreground">{store.slug}</p>
          </div>
          <div>
            <h3 className="font-medium">URL Toko</h3>
            <div className="flex items-center space-x-2">
              <p className="text-muted-foreground">sellzio.com/store/{store.slug}</p>
              <Button variant="ghost" size="icon" asChild>
                <Link href={`/store/${store.slug}`} target="_blank">
                  <ExternalLink className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="products">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="products">
            <ShoppingBag className="mr-2 h-4 w-4" />
            Produk
          </TabsTrigger>
          <TabsTrigger value="orders">
            <Package className="mr-2 h-4 w-4" />
            Pesanan
          </TabsTrigger>
        </TabsList>
        <TabsContent value="products" className="mt-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Daftar Produk</h2>
            <Button asChild>
              <Link href={`/dashboard/products/create?storeId=${id}`}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Tambah Produk
              </Link>
            </Button>
          </div>
          <ProductList storeId={id} />
        </TabsContent>
        <TabsContent value="orders" className="mt-6">
          <div className="text-center p-8">
            <h3 className="text-lg font-semibold mb-2">Belum ada pesanan</h3>
            <p className="text-muted-foreground">Toko ini belum memiliki pesanan.</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
