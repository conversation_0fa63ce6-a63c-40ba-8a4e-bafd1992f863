"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Edit,
  Trash2,
  ChevronLeft,
  Package,
  ShoppingBag,
  Users,
  DollarSign,
  BarChart,
  Settings,
  PlusCircle,
  ExternalLink,
} from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { storesAPI, type Store } from "@/lib/api/stores"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { ProductList } from "@/components/dashboard/products/product-list"

export default function TenantStoreDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [store, setStore] = useState<Store | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  useEffect(() => {
    const fetchStore = async () => {
      try {
        const data = await storesAPI.getById(params.id)
        setStore(data)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching store:", err)
        setError("Gagal memuat data toko. Silakan coba lagi.")
        setLoading(false)
      }
    }

    fetchStore()
  }, [params.id])

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await storesAPI.delete(params.id)
      router.push("/tenant/dashboard/stores")
    } catch (err) {
      console.error("Error deleting store:", err)
      setError("Gagal menghapus toko. Silakan coba lagi.")
      setIsDeleting(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-8 w-64" />
        </div>
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!store) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold mb-2">Toko tidak ditemukan</h3>
        <p className="text-muted-foreground mb-4">Toko yang Anda cari tidak ditemukan atau telah dihapus.</p>
        <Button asChild>
          <Link href="/tenant/dashboard/stores">Kembali ke Daftar Toko</Link>
        </Button>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="icon" asChild>
          <Link href="/tenant/dashboard/stores">
            <ChevronLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">{store.name}</h1>
      </div>

      <div className="relative h-64 bg-muted rounded-lg overflow-hidden">
        {store.banner ? (
          <img src={store.banner || "/placeholder.svg"} alt={store.name} className="w-full h-full object-cover" />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-6xl font-bold text-muted-foreground">{store.name.charAt(0)}</div>
          </div>
        )}
        <div className="absolute bottom-4 right-4 flex gap-2">
          <Button asChild>
            <Link href={`/tenant/dashboard/stores/${params.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Toko
            </Link>
          </Button>
          <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Hapus Toko
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Hapus Toko</AlertDialogTitle>
                <AlertDialogDescription>
                  Apakah Anda yakin ingin menghapus toko "{store.name}"? Tindakan ini tidak dapat dibatalkan.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Batal</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isDeleting ? "Menghapus..." : "Hapus"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <ShoppingBag className="h-8 w-8 text-primary mb-2" />
            <div className="text-2xl font-bold">45</div>
            <p className="text-sm text-muted-foreground">Produk</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <Package className="h-8 w-8 text-primary mb-2" />
            <div className="text-2xl font-bold">128</div>
            <p className="text-sm text-muted-foreground">Pesanan</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <Users className="h-8 w-8 text-primary mb-2" />
            <div className="text-2xl font-bold">256</div>
            <p className="text-sm text-muted-foreground">Pelanggan</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <DollarSign className="h-8 w-8 text-primary mb-2" />
            <div className="text-2xl font-bold">{formatCurrency(5250000)}</div>
            <p className="text-sm text-muted-foreground">Pendapatan</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informasi Toko</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium">Deskripsi</h3>
            <p className="text-muted-foreground">{store.description || "Tidak ada deskripsi"}</p>
          </div>
          <div>
            <h3 className="font-medium">Slug</h3>
            <p className="text-muted-foreground">{store.slug}</p>
          </div>
          <div>
            <h3 className="font-medium">URL Toko</h3>
            <div className="flex items-center space-x-2">
              <p className="text-muted-foreground">sellzio.com/store/{store.slug}</p>
              <Button variant="ghost" size="icon" asChild>
                <Link href={`/store/${store.slug}`} target="_blank">
                  <ExternalLink className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="products">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="products">
            <ShoppingBag className="mr-2 h-4 w-4" />
            Produk
          </TabsTrigger>
          <TabsTrigger value="orders">
            <Package className="mr-2 h-4 w-4" />
            Pesanan
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart className="mr-2 h-4 w-4" />
            Analitik
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="mr-2 h-4 w-4" />
            Pengaturan
          </TabsTrigger>
        </TabsList>
        <TabsContent value="products" className="mt-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Daftar Produk</h2>
            <Button asChild>
              <Link href={`/tenant/dashboard/products/create?storeId=${params.id}`}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Tambah Produk
              </Link>
            </Button>
          </div>
          <ProductList storeId={params.id} />
        </TabsContent>
        <TabsContent value="orders" className="mt-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Daftar Pesanan</h2>
            <Button variant="outline">
              <ExternalLink className="mr-2 h-4 w-4" />
              Ekspor Data
            </Button>
          </div>
          <Card>
            <CardContent className="p-6">
              <div className="text-center py-8">
                <Package className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-2 text-lg font-semibold">Belum ada pesanan</h3>
                <p className="text-muted-foreground mb-4">Toko ini belum memiliki pesanan.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="analytics" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Analitik Toko</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-2 text-lg font-semibold">Analitik Belum Tersedia</h3>
                <p className="text-muted-foreground mb-4">Fitur analitik akan segera tersedia.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Pengaturan Toko</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Settings className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-2 text-lg font-semibold">Pengaturan Belum Tersedia</h3>
                <p className="text-muted-foreground mb-4">Fitur pengaturan akan segera tersedia.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
