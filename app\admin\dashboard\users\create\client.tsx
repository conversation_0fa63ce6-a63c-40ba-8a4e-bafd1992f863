"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useNotifications } from "@/components/providers/notifications-provider"
import { UserForm } from "@/components/admin/users/user-form"
import { Button } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"

export default function UserCreateClient() {
  const [isClient, setIsClient] = useState(false)
  const router = useRouter()
  const { addNotification } = useNotifications()

  // Ensure component is mounted before rendering
  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleSubmit = async (data: any) => {
    try {
      // Simulate API call
      console.log("Creating user with data:", data)

      // Show success notification
      addNotification({
        message: "User created successfully!",
        type: "success",
      })

      // Redirect to users list
      router.push("/admin/dashboard/users")
    } catch (error) {
      console.error("Error creating user:", error)

      // Show error notification
      addNotification({
        message: "Failed to create user. Please try again.",
        type: "error",
      })
    }
  }

  if (!isClient) {
    return null
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h2 className="text-2xl font-bold tracking-tight">Create User</h2>
          <p className="text-muted-foreground">Create a new user in the Sellzio platform</p>
        </div>
        <Button variant="outline" size="sm" onClick={() => router.push("/admin/dashboard/users")}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Users
        </Button>
      </div>

      <div className="rounded-md border p-6">
        <UserForm onSubmit={handleSubmit} />
      </div>
    </div>
  )
}
