"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useAuth } from "@/contexts/auth-context"
import { Building, ShoppingBag, User, Shield, ChevronDown } from "lucide-react"

export function RoleSwitcher() {
  const { user, updateUserRole } = useAuth()
  const [isLoading, setIsLoading] = useState(false)

  const handleRoleChange = async (role: "admin" | "tenant" | "store" | "buyer") => {
    setIsLoading(true)
    try {
      await updateUserRole(role)
    } catch (error) {
      console.error("Failed to update role:", error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!user) return null

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" disabled={isLoading}>
          {isLoading ? (
            "Beralih..."
          ) : (
            <>
              {user.role === "admin" && <Shield className="mr-2 h-4 w-4" />}
              {user.role === "tenant" && <Building className="mr-2 h-4 w-4" />}
              {user.role === "store" && <ShoppingBag className="mr-2 h-4 w-4" />}
              {user.role === "buyer" && <User className="mr-2 h-4 w-4" />}
              {user.role === "admin" && "Admin"}
              {user.role === "tenant" && "Tenant"}
              {user.role === "store" && "Store"}
              {user.role === "buyer" && "Buyer"}
              <ChevronDown className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Beralih ke</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {user.role !== "admin" && (
          <DropdownMenuItem onClick={() => handleRoleChange("admin")}>
            <Shield className="mr-2 h-4 w-4" />
            Admin
          </DropdownMenuItem>
        )}
        {user.role !== "tenant" && (
          <DropdownMenuItem onClick={() => handleRoleChange("tenant")}>
            <Building className="mr-2 h-4 w-4" />
            Tenant
          </DropdownMenuItem>
        )}
        {user.role !== "store" && (
          <DropdownMenuItem onClick={() => handleRoleChange("store")}>
            <ShoppingBag className="mr-2 h-4 w-4" />
            Store
          </DropdownMenuItem>
        )}
        {user.role !== "buyer" && (
          <DropdownMenuItem onClick={() => handleRoleChange("buyer")}>
            <User className="mr-2 h-4 w-4" />
            Buyer
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
