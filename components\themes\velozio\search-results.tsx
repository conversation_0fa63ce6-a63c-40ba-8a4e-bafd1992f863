"use client"

import { useState, useEffect } from "react"
import { VelozioProductCard } from "./product-card"
import { cn } from "@/lib/utils"
import { ArrowUp, ArrowDown, AlertTriangle } from "lucide-react"

interface Product {
  id: number
  name: string
  price: string
  originalPrice?: string
  discount?: string
  image: string
  category: string
  shortName?: string
  isMall?: boolean
  rating?: number
  sold?: number
  shipping?: string
  cod?: boolean
}

interface SearchResultsProps {
  query: string
  products: Product[]
  className?: string
  onProductClick?: (product: Product) => void
}

export function VelozioSearchResults({ query, products, className, onProductClick }: SearchResultsProps) {
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products)
  const [activeFilter, setActiveFilter] = useState<string>("terkait")
  const [priceDirection, setPriceDirection] = useState<"asc" | "desc">("asc")
  const [showNotFound, setShowNotFound] = useState(false)

  // Apply filter when filter changes
  useEffect(() => {
    if (products.length === 0) {
      setShowNotFound(true)
      return
    }

    setShowNotFound(false)
    const result = [...products]

    switch (activeFilter) {
      case "terlaris":
        result.sort((a, b) => (b.sold || 0) - (a.sold || 0))
        break
      case "terbaru":
        // Sort by id (assuming newer products have higher ids)
        result.sort((a, b) => b.id - a.id)
        break
      case "harga":
        // Sort by price
        result.sort((a, b) => {
          const priceA = Number.parseInt(a.price.replace(/\D/g, ""))
          const priceB = Number.parseInt(b.price.replace(/\D/g, ""))
          return priceDirection === "asc" ? priceA - priceB : priceB - priceA
        })
        break
      default: // "terkait" or default
        // No specific sorting
        break
    }

    setFilteredProducts(result)
  }, [products, activeFilter, priceDirection])

  // Handle filter click
  const handleFilterClick = (filter: string) => {
    if (filter === "harga" && activeFilter === "harga") {
      // Toggle price direction
      setPriceDirection((prev) => (prev === "asc" ? "desc" : "asc"))
    } else {
      setActiveFilter(filter)
    }
  }

  if (showNotFound) {
    return (
      <div className={cn("bg-white p-8 rounded-lg text-center", className)}>
        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <AlertTriangle className="h-10 w-10 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium mb-2">Hasil tidak ditemukan</h3>
        <p className="text-gray-500 mb-6">Mohon coba kata kunci yang lain atau yang lebih umum.</p>
        <div className="space-y-3">
          <button className="w-full bg-primary text-white py-2 px-4 rounded-md">Coba kata kunci lain</button>
          <button className="w-full border border-primary text-primary py-2 px-4 rounded-md">
            Coba produk lainnya
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Filter tabs */}
      <div className="bg-white sticky top-0 z-10 border-b">
        <div className="flex max-w-3xl mx-auto">
          {["terkait", "terlaris", "terbaru", "harga"].map((filter) => (
            <button
              key={filter}
              className={cn(
                "flex-1 py-3 px-2 text-sm relative",
                activeFilter === filter ? "text-primary font-medium" : "text-gray-600",
              )}
              onClick={() => handleFilterClick(filter)}
            >
              <div className="flex items-center justify-center">
                {filter === "harga" ? (
                  <>
                    Harga
                    {activeFilter === "harga" &&
                      (priceDirection === "asc" ? (
                        <ArrowUp className="ml-1 h-3 w-3" />
                      ) : (
                        <ArrowDown className="ml-1 h-3 w-3" />
                      ))}
                  </>
                ) : (
                  filter.charAt(0).toUpperCase() + filter.slice(1)
                )}
              </div>
              {activeFilter === filter && <div className="absolute bottom-0 left-1/4 right-1/4 h-0.5 bg-primary"></div>}
            </button>
          ))}
        </div>
      </div>

      {/* Product grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 p-3">
        {filteredProducts.map((product) => (
          <VelozioProductCard key={product.id} product={product} onClick={() => onProductClick?.(product)} />
        ))}
      </div>
    </div>
  )
}
