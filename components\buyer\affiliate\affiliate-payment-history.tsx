import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, Eye } from "lucide-react"

export function AffiliatePaymentHistory() {
  // Sample payment history data
  const payments = [
    {
      id: "PAY-2023-05-15",
      date: "May 15, 2023",
      amount: 925.42,
      status: "completed",
      method: "Bank Transfer",
      transactionId: "TRX-********",
      reference: "REF-2023-05-15-001",
    },
    {
      id: "PAY-2023-04-15",
      date: "April 15, 2023",
      amount: 812.3,
      status: "completed",
      method: "PayPal",
      transactionId: "TRX-********",
      reference: "REF-2023-04-15-001",
    },
    {
      id: "PAY-2023-03-15",
      date: "March 15, 2023",
      amount: 764.85,
      status: "completed",
      method: "Bank Transfer",
      transactionId: "TRX-********",
      reference: "REF-2023-03-15-001",
    },
    {
      id: "PAY-2023-02-15",
      date: "February 15, 2023",
      amount: 695.2,
      status: "completed",
      method: "PayPal",
      transactionId: "TRX-********",
      reference: "REF-2023-02-15-001",
    },
    {
      id: "PAY-2023-06-15",
      date: "June 15, 2023",
      amount: 842.5,
      status: "pending",
      method: "Bank Transfer",
      transactionId: "TRX-PENDING",
      reference: "REF-2023-06-15-001",
    },
  ]

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Payment History</CardTitle>
        <CardDescription>View and download your payment receipts</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Method</TableHead>
              <TableHead>Transaction ID</TableHead>
              <TableHead>Reference</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {payments.map((payment) => (
              <TableRow key={payment.id}>
                <TableCell>{payment.date}</TableCell>
                <TableCell>${payment.amount.toFixed(2)}</TableCell>
                <TableCell>
                  <Badge variant={payment.status === "completed" ? "success" : "outline"}>
                    {payment.status === "completed" ? "Completed" : "Pending"}
                  </Badge>
                </TableCell>
                <TableCell>{payment.method}</TableCell>
                <TableCell>{payment.transactionId}</TableCell>
                <TableCell>{payment.reference}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button variant="ghost" size="icon" disabled={payment.status !== "completed"}>
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
