"use client"

import { useState } from "react"
import { DataTable } from "@/components/ui/data-table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Eye, Edit, UserX, Shield, Activity } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"

// Mock data for admin users
const adminUsers = [
  {
    id: "a1",
    name: "<PERSON>",
    email: "<EMAIL>",
    roleLevel: "Super Admin",
    createdDate: "2023-01-15",
    lastLogin: "2023-05-10",
    permissions: ["all"],
  },
  {
    id: "a2",
    name: "<PERSON>",
    email: "<EMAIL>",
    roleLevel: "System Admin",
    createdDate: "2023-02-20",
    lastLogin: "2023-05-09",
    permissions: ["users.view", "users.edit", "stores.view", "stores.edit", "products.view", "products.edit"],
  },
  {
    id: "a3",
    name: "Robert <PERSON>",
    email: "<EMAIL>",
    roleLevel: "Support Admin",
    createdDate: "2023-03-05",
    lastLogin: "2023-05-08",
    permissions: ["users.view", "stores.view", "products.view", "support.full"],
  },
  {
    id: "a4",
    name: "Emily Davis",
    email: "<EMAIL>",
    roleLevel: "Content Admin",
    createdDate: "2023-03-10",
    lastLogin: "2023-05-07",
    permissions: ["content.full", "marketing.view"],
  },
  {
    id: "a5",
    name: "Michael Wilson",
    email: "<EMAIL>",
    roleLevel: "Financial Admin",
    createdDate: "2023-03-15",
    lastLogin: "2023-05-06",
    permissions: ["financial.full", "reports.view"],
  },
]

// Mock data for admin activity logs
const activityLogs = [
  {
    id: "log1",
    adminId: "a1",
    adminName: "John Doe",
    action: "User suspended",
    target: "user/u5",
    targetName: "Michael Wilson",
    timestamp: "2023-05-10 14:32:45",
    ip: "***********",
  },
  {
    id: "log2",
    adminId: "a2",
    adminName: "Jane Smith",
    action: "Store approved",
    target: "store/s3",
    targetName: "Fashion Boutique",
    timestamp: "2023-05-10 13:15:22",
    ip: "***********",
  },
  {
    id: "log3",
    adminId: "a1",
    adminName: "John Doe",
    action: "Product removed",
    target: "product/p7",
    targetName: "Wireless Headphones",
    timestamp: "2023-05-10 11:45:10",
    ip: "***********",
  },
  {
    id: "log4",
    adminId: "a3",
    adminName: "Robert Johnson",
    action: "Support ticket resolved",
    target: "ticket/t12",
    targetName: "Payment Issue #12",
    timestamp: "2023-05-10 10:22:33",
    ip: "***********",
  },
  {
    id: "log5",
    adminId: "a2",
    adminName: "Jane Smith",
    action: "Admin user created",
    target: "user/a5",
    targetName: "Michael Wilson",
    timestamp: "2023-05-09 16:40:15",
    ip: "***********",
  },
]

// Permission categories
const permissionCategories = [
  {
    name: "Users",
    permissions: ["users.view", "users.create", "users.edit", "users.delete"],
  },
  {
    name: "Stores",
    permissions: ["stores.view", "stores.create", "stores.edit", "stores.delete", "stores.approve"],
  },
  {
    name: "Products",
    permissions: ["products.view", "products.create", "products.edit", "products.delete", "products.moderate"],
  },
  {
    name: "Content",
    permissions: ["content.view", "content.create", "content.edit", "content.delete", "content.publish"],
  },
  {
    name: "Financial",
    permissions: ["financial.view", "financial.process", "financial.refund", "financial.reports"],
  },
  {
    name: "Marketing",
    permissions: ["marketing.view", "marketing.create", "marketing.edit", "marketing.delete", "marketing.publish"],
  },
  {
    name: "Support",
    permissions: ["support.view", "support.respond", "support.escalate", "support.resolve"],
  },
  {
    name: "System",
    permissions: ["system.settings", "system.logs", "system.backup", "system.maintenance"],
  },
]

export default function AdminUsers() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedAdmin, setSelectedAdmin] = useState<(typeof adminUsers)[0] | null>(null)
  const [activeTab, setActiveTab] = useState("admins")

  // Filter admin users based on search term
  const filteredAdmins = adminUsers.filter((admin) => {
    return (
      admin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admin.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admin.roleLevel.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })

  // Filter activity logs for selected admin
  const filteredLogs = selectedAdmin ? activityLogs.filter((log) => log.adminId === selectedAdmin.id) : activityLogs

  const adminColumns = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "roleLevel",
      header: "Role Level",
      cell: ({ row }) => {
        const roleLevel = row.getValue("roleLevel") as string
        return (
          <Badge
            variant={
              roleLevel === "Super Admin" ? "destructive" : roleLevel === "System Admin" ? "default" : "secondary"
            }
          >
            {roleLevel}
          </Badge>
        )
      },
    },
    {
      accessorKey: "createdDate",
      header: "Created Date",
    },
    {
      accessorKey: "lastLogin",
      header: "Last Login",
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const admin = row.original as (typeof adminUsers)[0]
        return (
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" title="View Admin" onClick={() => setSelectedAdmin(admin)}>
              <Eye className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Edit Admin">
              <Edit className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Suspend Admin">
              <UserX className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              title="View Permissions"
              onClick={() => {
                setSelectedAdmin(admin)
                setActiveTab("permissions")
              }}
            >
              <Shield className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              title="View Activity"
              onClick={() => {
                setSelectedAdmin(admin)
                setActiveTab("activity")
              }}
            >
              <Activity className="h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]

  const activityColumns = [
    {
      accessorKey: "adminName",
      header: "Admin",
    },
    {
      accessorKey: "action",
      header: "Action",
    },
    {
      accessorKey: "targetName",
      header: "Target",
    },
    {
      accessorKey: "timestamp",
      header: "Timestamp",
    },
    {
      accessorKey: "ip",
      header: "IP Address",
    },
  ]

  return (
    <div>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="admins">Admin Users</TabsTrigger>
          <TabsTrigger value="permissions" disabled={!selectedAdmin}>
            Permissions
          </TabsTrigger>
          <TabsTrigger value="activity">Activity Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="admins">
          <Card>
            <CardHeader>
              <CardTitle>Platform Administrators</CardTitle>
              <CardDescription>Manage users with administrative access to the platform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center mb-6">
                <Input
                  placeholder="Search admins..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
                <Button>Add New Admin</Button>
              </div>

              <DataTable columns={adminColumns} data={filteredAdmins} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions">
          {selectedAdmin && (
            <Card>
              <CardHeader>
                <CardTitle>Permissions for {selectedAdmin.name}</CardTitle>
                <CardDescription>Manage permissions for this administrator</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {permissionCategories.map((category) => (
                    <div key={category.name} className="border rounded-lg p-4">
                      <h3 className="text-lg font-medium mb-2">{category.name}</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {category.permissions.map((permission) => (
                          <div key={permission} className="flex items-center space-x-2">
                            <Checkbox
                              id={permission}
                              checked={
                                selectedAdmin.permissions.includes("all") ||
                                selectedAdmin.permissions.includes(`${category.name.toLowerCase()}.full`) ||
                                selectedAdmin.permissions.includes(permission)
                              }
                              disabled={selectedAdmin.permissions.includes("all")}
                            />
                            <label
                              htmlFor={permission}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {permission.split(".")[1].charAt(0).toUpperCase() + permission.split(".")[1].slice(1)}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                  <div className="flex justify-end">
                    <Button>Save Permissions</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>
                {selectedAdmin ? `Activity Logs for ${selectedAdmin.name}` : "All Admin Activity Logs"}
              </CardTitle>
              <CardDescription>
                {selectedAdmin
                  ? `View all actions performed by ${selectedAdmin.name}`
                  : "View all administrative actions across the platform"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable columns={activityColumns} data={filteredLogs} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
