"use client"
import { Star, Truck, DollarSign } from "lucide-react"
import { cn } from "@/lib/utils"

interface ProductCardProps {
  product: {
    id: number
    name: string
    price: string
    originalPrice?: string
    discount?: string
    image: string
    category: string
    rating?: number
    sold?: number
    shipping?: string
    isMall?: boolean
    cod?: boolean
  }
  variant?: "simple" | "standard"
  className?: string
  onClick?: () => void
}

export function VelozioProductCard({ product, variant = "standard", className, onClick }: ProductCardProps) {
  if (variant === "simple") {
    return (
      <div
        className={cn(
          "bg-white rounded-md overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer transform hover:-translate-y-1",
          className,
        )}
        onClick={onClick}
      >
        <img src={product.image || "/placeholder.svg"} alt={product.name} className="w-full h-36 object-cover" />
        <div className="p-2 text-center">
          <h3 className="text-sm font-medium truncate">{product.name}</h3>
        </div>
      </div>
    )
  }

  return (
    <div
      className={cn(
        "bg-white rounded-md overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer transform hover:-translate-y-1 relative",
        className,
      )}
      onClick={onClick}
    >
      <img src={product.image || "/placeholder.svg"} alt={product.name} className="w-full h-40 object-cover" />
      <div className="p-3">
        <div className="mb-1">
          {product.isMall && (
            <span className="inline-block bg-primary text-white text-xs font-semibold px-1 rounded mr-1">Mall</span>
          )}
          <h3 className="text-sm font-medium line-clamp-2">{product.name}</h3>
        </div>

        <div className="flex items-center text-xs text-gray-600 mb-1">
          {product.rating && (
            <div className="flex items-center text-primary mr-2">
              <Star className="h-3 w-3 mr-0.5 fill-current" />
              <span>{product.rating}</span>
            </div>
          )}
          {product.sold && (
            <div className="flex items-center pl-2 border-l border-gray-300">
              <span>Terjual {product.sold}</span>
            </div>
          )}
        </div>

        {product.shipping && (
          <div className="flex items-center text-xs text-gray-600 mb-1">
            <Truck className="h-3 w-3 mr-1 text-emerald-500" />
            <span>{product.shipping}</span>
          </div>
        )}

        <div className="flex items-center flex-wrap">
          <span className="text-primary font-semibold">{product.price}</span>
          {product.originalPrice && (
            <span className="text-xs text-gray-400 line-through ml-1">{product.originalPrice}</span>
          )}
          {product.discount && (
            <span className="text-xs text-primary bg-red-50 px-1 rounded ml-1">-{product.discount}</span>
          )}
        </div>
      </div>

      {product.cod && (
        <div className="absolute bottom-2 right-2 bg-primary text-white text-xs px-1 py-0.5 rounded flex items-center">
          <DollarSign className="h-3 w-3 mr-0.5" />
          <span>COD</span>
        </div>
      )}
    </div>
  )
}
