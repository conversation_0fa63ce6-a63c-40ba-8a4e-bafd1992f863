"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, ArrowLeft, ThumbsUp, Edit, Trash2 } from "lucide-react"
import Image from "next/image"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export function ReviewDetail({ review, onClose, onEdit }) {
  const [liked, setLiked] = useState(false)
  const [likeCount, setLikeCount] = useState(review.likes || 0)

  const handleLike = () => {
    if (liked) {
      setLikeCount(likeCount - 1)
    } else {
      setLikeCount(likeCount + 1)
    }
    setLiked(!liked)
  }

  const handleDelete = () => {
    // Implementasi penghapusan ulasan
    console.log("Menghapus ulasan:", review.id)
    onClose()
  }

  // Render bintang rating
  const renderStars = (rating) => {
    return Array(5)
      .fill(0)
      .map((_, i) => (
        <Star key={i} className={`h-5 w-5 ${i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`} />
      ))
  }

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={onClose}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <CardTitle>Detail Ulasan</CardTitle>
        </div>
        <Badge variant={review.status === "published" ? "success" : "outline"}>
          {review.status === "published" ? "Dipublikasikan" : "Menunggu"}
        </Badge>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative h-40 w-full md:h-auto md:w-40 rounded-md overflow-hidden bg-gray-100">
            <Image
              src={review.productImage || "/placeholder.svg"}
              alt={review.productName}
              fill
              className="object-cover"
            />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-medium">{review.productName}</h3>
            <p className="text-sm text-muted-foreground">
              {review.storeName} • Order #{review.orderId.split("-")[1]}
            </p>
            <div className="mt-3 flex items-center">
              {renderStars(review.rating)}
              <span className="ml-2 text-sm font-medium">{review.rating}/5</span>
            </div>
            <p className="mt-4 text-sm">
              {new Date(review.date).toLocaleDateString("id-ID", {
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </p>
          </div>
        </div>

        <div>
          <h4 className="mb-2 font-medium">Komentar Anda</h4>
          <p className="text-sm">{review.comment}</p>
        </div>

        {review.hasImages && review.images && review.images.length > 0 && (
          <div>
            <h4 className="mb-2 font-medium">Foto</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {review.images.map((img, idx) => (
                <div key={idx} className="relative aspect-square rounded-md overflow-hidden bg-gray-100">
                  <Image
                    src={img || "/placeholder.svg"}
                    alt={`Review image ${idx + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className={liked ? "text-primary" : ""} onClick={handleLike}>
            <ThumbsUp className={`mr-1 h-4 w-4 ${liked ? "fill-primary" : ""}`} />
            {likeCount} Suka
          </Button>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onEdit}>
          <Edit className="mr-2 h-4 w-4" />
          Edit Ulasan
        </Button>

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="destructive">
              <Trash2 className="mr-2 h-4 w-4" />
              Hapus Ulasan
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Hapus Ulasan</AlertDialogTitle>
              <AlertDialogDescription>
                Apakah Anda yakin ingin menghapus ulasan ini? Tindakan ini tidak dapat dibatalkan.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Batal</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
                Hapus
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardFooter>
    </Card>
  )
}
