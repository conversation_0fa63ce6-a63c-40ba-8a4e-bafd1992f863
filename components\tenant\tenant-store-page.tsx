"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { formatCurrency } from "@/lib/utils"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { storesAPI, type Store } from "@/lib/api/stores"
import { productsAPI, type Product } from "@/lib/api/products"

interface TenantStorePageProps {
  tenantSlug: string
  storeSlug: string
}

export function TenantStorePage({ tenantSlug, storeSlug }: TenantStorePageProps) {
  const [store, setStore] = useState<Store | null>(null)
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStoreAndProducts = async () => {
      try {
        // Fetch store berdasarkan slug
        const storeData = await storesAPI.getBySlug(storeSlug)

        // Fetch products berdasarkan store ID
        const productsData = await productsAPI.getAll(storeData.id)

        setStore(storeData)
        setProducts(productsData)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching store data:", err)
        setError("Gagal memuat data toko. Silakan coba lagi.")
        setLoading(false)
      }
    }

    fetchStoreAndProducts()
  }, [tenantSlug, storeSlug])

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="h-64 bg-muted">
          <Skeleton className="h-full w-full" />
        </div>
        <div className="container mx-auto px-4 py-8">
          <Skeleton className="h-10 w-64 mb-6" />
          <Skeleton className="h-6 w-full max-w-2xl mb-8" />
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-64 w-full" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!store) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="max-w-md w-full">
          <CardContent className="pt-6 text-center">
            <h1 className="text-2xl font-bold mb-4">Toko Tidak Ditemukan</h1>
            <p className="text-muted-foreground mb-6">
              Toko dengan slug "{storeSlug}" tidak ditemukan atau telah dihapus.
            </p>
            <Button asChild>
              <Link href={`/tenant/${tenantSlug}`}>Kembali ke Tenant</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="h-64 bg-muted flex items-center justify-center">
        {store.banner ? (
          <img src={store.banner || "/placeholder.svg"} alt={store.name} className="w-full h-full object-cover" />
        ) : (
          <div className="text-6xl font-bold text-muted-foreground">{store.name.charAt(0)}</div>
        )}
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center mb-4">
          <Link href={`/tenant/${tenantSlug}`} className="text-sm text-primary hover:underline">
            {tenantSlug}
          </Link>
          <span className="mx-2">/</span>
          <span className="text-sm">{store.name}</span>
        </div>

        <h1 className="text-3xl font-bold mb-4">{store.name}</h1>
        <p className="text-muted-foreground max-w-2xl mb-8">{store.description || "Tidak ada deskripsi"}</p>

        <h2 className="text-2xl font-semibold mb-6">Produk Kami</h2>
        {products.length === 0 ? (
          <Card className="p-8 text-center">
            <h3 className="text-lg font-semibold mb-2">Belum ada produk</h3>
            <p className="text-muted-foreground mb-4">Toko ini belum memiliki produk.</p>
          </Card>
        ) : (
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {products.map((product) => (
              <Card key={product.id} className="overflow-hidden">
                <div className="h-48 bg-muted flex items-center justify-center">
                  {product.images && product.images.length > 0 ? (
                    <img
                      src={product.images[0] || "/placeholder.svg"}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="text-4xl font-bold text-muted-foreground">{product.name.charAt(0)}</div>
                  )}
                </div>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-2">{product.name}</h3>
                  <p className="text-muted-foreground text-sm line-clamp-2 mb-2">
                    {product.description || "Tidak ada deskripsi"}
                  </p>
                  <p className="font-medium text-lg mb-4">{formatCurrency(product.price)}</p>
                  <Button className="w-full">Tambah ke Keranjang</Button>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
