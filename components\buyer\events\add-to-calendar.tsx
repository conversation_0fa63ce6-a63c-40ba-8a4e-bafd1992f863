"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import type { Event } from "./types"

interface AddToCalendarProps {
  event: Event
  onClose: () => void
}

export function AddToCalendar({ event, onClose }: AddToCalendarProps) {
  const handleAddToCalendar = (type: string) => {
    // Implementasi untuk menambahkan ke kalender
    console.log(`Adding to ${type} calendar:`, event)
    onClose()
  }

  return (
    <Card className="absolute bottom-full right-0 z-50 mb-2 w-64">
      <CardHeader className="p-3">
        <CardTitle className="text-sm">Tambahkan ke Kalender</CardTitle>
        <CardDescription className="text-xs">Pilih layanan kalender</CardDescription>
      </CardHeader>
      <CardContent className="grid gap-2 p-3">
        <Button variant="outline" size="sm" className="justify-start" onClick={() => handleAddToCalendar("google")}>
          Google Calendar
        </Button>
        <Button variant="outline" size="sm" className="justify-start" onClick={() => handleAddToCalendar("apple")}>
          Apple Calendar
        </Button>
        <Button variant="outline" size="sm" className="justify-start" onClick={() => handleAddToCalendar("outlook")}>
          Outlook
        </Button>
      </CardContent>
      <CardFooter className="p-3 pt-0">
        <Button size="sm" variant="ghost" onClick={onClose} className="w-full">
          Batal
        </Button>
      </CardFooter>
    </Card>
  )
}
