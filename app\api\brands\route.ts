import { NextRequest, NextResponse } from 'next/server';
import { brandService } from '@/lib/services/brands';

// GET - Mendapatkan semua brands atau difilter berdasarkan query
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const with_counts = searchParams.get('with_counts') === 'true';
    
    const filters = {
      search: searchParams.get('search') || undefined,
      is_active: searchParams.get('is_active') ? searchParams.get('is_active') === 'true' : undefined,
      country: searchParams.get('country') || undefined,
    };
    
    let brands;
    
    if (with_counts) {
      brands = await brandService.getBrandsWithProductCounts();
    } else {
      brands = await brandService.getBrands(filters);
    }
    
    return NextResponse.json(brands);
  } catch (error) {
    console.error('Error fetching brands:', error);
    return NextResponse.json(
      { error: 'Failed to fetch brands' },
      { status: 500 }
    );
  }
}

// POST - Membuat brand baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newBrand = await brandService.createBrand(body);
    
    return NextResponse.json(newBrand, { status: 201 });
  } catch (error) {
    console.error('Error creating brand:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to create brand';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// PUT - Update multiple brands (untuk reordering)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;
    
    if (action === 'reorder') {
      await brandService.reorderBrands(data);
      return NextResponse.json({ message: 'Brands reordered successfully' });
    }
    
    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error updating brands:', error);
    return NextResponse.json(
      { error: 'Failed to update brands' },
      { status: 500 }
    );
  }
}
