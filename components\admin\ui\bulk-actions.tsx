"use client"

import type React from "react"

import { useState } from "react"
import { ChevronDown, Download } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface BulkAction {
  id: string
  label: string
  icon?: React.ReactNode
  destructive?: boolean
  onClick: (selectedIds: string[]) => void
}

interface BulkActionsProps {
  selectedIds: string[]
  actions: BulkAction[]
  onExport?: (format: "csv" | "excel" | "pdf") => void
  className?: string
}

export function BulkActions({ selectedIds, actions, onExport, className }: BulkActionsProps) {
  const [isOpen, setIsOpen] = useState(false)

  const hasSelection = selectedIds.length > 0

  return (
    <div className={className}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="gap-1" disabled={!hasSelection}>
            Actions <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          <DropdownMenuLabel>
            {selectedIds.length} item{selectedIds.length !== 1 ? "s" : ""} selected
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {actions.map((action) => (
            <DropdownMenuItem
              key={action.id}
              onClick={() => {
                action.onClick(selectedIds)
                setIsOpen(false)
              }}
              className={action.destructive ? "text-destructive" : ""}
            >
              {action.icon}
              {action.label}
            </DropdownMenuItem>
          ))}
          {onExport && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Export</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => onExport("csv")}>
                <Download className="mr-2 h-4 w-4" /> Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onExport("excel")}>
                <Download className="mr-2 h-4 w-4" /> Export as Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onExport("pdf")}>
                <Download className="mr-2 h-4 w-4" /> Export as PDF
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
