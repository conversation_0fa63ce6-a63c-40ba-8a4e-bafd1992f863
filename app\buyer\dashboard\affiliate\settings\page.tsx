import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { AffiliateAccountSettings } from "@/components/buyer/affiliate/affiliate-account-settings"
import { AffiliateCommissionSettings } from "@/components/buyer/affiliate/affiliate-commission-settings"
import { AffiliateLinkPreferences } from "@/components/buyer/affiliate/affiliate-link-preferences"

export default function AffiliateSettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">Manage your affiliate account settings and preferences</p>
      </div>

      <Tabs defaultValue="account" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="commission">Commission</TabsTrigger>
          <TabsTrigger value="links">Link Preferences</TabsTrigger>
        </TabsList>
        <TabsContent value="account">
          <AffiliateAccountSettings />
        </TabsContent>
        <TabsContent value="commission">
          <AffiliateCommissionSettings />
        </TabsContent>
        <TabsContent value="links">
          <AffiliateLinkPreferences />
        </TabsContent>
      </Tabs>
    </div>
  )
}
