"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { useImportExport, type ImportExportJob, type ImportOptions, type ExportOptions } from "@/hooks/use-import-export"
import {
  ArrowLeft,
  Upload,
  Download,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  File,
  Package,
  RefreshCw,
  History,
  Filter,
  Eye,
  Trash2
} from "lucide-react"
import Link from "next/link"

function getStatusBadge(status: string) {
  switch (status) {
    case "completed":
      return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Completed</Badge>
    case "processing":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800"><Clock className="h-3 w-3 mr-1" />Processing</Badge>
    case "failed":
      return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Failed</Badge>
    case "pending":
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

function getTypeBadge(type: string) {
  switch (type) {
    case "import":
      return <Badge variant="outline" className="bg-purple-100 text-purple-800"><Upload className="h-3 w-3 mr-1" />Import</Badge>
    case "export":
      return <Badge variant="outline" className="bg-orange-100 text-orange-800"><Download className="h-3 w-3 mr-1" />Export</Badge>
    default:
      return <Badge variant="secondary">{type}</Badge>
  }
}

function formatDateTime(dateTimeString: string) {
  return new Date(dateTimeString).toLocaleString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function ImportExportPage() {
  const {
    jobs,
    loading,
    startImport,
    startExport,
    downloadTemplate,
    downloadExport,
    deleteJob,
    getJobStats
  } = useImportExport()

  const [selectedTab, setSelectedTab] = useState("import")
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [stats, setStats] = useState<any>(null)
  const [importOptions, setImportOptions] = useState<ImportOptions>({
    skip_duplicates: true,
    update_existing: false,
    validate_only: false
  })
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'xlsx',
    filter: 'all'
  })

  // Load stats on mount
  useEffect(() => {
    const loadStats = async () => {
      const jobStats = await getJobStats()
      setStats(jobStats)
    }
    loadStats()
  }, [getJobStats])

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
    }
  }

  const handleStartImport = async () => {
    if (!selectedFile) return

    const success = await startImport(selectedFile, importOptions)
    if (success) {
      setSelectedFile(null)
      // Refresh stats
      const jobStats = await getJobStats()
      setStats(jobStats)
    }
  }

  const handleStartExport = async () => {
    const success = await startExport(exportOptions)
    if (success) {
      // Refresh stats
      const jobStats = await getJobStats()
      setStats(jobStats)
    }
  }

  const handleDownloadTemplate = async (type: 'csv' | 'xlsx') => {
    await downloadTemplate(type)
  }

  const handleDeleteJob = async (jobId: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus job ini?')) {
      await deleteJob(jobId)
      // Refresh stats
      const jobStats = await getJobStats()
      setStats(jobStats)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Import/Export Products</h1>
            <p className="text-muted-foreground">
              Kelola import dan export data produk secara bulk
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
            <History className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{stats.total}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-blue-600">{stats.processing}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Card>
        <CardHeader>
          <div className="flex space-x-1 rounded-lg bg-muted p-1">
            <button
              onClick={() => setSelectedTab("import")}
              className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                selectedTab === "import"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              <Upload className="h-4 w-4 mr-2 inline" />
              Import Products
            </button>
            <button
              onClick={() => setSelectedTab("export")}
              className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                selectedTab === "export"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              <Download className="h-4 w-4 mr-2 inline" />
              Export Products
            </button>
          </div>
        </CardHeader>
        <CardContent>
          {selectedTab === "import" && (
            <div className="space-y-6">
              {/* Import Section */}
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Upload File</h3>
                  <div className="space-y-4">
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                      <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-sm text-muted-foreground mb-4">
                        Drop your CSV or Excel file here, or click to browse
                      </p>
                      <Input
                        type="file"
                        accept=".csv,.xlsx,.xls"
                        onChange={handleFileSelect}
                        className="max-w-xs mx-auto"
                      />
                      {selectedFile && (
                        <p className="text-sm text-green-600 mt-2">
                          File selected: {selectedFile.name}
                        </p>
                      )}
                    </div>

                    <div className="space-y-3">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={importOptions.skip_duplicates}
                          onChange={(e) => setImportOptions(prev => ({
                            ...prev,
                            skip_duplicates: e.target.checked
                          }))}
                        />
                        <span className="text-sm">Skip duplicate products</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={importOptions.update_existing}
                          onChange={(e) => setImportOptions(prev => ({
                            ...prev,
                            update_existing: e.target.checked
                          }))}
                        />
                        <span className="text-sm">Update existing products</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={importOptions.validate_only}
                          onChange={(e) => setImportOptions(prev => ({
                            ...prev,
                            validate_only: e.target.checked
                          }))}
                        />
                        <span className="text-sm">Validate only (don't import)</span>
                      </label>
                    </div>

                    <Button
                      className="w-full"
                      disabled={!selectedFile}
                      onClick={handleStartImport}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Start Import
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-4">Templates & Guidelines</h3>
                  <div className="space-y-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <FileText className="h-8 w-8 text-green-600" />
                          <div>
                            <h4 className="font-medium">CSV Template</h4>
                            <p className="text-sm text-muted-foreground">Standard product import template</p>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full"
                          onClick={() => handleDownloadTemplate('csv')}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download CSV Template
                        </Button>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <FileText className="h-8 w-8 text-blue-600" />
                          <div>
                            <h4 className="font-medium">Excel Template</h4>
                            <p className="text-sm text-muted-foreground">Excel format with validation</p>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full"
                          onClick={() => handleDownloadTemplate('xlsx')}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download Excel Template
                        </Button>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <FileText className="h-8 w-8 text-orange-600" />
                          <div>
                            <h4 className="font-medium">Import Guidelines</h4>
                            <p className="text-sm text-muted-foreground">Format requirements and examples</p>
                          </div>
                        </div>
                        <Button size="sm" variant="outline" className="w-full">
                          <Eye className="h-4 w-4 mr-2" />
                          View Guidelines
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </div>
          )}

          {selectedTab === "export" && (
            <div className="space-y-6">
              {/* Export Section */}
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Export Options</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Export Format</label>
                      <select className="w-full mt-1 px-3 py-2 border rounded-md bg-background">
                        <option value="csv">CSV File</option>
                        <option value="xlsx">Excel File (XLSX)</option>
                        <option value="json">JSON File</option>
                      </select>
                    </div>

                    <div>
                      <label className="text-sm font-medium">Product Filter</label>
                      <select className="w-full mt-1 px-3 py-2 border rounded-md bg-background">
                        <option value="all">All Products</option>
                        <option value="active">Active Products Only</option>
                        <option value="inactive">Inactive Products Only</option>
                        <option value="outofstock">Out of Stock Products</option>
                      </select>
                    </div>

                    <div>
                      <label className="text-sm font-medium">Category Filter</label>
                      <select className="w-full mt-1 px-3 py-2 border rounded-md bg-background">
                        <option value="all">All Categories</option>
                        <option value="electronics">Electronics</option>
                        <option value="fashion">Fashion</option>
                        <option value="home">Home & Living</option>
                        <option value="beauty">Beauty</option>
                      </select>
                    </div>

                    <div>
                      <label className="text-sm font-medium">Date Range</label>
                      <div className="grid grid-cols-2 gap-2 mt-1">
                        <Input type="date" placeholder="From" />
                        <Input type="date" placeholder="To" />
                      </div>
                    </div>

                    <Button
                      className="w-full"
                      onClick={handleStartExport}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Start Export
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-4">Export Presets</h3>
                  <div className="space-y-3">
                    <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <CardContent className="p-4">
                        <h4 className="font-medium">All Products (Full Export)</h4>
                        <p className="text-sm text-muted-foreground">Export semua produk dengan detail lengkap</p>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <CardContent className="p-4">
                        <h4 className="font-medium">Inventory Report</h4>
                        <p className="text-sm text-muted-foreground">Export data stock dan inventory</p>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <CardContent className="p-4">
                        <h4 className="font-medium">Price List</h4>
                        <p className="text-sm text-muted-foreground">Export daftar harga produk</p>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <CardContent className="p-4">
                        <h4 className="font-medium">Product Catalog</h4>
                        <p className="text-sm text-muted-foreground">Export katalog produk untuk marketing</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Import/Export History
          </CardTitle>
          <CardDescription>
            Riwayat proses import dan export produk
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <Skeleton className="h-6 w-20" />
                        <Skeleton className="h-6 w-24" />
                        <Skeleton className="h-4 w-32" />
                      </div>
                      <Skeleton className="h-4 w-24" />
                    </div>
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {jobs.map((job) => (
                <Card key={job.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {getTypeBadge(job.type)}
                          {getStatusBadge(job.status)}
                        </div>
                        <h3 className="font-medium">{job.file_name}</h3>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{job.file_size}</span>
                        <span>•</span>
                        <span>by {job.uploaded_by}</span>
                      </div>
                    </div>

                  {job.status === "processing" && job.progress && (
                    <div className="mb-3">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Progress</span>
                        <span>{job.progress}%</span>
                      </div>
                      <Progress value={job.progress} className="h-2" />
                    </div>
                  )}

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3 text-sm">
                    <div>
                      <span className="text-muted-foreground">Total Rows:</span>
                      <span className="font-medium ml-1">{job.total_rows || 0}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Success:</span>
                      <span className="font-medium text-green-600 ml-1">{job.success_rows || 0}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Failed:</span>
                      <span className="font-medium text-red-600 ml-1">{job.failed_rows || 0}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Started:</span>
                      <span className="font-medium ml-1">{formatDateTime(job.start_time)}</span>
                    </div>
                  </div>

                  {job.errors && job.errors.length > 0 && (
                    <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <h4 className="text-sm font-medium text-red-800 mb-2">Errors ({job.errors.length})</h4>
                      <div className="space-y-1">
                        {job.errors.slice(0, 3).map((error, index) => (
                          <p key={index} className="text-sm text-red-700">
                            Row {error.row}: {error.message}
                          </p>
                        ))}
                        {job.errors.length > 3 && (
                          <p className="text-sm text-red-600">
                            +{job.errors.length - 3} more errors...
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {job.error_message && (
                    <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <h4 className="text-sm font-medium text-red-800 mb-1">Error</h4>
                      <p className="text-sm text-red-700">{job.error_message}</p>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      {job.end_time ? (
                        <span>Completed: {formatDateTime(job.end_time)}</span>
                      ) : (
                        <span>Started: {formatDateTime(job.start_time)}</span>
                      )}
                    </div>

                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      {job.download_url && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-green-600"
                          onClick={() => downloadExport(job.id)}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      )}
                      {job.status === "processing" && (
                        <Button size="sm" variant="outline" className="text-red-600">
                          <XCircle className="h-4 w-4 mr-2" />
                          Cancel
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteJob(job.id)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {!loading && jobs.length === 0 && (
              <div className="text-center py-12">
                <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Belum ada riwayat import/export</h3>
                <p className="text-muted-foreground mb-4">
                  Mulai dengan mengimpor atau mengekspor produk
                </p>
              </div>
            )}
          </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}