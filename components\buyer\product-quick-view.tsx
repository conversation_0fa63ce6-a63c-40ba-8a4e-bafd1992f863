import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Eye, ShoppingCart } from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { ReactNode } from "react"
import { toast } from "@/components/ui/use-toast"

interface ProductQuickViewProps {
  productId: string
  productName: string
  productPrice: number
  productImage: string
  storeName: string
  storeId: string
  inStock: boolean
  originalPrice?: number
  priceDropped?: boolean
  description?: string
  children: ReactNode
}

export function ProductQuickView({
  productId,
  productName,
  productPrice,
  productImage,
  storeName,
  storeId,
  inStock,
  originalPrice,
  priceDropped,
  description = "Detail produk tidak tersedia.",
  children,
}: ProductQuickViewProps) {
  // Fungsi untuk menambahkan ke keranjang
  const addToCart = () => {
    toast({
      title: "Berhasil",
      description: `${productName} ditambahkan ke keranjang`,
    })
  }

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">{productName}</DialogTitle>
          <DialogDescription className="text-muted-foreground cursor-pointer hover:underline">
            <a onClick={(e) => {
              e.preventDefault()
              window.open(`/store/${storeId}`, "_blank")
            }}>
              {storeName}
            </a>
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
          <div className="relative">
            <div className="aspect-square w-full rounded-md overflow-hidden bg-gray-100">
              <img 
                src={productImage || "/placeholder.svg"} 
                alt={productName} 
                className="h-full w-full object-cover" 
              />
            </div>
            {!inStock && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/60 rounded-md">
                <Badge variant="outline" className="bg-black text-white">
                  Stok Habis
                </Badge>
              </div>
            )}
            {priceDropped && <Badge className="absolute right-2 top-2 bg-red-500">Turun Harga</Badge>}
          </div>
          
          <div className="flex flex-col">
            <div className="flex-1 space-y-4">
              <div className="flex items-center gap-2">
                <p className="text-2xl font-bold">{formatCurrency(productPrice)}</p>
                {priceDropped && originalPrice && (
                  <p className="text-sm text-muted-foreground line-through">
                    {formatCurrency(originalPrice)}
                  </p>
                )}
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">Deskripsi Produk</h4>
                <p className="text-sm text-muted-foreground">{description}</p>
              </div>
            </div>
            
            <div className="mt-6 space-y-2">
              <Button 
                className="w-full" 
                size="lg" 
                disabled={!inStock}
                onClick={addToCart}
              >
                <ShoppingCart className="mr-2 h-4 w-4" />
                Tambah ke Keranjang
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full" 
                size="lg"
                onClick={() => window.open(`/products/${productId}`, "_blank")}
              >
                <Eye className="mr-2 h-4 w-4" />
                Lihat Detail Lengkap
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 