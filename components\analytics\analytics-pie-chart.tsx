"use client"
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Respons<PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from "recharts"

// Dummy data untuk chart
const data = [
  { name: "Fashion", value: 35 },
  { name: "Electronics", value: 25 },
  { name: "Home & Garden", value: 15 },
  { name: "<PERSON>", value: 12 },
  { name: "<PERSON>", value: 8 },
  { name: "Others", value: 5 },
]

// Colors for pie chart segments
const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#A4DE6C", "#8884D8"]

export function AnalyticsPieChart() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={100}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip
            formatter={(value: number) => [`${value}%`, "Percentage"]}
            contentStyle={{ backgroundColor: "hsl(var(--card))", borderColor: "hsl(var(--border))" }}
            labelStyle={{ color: "hsl(var(--foreground))" }}
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}
