"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Download, Code } from "lucide-react"
import Image from "next/image"

export function AffiliateBannerGallery() {
  const [bannerSize, setBannerSize] = useState<"all" | "leaderboard" | "square" | "skyscraper">("all")
  const [bannerCategory, setBannerCategory] = useState<"all" | "fashion" | "electronics" | "home" | "beauty">("all")
  const [searchTerm, setSearchTerm] = useState("")

  // Data dummy untuk banner
  const banners = [
    {
      id: "1",
      title: "Fashion Sale 50% Off",
      size: "leaderboard",
      dimensions: "728x90",
      category: "fashion",
      imageUrl: "/fashion-sale-50-off.png",
      htmlCode:
        '<a href="https://szio.co/a1b2c3"><img src="https://sellzio.com/banners/fashion-sale-50-off-728x90.jpg" alt="Fashion Sale 50% Off" width="728" height="90" /></a>',
    },
    {
      id: "2",
      title: "Electronics Mega Deals",
      size: "square",
      dimensions: "300x250",
      category: "electronics",
      imageUrl: "/electronics-mega-deals.png",
      htmlCode:
        '<a href="https://szio.co/d4e5f6"><img src="https://sellzio.com/banners/electronics-mega-deals-300x250.jpg" alt="Electronics Mega Deals" width="300" height="250" /></a>',
    },
    {
      id: "3",
      title: "Home Decor Collection",
      size: "skyscraper",
      dimensions: "160x600",
      category: "home",
      imageUrl: "/home-decor-collection.png",
      htmlCode:
        '<a href="https://szio.co/g7h8i9"><img src="https://sellzio.com/banners/home-decor-collection-160x600.jpg" alt="Home Decor Collection" width="160" height="600" /></a>',
    },
    {
      id: "4",
      title: "Beauty Products Sale",
      size: "leaderboard",
      dimensions: "728x90",
      category: "beauty",
      imageUrl: "/beauty-products-sale.png",
      htmlCode:
        '<a href="https://szio.co/j0k1l2"><img src="https://sellzio.com/banners/beauty-products-sale-728x90.jpg" alt="Beauty Products Sale" width="728" height="90" /></a>',
    },
    {
      id: "5",
      title: "Smartphone Deals",
      size: "square",
      dimensions: "300x250",
      category: "electronics",
      imageUrl: "/smartphone-deals.png",
      htmlCode:
        '<a href="https://szio.co/m3n4o5"><img src="https://sellzio.com/banners/smartphone-deals-300x250.jpg" alt="Smartphone Deals" width="300" height="250" /></a>',
    },
    {
      id: "6",
      title: "Fashion Accessories",
      size: "skyscraper",
      dimensions: "160x600",
      category: "fashion",
      imageUrl: "/assorted-fashion-accessories.png",
      htmlCode:
        '<a href="https://szio.co/p6q7r8"><img src="https://sellzio.com/banners/fashion-accessories-160x600.jpg" alt="Fashion Accessories" width="160" height="600" /></a>',
    },
  ]

  // Filter banners berdasarkan size, category, dan search term
  const filteredBanners = banners.filter(
    (banner) =>
      (bannerSize === "all" || banner.size === bannerSize) &&
      (bannerCategory === "all" || banner.category === bannerCategory) &&
      banner.title.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const copyHtmlCode = (code: string) => {
    navigator.clipboard.writeText(code)
    // Bisa tambahkan toast notification di sini
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-3">
        <div className="space-y-2">
          <Label htmlFor="banner-size">Ukuran Banner</Label>
          <Select value={bannerSize} onValueChange={setBannerSize}>
            <SelectTrigger id="banner-size">
              <SelectValue placeholder="Pilih ukuran" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Ukuran</SelectItem>
              <SelectItem value="leaderboard">Leaderboard (728x90)</SelectItem>
              <SelectItem value="square">Square (300x250)</SelectItem>
              <SelectItem value="skyscraper">Skyscraper (160x600)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="banner-category">Kategori</Label>
          <Select value={bannerCategory} onValueChange={setBannerCategory}>
            <SelectTrigger id="banner-category">
              <SelectValue placeholder="Pilih kategori" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Kategori</SelectItem>
              <SelectItem value="fashion">Fashion</SelectItem>
              <SelectItem value="electronics">Elektronik</SelectItem>
              <SelectItem value="home">Rumah & Dapur</SelectItem>
              <SelectItem value="beauty">Kecantikan</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="banner-search">Cari Banner</Label>
          <Input
            id="banner-search"
            placeholder="Cari berdasarkan judul..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredBanners.map((banner) => (
          <Card key={banner.id}>
            <CardContent className="p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">{banner.title}</h3>
                  <span className="text-xs text-muted-foreground">{banner.dimensions}</span>
                </div>

                <div className="relative overflow-hidden rounded-md border">
                  <Image
                    src={banner.imageUrl || "/placeholder.svg"}
                    alt={banner.title}
                    width={banner.size === "leaderboard" ? 728 : banner.size === "square" ? 300 : 160}
                    height={banner.size === "leaderboard" ? 90 : banner.size === "square" ? 250 : 600}
                    className="w-full object-cover"
                  />
                </div>

                <div className="flex flex-wrap gap-2">
                  <Button size="sm" variant="outline" onClick={() => copyHtmlCode(banner.htmlCode)}>
                    <Code className="mr-2 h-4 w-4" />
                    Copy HTML
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <a href={banner.imageUrl} download target="_blank" rel="noopener noreferrer">
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredBanners.length === 0 && (
        <div className="flex h-40 items-center justify-center rounded-md border">
          <p className="text-muted-foreground">Tidak ada banner yang sesuai dengan filter</p>
        </div>
      )}
    </div>
  )
}
