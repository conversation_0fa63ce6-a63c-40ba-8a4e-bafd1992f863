"use client"

import { useState } from "react"
import { DataTable } from "@/components/ui/data-table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Eye, Edit, Trash, Plus, Save } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"

// Mock data for user roles
const userRoles = [
  {
    id: "r1",
    name: "Super Admin",
    description: "Complete access to all platform features and settings",
    level: "Platform",
    userCount: 2,
    permissions: ["all"],
  },
  {
    id: "r2",
    name: "System Admin",
    description: "Administrative access to platform settings and management",
    level: "Platform",
    userCount: 5,
    permissions: [
      "users.view",
      "users.edit",
      "stores.view",
      "stores.edit",
      "products.view",
      "products.edit",
      "content.view",
      "content.edit",
      "system.view",
    ],
  },
  {
    id: "r3",
    name: "Support Admin",
    description: "Access to support features and limited user management",
    level: "Platform",
    userCount: 8,
    permissions: ["users.view", "stores.view", "products.view", "support.full"],
  },
  {
    id: "r4",
    name: "Content Admin",
    description: "Manage platform content and marketing materials",
    level: "Platform",
    userCount: 3,
    permissions: ["content.full", "marketing.view"],
  },
  {
    id: "r5",
    name: "Financial Admin",
    description: "Manage financial operations and reporting",
    level: "Platform",
    userCount: 4,
    permissions: ["financial.full", "reports.view"],
  },
  {
    id: "r6",
    name: "Tenant Owner",
    description: "Complete access to tenant features and settings",
    level: "Tenant",
    userCount: 12,
    permissions: ["tenant.full"],
  },
  {
    id: "r7",
    name: "Tenant Admin",
    description: "Administrative access to tenant settings and management",
    level: "Tenant",
    userCount: 25,
    permissions: ["tenant.stores", "tenant.products", "tenant.users", "tenant.content"],
  },
  {
    id: "r8",
    name: "Store Owner",
    description: "Complete access to store features and settings",
    level: "Store",
    userCount: 150,
    permissions: ["store.full"],
  },
  {
    id: "r9",
    name: "Store Manager",
    description: "Manage store operations and products",
    level: "Store",
    userCount: 80,
    permissions: ["store.products", "store.orders", "store.customers"],
  },
  {
    id: "r10",
    name: "End User",
    description: "Regular user with buyer capabilities",
    level: "User",
    userCount: 5000,
    permissions: ["user.profile", "user.orders", "user.reviews"],
  },
]

// Permission categories
const permissionCategories = [
  {
    name: "Users",
    permissions: ["users.view", "users.create", "users.edit", "users.delete"],
  },
  {
    name: "Stores",
    permissions: ["stores.view", "stores.create", "stores.edit", "stores.delete", "stores.approve"],
  },
  {
    name: "Products",
    permissions: ["products.view", "products.create", "products.edit", "products.delete", "products.moderate"],
  },
  {
    name: "Content",
    permissions: ["content.view", "content.create", "content.edit", "content.delete", "content.publish"],
  },
  {
    name: "Financial",
    permissions: ["financial.view", "financial.process", "financial.refund", "financial.reports"],
  },
  {
    name: "Marketing",
    permissions: ["marketing.view", "marketing.create", "marketing.edit", "marketing.delete", "marketing.publish"],
  },
  {
    name: "Support",
    permissions: ["support.view", "support.respond", "support.escalate", "support.resolve"],
  },
  {
    name: "System",
    permissions: ["system.settings", "system.logs", "system.backup", "system.maintenance"],
  },
  {
    name: "Tenant",
    permissions: ["tenant.stores", "tenant.products", "tenant.users", "tenant.content", "tenant.settings"],
  },
  {
    name: "Store",
    permissions: ["store.products", "store.orders", "store.customers", "store.settings", "store.analytics"],
  },
  {
    name: "User",
    permissions: ["user.profile", "user.orders", "user.reviews", "user.wishlist", "user.payments"],
  },
]

export default function UserRoles() {
  const [searchTerm, setSearchTerm] = useState("")
  const [levelFilter, setLevelFilter] = useState("all")
  const [selectedRole, setSelectedRole] = useState<(typeof userRoles)[0] | null>(null)
  const [editMode, setEditMode] = useState(false)
  const [editedRole, setEditedRole] = useState<{
    name: string
    description: string
    level: string
    permissions: string[]
  } | null>(null)

  // Filter roles based on search term and level filter
  const filteredRoles = userRoles.filter((role) => {
    const matchesSearch =
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesLevel = levelFilter === "all" || role.level === levelFilter

    return matchesSearch && matchesLevel
  })

  // Get unique values for level filter
  const levels = [...new Set(userRoles.map((role) => role.level))]

  const columns = [
    {
      accessorKey: "name",
      header: "Role Name",
    },
    {
      accessorKey: "description",
      header: "Description",
    },
    {
      accessorKey: "level",
      header: "Level",
      cell: ({ row }) => {
        const level = row.getValue("level") as string
        return (
          <Badge
            variant={
              level === "Platform"
                ? "destructive"
                : level === "Tenant"
                  ? "default"
                  : level === "Store"
                    ? "secondary"
                    : "outline"
            }
          >
            {level}
          </Badge>
        )
      },
    },
    {
      accessorKey: "userCount",
      header: "Users",
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const role = row.original as (typeof userRoles)[0]
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              title="View Role"
              onClick={() => {
                setSelectedRole(role)
                setEditMode(false)
                setEditedRole(null)
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              title="Edit Role"
              onClick={() => {
                setSelectedRole(role)
                setEditMode(true)
                setEditedRole({
                  name: role.name,
                  description: role.description,
                  level: role.level,
                  permissions: [...role.permissions],
                })
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Delete Role">
              <Trash className="h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]

  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (!editedRole) return

    if (checked) {
      setEditedRole({
        ...editedRole,
        permissions: [...editedRole.permissions, permission],
      })
    } else {
      setEditedRole({
        ...editedRole,
        permissions: editedRole.permissions.filter((p) => p !== permission),
      })
    }
  }

  const handleCategoryChange = (category: string, checked: boolean) => {
    if (!editedRole) return

    const categoryPermissions = permissionCategories.find((cat) => cat.name === category)?.permissions || []

    if (checked) {
      // Add all permissions from this category that aren't already in the list
      const newPermissions = [
        ...editedRole.permissions,
        ...categoryPermissions.filter((p) => !editedRole.permissions.includes(p)),
      ]
      setEditedRole({
        ...editedRole,
        permissions: newPermissions,
      })
    } else {
      // Remove all permissions from this category
      setEditedRole({
        ...editedRole,
        permissions: editedRole.permissions.filter((p) => !categoryPermissions.includes(p)),
      })
    }
  }

  const isCategoryChecked = (category: string) => {
    if (!editedRole) return false

    const categoryPermissions = permissionCategories.find((cat) => cat.name === category)?.permissions || []

    return categoryPermissions.every((p) => editedRole.permissions.includes(p))
  }

  const isCategoryIndeterminate = (category: string) => {
    if (!editedRole) return false

    const categoryPermissions = permissionCategories.find((cat) => cat.name === category)?.permissions || []

    const hasAny = categoryPermissions.some((p) => editedRole.permissions.includes(p))
    const hasAll = categoryPermissions.every((p) => editedRole.permissions.includes(p))

    return hasAny && !hasAll
  }

  return (
    <div>
      {selectedRole ? (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle>{editMode ? "Edit Role" : selectedRole.name}</CardTitle>
                <CardDescription>
                  {editMode ? "Modify role details and permissions" : `Role Level: ${selectedRole.level}`}
                </CardDescription>
              </div>
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedRole(null)
                  setEditMode(false)
                  setEditedRole(null)
                }}
              >
                Back to List
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {editMode && editedRole ? (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Role Details</h3>
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="role-name" className="block text-sm font-medium mb-1">
                          Role Name
                        </label>
                        <Input
                          id="role-name"
                          value={editedRole.name}
                          onChange={(e) => setEditedRole({ ...editedRole, name: e.target.value })}
                        />
                      </div>
                      <div>
                        <label htmlFor="role-description" className="block text-sm font-medium mb-1">
                          Description
                        </label>
                        <Textarea
                          id="role-description"
                          value={editedRole.description}
                          onChange={(e) => setEditedRole({ ...editedRole, description: e.target.value })}
                          rows={3}
                        />
                      </div>
                      <div>
                        <label htmlFor="role-level" className="block text-sm font-medium mb-1">
                          Level
                        </label>
                        <select
                          id="role-level"
                          value={editedRole.level}
                          onChange={(e) => setEditedRole({ ...editedRole, level: e.target.value })}
                          className="w-full p-2 border rounded-md"
                        >
                          {levels.map((level) => (
                            <option key={level} value={level}>
                              {level}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium mb-4">Role Information</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Current Users:</div>
                        <div className="col-span-2">{selectedRole.userCount}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Permission Count:</div>
                        <div className="col-span-2">
                          {editedRole.permissions.includes("all")
                            ? "All Permissions"
                            : `${editedRole.permissions.length} Permissions`}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Permissions</h3>
                  <div className="border rounded-lg p-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="all-permissions"
                        checked={editedRole.permissions.includes("all")}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setEditedRole({
                              ...editedRole,
                              permissions: ["all"],
                            })
                          } else {
                            setEditedRole({
                              ...editedRole,
                              permissions: [],
                            })
                          }
                        }}
                      />
                      <label
                        htmlFor="all-permissions"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        All Permissions (Full Access)
                      </label>
                    </div>
                  </div>

                  <div className="space-y-6">
                    {permissionCategories.map((category) => (
                      <div key={category.name} className="border rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-4">
                          <Checkbox
                            id={`category-${category.name}`}
                            checked={isCategoryChecked(category.name)}
                            indeterminate={isCategoryIndeterminate(category.name)}
                            disabled={editedRole.permissions.includes("all")}
                            onCheckedChange={(checked) => {
                              handleCategoryChange(category.name, checked === true)
                            }}
                          />
                          <label
                            htmlFor={`category-${category.name}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {category.name}
                          </label>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ml-6">
                          {category.permissions.map((permission) => (
                            <div key={permission} className="flex items-center space-x-2">
                              <Checkbox
                                id={permission}
                                checked={
                                  editedRole.permissions.includes("all") || editedRole.permissions.includes(permission)
                                }
                                disabled={editedRole.permissions.includes("all")}
                                onCheckedChange={(checked) => {
                                  handlePermissionChange(permission, checked === true)
                                }}
                              />
                              <label
                                htmlFor={permission}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                              >
                                {permission.split(".")[1].charAt(0).toUpperCase() + permission.split(".")[1].slice(1)}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Role Details</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Name:</div>
                        <div className="col-span-2">{selectedRole.name}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Description:</div>
                        <div className="col-span-2">{selectedRole.description}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Level:</div>
                        <div className="col-span-2">
                          <Badge
                            variant={
                              selectedRole.level === "Platform"
                                ? "destructive"
                                : selectedRole.level === "Tenant"
                                  ? "default"
                                  : selectedRole.level === "Store"
                                    ? "secondary"
                                    : "outline"
                            }
                          >
                            {selectedRole.level}
                          </Badge>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Users:</div>
                        <div className="col-span-2">{selectedRole.userCount}</div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium mb-4">Permission Summary</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Access Level:</div>
                        <div className="col-span-2">
                          <Badge variant={selectedRole.permissions.includes("all") ? "destructive" : "default"}>
                            {selectedRole.permissions.includes("all") ? "Full Access" : "Limited Access"}
                          </Badge>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Permission Count:</div>
                        <div className="col-span-2">
                          {selectedRole.permissions.includes("all")
                            ? "All Permissions"
                            : `${selectedRole.permissions.length} Permissions`}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Permissions</h3>
                  {selectedRole.permissions.includes("all") ? (
                    <div className="border rounded-lg p-4 text-center">
                      <p className="text-lg font-medium">This role has full access to all permissions</p>
                      <p className="text-sm text-muted-foreground">
                        Users with this role can perform any action on the platform
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {permissionCategories.map((category) => {
                        const categoryPermissions = category.permissions.filter((p) =>
                          selectedRole.permissions.includes(p),
                        )

                        if (categoryPermissions.length === 0) return null

                        return (
                          <div key={category.name} className="border rounded-lg p-4">
                            <h4 className="font-medium mb-2">{category.name}</h4>
                            <div className="flex flex-wrap gap-2">
                              {categoryPermissions.map((permission) => (
                                <Badge key={permission} variant="outline">
                                  {permission.split(".")[1].charAt(0).toUpperCase() + permission.split(".")[1].slice(1)}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-end space-x-2">
            {editMode ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    setEditMode(false)
                    setEditedRole(null)
                  }}
                >
                  Cancel
                </Button>
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    setEditMode(true)
                    setEditedRole({
                      name: selectedRole.name,
                      description: selectedRole.description,
                      level: selectedRole.level,
                      permissions: [...selectedRole.permissions],
                    })
                  }}
                >
                  Edit Role
                </Button>
                <Button variant="destructive">Delete Role</Button>
              </>
            )}
          </CardFooter>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>User Roles</CardTitle>
                <CardDescription>Manage roles and permissions across the platform</CardDescription>
              </div>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add New Role
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <Input
                  placeholder="Search roles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div>
                <select
                  value={levelFilter}
                  onChange={(e) => setLevelFilter(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="all">All Levels</option>
                  {levels.map((level) => (
                    <option key={level} value={level}>
                      {level}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <DataTable columns={columns} data={filteredRoles} />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
