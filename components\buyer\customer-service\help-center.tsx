"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  ThumbsDown,
  ThumbsUp,
  ChevronRight,
  Play,
  BookOpen,
  FileText,
  ShoppingBag,
  CreditCard,
  TruckIcon,
  Users,
} from "lucide-react"

// Dummy data untuk kategori
const categories = [
  { id: 1, name: "<PERSON><PERSON><PERSON> & <PERSON>", icon: Users, count: 15 },
  { id: 2, name: "Pesanan & Pengiriman", icon: TruckIcon, count: 23 },
  { id: 3, name: "Pembayaran", icon: CreditCard, count: 18 },
  { id: 4, name: "<PERSON>du<PERSON>", icon: ShoppingBag, count: 12 },
  { id: 5, name: "<PERSON><PERSON><PERSON><PERSON>", icon: FileText, count: 9 },
  { id: 6, name: "Panduan Penjual", icon: BookOpen, count: 21 },
]

// Dummy data untuk artikel populer
const popularArticles = [
  {
    id: 1,
    title: "Cara menambahkan produk baru ke toko Anda",
    category: "Panduan Penjual",
    views: 1245,
    helpful: 92,
  },
  {
    id: 2,
    title: "Panduan pengembalian barang dan refund",
    category: "Pesanan & Pengiriman",
    views: 987,
    helpful: 88,
  },
  {
    id: 3,
    title: "Cara mengatur metode pembayaran",
    category: "Pembayaran",
    views: 756,
    helpful: 95,
  },
  {
    id: 4,
    title: "Mengatasi masalah login dan reset password",
    category: "Akun & Keamanan",
    views: 1432,
    helpful: 91,
  },
  {
    id: 5,
    title: "Cara mengatur pengiriman dan ongkos kirim",
    category: "Pesanan & Pengiriman",
    views: 1123,
    helpful: 89,
  },
]

// Dummy data untuk FAQ
const faqs = [
  {
    id: 1,
    question: "Bagaimana cara mendaftar sebagai penjual di SellZio?",
    answer:
      "Untuk mendaftar sebagai penjual di SellZio, Anda perlu login ke akun pembeli Anda terlebih dahulu, kemudian klik menu 'Daftar Toko' di dashboard. Isi formulir pendaftaran toko dengan lengkap dan tunggu persetujuan dari tim kami.",
    category: "Panduan Penjual",
  },
  {
    id: 2,
    question: "Berapa lama waktu pengiriman produk?",
    answer:
      "Waktu pengiriman produk bervariasi tergantung lokasi dan jasa pengiriman yang dipilih. Secara umum, pengiriman dalam kota membutuhkan waktu 1-2 hari, pengiriman antar kota 2-4 hari, dan pengiriman ke daerah terpencil 5-7 hari kerja.",
    category: "Pesanan & Pengiriman",
  },
  {
    id: 3,
    question: "Bagaimana cara melacak pesanan saya?",
    answer:
      "Anda dapat melacak pesanan dengan mengakses halaman 'Pesanan' di dashboard pembeli. Klik pada pesanan yang ingin dilacak, kemudian Anda akan melihat status terkini dan nomor resi pengiriman yang dapat digunakan untuk melacak paket Anda.",
    category: "Pesanan & Pengiriman",
  },
  {
    id: 4,
    question: "Metode pembayaran apa saja yang tersedia?",
    answer:
      "SellZio mendukung berbagai metode pembayaran termasuk kartu kredit/debit, transfer bank, e-wallet (OVO, GoPay, DANA, LinkAja), dan pembayaran melalui minimarket (Indomaret, Alfamart).",
    category: "Pembayaran",
  },
  {
    id: 5,
    question: "Bagaimana cara mengajukan pengembalian dana?",
    answer:
      "Untuk mengajukan pengembalian dana, buka halaman detail pesanan Anda, klik tombol 'Ajukan Pengembalian', pilih alasan pengembalian, unggah bukti pendukung jika ada, dan kirimkan permintaan Anda. Tim kami akan memproses dalam 1-3 hari kerja.",
    category: "Pembayaran",
  },
]

// Dummy data untuk video tutorial
const videoTutorials = [
  {
    id: 1,
    title: "Cara Membuat Toko Online di SellZio",
    duration: "5:32",
    thumbnail: "/video-thumbnail.png",
  },
  {
    id: 2,
    title: "Tutorial Menambahkan Produk Baru",
    duration: "4:18",
    thumbnail: "/video-thumbnail.png",
  },
  {
    id: 3,
    title: "Cara Mengatur Pengiriman dan Pembayaran",
    duration: "7:45",
    thumbnail: "/video-thumbnail.png",
  },
  {
    id: 4,
    title: "Tips Meningkatkan Penjualan di SellZio",
    duration: "10:21",
    thumbnail: "/video-thumbnail.png",
  },
]

export function HelpCenter() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("articles")
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null)

  const toggleFaq = (id: number) => {
    if (expandedFaq === id) {
      setExpandedFaq(null)
    } else {
      setExpandedFaq(id)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Pusat Bantuan</h2>
        <p className="text-muted-foreground">
          Temukan jawaban untuk pertanyaan umum dan pelajari cara menggunakan SellZio
        </p>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
        <Input
          placeholder="Cari artikel bantuan, tutorial, atau FAQ..."
          className="pl-10 py-6 text-lg"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        {categories.map((category) => (
          <Card key={category.id} className="cursor-pointer hover:bg-accent/50 transition-colors">
            <CardContent className="flex items-center p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <category.icon className="h-6 w-6 text-primary" />
              </div>
              <div className="ml-4">
                <h3 className="font-medium">{category.name}</h3>
                <p className="text-sm text-muted-foreground">{category.count} artikel</p>
              </div>
              <ChevronRight className="ml-auto h-5 w-5 text-muted-foreground" />
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs defaultValue="articles" className="space-y-4" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="articles">Artikel Populer</TabsTrigger>
          <TabsTrigger value="faq">FAQ</TabsTrigger>
          <TabsTrigger value="videos">Video Tutorial</TabsTrigger>
        </TabsList>

        <TabsContent value="articles" className="space-y-4">
          {popularArticles.map((article) => (
            <Card key={article.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <h3 className="font-medium">{article.title}</h3>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Badge variant="secondary">{article.category}</Badge>
                      <span>{article.views} dilihat</span>
                      <span className="flex items-center">
                        <ThumbsUp className="mr-1 h-3 w-3" />
                        {article.helpful}%
                      </span>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    Baca
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
          <Button variant="outline" className="w-full">
            Lihat Semua Artikel
          </Button>
        </TabsContent>

        <TabsContent value="faq" className="space-y-4">
          {faqs.map((faq) => (
            <Card key={faq.id}>
              <CardHeader className="cursor-pointer p-6 pb-0" onClick={() => toggleFaq(faq.id)}>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-medium">{faq.question}</CardTitle>
                  <Button variant="ghost" size="icon">
                    <ChevronRight
                      className={`h-5 w-5 transition-transform ${expandedFaq === faq.id ? "rotate-90" : ""}`}
                    />
                  </Button>
                </div>
                <Badge variant="outline" className="mt-2">
                  {faq.category}
                </Badge>
              </CardHeader>
              {expandedFaq === faq.id && (
                <CardContent className="pt-4 pb-6">
                  <p className="text-muted-foreground">{faq.answer}</p>
                  <div className="mt-4 flex items-center gap-4">
                    <span className="text-sm text-muted-foreground">Apakah ini membantu?</span>
                    <Button variant="outline" size="sm" className="h-8 gap-1">
                      <ThumbsUp className="h-4 w-4" />
                      Ya
                    </Button>
                    <Button variant="outline" size="sm" className="h-8 gap-1">
                      <ThumbsDown className="h-4 w-4" />
                      Tidak
                    </Button>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
          <Button variant="outline" className="w-full">
            Lihat Semua FAQ
          </Button>
        </TabsContent>

        <TabsContent value="videos" className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2">
            {videoTutorials.map((video) => (
              <Card key={video.id} className="overflow-hidden">
                <div className="relative">
                  <Image
                    src={video.thumbnail || "/placeholder.svg"}
                    alt={video.title}
                    width={400}
                    height={225}
                    className="w-full object-cover"
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white/90">
                      <Play className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <div className="absolute bottom-2 right-2 rounded bg-black/70 px-2 py-1 text-xs text-white">
                    {video.duration}
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-medium">{video.title}</h3>
                </CardContent>
              </Card>
            ))}
          </div>
          <Button variant="outline" className="w-full">
            Lihat Semua Video
          </Button>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Tidak menemukan yang Anda cari?</CardTitle>
          <CardDescription>Hubungi tim dukungan kami untuk mendapatkan bantuan</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-4 sm:flex-row">
          <Button className="flex-1" asChild>
            <Link href="/buyer/dashboard/customer-service/contact">Hubungi Kami</Link>
          </Button>
          <Button variant="outline" className="flex-1" asChild>
            <Link href="/buyer/dashboard/customer-service/tickets">Buat Tiket Baru</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
