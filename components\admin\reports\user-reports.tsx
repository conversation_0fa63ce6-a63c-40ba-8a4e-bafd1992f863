"use client"

import { useState } from "react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>load,
  Calendar,
  Filter,
  RefreshCw,
  Users,
  UserPlus,
  UserMinus,
  Clock,
  MapPin,
  Smartphone,
  Monitor,
  ChevronDown,
  ChevronUp,
  FileText,
  Share2,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"

// Sample data for user growth
const userGrowthData = [
  { month: "Jan", users: 125000, newUsers: 12500, churnedUsers: 5000 },
  { month: "Feb", users: 132500, newUsers: 13000, churnedUsers: 5500 },
  { month: "Mar", users: 140000, newUsers: 14000, churnedUsers: 6500 },
  { month: "Apr", users: 147500, newUsers: 15000, churnedUsers: 7500 },
  { month: "May", users: 155000, newUsers: 16000, churnedUsers: 8500 },
  { month: "Jun", users: 162500, newUsers: 17000, churnedUsers: 9500 },
]

// Sample data for demographic breakdown
const demographicData = [
  { ageGroup: "18-24", percentage: 22, count: 35750 },
  { ageGroup: "25-34", percentage: 38, count: 61750 },
  { ageGroup: "35-44", percentage: 25, count: 40625 },
  { ageGroup: "45-54", percentage: 10, count: 16250 },
  { ageGroup: "55+", percentage: 5, count: 8125 },
]

// Sample data for geographic distribution
const geographicData = [
  { region: "North America", percentage: 45, count: 73125 },
  { region: "Europe", percentage: 30, count: 48750 },
  { region: "Asia", percentage: 15, count: 24375 },
  { region: "South America", percentage: 5, count: 8125 },
  { region: "Africa", percentage: 3, count: 4875 },
  { region: "Oceania", percentage: 2, count: 3250 },
]

// Sample data for user engagement
const engagementData = [
  { metric: "Daily Active Users", value: 78500, change: 12.5 },
  { metric: "Monthly Active Users", value: 155000, change: 8.2 },
  { metric: "Average Session Duration", value: "8m 45s", change: 5.7 },
  { metric: "Pages per Session", value: 4.2, change: -2.1 },
  { metric: "Bounce Rate", value: "32%", change: -3.5 },
]

// Sample data for retention cohorts
const retentionData = [
  { cohort: "Jan 2023", month1: 100, month2: 85, month3: 72, month4: 65, month5: 60, month6: 58 },
  { cohort: "Feb 2023", month1: 100, month2: 82, month3: 70, month4: 63, month5: 58, month6: null },
  { cohort: "Mar 2023", month1: 100, month2: 87, month3: 75, month4: 68, month5: null, month6: null },
  { cohort: "Apr 2023", month1: 100, month2: 88, month3: 76, month4: null, month5: null, month6: null },
  { cohort: "May 2023", month1: 100, month2: 90, month3: null, month4: null, month5: null, month6: null },
  { cohort: "Jun 2023", month1: 100, month2: null, month3: null, month4: null, month5: null, month6: null },
]

// Sample data for device usage
const deviceData = [
  { device: "Mobile", percentage: 65, count: 105625 },
  { device: "Desktop", percentage: 30, count: 48750 },
  { device: "Tablet", percentage: 5, count: 8125 },
]

export function UserReports() {
  const [activeTab, setActiveTab] = useState("growth")

  // Format large numbers
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num)
  }

  // Get change indicator
  const getChangeIndicator = (change: number) => {
    if (change > 0) {
      return (
        <div className="flex items-center text-green-500">
          <ChevronUp className="mr-1 h-4 w-4" />
          <span>{change}%</span>
        </div>
      )
    } else if (change < 0) {
      return (
        <div className="flex items-center text-red-500">
          <ChevronDown className="mr-1 h-4 w-4" />
          <span>{Math.abs(change)}%</span>
        </div>
      )
    } else {
      return <span>0%</span>
    }
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Reports</h1>
          <p className="text-muted-foreground">Analyze user growth, demographics, and engagement</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="default">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Total Users</CardTitle>
            <CardDescription>Platform-wide user base</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">162.5K</div>
            <div className="flex items-center text-xs text-green-500">
              <ChevronUp className="mr-1 h-4 w-4" />
              <span>4.8% from last month</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>New Users</CardTitle>
            <CardDescription>Last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">17K</div>
            <div className="flex items-center text-xs text-green-500">
              <ChevronUp className="mr-1 h-4 w-4" />
              <span>6.3% from previous period</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Churn Rate</CardTitle>
            <CardDescription>Last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">5.8%</div>
            <div className="flex items-center text-xs text-red-500">
              <ChevronUp className="mr-1 h-4 w-4" />
              <span>0.7% from previous period</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Retention Rate</CardTitle>
            <CardDescription>30-day retention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">72%</div>
            <div className="flex items-center text-xs text-green-500">
              <ChevronUp className="mr-1 h-4 w-4" />
              <span>2.1% from previous period</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="growth">Growth Analytics</TabsTrigger>
          <TabsTrigger value="demographics">Demographics</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
          <TabsTrigger value="retention">Retention</TabsTrigger>
          <TabsTrigger value="prediction">Churn Prediction</TabsTrigger>
        </TabsList>

        <TabsContent value="growth" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>User Growth Analytics</CardTitle>
                  <CardDescription>Track user acquisition and churn over time</CardDescription>
                </div>
                <Select defaultValue="6months">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select time period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30days">Last 30 days</SelectItem>
                    <SelectItem value="3months">Last 3 months</SelectItem>
                    <SelectItem value="6months">Last 6 months</SelectItem>
                    <SelectItem value="1year">Last year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">User Growth Trend</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="h-[300px] w-full bg-muted/20 rounded-md flex items-center justify-center">
                      <LineChart className="h-16 w-16 text-muted-foreground/50" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Acquisition vs Churn</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="h-[300px] w-full bg-muted/20 rounded-md flex items-center justify-center">
                      <BarChart className="h-16 w-16 text-muted-foreground/50" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="md:col-span-2">
                  <CardHeader>
                    <CardTitle className="text-base">Monthly Growth Metrics</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Month</TableHead>
                          <TableHead>Total Users</TableHead>
                          <TableHead>New Users</TableHead>
                          <TableHead>Churned Users</TableHead>
                          <TableHead>Net Growth</TableHead>
                          <TableHead>Growth Rate</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {userGrowthData.map((data) => {
                          const netGrowth = data.newUsers - data.churnedUsers
                          const growthRate = ((netGrowth / (data.users - netGrowth)) * 100).toFixed(1)

                          return (
                            <TableRow key={data.month}>
                              <TableCell className="font-medium">{data.month}</TableCell>
                              <TableCell>{formatNumber(data.users)}</TableCell>
                              <TableCell className="text-green-500">+{formatNumber(data.newUsers)}</TableCell>
                              <TableCell className="text-red-500">-{formatNumber(data.churnedUsers)}</TableCell>
                              <TableCell>
                                {netGrowth > 0 ? "+" : ""}
                                {formatNumber(netGrowth)}
                              </TableCell>
                              <TableCell>
                                <div
                                  className={`flex items-center ${Number.parseFloat(growthRate) > 0 ? "text-green-500" : "text-red-500"}`}
                                >
                                  {Number.parseFloat(growthRate) > 0 ? (
                                    <ChevronUp className="mr-1 h-4 w-4" />
                                  ) : (
                                    <ChevronDown className="mr-1 h-4 w-4" />
                                  )}
                                  <span>{growthRate}%</span>
                                </div>
                              </TableCell>
                            </TableRow>
                          )
                        })}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <Share2 className="mr-2 h-4 w-4" />
                Share Report
              </Button>
              <Button>
                <Download className="mr-2 h-4 w-4" />
                Export Growth Data
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="demographics" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Demographic Reports</CardTitle>
                  <CardDescription>User demographic breakdown and analysis</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Filter by segment" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Segments</SelectItem>
                      <SelectItem value="age">Age Groups</SelectItem>
                      <SelectItem value="region">Regions</SelectItem>
                      <SelectItem value="device">Devices</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    More Filters
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Age Distribution</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="mb-6 h-[200px] w-full bg-muted/20 rounded-md flex items-center justify-center">
                      <PieChart className="h-16 w-16 text-muted-foreground/50" />
                    </div>
                    <div className="space-y-4">
                      {demographicData.map((data) => (
                        <div key={data.ageGroup} className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{data.ageGroup}</span>
                            <span className="text-sm">
                              {data.percentage}% ({formatNumber(data.count)})
                            </span>
                          </div>
                          <Progress value={data.percentage} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Geographic Distribution</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="mb-6 h-[200px] w-full bg-muted/20 rounded-md flex items-center justify-center">
                      <MapPin className="h-16 w-16 text-muted-foreground/50" />
                    </div>
                    <div className="space-y-4">
                      {geographicData.map((data) => (
                        <div key={data.region} className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{data.region}</span>
                            <span className="text-sm">
                              {data.percentage}% ({formatNumber(data.count)})
                            </span>
                          </div>
                          <Progress value={data.percentage} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Device Usage</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      {deviceData.map((data) => (
                        <div
                          key={data.device}
                          className="flex flex-col items-center justify-center text-center p-4 rounded-lg border"
                        >
                          {data.device === "Mobile" ? (
                            <Smartphone className="h-8 w-8 mb-2 text-primary" />
                          ) : data.device === "Desktop" ? (
                            <Monitor className="h-8 w-8 mb-2 text-primary" />
                          ) : (
                            <Smartphone className="h-8 w-8 mb-2 text-primary" />
                          )}
                          <h3 className="font-medium">{data.device}</h3>
                          <div className="text-2xl font-bold">{data.percentage}%</div>
                          <p className="text-xs text-muted-foreground">{formatNumber(data.count)} users</p>
                        </div>
                      ))}
                    </div>
                    <div className="space-y-4">
                      {deviceData.map((data) => (
                        <div key={`bar-${data.device}`} className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{data.device}</span>
                            <span className="text-sm">{data.percentage}%</span>
                          </div>
                          <Progress value={data.percentage} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">User Segments</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center gap-4 rounded-lg border p-4">
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                          <Users className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-medium">Regular Shoppers</h3>
                          <p className="text-sm text-muted-foreground">Users who shop at least once a month</p>
                        </div>
                        <div className="ml-auto text-right">
                          <div className="font-bold">42%</div>
                          <div className="text-xs text-muted-foreground">68,250 users</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4 rounded-lg border p-4">
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                          <Users className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-medium">Occasional Buyers</h3>
                          <p className="text-sm text-muted-foreground">Users who shop every 2-3 months</p>
                        </div>
                        <div className="ml-auto text-right">
                          <div className="font-bold">35%</div>
                          <div className="text-xs text-muted-foreground">56,875 users</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4 rounded-lg border p-4">
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                          <Users className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-medium">Browsers</h3>
                          <p className="text-sm text-muted-foreground">Users who browse but rarely purchase</p>
                        </div>
                        <div className="ml-auto text-right">
                          <div className="font-bold">23%</div>
                          <div className="text-xs text-muted-foreground">37,375 users</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                <FileText className="mr-2 h-4 w-4" />
                Generate Comprehensive Demographic Report
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="engagement" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>User Engagement Metrics</CardTitle>
                  <CardDescription>Track how users interact with the platform</CardDescription>
                </div>
                <Select defaultValue="30days">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select time period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7days">Last 7 days</SelectItem>
                    <SelectItem value="30days">Last 30 days</SelectItem>
                    <SelectItem value="90days">Last 90 days</SelectItem>
                    <SelectItem value="1year">Last year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Key Engagement Metrics</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Metric</TableHead>
                          <TableHead>Value</TableHead>
                          <TableHead>Change</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {engagementData.map((data) => (
                          <TableRow key={data.metric}>
                            <TableCell className="font-medium">{data.metric}</TableCell>
                            <TableCell>
                              {typeof data.value === "number" ? formatNumber(data.value) : data.value}
                            </TableCell>
                            <TableCell>{getChangeIndicator(data.change)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Activity Heatmap</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="h-[250px] w-full bg-muted/20 rounded-md flex items-center justify-center">
                      <Clock className="h-16 w-16 text-muted-foreground/50" />
                    </div>
                    <div className="mt-4 text-center text-sm text-muted-foreground">
                      User activity distribution by day and hour
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Feature Usage</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Product Browsing</span>
                          <span className="text-sm">92%</span>
                        </div>
                        <Progress value={92} className="h-2" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Search Function</span>
                          <span className="text-sm">87%</span>
                        </div>
                        <Progress value={87} className="h-2" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Cart Usage</span>
                          <span className="text-sm">76%</span>
                        </div>
                        <Progress value={76} className="h-2" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Wishlist</span>
                          <span className="text-sm">58%</span>
                        </div>
                        <Progress value={58} className="h-2" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Reviews</span>
                          <span className="text-sm">42%</span>
                        </div>
                        <Progress value={42} className="h-2" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Referral Program</span>
                          <span className="text-sm">23%</span>
                        </div>
                        <Progress value={23} className="h-2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">User Journey</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="h-[250px] w-full bg-muted/20 rounded-md flex items-center justify-center">
                      <LineChart className="h-16 w-16 text-muted-foreground/50" />
                    </div>
                    <div className="mt-4 text-center text-sm text-muted-foreground">
                      Visualization of typical user paths through the platform
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <Calendar className="mr-2 h-4 w-4" />
                Compare Periods
              </Button>
              <Button>
                <Download className="mr-2 h-4 w-4" />
                Export Engagement Data
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="retention" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Retention Analysis</CardTitle>
                  <CardDescription>Track user retention over time by cohort</CardDescription>
                </div>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter Cohorts
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <Card className="md:col-span-2">
                  <CardHeader>
                    <CardTitle className="text-base">Cohort Retention Table</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Cohort</TableHead>
                          <TableHead>Month 1</TableHead>
                          <TableHead>Month 2</TableHead>
                          <TableHead>Month 3</TableHead>
                          <TableHead>Month 4</TableHead>
                          <TableHead>Month 5</TableHead>
                          <TableHead>Month 6</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {retentionData.map((data) => (
                          <TableRow key={data.cohort}>
                            <TableCell className="font-medium">{data.cohort}</TableCell>
                            <TableCell>
                              <div className="flex items-center justify-between">
                                <Progress value={data.month1} className="h-2 w-16" />
                                <span className="text-sm">{data.month1}%</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {data.month2 !== null ? (
                                <div className="flex items-center justify-between">
                                  <Progress value={data.month2} className="h-2 w-16" />
                                  <span className="text-sm">{data.month2}%</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {data.month3 !== null ? (
                                <div className="flex items-center justify-between">
                                  <Progress value={data.month3} className="h-2 w-16" />
                                  <span className="text-sm">{data.month3}%</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {data.month4 !== null ? (
                                <div className="flex items-center justify-between">
                                  <Progress value={data.month4} className="h-2 w-16" />
                                  <span className="text-sm">{data.month4}%</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {data.month5 !== null ? (
                                <div className="flex items-center justify-between">
                                  <Progress value={data.month5} className="h-2 w-16" />
                                  <span className="text-sm">{data.month5}%</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {data.month6 !== null ? (
                                <div className="flex items-center justify-between">
                                  <Progress value={data.month6} className="h-2 w-16" />
                                  <span className="text-sm">{data.month6}%</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">-</span>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Retention Curve</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="h-[300px] w-full bg-muted/20 rounded-md flex items-center justify-center">
                      <LineChart className="h-16 w-16 text-muted-foreground/50" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Retention Factors</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center gap-4 rounded-lg border p-4">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-500/10">
                          <UserPlus className="h-5 w-5 text-green-500" />
                        </div>
                        <div>
                          <h3 className="font-medium">Positive Factors</h3>
                          <ul className="mt-1 list-disc pl-5 text-sm text-muted-foreground">
                            <li>Frequent product updates</li>
                            <li>Loyalty program participation</li>
                            <li>Multiple store purchases</li>
                            <li>Customer support interactions</li>
                          </ul>
                        </div>
                      </div>

                      <div className="flex items-center gap-4 rounded-lg border p-4">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-500/10">
                          <UserMinus className="h-5 w-5 text-red-500" />
                        </div>
                        <div>
                          <h3 className="font-medium">Negative Factors</h3>
                          <ul className="mt-1 list-disc pl-5 text-sm text-muted-foreground">
                            <li>Infrequent platform visits</li>
                            <li>Abandoned carts</li>
                            <li>Lack of engagement with emails</li>
                            <li>No social connections on platform</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                <Download className="mr-2 h-4 w-4" />
                Export Retention Analysis
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="prediction" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Churn Prediction</CardTitle>
                  <CardDescription>AI-powered churn prediction and prevention</CardDescription>
                </div>
                <Button variant="outline">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Update Predictions
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-3">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">At-Risk Users</CardTitle>
                    <CardDescription>Users likely to churn in next 30 days</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">8,750</div>
                    <div className="flex items-center text-xs text-red-500">
                      <ChevronUp className="mr-1 h-4 w-4" />
                      <span>5.4% of active users</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Predicted Churn Rate</CardTitle>
                    <CardDescription>Next 30 days forecast</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">6.2%</div>
                    <div className="flex items-center text-xs text-red-500">
                      <ChevronUp className="mr-1 h-4 w-4" />
                      <span>0.4% from current rate</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Revenue Impact</CardTitle>
                    <CardDescription>Potential monthly loss</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">$87,500</div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <span>Based on average user value</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6 grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Churn Risk Factors</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Inactivity Period</span>
                          <span className="text-sm">High Impact</span>
                        </div>
                        <Progress value={85} className="h-2" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Declining Usage</span>
                          <span className="text-sm">High Impact</span>
                        </div>
                        <Progress value={78} className="h-2" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Support Tickets</span>
                          <span className="text-sm">Medium Impact</span>
                        </div>
                        <Progress value={62} className="h-2" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Payment Issues</span>
                          <span className="text-sm">Medium Impact</span>
                        </div>
                        <Progress value={58} className="h-2" />
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Feature Adoption</span>
                          <span className="text-sm">Medium Impact</span>
                        </div>
                        <Progress value={54} className="h-2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Churn Prevention Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="rounded-lg border p-4">
                        <h3 className="font-medium">Re-engagement Campaign</h3>
                        <p className="mt-1 text-sm text-muted-foreground">
                          Target 3,500 at-risk users with personalized emails highlighting unused features.
                        </p>
                        <div className="mt-2 flex items-center justify-between">
                          <span className="text-xs font-medium text-green-500">High Impact</span>
                          <span className="text-xs text-muted-foreground">Est. 25% effectiveness</span>
                        </div>
                      </div>

                      <div className="rounded-lg border p-4">
                        <h3 className="font-medium">Feature Onboarding</h3>
                        <p className="mt-1 text-sm text-muted-foreground">
                          Implement guided tours for 2,800 users who haven't adopted key features.
                        </p>
                        <div className="mt-2 flex items-center justify-between">
                          <span className="text-xs font-medium text-green-500">Medium Impact</span>
                          <span className="text-xs text-muted-foreground">Est. 18% effectiveness</span>
                        </div>
                      </div>

                      <div className="rounded-lg border p-4">
                        <h3 className="font-medium">Special Offers</h3>
                        <p className="mt-1 text-sm text-muted-foreground">
                          Provide targeted discounts to 1,950 price-sensitive at-risk users.
                        </p>
                        <div className="mt-2 flex items-center justify-between">
                          <span className="text-xs font-medium text-green-500">Medium Impact</span>
                          <span className="text-xs text-muted-foreground">Est. 15% effectiveness</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="md:col-span-2">
                  <CardHeader>
                    <CardTitle className="text-base">Churn Prediction Model Performance</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="rounded-lg border p-4 text-center">
                        <h3 className="text-sm font-medium text-muted-foreground">Accuracy</h3>
                        <div className="mt-1 text-2xl font-bold">87%</div>
                      </div>

                      <div className="rounded-lg border p-4 text-center">
                        <h3 className="text-sm font-medium text-muted-foreground">Precision</h3>
                        <div className="mt-1 text-2xl font-bold">82%</div>
                      </div>

                      <div className="rounded-lg border p-4 text-center">
                        <h3 className="text-sm font-medium text-muted-foreground">Recall</h3>
                        <div className="mt-1 text-2xl font-bold">79%</div>
                      </div>
                    </div>

                    <div className="mt-4 text-center text-sm text-muted-foreground">
                      Model last updated: May 10, 2023 | Next update scheduled: May 17, 2023
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <Share2 className="mr-2 h-4 w-4" />
                Share Insights
              </Button>
              <Button>
                <Download className="mr-2 h-4 w-4" />
                Export Prediction Report
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
