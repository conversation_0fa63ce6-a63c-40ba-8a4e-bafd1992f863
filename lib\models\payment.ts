export interface PaymentMethod {
  id: string
  name: string
  type: "bank_transfer" | "e_wallet" | "credit_card" | "virtual_account" | "retail_outlet"
  isActive: boolean
  logo: string
  instructions?: string[]
}

export interface PaymentGateway {
  id: string
  name: string
  isActive: boolean
  methods: PaymentMethod[]
}

export interface Payment {
  id: string
  orderId: string
  amount: number
  status: "pending" | "processing" | "completed" | "failed" | "expired" | "refunded"
  paymentMethod: {
    id: string
    name: string
    type: string
  }
  paymentGateway: {
    id: string
    name: string
  }
  transactionId?: string
  expiryTime?: string
  paymentInstructions?: string[]
  paymentProof?: string
  createdAt: string
  updatedAt: string
}

export interface PaymentRequest {
  orderId: string
  amount: number
  paymentMethodId: string
  paymentGatewayId: string
  customerName: string
  customerEmail: string
  customerPhone: string
  callbackUrl: string
  returnUrl: string
}

export interface PaymentResponse {
  payment: Payment
  redirectUrl?: string
  qrCode?: string
  virtualAccountNumber?: string
  paymentCode?: string
}
