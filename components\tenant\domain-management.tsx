"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { PlusCircle, Trash2, CheckCircle, XCircle, RefreshCw, Globe } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { tenantDomainsAPI } from "@/lib/api/tenant-domains"
import type { TenantDomain } from "@/lib/models/tenant-domain"

export function DomainManagement() {
  const router = useRouter()
  const [domains, setDomains] = useState<TenantDomain[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [newDomain, setNewDomain] = useState("")
  const [isAddingDomain, setIsAddingDomain] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [verificationInProgress, setVerificationInProgress] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)
  const [copyStatus, setCopyStatus] = useState<{[key: string]: boolean}>({})

  // Simulasi tenant ID (dalam implementasi sebenarnya, ini akan diambil dari context atau API)
  const tenantId = "1"

  // Fungsi untuk menyalin teks ke clipboard dengan pengecekan browser
  const copyToClipboard = async (text: string) => {
    if (!isClient) return false;
    
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        return true;
      } else {
        // Fallback untuk browser lama atau konteks tidak aman
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
          const successful = document.execCommand('copy');
          document.body.removeChild(textArea);
          return successful;
        } catch (err) {
          console.error('Failed to copy text: ', err);
          document.body.removeChild(textArea);
          return false;
        }
      }
    } catch (err) {
      console.error('Failed to copy: ', err);
      return false;
    }
  };
  
  // Set isClient to true after mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Handle copy button click
  const handleCopyClick = async (text: string, id: string) => {
    const success = await copyToClipboard(text);
    setCopyStatus(prev => ({ ...prev, [id]: success }));
    
    // Reset status setelah 2 detik
    setTimeout(() => {
      setCopyStatus(prev => ({ ...prev, [id]: false }));
    }, 2000);
  };

  const fetchDomains = async () => {
    setLoading(true)
    try {
      const data = await tenantDomainsAPI.getByTenantId(tenantId)
      setDomains(data)
      setError(null)
    } catch (err) {
      console.error("Error fetching domains:", err)
      setError("Failed to load domains. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDomains()
  }, [])

  const handleAddDomain = async () => {
    if (!newDomain) return

    setIsAddingDomain(true)
    try {
      const domain = await tenantDomainsAPI.create({
        tenantId,
        domain: newDomain,
      })

      if (domain) {
        setDomains([...domains, domain])
        setNewDomain("")
        setIsDialogOpen(false)
      }
    } catch (err) {
      console.error("Error adding domain:", err)
      setError("Failed to add domain. Please try again.")
    } finally {
      setIsAddingDomain(false)
    }
  }

  const handleDeleteDomain = async (id: string) => {
    try {
      const success = await tenantDomainsAPI.delete(id)
      if (success) {
        setDomains(domains.filter((domain) => domain.id !== id))
      }
    } catch (err) {
      console.error("Error deleting domain:", err)
      setError("Failed to delete domain. Please try again.")
    }
  }

  const handleSetPrimary = async (id: string) => {
    try {
      const updatedDomain = await tenantDomainsAPI.update(id, { isPrimary: true })
      if (updatedDomain) {
        setDomains(
          domains.map((domain) => ({
            ...domain,
            isPrimary: domain.id === id,
          })),
        )
      }
    } catch (err) {
      console.error("Error setting primary domain:", err)
      setError("Failed to set primary domain. Please try again.")
    }
  }

  const handleVerifyDomain = async (id: string) => {
    setVerificationInProgress(id)
    try {
      const verifiedDomain = await tenantDomainsAPI.verify(id)
      if (verifiedDomain) {
        setDomains(domains.map((domain) => (domain.id === id ? { ...domain, isVerified: true } : domain)))
      }
    } catch (err) {
      console.error("Error verifying domain:", err)
      setError("Failed to verify domain. Please try again.")
    } finally {
      setVerificationInProgress(null)
    }
  }

  // Simulasi data domain untuk demo
  useEffect(() => {
    if (domains.length === 0 && !loading) {
      setDomains([
        {
          id: "1",
          tenantId: "1",
          domain: "example-store.com",
          isVerified: true,
          isPrimary: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: "2",
          tenantId: "1",
          domain: "my-fashion-store.com",
          isVerified: false,
          isPrimary: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ])
    }
  }, [domains, loading])

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Custom Domains</CardTitle>
          <CardDescription>
            Connect your own domain to your marketplace. Your customers will be able to access your marketplace using
            this domain.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-6">
              <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : domains.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Globe className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No domains added yet</h3>
              <p className="text-sm text-muted-foreground mt-1 mb-4">
                Add a custom domain to make your marketplace more professional.
              </p>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Add Domain
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Custom Domain</DialogTitle>
                    <DialogDescription>Enter the domain you want to connect to your marketplace.</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Input
                        placeholder="yourdomain.com"
                        value={newDomain}
                        onChange={(e) => setNewDomain(e.target.value)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddDomain} disabled={isAddingDomain || !newDomain}>
                      {isAddingDomain ? "Adding..." : "Add Domain"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          ) : (
            <div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Domain</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Primary</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {domains.map((domain) => (
                    <TableRow key={domain.id}>
                      <TableCell className="font-medium">{domain.domain}</TableCell>
                      <TableCell>
                        {domain.isVerified ? (
                          <Badge variant="success" className="bg-green-100 text-green-800">
                            <CheckCircle className="mr-1 h-3 w-3" />
                            Verified
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                            <XCircle className="mr-1 h-3 w-3" />
                            Unverified
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {domain.isPrimary ? (
                          <Badge variant="default">Primary</Badge>
                        ) : (
                          <Button variant="ghost" size="sm" onClick={() => handleSetPrimary(domain.id)}>
                            Set as Primary
                          </Button>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          {!domain.isVerified && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleVerifyDomain(domain.id)}
                              disabled={verificationInProgress === domain.id}
                            >
                              {verificationInProgress === domain.id ? (
                                <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                              ) : (
                                <CheckCircle className="mr-1 h-3 w-3" />
                              )}
                              Verify
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteDomain(domain.id)}
                            disabled={domain.isPrimary}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className="mt-4 flex justify-end">
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                  <DialogTrigger asChild>
                    <Button>
                      <PlusCircle className="mr-2 h-4 w-4" />
                      Add Domain
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add Custom Domain</DialogTitle>
                      <DialogDescription>Enter the domain you want to connect to your marketplace.</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid gap-2">
                        <Input
                          placeholder="yourdomain.com"
                          value={newDomain}
                          onChange={(e) => setNewDomain(e.target.value)}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleAddDomain} disabled={isAddingDomain || !newDomain}>
                        {isAddingDomain ? "Adding..." : "Add Domain"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="border-t px-6 py-4">
          <div className="text-sm text-muted-foreground">
            <p>
              To verify your domain, you need to add a DNS record to your domain provider.
              <Button variant="link" className="h-auto p-0 ml-1">
                Learn more
              </Button>
            </p>
          </div>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Subdomain</CardTitle>
          <CardDescription>Your marketplace is also accessible via a subdomain on our platform.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <Input value="your-store.sellzio.com" readOnly className="max-w-xs" />
            <Button 
              variant="outline" 
              onClick={() => handleCopyClick("your-store.sellzio.com", 'default-domain')}
              disabled={!isClient}
            >
              {copyStatus['default-domain'] ? 'Copied!' : 'Copy'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Domain Settings</CardTitle>
          <CardDescription>Configure how your domains work with your marketplace.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-4">
              <div className="flex-1 space-y-1">
                <p className="font-medium">HTTPS Enforcement</p>
                <p className="text-sm text-muted-foreground">
                  Always redirect HTTP traffic to HTTPS for better security.
                </p>
              </div>
              <Button variant="outline">Enabled</Button>
            </div>
            <div className="flex items-start space-x-4">
              <div className="flex-1 space-y-1">
                <p className="font-medium">WWW Redirection</p>
                <p className="text-sm text-muted-foreground">
                  Automatically redirect www to non-www version of your domain or vice versa.
                </p>
              </div>
              <Button variant="outline">Configure</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
