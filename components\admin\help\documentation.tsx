"use client"

import { useState } from "react"
import {
  Search,
  Book,
  FileText,
  ChevronRight,
  Star,
  Clock,
  ThumbsUp,
  ThumbsDown,
  Copy,
  ExternalLink,
  BookOpen,
  Code,
  Settings,
  ShoppingCart,
  CreditCard,
  Users,
  BarChart,
  HelpCircle,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

// Dummy data for documentation categories
const categories = [
  { id: "getting-started", name: "Getting Started", icon: Book, count: 12 },
  { id: "platform-basics", name: "Platform Basics", icon: FileText, count: 18 },
  { id: "tenant-management", name: "Tenant Management", icon: Users, count: 15 },
  { id: "store-management", name: "Store Management", icon: ShoppingCart, count: 22 },
  { id: "product-management", name: "Product Management", icon: ShoppingCart, count: 20 },
  { id: "user-management", name: "User Management", icon: Users, count: 14 },
  { id: "financial-management", name: "Financial Management", icon: CreditCard, count: 16 },
  { id: "content-management", name: "Content Management", icon: FileText, count: 13 },
  { id: "marketing-tools", name: "Marketing Tools", icon: BarChart, count: 10 },
  { id: "system-administration", name: "System Administration", icon: Settings, count: 19 },
  { id: "api-development", name: "API & Development", icon: Code, count: 25 },
  { id: "troubleshooting", name: "Troubleshooting", icon: HelpCircle, count: 17 },
]

// Dummy data for documentation articles
const articles = [
  {
    id: 1,
    title: "Getting Started with SellZio",
    category: "getting-started",
    description: "Learn the basics of the SellZio platform and how to set up your account.",
    lastUpdated: "2023-04-15",
    views: 1245,
    rating: 4.8,
    isFavorite: true,
    content: `
# Getting Started with SellZio

Welcome to SellZio, the all-in-one multi-tenant e-commerce platform. This guide will help you get started with the platform and set up your account.

## What is SellZio?

SellZio is a comprehensive e-commerce platform that allows you to create and manage multiple online stores under a single tenant. It provides tools for store management, product management, order processing, and more.

## Creating Your Account

To get started with SellZio, you need to create an account:

1. Visit the SellZio website and click on "Sign Up"
2. Fill in your details and select your account type (Admin, Tenant, Store Owner, or Buyer)
3. Verify your email address
4. Complete your profile setup

## Dashboard Overview

After logging in, you'll be taken to your dashboard. The dashboard provides an overview of your account and quick access to various features:

- **Navigation Sidebar**: Access different sections of the platform
- **Quick Actions**: Perform common tasks quickly
- **Activity Feed**: See recent activities
- **System Status**: Check the status of various platform components

## Next Steps

Now that you have set up your account, you can:

- Create a new tenant (if you're an admin)
- Set up your first store (if you're a tenant or store owner)
- Browse products (if you're a buyer)

For more detailed instructions, check out our other documentation articles.
    `,
  },
  {
    id: 2,
    title: "Setting Up Your First Store",
    category: "store-management",
    description: "A step-by-step guide to creating and configuring your first store on the platform.",
    lastUpdated: "2023-04-20",
    views: 982,
    rating: 4.6,
    isFavorite: false,
    content: "Detailed guide content here...",
  },
  {
    id: 3,
    title: "Advanced API Usage",
    category: "api-development",
    description: "Learn how to use the SellZio API for custom integrations and automation.",
    lastUpdated: "2023-05-05",
    views: 756,
    rating: 4.7,
    isFavorite: true,
    content: "Detailed guide content here...",
  },
  {
    id: 4,
    title: "Managing Product Inventory",
    category: "product-management",
    description: "Learn how to add, edit, and organize products in your store's inventory.",
    lastUpdated: "2023-04-25",
    views: 845,
    rating: 4.5,
    isFavorite: false,
    content: "Detailed guide content here...",
  },
  {
    id: 5,
    title: "Processing Orders and Payments",
    category: "financial-management",
    description: "A comprehensive guide to handling customer orders and payment processing.",
    lastUpdated: "2023-04-30",
    views: 723,
    rating: 4.4,
    isFavorite: false,
    content: "Detailed guide content here...",
  },
  {
    id: 6,
    title: "User Roles and Permissions",
    category: "user-management",
    description: "Understanding the different user roles and how to manage permissions.",
    lastUpdated: "2023-05-02",
    views: 612,
    rating: 4.3,
    isFavorite: false,
    content: "Detailed guide content here...",
  },
  {
    id: 7,
    title: "Customizing Store Themes",
    category: "content-management",
    description: "Learn how to customize the appearance of your store with themes and templates.",
    lastUpdated: "2023-05-07",
    views: 589,
    rating: 4.6,
    isFavorite: true,
    content: "Detailed guide content here...",
  },
  {
    id: 8,
    title: "Setting Up Marketing Campaigns",
    category: "marketing-tools",
    description: "Create and manage marketing campaigns to promote your products and stores.",
    lastUpdated: "2023-05-10",
    views: 478,
    rating: 4.2,
    isFavorite: false,
    content: "Detailed guide content here...",
  },
]

export default function Documentation() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedArticle, setSelectedArticle] = useState(articles[0])
  const [activeTab, setActiveTab] = useState("browse")

  // Filter articles based on search query and selected category
  const filteredArticles = articles.filter((article) => {
    const matchesSearch =
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.description.toLowerCase().includes(searchQuery.toLowerCase())

    if (selectedCategory === "all") return matchesSearch
    if (selectedCategory === "favorites") return matchesSearch && article.isFavorite
    return matchesSearch && article.category === selectedCategory
  })

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col space-y-2 mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Documentation</h1>
        <p className="text-muted-foreground">Browse platform documentation and guides for the SellZio platform.</p>
      </div>

      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search documentation..."
          className="pl-10 h-12"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <Tabs defaultValue="browse" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="w-full max-w-md mx-auto grid grid-cols-2">
          <TabsTrigger value="browse">Browse Documentation</TabsTrigger>
          <TabsTrigger value="favorites">My Favorites</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
        <div className="md:col-span-1">
          <div className="space-y-1">
            <Button
              variant={selectedCategory === "all" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setSelectedCategory("all")}
            >
              <Book className="mr-2 h-4 w-4" />
              All Documentation
              <Badge className="ml-auto" variant="outline">
                {articles.length}
              </Badge>
            </Button>
            <Button
              variant={selectedCategory === "favorites" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setSelectedCategory("favorites")}
            >
              <Star className="mr-2 h-4 w-4" />
              Favorites
              <Badge className="ml-auto" variant="outline">
                {articles.filter((a) => a.isFavorite).length}
              </Badge>
            </Button>
          </div>

          <Separator className="my-4" />

          <div className="space-y-1">
            <h3 className="font-medium px-3 py-2">Categories</h3>
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "ghost"}
                className="w-full justify-start"
                onClick={() => setSelectedCategory(category.id)}
              >
                <category.icon className="mr-2 h-4 w-4" />
                {category.name}
                <Badge className="ml-auto" variant="outline">
                  {category.count}
                </Badge>
              </Button>
            ))}
          </div>
        </div>

        {activeTab === "browse" ? (
          <>
            <div className="md:col-span-1">
              <div className="space-y-2 max-h-[calc(100vh-300px)] overflow-y-auto">
                {filteredArticles.length > 0 ? (
                  filteredArticles.map((article) => (
                    <Card
                      key={article.id}
                      className={`cursor-pointer hover:bg-accent/50 transition-colors ${selectedArticle.id === article.id ? "border-primary" : ""}`}
                      onClick={() => setSelectedArticle(article)}
                    >
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div className="space-y-1">
                            <div className="font-medium">{article.title}</div>
                            <div className="text-xs text-muted-foreground">
                              {categories.find((c) => c.id === article.category)?.name}
                            </div>
                          </div>
                          {article.isFavorite && <Star className="h-4 w-4 text-yellow-500" />}
                        </div>
                        <div className="mt-2 text-sm text-muted-foreground line-clamp-2">{article.description}</div>
                        <div className="mt-2 flex items-center justify-between text-xs text-muted-foreground">
                          <div className="flex items-center">
                            <Clock className="mr-1 h-3 w-3" />
                            {formatDate(article.lastUpdated)}
                          </div>
                          <div className="flex items-center">
                            <BookOpen className="mr-1 h-3 w-3" />
                            {article.views} views
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">No articles found matching your criteria</div>
                )}
              </div>
            </div>

            <div className="md:col-span-2">
              {selectedArticle ? (
                <Card className="h-full">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{selectedArticle.title}</CardTitle>
                        <CardDescription>
                          {categories.find((c) => c.id === selectedArticle.category)?.name} • Last updated:{" "}
                          {formatDate(selectedArticle.lastUpdated)}
                        </CardDescription>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="icon">
                          <Star
                            className={`h-4 w-4 ${selectedArticle.isFavorite ? "text-yellow-500 fill-yellow-500" : ""}`}
                          />
                        </Button>
                        <Button variant="outline" size="icon">
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon">
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 mt-2 text-sm">
                      <div className="flex items-center">
                        <BookOpen className="mr-1 h-4 w-4 text-muted-foreground" />
                        <span>{selectedArticle.views} views</span>
                      </div>
                      <div className="flex items-center">
                        <Star className="mr-1 h-4 w-4 text-yellow-500" />
                        <span>{selectedArticle.rating}/5</span>
                      </div>
                    </div>
                    <Separator className="my-4" />
                  </CardHeader>
                  <CardContent className="max-h-[calc(100vh-400px)] overflow-y-auto prose dark:prose-invert">
                    <div dangerouslySetInnerHTML={{ __html: selectedArticle.content.replace(/\n/g, "<br />") }} />
                  </CardContent>
                  <CardFooter className="flex justify-between border-t p-4">
                    <div className="text-sm text-muted-foreground">Was this article helpful?</div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <ThumbsUp className="mr-2 h-4 w-4" /> Yes
                      </Button>
                      <Button variant="outline" size="sm">
                        <ThumbsDown className="mr-2 h-4 w-4" /> No
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ) : (
                <div className="h-full flex items-center justify-center text-muted-foreground">
                  Select an article to view its content
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="md:col-span-3">
            <h2 className="text-xl font-semibold mb-4">My Favorite Articles</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {articles
                .filter((a) => a.isFavorite)
                .map((article) => (
                  <Card key={article.id} className="cursor-pointer hover:bg-accent/50 transition-colors">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">{article.title}</CardTitle>
                      <CardDescription>{categories.find((c) => c.id === article.category)?.name}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground line-clamp-3">{article.description}</p>
                    </CardContent>
                    <CardFooter className="flex justify-between pt-0">
                      <div className="text-xs text-muted-foreground">Updated: {formatDate(article.lastUpdated)}</div>
                      <Button variant="ghost" size="sm" className="text-primary">
                        Read <ChevronRight className="ml-1 h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
