import type { Metada<PERSON> } from "next"
import { ThemeManager } from "@/components/admin/content/theme-manager"

export const metadata: Metadata = {
  title: "Theme Manager | Admin Dashboard",
  description: "Manage themes for tenants",
}

export default function ThemeManagerPage() {
  return (
    <div className="flex flex-col gap-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Theme Manager</h1>
        <p className="text-muted-foreground">Manage and customize themes for tenants across the platform.</p>
      </div>
      <ThemeManager />
    </div>
  )
}
