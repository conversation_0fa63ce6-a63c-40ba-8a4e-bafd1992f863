"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Plus,
  Zap,
  Clock,
  Calendar,
  Tag,
  ShoppingBag,
  PieChart,
  Eye,
  PencilIcon,
  Trash,
  MoreHorizontal,
  PauseCircle,
  PlayCircle,
  Box
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk flash sales
const flashSales = [
  {
    id: "flash-001",
    name: "Weekend Flash Sale",
    status: "active",
    startDate: "2024-01-20T08:00:00",
    endDate: "2024-01-21T23:59:59",
    discountPercentage: 30,
    itemsCount: 45,
    totalStock: 1500,
    soldCount: 350,
    revenue: 12500000,
    viewCount: 8500,
    conversionRate: 4.1
  },
  {
    id: "flash-002",
    name: "Midnight Sale",
    status: "scheduled",
    startDate: "2024-01-25T00:00:00",
    endDate: "2024-01-25T06:00:00",
    discountPercentage: 40,
    itemsCount: 30,
    totalStock: 900,
    soldCount: 0,
    revenue: 0,
    viewCount: 0,
    conversionRate: 0
  },
  {
    id: "flash-003",
    name: "Clearance Sale",
    status: "ended",
    startDate: "2024-01-10T09:00:00",
    endDate: "2024-01-12T21:00:00",
    discountPercentage: 50,
    itemsCount: 60,
    totalStock: 1800,
    soldCount: 1500,
    revenue: 30000000,
    viewCount: 15000,
    conversionRate: 10
  },
  {
    id: "flash-004",
    name: "Special Holiday Sale",
    status: "paused",
    startDate: "2024-01-15T10:00:00",
    endDate: "2024-01-18T22:00:00",
    discountPercentage: 25,
    itemsCount: 35,
    totalStock: 1200,
    soldCount: 450,
    revenue: 15750000,
    viewCount: 7800,
    conversionRate: 5.8
  },
  {
    id: "flash-005",
    name: "New Year Flash Sale",
    status: "ended",
    startDate: "2024-01-01T00:00:00",
    endDate: "2024-01-02T23:59:59",
    discountPercentage: 35,
    itemsCount: 50,
    totalStock: 1600,
    soldCount: 1200,
    revenue: 25500000,
    viewCount: 12000,
    conversionRate: 10
  }
]

// Fungsi untuk badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "active":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>
    case "scheduled":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">Scheduled</Badge>
    case "paused":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Paused</Badge>
    case "ended":
      return <Badge variant="outline" className="bg-red-100 text-red-800">Ended</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Fungsi untuk format persentase
function formatPercentage(value: number) {
  return `${value}%`
}

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Fungsi untuk menghitung waktu tersisa
function getRemainingTime(endDate: string) {
  const end = new Date(endDate).getTime()
  const now = new Date().getTime()
  const distance = end - now
  
  if (distance < 0) {
    return "Berakhir"
  }
  
  const days = Math.floor(distance / (1000 * 60 * 60 * 24))
  const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) {
    return `${days} hari ${hours} jam lagi`
  } else if (hours > 0) {
    return `${hours} jam ${minutes} menit lagi`
  } else {
    return `${minutes} menit lagi`
  }
}

export default function FlashSalesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Filter flash sales
  const filteredSales = flashSales.filter(sale => {
    const matchesSearch = sale.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || sale.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Statistik
  const stats = {
    total: flashSales.length,
    active: flashSales.filter(s => s.status === "active").length,
    scheduled: flashSales.filter(s => s.status === "scheduled").length,
    totalRevenue: flashSales.reduce((sum, s) => sum + s.revenue, 0),
    totalSold: flashSales.reduce((sum, s) => sum + s.soldCount, 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/marketing">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Flash Sales</h1>
            <p className="text-muted-foreground">
              Kelola promo flash sale dengan diskon dan batas waktu
            </p>
          </div>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Buat Flash Sale
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Flash Sales</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Active: {stats.active}, Scheduled: {stats.scheduled}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <Tag className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatCurrency(stats.totalRevenue)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Items Sold</CardTitle>
            <ShoppingBag className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.totalSold}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Conversion Rate</CardTitle>
            <PieChart className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {formatPercentage(
                Number((flashSales.reduce((sum, s) => sum + (s.conversionRate * (s.status === "ended" || s.status === "active" ? 1 : 0)), 0) / 
                (flashSales.filter(s => s.status === "ended" || s.status === "active").length || 1)).toFixed(1))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari nama flash sale..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="active">Active</option>
              <option value="scheduled">Scheduled</option>
              <option value="paused">Paused</option>
              <option value="ended">Ended</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Flash Sales List */}
      <div className="space-y-4">
        {filteredSales.map((sale) => (
          <Card key={sale.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex flex-col gap-4">
                <div className="flex flex-col md:flex-row justify-between gap-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-semibold text-lg">{sale.name}</span>
                      {getStatusBadge(sale.status)}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Calendar className="h-3.5 w-3.5" />
                      <span>{formatDate(sale.startDate)} - {formatDate(sale.endDate)}</span>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <div className="flex items-center gap-1">
                      <Tag className="h-4 w-4 text-red-600" />
                      <span className="text-lg font-bold text-red-600">{formatPercentage(sale.discountPercentage)} OFF</span>
                    </div>
                    {sale.status === "active" && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm font-medium text-yellow-600">{getRemainingTime(sale.endDate)}</span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Items</p>
                    <div className="flex items-center gap-1">
                      <Box className="h-4 w-4 text-blue-600" />
                      <p className="font-semibold">{sale.itemsCount} products</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Sold</p>
                    <p className="font-semibold">{sale.soldCount} / {sale.totalStock} units ({((sale.soldCount / sale.totalStock) * 100).toFixed(1)}%)</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Revenue</p>
                    <p className="font-semibold text-green-600">{formatCurrency(sale.revenue)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Conversion Rate</p>
                    <p className="font-semibold">{formatPercentage(sale.conversionRate)} ({sale.viewCount} views)</p>
                  </div>
                </div>
                <div className="flex gap-2 pt-2 border-t mt-2">
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button size="sm" variant="outline">
                    <PencilIcon className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  {sale.status === "active" ? (
                    <Button size="sm" variant="outline" className="text-yellow-600 hover:text-yellow-700">
                      <PauseCircle className="h-4 w-4 mr-2" />
                      Pause
                    </Button>
                  ) : sale.status === "paused" ? (
                    <Button size="sm" variant="outline" className="text-green-600 hover:text-green-700">
                      <PlayCircle className="h-4 w-4 mr-2" />
                      Resume
                    </Button>
                  ) : null}
                  <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                    <Trash className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                  <Button size="sm" variant="ghost">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        {filteredSales.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Tidak ada flash sale ditemukan</h3>
              <p className="text-muted-foreground mb-4">
                Tidak ada flash sale yang cocok dengan filter Anda
              </p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Buat Flash Sale
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
} 