"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Eye, Edit, AlertTriangle, Search, Filter, Download, Plus } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { ErrorBoundary } from "@/components/error-boundary"

// Mock data for stores
const mockStores = [
  {
    id: "store-1",
    name: "Fashion Boutique",
    tenant: "Fashion Corp",
    owner: "<PERSON> Smith",
    productCount: 120,
    orderCount: 450,
    revenue: "$12,500",
    status: "active",
  },
  {
    id: "store-2",
    name: "Tech Gadgets",
    tenant: "Tech Solutions",
    owner: "Jane Doe",
    productCount: 85,
    orderCount: 320,
    revenue: "$9,800",
    status: "active",
  },
  {
    id: "store-3",
    name: "Home Decor",
    tenant: "Interior Design Co",
    owner: "Michael Brown",
    productCount: 65,
    orderCount: 210,
    revenue: "$7,200",
    status: "suspended",
  },
  {
    id: "store-4",
    name: "Organic Foods",
    tenant: "Green Earth",
    owner: "Sarah Johnson",
    productCount: 95,
    orderCount: 380,
    revenue: "$11,300",
    status: "active",
  },
  {
    id: "store-5",
    name: "Sports Equipment",
    tenant: "Active Life",
    owner: "David Wilson",
    productCount: 110,
    orderCount: 290,
    revenue: "$8,700",
    status: "pending",
  },
  {
    id: "store-6",
    name: "Beauty Products",
    tenant: "Glow Beauty",
    owner: "Emily Davis",
    productCount: 75,
    orderCount: 260,
    revenue: "$6,900",
    status: "active",
  },
  {
    id: "store-7",
    name: "Pet Supplies",
    tenant: "Pet Lovers",
    owner: "Robert Miller",
    productCount: 60,
    orderCount: 180,
    revenue: "$5,400",
    status: "suspended",
  },
]

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const statusMap: Record<
    string,
    { label: string; variant: "default" | "outline" | "secondary" | "destructive" | "success" }
  > = {
    active: { label: "Active", variant: "success" },
    pending: { label: "Pending", variant: "secondary" },
    suspended: { label: "Suspended", variant: "destructive" },
  }

  const { label, variant } = statusMap[status] || { label: status, variant: "default" }

  return <Badge variant={variant}>{label}</Badge>
}

export function StoreList() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [tenantFilter, setTenantFilter] = useState("all")
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [storeToDelete, setStoreToDelete] = useState<string | null>(null)
  const [selectedStores, setSelectedStores] = useState<string[]>([])
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Ensure component is mounted before rendering
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Filter stores based on search term and filters
  const filteredStores = mockStores.filter((store) => {
    const matchesSearch =
      store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.owner.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || store.status === statusFilter
    const matchesTenant = tenantFilter === "all" || store.tenant === tenantFilter

    return matchesSearch && matchesStatus && matchesTenant
  })

  // Get unique tenants for filter
  const tenants = Array.from(new Set(mockStores.map((store) => store.tenant)))

  // Handle store selection
  const toggleStoreSelection = (storeId: string) => {
    setSelectedStores((prev) => {
      if (prev.includes(storeId)) {
        return prev.filter((id) => id !== storeId)
      } else {
        return [...prev, storeId]
      }
    })
  }

  // Handle select all stores
  const toggleSelectAll = () => {
    if (selectedStores.length === filteredStores.length) {
      setSelectedStores([])
    } else {
      setSelectedStores(filteredStores.map((store) => store.id))
    }
  }

  // Handle delete store
  const handleDeleteStore = (storeId: string) => {
    setStoreToDelete(storeId)
    setShowDeleteDialog(true)
  }

  // Confirm delete store
  const confirmDeleteStore = () => {
    // Here you would call your API to delete the store
    console.log(`Deleting store: ${storeToDelete}`)
    setShowDeleteDialog(false)
    setStoreToDelete(null)
  }

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedStores.length > 0) {
      setShowBulkDeleteDialog(true)
    }
  }

  // Confirm bulk delete
  const confirmBulkDelete = () => {
    // Here you would call your API to delete the selected stores
    console.log(`Deleting stores: ${selectedStores.join(", ")}`)
    setShowBulkDeleteDialog(false)
    setSelectedStores([])
  }

  // Handle view store
  const handleViewStore = (storeId: string) => {
    router.push(`/admin/dashboard/stores/${storeId}`)
  }

  // Handle edit store
  const handleEditStore = (storeId: string) => {
    router.push(`/admin/dashboard/stores/${storeId}/edit`)
  }

  // Handle add store
  const handleAddStore = () => {
    router.push("/admin/dashboard/stores/create")
  }

  if (!isClient) {
    return <div>Loading store list...</div>
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search stores..."
                className="w-full pl-8 md:w-[300px]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-9 gap-1">
                  <Filter className="h-4 w-4" />
                  <span className="hidden sm:inline">Filters</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[200px]">
                <div className="p-2">
                  <div className="space-y-2">
                    <div>
                      <label className="text-xs font-medium">Status</label>
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-xs font-medium">Tenant</label>
                      <Select value={tenantFilter} onValueChange={setTenantFilter}>
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Select tenant" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Tenants</SelectItem>
                          {tenants.map((tenant) => (
                            <SelectItem key={tenant} value={tenant}>
                              {tenant}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="h-9">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button size="sm" className="h-9" onClick={handleAddStore}>
              <Plus className="mr-2 h-4 w-4" />
              Add Store
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader className="p-4 flex flex-row items-center justify-between">
            <CardTitle className="text-lg">Stores</CardTitle>
            {selectedStores.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">{selectedStores.length} selected</span>
                <Button variant="destructive" size="sm" onClick={handleBulkDelete}>
                  Delete Selected
                </Button>
              </div>
            )}
          </CardHeader>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[40px]">
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300"
                      checked={selectedStores.length === filteredStores.length && filteredStores.length > 0}
                      onChange={toggleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Tenant</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead className="text-right">Products</TableHead>
                  <TableHead className="text-right">Orders</TableHead>
                  <TableHead className="text-right">Revenue</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStores.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                      No stores found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredStores.map((store) => (
                    <TableRow key={store.id}>
                      <TableCell>
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={selectedStores.includes(store.id)}
                          onChange={() => toggleStoreSelection(store.id)}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{store.name}</TableCell>
                      <TableCell>{store.tenant}</TableCell>
                      <TableCell>{store.owner}</TableCell>
                      <TableCell className="text-right">{store.productCount}</TableCell>
                      <TableCell className="text-right">{store.orderCount}</TableCell>
                      <TableCell className="text-right">{store.revenue}</TableCell>
                      <TableCell>
                        <StatusBadge status={store.status} />
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon" title="View" onClick={() => handleViewStore(store.id)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" title="Edit" onClick={() => handleEditStore(store.id)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            title="Suspend/Delete"
                            onClick={() => handleDeleteStore(store.id)}
                          >
                            <AlertTriangle className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Delete Store Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the store and all its data.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={confirmDeleteStore} className="bg-destructive text-destructive-foreground">
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Bulk Delete Dialog */}
        <AlertDialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete multiple stores?</AlertDialogTitle>
              <AlertDialogDescription>
                You are about to delete {selectedStores.length} stores. This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={confirmBulkDelete} className="bg-destructive text-destructive-foreground">
                Delete All
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </ErrorBoundary>
  )
}
