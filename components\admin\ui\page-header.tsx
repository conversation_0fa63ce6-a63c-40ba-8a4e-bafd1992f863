"use client"

import type React from "react"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Breadcrumbs } from "./breadcrumbs"

interface ActionItem {
  label: string
  href: string
  variant?: "default" | "outline" | "secondary" | "ghost" | "link"
}

interface PageHeaderProps {
  title: string
  description?: string
  breadcrumbs?: { title: string; href: string }[]
  actions?: ActionItem[] | React.ReactNode
  showBackButton?: boolean
  onBack?: () => void
}

export function PageHeader({
  title,
  description,
  breadcrumbs,
  actions,
  showBackButton = false,
  onBack,
}: PageHeaderProps) {
  // Fungsi untuk merender actions
  const renderActions = () => {
    if (!actions) return null

    // Jika actions adalah array objek (ActionItem[])
    if (Array.isArray(actions)) {
      return actions.map((action, index) => (
        <Button key={index} variant={action.variant || "default"} asChild>
          <Link href={action.href}>{action.label}</Link>
        </Button>
      ))
    }

    // Jika actions adalah ReactNode
    return actions
  }

  return (
    <div className="mb-6 space-y-2">
      <div className="flex items-center gap-2">
        {showBackButton && (
          <Button variant="ghost" size="icon" className="mr-2 h-8 w-8" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Back</span>
          </Button>
        )}
        <Breadcrumbs items={breadcrumbs} className="flex-1" />
      </div>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </div>
        {actions && <div className="flex items-center gap-2">{renderActions()}</div>}
      </div>
    </div>
  )
}
