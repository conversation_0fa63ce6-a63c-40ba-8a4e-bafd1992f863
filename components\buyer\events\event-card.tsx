"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { formatDistanceToNow } from "date-fns"
import { id } from "date-fns/locale"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { CalendarPlus, Clock, MapPin, Users, Bell, Share2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { AddToCalendar } from "./add-to-calendar"
import { ReminderSetup } from "./reminder-setup"
import type { Event } from "./types"

interface EventCardProps {
  event: Event
  className?: string
}

export function EventCard({ event, className }: EventCardProps) {
  const router = useRouter()
  const [showAddToCalendar, setShowAddToCalendar] = useState(false)
  const [showReminderSetup, setShowReminderSetup] = useState(false)

  const handleCardClick = () => {
    router.push(`/buyer/dashboard/events/${event.id}`)
  }

  const handleRegister = (e: React.MouseEvent) => {
    e.stopPropagation()
    router.push(`/buyer/dashboard/events/${event.id}?register=true`)
  }

  const handleAddToCalendar = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowAddToCalendar(!showAddToCalendar)
    setShowReminderSetup(false)
  }

  const handleSetReminder = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowReminderSetup(!showReminderSetup)
    setShowAddToCalendar(false)
  }

  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation()
    // Implementasi share
    if (navigator.share) {
      navigator
        .share({
          title: event.title,
          text: event.description,
          url: window.location.origin + `/buyer/dashboard/events/${event.id}`,
        })
        .catch((err) => console.error("Error sharing:", err))
    }
  }

  const eventDate = new Date(event.date)
  const formattedDate = eventDate.toLocaleDateString("id-ID", {
    weekday: "long",
    day: "numeric",
    month: "long",
    year: "numeric",
  })

  const formattedTime = eventDate.toLocaleTimeString("id-ID", {
    hour: "2-digit",
    minute: "2-digit",
  })

  const timeUntil = formatDistanceToNow(eventDate, {
    addSuffix: true,
    locale: id,
  })

  return (
    <Card
      className={cn("cursor-pointer overflow-hidden transition-all hover:shadow-md", className)}
      onClick={handleCardClick}
    >
      <div className="relative">
        <div className="aspect-[16/9] w-full overflow-hidden">
          <Image
            src={event.image || "/placeholder.svg"}
            alt={event.title}
            width={600}
            height={338}
            className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
          />
        </div>
        <Badge
          className="absolute right-2 top-2"
          variant={
            event.category === "Live Shopping Events"
              ? "destructive"
              : event.category === "Flash Sales"
                ? "default"
                : "secondary"
          }
        >
          {event.category}
        </Badge>
      </div>

      <CardContent className="p-4">
        <div className="mb-2 flex items-center justify-between">
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <Clock className="h-3.5 w-3.5" />
            <span>{timeUntil}</span>
          </div>
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <Users className="h-3.5 w-3.5" />
            <span>{event.participants} peserta</span>
          </div>
        </div>

        <h3 className="mb-1 line-clamp-2 text-xl font-bold">{event.title}</h3>

        <div className="mb-2 flex items-center gap-1 text-sm text-muted-foreground">
          <MapPin className="h-3.5 w-3.5" />
          <span>{event.location}</span>
        </div>

        <div className="mb-4 flex items-center gap-1 text-sm font-medium">
          <Clock className="h-3.5 w-3.5 text-primary" />
          <span>
            {formattedDate} • {formattedTime}
          </span>
        </div>

        <p className="line-clamp-2 text-sm text-muted-foreground">{event.description}</p>

        <div className="mt-3 flex items-center gap-2">
          <div className="flex -space-x-2">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="flex h-6 w-6 items-center justify-center rounded-full border-2 border-background bg-muted text-[10px] font-medium"
              >
                U{i}
              </div>
            ))}
          </div>
          <span className="text-xs text-muted-foreground">+{event.participants - 3} lainnya akan hadir</span>
        </div>
      </CardContent>

      <CardFooter className="flex flex-wrap gap-2 border-t p-4">
        <Button size="sm" className="flex-1" onClick={handleRegister}>
          Daftar
        </Button>
        <div className="flex gap-1">
          <Button size="icon" variant="outline" onClick={handleAddToCalendar} className="relative h-9 w-9">
            <CalendarPlus className="h-4 w-4" />
            {showAddToCalendar && <AddToCalendar event={event} onClose={() => setShowAddToCalendar(false)} />}
          </Button>
          <Button size="icon" variant="outline" onClick={handleSetReminder} className="relative h-9 w-9">
            <Bell className="h-4 w-4" />
            {showReminderSetup && <ReminderSetup event={event} onClose={() => setShowReminderSetup(false)} />}
          </Button>
          <Button size="icon" variant="outline" onClick={handleShare} className="h-9 w-9">
            <Share2 className="h-4 w-4" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
