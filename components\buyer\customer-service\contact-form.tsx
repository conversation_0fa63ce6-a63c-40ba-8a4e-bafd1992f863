"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { CheckCircle2, Mail, MessageSquare, PaperclipIcon, Phone, Send } from "lucide-react"

export function ContactForm() {
  const [contactForm, setContactForm] = useState({
    name: "",
    email: "",
    topic: "",
    orderNumber: "",
    message: "",
    priority: "medium",
    responsePreference: "email",
    attachFile: false,
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      console.log("Form submitted:", contactForm)
      setIsSubmitting(false)
      setIsSubmitted(true)

      // Reset form after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false)
        setContactForm({
          name: "",
          email: "",
          topic: "",
          orderNumber: "",
          message: "",
          priority: "medium",
          responsePreference: "email",
          attachFile: false,
        })
      }, 3000)
    }, 1500)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Hubungi Kami</h2>
        <p className="text-muted-foreground">Pilih metode kontak yang paling nyaman untuk Anda</p>
      </div>

      <Tabs defaultValue="form" className="space-y-4">
        <TabsList>
          <TabsTrigger value="form" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            <span>Form Kontak</span>
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span>Live Chat</span>
          </TabsTrigger>
          <TabsTrigger value="phone" className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            <span>Telepon</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="form">
          <Card>
            <CardHeader>
              <CardTitle>Form Kontak</CardTitle>
              <CardDescription>Isi form di bawah ini dan tim kami akan menghubungi Anda dalam 24 jam</CardDescription>
            </CardHeader>
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nama Lengkap</Label>
                    <Input
                      id="name"
                      placeholder="Masukkan nama lengkap Anda"
                      value={contactForm.name}
                      onChange={(e) => setContactForm({ ...contactForm, name: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      type="email"
                      id="email"
                      placeholder="Masukkan alamat email Anda"
                      value={contactForm.email}
                      onChange={(e) => setContactForm({ ...contactForm, email: e.target.value })}
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="topic">Topik</Label>
                  <Select
                    id="topic"
                    value={contactForm.topic}
                    onValueChange={(value) => setContactForm({ ...contactForm, topic: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih Topik" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pertanyaan">Pertanyaan</SelectItem>
                      <SelectItem value="masalah_teknis">Masalah Teknis</SelectItem>
                      <SelectItem value="saran">Saran</SelectItem>
                      <SelectItem value="lainnya">Lainnya</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="orderNumber">Nomor Order (Opsional)</Label>
                  <Input
                    id="orderNumber"
                    placeholder="Masukkan nomor order Anda"
                    value={contactForm.orderNumber}
                    onChange={(e) => setContactForm({ ...contactForm, orderNumber: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message">Pesan</Label>
                  <Textarea
                    id="message"
                    placeholder="Tuliskan pesan Anda di sini..."
                    rows={5}
                    value={contactForm.message}
                    onChange={(e) => setContactForm({ ...contactForm, message: e.target.value })}
                    required
                  />
                </div>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label>Prioritas</Label>
                    <RadioGroup
                      defaultValue={contactForm.priority}
                      onValueChange={(value) => setContactForm({ ...contactForm, priority: value })}
                      className="flex flex-col space-y-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="low" id="priority-low" />
                        <Label htmlFor="priority-low">Rendah</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="medium" id="priority-medium" />
                        <Label htmlFor="priority-medium">Sedang</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="high" id="priority-high" />
                        <Label htmlFor="priority-high">Tinggi</Label>
                      </div>
                    </RadioGroup>
                  </div>
                  <div className="space-y-2">
                    <Label>Preferensi Respon</Label>
                    <RadioGroup
                      defaultValue={contactForm.responsePreference}
                      onValueChange={(value) => setContactForm({ ...contactForm, responsePreference: value })}
                      className="flex flex-col space-y-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="email" id="response-email" />
                        <Label htmlFor="response-email">Email</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="phone" id="response-phone" />
                        <Label htmlFor="response-phone">Telepon</Label>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="attachFile"
                    checked={contactForm.attachFile}
                    onCheckedChange={(checked) => setContactForm({ ...contactForm, attachFile: checked })}
                  />
                  <Label
                    htmlFor="attachFile"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed"
                  >
                    Lampirkan File
                  </Label>
                </div>
                {contactForm.attachFile && (
                  <div className="space-y-2">
                    <Button variant="outline" disabled>
                      <PaperclipIcon className="mr-2 h-4 w-4" />
                      Pilih File
                    </Button>
                    <p className="text-sm text-muted-foreground">Ukuran maksimum file adalah 2MB</p>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                      Mengirim...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Kirim Pesan
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="chat">
          <Card>
            <CardHeader>
              <CardTitle>Live Chat</CardTitle>
              <CardDescription>Mulai percakapan dengan salah satu agen dukungan kami</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Fitur live chat akan segera hadir!</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="phone">
          <Card>
            <CardHeader>
              <CardTitle>Telepon</CardTitle>
              <CardDescription>Hubungi kami langsung melalui telepon</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Nomor telepon dukungan kami adalah +62 123 456 789</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {isSubmitted && (
        <div className="rounded-md border p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle2 className="h-4 w-4 text-green-500" />
            <p className="text-sm font-medium">Pesan Anda telah terkirim!</p>
          </div>
          <p className="text-sm text-muted-foreground">Kami akan segera menghubungi Anda.</p>
        </div>
      )}
    </div>
  )
}
