"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Edit, ExternalLink, Trash2 } from "lucide-react"
import { storesAPI, type Store } from "@/lib/api/stores"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

export function StoreList() {
  const [stores, setStores] = useState<Store[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [storeToDelete, setStoreToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const fetchStores = async () => {
    try {
      setLoading(true)
      const data = await storesAPI.getAll()
      setStores(data)
      setError(null)
    } catch (err) {
      console.error("Error fetching stores:", err)
      // Tambahkan informasi error yang lebih detail
      if (err.message === "Network Error") {
        setError("Tidak dapat terhubung ke server. Periksa koneksi internet Anda atau coba lagi nanti.")
      } else if (err.response && err.response.status === 401) {
        setError("Sesi Anda telah berakhir. Silakan login kembali.")
      } else if (err.response && err.response.status === 403) {
        setError("Anda tidak memiliki izin untuk mengakses data toko.")
      } else {
        setError("Gagal memuat daftar toko. Silakan coba lagi.")
      }

      // Gunakan data dummy jika dalam mode development
      if (process.env.NEXT_PUBLIC_NODE_ENV === "development" || process.env.NEXT_PUBLIC_VERCEL_ENV === "preview") {
        console.log("Using fallback data in development/preview mode")
        setStores([
          {
            id: "1",
            name: "Toko Demo 1",
            description: "Ini adalah toko demo untuk pengujian",
            slug: "toko-demo-1",
            logo: null,
            banner: null,
            ownerId: "1",
            tenantId: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: "2",
            name: "Toko Demo 2",
            description: "Toko demo kedua untuk pengujian interface",
            slug: "toko-demo-2",
            logo: null,
            banner: null,
            ownerId: "1",
            tenantId: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ])
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStores()
  }, [])

  const handleDelete = async (id: string) => {
    setIsDeleting(true)
    try {
      await storesAPI.delete(id)
      setStores(stores.filter((store) => store.id !== id))
      setStoreToDelete(null)
    } catch (err) {
      console.error("Error deleting store:", err)
      setError("Gagal menghapus toko. Silakan coba lagi.")
    } finally {
      setIsDeleting(false)
    }
  }

  if (loading) {
    return (
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <div className="h-48 bg-muted">
              <Skeleton className="h-full w-full" />
            </div>
            <CardContent className="p-6">
              <Skeleton className="h-6 w-2/3 mb-2" />
              <Skeleton className="h-4 w-full mb-4" />
            </CardContent>
            <CardFooter className="flex justify-between p-6 pt-0">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </CardFooter>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (stores.length === 0) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold mb-2">Belum ada toko</h3>
        <p className="text-muted-foreground mb-4">Anda belum memiliki toko. Buat toko pertama Anda sekarang.</p>
        <Button asChild>
          <Link href="/dashboard/stores/create">Buat Toko Baru</Link>
        </Button>
      </Card>
    )
  }

  return (
    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
      {stores.map((store) => (
        <Card key={store.id} className="overflow-hidden">
          <div className="h-48 bg-muted flex items-center justify-center">
            {store.banner ? (
              <img src={store.banner || "/placeholder.svg"} alt={store.name} className="w-full h-full object-cover" />
            ) : (
              <div className="text-4xl font-bold text-muted-foreground">{store.name.charAt(0)}</div>
            )}
          </div>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-2">{store.name}</h3>
            <p className="text-muted-foreground text-sm line-clamp-2">{store.description || "Tidak ada deskripsi"}</p>
          </CardContent>
          <CardFooter className="flex justify-between p-6 pt-0">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/stores/${store.id}`}>
                <ExternalLink className="mr-2 h-4 w-4" />
                Detail
              </Link>
            </Button>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/dashboard/stores/${store.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </Button>
              <AlertDialog open={storeToDelete === store.id} onOpenChange={(open) => !open && setStoreToDelete(null)}>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" size="sm" onClick={() => setStoreToDelete(store.id)}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Hapus
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Hapus Toko</AlertDialogTitle>
                    <AlertDialogDescription>
                      Apakah Anda yakin ingin menghapus toko "{store.name}"? Tindakan ini tidak dapat dibatalkan.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Batal</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => handleDelete(store.id)}
                      disabled={isDeleting}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      {isDeleting ? "Menghapus..." : "Hapus"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
