"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  AlertCircle,
  CheckCircle2,
  FileText,
  Filter,
  MessageSquare,
  PaperclipIcon,
  Plus,
  Search,
  Send,
} from "lucide-react"

// Dummy data untuk tickets
const tickets = [
  {
    id: "T-1234",
    title: "Pembayaran tidak diproses",
    status: "open",
    priority: "high",
    created: "2023-05-15T10:30:00",
    createdFormatted: "15 Mei 2023, 10:30",
    lastUpdate: "2023-05-15T14:45:00",
    lastUpdateFormatted: "15 Mei 2023, 14:45",
    category: "Pembayaran",
    messages: [
      {
        id: 1,
        sender: "user",
        name: "Anda",
        message:
          "Saya sudah melakukan pembayaran untuk pesanan #ORD-5678 tapi statusnya masih menunggu pembayaran. Saya sudah transfer melalui Bank BCA dan sudah 2 jam belum diproses.",
        time: "15 Mei 2023, 10:30",
        attachments: [{ id: 1, name: "bukti-transfer.jpg", size: "1.2 MB" }],
      },
      {
        id: 2,
        sender: "agent",
        name: "Ahmad (CS)",
        message:
          "Selamat siang, terima kasih telah menghubungi SellZio. Mohon maaf atas ketidaknyamanannya. Saya akan memeriksa status pembayaran Anda. Mohon tunggu sebentar.",
        time: "15 Mei 2023, 10:45",
        attachments: [],
      },
      {
        id: 3,
        sender: "agent",
        name: "Ahmad (CS)",
        message:
          "Saya sudah memeriksa dan menemukan bahwa pembayaran Anda sudah masuk ke sistem kami. Ada sedikit keterlambatan dalam pembaruan status. Saya sudah memperbarui status pesanan Anda menjadi 'Diproses'. Mohon periksa kembali status pesanan Anda dalam 5-10 menit ke depan.",
        time: "15 Mei 2023, 11:05",
        attachments: [],
      },
      {
        id: 4,
        sender: "user",
        name: "Anda",
        message: "Baik, terima kasih atas bantuannya. Saya akan periksa kembali nanti.",
        time: "15 Mei 2023, 11:10",
        attachments: [],
      },
      {
        id: 5,
        sender: "agent",
        name: "Ahmad (CS)",
        message:
          "Sama-sama. Jika Anda memiliki pertanyaan lain atau status belum berubah dalam 10 menit, jangan ragu untuk menghubungi kami kembali. Terima kasih telah menggunakan SellZio!",
        time: "15 Mei 2023, 11:15",
        attachments: [],
      },
    ],
  },
  {
    id: "T-1233",
    title: "Pertanyaan tentang pengiriman",
    status: "waiting",
    priority: "medium",
    created: "2023-05-14T09:15:00",
    createdFormatted: "14 Mei 2023, 09:15",
    lastUpdate: "2023-05-14T16:30:00",
    lastUpdateFormatted: "14 Mei 2023, 16:30",
    category: "Pengiriman",
    messages: [
      {
        id: 1,
        sender: "user",
        name: "Anda",
        message:
          "Halo, saya ingin menanyakan tentang opsi pengiriman untuk produk berat di atas 10kg. Apakah ada layanan khusus?",
        time: "14 Mei 2023, 09:15",
        attachments: [],
      },
      {
        id: 2,
        sender: "agent",
        name: "Budi (CS)",
        message:
          "Selamat pagi, terima kasih telah menghubungi SellZio. Untuk pengiriman produk dengan berat di atas 10kg, kami menyediakan layanan kargo khusus. Saya akan cek detail lebih lanjut untuk Anda.",
        time: "14 Mei 2023, 09:30",
        attachments: [],
      },
    ],
  },
  {
    id: "T-1232",
    title: "Produk tidak sesuai deskripsi",
    status: "closed",
    priority: "medium",
    created: "2023-05-10T14:20:00",
    createdFormatted: "10 Mei 2023, 14:20",
    lastUpdate: "2023-05-12T11:45:00",
    lastUpdateFormatted: "12 Mei 2023, 11:45",
    category: "Produk",
    messages: [
      {
        id: 1,
        sender: "user",
        name: "Anda",
        message:
          "Saya menerima produk yang tidak sesuai dengan deskripsi. Warna yang saya terima berbeda dengan yang ada di gambar.",
        time: "10 Mei 2023, 14:20",
        attachments: [{ id: 1, name: "foto-produk.jpg", size: "2.4 MB" }],
      },
      {
        id: 2,
        sender: "agent",
        name: "Siti (CS)",
        message:
          "Selamat siang, mohon maaf atas ketidaknyamanan ini. Saya akan membantu proses pengembalian atau penukaran produk. Mohon konfirmasi opsi mana yang Anda inginkan.",
        time: "10 Mei 2023, 14:45",
        attachments: [],
      },
      {
        id: 3,
        sender: "user",
        name: "Anda",
        message: "Saya lebih memilih untuk pengembalian dana saja.",
        time: "10 Mei 2023, 15:00",
        attachments: [],
      },
      {
        id: 4,
        sender: "agent",
        name: "Siti (CS)",
        message:
          "Baik, saya akan memproses pengembalian dana. Mohon ikuti instruksi pengembalian produk yang akan saya kirimkan via email.",
        time: "10 Mei 2023, 15:15",
        attachments: [],
      },
      {
        id: 5,
        sender: "agent",
        name: "Siti (CS)",
        message:
          "Pengembalian dana telah diproses dan akan masuk ke rekening Anda dalam 3-5 hari kerja. Terima kasih atas pengertiannya.",
        time: "12 Mei 2023, 11:45",
        attachments: [],
      },
    ],
  },
]

export function TicketsList() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTicket, setSelectedTicket] = useState<any>(tickets[0])
  const [newMessage, setNewMessage] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isNewTicketDialogOpen, setIsNewTicketDialogOpen] = useState(false)
  const [newTicket, setNewTicket] = useState({
    title: "",
    category: "",
    priority: "",
    message: "",
  })

  // Status badge renderer
  const renderStatusBadge = (status) => {
    switch (status) {
      case "open":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
            Aktif
          </Badge>
        )
      case "waiting":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
            Menunggu
          </Badge>
        )
      case "closed":
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
            Selesai
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Priority badge renderer
  const renderPriorityBadge = (priority) => {
    switch (priority) {
      case "high":
        return <Badge variant="destructive">Tinggi</Badge>
      case "medium":
        return <Badge variant="secondary">Sedang</Badge>
      case "low":
        return <Badge variant="outline">Rendah</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  const filteredTickets = tickets.filter((ticket) => {
    if (activeTab !== "all" && ticket.status !== activeTab) {
      return false
    }

    if (searchQuery) {
      return (
        ticket.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.category.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return true
  })

  const handleSendMessage = () => {
    if (!newMessage.trim()) return

    // In a real app, you would send this to your API
    console.log("Sending message:", newMessage)

    // Clear the input
    setNewMessage("")
  }

  const handleCreateTicket = () => {
    // In a real app, you would send this to your API
    console.log("Creating ticket:", newTicket)

    // Reset form and close dialog
    setNewTicket({
      title: "",
      category: "",
      priority: "",
      message: "",
    })
    setIsNewTicketDialogOpen(false)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Tiket Dukungan</h2>
          <p className="text-muted-foreground">Kelola dan pantau tiket dukungan Anda</p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isNewTicketDialogOpen} onOpenChange={setIsNewTicketDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Tiket Baru
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Buat Tiket Dukungan Baru</DialogTitle>
                <DialogDescription>
                  Jelaskan masalah atau pertanyaan Anda dengan detail untuk mendapatkan bantuan lebih cepat.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <label htmlFor="title" className="text-sm font-medium">
                    Judul
                  </label>
                  <Input
                    id="title"
                    placeholder="Ringkasan singkat masalah Anda"
                    value={newTicket.title}
                    onChange={(e) => setNewTicket({ ...newTicket, title: e.target.value })}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <label htmlFor="category" className="text-sm font-medium">
                      Kategori
                    </label>
                    <Select
                      value={newTicket.category}
                      onValueChange={(value) => setNewTicket({ ...newTicket, category: value })}
                    >
                      <SelectTrigger id="category">
                        <SelectValue placeholder="Pilih kategori" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="account">Akun</SelectItem>
                        <SelectItem value="payment">Pembayaran</SelectItem>
                        <SelectItem value="shipping">Pengiriman</SelectItem>
                        <SelectItem value="product">Produk</SelectItem>
                        <SelectItem value="other">Lainnya</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="priority" className="text-sm font-medium">
                      Prioritas
                    </label>
                    <Select
                      value={newTicket.priority}
                      onValueChange={(value) => setNewTicket({ ...newTicket, priority: value })}
                    >
                      <SelectTrigger id="priority">
                        <SelectValue placeholder="Pilih prioritas" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Rendah</SelectItem>
                        <SelectItem value="medium">Sedang</SelectItem>
                        <SelectItem value="high">Tinggi</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid gap-2">
                  <label htmlFor="message" className="text-sm font-medium">
                    Pesan
                  </label>
                  <Textarea
                    id="message"
                    placeholder="Jelaskan masalah Anda secara detail"
                    rows={5}
                    value={newTicket.message}
                    onChange={(e) => setNewTicket({ ...newTicket, message: e.target.value })}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" type="button">
                    <PaperclipIcon className="mr-2 h-4 w-4" />
                    Lampirkan File
                  </Button>
                  <span className="text-xs text-muted-foreground">Max 5MB (JPG, PNG, PDF)</span>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsNewTicketDialogOpen(false)}>
                  Batal
                </Button>
                <Button onClick={handleCreateTicket}>Kirim Tiket</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-1 space-y-4">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari tiket..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>

          <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span>Semua</span>
              </TabsTrigger>
              <TabsTrigger value="open" className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                <span>Aktif</span>
              </TabsTrigger>
              <TabsTrigger value="closed" className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4" />
                <span>Selesai</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="space-y-2">
            {filteredTickets.length > 0 ? (
              filteredTickets.map((ticket) => (
                <Card
                  key={ticket.id}
                  className={`cursor-pointer transition-colors hover:bg-accent/50 ${selectedTicket?.id === ticket.id ? "border-primary" : ""}`}
                  onClick={() => setSelectedTicket(ticket)}
                >
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{ticket.id}</span>
                        {renderStatusBadge(ticket.status)}
                      </div>
                      <h3 className="font-medium">{ticket.title}</h3>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{ticket.category}</span>
                        <span>
                          {new Date(ticket.lastUpdate).toLocaleDateString("id-ID", { day: "numeric", month: "short" })}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
                <MessageSquare className="h-10 w-10 text-muted-foreground/60" />
                <h3 className="mt-4 text-lg font-medium">Tidak ada tiket</h3>
                <p className="mt-2 text-sm text-muted-foreground">Tidak ada tiket yang sesuai dengan filter Anda</p>
              </div>
            )}
          </div>
        </div>

        <div className="md:col-span-2">
          {selectedTicket ? (
            <Card className="h-full flex flex-col">
              <CardHeader className="border-b">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <CardTitle>{selectedTicket.title}</CardTitle>
                      {renderStatusBadge(selectedTicket.status)}
                      {renderPriorityBadge(selectedTicket.priority)}
                    </div>
                    <CardDescription>
                      {selectedTicket.id} • Dibuat pada {selectedTicket.createdFormatted}
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    {selectedTicket.status !== "closed" && (
                      <Button variant="outline" size="sm">
                        Tutup Tiket
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      Cetak
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="flex-1 overflow-auto p-4">
                <div className="space-y-6">
                  {selectedTicket.messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
                    >
                      <div
                        className={`max-w-[80%] rounded-lg p-4 ${
                          message.sender === "user" ? "bg-primary text-primary-foreground" : "bg-muted"
                        }`}
                      >
                        <div className="mb-1 flex items-center justify-between gap-4">
                          <span className="font-medium">{message.name}</span>
                          <span className="text-xs opacity-80">{message.time}</span>
                        </div>
                        <p className="text-sm">{message.message}</p>
                        {message.attachments && message.attachments.length > 0 && (
                          <div className="mt-2 space-y-1">
                            {message.attachments.map((attachment) => (
                              <div
                                key={attachment.id}
                                className={`flex items-center gap-2 rounded-md p-2 ${
                                  message.sender === "user"
                                    ? "bg-primary-foreground/10 text-primary-foreground"
                                    : "bg-background"
                                }`}
                              >
                                <PaperclipIcon className="h-4 w-4" />
                                <span className="text-xs">{attachment.name}</span>
                                <span className="text-xs opacity-70">({attachment.size})</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              {selectedTicket.status !== "closed" && (
                <CardFooter className="border-t p-4">
                  <div className="flex w-full items-center gap-2">
                    <Button variant="outline" size="icon">
                      <PaperclipIcon className="h-4 w-4" />
                    </Button>
                    <Input
                      placeholder="Ketik pesan Anda..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      className="flex-1"
                    />
                    <Button onClick={handleSendMessage}>
                      <Send className="mr-2 h-4 w-4" />
                      Kirim
                    </Button>
                  </div>
                </CardFooter>
              )}
            </Card>
          ) : (
            <Card className="h-full flex items-center justify-center">
              <CardContent className="flex flex-col items-center justify-center p-8 text-center">
                <MessageSquare className="h-12 w-12 text-muted-foreground/60" />
                <h3 className="mt-4 text-lg font-medium">Tidak ada tiket yang dipilih</h3>
                <p className="mt-2 text-sm text-muted-foreground">Pilih tiket dari daftar atau buat tiket baru</p>
                <Button className="mt-4" onClick={() => setIsNewTicketDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Buat Tiket Baru
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
