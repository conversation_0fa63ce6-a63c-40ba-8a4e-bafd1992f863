"use client"

import { useCallback, useRef } from "react"

export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
): (...args: Parameters<T>) => void {
  const lastCall = useRef(0)
  const lastArgs = useRef<Parameters<T> | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  return useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now()
      lastArgs.current = args

      const execute = () => {
        lastCall.current = now
        lastArgs.current = null
        callback(...args)
      }

      if (now - lastCall.current >= delay) {
        execute()
      } else {
        // Clear any existing timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }

        // Schedule a new timeout
        timeoutRef.current = setTimeout(
          () => {
            if (lastArgs.current) {
              execute()
            }
          },
          delay - (now - lastCall.current),
        )
      }
    },
    [callback, delay],
  )
}
