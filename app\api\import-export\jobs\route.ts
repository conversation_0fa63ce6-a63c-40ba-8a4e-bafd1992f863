import { NextRequest, NextResponse } from 'next/server';
import { productImportExportService } from '@/lib/services/product-import-export';

// GET - Mendapatkan semua import/export jobs
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    
    const filters = {
      type: searchParams.get('type') || undefined,
      status: searchParams.get('status') || undefined,
      date_from: searchParams.get('date_from') || undefined,
      date_to: searchParams.get('date_to') || undefined,
    };

    // Remove undefined values
    const cleanFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    );
    
    const jobs = await productImportExportService.getJobs(cleanFilters);
    
    return NextResponse.json(jobs);
  } catch (error) {
    console.error('Error fetching import/export jobs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch import/export jobs' },
      { status: 500 }
    );
  }
}

// POST - Membuat job baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newJob = await productImportExportService.createJob(body);
    
    return NextResponse.json(newJob, { status: 201 });
  } catch (error) {
    console.error('Error creating import/export job:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to create job';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
