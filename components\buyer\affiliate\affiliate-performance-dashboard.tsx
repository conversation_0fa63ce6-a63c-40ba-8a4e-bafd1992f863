import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, LineChart } from "lucide-react"

export function AffiliatePerformanceDashboard() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Performance Dashboard</CardTitle>
        <CardDescription>Detailed metrics and analysis of your affiliate performance</CardDescription>
        <div className="flex items-center space-x-2">
          <Tabs defaultValue="daily" className="w-[400px]">
            <TabsList>
              <TabsTrigger value="daily">Daily</TabsTrigger>
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
              <TabsTrigger value="yearly">Yearly</TabsTrigger>
            </TabsList>
          </Tabs>
          <div className="flex items-center space-x-2 ml-auto">
            <button className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800">
              <LineChart className="h-5 w-5" />
            </button>
            <button className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800">
              <BarChart className="h-5 w-5" />
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Traffic Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                <p className="text-sm text-gray-500 dark:text-gray-400">Traffic sources chart</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Device Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                <p className="text-sm text-gray-500 dark:text-gray-400">Device distribution chart</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Geographic Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                <p className="text-sm text-gray-500 dark:text-gray-400">Geographic distribution map</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Time Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                <p className="text-sm text-gray-500 dark:text-gray-400">Time analysis chart</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  )
}
