"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Grid3X3, List, Search, FolderPlus, Folder, Trash2, Edit, Lock, Globe, Eye, Pencil, Share2 } from "lucide-react"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import { DeleteConfirmationDialog } from "@/components/buyer/delete-confirmation-dialog"
import { CollectionCreator } from "@/components/buyer/collection-creator"
import { ShareCollection } from "@/components/buyer/share-collection"

interface Collection {
  id: string
  name: string
  description: string
  isPublic: boolean
  items: string[]
  createdAt: string
  updatedAt: string
}

export default function CollectionsPage() {
  const [collections, setCollections] = useState<Collection[]>([])
  const [filteredCollections, setFilteredCollections] = useState<Collection[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const router = useRouter()

  // Fungsi untuk mengambil daftar koleksi
  const fetchCollections = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/collections')
      if (!response.ok) {
        throw new Error('Gagal mengambil data koleksi')
      }
      const data = await response.json()
      setCollections(data)
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal memuat data koleksi",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fungsi untuk menghapus koleksi
  const deleteCollection = async (id: string) => {
    try {
      const response = await fetch(`/api/collections/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        throw new Error('Gagal menghapus koleksi')
      }
      
      // Update state lokal setelah menghapus
      setCollections(prev => prev.filter(collection => collection.id !== id))
      
      toast({
        title: "Berhasil",
        description: "Koleksi berhasil dihapus",
      })
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal menghapus koleksi",
        variant: "destructive",
      })
    }
  }

  // Fungsi untuk melihat detail koleksi
  const viewCollectionDetail = (id: string) => {
    router.push(`/buyer/dashboard/collections/${id}`)
  }

  // Effect untuk mengambil data koleksi saat komponen dimuat
  useEffect(() => {
    fetchCollections()
  }, [])

  // Effect untuk memfilter koleksi berdasarkan pencarian
  useEffect(() => {
    if (!collections.length) {
      setFilteredCollections([])
      return
    }

    if (!searchQuery) {
      setFilteredCollections(collections)
      return
    }

    const query = searchQuery.toLowerCase()
    const results = collections.filter(
      collection => 
        collection.name.toLowerCase().includes(query) || 
        collection.description.toLowerCase().includes(query)
    )

    setFilteredCollections(results)
  }, [collections, searchQuery])

  // Fungsi untuk memformat tanggal
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Tampilkan loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-40" />
          <div className="flex gap-2">
            <Skeleton className="h-9 w-32" />
            <Skeleton className="h-9 w-32" />
          </div>
        </div>
        <div className="flex flex-col gap-4 sm:flex-row">
          <Skeleton className="h-10 flex-1" />
          <div className="flex gap-2">
            <Skeleton className="h-10 w-20" />
          </div>
        </div>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {Array(8).fill(0).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardContent className="p-4">
                <Skeleton className="mb-2 h-5 w-24" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="mt-1 h-4 w-3/4" />
              </CardContent>
              <CardFooter className="p-4 pt-0">
                <Skeleton className="h-9 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Koleksi Saya</h1>
        <div className="flex items-center gap-2">
          <CollectionCreator 
            buttonLabel="Buat Koleksi Baru" 
            onSuccess={() => fetchCollections()}
          />
        </div>
      </div>

      {/* Pencarian dan Tampilan */}
      <div className="flex flex-col gap-4 sm:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Cari koleksi..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <div className="flex rounded-md border">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="icon"
              className="rounded-none rounded-l-md"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="icon"
              className="rounded-none rounded-r-md"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Tampilkan pesan jika tidak ada koleksi */}
      {filteredCollections.length === 0 ? (
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">
          <div className="mb-4 rounded-full bg-muted p-4">
            <Folder className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="mb-2 text-xl font-medium">Belum Ada Koleksi</h3>
          <p className="mb-6 text-muted-foreground">
            {searchQuery 
              ? "Tidak ada koleksi yang cocok dengan pencarian Anda." 
              : "Anda belum membuat koleksi apapun."}
          </p>
          {searchQuery ? (
            <Button onClick={() => setSearchQuery("")}>Hapus Pencarian</Button>
          ) : (
            <CollectionCreator 
              buttonLabel="Buat Koleksi Pertama" 
              onSuccess={() => fetchCollections()}
            />
          )}
        </div>
      ) : viewMode === "grid" ? (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {filteredCollections.map((collection) => (
            <Card key={collection.id} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">{collection.name}</h3>
                  {collection.isPublic ? (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Globe className="h-3 w-3" />
                      <span>Publik</span>
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Lock className="h-3 w-3" />
                      <span>Privat</span>
                    </Badge>
                  )}
                </div>
                {collection.description && (
                  <p className="mt-2 text-sm text-muted-foreground line-clamp-2">
                    {collection.description}
                  </p>
                )}
                <div className="mt-3 flex items-center justify-between text-xs text-muted-foreground">
                  <span>{collection.items.length} item</span>
                  <span>Dibuat {formatDate(collection.createdAt)}</span>
                </div>
              </CardContent>
              <CardFooter className="flex gap-2 p-4 pt-0">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => viewCollectionDetail(collection.id)}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  Lihat
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => router.push(`/buyer/dashboard/collections/${collection.id}/edit`)}
                >
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit
                </Button>
                <ShareCollection
                  collectionId={collection.id}
                  collectionName={collection.name}
                  isPublic={collection.isPublic}
                >
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-10 px-0"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </ShareCollection>
                <DeleteConfirmationDialog
                  title="Hapus Koleksi"
                  description={`Apakah Anda yakin ingin menghapus koleksi "${collection.name}"? Tindakan ini tidak dapat dibatalkan.`}
                  onConfirm={() => deleteCollection(collection.id)}
                >
                  <Button 
                    variant="destructive" 
                    size="sm" 
                    className="w-10 px-0"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </DeleteConfirmationDialog>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredCollections.map((collection) => (
            <Card key={collection.id}>
              <CardContent className="flex items-start gap-4 p-4">
                <div className="h-12 w-12 rounded-md bg-muted flex items-center justify-center">
                  <Folder className="h-6 w-6 text-muted-foreground" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{collection.name}</h3>
                    {collection.isPublic ? (
                      <Badge variant="outline" className="flex items-center gap-1">
                        <Globe className="h-3 w-3" />
                        <span>Publik</span>
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="flex items-center gap-1">
                        <Lock className="h-3 w-3" />
                        <span>Privat</span>
                      </Badge>
                    )}
                  </div>
                  {collection.description && (
                    <p className="mt-1 text-sm text-muted-foreground">
                      {collection.description}
                    </p>
                  )}
                  <div className="mt-2 flex items-center gap-4 text-xs text-muted-foreground">
                    <span>{collection.items.length} item</span>
                    <span>Dibuat {formatDate(collection.createdAt)}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => viewCollectionDetail(collection.id)}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Lihat
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => router.push(`/buyer/dashboard/collections/${collection.id}/edit`)}
                  >
                    <Pencil className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                  <ShareCollection
                    collectionId={collection.id}
                    collectionName={collection.name}
                    isPublic={collection.isPublic}
                  >
                    <Button 
                      variant="outline" 
                      size="sm"
                    >
                      <Share2 className="mr-2 h-4 w-4" />
                      Bagikan
                    </Button>
                  </ShareCollection>
                  <DeleteConfirmationDialog
                    title="Hapus Koleksi"
                    description={`Apakah Anda yakin ingin menghapus koleksi "${collection.name}"? Tindakan ini tidak dapat dibatalkan.`}
                    onConfirm={() => deleteCollection(collection.id)}
                  >
                    <Button 
                      variant="destructive" 
                      size="sm"
                      className="gap-1"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Hapus
                    </Button>
                  </DeleteConfirmationDialog>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
} 