"use client"

import { Suspense } from "react"
import {
  BarChart,
  CreditCard,
  DollarSign,
  Package,
  Percent,
  ShoppingBag,
  ShoppingCart,
  Star,
  Users,
} from "lucide-react"
import { StatCard } from "@/components/dashboard/stat-card"
import { ChartCard } from "@/components/dashboard/chart-card"
import { ActivityFeed } from "@/components/dashboard/activity-feed"
import { QuickActions } from "@/components/dashboard/quick-actions"
import { formatCurrency } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

// Data dummy untuk grafik
const revenueData = [
  { name: "<PERSON>", total: 750000 },
  { name: "Feb", total: 1200000 },
  { name: "<PERSON>", total: 900000 },
  { name: "Apr", total: 1500000 },
  { name: "<PERSON>", total: 1300000 },
  { name: "<PERSON>", total: 1800000 },
  { name: "<PERSON>", total: 2100000 },
]

const ordersData = [
  { name: "<PERSON>", total: 12 },
  { name: "Feb", total: 18 },
  { name: "<PERSON>", total: 15 },
  { name: "Apr", total: 22 },
  { name: "<PERSON>", total: 20 },
  { name: "Jun", total: 25 },
  { name: "Jul", total: 30 },
]

const activityItems = [
  {
    id: "1",
    user: { name: "Budi Santoso", email: "<EMAIL>" },
    action: "membeli produk",
    target: "Kemeja Casual",
    date: "Baru saja",
    status: "success" as const,
  },
  {
    id: "2",
    user: { name: "Siti Rahayu", email: "<EMAIL>" },
    action: "menambahkan ulasan",
    target: "5 bintang",
    date: "5 menit yang lalu",
    status: "success" as const,
  },
  {
    id: "3",
    user: { name: "Agus Wijaya", email: "<EMAIL>" },
    action: "menambahkan produk ke keranjang",
    target: "Celana Jeans",
    date: "15 menit yang lalu",
    status: "info" as const,
  },
  {
    id: "4",
    user: { name: "Dewi Lestari", email: "<EMAIL>" },
    action: "membatalkan pesanan",
    target: "#12345",
    date: "30 menit yang lalu",
    status: "error" as const,
  },
  {
    id: "5",
    user: { name: "Rudi Hartono", email: "<EMAIL>" },
    action: "mengirim pesan",
    target: "tentang produk",
    date: "1 jam yang lalu",
    status: "info" as const,
  },
]

const quickActions = [
  {
    name: "Tambah Produk",
    icon: ShoppingBag,
    href: "/store/dashboard/products/create",
  },
  {
    name: "Lihat Pesanan",
    icon: Package,
    href: "/store/dashboard/orders",
  },
  {
    name: "Tambah Promo",
    icon: Percent,
    href: "/store/dashboard/marketing/promotions/create",
  },
  {
    name: "Lihat Ulasan",
    icon: Star,
    href: "/store/dashboard/products/reviews",
  },
  {
    name: "Analitik Toko",
    icon: BarChart,
    href: "/store/dashboard/analytics",
  },
  {
    name: "Pengaturan Toko",
    icon: CreditCard,
    href: "/store/dashboard/settings/profile",
  },
]

export default function StoreDashboardPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard Toko</h1>
        <div className="flex items-center gap-2">
          <div className="text-sm text-muted-foreground">Terakhir diperbarui: {new Date().toLocaleString("id-ID")}</div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Total Pendapatan"
            value={formatCurrency(2100000)}
            description="Pendapatan bulan ini"
            icon={DollarSign}
            trend="up"
            trendValue="15%"
            variant="primary"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Pesanan"
            value="30"
            description="Pesanan bulan ini"
            icon={ShoppingCart}
            trend="up"
            trendValue="20%"
            variant="success"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Produk"
            value="45"
            description="Total produk aktif"
            icon={ShoppingBag}
            trend="up"
            trendValue="5%"
            variant="warning"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Pelanggan"
            value="128"
            description="Total pelanggan"
            icon={Users}
            trend="up"
            trendValue="8%"
            variant="default"
          />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ChartCard
            title="Pendapatan"
            description="Pendapatan 7 bulan terakhir"
            data={revenueData}
            type="line"
            dataKey="total"
            valueFormatter={(value) => formatCurrency(value)}
            colors={{
              stroke: "#3B82F6",
              fill: "rgba(59, 130, 246, 0.1)",
            }}
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ChartCard
            title="Pesanan"
            description="Jumlah pesanan 7 bulan terakhir"
            data={ordersData}
            type="bar"
            dataKey="total"
            valueFormatter={(value) => `${value} pesanan`}
            colors={{
              stroke: "#3B82F6",
              fill: "url(#blueGradient)",
            }}
          />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <QuickActions title="Aksi Cepat" description="Akses cepat ke fitur utama" actions={quickActions} />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ActivityFeed title="Aktivitas Terbaru" description="Aktivitas terbaru di toko Anda" items={activityItems} />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <Card>
            <CardHeader>
              <CardTitle>Produk Terlaris</CardTitle>
              <CardDescription>Produk dengan penjualan tertinggi</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { name: "Kemeja Casual", sales: 24, revenue: 2400000 },
                  { name: "Celana Jeans", sales: 18, revenue: 1800000 },
                  { name: "Jaket Denim", sales: 15, revenue: 2250000 },
                  { name: "Kaos Polos", sales: 12, revenue: 600000 },
                  { name: "Topi Baseball", sales: 10, revenue: 500000 },
                ].map((product, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                        <ShoppingBag className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">{product.name}</p>
                        <p className="text-xs text-muted-foreground">{product.sales} terjual</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{formatCurrency(product.revenue)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <Card>
            <CardHeader>
              <CardTitle>Ulasan Terbaru</CardTitle>
              <CardDescription>Ulasan terbaru dari pelanggan</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    name: "Budi Santoso",
                    product: "Kemeja Casual",
                    rating: 5,
                    comment: "Kualitas bagus, pengiriman cepat!",
                  },
                  {
                    name: "Siti Rahayu",
                    product: "Celana Jeans",
                    rating: 4,
                    comment: "Sesuai dengan gambar, nyaman dipakai.",
                  },
                  { name: "Agus Wijaya", product: "Jaket Denim", rating: 5, comment: "Sangat puas dengan produknya!" },
                  {
                    name: "Dewi Lestari",
                    product: "Kaos Polos",
                    rating: 3,
                    comment: "Lumayan, tapi warnanya sedikit berbeda.",
                  },
                ].map((review, i) => (
                  <div key={i} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">{review.name}</p>
                      <div className="flex">
                        {Array(5)
                          .fill(0)
                          .map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${i < review.rating ? "text-yellow-500 fill-yellow-500" : "text-muted-foreground"}`}
                            />
                          ))}
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">Produk: {review.product}</p>
                    <p className="text-sm">{review.comment}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </Suspense>
      </div>
    </div>
  )
}
