import type { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"
import { CheckoutPage } from "@/components/checkout/checkout-page"
import { Skeleton } from "@/components/ui/skeleton"

export const metadata: Metadata = {
  title: "Checkout | Sellzio",
  description: "Selesaikan pembelian Anda",
}

export default function Checkout() {
  return (
    <Suspense fallback={<CheckoutSkeleton />}>
      <CheckoutPage />
    </Suspense>
  )
}

function CheckoutSkeleton() {
  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      <div className="grid gap-8 md:grid-cols-3">
        <div className="md:col-span-2">
          <Skeleton className="h-[600px] w-full rounded-lg" />
        </div>
        <div>
          <Skeleton className="h-[400px] w-full rounded-lg" />
        </div>
      </div>
    </div>
  )
}
