import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { AlertCircle, Calendar, DollarSign, TrendingUp } from "lucide-react"

export function AffiliatePaymentDashboard() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Payment Dashboard</CardTitle>
        <CardDescription>Track your earnings and upcoming payouts</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Available Balance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-2xl font-bold">$842.50</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">Last updated: Today at 09:45 AM</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pending Balance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <TrendingUp className="h-5 w-5 text-blue-500 mr-2" />
                <span className="text-2xl font-bold">$1,245.75</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">Will be available in 15-30 days</p>
            </CardContent>
          </Card>
        </div>

        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Payment Threshold</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">$842.50 of $1,000</span>
                <span className="text-sm font-medium">84.25%</span>
              </div>
              <Progress value={84.25} className="h-2" />
              <p className="text-xs text-gray-500">
                <AlertCircle className="h-3 w-3 inline mr-1" />
                You need $157.50 more to reach the payment threshold
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Next Payout</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="text-lg font-medium">June 15, 2023</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">Estimated amount: $842.50</p>
              </div>
              <Button variant="outline" size="sm">
                Request Early Payout
              </Button>
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  )
}
