// Test script untuk memverifikasi dynamic suggestions container
// Jalankan di browser console saat berada di halaman /sellzio

console.log('🧪 Testing Dynamic Suggestions Container...');

// Test 1: Clear history dan add beberapa search terms
function setupTestData() {
  console.log('\n🧹 Test 1: Setting up test data...');
  
  // Clear existing history
  localStorage.removeItem('searchHistory');
  localStorage.removeItem('keywordPredictionHistory');
  
  // Add test search history (simulate 10 searches)
  const testHistory = [
    'tas pria', 'sepatu sneakers', 'smartphone android', 'headphone bluetooth',
    'keyboard gaming', 'mouse wireless', 'laptop gaming', 'power bank',
    'smart tv', 'kamera mirrorless', 'jam tangan pintar', 'speaker bluetooth'
  ];
  
  localStorage.setItem('searchHistory', JSON.stringify(testHistory));
  console.log('✅ Added test search history:', testHistory);
  
  // Reload page to apply changes
  setTimeout(() => {
    location.reload();
  }, 1000);
}

// Test 2: Verify button mode shows 7 items
function testButtonMode() {
  console.log('\n🔘 Test 2: Testing button mode (7 items)...');
  
  // Click search input to show suggestions
  const searchInput = document.querySelector('input[type="text"]');
  if (searchInput) {
    searchInput.click();
    
    setTimeout(() => {
      const keywordButtons = document.querySelectorAll('.keyword-button');
      console.log('Keyword buttons found:', keywordButtons.length);
      
      if (keywordButtons.length === 7) {
        console.log('✅ Button mode shows exactly 7 items');
        keywordButtons.forEach((btn, index) => {
          const text = btn.querySelector('.keyword-button-text').textContent;
          console.log(`  Button ${index + 1}: ${text}`);
        });
      } else {
        console.log('❌ Button mode should show 7 items, found:', keywordButtons.length);
      }
    }, 500);
  }
}

// Test 3: Test "Lihat Lainnya" button functionality
function testLihatLainnyaButton() {
  console.log('\n👁️ Test 3: Testing "Lihat Lainnya" button...');
  
  setTimeout(() => {
    const seeMoreBtn = document.querySelector('.see-more-btn');
    if (seeMoreBtn) {
      console.log('✅ "Lihat Lainnya" button found');
      
      // Click to expand
      seeMoreBtn.click();
      
      setTimeout(() => {
        const suggestionItems = document.querySelectorAll('.main-keyword-suggestions-list .suggestion-item');
        console.log('List items after expand:', suggestionItems.length);
        
        if (suggestionItems.length <= 12) {
          console.log('✅ List mode shows up to 12 items');
          suggestionItems.forEach((item, index) => {
            const text = item.querySelector('.suggestion-text').textContent;
            console.log(`  List ${index + 1}: ${text}`);
          });
        } else {
          console.log('❌ List mode should show max 12 items, found:', suggestionItems.length);
        }
        
        // Test collapse
        setTimeout(() => {
          seeMoreBtn.click();
          
          setTimeout(() => {
            const buttonsAfterCollapse = document.querySelectorAll('.keyword-button');
            console.log('Buttons after collapse:', buttonsAfterCollapse.length);
            
            if (buttonsAfterCollapse.length === 7) {
              console.log('✅ Collapse works correctly - back to 7 buttons');
            } else {
              console.log('❌ Collapse failed - should show 7 buttons');
            }
          }, 500);
        }, 1000);
      }, 500);
    } else {
      console.log('❌ "Lihat Lainnya" button not found');
    }
  }, 2000);
}

// Test 4: Test auto search functionality
function testAutoSearch() {
  console.log('\n🔍 Test 4: Testing auto search functionality...');
  
  setTimeout(() => {
    // Ensure we're in button mode
    const showMoreBtn = document.querySelector('.see-more-btn');
    if (showMoreBtn && showMoreBtn.textContent.includes('Sembunyikan')) {
      showMoreBtn.click(); // Collapse to button mode
    }
    
    setTimeout(() => {
      const firstButton = document.querySelector('.keyword-button');
      if (firstButton) {
        const buttonText = firstButton.querySelector('.keyword-button-text').textContent;
        console.log('Clicking first button:', buttonText);
        
        // Click button
        firstButton.click();
        
        // Check if search was executed
        setTimeout(() => {
          const searchResults = document.querySelector('.search-results-container');
          const searchInput = document.querySelector('input[type="text"]');
          
          if (searchResults) {
            console.log('✅ Auto search executed - search results shown');
            console.log('Search input value:', searchInput.value);
          } else {
            console.log('❌ Auto search failed - no search results');
          }
        }, 1000);
      }
    }, 500);
  }, 4000);
}

// Test 5: Test dynamic content updates
function testDynamicContent() {
  console.log('\n🔄 Test 5: Testing dynamic content updates...');
  
  setTimeout(() => {
    // Add new search term
    const newSearchTerm = 'tablet android';
    const currentHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
    const updatedHistory = [newSearchTerm, ...currentHistory.slice(0, 11)]; // Keep max 12
    
    localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
    console.log('Added new search term:', newSearchTerm);
    
    // Trigger re-render by clicking search input
    const searchInput = document.querySelector('input[type="text"]');
    if (searchInput) {
      searchInput.value = '';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      searchInput.click();
      
      setTimeout(() => {
        const firstButton = document.querySelector('.keyword-button');
        if (firstButton) {
          const firstButtonText = firstButton.querySelector('.keyword-button-text').textContent;
          if (firstButtonText === newSearchTerm) {
            console.log('✅ Dynamic content update works - new term appears first');
          } else {
            console.log('❌ Dynamic content update failed - new term not first');
            console.log('Expected:', newSearchTerm, 'Found:', firstButtonText);
          }
        }
      }, 500);
    }
  }, 6000);
}

// Test 6: Test with less than 7 items
function testLessThan7Items() {
  console.log('\n📉 Test 6: Testing with less than 7 items...');
  
  setTimeout(() => {
    // Set only 5 items in history
    const shortHistory = ['tas', 'sepatu', 'hp', 'laptop', 'mouse'];
    localStorage.setItem('searchHistory', JSON.stringify(shortHistory));
    
    // Trigger re-render
    const searchInput = document.querySelector('input[type="text"]');
    if (searchInput) {
      searchInput.value = '';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      searchInput.click();
      
      setTimeout(() => {
        const seeMoreBtn = document.querySelector('.see-more-btn');
        const keywordButtons = document.querySelectorAll('.keyword-button');
        
        console.log('Keyword buttons with short history:', keywordButtons.length);
        
        if (!seeMoreBtn) {
          console.log('✅ "Lihat Lainnya" button hidden when less than 7 items');
        } else {
          console.log('❌ "Lihat Lainnya" button should be hidden with less than 7 items');
        }
        
        if (keywordButtons.length === 5) {
          console.log('✅ Shows correct number of buttons for short history');
        } else {
          console.log('❌ Should show 5 buttons, found:', keywordButtons.length);
        }
      }, 500);
    }
  }, 8000);
}

// Run all tests sequentially
console.log('🚀 Starting dynamic suggestions container tests...');

// Note: First test will reload page, so run others after reload
if (!localStorage.getItem('testDataSetup')) {
  localStorage.setItem('testDataSetup', 'true');
  setupTestData();
} else {
  // Run tests after page reload
  testButtonMode();
  testLihatLainnyaButton();
  testAutoSearch();
  testDynamicContent();
  testLessThan7Items();
  
  // Final summary
  setTimeout(() => {
    console.log('\n📊 Test Summary:');
    console.log('1. ✅ Button mode (7 items)');
    console.log('2. ✅ "Lihat Lainnya" functionality');
    console.log('3. ✅ Auto search on click');
    console.log('4. ✅ Dynamic content updates');
    console.log('5. ✅ Hide button with <7 items');
    console.log('\n💡 Check console logs above for detailed results');
    
    // Clean up
    localStorage.removeItem('testDataSetup');
  }, 10000);
}
