"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Filter,
  Download,
  ChevronDown,
  PlusCircle,
  MinusCircle,
  RefreshCw,
  Search,
  ArrowDown,
  ArrowUp,
  AlertCircle,
  FileText,
  Calendar,
  BarChart3,
  Tag
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { addDays } from "date-fns"

// Data dummy untuk ringkasan transaksi
const transactionsSummary = {
  totalRevenue: ********,
  totalRefunds: 3500000,
  totalFees: 1350000,
  netRevenue: ********,
  transactionCount: 284,
  refundCount: 12,
  averageOrderValue: 159300,
  topSources: [
    { name: "Web Store", percentage: 65 },
    { name: "Mobile App", percentage: 25 },
    { name: "Marketplace", percentage: 10 }
  ],
  paymentMethods: [
    { name: "Kartu Kredit", percentage: 40 },
    { name: "Transfer Bank", percentage: 30 },
    { name: "E-Wallet", percentage: 25 },
    { name: "QRIS", percentage: 5 }
  ]
}

// Data dummy untuk transaksi
const transactions = [
  {
    id: "TRX-001",
    type: "payment",
    amount: 850000,
    status: "completed",
    paymentMethod: "Kartu Kredit",
    customerName: "Budi Santoso",
    customerEmail: "<EMAIL>",
    date: "2024-05-20T14:30:00",
    orderNumber: "ORD-10001",
    items: [
      { name: "Produk Premium", price: 850000, quantity: 1 }
    ],
    fees: 25500,
    description: "Pembayaran untuk Produk Premium",
    source: "Web Store"
  },
  {
    id: "TRX-002",
    type: "payment",
    amount: 1200000,
    status: "completed",
    paymentMethod: "Transfer Bank",
    customerName: "Siti Nurhaliza",
    customerEmail: "<EMAIL>",
    date: "2024-05-19T10:15:00",
    orderNumber: "ORD-10002",
    items: [
      { name: "Paket Basic", price: 500000, quantity: 1 },
      { name: "Add-on Service", price: 700000, quantity: 1 }
    ],
    fees: 36000,
    description: "Pembayaran untuk Paket Basic + Add-on",
    source: "Mobile App"
  },
  {
    id: "TRX-003",
    type: "refund",
    amount: 500000,
    status: "completed",
    paymentMethod: "Kartu Kredit",
    customerName: "Hendro Wijaya",
    customerEmail: "<EMAIL>",
    date: "2024-05-18T16:45:00",
    orderNumber: "ORD-10003",
    items: [
      { name: "Paket Starter", price: 500000, quantity: 1 }
    ],
    fees: 0,
    description: "Pengembalian dana untuk Paket Starter - Pembatalan oleh customer",
    source: "Web Store",
    refundReason: "Pembatalan oleh customer"
  },
  {
    id: "TRX-004",
    type: "payment",
    amount: 1450000,
    status: "pending",
    paymentMethod: "Transfer Bank",
    customerName: "Diana Purnama",
    customerEmail: "<EMAIL>",
    date: "2024-05-20T09:30:00",
    orderNumber: "ORD-10004",
    items: [
      { name: "Paket Pro", price: 1450000, quantity: 1 }
    ],
    fees: 43500,
    description: "Pembayaran untuk Paket Pro - Menunggu konfirmasi transfer",
    source: "Web Store"
  },
  {
    id: "TRX-005",
    type: "payment",
    amount: 350000,
    status: "failed",
    paymentMethod: "E-Wallet",
    customerName: "Rudi Hartono",
    customerEmail: "<EMAIL>",
    date: "2024-05-20T11:20:00",
    orderNumber: "ORD-10005",
    items: [
      { name: "Add-on Marketing", price: 350000, quantity: 1 }
    ],
    fees: 10500,
    description: "Pembayaran untuk Add-on Marketing - Gagal diproses",
    source: "Mobile App",
    failReason: "Saldo tidak mencukupi"
  },
  {
    id: "TRX-006",
    type: "payment",
    amount: 750000,
    status: "completed",
    paymentMethod: "QRIS",
    customerName: "Anita Dewi",
    customerEmail: "<EMAIL>",
    date: "2024-05-17T13:15:00",
    orderNumber: "ORD-10006",
    items: [
      { name: "Paket Basic", price: 500000, quantity: 1 },
      { name: "Add-on Support", price: 250000, quantity: 1 }
    ],
    fees: 22500,
    description: "Pembayaran untuk Paket Basic + Support",
    source: "Mobile App"
  },
  {
    id: "TRX-007",
    type: "refund",
    amount: 250000,
    status: "processing",
    paymentMethod: "Kartu Kredit",
    customerName: "Maya Indriani",
    customerEmail: "<EMAIL>",
    date: "2024-05-20T10:45:00",
    orderNumber: "ORD-10007",
    items: [
      { name: "Add-on Analytics", price: 250000, quantity: 1 }
    ],
    fees: 0,
    description: "Pengembalian dana untuk Add-on Analytics - Permintaan customer",
    source: "Web Store",
    refundReason: "Fitur tidak sesuai kebutuhan"
  }
]

// Format mata uang
function formatCurrency(number: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(number)
}

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Format waktu
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format tanggal dan waktu
function formatDateTime(dateString: string) {
  return `${formatDate(dateString)}, ${formatTime(dateString)}`
}

export default function TransactionsPage() {
  const [selectedTab, setSelectedTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [filterType, setFilterType] = useState("all")
  const [filterSource, setFilterSource] = useState("all")
  const [startDate, setStartDate] = useState(addDays(new Date(), -30).toISOString().substring(0, 10))
  const [endDate, setEndDate] = useState(new Date().toISOString().substring(0, 10))
  
  // Filter transaksi berdasarkan tab, pencarian, dan filter
  const filteredTransactions = transactions.filter(transaction => {
    // Filter berdasarkan tab
    if (selectedTab === "payments" && transaction.type !== "payment") {
      return false
    }
    if (selectedTab === "refunds" && transaction.type !== "refund") {
      return false
    }
    
    // Filter berdasarkan status
    if (filterStatus !== "all" && transaction.status !== filterStatus) {
      return false
    }
    
    // Filter berdasarkan tipe
    if (filterType !== "all" && transaction.type !== filterType) {
      return false
    }
    
    // Filter berdasarkan sumber
    if (filterSource !== "all" && transaction.source !== filterSource) {
      return false
    }
    
    // Filter berdasarkan tanggal
    const transactionDate = new Date(transaction.date)
    const start = new Date(startDate)
    const end = new Date(endDate)
    end.setHours(23, 59, 59) // Set to end of day
    
    if (transactionDate < start || transactionDate > end) {
      return false
    }
    
    // Filter berdasarkan pencarian
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        transaction.id.toLowerCase().includes(query) ||
        transaction.orderNumber.toLowerCase().includes(query) ||
        transaction.customerName.toLowerCase().includes(query) ||
        transaction.customerEmail.toLowerCase().includes(query) ||
        transaction.description.toLowerCase().includes(query)
      )
    }
    
    return true
  })

  // Menghitung total dari transaksi yang difilter
  const filteredTotal = filteredTransactions.reduce((total, transaction) => {
    if (transaction.type === "payment") {
      return total + transaction.amount
    } else if (transaction.type === "refund") {
      return total - transaction.amount
    }
    return total
  }, 0)
  
  // Daftar sumber transaksi unik untuk filter
  const sourcesOptions = Array.from(new Set(transactions.map(t => t.source)))

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/financial">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Transaksi</h1>
            <p className="text-muted-foreground">
              Kelola dan pantau semua transaksi keuangan
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Ekspor
          </Button>
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Segarkan
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
            <ArrowUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(transactionsSummary.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Dari {transactionsSummary.transactionCount} transaksi
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Refund</CardTitle>
            <ArrowDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(transactionsSummary.totalRefunds)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Dari {transactionsSummary.refundCount} refund
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Biaya Layanan</CardTitle>
            <MinusCircle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(transactionsSummary.totalFees)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              3% dari total transaksi
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendapatan Bersih</CardTitle>
            <PlusCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(transactionsSummary.netRevenue)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Setelah biaya dan refund
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Transaction Analysis */}
      <Card className="hidden md:block">
        <CardHeader>
          <CardTitle>Analisis Transaksi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-8">
            <div>
              <h3 className="text-sm font-medium mb-4">Sumber Transaksi</h3>
              <div className="space-y-4">
                {transactionsSummary.topSources.map((source, index) => (
                  <div key={index}>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">{source.name}</span>
                      <span className="text-sm font-medium">{source.percentage}%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2.5">
                      <div 
                        className="bg-primary h-2.5 rounded-full" 
                        style={{ width: `${source.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium mb-4">Metode Pembayaran</h3>
              <div className="space-y-4">
                {transactionsSummary.paymentMethods.map((method, index) => (
                  <div key={index}>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">{method.name}</span>
                      <span className="text-sm font-medium">{method.percentage}%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2.5">
                      <div 
                        className="bg-primary h-2.5 rounded-full" 
                        style={{ width: `${method.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs and Filters */}
      <div className="flex flex-col gap-4">
        <Tabs defaultValue="all" value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="w-full md:w-auto">
            <TabsTrigger value="all">Semua Transaksi</TabsTrigger>
            <TabsTrigger value="payments">Pembayaran</TabsTrigger>
            <TabsTrigger value="refunds">Refund</TabsTrigger>
          </TabsList>
        </Tabs>
        
        <div className="flex flex-col md:flex-row gap-4 md:items-center">
          <div className="w-full md:w-[300px]">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input 
                placeholder="Cari transaksi..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-8"
              />
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-[150px]">
                <div className="flex items-center">
                  <Tag className="h-4 w-4 mr-2" />
                  <span>Tipe</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Tipe</SelectItem>
                <SelectItem value="payment">Pembayaran</SelectItem>
                <SelectItem value="refund">Refund</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[150px]">
                <div className="flex items-center">
                  <Filter className="h-4 w-4 mr-2" />
                  <span>Status</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Status</SelectItem>
                <SelectItem value="completed">Selesai</SelectItem>
                <SelectItem value="pending">Diproses</SelectItem>
                <SelectItem value="failed">Gagal</SelectItem>
                <SelectItem value="processing">Dalam Proses</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={filterSource} onValueChange={setFilterSource}>
              <SelectTrigger className="w-[180px]">
                <div className="flex items-center">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  <span>Sumber</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Sumber</SelectItem>
                {sourcesOptions.map((source, index) => (
                  <SelectItem key={index} value={source}>{source}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Dari:</span>
              </div>
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-auto"
              />
              <span className="text-sm text-muted-foreground">Sampai:</span>
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-auto"
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Transaction Results */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle>Daftar Transaksi</CardTitle>
            <div className="text-sm">
              Menampilkan <span className="font-medium">{filteredTransactions.length}</span> transaksi 
              (<span className="font-medium">{formatCurrency(filteredTotal)}</span>)
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="relative overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs uppercase bg-muted/50">
                <tr>
                  <th scope="col" className="px-6 py-3">ID</th>
                  <th scope="col" className="px-6 py-3">Tanggal</th>
                  <th scope="col" className="px-6 py-3">Tipe</th>
                  <th scope="col" className="px-6 py-3">Jumlah</th>
                  <th scope="col" className="px-6 py-3">Customer</th>
                  <th scope="col" className="px-6 py-3">Metode</th>
                  <th scope="col" className="px-6 py-3">Status</th>
                  <th scope="col" className="px-6 py-3">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {filteredTransactions.map(transaction => (
                  <tr key={transaction.id} className="border-b">
                    <td className="px-6 py-4 font-medium">
                      <Link href={`/tenant/dashboard/financial/transactions/${transaction.id}`} className="hover:underline">
                        {transaction.id}
                      </Link>
                      <div className="text-xs text-muted-foreground">{transaction.orderNumber}</div>
                    </td>
                    <td className="px-6 py-4">{formatDateTime(transaction.date)}</td>
                    <td className="px-6 py-4">
                      {transaction.type === "payment" ? (
                        <Badge className="bg-blue-100 text-blue-800">Pembayaran</Badge>
                      ) : (
                        <Badge className="bg-orange-100 text-orange-800">Refund</Badge>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className={`font-medium ${transaction.type === "payment" ? "text-green-600" : "text-red-600"}`}>
                        {transaction.type === "payment" ? "+" : "-"}{formatCurrency(transaction.amount)}
                      </div>
                      {transaction.fees > 0 && (
                        <div className="text-xs text-muted-foreground">
                          Biaya: {formatCurrency(transaction.fees)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div>{transaction.customerName}</div>
                      <div className="text-xs text-muted-foreground">{transaction.customerEmail}</div>
                    </td>
                    <td className="px-6 py-4">{transaction.paymentMethod}</td>
                    <td className="px-6 py-4">
                      {transaction.status === "completed" ? (
                        <Badge className="bg-green-100 text-green-800">Selesai</Badge>
                      ) : transaction.status === "pending" ? (
                        <Badge className="bg-yellow-100 text-yellow-800">Tertunda</Badge>
                      ) : transaction.status === "failed" ? (
                        <Badge className="bg-red-100 text-red-800">Gagal</Badge>
                      ) : (
                        <Badge className="bg-blue-100 text-blue-800">Diproses</Badge>
                      )}
                      {transaction.failReason && (
                        <div className="text-xs text-red-600 mt-1">{transaction.failReason}</div>
                      )}
                      {transaction.refundReason && (
                        <div className="text-xs text-orange-600 mt-1">{transaction.refundReason}</div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <span className="sr-only">Buka menu</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <FileText className="h-4 w-4 mr-2" />
                            Lihat Detail
                          </DropdownMenuItem>
                          {transaction.type === "payment" && transaction.status === "completed" && (
                            <DropdownMenuItem>
                              <ArrowDown className="h-4 w-4 mr-2" />
                              Proses Refund
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem>
                            <Calendar className="h-4 w-4 mr-2" />
                            Lihat Pesanan
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredTransactions.length === 0 && (
            <div className="p-8 text-center">
              <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">Tidak ada transaksi yang sesuai dengan filter</p>
              <Button onClick={() => {
                setSearchQuery("")
                setFilterStatus("all")
                setFilterType("all")
                setFilterSource("all")
                setStartDate(addDays(new Date(), -30).toISOString().substring(0, 10))
                setEndDate(new Date().toISOString().substring(0, 10))
              }} variant="link" className="mt-2">
                Hapus semua filter
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 