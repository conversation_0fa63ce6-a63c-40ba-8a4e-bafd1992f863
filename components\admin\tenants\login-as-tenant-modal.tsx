"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useRouter } from "next/navigation"
import { useNotifications } from "@/components/providers/notifications-provider"
import { LogIn, AlertCircle } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"

interface LoginAsTenantModalProps {
  tenant: {
    id: string
    name: string
    domain?: string
  }
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function LoginAsTenantModal({ tenant, open, onOpenChange }: LoginAsTenantModalProps) {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const [isLoading, setIsLoading] = useState(false)
  const [rememberSession, setRememberSession] = useState(false)

  const handleLogin = async () => {
    try {
      setIsLoading(true)

      // Simulasi proses login
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Simpan informasi impersonasi di localStorage atau sessionStorage
      const impersonationData = {
        isImpersonating: true,
        originalRole: "admin",
        impersonatedTenant: {
          id: tenant.id,
          name: tenant.name,
          domain: tenant.domain || `${tenant.name.toLowerCase().replace(/\s+/g, "-")}.sellzio.com`,
        },
        timestamp: new Date().toISOString(),
        expiresAt: rememberSession ? null : new Date(Date.now() + 3600000).toISOString(), // 1 jam jika tidak remember
      }

      const storageType = rememberSession ? localStorage : sessionStorage
      storageType.setItem("sellzio_impersonation", JSON.stringify(impersonationData))

      // Tampilkan notifikasi sukses
      addNotification({
        message: `Berhasil login sebagai tenant ${tenant.name}`,
        type: "success",
      })

      // Tutup modal
      onOpenChange(false)

      // Redirect ke dashboard tenant
      router.push(`/tenant/dashboard?impersonated=true&tenant=${tenant.id}`)
    } catch (error) {
      console.error("Error logging in as tenant:", error)
      addNotification({
        message: "Gagal login sebagai tenant. Silakan coba lagi.",
        type: "error",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Login sebagai Tenant</DialogTitle>
          <DialogDescription>
            Anda akan login sebagai tenant {tenant.name}. Tindakan ini akan dicatat dalam log sistem.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="rounded-md bg-yellow-50 p-4 mb-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-yellow-400" aria-hidden="true" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Perhatian</h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>
                    Saat Anda login sebagai tenant, Anda akan memiliki akses penuh ke akun tenant tersebut. Pastikan
                    untuk keluar dari sesi impersonasi setelah selesai.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="remember-session"
              checked={rememberSession}
              onCheckedChange={(checked) => setRememberSession(checked === true)}
            />
            <Label htmlFor="remember-session">Ingat sesi impersonasi (tidak akan kedaluwarsa)</Label>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            Batal
          </Button>
          <Button onClick={handleLogin} disabled={isLoading} className="gap-2">
            {isLoading ? (
              "Memproses..."
            ) : (
              <>
                <LogIn className="h-4 w-4" />
                Login sebagai {tenant.name}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
