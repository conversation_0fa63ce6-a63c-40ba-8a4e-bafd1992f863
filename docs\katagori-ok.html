<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <title>Shopee Categories</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto', Arial, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            padding: 0px;
        }
        
        .categories-section {
            position: relative;
            background-color: white;
            padding: 5px 10px;
            border-radius: 8px;
            border: 1px solid #ececec;
            box-shadow: 0 0px 0px rgba(0, 0, 0, 0.1);
            margin: 10px;
            max-width: 1200px;
            
        }
    
        
        /* Menghilangkan header kategori dan garis di bawahnya */
        .categories-header {
            display: none;
        }
        
        /* Memindahkan tombol ekspand ke bawah dan tengah */
        .expand-button-container {
            display: none !important; /* Sembunyikan tombol panah bawah */
        }
        
        .categories-container {
            display: flex;
            overflow-x: auto;
            gap: 6px;
            padding: 5px 0;
            padding-bottom: 15px;
            scrollbar-width: none;
            transition: opacity 0.6s ease; /* Menambahkan transisi untuk opasitas */
        }
        
        .categories-container::-webkit-scrollbar {
            display: none;
        }
        
        /* Mengubah kategori menjadi link pada seluruh card */
        .category-item {
    text-decoration: none;
    display: block;
    min-width: 65px;
    text-align: center;
    border-radius: 8px;
    padding: 6px 5px;
    padding-bottom: 1px;
    transition: background-color 0.2s;
    border: 1px solid #e0e0e0; /* Border tipis */
    cursor: pointer;
    background-color: #ffffff; /* Background putih */
    box-shadow: 0 1px 2px rgba(0,0,0,0.05); /* Sedikit bayangan */
}
        
        .category-item:hover {
            background-color: #f5f5f5;
        }
        
        .category-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
			color: #FF5722; /* Warna oranye default untuk semua ikon */
        }
        
        /* Memastikan tidak ada link di nama kategori */
        .category-name {
            font-size: 10px;
            color: #222;
            margin-top: 8px;
            text-decoration: none;
        }
        
        /* Gaya khusus untuk card "Lihat Semua" */
        .see-all-item {
            text-decoration: none;
            display: block;
            min-width: 70px;
            text-align: center;
            border-radius: 8px;
            padding: 8px 5px;
            padding-bottom: 10px;
            transition: background-color 0.2s;
            border: 1px solid #f0f0f0;
            background-color: #FFF5F0;
            cursor: pointer;
        }
        
        .see-all-item:hover {
            background-color: #FFE8E0;
        }
        
        .close-all-item {
            text-decoration: none;
            display: block;
            min-width: 70px;
            text-align: center;
            border-radius: 8px;
            padding: 8px 5px;
            padding-bottom: 10px;
            transition: background-color 0.2s;
            border: 1px solid #f0f0f0;
            background-color: #F5F5F5;
            cursor: pointer;
        }
        
        .close-all-item:hover {
            background-color: #E0E0E0;
        }
        
        .expanded-categories {
            height: 0;
            overflow: hidden;
            transition: height 0.3s ease;
        }
        
        .expanded-categories.active {
            overflow: visible;
        }
        
        .expanded-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            padding: 5px 10px 10px 10px;
            padding-bottom: 15px;
        }
        
        .expanded-item {
            text-decoration: none;
            display: block;
            text-align: center;
            border-radius: 8px;
            padding: 8px 5px;
            padding-bottom: 12px;
            transition: background-color 0.2s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
            cursor: pointer;
        }
        
        .expanded-item:hover {
            background-color: #f5f5f5;
        }
        
        .expanded-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        /* Memastikan tidak ada gaya khusus pada nama kategori expanded */
        .expanded-name {
            font-size: 12px;
            color: #222;
            margin-top: 8px;
            text-decoration: none;
        }
        
        /* Animation untuk transisi icon */
        @keyframes fadeRotate {
            0% { opacity: 0; transform: rotate(0deg); }
            100% { opacity: 1; transform: rotate(360deg); }
        }
        
        @keyframes fadeRotateOut {
            0% { opacity: 1; transform: rotate(0deg); }
            100% { opacity: 0; transform: rotate(-90deg); }
        }
        
        .animate-in {
            animation: fadeRotate 0.5s forwards;
        }
        
        .animate-out {
            animation: fadeRotateOut 0.3s forwards;
        }
        
        /* Styles for desktop */
        @media (min-width: 768px) {
            .body {
                padding: 20px;
            }
            
            .categories-section {
                padding: 15px;
                padding-bottom: 15px;
            }
            
            .desktop-wrapper {
                display: block;
            }
            
            .categories-container {
                display: none; /* Hide mobile category list on desktop */
            }
            
            .expand-button-container {
                display: none !important; /* Hide expand button on desktop */
            }
            
            .expanded-categories {
                height: auto !important;
                overflow: visible;
            }
            
            .expanded-grid {
                display: grid;
                grid-template-columns: repeat(7, 1fr);
                gap: 15px;
                padding: 10px;
            }
            
            .desktop-grid {
                display: grid;
                grid-template-columns: repeat(7, 1fr);
                gap: 15px;
                padding: 10px;
            }
            
            .extra-categories {
                height: 0;
                overflow: hidden;
                transition: height 0.3s ease;
            }
            
            .extra-categories.active {
                height: auto !important;
                overflow: visible;
            }
            
            .expanded-item {
                padding: 12px 10px;
                transition: transform 0.2s, box-shadow 0.2s;
            }
            
            .expanded-item:hover {
                transform: translateY(-5px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }
            
            .expanded-name {
                font-size: 13px;
                font-weight: 500;
                margin-top: 10px;
            }
            
            /* Menghapus header desktop */
            .desktop-header {
                display: none !important;
            }
            
            /* Sembunyikan tombol tutup di desktop */
            .close-button-container {
                display: none !important;
            }
            
            /* Sembunyikan tombol View All beranimasi di desktop */
            .floating-arrow {
                display: none !important;
            }
            
            .categories-container-wrapper::after {
                display: none !important;
            }
        }
        
        /* Tambahan CSS untuk mengecilkan card kategori di tampilan mobile horizontal */
        @media (max-width: 767px) {
            /* Mengecilkan kategori dalam tampilan horizontal saja */
            .categories-container .category-item {
                min-width: 55px; /* Mengecilkan lebar minimum */
                padding: 4px 3px; /* Mengecilkan padding */
                padding-bottom: 0px; /* Hapus padding bawah */
                border-width: 1px; /* Mengecilkan border */
            }
            
            .categories-container .category-icon svg {
                width: 24px; /* Mengecilkan ukuran icon */
                height: 24px;
            }
            
            .categories-container .category-name {
                font-size: 9px; /* Mengecilkan font */
                margin-top: 4px; /* Mengurangi margin */
            }
            
            /* Mengecilkan card Lihat Semua */
            .categories-container .see-all-item {
                min-width: 60px;
                padding: 4px 3px;
                padding-bottom: 6px;
            }
            
            .categories-container .see-all-item .category-icon svg {
                width: 28px;
                height: 28px;
            }
            
            /* Mengurangi gap antar item */
            .categories-container {
                gap: 10px;
                padding-bottom: 10px;
            }
        }
        
        /* CSS untuk tombol panah atas (tombol tutup) */
        .close-button-container {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-top: 10px;
            margin-bottom: 10px;
        }
        
        .close-button {
            background: #ff5722;
            border: none;
            cursor: pointer;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            transform: rotate(180deg); /* Arahkan panah ke atas */
        }
        
        .close-button:hover {
            background: #e64a19;
        }
        
        /* CSS untuk icon floating berbentuk panah dengan teks */
        .floating-arrow {
            position: absolute;
            right: 5px;
            top: 45%;
            transform: translateY(-50%);
            height: 36px;
            background-color: rgba(255, 245, 240, 0.95);
            border: 1px solid #FFDFD1;
            border-radius: 18px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            padding: 0 15px 0 12px;
            cursor: pointer;
            z-index: 10;
            transition: opacity 0.8s ease, transform 0.8s ease; /* Memperlambat transisi */
            opacity: 1;
        }
        
        .floating-arrow.hidden {
            opacity: 0;
            transform: translateY(-50%) translateX(10px);
            pointer-events: none;
        }
        
        .floating-arrow:active {
            transform: translateY(-50%) scale(0.97);
        }
        
        .floating-arrow-text {
            font-size: 12px;
            font-weight: 600;
            color: #FF5722;
            margin-right: 6px;
        }
        
        .floating-arrow-icon {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Animasi panah bergerak */
        @keyframes arrowMove {
            0% { transform: translateX(0); }
            50% { transform: translateX(3px); }
            100% { transform: translateX(0); }
        }
        
        .floating-arrow-icon svg {
            width: 15px;
            height: 15px;
            color: #FF5722;
            animation: arrowMove 1.5s infinite ease-in-out;
        }
        
        /* Gradient untuk efek fade di sisi kanan */
        .categories-container-wrapper::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50px;
            height: 100%;
            background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9));
            pointer-events: none;
        }
        
        /* Tambahkan style untuk container yang memiliki posisi relative */
        .categories-container-wrapper {
            position: relative;
            overflow: hidden;
        }

        /* Tambahan CSS untuk tampilan subkategori */
        .subcategory-view {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 1000;
            display: none;
            flex-direction: column;
            overflow: hidden;
        }

        .subcategory-header {
            background-color: white;
            padding: 15px;
            display: flex;
            align-items: center;
            box-shadow: none;
            z-index: 10;
        }

        .back-button {
            color: #ee4d2d; ;
            font-size: 20px;
            margin-right: 15px;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            cursor: pointer;
        }
		
		.back-button i {
    transform: scaleX(1.3); /* Membuat panah sedikit lebih panjang secara horizontal */
}



        .subcategory-header h1 {
            font-size: 18px;
            font-weight: 500;
        }

        .subcategory-container {
            display: flex;
            height: calc(100% - 70px);
            overflow: hidden;
			position: relative;
        }

        .category-sidebar {
            border-top: 1px solid #eee;
            width: 100px;
            background-color: #f9f9f9;
            overflow-y: auto;
            flex-shrink: 0;
			box-shadow: 3px 0 5px -2px rgba(0, 0, 0, 0.1); /* Menambahkan bayangan pada sisi kanan sidebar */
    height: 100%; /* Memastikan sidebar mengisi seluruh tinggi */
    position: relative; /* Diperlukan untuk memastikan bayangan muncul dengan benar */
    z-index: 1; /* Memastikan bayangan muncul di atas konten sebelahnya */
        }

        .category-sidebar::-webkit-scrollbar {
            display: none;
        }

        .sidebar-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 8px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            text-align: center;
        }

        .sidebar-item.active {
            background-color: white;
        }

        .sidebar-icon {
            color: #FF5722; /* Warna oranye default */
            margin-bottom: 6px;
        }

        .sidebar-item.active .sidebar-icon {
            color: #FF5722;
        }

        .sidebar-text {
            font-size: 11px;
            color: #555;
            text-align: center;
        }

        .sidebar-item.active .sidebar-text {
            color: #333;
            font-weight: 500;
        }

        .subcategory-content {
            flex-grow: 1;
            padding: 15px;
            background-color: white;
            overflow-y: auto;
			margin-left: 1px; 
        }

        .subcategory-content::-webkit-scrollbar {
            display: none;
        }

        .subcategory-banner {
            width: 100%;
            margin-bottom: 15px;
            border-radius: 8px;
            overflow: hidden;
        }

        .subcategory-banner img {
            width: 100%;
            height: auto;
            display: block;
        }

        .subcategory-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .subcategory-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; /* Tambahkan ini untuk perataan vertikal */
    text-align: center;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    height: 100px; /* Atur tinggi yang konsisten */
    width: 100%; /* Isi seluruh ruang dalam grid */
}

.subcategory-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.subcategory-title {
    font-size: 12px;
    font-weight: normal;
    color: #333;
    text-align: center;
    line-height: 1.3;
    width: 100%; /* Isi seluruh lebar */
    /* Tambahkan ini untuk membatasi teks yang terlalu panjang */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* Batasi ke 2 baris */
    -webkit-box-orient: vertical;
}

        @media (min-width: 768px) {
            .category-sidebar {
                width: 150px;
            }
            
            .sidebar-item {
                padding: 15px 10px;
            }
            
            .sidebar-text {
                font-size: 12px;
            }
            
            .subcategory-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            .subcategory-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
		
		
		.subcategory-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: white;
    z-index: 1000;
    display: none;
    flex-direction: column;
    overflow: hidden;
    max-width: 1200px; /* Sesuaikan dengan max-width categories-section */
    margin-left: auto;
    margin-right: auto;
    left: 0;
    right: 0;
}

@media (min-width: 768px) {
    .desktop-grid {
        display: grid;
		 grid-template-columns: repeat(7, 1fr) !important;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 12px;
    }
    
    .desktop-grid .expanded-item {
        height: 80px;
        width: 100%;
        border: 1px solid #f0f0f0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
}


.category-icon svg,
.expanded-icon svg,

.subcategory-icon svg {
    color: #FF5722 !important; /* Pastikan warna oranye diterapkan pada semua ikon SVG */
}

@media (min-width: 768px) {
    .subcategory-view {
     
    max-width: 900px;
  }
}

@media (min-width: 1200px) {
    .subcategory-view {
     
    max-width: 700px;
  }
}

    </style>
</head>
<body>
    <div class="categories-section">
        <!-- Mobile view -->
        <div id="categoryContainer" class="categories-container">
            <!-- Kategori akan diisi melalui JavaScript -->
        </div>
        
        <!-- Desktop grid view for first 13 items + "Lihat Semua" button -->
        <div id="desktopGrid" class="desktop-grid">
            <!-- Desktop grid akan diisi melalui JavaScript -->
        </div>
        
        <!-- Extra categories that show after clicking "Lihat Semua" in desktop -->
        <div id="extraCategories" class="extra-categories">
            <!-- Extra categories akan diisi melalui JavaScript -->
        </div>
        
        <!-- Expanded Categories (for mobile) -->
        <div id="expandedCategories" class="expanded-categories">
            <!-- Kategori yang diperluas untuk mobile akan diisi melalui JavaScript -->
        </div>
    </div>

    <!-- Tampilan Subkategori -->
    <div id="subcategoryView" class="subcategory-view">
        <div class="subcategory-header">
            <!-- Ganti definisi back-button -->
<div id="backButton" class="back-button"><i class="fa fa-arrow-left"></i></div>
            <h1>Kategori</h1>
        </div>
        
        <div class="subcategory-container">
            <div id="categorySidebar" class="category-sidebar">
                <!-- Sidebar kategori akan diisi oleh JavaScript -->
            </div>
            
            <div id="subcategoryContent" class="subcategory-content">
                <!-- Konten subkategori akan diisi oleh JavaScript -->
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('categoryContainer');
            const expandedCategories = document.getElementById('expandedCategories');
            const desktopGrid = document.getElementById('desktopGrid');
            const extraCategories = document.getElementById('extraCategories');
            let isExpanded = false;
            let isDesktopExpanded = false;
            
            // All categories data with updated links
            const allCategories = [
                { 
                    name: 'Elektronik', 
                    icon: 'device-mobile', 
                    color: '#1BA0E2', 
                    link: 'https://facebook.com',
                    subkategori: [
                        { title: 'Konsol Game', icon: 'gamepad' },
                        { title: 'Aksesoris Konsol', icon: 'controller' },
                        { title: 'Alat Casting', icon: 'cast' },
                        { title: 'Foot Bath & Spa', icon: 'droplet' },
                        { title: 'Mesin Jahit & Aksesoris', icon: 'scissors' },
                        { title: 'Setrika & Mesin Uap', icon: 'iron' },
                        { title: 'Purifier & Humidifier', icon: 'wind' },
                        { title: 'Penyedot Debu', icon: 'vacuum' },
                        { title: 'Telepon', icon: 'phone' },
                        { title: 'Mesin Cuci & Pengering', icon: 'washer' },
                        { title: 'Water Heater', icon: 'thermometer' },
                        { title: 'Pendingin Ruangan', icon: 'snowflake' }
                    ]
                },
                { 
                    name: 'Makanan', 
                    icon: 'utensils', 
                    color: '#F25C05', 
                    link: 'https://instagram.com',
                    subkategori: [
                        { title: 'Makanan Ringan', icon: 'cookie' },
                        { title: 'Minuman', icon: 'coffee' },
                        { title: 'Kue & Biskuit', icon: 'cake' },
                        { title: 'Bahan Masakan', icon: 'package' }
                    ]
                },
                { 
                    name: 'Belanja', 
                    icon: 'shopping-cart', 
                    color: '#E74C3C', 
                    link: 'https://twitter.com',
                    subkategori: [
                        { title: 'Belanja Bulanan', icon: 'shopping-bag' },
                        { title: 'Kebutuhan Harian', icon: 'shopping-cart' },
                        { title: 'Promo Spesial', icon: 'tag' }
                    ]
                },
                { 
                    name: 'Game', 
                    icon: 'gamepad', 
                    color: '#3498DB', 
                    link: 'https://linkedin.com',
                    subkategori: [
                        { title: 'Game Mobile', icon: 'smartphone' },
                        { title: 'Game PC', icon: 'monitor' },
                        { title: 'Aksesori Gaming', icon: 'headphones' }
                    ]
                },
                { 
                    name: 'Lokal', 
                    icon: 'map-pin', 
                    color: '#9B59B6', 
                    link: 'https://youtube.com',
                    subkategori: [
                        { title: 'Produk UMKM', icon: 'home' },
                        { title: 'Kerajinan Lokal', icon: 'tool' },
                        { title: 'Makanan Khas', icon: 'utensils' }
                    ]
                },
                { 
                    name: 'Fashion', 
                    icon: 'shirt', 
                    color: '#F1C40F', 
                    link: 'https://tiktok.com',
                    subkategori: [
                        { title: 'Pakaian Pria', icon: 'user' },
                        { title: 'Pakaian Wanita', icon: 'user-female' },
                        { title: 'Sepatu', icon: 'boot' },
                        { title: 'Tas', icon: 'briefcase' }
                    ]
                },
                { 
                    name: 'Kecantikan', 
                    icon: 'list', 
                    color: '#E84393', 
                    link: 'https://pinterest.com',
                    subkategori: [
                        { title: 'Skincare', icon: 'droplet' },
                        { title: 'Makeup', icon: 'smile' },
                        { title: 'Perawatan Rambut', icon: 'scissors' }
                    ]
                },
                { 
                    name: 'Otomotif', 
                    icon: 'car', 
                    color: '#2C3E50', 
                    link: 'https://whatsapp.com',
                    subkategori: [
                        { title: 'Aksesoris Mobil', icon: 'car' },
                        { title: 'Aksesoris Motor', icon: 'activity' },
                        { title: 'Perawatan Kendaraan', icon: 'tool' }
                    ]
                },
                { 
                    name: 'Komputer', 
                    icon: 'monitor', 
                    color: '#5D6D7E', 
                    link: 'https://telegram.org',
                    subkategori: [
                        { title: 'Laptop', icon: 'laptop' },
                        { title: 'PC Desktop', icon: 'monitor' },
                        { title: 'Aksesoris Komputer', icon: 'keyboard' }
                    ]
                },
                { 
                    name: 'Hobi', 
                    icon: 'camera', 
                    color: '#1ABC9C', 
                    link: 'https://reddit.com',
                    subkategori: [
                        { title: 'Fotografi', icon: 'camera' },
                        { title: 'Musik', icon: 'music' },
                        { title: 'Koleksi', icon: 'archive' }
                    ]
                },
                { 
                    name: 'Rumah', 
                    icon: 'home', 
                    color: '#8E44AD', 
                    link: 'https://shopee.co.id',
                    subkategori: [
                        { title: 'Furniture', icon: 'inbox' },
                        { title: 'Dekorasi', icon: 'image' },
                        { title: 'Perlengkapan Dapur', icon: 'coffee' }
                    ]
                },
                { 
                    name: 'Kesehatan', 
                    icon: 'activity', 
                    color: '#2ECC71', 
                    link: 'https://tokopedia.com',
                    subkategori: [
                        { title: 'Obat-obatan', icon: 'thermometer' },
                        { title: 'Suplemen', icon: 'package' },
                        { title: 'Alat Kesehatan', icon: 'activity' }
                    ]
                },
                { 
                    name: 'Olahraga', 
                    icon: 'activity', 
                    color: '#E67E22', 
                    link: 'https://bukalapak.com',
                    subkategori: [
                        { title: 'Pakaian Olahraga', icon: 'shirt' },
                        { title: 'Alat Olahraga', icon: 'dumbbell' },
                        { title: 'Sepatu Olahraga', icon: 'boot' }
                    ]
                },
                { 
                    name: 'Mainan', 
                    icon: 'gift', 
                    color: '#FF6B81', 
                    link: 'https://github.com',
                    subkategori: [
                        { title: 'Mainan Anak', icon: 'truck' },
                        { title: 'Mainan Edukatif', icon: 'book' },
                        { title: 'Action Figure', icon: 'star' }
                    ]
                },
                { 
                    name: 'Bayi', 
                    icon: 'smile', 
                    color: '#FDA7DF', 
                    link: 'https://stackoverflow.com',
                    subkategori: [
                        { title: 'Pakaian Bayi', icon: 'shirt' },
                        { title: 'Perlengkapan Bayi', icon: 'package' },
                        { title: 'Makanan Bayi', icon: 'coffee' }
                    ]
                },
                { 
                    name: 'Pendidikan', 
                    icon: 'book', 
                    color: '#4834DF', 
                    link: 'https://medium.com',
                    subkategori: [
                        { title: 'Buku', icon: 'book' },
                        { title: 'Alat Tulis', icon: 'edit' },
                        { title: 'Kursus Online', icon: 'monitor' }
                    ]
                }
            ];
            // Function to create icon SVG
            function getIconSVG(iconName) {
                switch(iconName) {
                    case 'device-mobile':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><path d="M8 6h.01M16 6h.01M12 6h.01M8 10h.01M16 10h.01M12 10h.01M8 14h.01M16 14h.01M12 14h.01M8 18h.01M16 18h.01M12 18h.01"></path></svg>';
                    case 'utensils':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path><path d="M7 2v20"></path><path d="M21 15V2"></path><path d="M18 15V2"></path><path d="M15 15V2"></path><path d="M21 15a3 3 0 1 1-6 0"></path></svg>';
                    case 'shopping-cart':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>';
                    case 'gamepad':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="6" width="20" height="12" rx="2"></rect><path d="M12 12h.01"></path><path d="M17 10V8"></path><path d="M19 12h.01"></path><path d="M17 14v2"></path><path d="M7 12h.01"></path><path d="M5 10v2"></path><path d="M5 14v-2"></path></svg>';
                    case 'map-pin':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>';
                    case 'shirt':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z"></path></svg>';
                    case 'list':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M7 21a4 4 0 0 1-4-4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v12a4 4 0 0 1-4 4H7z"></path><line x1="12" y1="7" x2="20" y2="7"></line><line x1="12" y1="11" x2="20" y2="11"></line><line x1="12" y1="15" x2="20" y2="15"></line><line x1="8" y1="7" x2="8" y2="7.01"></line><line x1="8" y1="11" x2="8" y2="11.01"></line><line x1="8" y1="15" x2="8" y2="15.01"></line></svg>';
                    case 'car':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="1" y="6" width="22" height="12" rx="6"></rect><circle cx="7" cy="12" r="3"></circle><circle cx="17" cy="12" r="3"></circle></svg>';
                    case 'monitor':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect><line x1="8" y1="21" x2="16" y2="21"></line><line x1="12" y1="17" x2="12" y2="21"></line></svg>';
                    case 'camera':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path><circle cx="12" cy="13" r="3"></circle></svg>';
                    case 'home':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>';
                    case 'activity':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline></svg>';
                    case 'gift':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 12v10H4V12"></path><path d="M2 7h20v5H2z"></path><path d="M12 22V7"></path><path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"></path><path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"></path></svg>';
                    case 'smile':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M8 14s1.5 2 4 2 4-2 4-2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line></svg>';
                    case 'book':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path></svg>';
                    default:
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>';
                }
            }

            // Function untuk mendapatkan ikon SVG subkategori berdasarkan nama ikon
            function getSubCategoryIconSVG(iconName) {
                switch(iconName) {
                    case 'gamepad':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="6" width="20" height="12" rx="2"></rect><path d="M12 12h.01"></path><path d="M17 10V8"></path><path d="M19 12h.01"></path><path d="M17 14v2"></path><path d="M7 12h.01"></path><path d="M5 10v2"></path><path d="M5 14v-2"></path></svg>';
                        
                    case 'controller':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="6" y1="12" x2="10" y2="12"></line><line x1="8" y1="10" x2="8" y2="14"></line><circle cx="15" cy="13" r="1"></circle><circle cx="18" cy="11" r="1"></circle><rect x="2" y="6" width="20" height="12" rx="2"></rect></svg>';
                        
                    case 'cast':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6"></path><line x1="2" y1="20" x2="2.01" y2="20"></line></svg>';
                        
                    case 'droplet':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path></svg>';
                        
                    case 'scissors':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="6" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><line x1="20" y1="4" x2="8.12" y2="15.88"></line><line x1="14.47" y1="14.48" x2="20" y2="20"></line><line x1="8.12" y1="8.12" x2="12" y2="12"></line></svg>';
                        
                    case 'iron':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zM4 16v-6c0-1.1.9-2 2-2h12c1.1 0 2 .9 2 2v6c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2z"></path><path d="M15 8v3"></path><path d="M17 10h-4"></path></svg>';
                        
                    case 'wind':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2"></path></svg>';
                        
                    case 'vacuum':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 6h5a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1h-5"></path><circle cx="8" cy="10" r="6"></circle><path d="M12 10a2 2 0 0 0-2-2"></path><path d="M8 16v3"></path></svg>';
                        
                    case 'phone':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72c.127.96.362 1.903.7 2.81a2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45c.907.338 1.85.573 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>';
                        
                    case 'washer':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="4" y="2" width="16" height="20" rx="2"></rect><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle><line x1="12" y1="6" x2="12" y2="6.01"></line><line x1="16" y1="12" x2="16" y2="12.01"></line><line x1="8" y1="12" x2="8" y2="12.01"></line><line x1="12" y1="18" x2="12" y2="18.01"></line></svg>';
                        
                    case 'thermometer':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"></path></svg>';
                        
                    case 'snowflake':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="2" y1="12" x2="22" y2="12"></line><line x1="12" y1="2" x2="12" y2="22"></line><path d="M20 16l-4-4 4-4"></path><path d="M4 8l4 4-4 4"></path><path d="M16 4l-4 4-4-4"></path><path d="M8 20l4-4 4 4"></path></svg>';
                    
                    case 'cookie':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><circle cx="8" cy="8" r="1"></circle><circle cx="16" cy="8" r="1"></circle><circle cx="12" cy="12" r="1"></circle><circle cx="16" cy="16" r="1"></circle><circle cx="8" cy="16" r="1"></circle></svg>';
                    
                    case 'coffee':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8h1a4 4 0 0 1 0 8h-1"></path><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path><line x1="6" y1="1" x2="6" y2="4"></line><line x1="10" y1="1" x2="10" y2="4"></line><line x1="14" y1="1" x2="14" y2="4"></line></svg>';
                    
                    case 'cake':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v8"></path><path d="M4 16s.5-1 2-1 2.5 2 4 2 2.5-2 4-2 2.5 2 4 2 2-1 2-1"></path><path d="M2 21h20"></path><path d="M7 8V7c0-2 2-3 5-3s5 1 5 3v1"></path><path d="M7 11v-1"></path><path d="M17 11v-1"></path></svg>';
                    
                    case 'package':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="16.5" y1="9.4" x2="7.5" y2="4.21"></line><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>';
                    
                    case 'shopping-bag':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path></svg>';
                    
                    case 'tag':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path><line x1="7" y1="7" x2="7.01" y2="7"></line></svg>';
                    
                    case 'smartphone':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line></svg>';
                    
                    case 'headphones':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 18v-6a9 9 0 0 1 18 0v6"></path><path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"></path></svg>';
                    
                    case 'tool':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path></svg>';
                    
                    case 'user':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>';
                    
                    case 'user-female':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle><path d="M12 11v4"></path><path d="M9 15h6"></path></svg>';
                    
                    case 'boot':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 11V4h16v7"></path><path d="M4 11a4 4 0 0 0 4 4h12"></path><path d="M4 11V4h16v7"></path><path d="M8 15v3h12v-3"></path><path d="M12 15v3"></path></svg>';
                    
                    case 'briefcase':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path></svg>';
                    
                    case 'laptop':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16"></path></svg>';
                    
                    case 'keyboard':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="4" width="20" height="16" rx="2" ry="2"></rect><path d="M6 8h.01"></path><path d="M10 8h.01"></path><path d="M14 8h.01"></path><path d="M18 8h.01"></path><path d="M8 12h.01"></path><path d="M12 12h.01"></path><path d="M16 12h.01"></path><path d="M7 16h10"></path></svg>';
                    
                    case 'music':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18V5l12-2v13"></path><circle cx="6" cy="18" r="3"></circle><circle cx="18" cy="16" r="3"></circle></svg>';
                    
                    case 'archive':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="21 8 21 21 3 21 3 8"></polyline><rect x="1" y="3" width="22" height="5"></rect><line x1="10" y1="12" x2="14" y2="12"></line></svg>';
                    
                    case 'inbox':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 12 16 12 14 15 10 15 8 12 2 12"></polyline><path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path></svg>';
                    
                    case 'image':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>';
                    
                    case 'dumbbell':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 5h12l3 5-3 5H6l-3-5 3-5Z"></path><path d="M7 15a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"></path><path d="M17 15a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"></path></svg>';
                    
                    case 'truck':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 3h15v13H1z"></path><path d="M16 8h4l3 3v5h-7V8z"></path><circle cx="5.5" cy="18.5" r="2.5"></circle><circle cx="18.5" cy="18.5" r="2.5"></circle></svg>';
                    
                    case 'star':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>';
                    
                    case 'edit':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>';
                        
                    default:
                        // Jika ikon tidak ditemukan, gunakan ikon dari kategori utama
                        return getIconSVG('device-mobile');
                }
            }
            
            // Function untuk icon "Lihat Semua"
            function getSeeAllIconSVG() {
                return `
                <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 40 40">
                    <rect x="2" y="2" width="36" height="36" rx="10" fill="#FFF5F0" stroke="#FFDFD1" stroke-width="2"/>
                    <rect x="6" y="6" width="12" height="12" rx="3" fill="#FF5722"/>
                    <rect x="22" y="6" width="12" height="12" rx="3" fill="#FF5722"/>
                    <rect x="6" y="22" width="12" height="12" rx="3" fill="#FF5722"/>
                    <rect x="22" y="22" width="12" height="12" rx="3" fill="#FF5722"/>
                </svg>`;
            }
            
            // Function untuk icon "Tutup Semua"
            function getCloseAllIconSVG() {
                return `
                <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 40 40">
                    <rect x="2" y="2" width="36" height="36" rx="10" fill="#F5F5F5" stroke="#E0E0E0" stroke-width="2"/>
                    <path d="M13 13L27 27M13 27L27 13" stroke="#555" stroke-width="3" stroke-linecap="round"/>
                </svg>`;
            }
            
            // Fungsi untuk icon arrow SVG
            function getArrowRightSVG() {
                return `
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M5 12h14"></path>
                    <path d="M12 5l7 7-7 7"></path>
                </svg>`;
            }
            
            // Fungsi untuk membuat tombol tutup
            function createCloseButton() {
                // Buat container untuk tombol tutup
                const closeButtonContainer = document.createElement('div');
                closeButtonContainer.className = 'close-button-container';
                closeButtonContainer.style.display = 'none'; // Sembunyikan dulu
                
                // Buat tombol tutup
                const closeButton = document.createElement('button');
                closeButton.className = 'close-button';
                closeButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                `;
                
                // Tambahkan event listener untuk menutup kategori
                closeButton.addEventListener('click', function() {
                    toggleExpandedCategories();
                });
                
                // Tambahkan tombol ke container
                closeButtonContainer.appendChild(closeButton);
                
                // Tambahkan container ke DOM
                const expandedCategoriesEl = document.getElementById('expandedCategories');
                expandedCategoriesEl.parentNode.insertBefore(closeButtonContainer, expandedCategoriesEl.nextSibling);
                
                return closeButtonContainer;
            }
            
            // Modifikasi pada HTML untuk wrapper
            function setupCategoryContainer() {
                // Ambil container
                const container = document.getElementById('categoryContainer');
                
                // Buat wrapper baru
                const wrapper = document.createElement('div');
                wrapper.className = 'categories-container-wrapper';
                
                // Pindahkan container ke dalam wrapper
                container.parentNode.insertBefore(wrapper, container);
                wrapper.appendChild(container);
                
                // Buat floating button di dalam wrapper
                if (window.innerWidth < 768) {
                    createFloatingArrow(wrapper);
                }
            }
            
           
            // Fungsi untuk membuka tampilan subkategori
function openSubcategoryView(category, resetOrder = true) {
    const subcategoryView = document.getElementById('subcategoryView');
    const categorySidebar = document.getElementById('categorySidebar');
    const subcategoryContent = document.getElementById('subcategoryContent');
    
    // Hapus pengambilan referensi ke categories-section
    
    // Ubah ikon back button
    document.querySelector('.back-button').innerHTML = '<i class="fa fa-arrow-left"></i>';
    
    // Hapus pengaturan maxWidth berdasarkan categories-section
    // subcategoryView menggunakan maxWidth dari CSS-nya sendiri
    
    // Tampilkan tampilan subkategori
    subcategoryView.style.display = 'flex';
            
            
    // Tampilkan tampilan subkategori
    subcategoryView.style.display = 'flex';
    // Hanya atur ulang sidebar jika parameter resetOrder true
    if (resetOrder) {
        // Buat array kategori baru dengan kategori yang dipilih di atas
        let sortedCategories = [...allCategories];
        
        // Hapus kategori yang dipilih dari array
        sortedCategories = sortedCategories.filter(cat => cat.name !== category.name);
        
        // Tambahkan kategori yang dipilih di awal array
        sortedCategories.unshift(category);
        
        // Reset sidebar dan isi dengan kategori yang telah diurutkan
        categorySidebar.innerHTML = '';
        // Di dalam fungsi openSubcategoryView(), pada bagian yang membuat sidebar items:
sortedCategories.forEach(cat => {
    const sidebarItem = document.createElement('div');
    sidebarItem.className = "sidebar-item" + (cat.name === category.name ? " active" : "");
    sidebarItem.innerHTML = `
        <div class="sidebar-icon" style="color: ${cat.name === category.name ? '#FF5722' : '#888'}">${getIconSVG(cat.icon)}</div>
        <div class="sidebar-text">${cat.name}</div>
    `;
    
    // Tambahkan event listener untuk beralih kategori
    sidebarItem.addEventListener('click', function() {
        // Hapus kelas active dari semua item
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.classList.remove('active');
            // Reset warna semua ikon ke abu-abu
            item.querySelector('.sidebar-icon').style.color = '#888';
        });
        
        // Tambahkan kelas active ke item yang diklik
        sidebarItem.classList.add('active');
        // Set warna ikon ke oranye
        sidebarItem.querySelector('.sidebar-icon').style.color = '#FF5722';
        
        // Update konten subkategori tanpa mengubah urutan sidebar
        updateSubcategoryContent(cat);
    });
    
 
            
            categorySidebar.appendChild(sidebarItem);
        });
    } else {
        // Hanya update status active pada sidebar yang sudah ada
        document.querySelectorAll('.sidebar-item').forEach(item => {
            const itemName = item.querySelector('.sidebar-text').textContent;
            if (itemName === category.name) {
                item.classList.add('active');
                item.querySelector('.sidebar-icon').style.color = category.color;
            } else {
                item.classList.remove('active');
                item.querySelector('.sidebar-icon').style.color = '';
            }
        });
    }
    
    // Isi konten subkategori dengan kategori yang dipilih
    updateSubcategoryContent(category);
    
    // Tambahkan event listener pada tombol kembali jika belum ada
    const backButton = document.getElementById('backButton');
    if (backButton) {
        backButton.removeEventListener('click', closeSubcategoryView);
        backButton.addEventListener('click', closeSubcategoryView);
    }
    
    // Disable scroll pada body
    document.body.style.overflow = 'hidden';
}

            // Fungsi untuk memperbarui konten subkategori
            function updateSubcategoryContent(category) {
                const subcategoryContent = document.getElementById('subcategoryContent');
                
                // Banner kategori
                let contentHTML = `
                    <div class="subcategory-banner">
                        <img src="/api/placeholder/800/200" alt="${category.name} Banner">
                    </div>
                `;
                
                // Grid subkategori
                contentHTML += '<div class="subcategory-grid">';
                
                // Isi dengan subkategori dari kategori yang dipilih
                if (category.subkategori && category.subkategori.length > 0) {
        category.subkategori.forEach(subcat => {
            contentHTML += `
                <div class="subcategory-item">
                    <div class="subcategory-icon" style="color: #FF5722">
                        ${getSubCategoryIconSVG(subcat.icon)}
                    </div>
                    <div class="subcategory-title">${subcat.title}</div>
                </div>
                        `;
                    });
                } else {
                    // Jika tidak ada subkategori, tampilkan pesan
                    contentHTML += `
                        <div style="grid-column: 1 / -1; text-align: center; padding: 20px;">
                            Belum ada subkategori untuk kategori ini.
                        </div>
                    `;
                }
                
                contentHTML += '</div>';
                subcategoryContent.innerHTML = contentHTML;
                
                // Tambahkan event listener untuk item subkategori
                document.querySelectorAll('.subcategory-item').forEach(item => {
                    item.addEventListener('click', function() {
                        // Di sini Anda bisa menambahkan kode untuk navigasi ke halaman produk subkategori
                        alert('Navigasi ke subkategori: ' + item.querySelector('.subcategory-title').textContent);
                    });
                });
            }

            // Fungsi untuk menutup tampilan subkategori
            function closeSubcategoryView() {
                const subcategoryView = document.getElementById('subcategoryView');
                subcategoryView.style.display = 'none';
                
                // Enable kembali scroll pada body
                document.body.style.overflow = '';
            }
            
            // Fungsi untuk membuat floating arrow dengan timer
            function createFloatingArrow(wrapper) {
                const floatingArrow = document.createElement('div');
                floatingArrow.className = 'floating-arrow hidden'; // Mulai dengan status hidden
                floatingArrow.innerHTML = `
                    <span class="floating-arrow-text">View All</span>
                    <div class="floating-arrow-icon">${getArrowRightSVG()}</div>
                `;
                
                // Tambahkan event listener untuk klik
                floatingArrow.addEventListener('click', toggleExpandedCategories);
                
                // Tambahkan ke wrapper
                wrapper.appendChild(floatingArrow);
                
                // Tambahkan event listener untuk scroll horizontal pada container
                const categoriesContainer = document.getElementById('categoryContainer');
                let scrollTimeout;
                let isScrolling = false;
                let hideTimeout; // Timeout untuk menyembunyikan tombol setelah 5 detik
                let cycleInterval; // Interval untuk siklus tampil-hilang
                let inactivityTimer; // Timer untuk menampilkan kembali setelah 5 menit tidak aktif
                
                // Fungsi untuk memeriksa apakah scroll sudah di ujung kanan
                function isScrollAtEnd() {
                    // Hitung dengan toleransi 5px untuk mengatasi perbedaan perhitungan di beberapa browser
                    return categoriesContainer.scrollLeft + categoriesContainer.clientWidth >= categoriesContainer.scrollWidth - 5;
                }
                
                // Fungsi untuk menampilkan tombol floating dengan transisi smooth
                function showFloatingButton() {
                    // Cek apakah container sudah di-scroll ke ujung
                    const atEnd = isScrollAtEnd();
                    
                    // Jika belum di ujung dan tidak dalam keadaan expanded
                    if (!atEnd && !isExpanded) {
                        // Pastikan transisi dimulai dari hidden state
                        setTimeout(() => {
                            floatingArrow.classList.remove('hidden');
                        }, 50);
                        
                        // Clear timeout yang ada jika ada
                        if (hideTimeout) clearTimeout(hideTimeout);
                        
                        // Set timeout baru untuk menyembunyikan tombol setelah 5 detik
                        hideTimeout = setTimeout(() => {
                            floatingArrow.classList.add('hidden');
                        }, 5000); // Tampilkan selama 5 detik
                    } else {
                        // Jika di ujung, pastikan tombol tersembunyi
                        floatingArrow.classList.add('hidden');
                    }
                }
                
                // Fungsi untuk menghilangkan elemen dengan transisi smooth
                function fadeOutContainer() {
                    // Mulai transisi opacity
                    categoriesContainer.style.transition = 'opacity 0.8s ease';
                    categoriesContainer.style.opacity = '0.7';
                    
                    // Sembunyikan floating button dengan smooth transition
                    floatingArrow.classList.add('hidden');
                }
                
                // Fungsi untuk menampilkan kembali elemen dengan transisi smooth
                function fadeInContainer() {
                    // Mulai transisi opacity kembali
                    categoriesContainer.style.transition = 'opacity 0.8s ease';
                    categoriesContainer.style.opacity = '1';
                }
                
                // Event listener untuk scroll
                categoriesContainer.addEventListener('scroll', function() {
                    // Tandai bahwa sedang scrolling
                    isScrolling = true;
                    
                    // Clear timeout sebelumnya
                    clearTimeout(scrollTimeout);
                    
                    // Menghilangkan elemen saat digeser dengan transisi smooth
                    fadeOutContainer();
                    
                    // Clear inactivity timer yang mungkin sedang berjalan
                    if (inactivityTimer) clearTimeout(inactivityTimer);
                    
                    // Set timeout untuk menandai bahwa scrolling telah berhenti
                    scrollTimeout = setTimeout(function() {
                        isScrolling = false;
                        
                        // Kembalikan opasitas container saat berhenti scroll dengan transisi smooth
                        fadeInContainer();
                        
                        // Set inactivity timer untuk menampilkan kembali setelah 5 menit tidak aktif
                        inactivityTimer = setTimeout(function() {
                            // Perbarui status tombol floating jika tidak di ujung dan tidak expanded
                            if (!isScrollAtEnd() && !isExpanded) {
                                showFloatingButton();
                                startFloatingButtonCycle();
                            }
                        }, 300000); // 5 menit = 300000 ms
                    }, 500); // Waktu lebih lama untuk deteksi berhenti scroll agar lebih smooth
                });
                
                // Jalankan siklus tampil-hilang
                function startFloatingButtonCycle() {
                    // Hentikan interval yang mungkin sedang berjalan
                    if (cycleInterval) clearInterval(cycleInterval);
                    
                    // Mulai interval baru
                    cycleInterval = setInterval(() => {
                        // Cek dulu apakah sudah di ujung kanan
                        if (!isScrollAtEnd() && !isScrolling && !isExpanded) {
                            // Tampilkan tombol hanya jika tidak di ujung kanan dan tidak sedang scroll
                            showFloatingButton();
                        }
                    }, 10000); // Siklus setiap 10 detik (5 detik tampil + 5 detik hilang)
                }
                
                // Mulai siklus tombol floating setelah 5 detik halaman dimuat
                setTimeout(() => {
                    // Cek dulu apakah sudah di ujung kanan
                    if (!isScrollAtEnd()) {
                        // Tampilkan tombol pertama kali hanya jika tidak di ujung kanan
                        showFloatingButton();
                    }
                    
                    // Mulai siklus
                    startFloatingButtonCycle();
                }, 5000);
                
                // Tambahkan event listener untuk expanded state
                document.addEventListener('expandedStateChanged', function(e) {
                    if (e.detail.expanded) {
                        // Jika expanded, sembunyikan tombol
                        floatingArrow.classList.add('hidden');
                        
                        // Clear semua timer
                        if (hideTimeout) clearTimeout(hideTimeout);
                        if (cycleInterval) clearInterval(cycleInterval);
                        if (inactivityTimer) clearTimeout(inactivityTimer);
                    } else {
                        // Jika kembali ke tampilan normal, mulai siklus lagi
                        setTimeout(() => {
                            // Cek dulu apakah sudah di ujung kanan
                            if (!isScrollAtEnd()) {
                                showFloatingButton();
                            }
                            startFloatingButtonCycle();
                        }, 300);
                    }
                });
                
                // Hentikan siklus saat halaman tidak terlihat
                document.addEventListener('visibilitychange', function() {
                    if (document.hidden) {
                        // Clear semua timer saat halaman tidak terlihat
                        if (hideTimeout) clearTimeout(hideTimeout);
                        if (cycleInterval) clearInterval(cycleInterval);
                        if (inactivityTimer) clearTimeout(inactivityTimer);
                    } else {
                        // Mulai ulang siklus saat halaman terlihat kembali
                        if (!isScrollAtEnd() && !isScrolling && !isExpanded) {
                            showFloatingButton();
                        }
                        startFloatingButtonCycle();
                        
                        // Set inactivity timer baru
                        if (inactivityTimer) clearTimeout(inactivityTimer);
                        inactivityTimer = setTimeout(function() {
                            if (!isScrollAtEnd() && !isExpanded) {
                                showFloatingButton();
                                startFloatingButtonCycle();
                            }
                        }, 300000); // 5 menit
                    }
                });
                
                return floatingArrow;
            }
            
            // MOBILE VIEW SETUP
            // Populate initial categories (first 9 categories only) for mobile view
            const visibleCategories = allCategories.slice(0, 9);
            visibleCategories.forEach(category => {
                // Buat div container untuk kategori
                const categoryItem = document.createElement('div');
                categoryItem.className = "category-item";
                categoryItem.innerHTML = `
                    <div class="category-icon" style="color: ${category.color};">
                        ${getIconSVG(category.icon)}
                        <div class="category-name">${category.name}</div>
                    </div>
                `;
                
                // Tambahkan event click untuk navigasi
                categoryItem.addEventListener('click', function() {
                    // Tampilkan tampilan subkategori dan inisialisasi dengan kategori yang dipilih
                     openSubcategoryView(category, true);
                });
                
                container.appendChild(categoryItem);
            });
            
            // Add "Lihat Semua" card as the last item in the row for mobile view
            const seeAllItem = document.createElement('div');
            seeAllItem.className = "see-all-item";
            seeAllItem.innerHTML = `
                <div class="category-icon">
                    ${getSeeAllIconSVG()}
                    <div class="category-name">Lihat Semua</div>
                </div>
            `;
            
            // Tambahkan event click untuk "Lihat Semua"
            seeAllItem.addEventListener('click', function() {
                // Jika di mobile, toggle expanded categories
                if (window.innerWidth < 768) {
                    toggleExpandedCategories();
                } else {
                    // Buka tampilan subkategori dengan kategori pertama
                    openSubcategoryView(allCategories[0]);
                }
            });
            
            container.appendChild(seeAllItem);
            
            // Create mobile expanded view
            let expandedHTML = '<div class="expanded-grid">';
            allCategories.forEach((category, i) => {
                expandedHTML += `
    <div class="expanded-item" onclick="openSubcategoryFromIndex(${i}, true)">
        <div class="expanded-icon" style="color: ${category.color}">
            ${getIconSVG(category.icon)}
        </div>
        <div class="expanded-name">${category.name}</div>
    </div>
`;
            });
            expandedHTML += '</div>';
            expandedCategories.innerHTML = expandedHTML;
            
            // DESKTOP VIEW SETUP
            // Create desktop grid view for first 13 categories
            const desktopVisibleCategories = allCategories.slice(0, 13);
            desktopVisibleCategories.forEach(category => {
                const desktopItem = document.createElement('div');
                desktopItem.className = "expanded-item";
                desktopItem.innerHTML = `
                    <div class="expanded-icon" style="color: ${category.color}">
                        ${getIconSVG(category.icon)}
                    </div>
                    <div class="expanded-name">${category.name}</div>
                `;
                
                desktopItem.addEventListener('click', function() {
                    if (window.innerWidth >= 768) {
                        openSubcategoryView(category);
                    } else {
                        window.location.href = category.link;
                    }
                });
                
                desktopGrid.appendChild(desktopItem);
            });
            
            // Add "View All" as the 14th item for desktop
            const desktopViewAllItem = document.createElement('div');
            desktopViewAllItem.className = "expanded-item see-all-item";
            desktopViewAllItem.id = "desktopViewAllItem";
            desktopViewAllItem.innerHTML = `
                <div class="expanded-icon">
                    ${getSeeAllIconSVG()}
                </div>
                <div class="expanded-name">View All</div>
            `;
            
            desktopViewAllItem.addEventListener('click', function() {
                if (window.innerWidth >= 768) {
                    toggleDesktopExtraCategories();
                } else {
                    // Buka tampilan subkategori dengan kategori pertama
                    openSubcategoryView(allCategories[0]);
                }
            });
            
            desktopGrid.appendChild(desktopViewAllItem);
            
           // Create extra categories view for desktop
const extraCategoriesList = allCategories.slice(13);
let extraHTML = '<div class="desktop-grid">';
extraCategoriesList.forEach((category, i) => {
    // Ubah kode ini untuk menggunakan index global dari allCategories
    const globalIndex = i + 13; // 13 kategori sudah ditampilkan sebelumnya
    extraHTML += `
        <div class="expanded-item" onclick="openSubcategoryFromIndex(${globalIndex}, true)">
            <div class="expanded-icon" style="color: ${category.color}">
                ${getIconSVG(category.icon)}
            </div>
            <div class="expanded-name">${category.name}</div>
        </div>
    `;
});
extraHTML += '</div>';
extraCategories.innerHTML = extraHTML;

           // Fungsi untuk membuka tampilan subkategori dari index (untuk diakses dari onclick inline)
window.openSubcategoryFromIndex = function(index, resetOrder = false) {
    // Ambil kategori berdasarkan index
    const category = allCategories[index];
    if (category) {
        // Buka tampilan subkategori
        openSubcategoryView(category, resetOrder);
    }
};
            
            // Function to toggle expanded categories for mobile view
            function toggleExpandedCategories() {
                isExpanded = !isExpanded;
                
                // Ambil atau buat tombol tutup jika belum ada
                let closeButtonContainer = document.querySelector('.close-button-container');
                if (!closeButtonContainer) {
                    closeButtonContainer = createCloseButton();
                }
                
                if (isExpanded) {
                    // Hide the default categories
                    container.style.display = 'none';
                    
                    // Show expanded categories
                    expandedCategories.classList.add('active');
                    
                    // Get the height of the expanded content for animation
                    const expandedContent = expandedCategories.querySelector('.expanded-grid');
                    const expandedHeight = expandedContent.offsetHeight;
                    
                    // Set height for animation
                    expandedCategories.style.height = expandedHeight + 'px';
                    
                    // Tampilkan tombol tutup
                    closeButtonContainer.style.display = 'flex';
                    
                    // Dispatch event bahwa expanded state berubah
                    document.dispatchEvent(new CustomEvent('expandedStateChanged', {
                        detail: { expanded: true }
                    }));
                } else {
                    // Show the default categories
                    container.style.display = 'flex';
                    
                    // Collapse expanded categories
                    expandedCategories.style.height = '0';
                    
                    // Sembunyikan tombol tutup
                    closeButtonContainer.style.display = 'none';
                    
                    // Remove active class after animation completes
                    setTimeout(() => {
                        expandedCategories.classList.remove('active');
                    }, 300);
                    
                    // Dispatch event bahwa expanded state berubah
                    document.dispatchEvent(new CustomEvent('expandedStateChanged', {
                        detail: { expanded: false }
                    }));
                }
            }
            
            // Function to toggle extra categories for desktop view
            function toggleDesktopExtraCategories() {
                isDesktopExpanded = !isDesktopExpanded;
                const viewAllButton = document.getElementById('desktopViewAllItem');
                
                if (isDesktopExpanded) {
                    // Show extra categories
                    extraCategories.classList.add('active');
                    
                    // Set height for animation
                    const extraContent = extraCategories.querySelector('.desktop-grid');
                    const extraHeight = extraContent.offsetHeight;
                    extraCategories.style.height = extraHeight + 'px';
                    
                    // Change "View All" to "Close"
                    viewAllButton.className = "expanded-item close-all-item";
                    viewAllButton.innerHTML = `
                        <div class="expanded-icon animate-in">
                            ${getCloseAllIconSVG()}
                        </div>
                        <div class="expanded-name">Close</div>
                    `;
                } else {
                    // Hide extra categories
                    extraCategories.style.height = '0';
                    
                    // Change "Close" back to "View All"
                    viewAllButton.className = "expanded-item see-all-item";
                    viewAllButton.innerHTML = `
                        <div class="expanded-icon animate-in">
                            ${getSeeAllIconSVG()}
                        </div>
                        <div class="expanded-name">View All</div>
                    `;
                    
                    // Remove active class after animation completes
                    setTimeout(() => {
                        extraCategories.classList.remove('active');
                    }, 300);
                }
            }
            
            // Setup desktop/mobile view based on initial screen size
            function setupResponsiveView() {
                if (window.innerWidth >= 768) {
                    // Desktop view
                    desktopGrid.style.display = 'grid';
                    container.style.display = 'none';
                    expandedCategories.style.display = 'none';
                    
                    // Sembunyikan tombol tutup di desktop
                    const closeButtonContainer = document.querySelector('.close-button-container');
                    if (closeButtonContainer) {
                        closeButtonContainer.style.display = 'none';
                    }
                } else {
                    // Mobile view
                    desktopGrid.style.display = 'none';
                    extraCategories.style.display = 'none';
                    container.style.display = 'flex';
                    expandedCategories.style.height = '0';
                    
                    // Tampilkan tombol tutup hanya jika kategori diperluas
                    const closeButtonContainer = document.querySelector('.close-button-container');
                    if (closeButtonContainer) {
                        closeButtonContainer.style.display = isExpanded ? 'flex' : 'none';
                    }
                }
            }
            
            // Setup category container wrapper & floating button
            setupCategoryContainer();
            
            // Initialize view
            setupResponsiveView();
            
            // Update on window resize
            window.addEventListener('resize', setupResponsiveView);
        });
    </script>
</body>
</html>