"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react"
import { format, addMonths, subMonths, setMonth, setYear, isValid } from "date-fns"
import { id } from "date-fns/locale"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface CustomDatePickerProps {
  value: Date | undefined
  onChange: (date: Date | undefined) => void
  placeholder?: string
  className?: string
  minDate?: Date
  maxDate?: Date
  disabled?: boolean
  required?: boolean
}

export function CustomDatePicker({
  value,
  onChange,
  placeholder = "Pilih tanggal",
  className,
  minDate,
  maxDate,
  disabled = false,
  required = false,
}: CustomDatePickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [currentMonth, setCurrentMonth] = useState<Date>(value || new Date())
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(value)
  const modalRef = useRef<HTMLDivElement>(null)

  // Update internal state when value changes
  useEffect(() => {
    setSelectedDate(value)
    if (value && isValid(value)) {
      setCurrentMonth(value)
    }
  }, [value])

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen])

  // Generate month options
  const monthOptions = Array.from({ length: 12 }, (_, i) => {
    const date = new Date(2021, i, 1)
    return {
      value: i,
      label: format(date, "MMMM", { locale: id }),
    }
  })

  // Generate year options (5 years back, 10 years forward)
  const yearOptions = () => {
    const currentYear = new Date().getFullYear()
    const years = []
    for (let year = currentYear - 5; year <= currentYear + 10; year++) {
      years.push(year)
    }
    return years
  }

  // Handle month navigation
  const prevMonth = () => {
    setCurrentMonth((prev) => subMonths(prev, 1))
  }

  const nextMonth = () => {
    setCurrentMonth((prev) => addMonths(prev, 1))
  }

  // Handle month/year selection
  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const month = Number.parseInt(e.target.value, 10)
    setCurrentMonth((prev) => setMonth(prev, month))
  }

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const year = Number.parseInt(e.target.value, 10)
    setCurrentMonth((prev) => setYear(prev, year))
  }

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear()
    const month = currentMonth.getMonth()

    // First day of the month
    const firstDay = new Date(year, month, 1)
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0)

    // Get the day of the week for the first day (0 = Sunday, 1 = Monday, etc.)
    let firstDayOfWeek = firstDay.getDay()
    // Adjust for Monday as first day of week
    firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1

    // Days from previous month
    const prevMonthDays = []
    for (let i = 0; i < firstDayOfWeek; i++) {
      const day = new Date(year, month, -firstDayOfWeek + i + 1)
      prevMonthDays.push(day)
    }

    // Days of current month
    const currentMonthDays = []
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const day = new Date(year, month, i)
      currentMonthDays.push(day)
    }

    // Days from next month
    const nextMonthDays = []
    const totalDaysDisplayed = 42 // 6 rows of 7 days
    const remainingDays = totalDaysDisplayed - prevMonthDays.length - currentMonthDays.length
    for (let i = 1; i <= remainingDays; i++) {
      const day = new Date(year, month + 1, i)
      nextMonthDays.push(day)
    }

    return [...prevMonthDays, ...currentMonthDays, ...nextMonthDays]
  }

  // Check if a date is disabled
  const isDateDisabled = (date: Date) => {
    if (minDate && date < minDate) return true
    if (maxDate && date > maxDate) return true
    return false
  }

  // Check if a date is selected
  const isDateSelected = (date: Date) => {
    if (!selectedDate) return false
    return (
      date.getDate() === selectedDate.getDate() &&
      date.getMonth() === selectedDate.getMonth() &&
      date.getFullYear() === selectedDate.getFullYear()
    )
  }

  // Check if a date is today
  const isToday = (date: Date) => {
    const today = new Date()
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    )
  }

  // Check if a date is in the current month
  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentMonth.getMonth()
  }

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date)
  }

  // Handle confirm selection
  const handleConfirm = () => {
    onChange(selectedDate)
    setIsOpen(false)
  }

  // Handle cancel
  const handleCancel = () => {
    setSelectedDate(value)
    setIsOpen(false)
  }

  // Format date for display
  const formatDate = (date: Date | undefined) => {
    if (!date) return ""
    return format(date, "dd MMMM yyyy", { locale: id })
  }

  // Days of week headers
  const daysOfWeek = ["Sen", "Sel", "Rab", "Kam", "Jum", "Sab", "Min"]

  // Group days into weeks
  const weeks = []
  const days = generateCalendarDays()
  for (let i = 0; i < days.length; i += 7) {
    weeks.push(days.slice(i, i + 7))
  }

  return (
    <div className={cn("relative", className)}>
      <div className="custom-date-picker">
        <Button
          type="button"
          variant="outline"
          className={cn("w-full justify-start text-left font-normal", !value && "text-muted-foreground")}
          onClick={() => !disabled && setIsOpen(true)}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {value ? formatDate(value) : placeholder}
        </Button>
      </div>

      {isOpen && (
        <div className="date-picker-modal">
          <div ref={modalRef} className="date-picker-modal-content">
            <div className="date-picker-header">
              <span className="date-picker-title">{format(currentMonth, "MMMM yyyy", { locale: id })}</span>
            </div>

            <div className="date-picker-dropdown">
              <select value={currentMonth.getMonth()} onChange={handleMonthChange} className="flex-1">
                {monthOptions.map((month) => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>

              <select value={currentMonth.getFullYear()} onChange={handleYearChange}>
                {yearOptions().map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex justify-between items-center mb-2">
              <button type="button" onClick={prevMonth} className="p-1 rounded-full hover:bg-accent">
                <ChevronLeft className="h-4 w-4" />
              </button>

              <button type="button" onClick={nextMonth} className="p-1 rounded-full hover:bg-accent">
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>

            <table className="date-picker-calendar">
              <thead>
                <tr>
                  {daysOfWeek.map((day) => (
                    <th key={day}>{day}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {weeks.map((week, weekIndex) => (
                  <tr key={weekIndex}>
                    {week.map((day, dayIndex) => (
                      <td key={dayIndex}>
                        <button
                          type="button"
                          onClick={() => handleDateSelect(day)}
                          disabled={isDateDisabled(day)}
                          className={cn(
                            !isCurrentMonth(day) && "opacity-40",
                            isDateSelected(day) && "selected",
                            isToday(day) && "today",
                          )}
                        >
                          {day.getDate()}
                        </button>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>

            <div className="date-picker-actions">
              <button type="button" onClick={handleCancel} className="date-picker-btn date-picker-btn-secondary">
                Batal
              </button>
              <button type="button" onClick={handleConfirm} className="date-picker-btn date-picker-btn-primary">
                Pilih
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
