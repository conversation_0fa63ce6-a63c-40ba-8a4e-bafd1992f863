"use client"

import Link from "next/link"
import { <PERSON>, <PERSON>, Settings } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { UserMenu } from "@/components/dashboard/user-menu"
import { Badge } from "@/components/ui/badge"
import { ModeToggle } from "@/components/mode-toggle"
import { RoleSwitcher } from "@/components/role-switcher"

export function TenantHeader() {
  return (
    <header className="sticky top-0 z-30 flex h-16 items-center justify-between border-b border-border/20 bg-background/80 px-6 backdrop-blur-md supports-[backdrop-filter]:bg-background/20">
      <div className="flex items-center gap-2">
        <Link href="/tenant/dashboard" className="flex items-center gap-2">
          <div className="text-primary flex h-8 w-8 items-center justify-center rounded-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="glow-text-primary"
            >
              <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
              <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" />
              <path d="M12 12v5" />
            </svg>
          </div>
          <span className="font-bold tracking-tight text-primary glow-text-primary">TENANT OS</span>
        </Link>
      </div>

      <div className="flex-1 max-w-md mx-auto">
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search marketplace..."
            className="w-full bg-background/30 border-border/30 pl-10 pr-4 h-9 rounded-full focus-visible:ring-primary/20"
          />
        </div>
      </div>

      <div className="flex items-center gap-4">
        <ModeToggle />
        <RoleSwitcher />
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          <Badge className="absolute -right-1 -top-1 h-4 w-4 p-0 flex items-center justify-center text-[10px]">2</Badge>
        </Button>
        <Button variant="ghost" size="icon">
          <Settings className="h-5 w-5" />
        </Button>
        <UserMenu />
      </div>
    </header>
  )
}
