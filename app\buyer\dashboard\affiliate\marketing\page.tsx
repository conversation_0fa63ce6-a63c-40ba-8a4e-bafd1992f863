import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { AffiliateBannerGallery } from "@/components/buyer/affiliate/affiliate-banner-gallery"
import { AffiliateProductWidgets } from "@/components/buyer/affiliate/affiliate-product-widgets"
import { AffiliateContentTemplates } from "@/components/buyer/affiliate/affiliate-content-templates"

export default function AffiliateMarketingPage() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Marketing Materials</CardTitle>
        <CardDescription>Akses berbagai materi marketing untuk mempromosikan produk dan toko</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="banners" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="banners">Banner Gallery</TabsTrigger>
            <TabsTrigger value="widgets">Product Widgets</TabsTrigger>
            <TabsTrigger value="templates">Content Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="banners" className="pt-6">
            <AffiliateBannerGallery />
          </TabsContent>

          <TabsContent value="widgets" className="pt-6">
            <AffiliateProductWidgets />
          </TabsContent>

          <TabsContent value="templates" className="pt-6">
            <AffiliateContentTemplates />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
