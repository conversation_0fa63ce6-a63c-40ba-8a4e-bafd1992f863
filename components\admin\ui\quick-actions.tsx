"use client"

import type React from "react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { PlusCircle, ChevronDown } from "lucide-react"

interface QuickAction {
  id: string
  label: string
  icon?: React.ReactNode
  onClick: () => void
}

interface QuickActionsProps {
  primaryAction?: QuickAction
  actions: QuickAction[]
  className?: string
}

export function QuickActions({ primaryAction, actions, className }: QuickActionsProps) {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {primaryAction && (
        <Button onClick={primaryAction.onClick} className="gap-1">
          {primaryAction.icon || <PlusCircle className="h-4 w-4" />}
          {primaryAction.label}
        </Button>
      )}
      {actions.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="gap-1">
              Actions <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {actions.map((action) => (
              <DropdownMenuItem key={action.id} onClick={action.onClick}>
                {action.icon}
                {action.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  )
}
