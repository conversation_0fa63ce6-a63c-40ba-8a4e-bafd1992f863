"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Monitor, Smartphone, Tablet, Loader2 } from "lucide-react"
import { tenantThemesAPI } from "@/lib/api/tenant-themes"
import type { TenantTheme } from "@/lib/models/tenant-theme"

interface ThemePreviewProps {
  themeId: string
}

export function ThemePreview({ themeId }: ThemePreviewProps) {
  const router = useRouter()
  const [theme, setTheme] = useState<TenantTheme | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<"desktop" | "tablet" | "mobile">("desktop")

  useEffect(() => {
    const fetchTheme = async () => {
      setLoading(true)
      try {
        const themeData = await tenantThemesAPI.getById(themeId)
        if (themeData) {
          setTheme(themeData)
        } else {
          setError("Theme not found")
        }
      } catch (err) {
        console.error("Error fetching theme:", err)
        setError("Failed to load theme")
      } finally {
        setLoading(false)
      }
    }

    fetchTheme()
  }, [themeId])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (error || !theme) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <p className="text-red-500 mb-4">{error || "Theme not found"}</p>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Go Back
        </Button>
      </div>
    )
  }

  const getPreviewWidth = () => {
    switch (viewMode) {
      case "mobile":
        return "max-w-[375px]"
      case "tablet":
        return "max-w-[768px]"
      case "desktop":
      default:
        return "max-w-full"
    }
  }

  // Apply theme styles to preview container
  const previewStyles = {
    "--primary": theme.colors.primary,
    "--secondary": theme.colors.secondary,
    "--accent": theme.colors.accent,
    "--background": theme.colors.background,
    "--foreground": theme.colors.foreground,
    "--muted": theme.colors.muted,
    "--muted-foreground": theme.colors.mutedForeground,
    "--border": theme.colors.border,
    "--input": theme.colors.input,
    "--card": theme.colors.card,
    "--card-foreground": theme.colors.cardForeground,
    "--destructive": theme.colors.destructive,
    "--destructive-foreground": theme.colors.destructiveForeground,
    "--font-heading": theme.fonts.heading,
    "--font-body": theme.fonts.body,
  } as React.CSSProperties

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Themes
        </Button>
        <div className="flex items-center gap-2">
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "desktop" | "tablet" | "mobile")}>
            <TabsList>
              <TabsTrigger value="desktop">
                <Monitor className="h-4 w-4" />
                <span className="ml-2 hidden sm:inline">Desktop</span>
              </TabsTrigger>
              <TabsTrigger value="tablet">
                <Tablet className="h-4 w-4" />
                <span className="ml-2 hidden sm:inline">Tablet</span>
              </TabsTrigger>
              <TabsTrigger value="mobile">
                <Smartphone className="h-4 w-4" />
                <span className="ml-2 hidden sm:inline">Mobile</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <div className="bg-gray-100 border-b p-2 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-500" />
            <div className="w-3 h-3 rounded-full bg-yellow-500" />
            <div className="w-3 h-3 rounded-full bg-green-500" />
          </div>
          <div className="bg-white rounded px-2 py-1 text-xs text-gray-500 flex-1 mx-4 text-center">
            {theme.name} Preview
          </div>
          <div className="w-6" />
        </div>
        <div className="bg-gray-50 p-4 flex justify-center overflow-auto" style={{ minHeight: "600px" }}>
          <div className={`transition-all duration-300 ${getPreviewWidth()}`}>
            <div className="bg-white rounded shadow-lg overflow-hidden" style={previewStyles}>
              {/* Header Preview */}
              <header
                className="p-4 border-b"
                style={{
                  backgroundColor: theme.colors.background,
                  color: theme.colors.foreground,
                  borderColor: theme.colors.border,
                }}
              >
                <div className="container mx-auto flex items-center justify-between">
                  <div className="flex items-center">
                    <img src={theme.logo?.light || "/your-logo.png"} alt="Logo" className="h-8 w-auto" />
                    <nav className="ml-6 hidden md:flex space-x-4">
                      <a href="#" className="hover:text-primary">
                        Home
                      </a>
                      <a href="#" className="hover:text-primary">
                        Products
                      </a>
                      <a href="#" className="hover:text-primary">
                        Categories
                      </a>
                      <a href="#" className="hover:text-primary">
                        About
                      </a>
                      <a href="#" className="hover:text-primary">
                        Contact
                      </a>
                    </nav>
                  </div>
                  <div className="flex items-center space-x-4">
                    <button className="p-2 rounded-full hover:bg-muted" style={{ color: theme.colors.foreground }}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <circle cx="11" cy="11" r="8" />
                        <path d="m21 21-4.3-4.3" />
                      </svg>
                    </button>
                    <button className="p-2 rounded-full hover:bg-muted" style={{ color: theme.colors.foreground }}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z" />
                        <path d="M3 6h18" />
                        <path d="M16 10a4 4 0 0 1-8 0" />
                      </svg>
                    </button>
                    <button className="p-2 rounded-full hover:bg-muted" style={{ color: theme.colors.foreground }}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                        <circle cx="12" cy="7" r="4" />
                      </svg>
                    </button>
                  </div>
                </div>
              </header>

              {/* Hero Section */}
              <section
                className="py-12 px-4"
                style={{
                  backgroundColor: theme.colors.background,
                  color: theme.colors.foreground,
                }}
              >
                <div className="container mx-auto max-w-6xl">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                    <div>
                      <h1 className="text-4xl font-bold mb-4" style={{ fontFamily: theme.fonts.heading }}>
                        Welcome to Our Store
                      </h1>
                      <p
                        className="text-lg mb-6"
                        style={{
                          color: theme.colors.mutedForeground,
                          fontFamily: theme.fonts.body,
                        }}
                      >
                        Discover amazing products at great prices. Shop now and enjoy free shipping on all orders.
                      </p>
                      <div className="flex flex-wrap gap-4">
                        <button
                          className="px-6 py-3 rounded-md font-medium"
                          style={{
                            backgroundColor: theme.colors.primary,
                            color: "#ffffff",
                          }}
                        >
                          Shop Now
                        </button>
                        <button
                          className="px-6 py-3 rounded-md font-medium"
                          style={{
                            backgroundColor: "transparent",
                            color: theme.colors.primary,
                            borderColor: theme.colors.primary,
                            borderWidth: "1px",
                          }}
                        >
                          Learn More
                        </button>
                      </div>
                    </div>
                    <div>
                      <img src="/colorful-shopping-items.png" alt="Featured Products" className="rounded-lg shadow-lg" />
                    </div>
                  </div>
                </div>
              </section>

              {/* Featured Products */}
              <section
                className="py-12 px-4"
                style={{
                  backgroundColor: theme.colors.muted,
                  color: theme.colors.foreground,
                }}
              >
                <div className="container mx-auto max-w-6xl">
                  <h2 className="text-3xl font-bold mb-8 text-center" style={{ fontFamily: theme.fonts.heading }}>
                    Featured Products
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {[1, 2, 3, 4].map((item) => (
                      <div
                        key={item}
                        className="rounded-lg overflow-hidden"
                        style={{
                          backgroundColor: theme.colors.card,
                          color: theme.colors.cardForeground,
                          borderColor: theme.colors.border,
                          borderWidth: "1px",
                        }}
                      >
                        <img
                          src={`/generic-product-display.png?height=200&width=300&query=product ${item}`}
                          alt={`Product ${item}`}
                          className="w-full h-48 object-cover"
                        />
                        <div className="p-4">
                          <h3 className="font-semibold mb-2" style={{ fontFamily: theme.fonts.heading }}>
                            Product Name {item}
                          </h3>
                          <p
                            className="text-sm mb-3"
                            style={{
                              color: theme.colors.mutedForeground,
                              fontFamily: theme.fonts.body,
                            }}
                          >
                            Short product description goes here.
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="font-bold">$99.99</span>
                            <button
                              className="p-2 rounded-md"
                              style={{
                                backgroundColor: theme.colors.primary,
                                color: "#ffffff",
                              }}
                            >
                              Add to Cart
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Footer */}
              <footer
                className="py-8 px-4"
                style={{
                  backgroundColor: theme.colors.card,
                  color: theme.colors.cardForeground,
                  borderTopColor: theme.colors.border,
                  borderTopWidth: "1px",
                }}
              >
                <div className="container mx-auto max-w-6xl">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <div>
                      <h4 className="font-bold mb-4" style={{ fontFamily: theme.fonts.heading }}>
                        About Us
                      </h4>
                      <p
                        className="text-sm"
                        style={{
                          color: theme.colors.mutedForeground,
                          fontFamily: theme.fonts.body,
                        }}
                      >
                        We are dedicated to providing the best shopping experience with quality products and excellent
                        customer service.
                      </p>
                    </div>
                    <div>
                      <h4 className="font-bold mb-4" style={{ fontFamily: theme.fonts.heading }}>
                        Categories
                      </h4>
                      <ul
                        className="space-y-2 text-sm"
                        style={{
                          color: theme.colors.mutedForeground,
                          fontFamily: theme.fonts.body,
                        }}
                      >
                        <li>
                          <a href="#" className="hover:text-primary">
                            Electronics
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary">
                            Clothing
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary">
                            Home & Garden
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary">
                            Beauty & Health
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold mb-4" style={{ fontFamily: theme.fonts.heading }}>
                        Customer Service
                      </h4>
                      <ul
                        className="space-y-2 text-sm"
                        style={{
                          color: theme.colors.mutedForeground,
                          fontFamily: theme.fonts.body,
                        }}
                      >
                        <li>
                          <a href="#" className="hover:text-primary">
                            Contact Us
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary">
                            FAQs
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary">
                            Shipping Policy
                          </a>
                        </li>
                        <li>
                          <a href="#" className="hover:text-primary">
                            Returns & Refunds
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold mb-4" style={{ fontFamily: theme.fonts.heading }}>
                        Connect With Us
                      </h4>
                      <div className="flex space-x-4">
                        <a href="#" className="hover:text-primary">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                          </svg>
                        </a>
                        <a href="#" className="hover:text-primary">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                            <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                            <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                          </svg>
                        </a>
                        <a href="#" className="hover:text-primary">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                          </svg>
                        </a>
                        <a href="#" className="hover:text-primary">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                            <rect width="4" height="12" x="2" y="9" />
                            <circle cx="4" cy="4" r="2" />
                          </svg>
                        </a>
                      </div>
                      <div className="mt-4">
                        <h5 className="font-semibold mb-2" style={{ fontFamily: theme.fonts.heading }}>
                          Subscribe to our newsletter
                        </h5>
                        <div className="flex">
                          <input
                            type="email"
                            placeholder="Your email"
                            className="px-3 py-2 rounded-l-md w-full text-sm"
                            style={{
                              backgroundColor: theme.colors.input,
                              color: theme.colors.foreground,
                              borderColor: theme.colors.border,
                              borderWidth: "1px",
                            }}
                          />
                          <button
                            className="px-3 py-2 rounded-r-md"
                            style={{
                              backgroundColor: theme.colors.primary,
                              color: "#ffffff",
                            }}
                          >
                            Subscribe
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    className="mt-8 pt-6 text-center text-sm"
                    style={{
                      borderTopColor: theme.colors.border,
                      borderTopWidth: "1px",
                      color: theme.colors.mutedForeground,
                      fontFamily: theme.fonts.body,
                    }}
                  >
                    &copy; {new Date().getFullYear()} Your Store Name. All rights reserved.
                  </div>
                </div>
              </footer>
            </div>
          </div>
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-bold mb-4">Theme Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">Colors</h3>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(theme.colors).map(([key, value]) => (
                  <div key={key} className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full border" style={{ backgroundColor: value }} />
                    <span className="text-sm capitalize">{key}: </span>
                    <span className="text-sm text-muted-foreground">{value}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Typography</h3>
              <div className="space-y-2">
                <div>
                  <span className="text-sm">Heading Font: </span>
                  <span className="text-sm font-medium">{theme.fonts.heading}</span>
                </div>
                <div>
                  <span className="text-sm">Body Font: </span>
                  <span className="text-sm font-medium">{theme.fonts.body}</span>
                </div>
              </div>

              <h3 className="font-semibold mt-4 mb-2">Layout</h3>
              <div className="space-y-2">
                {theme.layout ? (
                  <>
                    <div>
                      <span className="text-sm">Header: </span>
                      <span className="text-sm font-medium capitalize">{theme.layout.header || 'Default'}</span>
                    </div>
                    <div>
                      <span className="text-sm">Footer: </span>
                      <span className="text-sm font-medium capitalize">{theme.layout.footer || 'Default'}</span>
                    </div>
                    <div>
                      <span className="text-sm">Sidebar: </span>
                      <span className="text-sm font-medium capitalize">{theme.layout.sidebar || 'Default'}</span>
                    </div>
                  </>
                ) : (
                  <div className="text-sm text-muted-foreground">No layout information available</div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
