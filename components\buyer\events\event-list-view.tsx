"use client"

import { useState } from "react"
import { EventCard } from "./event-card"
import { mockEvents } from "./mock-data"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { DateRange } from "react-day-picker"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { Card, CardContent } from "@/components/ui/card"
import { CalendarIcon } from "lucide-react"

interface EventListViewProps {
  searchQuery: string
  selectedCategories: string[]
}

export function EventListView({ searchQuery, selectedCategories }: EventListViewProps) {
  const [sortBy, setSortBy] = useState<string>("date-asc")
  const [dateRange, setDateRange] = useState<DateRange | undefined>()

  // Filter events berdasarkan pencarian, kategori, dan rentang tanggal
  const filteredEvents = mockEvents.filter((event) => {
    const matchesSearch = searchQuery
      ? event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.description.toLowerCase().includes(searchQuery.toLowerCase())
      : true

    const matchesCategory = selectedCategories.length > 0 ? selectedCategories.includes(event.category) : true

    const eventDate = new Date(event.date)
    const matchesDateRange =
      dateRange && dateRange.from && dateRange.to ? eventDate >= dateRange.from && eventDate <= dateRange.to : true

    return matchesSearch && matchesCategory && matchesDateRange
  })

  // Urutkan events
  const sortedEvents = [...filteredEvents].sort((a, b) => {
    switch (sortBy) {
      case "date-asc":
        return new Date(a.date).getTime() - new Date(b.date).getTime()
      case "date-desc":
        return new Date(b.date).getTime() - new Date(a.date).getTime()
      case "popularity":
        return b.participants - a.participants
      default:
        return 0
    }
  })

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Urutkan:</span>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Urutkan berdasarkan" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date-asc">Tanggal (Terlama)</SelectItem>
              <SelectItem value="date-desc">Tanggal (Terbaru)</SelectItem>
              <SelectItem value="popularity">Popularitas</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <DateRangePicker value={dateRange} onChange={setDateRange} placeholder="Filter berdasarkan tanggal" />
      </div>

      {sortedEvents.length > 0 ? (
        <div className="space-y-4">
          {sortedEvents.map((event) => (
            <EventCard key={event.id} event={event} />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <CalendarIcon className="mb-2 h-10 w-10 text-muted-foreground" />
            <p className="text-center text-muted-foreground">Tidak ada event yang ditemukan</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
