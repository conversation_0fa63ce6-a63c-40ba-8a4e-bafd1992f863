// Debug script untuk navigasi
window.navigationDebug = {
  // Log route changes
  startRouteLogging: () => {
    if (typeof window !== "undefined") {
      const originalPushState = window.history.pushState
      const originalReplaceState = window.history.replaceState

      window.history.pushState = function () {
        console.log("Navigation: pushState", arguments)
        return originalPushState.apply(this, arguments)
      }

      window.history.replaceState = function () {
        console.log("Navigation: replaceState", arguments)
        return originalReplaceState.apply(this, arguments)
      }

      window.addEventListener("popstate", () => {
        console.log("Navigation: popstate", window.location.pathname)
      })

      console.log("Route logging started")
      return true
    }
    return false
  },

  // Get current route
  getCurrentRoute: () => {
    if (typeof window !== "undefined") {
      return {
        pathname: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash,
        url: window.location.href,
      }
    }
    return null
  },

  // Navigate to route
  navigateTo: (path) => {
    if (typeof window !== "undefined") {
      window.location.href = path
      return true
    }
    return false
  },
}

// Log pesan saat script dimuat
console.log("Navigation debug tools loaded. Use window.navigationDebug to access the tools.")
