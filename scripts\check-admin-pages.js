const fs = require("fs")
const path = require("path")

// Direktori admin yang akan diperiksa
const ADMIN_DIR = path.join(process.cwd(), "app/admin")

// Fungsi untuk memeriksa apakah file adalah halaman Next.js
function isNextPage(filePath) {
  return (
    (filePath.endsWith("page.js") || filePath.endsWith("page.tsx")) &&
    !filePath.includes("_app") &&
    !filePath.includes("_document")
  )
}

// Fungsi untuk memeriksa apakah file menggunakan useNotifications
function usesNotifications(content) {
  return content.includes("useNotifications")
}

// Fungsi untuk memeriksa apakah file memiliki direktif 'use client'
function hasUseClientDirective(content) {
  return content.includes('"use client"') || content.includes("'use client'")
}

// Fungsi untuk mencari semua file dalam direktori secara rekursif
function findAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir)

  files.forEach((file) => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)

    if (stat.isDirectory()) {
      findAllFiles(filePath, fileList)
    } else {
      fileList.push(filePath)
    }
  })

  return fileList
}

// Fungsi utama
function main() {
  console.log("Memeriksa halaman admin...")

  const allFiles = findAllFiles(ADMIN_DIR)
  const pages = allFiles.filter(isNextPage)

  console.log(`Ditemukan ${pages.length} halaman admin.`)

  const problematicPages = []

  pages.forEach((page) => {
    const content = fs.readFileSync(page, "utf8")

    if (usesNotifications(content)) {
      if (!hasUseClientDirective(content)) {
        problematicPages.push({
          page,
          issue: 'Menggunakan useNotifications tanpa direktif "use client"',
        })
      }
    }
  })

  if (problematicPages.length > 0) {
    console.log("\n⚠️ Halaman bermasalah ditemukan:")
    problematicPages.forEach(({ page, issue }) => {
      console.log(`- ${page.replace(process.cwd(), "")}: ${issue}`)
    })

    console.log("\nSaran perbaikan:")
    console.log('1. Tambahkan direktif "use client" di bagian atas file')
    console.log("2. ATAU Pisahkan komponen menjadi bagian client dan server")
    console.log("3. Pastikan komponen dibungkus oleh NotificationsProvider")
  } else {
    console.log("\n✅ Tidak ditemukan masalah!")
  }
}

main()
