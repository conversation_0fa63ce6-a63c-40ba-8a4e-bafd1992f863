"use client"

import { useState, useEffect } from "react"
import { <PERSON>, FolderPlus, Loader2, Plus } from "lucide-react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { CollectionCreator } from "./collection-creator"

interface Collection {
  id: string
  name: string
  description: string
  imageUrl: string
  createdAt: string
  itemCount: number
  isPublic: boolean
  items: string[]
}

interface CollectionSelectorProps {
  children: React.ReactNode
  itemId: string
  size?: "default" | "sm"
  variant?: "default" | "outline"
}

export function CollectionSelector({ 
  children, 
  itemId,
  size = "default",
  variant = "default" 
}: CollectionSelectorProps) {
  const [open, setOpen] = useState(false)
  const [collections, setCollections] = useState<Collection[]>([])
  const [selectedCollections, setSelectedCollections] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [originalSelections, setOriginalSelections] = useState<string[]>([])

  // Fungsi untuk mengambil daftar koleksi
  const fetchCollections = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/collections')
      if (!response.ok) throw new Error('Gagal mengambil daftar koleksi')
      
      const data = await response.json()
      setCollections(data)
      
      // Periksa koleksi mana yang sudah berisi item ini
      const collectionsWithItem = data.filter((collection: Collection) => 
        collection.items.includes(itemId)
      ).map((collection: Collection) => collection.id)
      
      setSelectedCollections(collectionsWithItem)
      setOriginalSelections(collectionsWithItem)
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal mengambil daftar koleksi",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fungsi untuk menyimpan perubahan
  const saveChanges = async () => {
    setIsSaving(true)
    
    try {
      // Koleksi yang perlu ditambahkan item
      const collectionsToAddItem = selectedCollections.filter(
        id => !originalSelections.includes(id)
      )
      
      // Koleksi yang perlu menghapus item
      const collectionsToRemoveItem = originalSelections.filter(
        id => !selectedCollections.includes(id)
      )
      
      // Tambahkan item ke koleksi yang dipilih
      for (const collectionId of collectionsToAddItem) {
        const response = await fetch(`/api/collections/${collectionId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'addItem',
            itemId,
          }),
        })
        
        if (!response.ok) {
          throw new Error(`Gagal menambahkan item ke koleksi ${collectionId}`)
        }
      }
      
      // Hapus item dari koleksi yang tidak dipilih lagi
      for (const collectionId of collectionsToRemoveItem) {
        const response = await fetch(`/api/collections/${collectionId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'removeItem',
            itemId,
          }),
        })
        
        if (!response.ok) {
          throw new Error(`Gagal menghapus item dari koleksi ${collectionId}`)
        }
      }
      
      toast({
        title: "Berhasil",
        description: "Koleksi berhasil diperbarui",
      })
      
      setOpen(false)
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal memperbarui koleksi",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Fungsi untuk toggle pilihan koleksi
  const toggleCollection = (collectionId: string) => {
    setSelectedCollections(prev => 
      prev.includes(collectionId)
        ? prev.filter(id => id !== collectionId)
        : [...prev, collectionId]
    )
  }

  // Effect untuk mengambil daftar koleksi saat dialog dibuka
  useEffect(() => {
    if (open) {
      fetchCollections()
    }
  }, [open])

  // Cek apakah ada perubahan yang perlu disimpan
  const hasChanges = () => {
    if (selectedCollections.length !== originalSelections.length) return true
    return selectedCollections.some(id => !originalSelections.includes(id))
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Tambahkan ke Koleksi</DialogTitle>
          <DialogDescription>
            Pilih koleksi untuk menyimpan produk ini atau buat koleksi baru.
          </DialogDescription>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex h-[300px] items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : collections.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="mb-4 rounded-full bg-muted p-4">
              <FolderPlus className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="mb-2 text-lg font-medium">Belum Ada Koleksi</h3>
            <p className="mb-6 text-sm text-muted-foreground">
              Anda belum memiliki koleksi. Buat koleksi baru untuk menyimpan produk ini.
            </p>
            <CollectionCreator 
              onSuccess={() => fetchCollections()}
              buttonLabel="Buat Koleksi Baru"
            />
          </div>
        ) : (
          <>
            <ScrollArea className="max-h-[300px] pr-4">
              <div className="space-y-1">
                {collections.map((collection) => (
                  <div key={collection.id}>
                    <button
                      type="button"
                      className="flex w-full items-center justify-between rounded-md px-3 py-2 text-left text-sm hover:bg-muted"
                      onClick={() => toggleCollection(collection.id)}
                    >
                      <div>
                        <div className="font-medium">{collection.name}</div>
                        {collection.description && (
                          <div className="text-xs text-muted-foreground line-clamp-1">
                            {collection.description}
                          </div>
                        )}
                      </div>
                      <div className="flex h-5 w-5 items-center justify-center">
                        {selectedCollections.includes(collection.id) && (
                          <Check className="h-4 w-4" />
                        )}
                      </div>
                    </button>
                    <Separator className="my-1" />
                  </div>
                ))}
              </div>
            </ScrollArea>
            
            <div className="flex items-center justify-between">
              <CollectionCreator 
                onSuccess={() => fetchCollections()}
                buttonIcon={<Plus className="mr-1 h-3 w-3" />}
                buttonLabel="Buat Koleksi Baru"
                size="sm"
              />
              
              <DialogFooter className="sm:justify-end">
                <Button 
                  variant="secondary" 
                  onClick={() => setOpen(false)}
                  disabled={isSaving}
                >
                  Batal
                </Button>
                <Button 
                  onClick={saveChanges} 
                  disabled={!hasChanges() || isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Menyimpan...
                    </>
                  ) : (
                    "Simpan Perubahan"
                  )}
                </Button>
              </DialogFooter>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
} 