"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Plus,
  Grid,
  Eye,
  PencilIcon,
  Trash,
  MoreHorizontal,
  ChevronDown,
  ChevronUp,
  ChevronRight,
  Settings,
  MenuSquare,
  ListTree,
  LayoutDashboard,
  MoveVertical,
  ExternalLink,
  Home
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk menu
const navigationData = {
  mainMenu: [
    {
      id: "menu-001",
      title: "<PERSON><PERSON>a",
      url: "/",
      icon: "home",
      type: "internal",
      position: 1,
      isActive: true,
      children: []
    },
    {
      id: "menu-002",
      title: "Produk",
      url: "/products",
      icon: "box",
      type: "internal",
      position: 2,
      isActive: true,
      children: [
        {
          id: "menu-002-001",
          title: "Se<PERSON><PERSON> Produk",
          url: "/products",
          type: "internal",
          position: 1,
          isActive: true,
          children: []
        },
        {
          id: "menu-002-002",
          title: "Kategori",
          url: "/products/categories",
          type: "internal",
          position: 2,
          isActive: true,
          children: []
        },
        {
          id: "menu-002-003",
          title: "Produk Terbaru",
          url: "/products/new",
          type: "internal",
          position: 3,
          isActive: true,
          children: []
        },
        {
          id: "menu-002-004",
          title: "Produk Diskon",
          url: "/products/discounts",
          type: "internal",
          position: 4,
          isActive: true,
          children: []
        }
      ]
    },
    {
      id: "menu-003",
      title: "Tentang Kami",
      url: "/about",
      icon: "info",
      type: "internal",
      position: 3,
      isActive: true,
      children: []
    },
    {
      id: "menu-004",
      title: "Blog",
      url: "/blog",
      icon: "file-text",
      type: "internal",
      position: 4,
      isActive: true,
      children: [
        {
          id: "menu-004-001",
          title: "Semua Artikel",
          url: "/blog",
          type: "internal",
          position: 1,
          isActive: true,
          children: []
        },
        {
          id: "menu-004-002",
          title: "Tips & Trik",
          url: "/blog/category/tips-trik",
          type: "internal",
          position: 2,
          isActive: true,
          children: []
        },
        {
          id: "menu-004-003",
          title: "Tutorial",
          url: "/blog/category/tutorial",
          type: "internal",
          position: 3,
          isActive: true,
          children: []
        }
      ]
    },
    {
      id: "menu-005",
      title: "Kontak",
      url: "/contact",
      icon: "mail",
      type: "internal",
      position: 5,
      isActive: true,
      children: []
    },
    {
      id: "menu-006",
      title: "Partner",
      url: "https://partner.example.com",
      icon: "users",
      type: "external",
      position: 6,
      isActive: true,
      children: []
    }
  ],
  footerMenu: [
    {
      id: "footer-001",
      title: "Bantuan",
      url: "/help",
      type: "internal",
      position: 1,
      isActive: true,
      children: []
    },
    {
      id: "footer-002",
      title: "Kebijakan Privasi",
      url: "/privacy-policy",
      type: "internal",
      position: 2,
      isActive: true,
      children: []
    },
    {
      id: "footer-003",
      title: "Syarat dan Ketentuan",
      url: "/terms",
      type: "internal",
      position: 3,
      isActive: true,
      children: []
    },
    {
      id: "footer-004",
      title: "FAQ",
      url: "/faq",
      type: "internal",
      position: 4,
      isActive: true,
      children: []
    },
    {
      id: "footer-005",
      title: "Hubungi Kami",
      url: "/contact",
      type: "internal",
      position: 5,
      isActive: true,
      children: []
    }
  ],
  sidebarMenu: [
    {
      id: "sidebar-001",
      title: "Kategori Populer",
      url: "/category/popular",
      type: "internal",
      position: 1,
      isActive: true,
      children: []
    },
    {
      id: "sidebar-002",
      title: "Produk Unggulan",
      url: "/featured-products",
      type: "internal",
      position: 2,
      isActive: true,
      children: []
    },
    {
      id: "sidebar-003",
      title: "Flash Sale",
      url: "/flash-sale",
      type: "internal",
      position: 3,
      isActive: true,
      children: []
    }
  ]
};

export default function ContentNavigationPage() {
  const [activeMenu, setActiveMenu] = useState<"main" | "footer" | "sidebar">("main")
  const [expandedMenuId, setExpandedMenuId] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  
  // Get active menu data
  const getActiveMenuData = () => {
    switch (activeMenu) {
      case "main":
        return navigationData.mainMenu
      case "footer":
        return navigationData.footerMenu
      case "sidebar":
        return navigationData.sidebarMenu
      default:
        return navigationData.mainMenu
    }
  }
  
  // Filter menu items
  const filteredMenuItems = getActiveMenuData().filter(menu => 
    menu.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    menu.url.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  // Toggle expanded menu
  const toggleExpandMenu = (menuId: string) => {
    if (expandedMenuId === menuId) {
      setExpandedMenuId(null)
    } else {
      setExpandedMenuId(menuId)
    }
  }

  // Render menu items recursively
  const renderMenuItems = (items: any[], level = 0) => {
    return items.map(item => (
      <div key={item.id} className="mb-2">
        <Card className={`hover:shadow-md transition-shadow ${level > 0 ? 'ml-6' : ''}`}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {item.children && item.children.length > 0 && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="w-6 h-6 p-0 mr-2"
                    onClick={() => toggleExpandMenu(item.id)}
                  >
                    {expandedMenuId === item.id ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                  </Button>
                )}
                {level === 0 && (
                  <div className="bg-primary/10 text-primary p-2 rounded-md mr-3">
                    {item.icon === "home" ? (
                      <Home className="h-4 w-4" />
                    ) : (
                      <MenuSquare className="h-4 w-4" />
                    )}
                  </div>
                )}
                <div className="flex-1">
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{item.title}</span>
                    {item.type === "external" && (
                      <ExternalLink className="h-3.5 w-3.5 text-muted-foreground" />
                    )}
                  </div>
                  <span className="text-sm text-muted-foreground">{item.url}</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={item.isActive ? "outline" : "secondary"} className={item.isActive ? "bg-green-100 text-green-800" : ""}>
                  {item.isActive ? "Aktif" : "Nonaktif"}
                </Badge>
                <div className="flex gap-1">
                  <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                    <ChevronUp className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <PencilIcon className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="text-red-600">
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {expandedMenuId === item.id && item.children && item.children.length > 0 && (
          <div className="mt-2">
            {renderMenuItems(item.children, level + 1)}
          </div>
        )}
      </div>
    ))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/content">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Navigasi</h1>
            <p className="text-muted-foreground">
              Kelola menu navigasi website
            </p>
          </div>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Tambah Menu Baru
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Menu Utama</CardTitle>
            <Grid className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{navigationData.mainMenu.length}</div>
            <p className="text-xs text-muted-foreground">
              {navigationData.mainMenu.reduce((count, menu) => count + (menu.children?.length || 0), 0)} submenu
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Menu Footer</CardTitle>
            <ListTree className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{navigationData.footerMenu.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Menu Sidebar</CardTitle>
            <LayoutDashboard className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{navigationData.sidebarMenu.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex border-b">
        <button
          className={`px-4 py-2 font-medium ${
            activeMenu === "main" 
              ? "border-b-2 border-primary text-primary" 
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveMenu("main")}
        >
          Menu Utama
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeMenu === "footer" 
              ? "border-b-2 border-primary text-primary" 
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveMenu("footer")}
        >
          Menu Footer
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeMenu === "sidebar" 
              ? "border-b-2 border-primary text-primary" 
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveMenu("sidebar")}
        >
          Menu Sidebar
        </button>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari menu..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <MoveVertical className="h-4 w-4 mr-2" />
              Atur Urutan
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Menu List */}
      <div className="space-y-2">
        {filteredMenuItems.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <MenuSquare className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Tidak ada menu ditemukan</h3>
              <p className="text-muted-foreground mb-4 text-center">
                Tidak ada menu yang cocok dengan pencarian Anda
              </p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Menu Baru
              </Button>
            </CardContent>
          </Card>
        ) : (
          renderMenuItems(filteredMenuItems)
        )}
      </div>

      {/* Settings Button */}
      <div className="mt-8">
        <Button variant="outline" asChild>
          <Link href="/tenant/dashboard/content/navigation/settings">
            <Settings className="h-4 w-4 mr-2" />
            Pengaturan Navigasi
          </Link>
        </Button>
      </div>
    </div>
  )
} 