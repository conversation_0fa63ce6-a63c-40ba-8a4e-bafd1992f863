"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, X } from "lucide-react"
import { productsAPI, type CreateProductData, type UpdateProductData } from "@/lib/api/products"
import { storesAPI, type Store } from "@/lib/api/stores"
import { FileUpload } from "@/components/ui/file-upload"

interface ProductFormProps {
  id?: string
}

export function ProductForm({ id }: ProductFormProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const initialStoreId = searchParams.get("storeId") || ""

  const [loading, setLoading] = useState(id ? true : false)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [stores, setStores] = useState<Store[]>([])
  const [loadingStores, setLoadingStores] = useState(true)
  const [productImages, setProductImages] = useState<string[]>([])

  const [formData, setFormData] = useState<CreateProductData | UpdateProductData>({
    name: "",
    description: "",
    price: 0,
    images: [],
    storeId: initialStoreId,
  })

  useEffect(() => {
    // Fetch stores
    const fetchStores = async () => {
      try {
        const storesData = await storesAPI.getAll()
        setStores(storesData)
        setLoadingStores(false)
      } catch (err) {
        console.error("Error fetching stores:", err)
        setError("Gagal memuat daftar toko. Silakan coba lagi.")
        setLoadingStores(false)
      }
    }

    fetchStores()

    // Fetch product if editing
    if (id) {
      const fetchProduct = async () => {
        try {
          const product = await productsAPI.getById(id)
          setFormData({
            name: product.name,
            description: product.description || "",
            price: product.price,
            storeId: product.storeId,
          })
          setProductImages(product.images || [])
          setLoading(false)
        } catch (err) {
          console.error("Error fetching product:", err)
          setError("Gagal memuat data produk. Silakan coba lagi.")
          setLoading(false)
        }
      }

      fetchProduct()
    }
  }, [id, initialStoreId])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    if (name === "price") {
      setFormData((prev) => ({ ...prev, [name]: Number.parseFloat(value) || 0 }))
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }))
    }
  }

  const handleStoreChange = (value: string) => {
    setFormData((prev) => ({ ...prev, storeId: value }))
  }

  const handleImageChange = (url: string) => {
    setProductImages((prev) => [...prev, url])
  }

  const handleRemoveImage = (index: number) => {
    setProductImages((prev) => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setSubmitting(true)
    setError(null)

    try {
      const dataToSubmit = {
        ...formData,
        images: productImages,
      }

      if (id) {
        await productsAPI.update(id, dataToSubmit as UpdateProductData)
      } else {
        await productsAPI.create(dataToSubmit as CreateProductData)
      }
      router.push(formData.storeId ? `/dashboard/stores/${formData.storeId}` : "/dashboard/products")
    } catch (err: any) {
      console.error("Error saving product:", err)
      setError(err.response?.data?.message || "Gagal menyimpan produk. Silakan coba lagi.")
      setSubmitting(false)
    }
  }

  if (loading || loadingStores) {
    return (
      <Card>
        <CardContent className="space-y-4 pt-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-24 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </CardFooter>
      </Card>
    )
  }

  return (
    <Card>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4 pt-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="name">Nama Produk</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Masukkan nama produk"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Deskripsi</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description || ""}
              onChange={handleChange}
              placeholder="Masukkan deskripsi produk"
              rows={4}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="price">Harga (Rp)</Label>
            <Input
              id="price"
              name="price"
              type="number"
              value={formData.price}
              onChange={handleChange}
              placeholder="0"
              min="0"
              step="1000"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="storeId">Toko</Label>
            <Select
              value={formData.storeId}
              onValueChange={handleStoreChange}
              disabled={!!initialStoreId || stores.length === 0}
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih toko" />
              </SelectTrigger>
              <SelectContent>
                {stores.map((store) => (
                  <SelectItem key={store.id} value={store.id}>
                    {store.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {stores.length === 0 && (
              <p className="text-sm text-destructive">Anda belum memiliki toko. Silakan buat toko terlebih dahulu.</p>
            )}
          </div>

          <div className="space-y-2">
            <Label>Gambar Produk</Label>
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {productImages.map((image, index) => (
                <div key={index} className="relative">
                  <img
                    src={image || "/placeholder.svg"}
                    alt={`Produk ${index + 1}`}
                    className="h-40 w-full object-cover rounded-md"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="absolute top-2 right-2 h-8 w-8"
                    onClick={() => handleRemoveImage(index)}
                    disabled={submitting}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}

              {productImages.length < 5 && (
                <FileUpload
                  label="Tambah Gambar"
                  folder="product-images"
                  onChange={handleImageChange}
                  onError={(err) => setError(err.message)}
                />
              )}
            </div>
            <p className="text-sm text-muted-foreground">
              Anda dapat menambahkan hingga 5 gambar produk. Ukuran maksimal 5MB per gambar.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              router.push(formData.storeId ? `/dashboard/stores/${formData.storeId}` : "/dashboard/products")
            }
          >
            Batal
          </Button>
          <Button type="submit" disabled={submitting || !formData.storeId || stores.length === 0}>
            {submitting ? "Menyimpan..." : id ? "Simpan Perubahan" : "Tambah Produk"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
