"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  SearchIcon,
  BookIcon,
  HelpCircleIcon,
  FileTextIcon,
  FolderIcon,
  BarChart3Icon,
  CheckIcon,
  XIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  FileIcon,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"

// Mock data
const mockArticles = [
  {
    id: 1,
    title: "Getting Started with SellZio",
    category: "Getting Started",
    status: "published",
    views: 1245,
    lastUpdated: "2023-05-15",
  },
  {
    id: 2,
    title: "How to Create Your First Store",
    category: "Stores",
    status: "published",
    views: 987,
    lastUpdated: "2023-05-20",
  },
  {
    id: 3,
    title: "Managing Product Inventory",
    category: "Products",
    status: "published",
    views: 756,
    lastUpdated: "2023-06-01",
  },
  {
    id: 4,
    title: "Setting Up Payment Methods",
    category: "Payments",
    status: "draft",
    views: 0,
    lastUpdated: "2023-06-10",
  },
  {
    id: 5,
    title: "Understanding Analytics Dashboard",
    category: "Analytics",
    status: "published",
    views: 543,
    lastUpdated: "2023-06-15",
  },
]

const mockCategories = [
  { id: 1, name: "Getting Started", articleCount: 5 },
  { id: 2, name: "Stores", articleCount: 8 },
  { id: 3, name: "Products", articleCount: 12 },
  { id: 4, name: "Payments", articleCount: 6 },
  { id: 5, name: "Analytics", articleCount: 4 },
  { id: 6, name: "Account Management", articleCount: 7 },
]

const mockFaqs = [
  {
    id: 1,
    question: "How do I reset my password?",
    answer: "You can reset your password by clicking on the 'Forgot Password' link on the login page.",
    category: "Account Management",
    status: "published",
  },
  {
    id: 2,
    question: "How do I create a new store?",
    answer: "To create a new store, go to the Stores section in your dashboard and click on the 'Create Store' button.",
    category: "Stores",
    status: "published",
  },
  {
    id: 3,
    question: "Can I have multiple stores?",
    answer:
      "Yes, depending on your subscription plan, you can create and manage multiple stores from a single account.",
    category: "Stores",
    status: "published",
  },
  {
    id: 4,
    question: "How do I add products to my store?",
    answer: "To add products, navigate to the Products section in your store dashboard and click on 'Add New Product'.",
    category: "Products",
    status: "published",
  },
  {
    id: 5,
    question: "What payment methods are supported?",
    answer:
      "We support various payment methods including credit cards, PayPal, and bank transfers. The available methods may vary by region.",
    category: "Payments",
    status: "draft",
  },
]

const mockGuides = [
  { id: 1, title: "Complete Store Setup Guide", sections: 8, status: "published", lastUpdated: "2023-05-10" },
  { id: 2, title: "Product Management Guide", sections: 6, status: "published", lastUpdated: "2023-05-25" },
  { id: 3, title: "Marketing Tools Guide", sections: 5, status: "draft", lastUpdated: "2023-06-05" },
  { id: 4, title: "Analytics & Reporting Guide", sections: 4, status: "published", lastUpdated: "2023-06-20" },
]

const mockSearchTerms = [
  { term: "password reset", searches: 245, successRate: 92 },
  { term: "create store", searches: 189, successRate: 88 },
  { term: "add product", searches: 156, successRate: 95 },
  { term: "payment setup", searches: 134, successRate: 76 },
  { term: "shipping settings", searches: 112, successRate: 82 },
  { term: "tax configuration", searches: 98, successRate: 65 },
  { term: "discount codes", searches: 87, successRate: 91 },
]

export function HelpCenter() {
  const [articles, setArticles] = useState(mockArticles)
  const [categories, setCategories] = useState(mockCategories)
  const [faqs, setFaqs] = useState(mockFaqs)
  const [guides, setGuides] = useState(mockGuides)
  const [searchTerms, setSearchTerms] = useState(mockSearchTerms)
  const [selectedArticle, setSelectedArticle] = useState(null)
  const [selectedFaq, setSelectedFaq] = useState(null)
  const [selectedCategory, setSelectedCategory] = useState(null)
  const [selectedGuide, setSelectedGuide] = useState(null)
  const [activeTab, setActiveTab] = useState("knowledge-base")
  const [isLoading, setIsLoading] = useState(false)

  // Handlers for Knowledge Base
  const handleCreateArticle = () => {
    // Implementation for creating a new article
    console.log("Creating new article")
  }

  const handleEditArticle = (article) => {
    setSelectedArticle(article)
    // Implementation for editing an article
    console.log("Editing article:", article)
  }

  const handleDeleteArticle = (articleId) => {
    // Implementation for deleting an article
    console.log("Deleting article:", articleId)
    setArticles(articles.filter((article) => article.id !== articleId))
  }

  // Handlers for FAQs
  const handleCreateFaq = () => {
    // Implementation for creating a new FAQ
    console.log("Creating new FAQ")
  }

  const handleEditFaq = (faq) => {
    setSelectedFaq(faq)
    // Implementation for editing a FAQ
    console.log("Editing FAQ:", faq)
  }

  const handleDeleteFaq = (faqId) => {
    // Implementation for deleting a FAQ
    console.log("Deleting FAQ:", faqId)
    setFaqs(faqs.filter((faq) => faq.id !== faqId))
  }

  // Handlers for Categories
  const handleCreateCategory = () => {
    // Implementation for creating a new category
    console.log("Creating new category")
  }

  const handleEditCategory = (category) => {
    setSelectedCategory(category)
    // Implementation for editing a category
    console.log("Editing category:", category)
  }

  const handleDeleteCategory = (categoryId) => {
    // Implementation for deleting a category
    console.log("Deleting category:", categoryId)
    setCategories(categories.filter((category) => category.id !== categoryId))
  }

  // Handlers for User Guides
  const handleCreateGuide = () => {
    // Implementation for creating a new guide
    console.log("Creating new guide")
  }

  const handleEditGuide = (guide) => {
    setSelectedGuide(guide)
    // Implementation for editing a guide
    console.log("Editing guide:", guide)
  }

  const handleDeleteGuide = (guideId) => {
    // Implementation for deleting a guide
    console.log("Deleting guide:", guideId)
    setGuides(guides.filter((guide) => guide.id !== guideId))
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="knowledge-base" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="knowledge-base" className="flex items-center gap-2">
            <BookIcon className="h-4 w-4" />
            <span className="hidden sm:inline">Knowledge Base</span>
          </TabsTrigger>
          <TabsTrigger value="faqs" className="flex items-center gap-2">
            <HelpCircleIcon className="h-4 w-4" />
            <span className="hidden sm:inline">FAQs</span>
          </TabsTrigger>
          <TabsTrigger value="categories" className="flex items-center gap-2">
            <FolderIcon className="h-4 w-4" />
            <span className="hidden sm:inline">Categories</span>
          </TabsTrigger>
          <TabsTrigger value="guides" className="flex items-center gap-2">
            <FileTextIcon className="h-4 w-4" />
            <span className="hidden sm:inline">User Guides</span>
          </TabsTrigger>
          <TabsTrigger value="search-analytics" className="flex items-center gap-2">
            <BarChart3Icon className="h-4 w-4" />
            <span className="hidden sm:inline">Search Analytics</span>
          </TabsTrigger>
        </TabsList>

        {/* Knowledge Base Tab */}
        <TabsContent value="knowledge-base" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Knowledge Base Articles</h2>
            <Button onClick={handleCreateArticle}>
              <PlusIcon className="h-4 w-4 mr-2" />
              New Article
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input type="search" placeholder="Search articles..." className="pl-8" />
            </div>
            <Select defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.name.toLowerCase()}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Views</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {articles.map((article) => (
                    <TableRow key={article.id}>
                      <TableCell className="font-medium">{article.title}</TableCell>
                      <TableCell>{article.category}</TableCell>
                      <TableCell>
                        <Badge variant={article.status === "published" ? "default" : "secondary"}>
                          {article.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">{article.views.toLocaleString()}</TableCell>
                      <TableCell>{article.lastUpdated}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleEditArticle(article)}>
                            <PencilIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleDeleteArticle(article.id)}>
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {selectedArticle && (
            <Card>
              <CardHeader>
                <CardTitle>Edit Article</CardTitle>
                <CardDescription>Make changes to the selected article</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input id="title" defaultValue={selectedArticle.title} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select defaultValue={selectedArticle.category.toLowerCase()}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.name.toLowerCase()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="content">Content</Label>
                  <Textarea id="content" rows={10} defaultValue="This is the article content..." />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select defaultValue={selectedArticle.status}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setSelectedArticle(null)}>
                  Cancel
                </Button>
                <Button>Save Changes</Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        {/* FAQs Tab */}
        <TabsContent value="faqs" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Frequently Asked Questions</h2>
            <Button onClick={handleCreateFaq}>
              <PlusIcon className="h-4 w-4 mr-2" />
              New FAQ
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input type="search" placeholder="Search FAQs..." className="pl-8" />
            </div>
            <Select defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.name.toLowerCase()}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Question</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {faqs.map((faq) => (
                    <TableRow key={faq.id}>
                      <TableCell className="font-medium">{faq.question}</TableCell>
                      <TableCell>{faq.category}</TableCell>
                      <TableCell>
                        <Badge variant={faq.status === "published" ? "default" : "secondary"}>{faq.status}</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleEditFaq(faq)}>
                            <PencilIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleDeleteFaq(faq.id)}>
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {selectedFaq && (
            <Card>
              <CardHeader>
                <CardTitle>Edit FAQ</CardTitle>
                <CardDescription>Make changes to the selected FAQ</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="question">Question</Label>
                  <Input id="question" defaultValue={selectedFaq.question} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="answer">Answer</Label>
                  <Textarea id="answer" rows={5} defaultValue={selectedFaq.answer} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="faq-category">Category</Label>
                  <Select defaultValue={selectedFaq.category.toLowerCase()}>
                    <SelectTrigger id="faq-category">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.name.toLowerCase()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="faq-status">Status</Label>
                  <Select defaultValue={selectedFaq.status}>
                    <SelectTrigger id="faq-status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setSelectedFaq(null)}>
                  Cancel
                </Button>
                <Button>Save Changes</Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        {/* Categories Tab */}
        <TabsContent value="categories" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Help Center Categories</h2>
            <Button onClick={handleCreateCategory}>
              <PlusIcon className="h-4 w-4 mr-2" />
              New Category
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categories.map((category) => (
              <Card key={category.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <CardTitle>{category.name}</CardTitle>
                  <CardDescription>{category.articleCount} articles</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">ID: {category.id}</span>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="icon" onClick={() => handleEditCategory(category)}>
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDeleteCategory(category.id)}>
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-2">
                  <Button variant="outline" className="w-full">
                    View Articles
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          {selectedCategory && (
            <Card>
              <CardHeader>
                <CardTitle>Edit Category</CardTitle>
                <CardDescription>Make changes to the selected category</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="category-name">Category Name</Label>
                  <Input id="category-name" defaultValue={selectedCategory.name} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category-description">Description</Label>
                  <Textarea id="category-description" rows={3} defaultValue="Category description..." />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category-icon">Icon</Label>
                  <Select defaultValue="folder">
                    <SelectTrigger id="category-icon">
                      <SelectValue placeholder="Select icon" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="folder">Folder</SelectItem>
                      <SelectItem value="book">Book</SelectItem>
                      <SelectItem value="file">File</SelectItem>
                      <SelectItem value="help">Help</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category-order">Display Order</Label>
                  <Input id="category-order" type="number" defaultValue="1" />
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setSelectedCategory(null)}>
                  Cancel
                </Button>
                <Button>Save Changes</Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        {/* User Guides Tab */}
        <TabsContent value="guides" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">User Guides</h2>
            <Button onClick={handleCreateGuide}>
              <PlusIcon className="h-4 w-4 mr-2" />
              New Guide
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead className="text-center">Sections</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {guides.map((guide) => (
                    <TableRow key={guide.id}>
                      <TableCell className="font-medium">{guide.title}</TableCell>
                      <TableCell className="text-center">{guide.sections}</TableCell>
                      <TableCell>
                        <Badge variant={guide.status === "published" ? "default" : "secondary"}>{guide.status}</Badge>
                      </TableCell>
                      <TableCell>{guide.lastUpdated}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleEditGuide(guide)}>
                            <PencilIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleDeleteGuide(guide.id)}>
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {selectedGuide && (
            <Card>
              <CardHeader>
                <CardTitle>Edit User Guide</CardTitle>
                <CardDescription>Make changes to the selected guide</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="guide-title">Title</Label>
                  <Input id="guide-title" defaultValue={selectedGuide.title} />
                </div>
                <div className="space-y-2">
                  <Label>Sections</Label>
                  <div className="border rounded-md">
                    <div className="p-4 border-b flex justify-between items-center">
                      <div className="font-medium">1. Introduction</div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="icon">
                          <ArrowUpIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <ArrowDownIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-4 border-b flex justify-between items-center">
                      <div className="font-medium">2. Getting Started</div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="icon">
                          <ArrowUpIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <ArrowDownIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-4 flex justify-center">
                      <Button variant="outline">
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Add Section
                      </Button>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="guide-status">Status</Label>
                  <Select defaultValue={selectedGuide.status}>
                    <SelectTrigger id="guide-status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setSelectedGuide(null)}>
                  Cancel
                </Button>
                <Button>Save Changes</Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        {/* Search Analytics Tab */}
        <TabsContent value="search-analytics" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Search Analytics</h2>
            <div className="flex gap-2">
              <Select defaultValue="7days">
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Time period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">Last 7 days</SelectItem>
                  <SelectItem value="30days">Last 30 days</SelectItem>
                  <SelectItem value="90days">Last 90 days</SelectItem>
                  <SelectItem value="year">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline">
                <FileIcon className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Total Searches</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">1,245</div>
                <p className="text-sm text-muted-foreground">+12% from last period</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Success Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">84%</div>
                <p className="text-sm text-muted-foreground">+3% from last period</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>No Results Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">16%</div>
                <p className="text-sm text-muted-foreground">-3% from last period</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Top Search Terms</CardTitle>
              <CardDescription>Most frequently searched terms and their success rates</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Search Term</TableHead>
                    <TableHead className="text-right">Searches</TableHead>
                    <TableHead className="text-right">Success Rate</TableHead>
                    <TableHead>Success Indicator</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {searchTerms.map((term) => (
                    <TableRow key={term.term}>
                      <TableCell className="font-medium">{term.term}</TableCell>
                      <TableCell className="text-right">{term.searches}</TableCell>
                      <TableCell className="text-right">{term.successRate}%</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Progress value={term.successRate} className="h-2 flex-1" />
                          <span className="ml-2 w-8">
                            {term.successRate >= 80 ? (
                              <CheckIcon className="h-4 w-4 text-green-500" />
                            ) : term.successRate >= 60 ? (
                              <div className="h-4 w-4 rounded-full bg-yellow-500" />
                            ) : (
                              <XIcon className="h-4 w-4 text-red-500" />
                            )}
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Search Terms with No Results</CardTitle>
              <CardDescription>Terms that users searched for but found no matching content</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Search Term</TableHead>
                    <TableHead className="text-right">Searches</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">refund policy</TableCell>
                    <TableCell className="text-right">42</TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm">
                        Create Content
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">api documentation</TableCell>
                    <TableCell className="text-right">38</TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm">
                        Create Content
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">custom domain</TableCell>
                    <TableCell className="text-right">27</TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm">
                        Create Content
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
