"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import { ImageSlider } from "./image-slider"
import { FlashSale } from "./flash-sale"
import { VideoCard } from "./video-card"
import {
  MallBadge,
  StarBadge,
  StarLiteBadge,
  TermurahDiTokoBadge,
  CodBadge,
  DiscountBadge,
  LiveBadge,
  LiveCornerBadge,
  ShippingBadge,
  RatingStars,
  TerlarisBadge,
  KomisiXtraBadge,
} from "./badges"

export type FeedCardType = "standard" | "flash-sale" | "video" | "image-slider" | "promo" | "image-only"

export type BadgeType = "mall" | "star" | "star-lite" | "termurah" | "terlaris" | "komisi-xtra" | "none"

export interface FeedCardProps {
  type: FeedCardType
  name: string
  price: string
  originalPrice?: string
  discount?: string
  image: string
  images?: { src: string; alt: string }[]
  videoThumbnail?: string
  videoSrc?: string
  badgeType?: BadgeType
  rating?: number
  sold?: number
  hasCod?: boolean
  isLive?: boolean
  flashSale?: {
    endTime: Date
    remaining: number
    total: number
  }
  link?: string
}

export const FeedCard: React.FC<FeedCardProps> = ({
  type,
  name,
  price,
  originalPrice,
  discount,
  image,
  images,
  videoThumbnail,
  videoSrc,
  badgeType = "none",
  rating = 0,
  sold = 0,
  hasCod = false,
  isLive = false,
  flashSale,
  link,
}) => {
  const [isLoaded, setIsLoaded] = useState(false)

  // Render promo or image-only card
  if (type === "promo" || type === "image-only") {
    const cardContent = (
      <div className="relative w-full overflow-hidden rounded-sm">
        {images && images.length > 0 ? (
          <ImageSlider images={images} />
        ) : (
          <div className="relative pt-[177.78%]">
            <Image
              src={image || "/placeholder.svg"}
              alt={name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 16vw"
            />
          </div>
        )}
      </div>
    )

    return link ? (
      <a href={link} className="block w-full">
        {cardContent}
      </a>
    ) : (
      cardContent
    )
  }

  // Render video card
  if (type === "video" && videoThumbnail) {
    return (
      <VideoCard
        thumbnailSrc={videoThumbnail}
        videoSrc={videoSrc}
        isLive={isLive}
        productImage={image}
        productName={name}
        rating={rating}
        sold={sold}
        price={price}
        originalPrice={originalPrice}
      />
    )
  }

  // Render flash sale card
  if (type === "flash-sale" && flashSale) {
    return (
      <div className="bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 cursor-pointer w-full">
        <div className="relative">
          <div className="relative pt-[100%] overflow-hidden">
            <Image
              src={image || "/placeholder.svg"}
              alt={name}
              fill
              className={`object-contain transition-opacity duration-300 ${isLoaded ? "opacity-100" : "opacity-0"}`}
              onLoad={() => setIsLoaded(true)}
              sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 16vw"
            />
            {!isLoaded && <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>}
          </div>
          <FlashSale
            endTime={flashSale.endTime}
            originalPrice={originalPrice || ""}
            currentPrice={price}
            remaining={flashSale.remaining || 0}
            total={flashSale.total || 100}
          />
          {discount && <DiscountBadge discount={discount} />}
          {hasCod && <CodBadge />}
          {isLive && <LiveCornerBadge />}
        </div>

        <div className="p-3">
          <h3 className="text-xs text-gray-800 line-clamp-2 mb-1.5">
            {/* Render only one badge based on priority */}
            {isLive ? (
              <LiveBadge />
            ) : badgeType === "mall" ? (
              <MallBadge />
            ) : badgeType === "star" ? (
              <StarBadge />
            ) : badgeType === "star-lite" ? (
              <StarLiteBadge />
            ) : null}
            {name}
          </h3>

          {badgeType === "termurah" && <TermurahDiTokoBadge />}
          {badgeType === "terlaris" && <TerlarisBadge />}
          {badgeType === "komisi-xtra" && <KomisiXtraBadge />}

          <div className="flex items-center text-xs text-gray-500 mb-1.5">
            <RatingStars rating={rating} />
            <div className="mx-1 w-[1px] h-2 bg-gray-300"></div>
            <span>Terjual {sold}</span>
          </div>

          <ShippingBadge type="instan" />

          <div className="flex items-center">
            <div className="text-sm font-bold text-[#ee4d2d]">{price}</div>
            {originalPrice && (
              <div className="ml-1 text-xs text-gray-400 line-through overflow-hidden text-ellipsis max-w-[40%]">
                {originalPrice}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Render image slider card
  if (type === "image-slider" && images && images.length > 0) {
    return (
      <a href={link || "#"} className="block w-full">
        <div className="bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 cursor-pointer w-full">
          <div className="relative">
            <ImageSlider images={images} />
          </div>
        </div>
      </a>
    )
  }

  // Render standard card
  return (
    <div className="bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-0.5 cursor-pointer w-full">
      <div className="relative pt-[100%] overflow-hidden">
        <Image
          src={image || "/placeholder.svg"}
          alt={name}
          fill
          className={`object-contain transition-opacity duration-300 ${isLoaded ? "opacity-100" : "opacity-0"}`}
          onLoad={() => setIsLoaded(true)}
          sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 16vw"
        />
        {!isLoaded && <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>}
        {discount && <DiscountBadge discount={discount} />}
        {hasCod && <CodBadge />}
        {isLive && <LiveCornerBadge />}
      </div>

      <div className="p-3">
        <h3 className="text-xs text-gray-800 line-clamp-2 mb-1.5">
          {/* Render only one badge based on priority */}
          {isLive ? (
            <LiveBadge />
          ) : badgeType === "mall" ? (
            <MallBadge />
          ) : badgeType === "star" ? (
            <StarBadge />
          ) : badgeType === "star-lite" ? (
            <StarLiteBadge />
          ) : null}
          {name}
        </h3>

        {badgeType === "termurah" && <TermurahDiTokoBadge />}
        {badgeType === "terlaris" && <TerlarisBadge />}
        {badgeType === "komisi-xtra" && <KomisiXtraBadge />}

        <div className="flex items-center text-xs text-gray-500 mb-1.5">
          <RatingStars rating={rating} />
          <div className="mx-1 w-[1px] h-2 bg-gray-300"></div>
          <span>Terjual {sold}</span>
        </div>

        <ShippingBadge type="instan" />

        <div className="flex items-center">
          <div className="text-sm font-bold text-[#ee4d2d]">{price}</div>
          {originalPrice && (
            <div className="ml-1 text-xs text-gray-400 line-through overflow-hidden text-ellipsis max-w-[40%]">
              {originalPrice}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
