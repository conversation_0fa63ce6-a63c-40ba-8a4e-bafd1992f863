"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Wallet,
  CreditCard,
  Receipt,
  DollarSign,
  FileText,
  BarChart3,
  Calendar,
  RefreshCw,
  ChevronDown
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Data dummy untuk ringkasan keuangan
const financialSummary = {
  availableBalance: 4500000,
  pendingBalance: 3350000,
  totalRevenue: 45250000,
  totalExpenses: 27530000,
  netProfit: 17720000,
  profitMargin: 39.16,
  nextPayoutDate: "2024-05-30T00:00:00",
  nextPayoutAmount: 4500000,
  transactionCount: 284,
  recentTransactions: [
    {
      id: "TRX-001",
      type: "payment",
      amount: 850000,
      date: "2024-05-20T14:30:00",
      description: "Pembayaran untuk Produk Premium"
    },
    {
      id: "TRX-002",
      type: "payment",
      amount: 1200000,
      date: "2024-05-19T10:15:00",
      description: "Pembayaran untuk Paket Basic + Add-on"
    },
    {
      id: "TRX-003",
      type: "refund",
      amount: 500000,
      date: "2024-05-18T16:45:00",
      description: "Pengembalian dana untuk Paket Starter"
    }
  ],
  recentPayouts: [
    {
      id: "PYT-001",
      amount: 4500000,
      date: "2024-05-15T16:30:00",
      description: "Pembayaran periode 1-15 Mei 2024"
    },
    {
      id: "PYT-002",
      amount: 6200000,
      date: "2024-04-30T15:45:00",
      description: "Pembayaran periode 16-30 April 2024"
    }
  ]
}

// Format mata uang
function formatCurrency(number: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(number)
}

// Format persentase
function formatPercentage(number: number) {
  return `${number.toFixed(2)}%`
}

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

export default function FinancialDashboardPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Keuangan</h1>
          <p className="text-muted-foreground">
            Kelola keuangan, transaksi, dan laporan bisnis Anda
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Perbarui Data
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button>
                <DollarSign className="h-4 w-4 mr-2" />
                Tindakan
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Link href="/tenant/dashboard/financial/payouts" className="flex items-center">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Ajukan Penarikan
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="/tenant/dashboard/financial/statements" className="flex items-center">
                  <FileText className="h-4 w-4 mr-2" />
                  Buat Laporan
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Balance Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo Tersedia</CardTitle>
            <Wallet className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(financialSummary.availableBalance)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Dapat ditarik kapan saja
            </p>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="link" size="sm" className="px-0" asChild>
              <Link href="/tenant/dashboard/financial/payouts">
                Ajukan Penarikan
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo Tertahan</CardTitle>
            <Wallet className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{formatCurrency(financialSummary.pendingBalance)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Dalam masa tunggu pelepasan
            </p>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="link" size="sm" className="px-0" asChild>
              <Link href="/tenant/dashboard/financial/payouts">
                Lihat Rincian
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pembayaran Berikutnya</CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatCurrency(financialSummary.nextPayoutAmount)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Pada {formatDate(financialSummary.nextPayoutDate)}
            </p>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="link" size="sm" className="px-0" asChild>
              <Link href="/tenant/dashboard/financial/payouts">
                Jadwal Pembayaran
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Financial Sections */}
      <div className="grid gap-6 md:grid-cols-3">
        {/* Payouts Section */}
        <Card className="col-span-1">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Pembayaran</CardTitle>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/tenant/dashboard/financial/payouts">
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
            <CardDescription>Riwayat dan jadwal pembayaran</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {financialSummary.recentPayouts.map(payout => (
                <div key={payout.id} className="flex items-start justify-between border-b pb-3">
                  <div>
                    <div className="font-medium">{payout.id}</div>
                    <div className="text-sm text-muted-foreground">{formatDate(payout.date)}</div>
                    <div className="text-sm mt-1 line-clamp-1">{payout.description}</div>
                  </div>
                  <div className="font-medium text-right text-green-600">
                    {formatCurrency(payout.amount)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/tenant/dashboard/financial/payouts">
                <CreditCard className="h-4 w-4 mr-2" />
                Kelola Pembayaran
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Transactions Section */}
        <Card className="col-span-1">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Transaksi</CardTitle>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/tenant/dashboard/financial/transactions">
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
            <CardDescription>Riwayat transaksi terbaru</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {financialSummary.recentTransactions.map(transaction => (
                <div key={transaction.id} className="flex items-start justify-between border-b pb-3">
                  <div>
                    <div className="font-medium">{transaction.id}</div>
                    <div className="text-sm text-muted-foreground">{formatDate(transaction.date)}</div>
                    <div className="text-sm mt-1 line-clamp-1">{transaction.description}</div>
                  </div>
                  <div className={`font-medium text-right ${transaction.type === 'payment' ? 'text-green-600' : 'text-red-600'}`}>
                    {transaction.type === 'payment' ? '+' : '-'}{formatCurrency(transaction.amount)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/tenant/dashboard/financial/transactions">
                <Receipt className="h-4 w-4 mr-2" />
                Lihat Semua Transaksi
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Statements Section */}
        <Card className="col-span-1">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Laporan Keuangan</CardTitle>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/tenant/dashboard/financial/statements">
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
            <CardDescription>Ringkasan keuangan periode ini</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="text-sm">Total Pendapatan</div>
                <div className="font-medium text-green-600">{formatCurrency(financialSummary.totalRevenue)}</div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-sm">Total Pengeluaran</div>
                <div className="font-medium text-red-600">{formatCurrency(financialSummary.totalExpenses)}</div>
              </div>
              <div className="flex items-center justify-between border-t pt-3">
                <div className="font-medium">Laba Bersih</div>
                <div className="font-medium">{formatCurrency(financialSummary.netProfit)}</div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-sm">Margin Keuntungan</div>
                <div className="font-medium text-blue-600">{formatPercentage(financialSummary.profitMargin)}</div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-sm">Jumlah Transaksi</div>
                <div className="font-medium">{financialSummary.transactionCount}</div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/tenant/dashboard/financial/statements">
                <FileText className="h-4 w-4 mr-2" />
                Lihat Laporan Lengkap
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Financial Links */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Pembayaran</CardTitle>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-sm text-muted-foreground">
              Kelola pembayaran dan metode penarikan dana Anda
            </p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/tenant/dashboard/financial/payouts">
                <CreditCard className="h-4 w-4 mr-2" />
                Buka Pembayaran
              </Link>
            </Button>
          </CardFooter>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Transaksi</CardTitle>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-sm text-muted-foreground">
              Lihat dan filter semua transaksi keuangan
            </p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/tenant/dashboard/financial/transactions">
                <Receipt className="h-4 w-4 mr-2" />
                Buka Transaksi
              </Link>
            </Button>
          </CardFooter>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Laporan</CardTitle>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-sm text-muted-foreground">
              Akses dan unduh laporan keuangan Anda
            </p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/tenant/dashboard/financial/statements">
                <FileText className="h-4 w-4 mr-2" />
                Buka Laporan
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
} 