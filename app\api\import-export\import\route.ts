import { NextRequest, NextResponse } from 'next/server';
import { productImportExportService } from '@/lib/services/product-import-export';

// POST - Start import job
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const optionsStr = formData.get('options') as string;
    
    if (!file) {
      return NextResponse.json(
        { error: 'File is required' },
        { status: 400 }
      );
    }
    
    let options = {};
    if (optionsStr) {
      try {
        options = JSON.parse(optionsStr);
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid options format' },
          { status: 400 }
        );
      }
    }
    
    // Validate file type
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only CSV and Excel files are allowed.' },
        { status: 400 }
      );
    }
    
    // For development, we'll simulate file upload
    // In production, you would upload to cloud storage
    const fileUrl = `/uploads/${file.name}`;
    
    const job = await productImportExportService.createImportJob(
      file.name,
      fileUrl,
      file.size,
      {}, // mapping config
      options
    );
    
    // Start processing the import job
    // In a real implementation, this would be handled by a background worker
    setTimeout(async () => {
      try {
        await productImportExportService.updateJobStatus(job.id, 'processing', 10);
        
        // Simulate processing steps
        await new Promise(resolve => setTimeout(resolve, 2000));
        await productImportExportService.updateJobStatus(job.id, 'processing', 50);
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        await productImportExportService.updateJobStatus(job.id, 'processing', 80);
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        await productImportExportService.updateJobStatus(job.id, 'completed', 100);
        
        // Update job with results
        await productImportExportService.updateJob(job.id, {
          processed_count: 25,
          success_count: 23,
          error_count: 2,
          completed_at: new Date().toISOString(),
          result_summary: {
            total_rows: 25,
            imported: 23,
            skipped: 0,
            errors: 2,
            warnings: 1
          }
        });
      } catch (error) {
        console.error('Error processing import:', error);
        await productImportExportService.updateJobStatus(job.id, 'failed', 0);
      }
    }, 1000);
    
    return NextResponse.json(job, { status: 201 });
  } catch (error) {
    console.error('Error starting import:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to start import';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
