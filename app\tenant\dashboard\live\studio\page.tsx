"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Video,
  MicIcon,
  ScreenShare,
  Settings,
  Users,
  MessageSquare,
  PanelRight,
  Play,
  Eye,
  Send,
  Link2,
  Copy,
  Layout,
  LayoutGrid,
  User,
  Share,
  AlertCircle
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"

// Data untuk jadwal siaran yang sedang dijalankan
const currentStream = {
  id: "stream-002",
  title: "Workshop: Tips Pemasaran Digital",
  scheduledFor: "2024-06-05T10:30:00",
  status: "scheduled",
  description: "Workshop interaktif tentang strategi pemasaran digital terkini",
  isLive: false,
  viewerCount: 0,
  duration: "00:00:00",
  startedAt: null,
  settings: {
    privacy: "public",
    chat: true,
    recording: true,
    allowQuestions: true,
    lowLatency: true,
    enableComments: true
  }
}

// Data untuk pesan chat
const chatMessages = [
  {
    id: "msg-001",
    userId: "user-123",
    userName: "Ahmad",
    message: "Halo! Apakah workshop akan dimulai tepat waktu?",
    timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString()
  },
  {
    id: "msg-002",
    userId: "user-456",
    userName: "Siti",
    message: "Saya sudah tidak sabar untuk belajar tentang strategi pemasaran digital terbaru!",
    timestamp: new Date(Date.now() - 1000 * 60 * 3).toISOString()
  },
  {
    id: "msg-003",
    userId: "user-789",
    userName: "Budi",
    message: "Berharap akan ada sesi tanya jawab di akhir workshop",
    timestamp: new Date(Date.now() - 1000 * 60 * 1).toISOString()
  }
]

// Format waktu
function formatTime(date: Date) {
  return date.toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function LiveStudioPage() {
  const [isLive, setIsLive] = useState(false)
  const [viewerCount, setViewerCount] = useState(0)
  const [duration, setDuration] = useState("00:00:00")
  const [chatInput, setChatInput] = useState("")
  const [streamSettings, setStreamSettings] = useState(currentStream.settings)
  const [streamTitle, setStreamTitle] = useState(currentStream.title)
  const [streamDescription, setStreamDescription] = useState(currentStream.description)
  const [selectedTab, setSelectedTab] = useState("preview")
  const [selectedLayout, setSelectedLayout] = useState("default")
  const [isMicOn, setIsMicOn] = useState(true)
  const [isCameraOn, setIsCameraOn] = useState(true)
  const [isScreenShareOn, setIsScreenShareOn] = useState(false)
  
  // Fungsi untuk memulai siaran
  const startStream = () => {
    if (!isLive) {
      setIsLive(true)
      setViewerCount(1)
      // Dalam implementasi sebenarnya, di sini akan ada logika untuk memulai streaming
    }
  }
  
  // Fungsi untuk mengakhiri siaran
  const endStream = () => {
    if (isLive) {
      setIsLive(false)
      // Dalam implementasi sebenarnya, di sini akan ada logika untuk mengakhiri streaming
    }
  }

  // Fungsi untuk mengirim pesan chat
  const sendChatMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (chatInput.trim()) {
      // Dalam implementasi sebenarnya, di sini akan ada logika untuk mengirim pesan
      setChatInput("")
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/live">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Studio</h1>
            <p className="text-muted-foreground">
              Siapkan dan jalankan siaran langsung Anda
            </p>
          </div>
        </div>
        {isLive ? (
          <div className="flex items-center gap-3">
            <Badge className="bg-red-100 text-red-800 animate-pulse px-3 py-1">
              LIVE
            </Badge>
            <div className="flex items-center gap-2 text-sm">
              <Eye className="h-4 w-4" />
              <span>{viewerCount} penonton</span>
            </div>
            <div className="text-sm">{duration}</div>
            <Button variant="destructive" onClick={endStream}>
              Akhiri Siaran
            </Button>
          </div>
        ) : (
          <Button onClick={startStream} className="bg-red-600 hover:bg-red-700">
            <Play className="h-4 w-4 mr-2" />
            Mulai Siaran
          </Button>
        )}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Stream Preview & Settings */}
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardContent className="p-0">
              <Tabs defaultValue="preview" value={selectedTab} onValueChange={setSelectedTab}>
                <TabsList className="w-full rounded-none border-b">
                  <TabsTrigger value="preview" className="flex-1">Preview</TabsTrigger>
                  <TabsTrigger value="settings" className="flex-1">Pengaturan</TabsTrigger>
                  <TabsTrigger value="audience" className="flex-1">Audiens</TabsTrigger>
                </TabsList>
                
                <TabsContent value="preview" className="m-0">
                  {/* Stream Preview */}
                  <div className="aspect-video bg-black flex items-center justify-center relative">
                    {!isCameraOn && !isScreenShareOn ? (
                      <div className="text-center">
                        <Video className="h-12 w-12 text-white/50 mx-auto mb-2" />
                        <p className="text-white/80">Kamera atau screen share tidak aktif</p>
                        <p className="text-white/50 text-sm">Aktifkan untuk melihat preview</p>
                      </div>
                    ) : (
                      <div className="text-center text-white/80">
                        {isLive ? "Siaran sedang berlangsung" : "Preview siaran"}
                      </div>
                    )}
                    
                    {/* Stream Controls */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-3">
                      <Button 
                        variant={isMicOn ? "default" : "outline"} 
                        size="icon" 
                        onClick={() => setIsMicOn(!isMicOn)}
                        className={!isMicOn ? "bg-muted/30 text-white" : ""}
                      >
                        <MicIcon className="h-5 w-5" />
                      </Button>
                      <Button 
                        variant={isCameraOn ? "default" : "outline"} 
                        size="icon"
                        onClick={() => setIsCameraOn(!isCameraOn)}
                        className={!isCameraOn ? "bg-muted/30 text-white" : ""}
                      >
                        <Video className="h-5 w-5" />
                      </Button>
                      <Button 
                        variant={isScreenShareOn ? "default" : "outline"} 
                        size="icon"
                        onClick={() => setIsScreenShareOn(!isScreenShareOn)}
                        className={!isScreenShareOn ? "bg-muted/30 text-white" : ""}
                      >
                        <ScreenShare className="h-5 w-5" />
                      </Button>
                      <Button variant="outline" size="icon" className="bg-muted/30 text-white">
                        <Settings className="h-5 w-5" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Stream Info */}
                  <div className="p-4 space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-1 block">Judul Siaran</label>
                      <Input 
                        value={streamTitle} 
                        onChange={(e) => setStreamTitle(e.target.value)}
                        placeholder="Masukkan judul siaran" 
                        disabled={isLive}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-1 block">Deskripsi</label>
                      <Textarea 
                        value={streamDescription} 
                        onChange={(e) => setStreamDescription(e.target.value)}
                        placeholder="Tambahkan deskripsi siaran Anda" 
                        rows={3}
                        disabled={isLive}
                      />
                    </div>
                    
                    <div className="flex flex-col sm:flex-row gap-3">
                      <div className="flex-1">
                        <label className="text-sm font-medium mb-1 block">Privasi</label>
                        <Select 
                          disabled={isLive}
                          value={streamSettings.privacy}
                          onValueChange={(val) => setStreamSettings({...streamSettings, privacy: val})}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih pengaturan privasi" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="public">Publik</SelectItem>
                            <SelectItem value="unlisted">Tidak terdaftar</SelectItem>
                            <SelectItem value="private">Privat</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex-1">
                        <label className="text-sm font-medium mb-1 block">Layout</label>
                        <Select 
                          value={selectedLayout}
                          onValueChange={setSelectedLayout}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih layout siaran" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">Default</SelectItem>
                            <SelectItem value="side-by-side">Berdampingan</SelectItem>
                            <SelectItem value="picture-in-picture">Picture in Picture</SelectItem>
                            <SelectItem value="full-screen">Layar Penuh</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Link2 className="h-4 w-4" />
                        <span className="text-sm font-medium">Link Siaran</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input 
                          value="https://yourstore.com/live/workshop-pemasaran-digital" 
                          className="max-w-60 text-sm" 
                          readOnly
                        />
                        <Button variant="outline" size="sm">
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Share className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="settings" className="m-0 p-4 space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Pengaturan Siaran</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Rekam Siaran</h4>
                          <p className="text-sm text-muted-foreground">Rekam siaran untuk ditonton ulang</p>
                        </div>
                        <Switch 
                          checked={streamSettings.recording} 
                          onCheckedChange={(checked) => setStreamSettings({...streamSettings, recording: checked})}
                          disabled={isLive}
                        />
                      </div>
                      <Separator />
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Aktifkan Chat</h4>
                          <p className="text-sm text-muted-foreground">Izinkan penonton berinteraksi melalui chat</p>
                        </div>
                        <Switch 
                          checked={streamSettings.chat} 
                          onCheckedChange={(checked) => setStreamSettings({...streamSettings, chat: checked})}
                        />
                      </div>
                      <Separator />
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Izinkan Pertanyaan</h4>
                          <p className="text-sm text-muted-foreground">Penonton dapat mengajukan pertanyaan</p>
                        </div>
                        <Switch 
                          checked={streamSettings.allowQuestions} 
                          onCheckedChange={(checked) => setStreamSettings({...streamSettings, allowQuestions: checked})}
                        />
                      </div>
                      <Separator />
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Mode Latensi Rendah</h4>
                          <p className="text-sm text-muted-foreground">Kurangi delay siaran (membutuhkan bandwidth lebih tinggi)</p>
                        </div>
                        <Switch 
                          checked={streamSettings.lowLatency} 
                          onCheckedChange={(checked) => setStreamSettings({...streamSettings, lowLatency: checked})}
                          disabled={isLive}
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-4">Pengaturan Kamera & Mikrofon</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium mb-1 block">Pilih Kamera</label>
                        <Select disabled={isLive}>
                          <SelectTrigger>
                            <SelectValue placeholder="Webcam Default" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">Webcam Default</SelectItem>
                            <SelectItem value="external">Kamera Eksternal</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium mb-1 block">Pilih Mikrofon</label>
                        <Select disabled={isLive}>
                          <SelectTrigger>
                            <SelectValue placeholder="Mikrofon Default" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">Mikrofon Default</SelectItem>
                            <SelectItem value="headset">Headset Mikrofon</SelectItem>
                            <SelectItem value="external">Mikrofon Eksternal</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <label className="text-sm font-medium">Volume Mikrofon</label>
                          <span className="text-xs">80%</span>
                        </div>
                        <Slider defaultValue={[80]} max={100} step={1} />
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="audience" className="m-0 p-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Audiens</h3>
                      <Badge variant="outline">{isLive ? `${viewerCount} sedang menonton` : 'Belum ada penonton'}</Badge>
                    </div>
                    
                    {isLive ? (
                      <div className="border rounded-md">
                        <div className="p-3 bg-muted/30 border-b">
                          <h4 className="font-medium">Penonton Aktif</h4>
                        </div>
                        <div className="p-4 divide-y">
                          {viewerCount > 0 ? (
                            Array(viewerCount).fill(null).map((_, index) => (
                              <div key={index} className="flex items-center gap-3 py-2">
                                <div className="rounded-full bg-muted w-8 h-8 flex items-center justify-center">
                                  <User className="h-4 w-4" />
                                </div>
                                <div>
                                  <p className="font-medium">Penonton {index + 1}</p>
                                  <p className="text-xs text-muted-foreground">Bergabung {formatTime(new Date())}</p>
                                </div>
                              </div>
                            ))
                          ) : (
                            <div className="text-center py-8">
                              <Users className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                              <p className="text-muted-foreground">Belum ada penonton yang bergabung</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <Card>
                        <CardContent className="flex flex-col items-center justify-center pt-6 pb-8">
                          <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                          <h3 className="text-lg font-medium mb-2">Siaran belum dimulai</h3>
                          <p className="text-muted-foreground mb-4 text-center">
                            Mulai siaran untuk melihat audiens yang bergabung
                          </p>
                          <Button onClick={startStream} className="bg-red-600 hover:bg-red-700">
                            <Play className="h-4 w-4 mr-2" />
                            Mulai Siaran
                          </Button>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
        
        {/* Right Column - Chat */}
        <div className="space-y-4">
          <Card className="h-[calc(100vh-16rem)]">
            <CardHeader className="px-4 py-3 border-b">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">Chat Siaran</CardTitle>
                <Badge variant={streamSettings.chat ? "outline" : "secondary"} className={streamSettings.chat ? "bg-green-100 text-green-800" : ""}>
                  {streamSettings.chat ? "Aktif" : "Nonaktif"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-0 flex flex-col h-[calc(100%-4rem)]">
              <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {chatMessages.length > 0 ? (
                  chatMessages.map(message => (
                    <div key={message.id} className="flex gap-2">
                      <div className="rounded-full bg-muted w-8 h-8 flex items-center justify-center flex-shrink-0">
                        <User className="h-4 w-4" />
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{message.userName}</span>
                          <span className="text-xs text-muted-foreground">{formatTime(new Date(message.timestamp))}</span>
                        </div>
                        <p className="text-sm">{message.message}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center h-full text-center">
                    <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">Belum ada pesan</h3>
                    <p className="text-muted-foreground">
                      Pesan dari penonton akan muncul di sini
                    </p>
                  </div>
                )}
              </div>
              
              {streamSettings.chat && (
                <div className="p-3 border-t">
                  <form onSubmit={sendChatMessage} className="flex gap-2">
                    <Input 
                      placeholder="Ketik pesan..." 
                      value={chatInput}
                      onChange={(e) => setChatInput(e.target.value)}
                    />
                    <Button type="submit" size="icon">
                      <Send className="h-4 w-4" />
                    </Button>
                  </form>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 