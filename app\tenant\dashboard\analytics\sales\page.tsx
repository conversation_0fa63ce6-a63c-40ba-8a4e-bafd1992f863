"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  BarChart3,
  LineChart,
  PieChart,
  DollarSign,
  ShoppingCart,
  TrendingUp,
  TrendingDown,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Download,
  Filter,
  CalendarDays
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Data dummy untuk metrik penjualan
const salesMetrics = {
  totalRevenue: 1250650000,
  averageOrderValue: 520000,
  conversionRate: 3.5,
  salesGrowth: 12.7,
  totalOrders: 2405,
  returningCustomers: 685,
  topSellingProduct: {
    name: "Kemeja Linen Premium",
    sales: 124,
    revenue: 43400000
  }
}

// Data dummy untuk tren penjualan
const salesTrend = {
  months: ["<PERSON>", "Feb", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
  revenue: [52500000, 68900000, 87500000, 76400000, 92500000, 115000000],
  orders: [120, 155, 201, 178, 225, 265]
}

// Data dummy untuk produk terlaris
const topProducts = [
  {
    id: "prod-001",
    name: "Kemeja Linen Premium",
    category: "Pakaian",
    price: 350000,
    sales: 124,
    revenue: 43400000,
    growth: 15.4
  },
  {
    id: "prod-002",
    name: "Sepatu Sneakers Casual",
    category: "Sepatu",
    price: 620000,
    sales: 98,
    revenue: 60760000,
    growth: 8.2
  },
  {
    id: "prod-003",
    name: "Tas Ransel Kanvas",
    category: "Tas",
    price: 480000,
    sales: 87,
    revenue: 41760000,
    growth: 11.5
  },
  {
    id: "prod-004",
    name: "Celana Chino Slim Fit",
    category: "Pakaian",
    price: 280000,
    sales: 112,
    revenue: 31360000,
    growth: 7.8
  },
  {
    id: "prod-005",
    name: "Kacamata Vintage",
    category: "Aksesoris",
    price: 175000,
    sales: 78,
    revenue: 13650000,
    growth: 5.6
  }
]

// Data dummy untuk performa kategori
const categoryPerformance = [
  {
    name: "Pakaian",
    revenue: *********,
    orders: 345,
    growth: 14.2
  },
  {
    name: "Sepatu",
    revenue: ********,
    orders: 165,
    growth: 8.7
  },
  {
    name: "Tas",
    revenue: ********,
    orders: 210,
    growth: 11.8
  },
  {
    name: "Aksesoris",
    revenue: ********,
    orders: 320,
    growth: 6.5
  },
  {
    name: "Elektronik",
    revenue: ********,
    orders: 85,
    growth: 9.2
  }
]

// Data dummy untuk performa metode pembayaran
const paymentMethods = [
  {
    name: "Transfer Bank",
    transactions: 986,
    percentage: 41,
    revenue: *********
  },
  {
    name: "Kartu Kredit",
    transactions: 632,
    percentage: 26.3,
    revenue: *********
  },
  {
    name: "E-Wallet",
    transactions: 557,
    percentage: 23.2,
    revenue: *********
  },
  {
    name: "QRIS",
    transactions: 230,
    percentage: 9.5,
    revenue: *********
  }
]

// Format mata uang
function formatCurrency(number: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(number)
}

// Format persentase
function formatPercentage(number: number) {
  return `${number.toFixed(1)}%`
}

// Format angka dengan pemisah ribuan
function formatNumber(number: number) {
  return number.toLocaleString('id-ID')
}

export default function SalesAnalyticsPage() {
  const [dateFilter, setDateFilter] = useState("last-6-months")
  const [selectedTab, setSelectedTab] = useState("overview")

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/analytics">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analitik Penjualan</h1>
            <p className="text-muted-foreground">
              Lihat performa penjualan dan tren bisnis Anda
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Select
            value={dateFilter}
            onValueChange={setDateFilter}
          >
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <CalendarDays className="h-4 w-4 mr-2" />
                <span>Rentang Waktu</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last-7-days">7 Hari Terakhir</SelectItem>
              <SelectItem value="last-30-days">30 Hari Terakhir</SelectItem>
              <SelectItem value="last-3-months">3 Bulan Terakhir</SelectItem>
              <SelectItem value="last-6-months">6 Bulan Terakhir</SelectItem>
              <SelectItem value="last-year">1 Tahun Terakhir</SelectItem>
              <SelectItem value="all-time">Sepanjang Waktu</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Ekspor Data
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="overview" value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="w-full md:w-auto">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Produk</TabsTrigger>
          <TabsTrigger value="categories">Kategori</TabsTrigger>
          <TabsTrigger value="payments">Pembayaran</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab Content */}
        <TabsContent value="overview" className="space-y-6">
          {/* Metrik Utama */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
                <DollarSign className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{formatCurrency(salesMetrics.totalRevenue)}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <TrendingUp className="mr-1 h-3.5 w-3.5 text-green-600" />
                  <span className="text-green-600 font-medium">{formatPercentage(salesMetrics.salesGrowth)}</span>
                  <span className="ml-1">dibanding periode sebelumnya</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Pesanan</CardTitle>
                <ShoppingCart className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{formatNumber(salesMetrics.totalOrders)}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <span>{formatNumber(salesMetrics.returningCustomers)} pelanggan kembali</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Nilai Order Rata-rata</CardTitle>
                <BarChart3 className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{formatCurrency(salesMetrics.averageOrderValue)}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <ArrowUpRight className="mr-1 h-3.5 w-3.5 text-green-600" />
                  <span className="text-green-600 font-medium">5.2%</span>
                  <span className="ml-1">dibanding periode sebelumnya</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tingkat Konversi</CardTitle>
                <TrendingUp className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{formatPercentage(salesMetrics.conversionRate)}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <ArrowUpRight className="mr-1 h-3.5 w-3.5 text-green-600" />
                  <span className="text-green-600 font-medium">0.8%</span>
                  <span className="ml-1">dibanding periode sebelumnya</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Grafik Tren Penjualan dan Produk Terlaris */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Tren Penjualan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <LineChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                    <p>Grafik tren penjualan akan ditampilkan di sini</p>
                    <p className="text-xs">Data untuk {salesTrend.months.length} bulan terakhir</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Produk Terlaris</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topProducts.slice(0, 5).map((product, index) => (
                    <div key={product.id} className="border rounded-md p-3">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="flex items-center">
                            <span className="mr-2 bg-muted w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium">{index + 1}</span>
                            <div>
                              <h4 className="font-medium text-sm">{product.name}</h4>
                              <p className="text-xs text-muted-foreground">{product.category}</p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{formatCurrency(product.revenue)}</div>
                          <div className="flex items-center justify-end text-xs">
                            <span className="text-muted-foreground mr-1">{product.sales} terjual</span>
                            <TrendingUp className="h-3 w-3 text-green-600" />
                            <span className="text-green-600">{formatPercentage(product.growth)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Products Tab Content */}
        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performa Produk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative overflow-x-auto">
                <table className="w-full text-sm text-left">
                  <thead className="text-xs uppercase bg-muted/50">
                    <tr>
                      <th scope="col" className="px-6 py-3">Produk</th>
                      <th scope="col" className="px-6 py-3">Kategori</th>
                      <th scope="col" className="px-6 py-3">Harga</th>
                      <th scope="col" className="px-6 py-3">Terjual</th>
                      <th scope="col" className="px-6 py-3">Pendapatan</th>
                      <th scope="col" className="px-6 py-3">Pertumbuhan</th>
                    </tr>
                  </thead>
                  <tbody>
                    {topProducts.map(product => (
                      <tr key={product.id} className="border-b">
                        <td className="px-6 py-4 font-medium">
                          <Link href={`/tenant/dashboard/products/${product.id}`} className="hover:underline">
                            {product.name}
                          </Link>
                        </td>
                        <td className="px-6 py-4">{product.category}</td>
                        <td className="px-6 py-4">{formatCurrency(product.price)}</td>
                        <td className="px-6 py-4">{product.sales}</td>
                        <td className="px-6 py-4">{formatCurrency(product.revenue)}</td>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <TrendingUp className="h-4 w-4 mr-1 text-green-600" />
                            <span className="text-green-600">{formatPercentage(product.growth)}</span>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              <div className="h-[300px] flex items-center justify-center mt-6">
                <div className="text-center text-muted-foreground">
                  <BarChart3 className="h-16 w-16 mx-auto mb-2 opacity-50" />
                  <p>Grafik perbandingan produk akan ditampilkan di sini</p>
                  <p className="text-xs">Perbandingan penjualan produk berdasarkan pendapatan</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Categories Tab Content */}
        <TabsContent value="categories" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Performa Kategori</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative overflow-x-auto">
                  <table className="w-full text-sm text-left">
                    <thead className="text-xs uppercase bg-muted/50">
                      <tr>
                        <th scope="col" className="px-6 py-3">Kategori</th>
                        <th scope="col" className="px-6 py-3">Pendapatan</th>
                        <th scope="col" className="px-6 py-3">Pesanan</th>
                        <th scope="col" className="px-6 py-3">Pertumbuhan</th>
                      </tr>
                    </thead>
                    <tbody>
                      {categoryPerformance.map(category => (
                        <tr key={category.name} className="border-b">
                          <td className="px-6 py-4 font-medium">
                            <Link href={`/tenant/dashboard/products?category=${category.name}`} className="hover:underline">
                              {category.name}
                            </Link>
                          </td>
                          <td className="px-6 py-4">{formatCurrency(category.revenue)}</td>
                          <td className="px-6 py-4">{category.orders}</td>
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <TrendingUp className="h-4 w-4 mr-1 text-green-600" />
                              <span className="text-green-600">{formatPercentage(category.growth)}</span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Distribusi Kategori</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <PieChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                    <p>Grafik distribusi kategori akan ditampilkan di sini</p>
                    <p className="text-xs">Persentase pendapatan per kategori</p>
                  </div>
                </div>
                
                <div className="space-y-2 mt-4">
                  {categoryPerformance.map((category, index) => {
                    const colors = ["bg-blue-500", "bg-green-500", "bg-purple-500", "bg-orange-500", "bg-red-500"]
                    return (
                      <div key={category.name} className="flex items-center">
                        <div className={`w-3 h-3 rounded-full mr-2 ${colors[index]}`}></div>
                        <span className="text-sm">{category.name}</span>
                        <div className="flex-1 mx-2">
                          <div className="h-2 rounded-full bg-muted overflow-hidden">
                            <div 
                              className={`h-full rounded-full ${colors[index]}`} 
                              style={{ width: `${(category.revenue / salesMetrics.totalRevenue) * 100}%` }}
                            />
                          </div>
                        </div>
                        <span className="text-xs font-medium">
                          {formatPercentage((category.revenue / salesMetrics.totalRevenue) * 100)}
                        </span>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Payments Tab Content */}
        <TabsContent value="payments" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Metode Pembayaran</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative overflow-x-auto">
                  <table className="w-full text-sm text-left">
                    <thead className="text-xs uppercase bg-muted/50">
                      <tr>
                        <th scope="col" className="px-6 py-3">Metode</th>
                        <th scope="col" className="px-6 py-3">Transaksi</th>
                        <th scope="col" className="px-6 py-3">Persentase</th>
                        <th scope="col" className="px-6 py-3">Pendapatan</th>
                      </tr>
                    </thead>
                    <tbody>
                      {paymentMethods.map(method => (
                        <tr key={method.name} className="border-b">
                          <td className="px-6 py-4 font-medium">{method.name}</td>
                          <td className="px-6 py-4">{method.transactions}</td>
                          <td className="px-6 py-4">{formatPercentage(method.percentage)}</td>
                          <td className="px-6 py-4">{formatCurrency(method.revenue)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Distribusi Metode Pembayaran</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[250px] flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <PieChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                    <p>Grafik distribusi pembayaran akan ditampilkan di sini</p>
                    <p className="text-xs">Persentase transaksi per metode pembayaran</p>
                  </div>
                </div>
                
                <div className="space-y-2 mt-4">
                  {paymentMethods.map((method, index) => {
                    const colors = ["bg-blue-500", "bg-green-500", "bg-purple-500", "bg-orange-500"]
                    return (
                      <div key={method.name} className="flex items-center">
                        <div className={`w-3 h-3 rounded-full mr-2 ${colors[index]}`}></div>
                        <span className="text-sm">{method.name}</span>
                        <div className="flex-1 mx-2">
                          <div className="h-2 rounded-full bg-muted overflow-hidden">
                            <div 
                              className={`h-full rounded-full ${colors[index]}`} 
                              style={{ width: `${method.percentage}%` }}
                            />
                          </div>
                        </div>
                        <span className="text-xs font-medium">
                          {formatPercentage(method.percentage)}
                        </span>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Tren Metode Pembayaran</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <LineChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                  <p>Grafik tren metode pembayaran akan ditampilkan di sini</p>
                  <p className="text-xs">Perbandingan penggunaan metode pembayaran dari waktu ke waktu</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 