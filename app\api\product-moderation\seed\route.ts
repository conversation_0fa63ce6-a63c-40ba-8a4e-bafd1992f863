import { NextRequest, NextResponse } from 'next/server';
import { productModerationService } from '@/lib/services/product-moderation';
import { getServerClient } from '@/lib/supabase';

// POST - Seed product moderation data
export async function POST(request: NextRequest) {
  try {
    const supabase = getServerClient()

    // Clear existing data first
    await supabase.from('product_moderation').delete().neq('id', '00000000-0000-0000-0000-000000000000')

    // Create sample moderations with mock product data
    const sampleModerations = [
      {
        product_id: 'mock-product-1',
        status: 'pending' as const,
        priority: 'high' as const,
        flags: ['pricing_review', 'description_check'],
        auto_flags: ['suspicious_pricing'],
        required_changes: [],
        review_checklist: {
          'product_authenticity': false,
          'pricing_reasonable': false,
          'description_accurate': false,
          'images_quality': true
        },
        rejection_reason: null,
        // Mock product data
        product: {
          id: 'mock-product-1',
          name: 'iPhone 15 Pro Max 256GB',
          sku: 'APPLE-IP15PM-256',
          price: 18999000,
          featured_image: '/api/placeholder/300/300',
          status: 'pending_review',
          description: 'iPhone 15 Pro Max terbaru dengan chipset A17 Pro dan kamera 48MP yang canggih',
          images: ['/api/placeholder/300/300', '/api/placeholder/300/300'],
          inventory_quantity: 25,
          tags: ['smartphone', 'apple', '5g', 'premium'],
          store: {
            id: 'store-1',
            name: 'TechWorld Store',
            owner_name: 'Ahmad Rizki'
          }
        }
      },
      {
        product_id: 'mock-product-2',
        status: 'under_review' as const,
        priority: 'normal' as const,
        flags: ['image_quality'],
        auto_flags: [],
        required_changes: ['Improve product images quality'],
        review_checklist: {
          'product_authenticity': true,
          'pricing_reasonable': true,
          'description_accurate': true,
          'images_quality': false
        },
        rejection_reason: null,
        reviewer_id: 'reviewer-1',
        review_notes: 'Product looks good but images need improvement',
        // Mock product data
        product: {
          id: 'mock-product-2',
          name: 'Nike Air Max 270 React',
          sku: 'NIKE-AM270R-BLK',
          price: 1299000,
          featured_image: '/api/placeholder/300/300',
          status: 'under_review',
          description: 'Sepatu running dengan teknologi Nike React foam untuk kenyamanan maksimal',
          images: ['/api/placeholder/300/300'],
          inventory_quantity: 15,
          tags: ['sepatu', 'running', 'sport', 'nike'],
          store: {
            id: 'store-2',
            name: 'SportZone',
            owner_name: 'Sarah Putri'
          }
        }
      },
      {
        product_id: 'mock-product-3',
        status: 'approved' as const,
        priority: 'low' as const,
        flags: [],
        auto_flags: [],
        required_changes: [],
        review_checklist: {
          'product_authenticity': true,
          'pricing_reasonable': true,
          'description_accurate': true,
          'images_quality': true
        },
        rejection_reason: null,
        reviewer_id: 'reviewer-2',
        review_notes: 'All checks passed, approved for listing',
        approved_at: new Date().toISOString(),
        // Mock product data
        product: {
          id: 'mock-product-3',
          name: 'Laptop Gaming ASUS ROG Strix',
          sku: 'ASUS-ROG-G15',
          price: 15999000,
          featured_image: '/api/placeholder/300/300',
          status: 'approved',
          description: 'Laptop gaming dengan RTX 4060 dan processor Intel i7 generasi terbaru',
          images: ['/api/placeholder/300/300', '/api/placeholder/300/300', '/api/placeholder/300/300'],
          inventory_quantity: 8,
          tags: ['laptop', 'gaming', 'asus', 'rtx'],
          store: {
            id: 'store-3',
            name: 'Gaming Central',
            owner_name: 'Budi Santoso'
          }
        }
      },
      {
        product_id: 'mock-product-4',
        status: 'rejected' as const,
        priority: 'urgent' as const,
        flags: ['fake_product', 'misleading_info'],
        auto_flags: ['suspicious_brand', 'unrealistic_claims'],
        required_changes: ['Verify product authenticity', 'Remove misleading health claims'],
        review_checklist: {
          'product_authenticity': false,
          'pricing_reasonable': false,
          'description_accurate': false,
          'images_quality': false
        },
        rejection_reason: 'Product contains unverified health claims and brand authenticity cannot be confirmed',
        reviewer_id: 'reviewer-3',
        review_notes: 'Multiple violations found, product rejected',
        rejected_at: new Date().toISOString(),
        // Mock product data
        product: {
          id: 'mock-product-4',
          name: 'Skincare Set Korea Premium',
          sku: 'BEAUTY-KOREA-SET',
          price: 450000,
          featured_image: '/api/placeholder/300/300',
          status: 'rejected',
          description: 'Set skincare premium dari Korea dengan bahan natural dan teruji klinis',
          images: ['/api/placeholder/300/300', '/api/placeholder/300/300'],
          inventory_quantity: 0,
          tags: ['skincare', 'korea', 'beauty', 'premium'],
          store: {
            id: 'store-4',
            name: 'Beauty Corner',
            owner_name: 'Lisa Chen'
          }
        }
      },
      {
        product_id: 'mock-product-5',
        status: 'requires_changes' as const,
        priority: 'normal' as const,
        flags: ['incomplete_description'],
        auto_flags: [],
        required_changes: ['Add more detailed product specifications', 'Include size chart'],
        review_checklist: {
          'product_authenticity': true,
          'pricing_reasonable': true,
          'description_accurate': false,
          'images_quality': true
        },
        rejection_reason: null,
        reviewer_id: 'reviewer-1',
        review_notes: 'Product is good but needs more detailed information',
        // Mock product data
        product: {
          id: 'mock-product-5',
          name: 'Jaket Hoodie Unisex Premium',
          sku: 'FASHION-HOODIE-UNI',
          price: 199000,
          featured_image: '/api/placeholder/300/300',
          status: 'requires_changes',
          description: 'Jaket hoodie berkualitas premium dengan bahan cotton fleece yang nyaman',
          images: ['/api/placeholder/300/300', '/api/placeholder/300/300'],
          inventory_quantity: 50,
          tags: ['jaket', 'hoodie', 'fashion', 'unisex'],
          store: {
            id: 'store-5',
            name: 'Fashion Street',
            owner_name: 'Michael Wong'
          }
        }
      }
    ]

    const createdModerations = []
    const errors = []

    for (const moderation of sampleModerations) {
      try {
        const created = await productModerationService.createModeration(moderation)
        createdModerations.push(created)
      } catch (error) {
        console.error(`Error creating moderation for product ${moderation.product_id}:`, error)
        errors.push({
          product_id: moderation.product_id,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      message: 'Product moderations seeded successfully',
      created: createdModerations.length,
      errors: errors.length,
      errorDetails: errors
    }, { status: 201 })

  } catch (error) {
    console.error('Error seeding product moderations:', error)
    const errorMessage = error instanceof Error ? error.message : 'Failed to seed product moderations'
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}
