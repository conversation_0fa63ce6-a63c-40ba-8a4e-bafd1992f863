"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFoot<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Download, Filter, Plus, CheckCircle, AlertTriangle, FileText } from "lucide-react"

// Mock data for tax rates
const taxRates = [
  {
    id: "TAX-12345",
    region: "United States",
    state: "California",
    rate: "7.25%",
    type: "Sales Tax",
    effectiveDate: "2023-01-01",
    status: "Active",
  },
  {
    id: "TAX-12346",
    region: "United States",
    state: "New York",
    rate: "8.875%",
    type: "Sales Tax",
    effectiveDate: "2023-01-01",
    status: "Active",
  },
  {
    id: "TAX-12347",
    region: "European Union",
    state: "Germany",
    rate: "19%",
    type: "VAT",
    effectiveDate: "2023-01-01",
    status: "Active",
  },
  {
    id: "TAX-12348",
    region: "European Union",
    state: "France",
    rate: "20%",
    type: "VAT",
    effectiveDate: "2023-01-01",
    status: "Active",
  },
  {
    id: "TAX-12349",
    region: "United Kingdom",
    state: "All",
    rate: "20%",
    type: "VAT",
    effectiveDate: "2023-01-01",
    status: "Active",
  },
  {
    id: "TAX-12350",
    region: "Canada",
    state: "Ontario",
    rate: "13%",
    type: "HST",
    effectiveDate: "2023-01-01",
    status: "Active",
  },
  {
    id: "TAX-12351",
    region: "Australia",
    state: "All",
    rate: "10%",
    type: "GST",
    effectiveDate: "2023-01-01",
    status: "Active",
  },
  {
    id: "TAX-12352",
    region: "Japan",
    state: "All",
    rate: "10%",
    type: "Consumption Tax",
    effectiveDate: "2023-10-01",
    status: "Active",
  },
  {
    id: "TAX-12353",
    region: "Singapore",
    state: "All",
    rate: "8%",
    type: "GST",
    effectiveDate: "2023-01-01",
    status: "Active",
  },
  {
    id: "TAX-12354",
    region: "United States",
    state: "Texas",
    rate: "6.25%",
    type: "Sales Tax",
    effectiveDate: "2023-01-01",
    status: "Active",
  },
]

// Mock data for tax categories
const taxCategories = [
  {
    id: "CAT-12345",
    name: "Physical Goods",
    description: "Tangible products that are shipped to customers",
    taxable: true,
    defaultRate: "Standard Rate",
  },
  {
    id: "CAT-12346",
    name: "Digital Products",
    description: "Downloadable software, e-books, and digital content",
    taxable: true,
    defaultRate: "Digital Rate",
  },
  {
    id: "CAT-12347",
    name: "Services",
    description: "Professional services and consulting",
    taxable: true,
    defaultRate: "Service Rate",
  },
  {
    id: "CAT-12348",
    name: "Subscriptions",
    description: "Recurring subscription services",
    taxable: true,
    defaultRate: "Standard Rate",
  },
  {
    id: "CAT-12349",
    name: "Food & Beverages",
    description: "Edible products",
    taxable: true,
    defaultRate: "Reduced Rate",
  },
  {
    id: "CAT-12350",
    name: "Medical Supplies",
    description: "Health-related products",
    taxable: false,
    defaultRate: "Exempt",
  },
]

// Mock data for tax documents
const taxDocuments = [
  {
    id: "DOC-12345",
    name: "Annual Tax Report 2022",
    type: "Report",
    uploadDate: "2023-01-15",
    size: "2.5 MB",
  },
  {
    id: "DOC-12346",
    name: "VAT Registration Certificate - EU",
    type: "Certificate",
    uploadDate: "2023-02-10",
    size: "1.2 MB",
  },
  {
    id: "DOC-12347",
    name: "US Sales Tax Exemption Forms",
    type: "Form",
    uploadDate: "2023-03-05",
    size: "3.8 MB",
  },
  {
    id: "DOC-12348",
    name: "Tax Policy Documentation",
    type: "Policy",
    uploadDate: "2023-04-20",
    size: "1.7 MB",
  },
  {
    id: "DOC-12349",
    name: "Quarterly Tax Filing - Q1 2023",
    type: "Filing",
    uploadDate: "2023-04-15",
    size: "4.2 MB",
  },
  {
    id: "DOC-12350",
    name: "Tax Compliance Audit Report",
    type: "Audit",
    uploadDate: "2023-05-30",
    size: "5.1 MB",
  },
]

// Mock data for compliance status
const complianceStatus = [
  {
    id: "COMP-12345",
    region: "United States",
    status: "Compliant",
    lastReview: "2023-09-15",
    nextReview: "2023-12-15",
    issues: 0,
  },
  {
    id: "COMP-12346",
    region: "European Union",
    status: "Compliant",
    lastReview: "2023-08-20",
    nextReview: "2023-11-20",
    issues: 0,
  },
  {
    id: "COMP-12347",
    region: "United Kingdom",
    status: "Compliant",
    lastReview: "2023-09-05",
    nextReview: "2023-12-05",
    issues: 0,
  },
  {
    id: "COMP-12348",
    region: "Canada",
    status: "Attention Required",
    lastReview: "2023-07-10",
    nextReview: "2023-10-10",
    issues: 2,
  },
  {
    id: "COMP-12349",
    region: "Australia",
    status: "Compliant",
    lastReview: "2023-08-15",
    nextReview: "2023-11-15",
    issues: 0,
  },
  {
    id: "COMP-12350",
    region: "Japan",
    status: "Attention Required",
    lastReview: "2023-06-25",
    nextReview: "2023-09-25",
    issues: 1,
  },
]

// Helper function to get badge variant based on status
function getStatusBadge(status: string) {
  switch (status) {
    case "Active":
    case "Compliant":
      return { variant: "success" as const, icon: CheckCircle }
    case "Inactive":
      return { variant: "secondary" as const, icon: null }
    case "Attention Required":
      return { variant: "warning" as const, icon: AlertTriangle }
    default:
      return { variant: "outline" as const, icon: null }
  }
}

export function TaxManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [regionFilter, setRegionFilter] = useState("all")

  // Filter tax rates based on search term and region
  const filteredTaxRates = taxRates.filter(
    (tax) =>
      (tax.region.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tax.state.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tax.id.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (regionFilter === "all" || tax.region === regionFilter),
  )

  return (
    <div className="grid gap-6">
      <Tabs defaultValue="rates" className="w-full">
        <TabsList className="grid w-full grid-cols-4 lg:w-[600px]">
          <TabsTrigger value="rates">Tax Rates</TabsTrigger>
          <TabsTrigger value="categories">Tax Categories</TabsTrigger>
          <TabsTrigger value="documents">Tax Documents</TabsTrigger>
          <TabsTrigger value="compliance">Compliance Status</TabsTrigger>
        </TabsList>
        <TabsContent value="rates" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div>
                  <CardTitle>Tax Rates by Region</CardTitle>
                  <CardDescription>Manage tax rates for different regions and jurisdictions</CardDescription>
                </div>
                <div className="flex flex-col gap-2 sm:flex-row">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search tax rates..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select defaultValue={regionFilter} onValueChange={setRegionFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by region" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Regions</SelectItem>
                      <SelectItem value="United States">United States</SelectItem>
                      <SelectItem value="European Union">European Union</SelectItem>
                      <SelectItem value="United Kingdom">United Kingdom</SelectItem>
                      <SelectItem value="Canada">Canada</SelectItem>
                      <SelectItem value="Australia">Australia</SelectItem>
                      <SelectItem value="Japan">Japan</SelectItem>
                      <SelectItem value="Singapore">Singapore</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Tax Rate
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tax ID</TableHead>
                      <TableHead>Region</TableHead>
                      <TableHead>State/Province</TableHead>
                      <TableHead className="text-center">Rate</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Effective Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTaxRates.map((tax) => {
                      const { variant, icon: StatusIcon } = getStatusBadge(tax.status)
                      return (
                        <TableRow key={tax.id}>
                          <TableCell className="font-medium">{tax.id}</TableCell>
                          <TableCell>{tax.region}</TableCell>
                          <TableCell>{tax.state}</TableCell>
                          <TableCell className="text-center">
                            <Badge variant="outline">{tax.rate}</Badge>
                          </TableCell>
                          <TableCell>{tax.type}</TableCell>
                          <TableCell>{tax.effectiveDate}</TableCell>
                          <TableCell>
                            <Badge variant={variant} className="flex w-24 justify-center">
                              {StatusIcon && <StatusIcon className="mr-1 h-3 w-3" />}
                              {tax.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="ghost" size="sm">
                                Edit
                              </Button>
                              <Button variant="ghost" size="sm">
                                Deactivate
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
              <div className="flex items-center justify-end space-x-2 py-4">
                <Button variant="outline" size="sm">
                  Previous
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle>Tax Categories</CardTitle>
                  <CardDescription>Manage product and service tax categories</CardDescription>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Category
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Category ID</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-center">Taxable</TableHead>
                      <TableHead>Default Rate</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {taxCategories.map((category) => (
                      <TableRow key={category.id}>
                        <TableCell className="font-medium">{category.id}</TableCell>
                        <TableCell>{category.name}</TableCell>
                        <TableCell>{category.description}</TableCell>
                        <TableCell className="text-center">
                          {category.taxable ? (
                            <Badge variant="outline" className="bg-green-50">
                              Yes
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-red-50">
                              No
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{category.defaultRate}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="sm">
                              Edit
                            </Button>
                            <Button variant="ghost" size="sm">
                              Delete
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle>Tax Documents</CardTitle>
                  <CardDescription>Manage tax-related documents and certificates</CardDescription>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Upload Document
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Document ID</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Upload Date</TableHead>
                      <TableHead>Size</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {taxDocuments.map((document) => (
                      <TableRow key={document.id}>
                        <TableCell className="font-medium">{document.id}</TableCell>
                        <TableCell>{document.name}</TableCell>
                        <TableCell>{document.type}</TableCell>
                        <TableCell>{document.uploadDate}</TableCell>
                        <TableCell>{document.size}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="sm">
                              <FileText className="mr-1 h-3 w-3" />
                              View
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Download className="mr-1 h-3 w-3" />
                              Download
                            </Button>
                            <Button variant="ghost" size="sm">
                              Delete
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="compliance" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle>Compliance Status</CardTitle>
                  <CardDescription>Monitor tax compliance status across regions</CardDescription>
                </div>
                <Button>Run Compliance Check</Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Compliance ID</TableHead>
                      <TableHead>Region</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-center">Issues</TableHead>
                      <TableHead>Last Review</TableHead>
                      <TableHead>Next Review</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {complianceStatus.map((compliance) => {
                      const { variant, icon: StatusIcon } = getStatusBadge(compliance.status)
                      return (
                        <TableRow key={compliance.id}>
                          <TableCell className="font-medium">{compliance.id}</TableCell>
                          <TableCell>{compliance.region}</TableCell>
                          <TableCell>
                            <Badge variant={variant} className="flex w-40 justify-center">
                              {StatusIcon && <StatusIcon className="mr-1 h-3 w-3" />}
                              {compliance.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">
                            {compliance.issues > 0 ? (
                              <Badge variant="destructive">{compliance.issues}</Badge>
                            ) : (
                              <Badge variant="outline" className="bg-green-50">
                                0
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>{compliance.lastReview}</TableCell>
                          <TableCell>{compliance.nextReview}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="ghost" size="sm">
                                View Details
                              </Button>
                              {compliance.issues > 0 && (
                                <Button variant="ghost" size="sm">
                                  Resolve Issues
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                Last compliance check: <span className="font-medium">2023-10-01</span>
              </div>
              <div className="flex gap-2">
                <Button variant="outline">Configure Alerts</Button>
                <Button variant="outline">View Reports</Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
