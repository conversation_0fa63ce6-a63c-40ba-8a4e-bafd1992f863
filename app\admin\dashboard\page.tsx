"use client"

import { Suspense, useEffect, useState } from "react"
import { Building, CreditCard, DollarSign, Globe, Server, Settings, ShieldCheck, Store, Users } from "lucide-react"
import { StatCard } from "@/components/dashboard/stat-card"
import { ChartCard } from "@/components/dashboard/chart-card"
import { SystemStatus } from "@/components/dashboard/system-status"
import { ActivityFeed } from "@/components/dashboard/activity-feed"
import { QuickActions } from "@/components/dashboard/quick-actions"
import { formatCurrency } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

// Komponen untuk menampilkan tanggal yang hanya dirender di client-side
function FormattedDate({ date, locale = "id-ID" }: { date?: Date; locale?: string }) {
  const [formattedDate, setFormattedDate] = useState("");
  
  useEffect(() => {
    // Gunakan tanggal yang diberikan atau tanggal saat ini
    const dateToFormat = date || new Date();
    setFormattedDate(dateToFormat.toLocaleString(locale));
  }, [date, locale]);
  
  return <>{formattedDate}</>;
}

// Data dummy untuk grafik
const revenueData = [
  { name: "Jan", total: 15000000 },
  { name: "Feb", total: 22000000 },
  { name: "Mar", total: 18000000 },
  { name: "Apr", total: 25000000 },
  { name: "Mei", total: 27000000 },
  { name: "Jun", total: 32000000 },
  { name: "Jul", total: 38000000 },
]

const tenantsData = [
  { name: "Jan", total: 5 },
  { name: "Feb", total: 7 },
  { name: "Mar", total: 8 },
  { name: "Apr", total: 10 },
  { name: "Mei", total: 12 },
  { name: "Jun", total: 15 },
  { name: "Jul", total: 18 },
]

const systemStatusItems = [
  { name: "API Server", status: "online" as const, uptime: "99.9%" },
  { name: "Database", status: "online" as const, uptime: "99.8%" },
  { name: "Storage", status: "online" as const, uptime: "99.9%" },
  { name: "Payment Gateway", status: "online" as const, uptime: "99.7%" },
  { name: "Email Service", status: "warning" as const, uptime: "98.5%" },
  { name: "Cache Server", status: "online" as const, uptime: "99.9%" },
  { name: "Search Service", status: "online" as const, uptime: "99.6%" },
]

const activityItems = [
  {
    id: "1",
    user: { name: "Admin System", email: "<EMAIL>" },
    action: "menambahkan tenant baru",
    target: "Fashion Marketplace",
    date: "Baru saja",
    status: "success" as const,
  },
  {
    id: "2",
    user: { name: "Admin System", email: "<EMAIL>" },
    action: "mengupdate konfigurasi",
    target: "Payment Gateway",
    date: "5 menit yang lalu",
    status: "success" as const,
  },
  {
    id: "3",
    user: { name: "Admin System", email: "<EMAIL>" },
    action: "melakukan restart",
    target: "API Server",
    date: "15 menit yang lalu",
    status: "info" as const,
  },
  {
    id: "4",
    user: { name: "Admin System", email: "<EMAIL>" },
    action: "menambahkan fitur baru",
    target: "Affiliate System",
    date: "30 menit yang lalu",
    status: "success" as const,
  },
  {
    id: "5",
    user: { name: "Admin System", email: "<EMAIL>" },
    action: "mengatasi error",
    target: "Email Service",
    date: "1 jam yang lalu",
    status: "error" as const,
  },
]

const quickActions = [
  {
    name: "Kelola Tenant",
    icon: Building,
    href: "/admin/dashboard/tenants",
  },
  {
    name: "Kelola Pengguna",
    icon: Users,
    href: "/admin/dashboard/users",
  },
  {
    name: "Konfigurasi Sistem",
    icon: Settings,
    href: "/admin/dashboard/settings",
  },
  {
    name: "Keamanan",
    icon: ShieldCheck,
    href: "/admin/dashboard/security",
  },
  {
    name: "Pembayaran",
    icon: CreditCard,
    href: "/admin/dashboard/payments",
  },
  {
    name: "Server",
    icon: Server,
    href: "/admin/dashboard/server",
  },
  {
    name: "Domain",
    icon: Globe,
    href: "/admin/dashboard/domains",
  },
]

export default function AdminDashboardPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard Admin</h1>
        <div className="flex items-center gap-2">
          <div className="text-sm text-muted-foreground">
            Terakhir diperbarui: <FormattedDate />
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Total Pendapatan"
            value={formatCurrency(85000000)}
            description="Total pendapatan platform"
            icon={DollarSign}
            trend="up"
            trendValue="18%"
            variant="primary"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Tenant"
            value="18"
            description="Total tenant aktif"
            icon={Building}
            trend="up"
            trendValue="20%"
            variant="success"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Toko"
            value="245"
            description="Total toko di platform"
            icon={Store}
            trend="up"
            trendValue="15%"
            variant="warning"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Pengguna"
            value="5,842"
            description="Total pengguna terdaftar"
            icon={Users}
            trend="up"
            trendValue="12%"
            variant="default"
          />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ChartCard
            title="Pendapatan Platform"
            description="Pendapatan 7 bulan terakhir"
            data={revenueData}
            type="line"
            dataKey="total"
            valueFormatter={(value) => formatCurrency(value)}
            colors={{
              stroke: "#3B82F6",
              fill: "rgba(59, 130, 246, 0.1)",
            }}
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ChartCard
            title="Pertumbuhan Tenant"
            description="Jumlah tenant 7 bulan terakhir"
            data={tenantsData}
            type="bar"
            dataKey="total"
            valueFormatter={(value) => `${value} tenant`}
            colors={{
              stroke: "#3B82F6",
              fill: "url(#blueGradient)",
            }}
          />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <QuickActions
            title="Aksi Cepat"
            description="Akses cepat ke fitur utama"
            actions={quickActions}
            className="lg:col-span-2"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <SystemStatus title="Status Sistem" items={systemStatusItems} />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ActivityFeed title="Aktivitas Admin" description="Aktivitas terbaru di sistem" items={activityItems} />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <Card>
            <CardHeader>
              <CardTitle>Tenant Terbaru</CardTitle>
              <CardDescription>Tenant yang baru bergabung</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { name: "Fashion Marketplace", date: "2025-05-10", status: "active", stores: 15, users: 120 },
                  { name: "Electronic Store", date: "2025-05-08", status: "active", stores: 8, users: 75 },
                  { name: "Food Delivery", date: "2025-05-05", status: "pending", stores: 0, users: 5 },
                  { name: "Book Store", date: "2025-05-01", status: "active", stores: 3, users: 25 },
                  { name: "Sports Equipment", date: "2025-04-28", status: "active", stores: 5, users: 40 },
                ].map((tenant, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                        <Building className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">{tenant.name}</p>
                        <p className="text-xs text-muted-foreground">
                          <FormattedDate date={new Date(tenant.date)} />
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`text-xs ${tenant.status === "active" ? "text-green-500" : "text-yellow-500"}`}>
                        {tenant.status === "active" ? "Aktif" : "Pending"}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {tenant.stores} toko • {tenant.users} pengguna
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </Suspense>
      </div>
    </div>
  )
}
