"use client"

import { cn } from "@/lib/utils"
import { Check, Clock } from "lucide-react"

interface ApplicationProgressTrackerProps {
  currentStep: number
  totalSteps: number
  applicationId: string
  estimatedTime?: string
  onStepClick?: (step: number) => void
}

export function ApplicationProgressTracker({
  currentStep,
  totalSteps,
  applicationId,
  estimatedTime = "15-20 menit",
  onStepClick,
}: ApplicationProgressTrackerProps) {
  const steps = ["Informasi Toko", "Informasi Produk", "Detail Bisnis", "Kebijakan Toko", "Review & Submit"]

  return (
    <div className="bg-card rounded-lg border border-border p-6 mb-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h3 className="text-lg font-semibold">Progress Aplikasi</h3>
          <p className="text-sm text-muted-foreground">Selesaikan semua langkah untuk mengirimkan aplikasi Anda</p>
        </div>
        <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
          <div className="bg-primary/10 text-primary px-3 py-1.5 rounded-md text-sm flex items-center">
            <span className="font-medium">ID Aplikasi:</span>
            <span className="ml-2">{applicationId}</span>
          </div>
          <div className="bg-amber-500/10 text-amber-600 dark:text-amber-400 px-3 py-1.5 rounded-md text-sm flex items-center">
            <Clock className="h-4 w-4 mr-1.5" />
            <span>Estimasi waktu: {estimatedTime}</span>
          </div>
        </div>
      </div>

      <div className="relative">
        {/* Progress Bar */}
        <div className="hidden md:block absolute top-4 left-0 h-0.5 bg-muted w-full z-0">
          <div
            className="h-0.5 bg-primary transition-all duration-300"
            style={{ width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` }}
          ></div>
        </div>

        {/* Steps */}
        <div className="flex flex-col md:flex-row justify-between relative z-10">
          {steps.map((step, index) => {
            const stepNumber = index + 1
            const isCompleted = stepNumber < currentStep
            const isCurrent = stepNumber === currentStep
            const isClickable = onStepClick && (isCompleted || stepNumber === currentStep)

            return (
              <div
                key={step}
                className={cn(
                  "flex md:flex-col items-start md:items-center gap-3 md:gap-2 mb-4 md:mb-0",
                  isClickable && "cursor-pointer",
                )}
                onClick={() => isClickable && onStepClick(stepNumber)}
              >
                <div
                  className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors",
                    isCompleted
                      ? "bg-primary text-primary-foreground"
                      : isCurrent
                        ? "bg-primary/20 text-primary border-2 border-primary"
                        : "bg-muted text-muted-foreground border border-border",
                  )}
                >
                  {isCompleted ? <Check className="h-4 w-4" /> : stepNumber}
                </div>
                <div className="md:text-center">
                  <p
                    className={cn(
                      "text-sm font-medium",
                      isCurrent ? "text-primary" : isCompleted ? "text-foreground" : "text-muted-foreground",
                    )}
                  >
                    {step}
                  </p>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
