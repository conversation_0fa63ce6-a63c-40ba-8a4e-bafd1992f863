"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { 
  AlertTriangle, 
  Clock, 
  Package, 
  Users, 
  CreditCard, 
  Star,
  ShoppingCart,
  MessageSquare,
  TrendingDown,
  AlertCircle,
  ExternalLink
} from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface AlertItem {
  id: string
  title: string
  description: string
  type: "error" | "warning" | "info" | "urgent"
  priority: "high" | "medium" | "low"
  icon: React.ElementType
  count?: number
  href?: string
  action?: string
  timestamp?: string
}

const alertData: AlertItem[] = [
  {
    id: "1",
    title: "Aplikasi Store Menunggu Persetujuan",
    description: "12 aplikasi store baru menunggu review dan persetujuan",
    type: "warning",
    priority: "high",
    icon: Clock,
    count: 12,
    href: "/tenant/dashboard/stores/applications",
    action: "Review Aplikasi",
    timestamp: "2 jam yang lalu"
  },
  {
    id: "2",
    title: "Produk Stok Hampir Habis",
    description: "25 produk memiliki stok di bawah 10 unit",
    type: "error",
    priority: "high",
    icon: Package,
    count: 25,
    href: "/tenant/dashboard/products?filter=low-stock",
    action: "Lihat Produk",
    timestamp: "1 jam yang lalu"
  },
  {
    id: "3",
    title: "Review Negatif Belum Direspon",
    description: "8 review dengan rating 1-2 bintang belum mendapat respon",
    type: "warning",
    priority: "medium",
    icon: Star,
    count: 8,
    href: "/tenant/dashboard/customers/reviews?filter=negative",
    action: "Kelola Review",
    timestamp: "3 jam yang lalu"
  },
  {
    id: "4",
    title: "Pembayaran Gagal",
    description: "15 transaksi gagal dalam 24 jam terakhir",
    type: "error",
    priority: "high",
    icon: CreditCard,
    count: 15,
    href: "/tenant/dashboard/financial/transactions?filter=failed",
    action: "Periksa Transaksi",
    timestamp: "30 menit yang lalu"
  },
  {
    id: "5",
    title: "Pesanan Belum Diproses",
    description: "18 pesanan baru menunggu konfirmasi dari toko",
    type: "urgent",
    priority: "high",
    icon: ShoppingCart,
    count: 18,
    href: "/tenant/dashboard/orders?status=pending",
    action: "Lihat Pesanan",
    timestamp: "15 menit yang lalu"
  },
  {
    id: "6",
    title: "Komplain Customer",
    description: "5 komplain customer belum mendapat tanggapan",
    type: "warning",
    priority: "medium",
    icon: MessageSquare,
    count: 5,
    href: "/tenant/dashboard/customers?filter=complaints",
    action: "Tangani Komplain",
    timestamp: "4 jam yang lalu"
  },
  {
    id: "7",
    title: "Penurunan Konversi",
    description: "Conversion rate turun 15% dibanding minggu lalu",
    type: "info",
    priority: "medium",
    icon: TrendingDown,
    href: "/tenant/dashboard/analytics/customers",
    action: "Analisis Data",
    timestamp: "1 hari yang lalu"
  },
  {
    id: "8",
    title: "Domain SSL Akan Expire",
    description: "Sertifikat SSL akan berakhir dalam 7 hari",
    type: "warning",
    priority: "medium",
    icon: AlertCircle,
    href: "/tenant/dashboard/settings/domain",
    action: "Perbarui SSL",
    timestamp: "2 hari yang lalu"
  }
]

function getAlertStyle(type: AlertItem["type"]) {
  switch (type) {
    case "error":
      return {
        borderColor: "border-red-200",
        bgColor: "bg-red-50",
        iconColor: "text-red-500",
        badgeVariant: "destructive" as const
      }
    case "warning":
      return {
        borderColor: "border-yellow-200",
        bgColor: "bg-yellow-50",
        iconColor: "text-yellow-500",
        badgeVariant: "secondary" as const
      }
    case "urgent":
      return {
        borderColor: "border-orange-200",
        bgColor: "bg-orange-50",
        iconColor: "text-orange-500",
        badgeVariant: "destructive" as const
      }
    case "info":
      return {
        borderColor: "border-blue-200",
        bgColor: "bg-blue-50",
        iconColor: "text-blue-500",
        badgeVariant: "outline" as const
      }
    default:
      return {
        borderColor: "border-gray-200",
        bgColor: "bg-gray-50",
        iconColor: "text-gray-500",
        badgeVariant: "outline" as const
      }
  }
}

function AlertCard({ alert }: { alert: AlertItem }) {
  const style = getAlertStyle(alert.type)
  
  return (
    <Card className={cn("relative", style.borderColor, style.bgColor)}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className={cn("p-2 rounded-full bg-background", style.iconColor)}>
            <alert.icon className="h-4 w-4" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-foreground leading-tight">
                  {alert.title}
                  {alert.count && (
                    <Badge variant={style.badgeVariant} className="ml-2 text-xs">
                      {alert.count}
                    </Badge>
                  )}
                </h4>
                <p className="text-xs text-muted-foreground mt-1 leading-relaxed">
                  {alert.description}
                </p>
                {alert.timestamp && (
                  <p className="text-xs text-muted-foreground mt-2">
                    {alert.timestamp}
                  </p>
                )}
              </div>
              <Badge 
                variant={alert.priority === "high" ? "destructive" : 
                        alert.priority === "medium" ? "secondary" : "outline"}
                className="text-xs"
              >
                {alert.priority === "high" ? "Tinggi" : 
                 alert.priority === "medium" ? "Sedang" : "Rendah"}
              </Badge>
            </div>
            {alert.href && alert.action && (
              <div className="mt-3">
                <Button variant="outline" size="sm" asChild>
                  <Link href={alert.href} className="text-xs">
                    {alert.action}
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function AlertDashboard() {
  // Filter alerts berdasarkan prioritas
  const highPriorityAlerts = alertData.filter(alert => alert.priority === "high")
  const otherAlerts = alertData.filter(alert => alert.priority !== "high")

  const totalAlerts = alertData.length
  const highPriorityCount = highPriorityAlerts.length

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Alert Dashboard
              {totalAlerts > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {totalAlerts}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Item yang memerlukan perhatian Anda
              {highPriorityCount > 0 && (
                <span className="text-red-500 font-medium">
                  {" "}• {highPriorityCount} prioritas tinggi
                </span>
              )}
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" asChild>
            <Link href="/tenant/dashboard/alerts">
              Lihat Semua
              <ExternalLink className="ml-1 h-3 w-3" />
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {totalAlerts === 0 ? (
          <div className="text-center py-8">
            <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <Package className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-sm font-medium text-foreground mb-1">
              Semua Baik-baik Saja
            </h3>
            <p className="text-xs text-muted-foreground">
              Tidak ada item yang memerlukan perhatian saat ini
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {/* High Priority Alerts */}
            {highPriorityCount > 0 && (
              <div>
                <h4 className="text-sm font-medium text-red-600 mb-2">
                  Prioritas Tinggi ({highPriorityCount})
                </h4>
                <div className="space-y-2">
                  {highPriorityAlerts.map((alert) => (
                    <AlertCard key={alert.id} alert={alert} />
                  ))}
                </div>
              </div>
            )}

            {/* Other Alerts */}
            {otherAlerts.length > 0 && (
              <div>
                {highPriorityCount > 0 && (
                  <h4 className="text-sm font-medium text-muted-foreground mb-2 mt-4">
                    Lainnya ({otherAlerts.length})
                  </h4>
                )}
                <div className="space-y-2">
                  {otherAlerts.slice(0, 3).map((alert) => (
                    <AlertCard key={alert.id} alert={alert} />
                  ))}
                </div>
                {otherAlerts.length > 3 && (
                  <div className="mt-3 text-center">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href="/tenant/dashboard/alerts">
                        Lihat {otherAlerts.length - 3} alert lainnya
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
