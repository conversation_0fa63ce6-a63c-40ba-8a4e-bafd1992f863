import { motion, AnimatePresence } from 'framer-motion';
import type { Category } from '@/components/themes/velozio/types';

interface SubcategoryViewProps {
  category: Category;
  subcategories: Category[];
  onBack: () => void;
  onSubcategoryClick: (subcategory: Category) => void;
}

const SubcategoryView: React.FC<SubcategoryViewProps> = ({
  category,
  subcategories,
  onBack,
  onSubcategoryClick,
}) => {
  // Ikon untuk subkategori
  const subcategoryIcons: Record<string, React.ReactNode> = {
    'Handphone': '📱',
    'Laptop': '💻',
    'Tablet': '📱',
    'Pria': '👨',
    'Wanita': '👩',
    'Anak': '👶',
    'Makanan Instan': '🍜',
    'Minuman': '🥤',
    'default': '📦'
  };

  return (
    <div className="w-full bg-white rounded-lg shadow-sm">
      {/* Header Subkategori */}
      <div className="flex items-center p-4 border-b border-gray-100">
        <button 
          onClick={onBack}
          className="mr-3 p-1 rounded-full hover:bg-gray-100 transition-colors"
          aria-label="Kembali ke kategori"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="24" 
            height="24" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
            className="text-gray-600"
          >
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
        </button>
        <h2 className="text-lg font-medium text-gray-800">{category.name}</h2>
      </div>

      {/* Daftar Subkategori */}
      <div className="p-4">
        <div className="grid grid-cols-4 gap-3">
          {subcategories.map((subcategory) => (
            <motion.div
              key={subcategory.id}
              className="flex flex-col items-center p-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              onClick={() => onSubcategoryClick(subcategory)}
            >
              <div 
                className="w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-1"
                style={{ backgroundColor: `${category.color}1a` }}
              >
                {subcategoryIcons[subcategory.name] || subcategoryIcons['default']}
              </div>
              <span className="text-xs text-center font-medium text-gray-700 line-clamp-2">
                {subcategory.name}
              </span>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SubcategoryView;
