"use client"

import { useState, useEffect } from "react"
import {
  CalendarIcon,
  ChevronDownIcon,
  FilterIcon,
  PlusIcon,
  SearchIcon,
  EyeIcon,
  EditIcon,
  TrashIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  ImageIcon,
  GripIcon,
  BarChart3Icon,
  TrendingUpIcon,
  UsersIcon,
  MousePointerClickIcon,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { DateRangePicker } from "@/components/analytics/date-range-picker"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { AnalyticsTimeSeriesChart } from "@/components/analytics/analytics-time-series-chart"
import { AnalyticsPieChart } from "@/components/analytics/analytics-pie-chart"

// Mock data for featured items
const featuredItems = [
  {
    id: "1",
    title: "Summer Collection",
    type: "Carousel",
    location: "Homepage",
    startDate: "2023-06-01",
    endDate: "2023-08-31",
    status: "Active",
    views: 45890,
    clicks: 12450,
    ctr: "27.1%",
  },
  {
    id: "2",
    title: "New Arrivals",
    type: "Banner",
    location: "Category Pages",
    startDate: "2023-07-15",
    endDate: "2023-09-15",
    status: "Active",
    views: 32560,
    clicks: 8760,
    ctr: "26.9%",
  },
  {
    id: "3",
    title: "Featured Stores",
    type: "Grid",
    location: "Homepage",
    startDate: "2023-05-01",
    endDate: "2023-12-31",
    status: "Active",
    views: 78450,
    clicks: 15670,
    ctr: "20.0%",
  },
  {
    id: "4",
    title: "Back to School",
    type: "Banner",
    location: "Homepage",
    startDate: "2023-08-01",
    endDate: "2023-09-15",
    status: "Active",
    views: 28760,
    clicks: 6540,
    ctr: "22.7%",
  },
  {
    id: "5",
    title: "Holiday Special",
    type: "Carousel",
    location: "Homepage",
    startDate: "2023-12-01",
    endDate: "2023-12-31",
    status: "Scheduled",
    views: 0,
    clicks: 0,
    ctr: "0%",
  },
  {
    id: "6",
    title: "Top Rated Products",
    type: "Grid",
    location: "Product Pages",
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    status: "Active",
    views: 124500,
    clicks: 34560,
    ctr: "27.8%",
  },
]

// Mock data for featured stores
const featuredStores = [
  {
    id: "1",
    name: "Tech Haven",
    logo: "/brand-logo-1.png",
    category: "Electronics",
    featured: true,
    clicks: 4560,
    conversion: "3.2%",
  },
  {
    id: "2",
    name: "Fashion Forward",
    logo: "/brand-logo-2.png",
    category: "Apparel",
    featured: true,
    clicks: 3870,
    conversion: "4.1%",
  },
  {
    id: "3",
    name: "Home Essentials",
    logo: "/brand-logo-3.png",
    category: "Home & Garden",
    featured: true,
    clicks: 2980,
    conversion: "2.8%",
  },
  {
    id: "4",
    name: "Gourmet Delights",
    logo: "/brand-logo-4.png",
    category: "Food & Beverage",
    featured: false,
    clicks: 1450,
    conversion: "3.5%",
  },
  {
    id: "5",
    name: "Fitness Pro",
    logo: "/brand-logo-5.png",
    category: "Sports & Fitness",
    featured: false,
    clicks: 2340,
    conversion: "2.9%",
  },
  {
    id: "6",
    name: "Kids Corner",
    logo: "/brand-logo-6.png",
    category: "Toys & Kids",
    featured: false,
    clicks: 1870,
    conversion: "3.7%",
  },
]

// Mock data for featured products
const featuredProducts = [
  {
    id: "1",
    name: "Wireless Noise-Cancelling Headphones",
    image: "/product-image-1.png",
    category: "Electronics",
    featured: true,
    clicks: 2340,
    conversion: "4.2%",
  },
  {
    id: "2",
    name: "Premium Leather Backpack",
    image: "/product-image-2.png",
    category: "Accessories",
    featured: true,
    clicks: 1980,
    conversion: "3.8%",
  },
  {
    id: "3",
    name: "Smart Fitness Watch",
    image: "/product-image-3.png",
    category: "Electronics",
    featured: true,
    clicks: 3450,
    conversion: "5.1%",
  },
  {
    id: "4",
    name: "Organic Cotton T-Shirt",
    image: "/product-image-4.png",
    category: "Apparel",
    featured: false,
    clicks: 1240,
    conversion: "2.9%",
  },
  {
    id: "5",
    name: "Stainless Steel Water Bottle",
    image: "/product-image-5.png",
    category: "Home & Garden",
    featured: false,
    clicks: 980,
    conversion: "2.5%",
  },
  {
    id: "6",
    name: "Bluetooth Portable Speaker",
    image: "/product-image-6.png",
    category: "Electronics",
    featured: false,
    clicks: 1670,
    conversion: "3.4%",
  },
]

// Mock data for performance over time
const performanceData = [
  { date: "2023-06-01", views: 15000, clicks: 4200, conversions: 420 },
  { date: "2023-07-01", views: 18500, clicks: 5100, conversions: 510 },
  { date: "2023-08-01", views: 22000, clicks: 6300, conversions: 630 },
  { date: "2023-09-01", views: 19800, clicks: 5500, conversions: 550 },
  { date: "2023-10-01", views: 21500, clicks: 6100, conversions: 610 },
  { date: "2023-11-01", views: 24000, clicks: 7200, conversions: 720 },
]

// Mock data for traffic sources
const trafficSourcesData = [
  { name: "Homepage", value: 45 },
  { name: "Category Pages", value: 25 },
  { name: "Product Pages", value: 15 },
  { name: "Search Results", value: 10 },
  { name: "External", value: 5 },
]

// Fallback data for charts
const fallbackTimeSeriesData = [
  { date: "2023-01-01", views: 0, clicks: 0, conversions: 0 },
  { date: "2023-02-01", views: 0, clicks: 0, conversions: 0 },
]

const fallbackPieData = [{ name: "No Data", value: 100 }]

export default function FeaturedContent() {
  const [dateRange, setDateRange] = useState({
    from: new Date(2023, 0, 1),
    to: new Date(2023, 11, 31),
  })
  const [activeTab, setActiveTab] = useState("featured-items")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [topItems, setTopItems] = useState([])
  const [chartData, setChartData] = useState(fallbackTimeSeriesData)
  const [pieData, setPieData] = useState(fallbackPieData)

  // Simulate data loading and processing
  useEffect(() => {
    try {
      setIsLoading(true)
      setError(null)

      // Process data for top performing items
      const processedItems = Array.isArray(featuredItems)
        ? [...featuredItems]
            .filter((item) => item && typeof item === "object" && item.views && item.views > 0)
            .sort((a, b) => (b.views || 0) - (a.views || 0))
            .slice(0, 5)
        : []

      setTopItems(processedItems)

      // Set chart data if available, otherwise use fallback
      setChartData(
        Array.isArray(performanceData) && performanceData.length > 0 ? performanceData : fallbackTimeSeriesData,
      )

      // Set pie data if available, otherwise use fallback
      setPieData(
        Array.isArray(trafficSourcesData) && trafficSourcesData.length > 0 ? trafficSourcesData : fallbackPieData,
      )

      setIsLoading(false)
    } catch (err) {
      console.error("Error processing data:", err)
      setError("Failed to process data")
      setIsLoading(false)

      // Set fallback data
      setTopItems([])
      setChartData(fallbackTimeSeriesData)
      setPieData(fallbackPieData)
    }
  }, [])

  // Safe render function for top items
  const renderTopItems = () => {
    if (!Array.isArray(topItems) || topItems.length === 0) {
      return <div className="py-8 text-center text-muted-foreground">No performance data available</div>
    }

    return topItems.map((item, index) => {
      if (!item || typeof item !== "object") return null

      const title = item.title || "Untitled Item"
      const type = item.type || "Unknown"
      const location = item.location || "Unknown"
      const views = item.views || 0

      return (
        <div key={item.id || index} className="flex items-center">
          <div className="flex h-10 w-10 items-center justify-center rounded-md bg-muted">{index + 1}</div>
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">{title}</p>
            <p className="text-sm text-muted-foreground">
              {type} • {location}
            </p>
          </div>
          <div className="ml-auto text-sm">{views.toLocaleString()} views</div>
        </div>
      )
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold tracking-tight">Featured Content</h1>
        <div className="flex items-center gap-2">
          <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" />
            New Featured Item
          </Button>
        </div>
      </div>

      <Tabs defaultValue="featured-items" onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="featured-items">Featured Items</TabsTrigger>
          <TabsTrigger value="carousel-manager">Carousel Manager</TabsTrigger>
          <TabsTrigger value="featured-stores">Featured Stores</TabsTrigger>
          <TabsTrigger value="featured-products">Featured Products</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="featured-items" className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <div className="relative">
                <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" placeholder="Search featured items..." className="w-full md:w-[300px] pl-8" />
              </div>
              <Select defaultValue="all">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="ended">Ended</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <FilterIcon className="mr-2 h-4 w-4" />
                Filter
              </Button>
              <Button variant="outline" size="sm">
                <ChevronDownIcon className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>

          <div className="rounded-md border">
            <table className="w-full caption-bottom text-sm">
              <thead>
                <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                  <th className="h-12 px-4 text-left align-middle font-medium">Title</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Type</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Location</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Date Range</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Performance</th>
                  <th className="h-12 px-4 text-left align-middle font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {Array.isArray(featuredItems) && featuredItems.length > 0 ? (
                  featuredItems.map((item) => (
                    <tr
                      key={item?.id || Math.random().toString()}
                      className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                    >
                      <td className="p-4 align-middle">{item?.title || "Untitled"}</td>
                      <td className="p-4 align-middle">{item?.type || "Unknown"}</td>
                      <td className="p-4 align-middle">{item?.location || "Unknown"}</td>
                      <td className="p-4 align-middle">
                        {item?.startDate ? new Date(item.startDate).toLocaleDateString() : "N/A"} -
                        {item?.endDate ? new Date(item.endDate).toLocaleDateString() : "N/A"}
                      </td>
                      <td className="p-4 align-middle">
                        <Badge
                          variant={
                            item?.status === "Active"
                              ? "default"
                              : item?.status === "Scheduled"
                                ? "secondary"
                                : item?.status === "Ended"
                                  ? "outline"
                                  : "destructive"
                          }
                        >
                          {item?.status || "Unknown"}
                        </Badge>
                      </td>
                      <td className="p-4 align-middle">
                        <div className="text-xs space-y-1">
                          <div>Views: {(item?.views || 0).toLocaleString()}</div>
                          <div>Clicks: {(item?.clicks || 0).toLocaleString()}</div>
                          <div>CTR: {item?.ctr || "0%"}</div>
                        </div>
                      </td>
                      <td className="p-4 align-middle">
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="icon">
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <EditIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="p-4 text-center text-muted-foreground">
                      No featured items found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>

        <TabsContent value="carousel-manager" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Homepage Featured Carousel</CardTitle>
              <CardDescription>Manage the items in your homepage featured carousel</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <h3 className="font-medium">Carousel Settings</h3>
                  <p className="text-sm text-muted-foreground">Configure how the carousel appears and behaves</p>
                </div>
                <Button variant="outline">
                  <EyeIcon className="mr-2 h-4 w-4" />
                  Preview
                </Button>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="carousel-title">Carousel Title</Label>
                  <Input id="carousel-title" defaultValue="Summer Collection" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="carousel-location">Display Location</Label>
                  <Select defaultValue="homepage">
                    <SelectTrigger id="carousel-location">
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="homepage">Homepage</SelectItem>
                      <SelectItem value="category">Category Pages</SelectItem>
                      <SelectItem value="product">Product Pages</SelectItem>
                      <SelectItem value="checkout">Checkout Page</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="carousel-type">Carousel Type</Label>
                  <Select defaultValue="auto">
                    <SelectTrigger id="carousel-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">Auto-rotating</SelectItem>
                      <SelectItem value="manual">Manual Navigation</SelectItem>
                      <SelectItem value="both">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <div className="grid gap-2 md:grid-cols-2">
                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                      <Input type="date" defaultValue="2023-06-01" />
                    </div>
                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                      <Input type="date" defaultValue="2023-08-31" />
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Display Options</Label>
                  <div className="grid gap-2 md:grid-cols-2">
                    <div className="flex items-center space-x-2">
                      <Switch id="auto-rotate" defaultChecked />
                      <Label htmlFor="auto-rotate">Auto-rotate</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="show-indicators" defaultChecked />
                      <Label htmlFor="show-indicators">Show Indicators</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="show-arrows" defaultChecked />
                      <Label htmlFor="show-arrows">Show Arrows</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="infinite-loop" defaultChecked />
                      <Label htmlFor="infinite-loop">Infinite Loop</Label>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Carousel Items</h3>
                  <Button size="sm">
                    <PlusIcon className="mr-2 h-4 w-4" />
                    Add Item
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="rounded-md border">
                    <div className="flex items-center p-4">
                      <div className="flex items-center gap-2">
                        <GripIcon className="h-4 w-4 text-muted-foreground" />
                        <div className="h-16 w-16 rounded-md bg-muted flex items-center justify-center">
                          <ImageIcon className="h-8 w-8 text-muted-foreground" />
                        </div>
                      </div>
                      <div className="ml-4 flex-1 space-y-1">
                        <div className="font-medium">Summer Collection Banner</div>
                        <div className="text-sm text-muted-foreground">Homepage • Active</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon">
                          <ArrowLeftIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <ArrowRightIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <EditIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-md border">
                    <div className="flex items-center p-4">
                      <div className="flex items-center gap-2">
                        <GripIcon className="h-4 w-4 text-muted-foreground" />
                        <div className="h-16 w-16 rounded-md bg-muted flex items-center justify-center">
                          <ImageIcon className="h-8 w-8 text-muted-foreground" />
                        </div>
                      </div>
                      <div className="ml-4 flex-1 space-y-1">
                        <div className="font-medium">New Arrivals Showcase</div>
                        <div className="text-sm text-muted-foreground">Homepage • Active</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon">
                          <ArrowLeftIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <ArrowRightIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <EditIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-md border">
                    <div className="flex items-center p-4">
                      <div className="flex items-center gap-2">
                        <GripIcon className="h-4 w-4 text-muted-foreground" />
                        <div className="h-16 w-16 rounded-md bg-muted flex items-center justify-center">
                          <ImageIcon className="h-8 w-8 text-muted-foreground" />
                        </div>
                      </div>
                      <div className="ml-4 flex-1 space-y-1">
                        <div className="font-medium">Limited Time Offers</div>
                        <div className="text-sm text-muted-foreground">Homepage • Active</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon">
                          <ArrowLeftIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <ArrowRightIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <EditIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel</Button>
              <Button>Save Changes</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="featured-stores" className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <div className="relative">
                <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" placeholder="Search stores..." className="w-full md:w-[300px] pl-8" />
              </div>
              <Select defaultValue="all">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="electronics">Electronics</SelectItem>
                  <SelectItem value="apparel">Apparel</SelectItem>
                  <SelectItem value="home">Home & Garden</SelectItem>
                  <SelectItem value="food">Food & Beverage</SelectItem>
                  <SelectItem value="sports">Sports & Fitness</SelectItem>
                  <SelectItem value="toys">Toys & Kids</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button>
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Featured Store
            </Button>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Array.isArray(featuredStores) && featuredStores.length > 0 ? (
              featuredStores.map((store) => (
                <Card key={store?.id || Math.random().toString()} className={store?.featured ? "border-primary" : ""}>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-10 w-10 rounded-md overflow-hidden">
                          <img
                            src={store?.logo || "/placeholder.svg"}
                            alt={store?.name || "Store"}
                            className="h-full w-full object-cover"
                          />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{store?.name || "Unnamed Store"}</CardTitle>
                          <CardDescription>{store?.category || "Uncategorized"}</CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Checkbox id={`featured-${store?.id || "unknown"}`} checked={store?.featured || false} />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <div className="text-muted-foreground">Clicks</div>
                        <div className="font-medium">{(store?.clicks || 0).toLocaleString()}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Conversion</div>
                        <div className="font-medium">{store?.conversion || "0%"}</div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="ghost" size="sm">
                      <EyeIcon className="mr-2 h-4 w-4" />
                      View
                    </Button>
                    <Button variant="ghost" size="sm">
                      <EditIcon className="mr-2 h-4 w-4" />
                      Edit
                    </Button>
                  </CardFooter>
                </Card>
              ))
            ) : (
              <div className="col-span-3 py-8 text-center text-muted-foreground">No featured stores found</div>
            )}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Featured Stores Settings</CardTitle>
              <CardDescription>Configure how featured stores are displayed</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="display-location">Display Location</Label>
                  <Select defaultValue="homepage">
                    <SelectTrigger id="display-location">
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="homepage">Homepage</SelectItem>
                      <SelectItem value="sidebar">Sidebar</SelectItem>
                      <SelectItem value="category">Category Pages</SelectItem>
                      <SelectItem value="footer">Footer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="display-style">Display Style</Label>
                  <Select defaultValue="grid">
                    <SelectTrigger id="display-style">
                      <SelectValue placeholder="Select style" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="grid">Grid</SelectItem>
                      <SelectItem value="carousel">Carousel</SelectItem>
                      <SelectItem value="list">List</SelectItem>
                      <SelectItem value="compact">Compact</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-stores">Maximum Stores</Label>
                  <Input id="max-stores" type="number" defaultValue="6" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sort-order">Sort Order</Label>
                  <Select defaultValue="featured">
                    <SelectTrigger id="sort-order">
                      <SelectValue placeholder="Select order" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="featured">Featured First</SelectItem>
                      <SelectItem value="alphabetical">Alphabetical</SelectItem>
                      <SelectItem value="popular">Most Popular</SelectItem>
                      <SelectItem value="newest">Newest First</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Display Options</Label>
                <div className="grid gap-2 md:grid-cols-2">
                  <div className="flex items-center space-x-2">
                    <Switch id="show-logo" defaultChecked />
                    <Label htmlFor="show-logo">Show Logo</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="show-category" defaultChecked />
                    <Label htmlFor="show-category">Show Category</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="show-description" />
                    <Label htmlFor="show-description">Show Description</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="show-rating" defaultChecked />
                    <Label htmlFor="show-rating">Show Rating</Label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel</Button>
              <Button>Save Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="featured-products" className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <div className="relative">
                <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" placeholder="Search products..." className="w-full md:w-[300px] pl-8" />
              </div>
              <Select defaultValue="all">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="electronics">Electronics</SelectItem>
                  <SelectItem value="apparel">Apparel</SelectItem>
                  <SelectItem value="accessories">Accessories</SelectItem>
                  <SelectItem value="home">Home & Garden</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button>
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Featured Product
            </Button>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Array.isArray(featuredProducts) && featuredProducts.length > 0 ? (
              featuredProducts.map((product) => (
                <Card
                  key={product?.id || Math.random().toString()}
                  className={product?.featured ? "border-primary" : ""}
                >
                  <CardHeader className="pb-2">
                    <div className="aspect-video relative rounded-md overflow-hidden">
                      <img
                        src={product?.image || "/placeholder.svg"}
                        alt={product?.name || "Product"}
                        className="h-full w-full object-cover"
                      />
                      <div className="absolute top-2 right-2">
                        <Checkbox id={`featured-${product?.id || "unknown"}`} checked={product?.featured || false} />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="space-y-1">
                      <CardTitle className="text-lg">{product?.name || "Unnamed Product"}</CardTitle>
                      <CardDescription>{product?.category || "Uncategorized"}</CardDescription>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm mt-2">
                      <div>
                        <div className="text-muted-foreground">Clicks</div>
                        <div className="font-medium">{(product?.clicks || 0).toLocaleString()}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Conversion</div>
                        <div className="font-medium">{product?.conversion || "0%"}</div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="ghost" size="sm">
                      <EyeIcon className="mr-2 h-4 w-4" />
                      View
                    </Button>
                    <Button variant="ghost" size="sm">
                      <EditIcon className="mr-2 h-4 w-4" />
                      Edit
                    </Button>
                  </CardFooter>
                </Card>
              ))
            ) : (
              <div className="col-span-3 py-8 text-center text-muted-foreground">No featured products found</div>
            )}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Featured Products Settings</CardTitle>
              <CardDescription>Configure how featured products are displayed</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="product-display-location">Display Location</Label>
                  <Select defaultValue="homepage">
                    <SelectTrigger id="product-display-location">
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="homepage">Homepage</SelectItem>
                      <SelectItem value="category">Category Pages</SelectItem>
                      <SelectItem value="product">Product Pages (Related)</SelectItem>
                      <SelectItem value="checkout">Checkout Page</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="product-display-style">Display Style</Label>
                  <Select defaultValue="grid">
                    <SelectTrigger id="product-display-style">
                      <SelectValue placeholder="Select style" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="grid">Grid</SelectItem>
                      <SelectItem value="carousel">Carousel</SelectItem>
                      <SelectItem value="list">List</SelectItem>
                      <SelectItem value="compact">Compact</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-products">Maximum Products</Label>
                  <Input id="max-products" type="number" defaultValue="6" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="product-sort-order">Sort Order</Label>
                  <Select defaultValue="featured">
                    <SelectTrigger id="product-sort-order">
                      <SelectValue placeholder="Select order" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="featured">Featured First</SelectItem>
                      <SelectItem value="price-low">Price: Low to High</SelectItem>
                      <SelectItem value="price-high">Price: High to Low</SelectItem>
                      <SelectItem value="popular">Most Popular</SelectItem>
                      <SelectItem value="newest">Newest First</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Display Options</Label>
                <div className="grid gap-2 md:grid-cols-2">
                  <div className="flex items-center space-x-2">
                    <Switch id="show-price" defaultChecked />
                    <Label htmlFor="show-price">Show Price</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="show-product-category" defaultChecked />
                    <Label htmlFor="show-product-category">Show Category</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="show-product-rating" defaultChecked />
                    <Label htmlFor="show-product-rating">Show Rating</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="show-quick-add" defaultChecked />
                    <Label htmlFor="show-quick-add">Show Quick Add</Label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel</Button>
              <Button>Save Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-10">
              <div className="text-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading performance data...</p>
              </div>
            </div>
          ) : error ? (
            <div className="bg-destructive/10 p-4 rounded-md text-center">
              <p className="text-destructive font-medium">Error loading performance data</p>
              <p className="text-sm text-muted-foreground mt-1">{error}</p>
              <Button variant="outline" className="mt-4">
                Retry
              </Button>
            </div>
          ) : (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Featured Content Performance</CardTitle>
                  <CardDescription>Track the performance of your featured content over time</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="h-[300px]">
                    {chartData && chartData.length > 0 ? (
                      <AnalyticsTimeSeriesChart
                        data={chartData}
                        categories={["views", "clicks", "conversions"]}
                        index="date"
                        colors={["#94a3b8", "#3b82f6", "#10b981"]}
                        valueFormatter={(value) => value.toString()}
                      />
                    ) : (
                      <div className="h-full flex items-center justify-center bg-muted/20 rounded-md">
                        <div className="text-center">
                          <BarChart3Icon className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                          <p className="text-muted-foreground">No chart data available</p>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                      <CardHeader className="pb-2">
                        <div className="flex items-center space-x-2">
                          <EyeIcon className="h-4 w-4 text-muted-foreground" />
                          <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">120,800</div>
                        <p className="text-xs text-muted-foreground">
                          <span className="text-green-500">+12.5%</span> from previous period
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <div className="flex items-center space-x-2">
                          <MousePointerClickIcon className="h-4 w-4 text-muted-foreground" />
                          <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">34,400</div>
                        <p className="text-xs text-muted-foreground">
                          <span className="text-green-500">+8.2%</span> from previous period
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <div className="flex items-center space-x-2">
                          <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
                          <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">3.4%</div>
                        <p className="text-xs text-muted-foreground">
                          <span className="text-green-500">+0.5%</span> from previous period
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>

              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Traffic Sources</CardTitle>
                        <CardDescription>Where visitors are coming from</CardDescription>
                      </div>
                      <UsersIcon className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      {pieData && pieData.length > 0 ? (
                        <AnalyticsPieChart
                          data={pieData}
                          category="value"
                          index="name"
                          colors={["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6"]}
                          valueFormatter={(value) => `${value}%`}
                        />
                      ) : (
                        <div className="h-full flex items-center justify-center bg-muted/20 rounded-md">
                          <div className="text-center">
                            <BarChart3Icon className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                            <p className="text-muted-foreground">No chart data available</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Top Performing Content</CardTitle>
                        <CardDescription>Content with the highest engagement</CardDescription>
                      </div>
                      <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">{renderTopItems()}</div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
