"use client"

import { createContext, useContext, useState, useRef, useCallback, type ReactNode } from "react"

type NotificationType = "success" | "error" | "info" | "warning"

interface Notification {
  message: string
  type: NotificationType
  id: string
  title?: string
}

interface NotificationsContextType {
  notifications: Notification[]
  showNotification: (message: string, type: NotificationType, title?: string) => string
  addNotification: (notification: Omit<Notification, "id">) => string
  dismissNotification: (id: string) => void
}

const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined)

export function NotificationsProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  // Gunakan useRef untuk menyimpan timeout IDs
  const timeoutIdsRef = useRef<Record<string, NodeJS.Timeout>>({})

  // Gunakan useCallback untuk memastikan fungsi tidak berubah pada setiap render
  const dismissNotification = useCallback((id: string) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id))

    // Hapus timeout jika ada
    if (timeoutIdsRef.current[id]) {
      clearTimeout(timeoutIdsRef.current[id])
      delete timeoutIdsRef.current[id]
    }
  }, [])

  const addNotification = useCallback(
    (notification: Omit<Notification, "id">) => {
      const id = Math.random().toString(36).substring(2, 9)
      const newNotification = { ...notification, id }

      setNotifications((prev) => [...prev, newNotification])

      // Simpan timeout ID di ref
      timeoutIdsRef.current[id] = setTimeout(() => {
        dismissNotification(id)
      }, 5000)

      return id
    },
    [dismissNotification],
  )

  const showNotification = useCallback(
    (message: string, type: NotificationType, title?: string) => {
      return addNotification({ message, type, title })
    },
    [addNotification],
  )

  return (
    <NotificationsContext.Provider
      value={{
        notifications,
        showNotification,
        addNotification,
        dismissNotification,
      }}
    >
      {children}

      {/* Render notifications */}
      <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`px-4 py-2 rounded shadow-md ${
              notification.type === "success"
                ? "bg-green-100 text-green-800"
                : notification.type === "error"
                  ? "bg-red-100 text-red-800"
                  : notification.type === "warning"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-blue-100 text-blue-800"
            }`}
          >
            <div className="flex justify-between">
              <div>
                {notification.title && <p className="font-bold">{notification.title}</p>}
                <p>{notification.message}</p>
              </div>
              <button
                onClick={() => dismissNotification(notification.id)}
                className="ml-4 text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            </div>
          </div>
        ))}
      </div>
    </NotificationsContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationsContext)
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationsProvider")
  }
  return context
}
