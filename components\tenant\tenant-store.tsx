"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { storesAPI, type Store } from "@/lib/api/stores"

interface TenantStoreProps {
  slug: string
}

export function TenantStore({ slug }: TenantStoreProps) {
  const [tenant, setTenant] = useState<any | null>(null)
  const [stores, setStores] = useState<Store[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Simulasi fetch data tenant dan stores dari API
    // Dalam implementasi sebenarnya, Anda perlu membuat API untuk mendapatkan tenant berdasarkan slug
    const fetchTenantAndStores = async () => {
      try {
        // Simulasi data tenant
        const tenantData = {
          id: "1",
          name: slug.charAt(0).toUpperCase() + slug.slice(1),
          description: `Ini adalah tenant ${slug} untuk Sellzio SaaS`,
          slug: slug,
          logo: null,
          banner: null,
        }

        // Fetch stores berdasarkan tenant ID
        const storesData = await storesAPI.getAll(undefined, tenantData.id)

        setTenant(tenantData)
        setStores(storesData)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching tenant data:", err)
        setError("Gagal memuat data tenant. Silakan coba lagi.")
        setLoading(false)
      }
    }

    fetchTenantAndStores()
  }, [slug])

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="h-64 bg-muted">
          <Skeleton className="h-full w-full" />
        </div>
        <div className="container mx-auto px-4 py-8">
          <Skeleton className="h-10 w-64 mb-6" />
          <Skeleton className="h-6 w-full max-w-2xl mb-8" />
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-64 w-full" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!tenant) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="max-w-md w-full">
          <CardContent className="pt-6 text-center">
            <h1 className="text-2xl font-bold mb-4">Tenant Tidak Ditemukan</h1>
            <p className="text-muted-foreground mb-6">
              Tenant dengan slug "{slug}" tidak ditemukan atau telah dihapus.
            </p>
            <Button asChild>
              <Link href="/">Kembali ke Beranda</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="h-64 bg-muted flex items-center justify-center">
        {tenant.banner ? (
          <img src={tenant.banner || "/placeholder.svg"} alt={tenant.name} className="w-full h-full object-cover" />
        ) : (
          <div className="text-6xl font-bold text-muted-foreground">{tenant.name.charAt(0)}</div>
        )}
      </div>

      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-4">{tenant.name}</h1>
        <p className="text-muted-foreground max-w-2xl mb-8">{tenant.description || "Tidak ada deskripsi"}</p>

        <h2 className="text-2xl font-semibold mb-6">Toko Kami</h2>
        {stores.length === 0 ? (
          <Card className="p-8 text-center">
            <h3 className="text-lg font-semibold mb-2">Belum ada toko</h3>
            <p className="text-muted-foreground mb-4">Tenant ini belum memiliki toko.</p>
          </Card>
        ) : (
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {stores.map((store) => (
              <Card key={store.id} className="overflow-hidden">
                <div className="h-48 bg-muted flex items-center justify-center">
                  {store.banner ? (
                    <img
                      src={store.banner || "/placeholder.svg"}
                      alt={store.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="text-4xl font-bold text-muted-foreground">{store.name.charAt(0)}</div>
                  )}
                </div>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-2">{store.name}</h3>
                  <p className="text-muted-foreground text-sm line-clamp-2 mb-4">
                    {store.description || "Tidak ada deskripsi"}
                  </p>
                  <Button asChild className="w-full">
                    <Link href={`/tenant/${slug}/store/${store.slug}`}>Kunjungi Toko</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
