"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useNotifications } from "@/components/providers/notifications-provider"
import { TenantForm } from "@/components/admin/tenants/tenant-form"
import { Button } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"

// Simulasi service untuk tenant
import { addTenant } from "@/lib/services/tenant-service"

export default function TenantCreateClient() {
  const [isClient, setIsClient] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()
  const { addNotification } = useNotifications()

  // Ensure component is mounted before rendering
  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleSubmit = async (data: any) => {
    try {
      setIsSubmitting(true)

      // Tambahkan tenant baru (simulasi atau API call sebenarnya)
      await addTenant({
        ...data,
        id: `tenant-${Date.now()}`, // Generate ID unik
        status: "active",
        storeCount: 0,
        userCount: 1, // Admin tenant
        revenue: "$0",
        createdAt: new Date().toISOString().split("T")[0], // Format: YYYY-MM-DD
      })

      // Show success notification
      addNotification({
        message: "Tenant created successfully!",
        type: "success",
      })

      // Redirect to tenants list with refresh parameter
      router.push("/admin/dashboard/tenants?refresh=true")
    } catch (error) {
      console.error("Error creating tenant:", error)

      // Show error notification
      addNotification({
        message: "Failed to create tenant. Please try again.",
        type: "error",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isClient) {
    return null
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h2 className="text-2xl font-bold tracking-tight">Create Tenant</h2>
          <p className="text-muted-foreground">Create a new tenant in the Sellzio platform</p>
        </div>
        <Button variant="outline" size="sm" onClick={() => router.push("/admin/dashboard/tenants")}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Tenants
        </Button>
      </div>

      <div className="rounded-md border p-6">
        <TenantForm onSubmit={handleSubmit} isSubmitting={isSubmitting} />
      </div>
    </div>
  )
}
