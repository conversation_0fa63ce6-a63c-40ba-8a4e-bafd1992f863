import { NextRequest, NextResponse } from 'next/server';
import { brandService } from '@/lib/services/brands';

const brands = [
  {
    name: 'Samsung',
    slug: 'samsung',
    description: 'Samsung Electronics adalah perusahaan teknologi multinasional Korea Selatan',
    logo_url: '/brands/samsung-logo.png',
    website_url: 'https://www.samsung.com',
    country: 'Korea Selatan',
    is_active: true,
    sort_order: 1,
    meta_title: 'Samsung - Produk Elektronik Terpercaya',
    meta_description: 'Temukan produk Samsung terbaru dengan teknologi canggih',
    meta_keywords: ['samsung', 'smartphone', 'galaxy', 'elektronik']
  },
  {
    name: 'Apple',
    slug: 'apple',
    description: 'Apple Inc. adalah perusahaan teknologi multinasional Amerika',
    logo_url: '/brands/apple-logo.png',
    website_url: 'https://www.apple.com',
    country: 'Amerika Serikat',
    is_active: true,
    sort_order: 2,
    meta_title: 'Apple - Inovasi Teknologi Terdepan',
    meta_description: 'Produk Apple dengan desain premium dan teknologi terdepan',
    meta_keywords: ['apple', 'iphone', 'ipad', 'macbook']
  },
  {
    name: 'Nike',
    slug: 'nike',
    description: 'Nike adalah perusahaan multinasional Amerika yang merancang dan memproduksi sepatu, pakaian, dan aksesoris olahraga',
    logo_url: '/brands/nike-logo.png',
    website_url: 'https://www.nike.com',
    country: 'Amerika Serikat',
    is_active: true,
    sort_order: 3,
    meta_title: 'Nike - Just Do It',
    meta_description: 'Produk olahraga Nike untuk performa terbaik',
    meta_keywords: ['nike', 'sepatu', 'olahraga', 'sneakers']
  },
  {
    name: 'Adidas',
    slug: 'adidas',
    description: 'Adidas adalah perusahaan multinasional Jerman yang merancang dan memproduksi sepatu, pakaian, dan aksesoris',
    logo_url: '/brands/adidas-logo.png',
    website_url: 'https://www.adidas.com',
    country: 'Jerman',
    is_active: true,
    sort_order: 4,
    meta_title: 'Adidas - Impossible is Nothing',
    meta_description: 'Produk olahraga Adidas dengan kualitas premium',
    meta_keywords: ['adidas', 'sepatu', 'olahraga', 'three stripes']
  },
  {
    name: 'Uniqlo',
    slug: 'uniqlo',
    description: 'Uniqlo adalah merek pakaian kasual Jepang yang dikenal dengan produk berkualitas tinggi',
    logo_url: '/brands/uniqlo-logo.png',
    website_url: 'https://www.uniqlo.com',
    country: 'Jepang',
    is_active: true,
    sort_order: 5,
    meta_title: 'Uniqlo - LifeWear',
    meta_description: 'Pakaian kasual berkualitas tinggi dari Jepang',
    meta_keywords: ['uniqlo', 'pakaian', 'fashion', 'lifewear']
  },
  {
    name: 'Zara',
    slug: 'zara',
    description: 'Zara adalah merek fashion asal Spanyol yang dikenal dengan tren fashion terkini',
    logo_url: '/brands/zara-logo.png',
    website_url: 'https://www.zara.com',
    country: 'Spanyol',
    is_active: true,
    sort_order: 6,
    meta_title: 'Zara - Fashion Terkini',
    meta_description: 'Fashion terkini dan trendy dari Zara',
    meta_keywords: ['zara', 'fashion', 'pakaian', 'trendy']
  },
  {
    name: 'IKEA',
    slug: 'ikea',
    description: 'IKEA adalah perusahaan furniture dan dekorasi rumah asal Swedia',
    logo_url: '/brands/ikea-logo.png',
    website_url: 'https://www.ikea.com',
    country: 'Swedia',
    is_active: true,
    sort_order: 7,
    meta_title: 'IKEA - Furniture dan Dekorasi Rumah',
    meta_description: 'Furniture dan dekorasi rumah dengan desain Skandinavia',
    meta_keywords: ['ikea', 'furniture', 'rumah', 'dekorasi']
  },
  {
    name: 'Sony',
    slug: 'sony',
    description: 'Sony Corporation adalah konglomerat teknologi multinasional Jepang',
    logo_url: '/brands/sony-logo.png',
    website_url: 'https://www.sony.com',
    country: 'Jepang',
    is_active: true,
    sort_order: 8,
    meta_title: 'Sony - Teknologi Entertainment',
    meta_description: 'Produk elektronik dan entertainment dari Sony',
    meta_keywords: ['sony', 'elektronik', 'playstation', 'audio']
  },
  {
    name: 'Xiaomi',
    slug: 'xiaomi',
    description: 'Xiaomi adalah perusahaan teknologi Tiongkok yang memproduksi smartphone dan produk elektronik',
    logo_url: '/brands/xiaomi-logo.png',
    website_url: 'https://www.mi.com',
    country: 'Tiongkok',
    is_active: true,
    sort_order: 9,
    meta_title: 'Xiaomi - Innovation for Everyone',
    meta_description: 'Produk teknologi inovatif dengan harga terjangkau',
    meta_keywords: ['xiaomi', 'smartphone', 'mi', 'redmi']
  },
  {
    name: 'H&M',
    slug: 'hm',
    description: 'H&M adalah perusahaan fashion multinasional Swedia',
    logo_url: '/brands/hm-logo.png',
    website_url: 'https://www.hm.com',
    country: 'Swedia',
    is_active: true,
    sort_order: 10,
    meta_title: 'H&M - Fashion and Quality',
    meta_description: 'Fashion berkualitas dengan harga terjangkau',
    meta_keywords: ['hm', 'fashion', 'pakaian', 'affordable']
  },
  {
    name: 'Logitech',
    slug: 'logitech',
    description: 'Logitech adalah perusahaan teknologi Swiss yang memproduksi perangkat komputer',
    logo_url: '/brands/logitech-logo.png',
    website_url: 'https://www.logitech.com',
    country: 'Swiss',
    is_active: true,
    sort_order: 11,
    meta_title: 'Logitech - Computer Peripherals',
    meta_description: 'Perangkat komputer dan gaming berkualitas tinggi',
    meta_keywords: ['logitech', 'mouse', 'keyboard', 'gaming']
  },
  {
    name: 'Canon',
    slug: 'canon',
    description: 'Canon Inc. adalah perusahaan multinasional Jepang yang memproduksi kamera dan printer',
    logo_url: '/brands/canon-logo.png',
    website_url: 'https://www.canon.com',
    country: 'Jepang',
    is_active: true,
    sort_order: 12,
    meta_title: 'Canon - Imaging Solutions',
    meta_description: 'Kamera dan printer berkualitas tinggi dari Canon',
    meta_keywords: ['canon', 'kamera', 'printer', 'fotografi']
  }
]

// POST - Seed brands data
export async function POST(request: NextRequest) {
  try {
    // Check if brands already exist
    const existingBrands = await brandService.getBrands()
    
    if (existingBrands.length > 0) {
      return NextResponse.json(
        { message: 'Brands already exist', count: existingBrands.length },
        { status: 200 }
      )
    }
    
    // Create brands one by one to handle validation
    const createdBrands = []
    const errors = []
    
    for (const brand of brands) {
      try {
        const created = await brandService.createBrand(brand)
        createdBrands.push(created)
      } catch (error) {
        console.error(`Error creating brand ${brand.name}:`, error)
        errors.push({
          name: brand.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    return NextResponse.json({
      message: 'Brands seeded successfully',
      created: createdBrands.length,
      errors: errors.length,
      errorDetails: errors
    }, { status: 201 })
    
  } catch (error) {
    console.error('Error seeding brands:', error)
    const errorMessage = error instanceof Error ? error.message : 'Failed to seed brands'
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}
