"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Separator } from "@/components/ui/separator"
import { useStoreSettings, type StoreSetting } from "@/hooks/use-store-settings"
import {
  Save,
  Settings,
  Store,
  CreditCard,
  Truck,
  Bell,
  Shield,
  Search,
  RefreshCw
} from "lucide-react"

const categoryIcons = {
  general: Store,
  commission: CreditCard,
  payment: CreditCard,
  shipping: Truck,
  notification: Bell,
  security: Shield,
  seo: Search,
}

const categoryLabels = {
  general: 'Umum',
  commission: 'Komisi',
  payment: 'Pembayaran',
  shipping: 'Pengiriman',
  notification: 'Notifikasi',
  security: 'Keamanan',
  seo: 'SEO',
}

export default function StoreSettingsPage() {
  const {
    settingsByCategory,
    loading,
    updateMultipleSettings,
    fetchSettingsByCategory
  } = useStoreSettings()

  const [formData, setFormData] = useState<Record<string, string>>({})
  const [hasChanges, setHasChanges] = useState(false)
  const [saving, setSaving] = useState(false)

  // Initialize form data when settings are loaded
  useEffect(() => {
    const initialData: Record<string, string> = {}
    Object.values(settingsByCategory).flat().forEach(setting => {
      initialData[setting.setting_key] = setting.setting_value || ''
    })
    setFormData(initialData)
    setHasChanges(false)
  }, [settingsByCategory])

  // Load settings grouped by category
  useEffect(() => {
    fetchSettingsByCategory()
  }, [fetchSettingsByCategory])

  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Only send changed values
      const originalData: Record<string, string> = {}
      Object.values(settingsByCategory).flat().forEach(setting => {
        originalData[setting.setting_key] = setting.setting_value || ''
      })

      const changedData: Record<string, string> = {}
      Object.entries(formData).forEach(([key, value]) => {
        if (originalData[key] !== value) {
          changedData[key] = value
        }
      })

      if (Object.keys(changedData).length === 0) {
        setHasChanges(false)
        return
      }

      const success = await updateMultipleSettings(changedData)
      if (success) {
        setHasChanges(false)
      }
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    const initialData: Record<string, string> = {}
    Object.values(settingsByCategory).flat().forEach(setting => {
      initialData[setting.setting_key] = setting.setting_value || ''
    })
    setFormData(initialData)
    setHasChanges(false)
  }

  const renderSettingInput = (setting: StoreSetting) => {
    const value = formData[setting.setting_key] || ''

    switch (setting.setting_type) {
      case 'text':
      case 'email':
      case 'tel':
        return (
          <Input
            type={setting.setting_type}
            value={value}
            onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
            placeholder={setting.description}
            required={setting.is_required}
          />
        )

      case 'textarea':
        return (
          <Textarea
            value={value}
            onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
            placeholder={setting.description}
            required={setting.is_required}
            rows={3}
          />
        )

      case 'number':
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
            placeholder={setting.description}
            required={setting.is_required}
            min={setting.validation_rules.min}
            max={setting.validation_rules.max}
            step={setting.validation_rules.step || 1}
          />
        )

      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Switch
              checked={value === 'true'}
              onCheckedChange={(checked) => handleInputChange(setting.setting_key, checked.toString())}
            />
            <Label className="text-sm text-muted-foreground">
              {value === 'true' ? 'Aktif' : 'Tidak Aktif'}
            </Label>
          </div>
        )

      case 'select':
        return (
          <Select
            value={value}
            onValueChange={(newValue) => handleInputChange(setting.setting_key, newValue)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Pilih opsi..." />
            </SelectTrigger>
            <SelectContent>
              {setting.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'file':
        return (
          <div className="space-y-2">
            <Input
              type="file"
              accept={setting.validation_rules.file_types?.map(type => `.${type}`).join(',')}
              onChange={(e) => {
                const file = e.target.files?.[0]
                if (file) {
                  // In a real app, you would upload the file and get a URL
                  handleInputChange(setting.setting_key, file.name)
                }
              }}
            />
            {value && (
              <p className="text-sm text-muted-foreground">
                File saat ini: {value}
              </p>
            )}
          </div>
        )

      default:
        return (
          <Input
            value={value}
            onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
            placeholder={setting.description}
            required={setting.is_required}
          />
        )
    }
  }

  const categories = Object.keys(settingsByCategory)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pengaturan Store</h1>
          <p className="text-muted-foreground">
            Kelola konfigurasi dan pengaturan store Anda
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={!hasChanges || saving}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset
          </Button>
          <Button
            onClick={handleSave}
            disabled={!hasChanges || saving}
          >
            <Save className="mr-2 h-4 w-4" />
            {saving ? 'Menyimpan...' : 'Simpan Perubahan'}
          </Button>
        </div>
      </div>

      {/* Status */}
      {hasChanges && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-orange-800">
              <Settings className="h-4 w-4" />
              <span className="text-sm font-medium">
                Ada perubahan yang belum disimpan
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Settings Tabs */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-1/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Array.from({ length: 4 }).map((_, j) => (
                    <div key={j} className="space-y-2">
                      <Skeleton className="h-4 w-1/3" />
                      <Skeleton className="h-10 w-full" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : categories.length > 0 ? (
        <Tabs defaultValue={categories[0]} className="space-y-6">
          <TabsList className="grid w-full grid-cols-7">
            {categories.map((category) => {
              const Icon = categoryIcons[category as keyof typeof categoryIcons] || Settings
              return (
                <TabsTrigger key={category} value={category} className="flex items-center gap-2">
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">
                    {categoryLabels[category as keyof typeof categoryLabels] || category}
                  </span>
                </TabsTrigger>
              )
            })}
          </TabsList>

          {categories.map((category) => {
            const categorySettings = settingsByCategory[category] || []
            const Icon = categoryIcons[category as keyof typeof categoryIcons] || Settings

            return (
              <TabsContent key={category} value={category}>
                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Icon className="h-5 w-5" />
                      <CardTitle>
                        {categoryLabels[category as keyof typeof categoryLabels] || category}
                      </CardTitle>
                    </div>
                    <CardDescription>
                      Pengaturan {categoryLabels[category as keyof typeof categoryLabels]?.toLowerCase() || category} untuk store Anda
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {categorySettings.map((setting, index) => (
                      <div key={setting.id}>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Label htmlFor={setting.setting_key} className="text-sm font-medium">
                              {setting.setting_name}
                            </Label>
                            {setting.is_required && (
                              <Badge variant="destructive" className="text-xs">
                                Wajib
                              </Badge>
                            )}
                            {setting.is_public && (
                              <Badge variant="secondary" className="text-xs">
                                Publik
                              </Badge>
                            )}
                          </div>
                          {setting.description && (
                            <p className="text-sm text-muted-foreground">
                              {setting.description}
                            </p>
                          )}
                          {renderSettingInput(setting)}
                        </div>
                        {index < categorySettings.length - 1 && (
                          <Separator className="mt-6" />
                        )}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </TabsContent>
            )
          })}
        </Tabs>
      ) : (
        <Card>
          <CardContent className="text-center py-8">
            <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Tidak ada pengaturan ditemukan</h3>
            <p className="text-muted-foreground">
              Belum ada pengaturan yang dikonfigurasi untuk store ini.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}