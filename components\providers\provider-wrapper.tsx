"use client"

import type React from "react"
import { NotificationsProvider } from "./notifications-provider"
// Import provider lain yang digunakan di aplikasi
// import { ThemeProvider } from '../theme-provider'
// import { AuthProvider } from '../../contexts/auth-context'
import { ThemeProvider } from "../theme-provider"

export const ProviderWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <NotificationsProvider>
        {/* Provider lain bisa ditambahkan di sini jika diperlukan */}
        {children}
      </NotificationsProvider>
    </ThemeProvider>
  )
}
