"use client"

import { useState } from "react"
import Link from "next/link"
import {
  Globe,
  Check,
  X,
  AlertTriangle,
  Facebook,
  Twitter,
  Instagram,
  Github,
  ChromeIcon as Google,
  Apple,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"

// Sample connected accounts data
const connectedAccounts = [
  {
    id: "google",
    name: "Google",
    icon: <Google className="h-5 w-5" />,
    connected: true,
    email: "<EMAIL>",
    connectedDate: "2023-01-15T10:30:00",
  },
  {
    id: "facebook",
    name: "Facebook",
    icon: <Facebook className="h-5 w-5" />,
    connected: true,
    email: "<EMAIL>",
    connectedDate: "2023-02-20T14:20:00",
  },
  {
    id: "apple",
    name: "Apple",
    icon: <Apple className="h-5 w-5" />,
    connected: false,
    email: null,
    connectedDate: null,
  },
  {
    id: "twitter",
    name: "Twitter",
    icon: <Twitter className="h-5 w-5" />,
    connected: false,
    email: null,
    connectedDate: null,
  },
  {
    id: "instagram",
    name: "Instagram",
    icon: <Instagram className="h-5 w-5" />,
    connected: false,
    email: null,
    connectedDate: null,
  },
  {
    id: "github",
    name: "GitHub",
    icon: <Github className="h-5 w-5" />,
    connected: false,
    email: null,
    connectedDate: null,
  },
]

export default function ConnectedAccountsPage() {
  const [accounts, setAccounts] = useState(connectedAccounts)
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false)
  const [accountToDisconnect, setAccountToDisconnect] = useState<string | null>(null)

  const handleDisconnect = (id: string) => {
    setAccountToDisconnect(id)
    setShowDisconnectDialog(true)
  }

  const confirmDisconnect = () => {
    if (accountToDisconnect) {
      setAccounts(
        accounts.map((account) =>
          account.id === accountToDisconnect
            ? { ...account, connected: false, email: null, connectedDate: null }
            : account,
        ),
      )
      setShowDisconnectDialog(false)
      setAccountToDisconnect(null)
    }
  }

  const handleConnect = (id: string) => {
    // In a real app, this would trigger the OAuth flow
    // For demo purposes, we'll just update the state
    setAccounts(
      accounts.map((account) =>
        account.id === id
          ? {
              ...account,
              connected: true,
              email: `john.doe@${id}.com`,
              connectedDate: new Date().toISOString(),
            }
          : account,
      ),
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Account Settings</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/buyer/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="connected" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6 lg:w-auto">
          <TabsTrigger value="profile" asChild>
            <Link href="/buyer/dashboard/account/profile">Profile</Link>
          </TabsTrigger>
          <TabsTrigger value="addresses" asChild>
            <Link href="/buyer/dashboard/account/addresses">Addresses</Link>
          </TabsTrigger>
          <TabsTrigger value="payment" asChild>
            <Link href="/buyer/dashboard/account/payment">Payment Methods</Link>
          </TabsTrigger>
          <TabsTrigger value="communication" asChild>
            <Link href="/buyer/dashboard/account/communication">Communication</Link>
          </TabsTrigger>
          <TabsTrigger value="security" asChild>
            <Link href="/buyer/dashboard/account/security">Security</Link>
          </TabsTrigger>
          <TabsTrigger value="connected">Connected Accounts</TabsTrigger>
        </TabsList>

        <TabsContent value="connected" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold">Connected Accounts</h2>
            <p className="text-sm text-muted-foreground">
              Connect your accounts for easier login and access to more features.
            </p>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Social Accounts</CardTitle>
              </div>
              <CardDescription>Connect your social media accounts for easier login</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {accounts.map((account) => (
                <div key={account.id} className="flex items-center justify-between rounded-md border p-4">
                  <div className="flex items-center gap-3">
                    <div
                      className={`flex h-10 w-10 items-center justify-center rounded-full ${
                        account.connected ? "bg-primary/10 text-primary" : "bg-muted text-muted-foreground"
                      }`}
                    >
                      {account.icon}
                    </div>
                    <div>
                      <div className="font-medium">{account.name}</div>
                      {account.connected ? (
                        <div className="text-sm text-muted-foreground">{account.email}</div>
                      ) : (
                        <div className="text-sm text-muted-foreground">Not connected</div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {account.connected ? (
                      <>
                        <Badge variant="outline" className="bg-green-50 text-green-600">
                          <Check className="mr-1 h-3 w-3" />
                          Connected
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          className="gap-1 text-red-500 hover:bg-red-50 hover:text-red-600"
                          onClick={() => handleDisconnect(account.id)}
                        >
                          <X className="h-4 w-4" />
                          Disconnect
                        </Button>
                      </>
                    ) : (
                      <Button variant="outline" size="sm" className="gap-1" onClick={() => handleConnect(account.id)}>
                        <Check className="h-4 w-4" />
                        Connect
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
            <CardFooter className="flex flex-col items-start">
              <div className="rounded-md bg-muted p-4 text-sm">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="mt-0.5 h-5 w-5 text-yellow-500" />
                  <div>
                    <h3 className="font-medium">Account Linking Information</h3>
                    <p className="text-muted-foreground">
                      Connecting accounts allows for easier login and enhanced features, but does not give the platform
                      permission to post on your behalf.
                    </p>
                  </div>
                </div>
              </div>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Account Sharing</CardTitle>
              </div>
              <CardDescription>Control how your account information is shared</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="rounded-md border p-4">
                <h3 className="font-medium">Social Login Privacy</h3>
                <p className="mt-1 text-sm text-muted-foreground">
                  When you use social login, we receive basic profile information from the provider. You can control
                  what information is shared.
                </p>
                <Separator className="my-4" />
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Share my email address</h4>
                      <p className="text-sm text-muted-foreground">Allow connected accounts to access your email</p>
                    </div>
                    <Button variant="outline" size="sm">
                      Manage
                    </Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Share my profile picture</h4>
                      <p className="text-sm text-muted-foreground">
                        Allow connected accounts to access your profile image
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      Manage
                    </Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Share my activity</h4>
                      <p className="text-sm text-muted-foreground">
                        Allow connected accounts to access your activity data
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      Manage
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Disconnect Account Dialog */}
      <Dialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Disconnect Account</DialogTitle>
            <DialogDescription>
              Are you sure you want to disconnect this account? You will no longer be able to use it for login.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4 flex gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => setShowDisconnectDialog(false)}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDisconnect}>
              <X className="mr-2 h-4 w-4" />
              Disconnect
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
