import axios from "axios"
import Cookies from "js-cookie"

// Buat instance axios dengan konfigurasi dasar
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001",
  headers: {
    "Content-Type": "application/json",
  },
})

// Interceptor untuk menambahkan token ke setiap request
apiClient.interceptors.request.use(
  (config) => {
    const token = Cookies.get("token") || localStorage.getItem("token")
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// Interceptor untuk handling error response
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // Handle token expired atau unauthorized
    if (error.response && error.response.status === 401) {
      Cookies.remove("token")
      localStorage.removeItem("token")
      window.location.href = "/auth/login"
    }
    return Promise.reject(error)
  },
)

export { apiClient }
