"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Edit, Store, Trash2 } from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { productsAPI, type Product } from "@/lib/api/products"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface ProductDetailProps {
  id: string
}

export function ProductDetail({ id }: ProductDetailProps) {
  const router = useRouter()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const data = await productsAPI.getById(id)
        setProduct(data)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching product:", err)
        setError("Gagal memuat data produk. Silakan coba lagi.")
        setLoading(false)
      }
    }

    fetchProduct()
  }, [id])

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await productsAPI.delete(id)
      router.push(product?.storeId ? `/dashboard/stores/${product.storeId}` : "/dashboard/products")
    } catch (err) {
      console.error("Error deleting product:", err)
      setError("Gagal menghapus produk. Silakan coba lagi.")
      setIsDeleting(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <Skeleton className="h-80 w-full" />
          <div className="space-y-6">
            <Skeleton className="h-10 w-1/2" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!product) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold mb-2">Produk tidak ditemukan</h3>
        <p className="text-muted-foreground mb-4">Produk yang Anda cari tidak ditemukan atau telah dihapus.</p>
        <Button asChild>
          <Link href="/dashboard/products">Kembali ke Daftar Produk</Link>
        </Button>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">{product.name}</h1>
        <div className="flex space-x-2">
          <Button asChild>
            <Link href={`/dashboard/products/${id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Produk
            </Link>
          </Button>
          <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Hapus Produk
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Hapus Produk</AlertDialogTitle>
                <AlertDialogDescription>
                  Apakah Anda yakin ingin menghapus produk "{product.name}"? Tindakan ini tidak dapat dibatalkan.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Batal</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isDeleting ? "Menghapus..." : "Hapus"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="bg-muted rounded-lg flex items-center justify-center h-80">
          {product.images && product.images.length > 0 ? (
            <img
              src={product.images[0] || "/placeholder.svg"}
              alt={product.name}
              className="w-full h-full object-contain rounded-lg"
            />
          ) : (
            <div className="text-6xl font-bold text-muted-foreground">{product.name.charAt(0)}</div>
          )}
        </div>

        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold">{formatCurrency(product.price)}</h2>
          </div>

          <div>
            <h3 className="font-medium mb-2">Deskripsi</h3>
            <p className="text-muted-foreground">{product.description || "Tidak ada deskripsi"}</p>
          </div>

          {product.store && (
            <div>
              <h3 className="font-medium mb-2">Toko</h3>
              <Link
                href={`/dashboard/stores/${product.storeId}`}
                className="flex items-center text-primary hover:underline"
              >
                <Store className="mr-2 h-4 w-4" />
                {product.store.name}
              </Link>
            </div>
          )}

          <Button className="w-full" asChild>
            <Link href={product.store ? `/store/${product.store.slug}/products/${product.id}` : "#"}>
              Lihat di Toko
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
