
C:\Users\<USER>\Docume…llzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Media Player" - using hardcoded products: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Docume…llzio-facet.tsx:254 ✅ FACET: Added subcategory: Media Player with actual count: 0
C:\Users\<USER>\Docume…llzio-facet.tsx:255 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:256 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Audio & Speaker" - using hardcoded products: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Docume…llzio-facet.tsx:254 ✅ FACET: Added subcategory: Perangkat Audio & Speaker with actual count: 0
C:\Users\<USER>\Docume…llzio-facet.tsx:255 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:256 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Elektronik Lainnya" - using hardcoded products: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Docume…llzio-facet.tsx:254 ✅ FACET: Added subcategory: Elektronik Lainnya with actual count: 0
C:\Users\<USER>\Docume…llzio-facet.tsx:255 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:256 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Docume…llzio-facet.tsx:279 ✅ FACET: Added main category: Elektronik with count: 33
C:\Users\<USER>\Docume…llzio-facet.tsx:282 ✅ FACET: Final categories object: 
{Konsol Game: 7, Aksesoris Konsol: 10, Alat Casing: 0, Foot Bath & Spa: 0, Mesin Jahit & Aksesoris: 4, …}
C:\Users\<USER>\Docume…ellzio-facet.tsx:87 Set facet data: 
{categories: {…}, priceRanges: {…}, ratings: {…}, shipping: {…}, features: {…}}
200:1 
 GET http://localhost:3001/api/placeholder/200/200 404 (Not Found)
﻿

