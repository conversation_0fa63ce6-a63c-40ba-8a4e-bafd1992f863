Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\contexts\auth-context.tsx:97 [Auth] Checking auth, token: not found
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:78 No prediction history found in localStorage
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\categories.tsx:794 Subkategori dipilih: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\categories.tsx:809 Categories - Dispatching event with data: {query: 'Konsol Game', category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27), products: Array(5)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:279 Received subcategory search event: Konsol Game Elektronik Konsol Game (27) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:280 All subcategories received: (27) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:281 Products received: (5) [{…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:293 Stored subcategory context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:306 Using provided products: (5) [{…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1393 🔥 DEBUG: getAllAllProducts called
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1416 🔥 DEBUG: getAllAllProducts returning: 10 products
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1417 🔥 DEBUG: First few products: (3) ['PlayStation 5', 'Xbox Series X', 'Nintendo Switch OLED']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1393 🔥 DEBUG: getAllAllProducts called
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1416 🔥 DEBUG: getAllAllProducts returning: 10 products
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1417 🔥 DEBUG: First few products: (3) ['PlayStation 5', 'Xbox Series X', 'Nintendo Switch OLED']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1393 🔥 DEBUG: getAllAllProducts called
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1416 🔥 DEBUG: getAllAllProducts returning: 10 products
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1417 🔥 DEBUG: First few products: (3) ['PlayStation 5', 'Xbox Series X', 'Nintendo Switch OLED']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1393 🔥 DEBUG: getAllAllProducts called
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1416 🔥 DEBUG: getAllAllProducts returning: 10 products
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:1417 🔥 DEBUG: First few products: (3) ['PlayStation 5', 'Xbox Series X', 'Nintendo Switch OLED']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:84 Facet data useEffect triggered - searchResults: 5 subcategoryContext: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:130 🔥 FACET DEBUG: extractFacets called
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:131 🔥 FACET DEBUG: results length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:132 🔥 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:133 🔥 FACET DEBUG: context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:148 🔥 FACET DEBUG: Using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:173 🔥 FACET: Creating categories from subcategories: (27) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:174 🔥 FACET: Selected subcategory: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:175 🔥 FACET: Category: Elektronik
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:176 🔥 FACET: All subcategories count: 27
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:177 🔥 FACET: Has subcategory selected: false
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Konsol Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Konsol Game with actual count: 7
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Konsol Game matched products: (7) ['PlayStation 5 (Konsol Game)', 'Xbox Series X (Konsol Game)', 'Nintendo Switch OLED (Konsol Game)', 'Steam Deck (Konsol Game)', 'PlayStation 4 Pro (Konsol Game)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Aksesoris Konsol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Aksesoris Konsol with actual count: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Aksesoris Konsol matched products: (5) ['DualSense Controller (Aksesoris Konsol)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)', 'Gaming Headset (Aksesoris Konsol)', 'Charging Station (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Alat Casing" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Alat Casing with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Foot Bath & Spa" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Foot Bath & Spa with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Jahit & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Jahit & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Setrika & Mesin Uap" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Setrika & Mesin Uap with actual count: 1
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Purifier & Humidifier" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Purifier & Humidifier with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Debu & Peralatan Perawatan Lantai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Debu & Peralatan Perawatan Lantai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Telepon" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Telepon with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Cuci & Pengering" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Cuci & Pengering with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Water Heater" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Water Heater with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pendingin Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pendingin Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pengering Sepatu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pengering Sepatu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Penghangat Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Penghangat Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "TV & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: TV & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Dapur" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Dapur with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Lampu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Lampu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kamera Keamanan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kamera Keamanan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Video Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Video Game with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kelastrian" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kelastrian with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Baterai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Baterai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Rokok Elektronik & Shisha" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Rokok Elektronik & Shisha with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Remote Kontrol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Remote Kontrol with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Walkie Talkie" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Walkie Talkie with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Media Player" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Media Player with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Audio & Speaker" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Audio & Speaker with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Elektronik Lainnya" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Elektronik Lainnya with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:295 ✅ FACET: Added main category: Elektronik with count: 13
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:298 ✅ FACET: Final categories object: {Konsol Game: 7, Aksesoris Konsol: 5, Alat Casing: 0, Foot Bath & Spa: 0, Mesin Jahit & Aksesoris: 0, …}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:87 Set facet data: {categories: {…}, priceRanges: {…}, ratings: {…}, shipping: {…}, features: {…}}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:96 Facet useEffect - context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:101 Auto-checking subcategory: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:102 Auto-checking category: Elektronik
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:84 Facet data useEffect triggered - searchResults: 5 subcategoryContext: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:130 🔥 FACET DEBUG: extractFacets called
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:131 🔥 FACET DEBUG: results length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:132 🔥 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:133 🔥 FACET DEBUG: context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:148 🔥 FACET DEBUG: Using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:173 🔥 FACET: Creating categories from subcategories: (27) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:174 🔥 FACET: Selected subcategory: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:175 🔥 FACET: Category: Elektronik
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:176 🔥 FACET: All subcategories count: 27
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:177 🔥 FACET: Has subcategory selected: false
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Konsol Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Konsol Game with actual count: 7
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Konsol Game matched products: (7) ['PlayStation 5 (Konsol Game)', 'Xbox Series X (Konsol Game)', 'Nintendo Switch OLED (Konsol Game)', 'Steam Deck (Konsol Game)', 'PlayStation 4 Pro (Konsol Game)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Aksesoris Konsol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Aksesoris Konsol with actual count: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Aksesoris Konsol matched products: (5) ['DualSense Controller (Aksesoris Konsol)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)', 'Gaming Headset (Aksesoris Konsol)', 'Charging Station (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Alat Casing" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Alat Casing with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Foot Bath & Spa" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Foot Bath & Spa with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Jahit & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Jahit & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Setrika & Mesin Uap" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Setrika & Mesin Uap with actual count: 1
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Purifier & Humidifier" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Purifier & Humidifier with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Debu & Peralatan Perawatan Lantai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Debu & Peralatan Perawatan Lantai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Telepon" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Telepon with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Cuci & Pengering" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Cuci & Pengering with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Water Heater" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Water Heater with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pendingin Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pendingin Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pengering Sepatu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pengering Sepatu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Penghangat Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Penghangat Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "TV & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: TV & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Dapur" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Dapur with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Lampu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Lampu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kamera Keamanan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kamera Keamanan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Video Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Video Game with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kelastrian" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kelastrian with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Baterai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Baterai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Rokok Elektronik & Shisha" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Rokok Elektronik & Shisha with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Remote Kontrol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Remote Kontrol with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Walkie Talkie" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Walkie Talkie with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Media Player" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Media Player with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Audio & Speaker" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Audio & Speaker with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Elektronik Lainnya" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Elektronik Lainnya with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:295 ✅ FACET: Added main category: Elektronik with count: 13
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:298 ✅ FACET: Final categories object: {Konsol Game: 7, Aksesoris Konsol: 5, Alat Casing: 0, Foot Bath & Spa: 0, Mesin Jahit & Aksesoris: 0, …}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:87 Set facet data: {categories: {…}, priceRanges: {…}, ratings: {…}, shipping: {…}, features: {…}}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:96 Facet useEffect - context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:101 Auto-checking subcategory: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:102 Auto-checking category: Elektronik
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:84 Facet data useEffect triggered - searchResults: 5 subcategoryContext: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:130 🔥 FACET DEBUG: extractFacets called
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:131 🔥 FACET DEBUG: results length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:132 🔥 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:133 🔥 FACET DEBUG: context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:148 🔥 FACET DEBUG: Using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:173 🔥 FACET: Creating categories from subcategories: (27) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:174 🔥 FACET: Selected subcategory: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:175 🔥 FACET: Category: Elektronik
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:176 🔥 FACET: All subcategories count: 27
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:177 🔥 FACET: Has subcategory selected: false
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Konsol Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Konsol Game with actual count: 7
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Konsol Game matched products: (7) ['PlayStation 5 (Konsol Game)', 'Xbox Series X (Konsol Game)', 'Nintendo Switch OLED (Konsol Game)', 'Steam Deck (Konsol Game)', 'PlayStation 4 Pro (Konsol Game)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Aksesoris Konsol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Aksesoris Konsol with actual count: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Aksesoris Konsol matched products: (5) ['DualSense Controller (Aksesoris Konsol)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)', 'Gaming Headset (Aksesoris Konsol)', 'Charging Station (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Alat Casing" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Alat Casing with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Foot Bath & Spa" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Foot Bath & Spa with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Jahit & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Jahit & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Setrika & Mesin Uap" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Setrika & Mesin Uap with actual count: 1
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Purifier & Humidifier" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Purifier & Humidifier with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Debu & Peralatan Perawatan Lantai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Debu & Peralatan Perawatan Lantai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Telepon" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Telepon with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Cuci & Pengering" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Cuci & Pengering with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Water Heater" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Water Heater with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pendingin Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pendingin Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pengering Sepatu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pengering Sepatu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Penghangat Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Penghangat Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "TV & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: TV & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Dapur" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Dapur with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Lampu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Lampu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kamera Keamanan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kamera Keamanan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Video Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Video Game with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kelastrian" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kelastrian with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Baterai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Baterai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Rokok Elektronik & Shisha" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Rokok Elektronik & Shisha with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Remote Kontrol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Remote Kontrol with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Walkie Talkie" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Walkie Talkie with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Media Player" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Media Player with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Audio & Speaker" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Audio & Speaker with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Elektronik Lainnya" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Elektronik Lainnya with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:295 ✅ FACET: Added main category: Elektronik with count: 13
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:298 ✅ FACET: Final categories object: {Konsol Game: 7, Aksesoris Konsol: 5, Alat Casing: 0, Foot Bath & Spa: 0, Mesin Jahit & Aksesoris: 0, …}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:87 Set facet data: {categories: {…}, priceRanges: {…}, ratings: {…}, shipping: {…}, features: {…}}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:96 Facet useEffect - context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:101 Auto-checking subcategory: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:102 Auto-checking category: Elektronik
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:84 Facet data useEffect triggered - searchResults: 5 subcategoryContext: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:130 🔥 FACET DEBUG: extractFacets called
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:131 🔥 FACET DEBUG: results length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:132 🔥 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:133 🔥 FACET DEBUG: context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:148 🔥 FACET DEBUG: Using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:173 🔥 FACET: Creating categories from subcategories: (27) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:174 🔥 FACET: Selected subcategory: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:175 🔥 FACET: Category: Elektronik
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:176 🔥 FACET: All subcategories count: 27
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:177 🔥 FACET: Has subcategory selected: false
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Konsol Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Konsol Game with actual count: 7
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Konsol Game matched products: (7) ['PlayStation 5 (Konsol Game)', 'Xbox Series X (Konsol Game)', 'Nintendo Switch OLED (Konsol Game)', 'Steam Deck (Konsol Game)', 'PlayStation 4 Pro (Konsol Game)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Aksesoris Konsol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Aksesoris Konsol with actual count: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Aksesoris Konsol matched products: (5) ['DualSense Controller (Aksesoris Konsol)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)', 'Gaming Headset (Aksesoris Konsol)', 'Charging Station (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Alat Casing" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Alat Casing with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Foot Bath & Spa" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Foot Bath & Spa with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Jahit & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Jahit & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Setrika & Mesin Uap" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Setrika & Mesin Uap with actual count: 1
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Purifier & Humidifier" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Purifier & Humidifier with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Debu & Peralatan Perawatan Lantai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Debu & Peralatan Perawatan Lantai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Telepon" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Telepon with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Cuci & Pengering" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Cuci & Pengering with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Water Heater" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Water Heater with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pendingin Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pendingin Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pengering Sepatu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pengering Sepatu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Penghangat Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Penghangat Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "TV & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: TV & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Dapur" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Dapur with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Lampu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Lampu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kamera Keamanan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kamera Keamanan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Video Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Video Game with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kelastrian" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kelastrian with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Baterai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Baterai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Rokok Elektronik & Shisha" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Rokok Elektronik & Shisha with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Remote Kontrol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Remote Kontrol with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Walkie Talkie" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Walkie Talkie with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Media Player" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Media Player with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Audio & Speaker" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Audio & Speaker with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Elektronik Lainnya" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Elektronik Lainnya with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:295 ✅ FACET: Added main category: Elektronik with count: 13
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:298 ✅ FACET: Final categories object: {Konsol Game: 7, Aksesoris Konsol: 5, Alat Casing: 0, Foot Bath & Spa: 0, Mesin Jahit & Aksesoris: 0, …}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:87 Set facet data: {categories: {…}, priceRanges: {…}, ratings: {…}, shipping: {…}, features: {…}}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:96 Facet useEffect - context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:101 Auto-checking subcategory: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:102 Auto-checking category: Elektronik
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:84 Facet data useEffect triggered - searchResults: 5 subcategoryContext: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:130 🔥 FACET DEBUG: extractFacets called
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:131 🔥 FACET DEBUG: results length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:132 🔥 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:133 🔥 FACET DEBUG: context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:148 🔥 FACET DEBUG: Using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:173 🔥 FACET: Creating categories from subcategories: (27) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:174 🔥 FACET: Selected subcategory: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:175 🔥 FACET: Category: Elektronik
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:176 🔥 FACET: All subcategories count: 27
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:177 🔥 FACET: Has subcategory selected: true
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Konsol Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Konsol Game with actual count: 7
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Konsol Game matched products: (7) ['PlayStation 5 (Konsol Game)', 'Xbox Series X (Konsol Game)', 'Nintendo Switch OLED (Konsol Game)', 'Steam Deck (Konsol Game)', 'PlayStation 4 Pro (Konsol Game)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Aksesoris Konsol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Aksesoris Konsol with actual count: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Aksesoris Konsol matched products: (5) ['DualSense Controller (Aksesoris Konsol)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)', 'Gaming Headset (Aksesoris Konsol)', 'Charging Station (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Alat Casing" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Alat Casing with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Foot Bath & Spa" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Foot Bath & Spa with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Jahit & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Jahit & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Setrika & Mesin Uap" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Setrika & Mesin Uap with actual count: 1
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Purifier & Humidifier" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Purifier & Humidifier with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Debu & Peralatan Perawatan Lantai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Debu & Peralatan Perawatan Lantai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Telepon" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Telepon with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Cuci & Pengering" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Cuci & Pengering with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Water Heater" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Water Heater with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pendingin Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pendingin Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pengering Sepatu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pengering Sepatu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Penghangat Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Penghangat Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "TV & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: TV & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Dapur" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Dapur with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Lampu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Lampu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kamera Keamanan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kamera Keamanan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Video Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Video Game with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kelastrian" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kelastrian with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Baterai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Baterai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Rokok Elektronik & Shisha" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Rokok Elektronik & Shisha with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Remote Kontrol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Remote Kontrol with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Walkie Talkie" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Walkie Talkie with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Media Player" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Media Player with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Audio & Speaker" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Audio & Speaker with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Elektronik Lainnya" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Elektronik Lainnya with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:295 ✅ FACET: Added main category: Elektronik with count: 13
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:298 ✅ FACET: Final categories object: {Konsol Game: 7, Aksesoris Konsol: 5, Alat Casing: 0, Foot Bath & Spa: 0, Mesin Jahit & Aksesoris: 0, …}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:87 Set facet data: {categories: {…}, priceRanges: {…}, ratings: {…}, shipping: {…}, features: {…}}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:84 Facet data useEffect triggered - searchResults: 5 subcategoryContext: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:130 🔥 FACET DEBUG: extractFacets called
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:131 🔥 FACET DEBUG: results length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:132 🔥 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:133 🔥 FACET DEBUG: context: {category: 'Elektronik', selectedSubcategory: 'Konsol Game', allSubcategories: Array(27)}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:148 🔥 FACET DEBUG: Using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:173 🔥 FACET: Creating categories from subcategories: (27) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:174 🔥 FACET: Selected subcategory: Konsol Game
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:175 🔥 FACET: Category: Elektronik
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:176 🔥 FACET: All subcategories count: 27
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:177 🔥 FACET: Has subcategory selected: true
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Konsol Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Konsol Game with actual count: 7
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Konsol Game matched products: (7) ['PlayStation 5 (Konsol Game)', 'Xbox Series X (Konsol Game)', 'Nintendo Switch OLED (Konsol Game)', 'Steam Deck (Konsol Game)', 'PlayStation 4 Pro (Konsol Game)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Aksesoris Konsol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Aksesoris Konsol with actual count: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:289 🎮 FACET: Aksesoris Konsol matched products: (5) ['DualSense Controller (Aksesoris Konsol)', 'Xbox Wireless Controller (Aksesoris Konsol)', 'Pro Controller Nintendo (Aksesoris Konsol)', 'Gaming Headset (Aksesoris Konsol)', 'Charging Station (Aksesoris Konsol)']
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Alat Casing" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Alat Casing with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Foot Bath & Spa" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Foot Bath & Spa with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Jahit & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Jahit & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Setrika & Mesin Uap" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Setrika & Mesin Uap with actual count: 1
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Purifier & Humidifier" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Purifier & Humidifier with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Debu & Peralatan Perawatan Lantai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Debu & Peralatan Perawatan Lantai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Telepon" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Telepon with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Mesin Cuci & Pengering" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Mesin Cuci & Pengering with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Water Heater" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Water Heater with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pendingin Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pendingin Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Pengering Sepatu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Pengering Sepatu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Penghangat Ruangan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Penghangat Ruangan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "TV & Aksesoris" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: TV & Aksesoris with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Dapur" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Dapur with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Lampu" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Lampu with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kamera Keamanan" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kamera Keamanan with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Video Game" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Video Game with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Kelastrian" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Kelastrian with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Baterai" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Baterai with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Rokok Elektronik & Shisha" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Rokok Elektronik & Shisha with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Remote Kontrol" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Remote Kontrol with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Walkie Talkie" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Walkie Talkie with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Media Player" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Media Player with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Perangkat Audio & Speaker" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Perangkat Audio & Speaker with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:187 🔍 FACET DEBUG: Subcategory "Elektronik Lainnya" - using hardcoded products: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:188 🔍 FACET DEBUG: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:189 🔍 FACET DEBUG: searchResults length: 5
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:257 ✅ FACET: Added subcategory: Elektronik Lainnya with actual count: 0
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:258 🔍 FACET: Products used for counting: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:259 🔍 FACET: allProducts length: 10
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:295 ✅ FACET: Added main category: Elektronik with count: 13
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:298 ✅ FACET: Final categories object: {Konsol Game: 7, Aksesoris Konsol: 5, Alat Casing: 0, Foot Bath & Spa: 0, Mesin Jahit & Aksesoris: 0, …}
C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\components\themes\sellzio\sellzio-facet.tsx:87 Set facet data: {categories: {…}, priceRanges: {…}, ratings: {…}, shipping: {…}, features: {…}}
200:1 
            
            
           GET http://localhost:3001/api/placeholder/200/200 404 (Not Found)
Image
commitMount @ react-dom-client.development.js:19400
runWithFiberInDEV @ react-dom-client.development.js:1511
commitHostMount @ react-dom-client.development.js:10890
commitLayoutEffectOnFiber @ react-dom-client.development.js:11324
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11323
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11323
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11323
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11323
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11323
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11379
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11348
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11323
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11323
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11212
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11207
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11389
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:12195
commitLayoutEffectOnFiber @ react-dom-client.development.js:11289
flushLayoutEffects @ react-dom-client.development.js:15547
commitRoot @ react-dom-client.development.js:15390
commitRootWhenReady @ react-dom-client.development.js:14644
performWorkOnRoot @ react-dom-client.development.js:14567
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<img>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
eval @ C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:2049
eval @ C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:2047
SellzioPage @ C:\Users\<USER>\Documents\Projects\vo-sellzio\v0-windsuf\app\sellzio\page.tsx:2085
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this error