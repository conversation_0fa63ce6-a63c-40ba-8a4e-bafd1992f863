export interface TenantTheme {
  id: string
  tenantId: string
  name: string
  isActive: boolean
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    foreground: string
    muted: string
    mutedForeground: string
    border: string
    input: string
    card: string
    cardForeground: string
    destructive: string
    destructiveForeground: string
  }
  fonts: {
    heading: string
    body: string
  }
  logo: {
    light: string
    dark: string
    favicon: string
  }
  layout: {
    header: "default" | "centered" | "minimal"
    footer: "default" | "minimal" | "none"
    sidebar: "default" | "compact" | "none"
  }
  customCSS?: string
  createdAt: string
  updatedAt: string
}

export interface CreateTenantThemeDTO {
  tenantId: string
  name: string
  colors: Partial<TenantTheme["colors"]>
  fonts?: Partial<TenantTheme["fonts"]>
  logo?: Partial<TenantTheme["logo"]>
  layout?: Partial<TenantTheme["layout"]>
  customCSS?: string
}

export interface UpdateTenantThemeDTO {
  name?: string
  isActive?: boolean
  colors?: Partial<TenantTheme["colors"]>
  fonts?: Partial<TenantTheme["fonts"]>
  logo?: Partial<TenantTheme["logo"]>
  layout?: Partial<TenantTheme["layout"]>
  customCSS?: string
}
