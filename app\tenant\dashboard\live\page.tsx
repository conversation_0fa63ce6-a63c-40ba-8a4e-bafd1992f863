"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  CalendarDays,
  Video,
  BarChart3,
  Clock,
  Users,
  Play,
  Calendar,
  Rocket,
  PlusCircle,
  Clock3,
  Eye,
  ThumbsUp,
  MessagesSquare,
  ArrowUpRight,
  PlayCircle,
  Clock10,
  Film
} from "lucide-react"

// Data dummy untuk live streaming dashboard
const liveStats = {
  upcomingStreams: 4,
  completedStreams: 12,
  totalViewers: 2450,
  averageWatchTime: "12:45",
  currentSubscribers: 580,
  totalStreamHours: 24.5,
  averageConcurrentViewers: 145,
  activeStreamNow: false
}

// Data dummy untuk siaran akan datang
const upcomingStreams = [
  {
    id: "stream-001",
    title: "Peluncuran Produk Terbaru 2024",
    scheduledFor: "2024-06-02T14:00:00",
    status: "scheduled",
    estimatedDuration: 60,
    participants: 3,
    thumbnailUrl: "/images/thumbnails/product-launch.jpg",
    description: "Siaran langsung peluncuran produk unggulan terbaru tahun 2024"
  },
  {
    id: "stream-002",
    title: "Workshop: Tips Pemasaran Digital",
    scheduledFor: "2024-06-05T10:30:00",
    status: "scheduled",
    estimatedDuration: 90,
    participants: 2,
    thumbnailUrl: "/images/thumbnails/marketing-workshop.jpg",
    description: "Workshop interaktif tentang strategi pemasaran digital terkini"
  },
  {
    id: "stream-003",
    title: "Tanya Jawab dengan Founder",
    scheduledFor: "2024-06-08T16:00:00",
    status: "scheduled",
    estimatedDuration: 45,
    participants: 1,
    thumbnailUrl: "/images/thumbnails/qa-session.jpg",
    description: "Sesi tanya jawab langsung dengan pendiri perusahaan"
  },
  {
    id: "stream-004",
    title: "Demo Fitur Aplikasi Baru",
    scheduledFor: "2024-06-10T11:00:00",
    status: "scheduled",
    estimatedDuration: 30,
    participants: 2,
    thumbnailUrl: "/images/thumbnails/app-demo.jpg",
    description: "Demonstrasi fitur-fitur terbaru aplikasi kami"
  }
]

// Data dummy untuk rekaman terakhir
const recentRecordings = [
  {
    id: "rec-001",
    title: "Webinar Strategi SEO 2024",
    recordedOn: "2024-05-15T15:00:00",
    duration: "01:12:35",
    views: 342,
    likes: 78,
    comments: 25,
    thumbnailUrl: "/images/thumbnails/seo-webinar.jpg"
  },
  {
    id: "rec-002",
    title: "Tutorial Penggunaan Platform Baru",
    recordedOn: "2024-05-10T11:30:00",
    duration: "00:45:22",
    views: 215,
    likes: 45,
    comments: 12,
    thumbnailUrl: "/images/thumbnails/platform-tutorial.jpg"
  },
  {
    id: "rec-003",
    title: "Diskusi Panel: Tren E-commerce 2024",
    recordedOn: "2024-05-05T14:00:00",
    duration: "01:35:48",
    views: 521,
    likes: 103,
    comments: 37,
    thumbnailUrl: "/images/thumbnails/ecommerce-panel.jpg"
  }
]

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Format waktu
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format tanggal dan waktu
function formatDateTime(dateString: string) {
  return `${formatDate(dateString)}, ${formatTime(dateString)}`
}

// Format durasi ke format jam:menit
function formatDuration(minutes: number) {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours > 0 ? hours + ' jam ' : ''}${mins > 0 ? mins + ' menit' : ''}`
}

export default function LiveDashboardPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Live Streaming</h1>
        <p className="text-muted-foreground">
          Kelola dan jadwalkan sesi siaran langsung untuk pengikut Anda
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Siaran Mendatang</CardTitle>
            <CalendarDays className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{liveStats.upcomingStreams}</div>
            <p className="text-xs text-muted-foreground">
              Siaran terjadwal dalam 30 hari ke depan
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Penonton</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{liveStats.totalViewers.toLocaleString('id-ID')}</div>
            <p className="text-xs text-muted-foreground">
              Dari {liveStats.completedStreams} siaran selesai
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rata-rata Penonton</CardTitle>
            <Eye className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{liveStats.averageConcurrentViewers}</div>
            <p className="text-xs text-muted-foreground">
              Rata-rata penonton bersamaan
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Waktu Tonton</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{liveStats.averageWatchTime}</div>
            <p className="text-xs text-muted-foreground">
              Rata-rata waktu tonton per pengguna
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Menu Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-md transition-shadow">
          <Link href="/tenant/dashboard/live/schedule">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <Calendar className="h-12 w-12 text-blue-600 mb-4" />
              <h3 className="text-xl font-bold mb-2">Jadwal</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Jadwalkan dan kelola sesi live streaming mendatang
              </p>
              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                {liveStats.upcomingStreams} siaran terjadwal
              </Badge>
            </CardContent>
          </Link>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <Link href="/tenant/dashboard/live/studio">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <Video className="h-12 w-12 text-red-600 mb-4" />
              <h3 className="text-xl font-bold mb-2">Studio</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Mulai siaran langsung dan atur pengaturan streaming
              </p>
              <Badge variant="outline" className={liveStats.activeStreamNow ? "bg-red-100 text-red-800" : "bg-gray-100"}>
                {liveStats.activeStreamNow ? "Siaran aktif" : "Tidak ada siaran"}
              </Badge>
            </CardContent>
          </Link>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <Link href="/tenant/dashboard/live/recordings">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <Film className="h-12 w-12 text-purple-600 mb-4" />
              <h3 className="text-xl font-bold mb-2">Rekaman</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Akses dan kelola rekaman siaran langsung sebelumnya
              </p>
              <Badge variant="outline" className="bg-purple-50 text-purple-700">
                {liveStats.completedStreams} rekaman tersedia
              </Badge>
            </CardContent>
          </Link>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <Link href="/tenant/dashboard/live/analytics">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <BarChart3 className="h-12 w-12 text-green-600 mb-4" />
              <h3 className="text-xl font-bold mb-2">Analitik</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Lihat statistik dan performa siaran langsung Anda
              </p>
              <Badge variant="outline" className="bg-green-50 text-green-700">
                {liveStats.totalStreamHours} jam total siaran
              </Badge>
            </CardContent>
          </Link>
        </Card>
      </div>

      {/* Upcoming Streams */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Siaran Mendatang</h2>
          <Button asChild>
            <Link href="/tenant/dashboard/live/schedule">
              <PlusCircle className="h-4 w-4 mr-2" />
              Jadwalkan Siaran
            </Link>
          </Button>
        </div>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {upcomingStreams.slice(0, 3).map(stream => (
            <Card key={stream.id} className="overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative aspect-video bg-muted">
                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                  <PlayCircle className="h-12 w-12 text-white opacity-80" />
                </div>
                <div className="absolute bottom-2 right-2">
                  <Badge className="bg-blue-600">
                    <Clock10 className="h-3 w-3 mr-1" />
                    {formatDuration(stream.estimatedDuration)}
                  </Badge>
                </div>
              </div>
              <CardContent className="p-4">
                <h3 className="font-bold line-clamp-2 mb-2">{stream.title}</h3>
                <div className="flex items-center text-sm text-muted-foreground mb-2">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>{formatDateTime(stream.scheduledFor)}</span>
                </div>
                <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                  {stream.description}
                </p>
                <div className="flex justify-between">
                  <Badge variant="outline" className="bg-blue-50 text-blue-700">
                    {stream.participants} peserta
                  </Badge>
                  <Button size="sm" variant="outline" asChild>
                    <Link href={`/tenant/dashboard/live/schedule/${stream.id}`}>
                      Lihat Detail
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {upcomingStreams.length > 3 && (
            <Card className="flex items-center justify-center hover:shadow-md transition-shadow">
              <CardContent className="p-6 text-center">
                <ArrowUpRight className="h-10 w-10 text-muted-foreground mb-3 mx-auto" />
                <h3 className="font-medium mb-2">Lihat Semua Jadwal</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Ada {upcomingStreams.length - 3} siaran lainnya
                </p>
                <Button variant="outline" asChild>
                  <Link href="/tenant/dashboard/live/schedule">
                    Lihat Semua
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Recent Recordings */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Rekaman Terbaru</h2>
          <Button variant="outline" asChild>
            <Link href="/tenant/dashboard/live/recordings">
              Lihat Semua Rekaman
            </Link>
          </Button>
        </div>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {recentRecordings.map(recording => (
            <Card key={recording.id} className="overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative aspect-video bg-muted">
                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                  <Play className="h-12 w-12 text-white opacity-80" />
                </div>
                <div className="absolute bottom-2 right-2">
                  <Badge className="bg-purple-600">
                    {recording.duration}
                  </Badge>
                </div>
              </div>
              <CardContent className="p-4">
                <h3 className="font-bold line-clamp-2 mb-2">{recording.title}</h3>
                <div className="flex items-center text-sm text-muted-foreground mb-2">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>{formatDate(recording.recordedOn)}</span>
                </div>
                
                <div className="flex justify-between text-sm text-muted-foreground mt-3">
                  <div className="flex items-center">
                    <Eye className="h-4 w-4 mr-1" />
                    <span>{recording.views}</span>
                  </div>
                  <div className="flex items-center">
                    <ThumbsUp className="h-4 w-4 mr-1" />
                    <span>{recording.likes}</span>
                  </div>
                  <div className="flex items-center">
                    <MessagesSquare className="h-4 w-4 mr-1" />
                    <span>{recording.comments}</span>
                  </div>
                  <Button size="sm" variant="ghost" asChild>
                    <Link href={`/tenant/dashboard/live/recordings/${recording.id}`}>
                      Detail
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
} 