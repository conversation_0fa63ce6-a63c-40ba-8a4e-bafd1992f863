"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, Bell, Check, Mail, Smartphone, Trash2 } from "lucide-react"

// Dummy notification data
const notifications = [
  {
    id: 1,
    title: "New tenant application submitted",
    description: "A new tenant has applied for an account on the platform.",
    time: "10 minutes ago",
    read: false,
    type: "tenant",
  },
  {
    id: 2,
    title: "Store verification request",
    description: "A store has requested verification for their account.",
    time: "1 hour ago",
    read: false,
    type: "store",
  },
  {
    id: 3,
    title: "System update completed",
    description: "The scheduled system update has been completed successfully.",
    time: "3 hours ago",
    read: true,
    type: "system",
  },
  {
    id: 4,
    title: "New admin user added",
    description: "A new admin user has been added to the platform.",
    time: "Yesterday",
    read: true,
    type: "security",
  },
  {
    id: 5,
    title: "Monthly report generated",
    description: "The monthly sales report for all tenants has been generated.",
    time: "2 days ago",
    read: true,
    type: "report",
  },
]

export function NotificationSettings() {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("all")
  const [notificationList, setNotificationList] = useState(notifications)
  const [emailEnabled, setEmailEnabled] = useState(true)
  const [pushEnabled, setPushEnabled] = useState(false)
  const [inAppEnabled, setInAppEnabled] = useState(true)

  const unreadCount = notificationList.filter((n) => !n.read).length

  const markAllAsRead = () => {
    setNotificationList(notificationList.map((n) => ({ ...n, read: true })))
    toast({
      title: "All notifications marked as read",
      description: `${unreadCount} notifications have been marked as read.`,
    })
  }

  const deleteNotification = (id: number) => {
    setNotificationList(notificationList.filter((n) => n.id !== id))
    toast({
      title: "Notification deleted",
      description: "The notification has been removed from your list.",
    })
  }

  const markAsRead = (id: number) => {
    setNotificationList(notificationList.map((n) => (n.id === id ? { ...n, read: true } : n)))
  }

  const saveChannelSettings = () => {
    toast({
      title: "Notification settings saved",
      description: "Your notification preferences have been updated.",
    })
  }

  const filteredNotifications =
    activeTab === "all"
      ? notificationList
      : activeTab === "unread"
        ? notificationList.filter((n) => !n.read)
        : notificationList.filter((n) => n.type === activeTab)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Notifications</h2>
            <p className="text-muted-foreground">Manage your notification preferences and view recent alerts</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {unreadCount > 0 && (
            <Button variant="outline" onClick={markAllAsRead}>
              <Check className="mr-2 h-4 w-4" />
              Mark all as read
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">
            All
            <Badge variant="secondary" className="ml-2">
              {notificationList.length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="unread">
            Unread
            <Badge variant="secondary" className="ml-2">
              {unreadCount}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="tenant">Tenant</TabsTrigger>
          <TabsTrigger value="store">Store</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {(activeTab === "all" ||
          activeTab === "unread" ||
          activeTab === "tenant" ||
          activeTab === "store" ||
          activeTab === "system" ||
          activeTab === "security") && (
          <TabsContent value={activeTab} className="space-y-4">
            {filteredNotifications.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-10">
                  <Bell className="h-10 w-10 text-muted-foreground" />
                  <p className="mt-4 text-lg font-medium">No notifications</p>
                  <p className="text-sm text-muted-foreground">
                    {activeTab === "unread"
                      ? "You have no unread notifications."
                      : "You don't have any notifications yet."}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>
                    {activeTab === "all"
                      ? "All Notifications"
                      : activeTab === "unread"
                        ? "Unread Notifications"
                        : `${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Notifications`}
                  </CardTitle>
                  <CardDescription>
                    {activeTab === "unread"
                      ? `You have ${unreadCount} unread notification${unreadCount !== 1 ? "s" : ""}.`
                      : "Recent notifications and alerts from the platform."}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {filteredNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`flex items-start space-x-4 rounded-lg border p-4 ${
                        !notification.read ? "bg-accent/10" : ""
                      }`}
                    >
                      <Avatar className="h-9 w-9">
                        <AvatarFallback
                          className={`${
                            notification.type === "tenant"
                              ? "bg-blue-100 text-blue-600"
                              : notification.type === "store"
                                ? "bg-green-100 text-green-600"
                                : notification.type === "system"
                                  ? "bg-purple-100 text-purple-600"
                                  : notification.type === "security"
                                    ? "bg-red-100 text-red-600"
                                    : "bg-orange-100 text-orange-600"
                          }`}
                        >
                          {notification.type.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <p className={`text-sm font-medium ${!notification.read ? "font-semibold" : ""}`}>
                            {notification.title}
                          </p>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-muted-foreground">{notification.time}</span>
                            {!notification.read && (
                              <Badge variant="secondary" className="h-1.5 w-1.5 rounded-full bg-primary p-0" />
                            )}
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">{notification.description}</p>
                        <div className="flex items-center space-x-2 pt-1">
                          {!notification.read && (
                            <Button variant="ghost" size="sm" onClick={() => markAsRead(notification.id)}>
                              Mark as read
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => deleteNotification(notification.id)}
                          >
                            <Trash2 className="mr-1 h-3.5 w-3.5" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
                <CardFooter className="flex justify-center border-t p-4">
                  <Button variant="outline">Load More</Button>
                </CardFooter>
              </Card>
            )}
          </TabsContent>
        )}

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Channels</CardTitle>
              <CardDescription>Choose how you want to receive notifications from the platform</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="rounded-full bg-primary/10 p-2">
                    <Mail className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Email Notifications</p>
                    <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                  </div>
                </div>
                <Switch checked={emailEnabled} onCheckedChange={setEmailEnabled} />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="rounded-full bg-primary/10 p-2">
                    <Smartphone className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Push Notifications</p>
                    <p className="text-sm text-muted-foreground">Receive push notifications on your devices</p>
                  </div>
                </div>
                <Switch checked={pushEnabled} onCheckedChange={setPushEnabled} />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="rounded-full bg-primary/10 p-2">
                    <Bell className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">In-App Notifications</p>
                    <p className="text-sm text-muted-foreground">Receive notifications within the dashboard</p>
                  </div>
                </div>
                <Switch checked={inAppEnabled} onCheckedChange={setInAppEnabled} />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveChannelSettings} className="ml-auto">
                Save Channel Settings
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>Select which types of notifications you want to receive</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="mb-4 text-sm font-medium">Tenant Notifications</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="tenant-applications" defaultChecked />
                    <Label htmlFor="tenant-applications">New tenant applications</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="tenant-approvals" defaultChecked />
                    <Label htmlFor="tenant-approvals">Tenant approval status changes</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="tenant-subscriptions" defaultChecked />
                    <Label htmlFor="tenant-subscriptions">Subscription plan changes</Label>
                  </div>
                </div>
              </div>
              <Separator />
              <div>
                <h3 className="mb-4 text-sm font-medium">Store Notifications</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="store-registrations" defaultChecked />
                    <Label htmlFor="store-registrations">New store registrations</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="store-verifications" defaultChecked />
                    <Label htmlFor="store-verifications">Store verification requests</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="store-reports" />
                    <Label htmlFor="store-reports">Store reported by users</Label>
                  </div>
                </div>
              </div>
              <Separator />
              <div>
                <h3 className="mb-4 text-sm font-medium">System Notifications</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="system-updates" defaultChecked />
                    <Label htmlFor="system-updates">System updates and maintenance</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="system-errors" defaultChecked />
                    <Label htmlFor="system-errors">Critical system errors</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="system-performance" />
                    <Label htmlFor="system-performance">Performance alerts</Label>
                  </div>
                </div>
              </div>
              <Separator />
              <div>
                <h3 className="mb-4 text-sm font-medium">Security Notifications</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="security-logins" defaultChecked />
                    <Label htmlFor="security-logins">Unusual login attempts</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="security-changes" defaultChecked />
                    <Label htmlFor="security-changes">Account security changes</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="security-permissions" defaultChecked />
                    <Label htmlFor="security-permissions">Permission changes</Label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="ml-auto">Save Preferences</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
