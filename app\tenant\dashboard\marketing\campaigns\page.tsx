"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Plus,
  BarChart2,
  Megaphone,
  Users,
  Eye,
  PencilIcon,
  Trash,
  MoreHorizontal,
  Calendar,
  CheckCircle2,
  XCircle,
  PauseCircle,
  PlayCircle
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk campaign
const campaigns = [
  {
    id: "cmp-001",
    name: "Promo Tahun Baru 2024",
    type: "Email",
    status: "active",
    startDate: "2024-01-01",
    endDate: "2024-01-10",
    impressions: 12000,
    clicks: 1800,
    conversions: 320,
    budget: 5000000,
    spent: 4200000
  },
  {
    id: "cmp-002",
    name: "Flash Sale Akhir Pekan",
    type: "Flash Sale",
    status: "paused",
    startDate: "2024-01-12",
    endDate: "2024-01-14",
    impressions: 8000,
    clicks: 1200,
    conversions: 210,
    budget: 3000000,
    spent: 1500000
  },
  {
    id: "cmp-003",
    name: "<PERSON><PERSON><PERSON> Spesial Fashion",
    type: "Promotion",
    status: "draft",
    startDate: "2024-02-01",
    endDate: "2024-02-10",
    impressions: 0,
    clicks: 0,
    conversions: 0,
    budget: 2000000,
    spent: 0
  },
  {
    id: "cmp-004",
    name: "Campaign Media Sosial",
    type: "Social Media",
    status: "ended",
    startDate: "2023-12-01",
    endDate: "2023-12-10",
    impressions: 15000,
    clicks: 2500,
    conversions: 400,
    budget: 6000000,
    spent: 6000000
  }
]

// Fungsi untuk badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "active":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>
    case "paused":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Paused</Badge>
    case "draft":
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>
    case "ended":
      return <Badge variant="outline" className="bg-red-100 text-red-800">Ended</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

export default function MarketingCampaignsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Filter campaigns
  const filteredCampaigns = campaigns.filter(cmp => {
    const matchesSearch = cmp.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || cmp.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Statistik
  const stats = {
    total: campaigns.length,
    active: campaigns.filter(c => c.status === "active").length,
    paused: campaigns.filter(c => c.status === "paused").length,
    ended: campaigns.filter(c => c.status === "ended").length,
    totalBudget: campaigns.reduce((sum, c) => sum + c.budget, 0),
    totalSpent: campaigns.reduce((sum, c) => sum + c.spent, 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/marketing">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Marketing Campaigns</h1>
            <p className="text-muted-foreground">
              Kelola campaign marketing, performa, dan anggaran
            </p>
          </div>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Buat Campaign
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
            <Megaphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Active: {stats.active}, Paused: {stats.paused}, Ended: {stats.ended}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
            <BarChart2 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatCurrency(stats.totalBudget)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <BarChart2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalSpent)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.active}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari nama campaign..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="active">Active</option>
              <option value="paused">Paused</option>
              <option value="draft">Draft</option>
              <option value="ended">Ended</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Campaigns List */}
      <div className="space-y-4">
        {filteredCampaigns.map((cmp) => (
          <Card key={cmp.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex flex-col gap-2">
                <div className="flex flex-col md:flex-row justify-between gap-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-semibold text-lg">{cmp.name}</span>
                      {getStatusBadge(cmp.status)}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{cmp.type}</span>
                      <Calendar className="h-3.5 w-3.5" />
                      <span>{cmp.startDate} - {cmp.endDate}</span>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <div className="flex items-center gap-1">
                      <BarChart2 className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">{formatCurrency(cmp.budget)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <BarChart2 className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">{formatCurrency(cmp.spent)}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-4 mt-2 text-xs">
                  <span>Impressions: <b>{cmp.impressions}</b></span>
                  <span>Clicks: <b>{cmp.clicks}</b></span>
                  <span>Conversions: <b>{cmp.conversions}</b></span>
                </div>
                <div className="flex gap-2 pt-2 border-t mt-2">
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button size="sm" variant="outline">
                    <PencilIcon className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  {cmp.status === "active" ? (
                    <Button size="sm" variant="outline" className="text-yellow-600 hover:text-yellow-700">
                      <PauseCircle className="h-4 w-4 mr-2" />
                      Pause
                    </Button>
                  ) : cmp.status === "paused" ? (
                    <Button size="sm" variant="outline" className="text-green-600 hover:text-green-700">
                      <PlayCircle className="h-4 w-4 mr-2" />
                      Resume
                    </Button>
                  ) : null}
                  <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                    <Trash className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                  <Button size="sm" variant="ghost">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        {filteredCampaigns.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <Megaphone className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Tidak ada campaign ditemukan</h3>
              <p className="text-muted-foreground mb-4">
                Tidak ada campaign yang cocok dengan filter Anda
              </p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Buat Campaign
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
} 