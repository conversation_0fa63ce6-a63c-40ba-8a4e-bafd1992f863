"use client"

import { useState } from "react"
import { DataTable } from "@/components/ui/data-table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Eye, Mail, UserX, ShoppingCart, BarChart3, Activity } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"

// Mock data for end users
const endUsers = [
  {
    id: "eu1",
    name: "<PERSON>",
    email: "<EMAIL>",
    tenant: "Tech Solutions",
    orders: 12,
    totalSpent: 850,
    registrationDate: "2023-03-10",
    status: "Active",
    activity: {
      lastLogin: "2023-05-07",
      lastPurchase: "2023-05-01",
      visitFrequency: "High",
      cartAbandonment: "Low",
    },
    orderHistory: [
      { id: "o1", date: "2023-05-01", amount: 120, status: "Completed" },
      { id: "o2", date: "2023-04-15", amount: 85, status: "Completed" },
      { id: "o3", date: "2023-04-02", amount: 210, status: "Completed" },
    ],
    behavioralData: {
      preferredCategories: ["Electronics", "Books"],
      averageOrderValue: 70.83,
      purchaseFrequency: "Bi-weekly",
      reviewsSubmitted: 5,
    },
  },
  {
    id: "eu2",
    name: "James Anderson",
    email: "<EMAIL>",
    tenant: "Tech Solutions",
    orders: 8,
    totalSpent: 620,
    registrationDate: "2023-04-05",
    status: "Active",
    activity: {
      lastLogin: "2023-05-02",
      lastPurchase: "2023-04-28",
      visitFrequency: "Medium",
      cartAbandonment: "Medium",
    },
    orderHistory: [
      { id: "o4", date: "2023-04-28", amount: 95, status: "Completed" },
      { id: "o5", date: "2023-04-10", amount: 150, status: "Completed" },
      { id: "o6", date: "2023-03-22", amount: 75, status: "Completed" },
    ],
    behavioralData: {
      preferredCategories: ["Sports", "Men's Fashion"],
      averageOrderValue: 77.5,
      purchaseFrequency: "Monthly",
      reviewsSubmitted: 3,
    },
  },
  {
    id: "eu3",
    name: "Lisa Taylor",
    email: "<EMAIL>",
    tenant: "SaaS Corp",
    orders: 3,
    totalSpent: 210,
    registrationDate: "2023-04-01",
    status: "Inactive",
    activity: {
      lastLogin: "2023-05-03",
      lastPurchase: "2023-04-20",
      visitFrequency: "Low",
      cartAbandonment: "High",
    },
    orderHistory: [
      { id: "o7", date: "2023-04-20", amount: 65, status: "Completed" },
      { id: "o8", date: "2023-04-05", amount: 85, status: "Completed" },
      { id: "o9", date: "2023-03-15", amount: 60, status: "Completed" },
    ],
    behavioralData: {
      preferredCategories: ["Beauty", "Home Decor"],
      averageOrderValue: 70.0,
      purchaseFrequency: "Monthly",
      reviewsSubmitted: 1,
    },
  },
  {
    id: "eu4",
    name: "Michael Wilson",
    email: "<EMAIL>",
    tenant: "Fashion Hub",
    orders: 0,
    totalSpent: 0,
    registrationDate: "2023-03-15",
    status: "Suspended",
    activity: {
      lastLogin: "2023-05-06",
      lastPurchase: "Never",
      visitFrequency: "Low",
      cartAbandonment: "High",
    },
    orderHistory: [],
    behavioralData: {
      preferredCategories: [],
      averageOrderValue: 0,
      purchaseFrequency: "Never",
      reviewsSubmitted: 0,
    },
  },
  {
    id: "eu5",
    name: "Sarah Brown",
    email: "<EMAIL>",
    tenant: "SaaS Corp",
    orders: 15,
    totalSpent: 1250,
    registrationDate: "2023-03-20",
    status: "Active",
    activity: {
      lastLogin: "2023-05-05",
      lastPurchase: "2023-05-03",
      visitFrequency: "High",
      cartAbandonment: "Low",
    },
    orderHistory: [
      { id: "o10", date: "2023-05-03", amount: 120, status: "Completed" },
      { id: "o11", date: "2023-04-25", amount: 95, status: "Completed" },
      { id: "o12", date: "2023-04-12", amount: 180, status: "Completed" },
    ],
    behavioralData: {
      preferredCategories: ["Women's Fashion", "Jewelry"],
      averageOrderValue: 83.33,
      purchaseFrequency: "Weekly",
      reviewsSubmitted: 8,
    },
  },
]

export default function EndUsers() {
  const [searchTerm, setSearchTerm] = useState("")
  const [tenantFilter, setTenantFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedUser, setSelectedUser] = useState<(typeof endUsers)[0] | null>(null)
  const [activeTab, setActiveTab] = useState("overview")

  // Get unique values for filters
  const tenants = [...new Set(endUsers.map((user) => user.tenant))]
  const statuses = [...new Set(endUsers.map((user) => user.status))]

  // Filter end users based on search term and filters
  const filteredUsers = endUsers.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesTenant = tenantFilter === "all" || user.tenant === tenantFilter
    const matchesStatus = statusFilter === "all" || user.status === statusFilter

    return matchesSearch && matchesTenant && matchesStatus
  })

  const columns = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "tenant",
      header: "Tenant",
    },
    {
      accessorKey: "orders",
      header: "Orders",
    },
    {
      accessorKey: "totalSpent",
      header: "Total Spent",
      cell: ({ row }) => {
        const amount = Number.parseFloat(row.getValue("totalSpent"))
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount)
        return formatted
      },
    },
    {
      accessorKey: "registrationDate",
      header: "Registration Date",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <Badge variant={status === "Active" ? "success" : status === "Inactive" ? "secondary" : "destructive"}>
            {status}
          </Badge>
        )
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const user = row.original as (typeof endUsers)[0]
        return (
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" title="View User" onClick={() => setSelectedUser(user)}>
              <Eye className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Contact User">
              <Mail className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="View Orders">
              <ShoppingCart className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Suspend User">
              <UserX className="h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]

  return (
    <div>
      {selectedUser ? (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle>{selectedUser.name}</CardTitle>
                <CardDescription>{selectedUser.email}</CardDescription>
              </div>
              <Button variant="outline" onClick={() => setSelectedUser(null)}>
                Back to List
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="orders">Order History</TabsTrigger>
                <TabsTrigger value="activity">User Activity</TabsTrigger>
                <TabsTrigger value="behavior">Behavioral Analytics</TabsTrigger>
              </TabsList>

              <TabsContent value="overview">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">User Details</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Tenant:</div>
                        <div className="col-span-2">{selectedUser.tenant}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Registration:</div>
                        <div className="col-span-2">{selectedUser.registrationDate}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Status:</div>
                        <div className="col-span-2">
                          <Badge
                            variant={
                              selectedUser.status === "Active"
                                ? "success"
                                : selectedUser.status === "Inactive"
                                  ? "secondary"
                                  : "destructive"
                            }
                          >
                            {selectedUser.status}
                          </Badge>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Last Login:</div>
                        <div className="col-span-2">{selectedUser.activity.lastLogin}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Last Purchase:</div>
                        <div className="col-span-2">{selectedUser.activity.lastPurchase}</div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium mb-4">Purchase Summary</h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="border rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold">{selectedUser.orders}</div>
                          <div className="text-sm text-muted-foreground">Total Orders</div>
                        </div>
                        <div className="border rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold">${selectedUser.totalSpent}</div>
                          <div className="text-sm text-muted-foreground">Total Spent</div>
                        </div>
                        <div className="border rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold">
                            $
                            {selectedUser.orders > 0
                              ? (selectedUser.totalSpent / selectedUser.orders).toFixed(2)
                              : "0.00"}
                          </div>
                          <div className="text-sm text-muted-foreground">Average Order</div>
                        </div>
                        <div className="border rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold">{selectedUser.behavioralData.reviewsSubmitted}</div>
                          <div className="text-sm text-muted-foreground">Reviews</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-6 flex justify-end space-x-2">
                  <Button variant="outline">Contact User</Button>
                  <Button variant="outline">View Orders</Button>
                  <Button variant="destructive">Suspend User</Button>
                </div>
              </TabsContent>

              <TabsContent value="orders">
                <Card>
                  <CardHeader>
                    <CardTitle>Order History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedUser.orderHistory.length > 0 ? (
                      <div className="space-y-4">
                        {selectedUser.orderHistory.map((order) => (
                          <div key={order.id} className="border rounded-lg p-4">
                            <div className="flex justify-between items-center">
                              <div>
                                <h4 className="font-medium">Order #{order.id}</h4>
                                <p className="text-sm text-muted-foreground">
                                  {order.date} • ${order.amount}
                                </p>
                              </div>
                              <Badge variant={order.status === "Completed" ? "success" : "secondary"}>
                                {order.status}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <ShoppingCart className="h-12 w-12 mx-auto text-muted-foreground" />
                        <p className="mt-2 text-muted-foreground">No orders found</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="activity">
                <Card>
                  <CardHeader>
                    <CardTitle>User Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-medium mb-4">Activity Summary</h3>
                        <div className="space-y-2">
                          <div className="grid grid-cols-3 gap-4">
                            <div className="font-medium">Last Login:</div>
                            <div className="col-span-2">{selectedUser.activity.lastLogin}</div>
                          </div>
                          <div className="grid grid-cols-3 gap-4">
                            <div className="font-medium">Last Purchase:</div>
                            <div className="col-span-2">{selectedUser.activity.lastPurchase}</div>
                          </div>
                          <div className="grid grid-cols-3 gap-4">
                            <div className="font-medium">Visit Frequency:</div>
                            <div className="col-span-2">
                              <Badge
                                variant={
                                  selectedUser.activity.visitFrequency === "High"
                                    ? "success"
                                    : selectedUser.activity.visitFrequency === "Medium"
                                      ? "default"
                                      : "secondary"
                                }
                              >
                                {selectedUser.activity.visitFrequency}
                              </Badge>
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-4">
                            <div className="font-medium">Cart Abandonment:</div>
                            <div className="col-span-2">
                              <Badge
                                variant={
                                  selectedUser.activity.cartAbandonment === "Low"
                                    ? "success"
                                    : selectedUser.activity.cartAbandonment === "Medium"
                                      ? "warning"
                                      : "destructive"
                                }
                              >
                                {selectedUser.activity.cartAbandonment}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-medium mb-4">Activity Metrics</h3>
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">Engagement Score</span>
                              <span className="text-sm font-medium">
                                {selectedUser.activity.visitFrequency === "High"
                                  ? "75%"
                                  : selectedUser.activity.visitFrequency === "Medium"
                                    ? "50%"
                                    : "25%"}
                              </span>
                            </div>
                            <Progress
                              value={
                                selectedUser.activity.visitFrequency === "High"
                                  ? 75
                                  : selectedUser.activity.visitFrequency === "Medium"
                                    ? 50
                                    : 25
                              }
                              className="h-2"
                            />
                          </div>
                          <div>
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">Purchase Conversion</span>
                              <span className="text-sm font-medium">
                                {selectedUser.activity.cartAbandonment === "Low"
                                  ? "80%"
                                  : selectedUser.activity.cartAbandonment === "Medium"
                                    ? "50%"
                                    : "20%"}
                              </span>
                            </div>
                            <Progress
                              value={
                                selectedUser.activity.cartAbandonment === "Low"
                                  ? 80
                                  : selectedUser.activity.cartAbandonment === "Medium"
                                    ? 50
                                    : 20
                              }
                              className="h-2"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-6 flex justify-center">
                      <Card className="w-full">
                        <CardHeader>
                          <CardTitle>Activity Timeline</CardTitle>
                        </CardHeader>
                        <CardContent className="flex items-center justify-center h-40">
                          <Activity className="h-24 w-24 text-muted-foreground" />
                          <div className="text-center ml-4">
                            <p className="text-muted-foreground">Detailed activity timeline available</p>
                            <Button variant="outline" className="mt-2">
                              View Timeline
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="behavior">
                <Card>
                  <CardHeader>
                    <CardTitle>Behavioral Analytics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-medium mb-4">Shopping Preferences</h3>
                        <div className="space-y-2">
                          <div className="grid grid-cols-3 gap-4">
                            <div className="font-medium">Preferred Categories:</div>
                            <div className="col-span-2">
                              {selectedUser.behavioralData.preferredCategories.length > 0 ? (
                                <div className="flex flex-wrap gap-1">
                                  {selectedUser.behavioralData.preferredCategories.map((category, index) => (
                                    <Badge key={index} variant="outline">
                                      {category}
                                    </Badge>
                                  ))}
                                </div>
                              ) : (
                                <span className="text-muted-foreground">None</span>
                              )}
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-4">
                            <div className="font-medium">Average Order Value:</div>
                            <div className="col-span-2">
                              ${selectedUser.behavioralData.averageOrderValue.toFixed(2)}
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-4">
                            <div className="font-medium">Purchase Frequency:</div>
                            <div className="col-span-2">{selectedUser.behavioralData.purchaseFrequency}</div>
                          </div>
                          <div className="grid grid-cols-3 gap-4">
                            <div className="font-medium">Reviews Submitted:</div>
                            <div className="col-span-2">{selectedUser.behavioralData.reviewsSubmitted}</div>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-medium mb-4">Behavioral Insights</h3>
                        <div className="space-y-4">
                          <div className="border rounded-lg p-4">
                            <h4 className="font-medium mb-2">Customer Segment</h4>
                            <Badge
                              variant={
                                selectedUser.orders > 10
                                  ? "success"
                                  : selectedUser.orders > 5
                                    ? "default"
                                    : selectedUser.orders > 0
                                      ? "secondary"
                                      : "outline"
                              }
                            >
                              {selectedUser.orders > 10
                                ? "Loyal Customer"
                                : selectedUser.orders > 5
                                  ? "Regular Customer"
                                  : selectedUser.orders > 0
                                    ? "Occasional Customer"
                                    : "New User"}
                            </Badge>
                          </div>
                          <div className="border rounded-lg p-4">
                            <h4 className="font-medium mb-2">Engagement Level</h4>
                            <Badge
                              variant={
                                selectedUser.activity.visitFrequency === "High"
                                  ? "success"
                                  : selectedUser.activity.visitFrequency === "Medium"
                                    ? "default"
                                    : "secondary"
                              }
                            >
                              {selectedUser.activity.visitFrequency === "High"
                                ? "Highly Engaged"
                                : selectedUser.activity.visitFrequency === "Medium"
                                  ? "Moderately Engaged"
                                  : "Low Engagement"}
                            </Badge>
                          </div>
                          <div className="border rounded-lg p-4">
                            <h4 className="font-medium mb-2">Recommended Actions</h4>
                            <ul className="list-disc list-inside text-sm text-muted-foreground">
                              {selectedUser.orders === 0 && <li>Send welcome discount to encourage first purchase</li>}
                              {selectedUser.activity.cartAbandonment === "High" && (
                                <li>Send cart abandonment reminder emails</li>
                              )}
                              {selectedUser.activity.visitFrequency === "Low" && (
                                <li>Re-engagement campaign with personalized offers</li>
                              )}
                              {selectedUser.behavioralData.preferredCategories.length > 0 && (
                                <li>Targeted promotions for preferred categories</li>
                              )}
                              {selectedUser.orders > 10 && <li>Loyalty program invitation</li>}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-6 flex justify-center">
                      <Card className="w-full">
                        <CardHeader>
                          <CardTitle>Behavioral Analytics</CardTitle>
                        </CardHeader>
                        <CardContent className="flex items-center justify-center h-40">
                          <BarChart3 className="h-24 w-24 text-muted-foreground" />
                          <div className="text-center ml-4">
                            <p className="text-muted-foreground">Detailed behavioral analytics available</p>
                            <Button variant="outline" className="mt-2">
                              View Analytics
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>End Users</CardTitle>
            <CardDescription>Manage regular users (buyers) on the platform</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <Input
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Select value={tenantFilter} onValueChange={setTenantFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by Tenant" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Tenants</SelectItem>
                    {tenants.map((tenant) => (
                      <SelectItem key={tenant} value={tenant}>
                        {tenant}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {statuses.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DataTable
              columns={columns}
              data={filteredUsers}
              searchColumn="name"
              searchPlaceholder="Search end users..."
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
