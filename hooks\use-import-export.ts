import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface ImportExportJob {
  id: string;
  type: 'import' | 'export';
  file_name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  total_rows?: number;
  success_rows?: number;
  failed_rows?: number;
  start_time: string;
  end_time?: string;
  file_size?: string;
  uploaded_by: string;
  download_url?: string;
  error_message?: string;
  progress?: number;
  errors?: Array<{
    row: number;
    message: string;
  }>;
  created_at: string;
  updated_at: string;
}

export interface ImportExportStats {
  total: number;
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  cancelled: number;
  imports: number;
  exports: number;
}

export interface ImportOptions {
  skip_duplicates: boolean;
  update_existing: boolean;
  validate_only: boolean;
}

export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'json';
  filter: 'all' | 'active' | 'inactive' | 'outofstock';
  category_filter?: string;
  date_from?: string;
  date_to?: string;
}

export function useImportExport() {
  const [jobs, setJobs] = useState<ImportExportJob[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch import/export jobs
  const fetchJobs = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/import-export/jobs');

      if (!response.ok) {
        throw new Error('Failed to fetch import/export jobs');
      }

      const data = await response.json();
      setJobs(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Get job statistics
  const getJobStats = useCallback(async (): Promise<ImportExportStats | null> => {
    try {
      const response = await fetch('/api/import-export/stats');

      if (!response.ok) {
        throw new Error('Failed to fetch job stats');
      }

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Start import job
  const startImport = useCallback(async (
    file: File,
    options: ImportOptions
  ): Promise<boolean> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('options', JSON.stringify(options));

      const response = await fetch('/api/import-export/import', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start import');
      }

      const newJob = await response.json();

      // Update local state
      setJobs(prev => [newJob, ...prev]);

      toast.success('Import job started successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Start export job
  const startExport = useCallback(async (options: ExportOptions): Promise<boolean> => {
    try {
      const response = await fetch('/api/import-export/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start export');
      }

      const newJob = await response.json();

      // Update local state
      setJobs(prev => [newJob, ...prev]);

      toast.success('Export job started successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Get single job
  const getJob = useCallback(async (id: string): Promise<ImportExportJob | null> => {
    try {
      const response = await fetch(`/api/import-export/jobs/${id}`);

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch job');
      }

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Cancel job
  const cancelJob = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/import-export/jobs/${id}/cancel`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to cancel job');
      }

      const updatedJob = await response.json();

      // Update local state
      setJobs(prev =>
        prev.map(job => job.id === id ? updatedJob : job)
      );

      toast.success('Job cancelled successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Delete job
  const deleteJob = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/import-export/jobs/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete job');
      }

      // Update local state
      setJobs(prev => prev.filter(job => job.id !== id));

      toast.success('Job deleted successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Download template
  const downloadTemplate = useCallback(async (type: 'csv' | 'xlsx'): Promise<boolean> => {
    try {
      const response = await fetch(`/api/import-export/template?type=${type}`);

      if (!response.ok) {
        throw new Error('Failed to download template');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `product_import_template.${type}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('Template downloaded successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Download export file
  const downloadExport = useCallback(async (jobId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/import-export/jobs/${jobId}/download`);

      if (!response.ok) {
        throw new Error('Failed to download export file');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;

      // Get filename from response headers or use default
      const contentDisposition = response.headers.get('content-disposition');
      const filename = contentDisposition
        ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
        : 'export.xlsx';

      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('Export file downloaded successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Refresh jobs (re-fetch)
  const refreshJobs = useCallback(async () => {
    await fetchJobs();
  }, [fetchJobs]);

  // Initial fetch on mount
  useEffect(() => {
    fetchJobs();
  }, [fetchJobs]);

  return {
    jobs,
    loading,
    error,
    fetchJobs,
    getJobStats,
    startImport,
    startExport,
    getJob,
    cancelJob,
    deleteJob,
    downloadTemplate,
    downloadExport,
    refreshJobs,
  };
}
