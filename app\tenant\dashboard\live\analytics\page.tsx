"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  Calendar,
  Users,
  Clock,
  Eye,
  ArrowUpRight,
  ThumbsUp,
  MessagesSquare,
  Calendar as CalendarIcon,
  Download,
  TrendingUp,
  TrendingDown,
  Activity,
  Timer,
  Heart,
  Share2,
  Filter,
  CalendarDays
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Data dummy untuk metrik overview
const overviewStats = {
  totalStreams: 15,
  totalViewers: 2450,
  averageConcurrentViewers: 145,
  totalWatchTime: 24650, // dalam menit
  averageWatchTime: "12:45",
  totalStreamHours: 24.5,
  totalLikes: 845,
  totalComments: 376,
  totalShares: 192,
  retentionRate: 68.5, // dalam persen
  conversionRate: 5.2, // dalam persen
  mostViewedStream: {
    title: "Webinar Strategi SEO 2024",
    views: 342,
    date: "2024-05-15T15:00:00"
  }
}

// Data dummy untuk siaran terbaru
const recentStreams = [
  {
    id: "stream-001",
    title: "Webinar Strategi SEO 2024",
    streamedOn: "2024-05-15T15:00:00",
    duration: "01:12:35",
    views: 342,
    concurrentViewers: 85,
    likes: 78,
    comments: 25,
    shares: 45,
    retentionRate: 72.4,
    status: "completed"
  },
  {
    id: "stream-002",
    title: "Tutorial Penggunaan Platform Baru",
    streamedOn: "2024-05-10T11:30:00",
    duration: "00:45:22",
    views: 215,
    concurrentViewers: 65,
    likes: 45,
    comments: 12,
    shares: 28,
    retentionRate: 68.9,
    status: "completed"
  },
  {
    id: "stream-003",
    title: "Diskusi Panel: Tren E-commerce 2024",
    streamedOn: "2024-05-05T14:00:00",
    duration: "01:35:48",
    views: 521,
    concurrentViewers: 130,
    likes: 103,
    comments: 37,
    shares: 84,
    retentionRate: 74.2,
    status: "completed"
  },
  {
    id: "stream-004",
    title: "Tips Meningkatkan Penjualan di Marketplace",
    streamedOn: "2024-04-28T13:15:00",
    duration: "00:52:18",
    views: 187,
    concurrentViewers: 52,
    likes: 42,
    comments: 15,
    shares: 19,
    retentionRate: 65.8,
    status: "completed"
  }
]

// Data dummy untuk grafik viewers
const viewersChartData = {
  months: ["Jan", "Feb", "Mar", "Apr", "Mei", "Jun"],
  viewers: [125, 210, 380, 425, 520, 610],
  streams: [2, 3, 4, 3, 2, 1]
}

// Data dummy untuk grafik engagement
const engagementChartData = {
  months: ["Jan", "Feb", "Mar", "Apr", "Mei", "Jun"],
  likes: [45, 78, 120, 135, 190, 220],
  comments: [22, 36, 58, 62, 85, 103],
  shares: [12, 25, 33, 40, 55, 68]
}

// Data dummy untuk distribusi demografis
const demographicsData = {
  age: [
    { label: "18-24", value: 25 },
    { label: "25-34", value: 40 },
    { label: "35-44", value: 20 },
    { label: "45-54", value: 10 },
    { label: "55+", value: 5 }
  ],
  gender: [
    { label: "Pria", value: 55 },
    { label: "Wanita", value: 44 },
    { label: "Lainnya", value: 1 }
  ],
  location: [
    { label: "Jakarta", value: 35 },
    { label: "Surabaya", value: 20 },
    { label: "Bandung", value: 15 },
    { label: "Medan", value: 10 },
    { label: "Lainnya", value: 20 }
  ],
  device: [
    { label: "Mobile", value: 65 },
    { label: "Desktop", value: 30 },
    { label: "Tablet", value: 5 }
  ]
}

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Format waktu
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format tanggal dan waktu
function formatDateTime(dateString: string) {
  return `${formatDate(dateString)}, ${formatTime(dateString)}`
}

// Format angka dengan pemisah ribuan
function formatNumber(number: number) {
  return number.toLocaleString('id-ID')
}

// Format persentase
function formatPercentage(number: number) {
  return `${number.toFixed(1)}%`
}

export default function LiveAnalyticsPage() {
  const [dateFilter, setDateFilter] = useState("last-6-months")
  const [selectedTab, setSelectedTab] = useState("overview")

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/live">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analitik</h1>
            <p className="text-muted-foreground">
              Lihat statistik dan performa siaran langsung Anda
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Select
            value={dateFilter}
            onValueChange={setDateFilter}
          >
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <CalendarDays className="h-4 w-4 mr-2" />
                <span>Rentang Waktu</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last-7-days">7 Hari Terakhir</SelectItem>
              <SelectItem value="last-30-days">30 Hari Terakhir</SelectItem>
              <SelectItem value="last-3-months">3 Bulan Terakhir</SelectItem>
              <SelectItem value="last-6-months">6 Bulan Terakhir</SelectItem>
              <SelectItem value="last-year">1 Tahun Terakhir</SelectItem>
              <SelectItem value="all-time">Sepanjang Waktu</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Ekspor Laporan
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="overview" value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="w-full md:w-auto">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="streams">Siaran</TabsTrigger>
          <TabsTrigger value="audience">Audiens</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab Content */}
        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Siaran</CardTitle>
                <BarChart3 className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{overviewStats.totalStreams}</div>
                <p className="text-xs text-muted-foreground">
                  {overviewStats.totalStreamHours} jam total waktu siaran
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Penonton</CardTitle>
                <Users className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{formatNumber(overviewStats.totalViewers)}</div>
                <p className="text-xs text-muted-foreground">
                  Rata-rata {overviewStats.averageConcurrentViewers} penonton bersamaan
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Waktu Tonton</CardTitle>
                <Clock className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{formatNumber(overviewStats.totalWatchTime)} menit</div>
                <p className="text-xs text-muted-foreground">
                  Rata-rata {overviewStats.averageWatchTime} per pengguna
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Retensi Penonton</CardTitle>
                <TrendingUp className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{formatPercentage(overviewStats.retentionRate)}</div>
                <p className="text-xs text-muted-foreground">
                  {formatPercentage(overviewStats.conversionRate)} konversi ke produk
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Engagement Stats */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Likes</CardTitle>
                <ThumbsUp className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(overviewStats.totalLikes)}</div>
                <p className="text-xs text-muted-foreground">
                  Rata-rata {Math.round(overviewStats.totalLikes / overviewStats.totalStreams)} per siaran
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Komentar</CardTitle>
                <MessagesSquare className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(overviewStats.totalComments)}</div>
                <p className="text-xs text-muted-foreground">
                  Rata-rata {Math.round(overviewStats.totalComments / overviewStats.totalStreams)} per siaran
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Shares</CardTitle>
                <Share2 className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(overviewStats.totalShares)}</div>
                <p className="text-xs text-muted-foreground">
                  Rata-rata {Math.round(overviewStats.totalShares / overviewStats.totalStreams)} per siaran
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Siaran Paling Populer dan Grafik Viewers */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Siaran Paling Populer</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-md p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{overviewStats.mostViewedStream.title}</h3>
                      <Badge className="bg-green-100 text-green-800">Terpopuler</Badge>
                    </div>
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 mr-1" />
                        <span>{formatDate(overviewStats.mostViewedStream.date)}</span>
                      </div>
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 mr-1" />
                        <span>{formatNumber(overviewStats.mostViewedStream.views)} penonton</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {recentStreams.slice(0, 4).map((stream, index) => (
                      <div key={stream.id} className="border rounded-md p-3">
                        <div className="flex flex-col space-y-1">
                          <h4 className="font-medium text-sm truncate">{stream.title}</h4>
                          <div className="flex items-center text-xs text-muted-foreground">
                            <Eye className="h-3 w-3 mr-1" />
                            <span>{formatNumber(stream.views)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Tren Penonton</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[250px] flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <LineChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                    <p>Grafik tren penonton akan ditampilkan di sini</p>
                    <p className="text-xs">Data untuk {viewersChartData.months.length} bulan terakhir</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Streams Tab Content */}
        <TabsContent value="streams" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performa Siaran</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="relative overflow-x-auto">
                  <table className="w-full text-sm text-left">
                    <thead className="text-xs uppercase bg-muted/50">
                      <tr>
                        <th scope="col" className="px-6 py-3">Judul Siaran</th>
                        <th scope="col" className="px-6 py-3">Tanggal</th>
                        <th scope="col" className="px-6 py-3">Durasi</th>
                        <th scope="col" className="px-6 py-3">Penonton</th>
                        <th scope="col" className="px-6 py-3">Penonton Bersamaan</th>
                        <th scope="col" className="px-6 py-3">Retensi</th>
                        <th scope="col" className="px-6 py-3">Engagement</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentStreams.map(stream => (
                        <tr key={stream.id} className="border-b">
                          <td className="px-6 py-4 font-medium">
                            <Link href={`/tenant/dashboard/live/recordings/${stream.id}`} className="hover:underline">
                              {stream.title}
                            </Link>
                          </td>
                          <td className="px-6 py-4">{formatDate(stream.streamedOn)}</td>
                          <td className="px-6 py-4">{stream.duration}</td>
                          <td className="px-6 py-4">{formatNumber(stream.views)}</td>
                          <td className="px-6 py-4">{formatNumber(stream.concurrentViewers)}</td>
                          <td className="px-6 py-4">{formatPercentage(stream.retentionRate)}</td>
                          <td className="px-6 py-4">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center">
                                <ThumbsUp className="h-3 w-3 mr-1" />
                                <span>{stream.likes}</span>
                              </div>
                              <div className="flex items-center">
                                <MessagesSquare className="h-3 w-3 mr-1" />
                                <span>{stream.comments}</span>
                              </div>
                              <div className="flex items-center">
                                <Share2 className="h-3 w-3 mr-1" />
                                <span>{stream.shares}</span>
                              </div>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                <div className="h-[300px] flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <LineChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                    <p>Grafik performa siaran akan ditampilkan di sini</p>
                    <p className="text-xs">Bandingkan metrik performa antar siaran</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Audience Tab Content */}
        <TabsContent value="audience" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Demografi Penonton</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium mb-3">Distribusi Usia</h3>
                    <div className="space-y-2">
                      {demographicsData.age.map(item => (
                        <div key={item.label} className="flex items-center">
                          <span className="w-10 text-xs">{item.label}</span>
                          <div className="flex-1 mx-2">
                            <div className="h-2 rounded-full bg-muted overflow-hidden">
                              <div 
                                className="h-full bg-blue-600 rounded-full" 
                                style={{ width: `${item.value}%` }}
                              />
                            </div>
                          </div>
                          <span className="text-xs">{item.value}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium mb-3">Distribusi Gender</h3>
                    <div className="space-y-2">
                      {demographicsData.gender.map(item => (
                        <div key={item.label} className="flex items-center">
                          <span className="w-16 text-xs">{item.label}</span>
                          <div className="flex-1 mx-2">
                            <div className="h-2 rounded-full bg-muted overflow-hidden">
                              <div 
                                className="h-full bg-purple-600 rounded-full" 
                                style={{ width: `${item.value}%` }}
                              />
                            </div>
                          </div>
                          <span className="text-xs">{item.value}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Lokasi dan Perangkat</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium mb-3">Lokasi Teratas</h3>
                    <div className="space-y-2">
                      {demographicsData.location.map(item => (
                        <div key={item.label} className="flex items-center">
                          <span className="w-20 text-xs">{item.label}</span>
                          <div className="flex-1 mx-2">
                            <div className="h-2 rounded-full bg-muted overflow-hidden">
                              <div 
                                className="h-full bg-green-600 rounded-full" 
                                style={{ width: `${item.value}%` }}
                              />
                            </div>
                          </div>
                          <span className="text-xs">{item.value}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium mb-3">Jenis Perangkat</h3>
                    <div className="space-y-2">
                      {demographicsData.device.map(item => (
                        <div key={item.label} className="flex items-center">
                          <span className="w-20 text-xs">{item.label}</span>
                          <div className="flex-1 mx-2">
                            <div className="h-2 rounded-full bg-muted overflow-hidden">
                              <div 
                                className="h-full bg-orange-600 rounded-full" 
                                style={{ width: `${item.value}%` }}
                              />
                            </div>
                          </div>
                          <span className="text-xs">{item.value}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Perilaku Penonton</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="border rounded-md p-4">
                  <div className="flex items-center mb-2">
                    <Timer className="h-5 w-5 mr-2 text-blue-600" />
                    <h3 className="font-medium">Durasi Rata-rata Tonton</h3>
                  </div>
                  <div className="text-2xl font-bold">{overviewStats.averageWatchTime}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Durasi rata-rata seorang penonton menonton siaran Anda
                  </p>
                </div>
                
                <div className="border rounded-md p-4">
                  <div className="flex items-center mb-2">
                    <Activity className="h-5 w-5 mr-2 text-green-600" />
                    <h3 className="font-medium">Retensi Penonton</h3>
                  </div>
                  <div className="text-2xl font-bold">{formatPercentage(overviewStats.retentionRate)}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Persentase penonton yang tetap menonton hingga akhir
                  </p>
                </div>
                
                <div className="border rounded-md p-4">
                  <div className="flex items-center mb-2">
                    <TrendingUp className="h-5 w-5 mr-2 text-purple-600" />
                    <h3 className="font-medium">Tingkat Konversi</h3>
                  </div>
                  <div className="text-2xl font-bold">{formatPercentage(overviewStats.conversionRate)}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Persentase penonton yang melakukan pembelian setelah menonton
                  </p>
                </div>
              </div>
              
              <div className="h-[300px] flex items-center justify-center mt-6">
                <div className="text-center text-muted-foreground">
                  <LineChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                  <p>Grafik retensi penonton akan ditampilkan di sini</p>
                  <p className="text-xs">Analisis di mana penonton berhenti menonton siaran</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Engagement Tab Content */}
        <TabsContent value="engagement" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Interaksi Penonton</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[250px] flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <BarChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                    <p>Grafik interaksi penonton akan ditampilkan di sini</p>
                    <p className="text-xs">Likes, komentar, dan shares dari waktu ke waktu</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 mt-6">
                  <div className="border rounded-md p-3 text-center">
                    <div className="flex justify-center items-center mb-2">
                      <ThumbsUp className="h-5 w-5 text-red-600" />
                    </div>
                    <div className="text-xl font-bold">{formatNumber(overviewStats.totalLikes)}</div>
                    <p className="text-xs text-muted-foreground">Total Likes</p>
                  </div>
                  
                  <div className="border rounded-md p-3 text-center">
                    <div className="flex justify-center items-center mb-2">
                      <MessagesSquare className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="text-xl font-bold">{formatNumber(overviewStats.totalComments)}</div>
                    <p className="text-xs text-muted-foreground">Total Komentar</p>
                  </div>
                  
                  <div className="border rounded-md p-3 text-center">
                    <div className="flex justify-center items-center mb-2">
                      <Share2 className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="text-xl font-bold">{formatNumber(overviewStats.totalShares)}</div>
                    <p className="text-xs text-muted-foreground">Total Shares</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Top Comments</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="border rounded-md p-3">
                      <div className="flex items-start gap-3">
                        <div className="rounded-full bg-muted w-8 h-8 flex items-center justify-center flex-shrink-0">
                          <Users className="h-4 w-4" />
                        </div>
                        <div className="space-y-1 flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-sm">Penonton {i + 1}</span>
                            <div className="flex items-center">
                              <Heart className="h-3 w-3 mr-1 text-red-500" />
                              <span className="text-xs">{15 - i * 2}</span>
                            </div>
                          </div>
                          <p className="text-sm">
                            {i === 0 && "Siaran ini sangat membantu! Terima kasih banyak."}
                            {i === 1 && "Kapan jadwal siaran berikutnya?"}
                            {i === 2 && "Saya sudah menerapkan tips ini dan hasilnya luar biasa!"}
                            {i === 3 && "Boleh minta presentasi yang digunakan?"}
                            {i === 4 && "Konten seperti ini yang saya cari, informatif dan jelas."}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Dari siaran: {recentStreams[i % 4].title}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Engagement Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <LineChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                  <p>Grafik engagement rate akan ditampilkan di sini</p>
                  <p className="text-xs">Perbandingan engagement rate antar siaran</p>
                </div>
              </div>
              
              <div className="mt-6">
                <h3 className="text-sm font-medium mb-3">Tips Meningkatkan Engagement</h3>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <ArrowUpRight className="h-4 w-4 text-green-600 mt-0.5" />
                    <p className="text-sm">Jadwalkan siaran di waktu peak traffic untuk mendapatkan lebih banyak penonton</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <ArrowUpRight className="h-4 w-4 text-green-600 mt-0.5" />
                    <p className="text-sm">Ajak penonton berinteraksi dengan memberi pertanyaan atau polling</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <ArrowUpRight className="h-4 w-4 text-green-600 mt-0.5" />
                    <p className="text-sm">Promosikan siaran Anda di sosial media sebelum jadwal siaran</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <ArrowUpRight className="h-4 w-4 text-green-600 mt-0.5" />
                    <p className="text-sm">Tawarkan promo khusus yang hanya tersedia selama siaran berlangsung</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 