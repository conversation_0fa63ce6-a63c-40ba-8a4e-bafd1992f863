"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"

export default function SeedPage() {
  const [loading, setLoading] = useState(false)

  const seedStoreSettings = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/store-settings/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (response.ok) {
        toast.success(`Store settings seeded successfully! Created: ${result.created}`)
      } else {
        toast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      toast.error('Failed to seed store settings')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const seedProductCategories = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/product-categories/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (response.ok) {
        toast.success(`Product categories seeded successfully! Root: ${result.rootCategories}, Sub: ${result.subCategories}`)
      } else {
        toast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      toast.error('Failed to seed product categories')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const seedBrands = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/brands/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (response.ok) {
        toast.success(`Brands seeded successfully! Created: ${result.created}`)
      } else {
        toast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      toast.error('Failed to seed brands')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const seedProductModeration = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/product-moderation/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (response.ok) {
        toast.success(`Product moderation seeded successfully! Created: ${result.created}`)
      } else {
        toast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      toast.error('Failed to seed product moderation')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const seedImportExport = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/import-export/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (response.ok) {
        toast.success(`Import/Export jobs seeded successfully! Created: ${result.created}`)
      } else {
        toast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      toast.error('Failed to seed import/export jobs')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Database Seeding</h1>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Store Settings</CardTitle>
            <CardDescription>
              Seed sample store settings data for testing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={seedStoreSettings}
              disabled={loading}
            >
              {loading ? 'Seeding...' : 'Seed Store Settings'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Product Categories</CardTitle>
            <CardDescription>
              Seed sample product categories data for testing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={seedProductCategories}
              disabled={loading}
            >
              {loading ? 'Seeding...' : 'Seed Product Categories'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Brands</CardTitle>
            <CardDescription>
              Seed sample brands data for testing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={seedBrands}
              disabled={loading}
            >
              {loading ? 'Seeding...' : 'Seed Brands'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Product Moderation</CardTitle>
            <CardDescription>
              Seed sample product moderation data for testing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={seedProductModeration}
              disabled={loading}
            >
              {loading ? 'Seeding...' : 'Seed Product Moderation'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Import/Export Jobs</CardTitle>
            <CardDescription>
              Seed sample import/export jobs data for testing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={seedImportExport}
              disabled={loading}
            >
              {loading ? 'Seeding...' : 'Seed Import/Export Jobs'}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
