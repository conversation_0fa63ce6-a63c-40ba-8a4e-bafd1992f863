"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Download,
  FileText,
  Search,
  Filter,
  Calendar,
  ChevronDown,
  File,
  PlusCircle,
  Printer,
  Share2,
  ExternalLink,
  ChevronRight,
  Clock
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { addDays } from "date-fns"

// Data dummy untuk ringkasan laporan keuangan
const statementsSummary = {
  totalRevenue: 45250000,
  totalExpenses: 27530000,
  netProfit: 17720000,
  profitMargin: 39.16,
  lastUpdated: "2024-05-25T10:30:00"
}

// Data dummy untuk laporan keuangan
const statements = [
  {
    id: "STM-001",
    type: "monthly",
    title: "Laporan Bulanan Mei 2024",
    period: "Mei 2024",
    periodStart: "2024-05-01",
    periodEnd: "2024-05-31",
    createdAt: "2024-05-25T10:30:00",
    status: "completed",
    format: "pdf",
    size: "1.2 MB",
    url: "#",
    summary: {
      totalRevenue: 15250000,
      totalExpenses: 9530000,
      netProfit: 5720000,
      profitMargin: 37.51
    }
  },
  {
    id: "STM-002",
    type: "monthly",
    title: "Laporan Bulanan April 2024",
    period: "April 2024",
    periodStart: "2024-04-01",
    periodEnd: "2024-04-30",
    createdAt: "2024-05-01T09:15:00",
    status: "completed",
    format: "pdf",
    size: "1.3 MB",
    url: "#",
    summary: {
      totalRevenue: 14750000,
      totalExpenses: 8950000,
      netProfit: 5800000,
      profitMargin: 39.32
    }
  },
  {
    id: "STM-003",
    type: "monthly",
    title: "Laporan Bulanan Maret 2024",
    period: "Maret 2024",
    periodStart: "2024-03-01",
    periodEnd: "2024-03-31",
    createdAt: "2024-04-01T11:00:00",
    status: "completed",
    format: "pdf",
    size: "1.1 MB",
    url: "#",
    summary: {
      totalRevenue: 13850000,
      totalExpenses: 8650000,
      netProfit: 5200000,
      profitMargin: 37.55
    }
  },
  {
    id: "STM-004",
    type: "quarterly",
    title: "Laporan Kuartal 1 2024",
    period: "Q1 2024",
    periodStart: "2024-01-01",
    periodEnd: "2024-03-31",
    createdAt: "2024-04-10T14:20:00",
    status: "completed",
    format: "pdf",
    size: "2.8 MB",
    url: "#",
    summary: {
      totalRevenue: 38500000,
      totalExpenses: 23200000,
      netProfit: 15300000,
      profitMargin: 39.74
    }
  },
  {
    id: "STM-005",
    type: "custom",
    title: "Laporan Promosi Mei 2024",
    period: "10-20 Mei 2024",
    periodStart: "2024-05-10",
    periodEnd: "2024-05-20",
    createdAt: "2024-05-22T15:45:00",
    status: "completed",
    format: "excel",
    size: "950 KB",
    url: "#",
    summary: {
      totalRevenue: 6800000,
      totalExpenses: 4200000,
      netProfit: 2600000,
      profitMargin: 38.24
    }
  },
  {
    id: "STM-006",
    type: "annual",
    title: "Laporan Tahunan 2023",
    period: "2023",
    periodStart: "2023-01-01",
    periodEnd: "2023-12-31",
    createdAt: "2024-01-15T09:00:00",
    status: "completed",
    format: "pdf",
    size: "4.2 MB",
    url: "#",
    summary: {
      totalRevenue: 156800000,
      totalExpenses: 92500000,
      netProfit: 64300000,
      profitMargin: 41.01
    }
  },
  {
    id: "STM-007",
    type: "custom",
    title: "Laporan Pajak Q1 2024",
    period: "Q1 2024",
    periodStart: "2024-01-01",
    periodEnd: "2024-03-31",
    createdAt: "2024-04-20T10:30:00",
    status: "completed",
    format: "excel",
    size: "1.5 MB",
    url: "#",
    summary: {
      totalRevenue: 38500000,
      totalExpenses: 23200000,
      netProfit: 15300000,
      profitMargin: 39.74,
      taxAmount: 3825000
    }
  }
]

// Data dummy untuk template laporan
const reportTemplates = [
  { id: "TPL-001", name: "Laporan Bulanan", description: "Ringkasan pendapatan dan pengeluaran bulanan" },
  { id: "TPL-002", name: "Laporan Kuartalan", description: "Analisis keuangan per kuartal" },
  { id: "TPL-003", name: "Laporan Tahunan", description: "Laporan keuangan tahunan lengkap" },
  { id: "TPL-004", name: "Laporan Laba Rugi", description: "Detail laba dan rugi dalam periode tertentu" },
  { id: "TPL-005", name: "Laporan Arus Kas", description: "Analisis arus kas masuk dan keluar" },
  { id: "TPL-006", name: "Laporan Pajak", description: "Ringkasan untuk keperluan perpajakan" },
  { id: "TPL-007", name: "Laporan Aset", description: "Daftar dan nilai semua aset" }
]

// Format mata uang
function formatCurrency(number: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(number)
}

// Format persentase
function formatPercentage(number: number) {
  return `${number.toFixed(2)}%`
}

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Format waktu
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format tanggal dan waktu
function formatDateTime(dateString: string) {
  return `${formatDate(dateString)}, ${formatTime(dateString)}`
}

export default function StatementsPage() {
  const [selectedTab, setSelectedTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [startDate, setStartDate] = useState(addDays(new Date(), -90).toISOString().substring(0, 10))
  const [endDate, setEndDate] = useState(new Date().toISOString().substring(0, 10))
  
  // Filter laporan berdasarkan tab, pencarian, dan filter
  const filteredStatements = statements.filter(statement => {
    // Filter berdasarkan tipe
    if (filterType !== "all" && statement.type !== filterType) {
      return false
    }
    
    // Filter berdasarkan tanggal
    const statementDate = new Date(statement.createdAt)
    const start = new Date(startDate)
    const end = new Date(endDate)
    end.setHours(23, 59, 59) // Set to end of day
    
    if (statementDate < start || statementDate > end) {
      return false
    }
    
    // Filter berdasarkan pencarian
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        statement.id.toLowerCase().includes(query) ||
        statement.title.toLowerCase().includes(query) ||
        statement.period.toLowerCase().includes(query)
      )
    }
    
    return true
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/financial">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Laporan Keuangan</h1>
            <p className="text-muted-foreground">
              Akses dan unduh laporan keuangan Anda
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button>
            <FileText className="h-4 w-4 mr-2" />
            Buat Laporan Baru
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(statementsSummary.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Periode berjalan
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pengeluaran</CardTitle>
            <FileText className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(statementsSummary.totalExpenses)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Periode berjalan
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Laba Bersih</CardTitle>
            <FileText className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(statementsSummary.netProfit)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Margin: {formatPercentage(statementsSummary.profitMargin)}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Terakhir Diperbarui</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatDate(statementsSummary.lastUpdated)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {formatTime(statementsSummary.lastUpdated)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs and Filters */}
      <div className="flex flex-col gap-4">
        <Tabs defaultValue="all" value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="w-full md:w-auto">
            <TabsTrigger value="all">Semua Laporan</TabsTrigger>
            <TabsTrigger value="create">Buat Laporan</TabsTrigger>
          </TabsList>
        </Tabs>
        
        {selectedTab === "all" && (
          <div className="flex flex-col md:flex-row gap-4 md:items-center">
            <div className="w-full md:w-[300px]">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Cari laporan..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-8"
                />
              </div>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-[180px]">
                  <div className="flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    <span>Tipe Laporan</span>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Tipe</SelectItem>
                  <SelectItem value="monthly">Bulanan</SelectItem>
                  <SelectItem value="quarterly">Kuartalan</SelectItem>
                  <SelectItem value="annual">Tahunan</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
              
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Dari:</span>
                </div>
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-auto"
                />
                <span className="text-sm text-muted-foreground">Sampai:</span>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-auto"
                />
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Report Content */}
      {selectedTab === "all" ? (
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle>Daftar Laporan</CardTitle>
              <div className="text-sm">
                Menampilkan <span className="font-medium">{filteredStatements.length}</span> laporan
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="relative overflow-x-auto">
              <table className="w-full text-sm text-left">
                <thead className="text-xs uppercase bg-muted/50">
                  <tr>
                    <th scope="col" className="px-6 py-3">ID</th>
                    <th scope="col" className="px-6 py-3">Laporan</th>
                    <th scope="col" className="px-6 py-3">Periode</th>
                    <th scope="col" className="px-6 py-3">Tanggal Dibuat</th>
                    <th scope="col" className="px-6 py-3">Format</th>
                    <th scope="col" className="px-6 py-3">Ukuran</th>
                    <th scope="col" className="px-6 py-3">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredStatements.map(statement => (
                    <tr key={statement.id} className="border-b">
                      <td className="px-6 py-4 font-medium">
                        <Link href={`/tenant/dashboard/financial/statements/${statement.id}`} className="hover:underline">
                          {statement.id}
                        </Link>
                      </td>
                      <td className="px-6 py-4">
                        <div className="font-medium">{statement.title}</div>
                        <div className="text-xs text-muted-foreground">
                          {statement.type === "monthly" && "Laporan Bulanan"}
                          {statement.type === "quarterly" && "Laporan Kuartalan"}
                          {statement.type === "annual" && "Laporan Tahunan"}
                          {statement.type === "custom" && "Laporan Kustom"}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>{statement.period}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatDate(statement.periodStart)} - {formatDate(statement.periodEnd)}
                        </div>
                      </td>
                      <td className="px-6 py-4">{formatDateTime(statement.createdAt)}</td>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          {statement.format === "pdf" ? (
                            <>
                              <FileText className="h-4 w-4 text-red-600 mr-1.5" />
                              <span className="text-xs">PDF</span>
                            </>
                          ) : (
                            <>
                              <FileText className="h-4 w-4 text-green-600 mr-1.5" />
                              <span>Excel</span>
                            </>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">{statement.size}</td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="icon" asChild>
                            <Link href={statement.url} target="_blank">
                              <FileText className="h-4 w-4" />
                              <span className="sr-only">Lihat</span>
                            </Link>
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Download className="h-4 w-4" />
                            <span className="sr-only">Unduh</span>
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <ChevronDown className="h-4 w-4" />
                                <span className="sr-only">Buka menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Printer className="h-4 w-4 mr-2" />
                                Cetak
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Share2 className="h-4 w-4 mr-2" />
                                Bagikan
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {filteredStatements.length === 0 && (
              <div className="p-8 text-center">
                <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Tidak ada laporan yang sesuai dengan filter</p>
                <Button onClick={() => {
                  setSearchQuery("")
                  setFilterType("all")
                  setStartDate(addDays(new Date(), -90).toISOString().substring(0, 10))
                  setEndDate(new Date().toISOString().substring(0, 10))
                }} variant="link" className="mt-2">
                  Hapus semua filter
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Buat Laporan Baru</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Pilih Template Laporan</label>
                  <Select defaultValue="">
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih template laporan..." />
                    </SelectTrigger>
                    <SelectContent>
                      {reportTemplates.map(template => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Tanggal Mulai</label>
                    <Input type="date" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Tanggal Akhir</label>
                    <Input type="date" />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Format Laporan</label>
                  <Select defaultValue="pdf">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="pt-4">
                  <Button>
                    <FileText className="h-4 w-4 mr-2" />
                    Hasilkan Laporan
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Template Laporan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportTemplates.map(template => (
                  <div key={template.id} className="border rounded-md p-4 flex justify-between items-center">
                    <div>
                      <div className="font-medium">{template.name}</div>
                      <div className="text-sm text-muted-foreground">{template.description}</div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <span>Gunakan</span>
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
} 