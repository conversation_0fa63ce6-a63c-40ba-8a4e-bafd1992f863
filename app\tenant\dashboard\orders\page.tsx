"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Package, 
  Eye,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  DollarSign,
  ShoppingCart,
  RefreshCw,
  Download
} from "lucide-react"
import Link from "next/link"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Data dummy untuk pesanan
const ordersData = [
  {
    id: "ORD-001",
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+62812345678"
    },
    store: "Toko Fashion Kita",
    products: [
      { name: "Kemeja Batik Premium", quantity: 2, price: 250000 }
    ],
    total: 500000,
    status: "pending",
    paymentStatus: "paid",
    shippingStatus: "preparing",
    orderDate: "2023-11-20T10:30:00",
    shippingAddress: "Jl. Sudirman No. 123, Jakarta Pusat",
    trackingNumber: null,
    notes: "Mohon dikemas dengan rapi"
  },
  {
    id: "ORD-002",
    customer: {
      name: "Siti Nurhaliza",
      email: "<EMAIL>",
      phone: "+62823456789"
    },
    store: "Elektronik Murah",
    products: [
      { name: "Smartphone Android 128GB", quantity: 1, price: 3500000 }
    ],
    total: 3500000,
    status: "shipped",
    paymentStatus: "paid",
    shippingStatus: "shipped",
    orderDate: "2023-11-18T14:15:00",
    shippingAddress: "Jl. Asia Afrika No. 456, Bandung",
    trackingNumber: "JNE123456789",
    notes: null
  },
  {
    id: "ORD-003",
    customer: {
      name: "Budi Santoso",
      email: "<EMAIL>",
      phone: "+62834567890"
    },
    store: "Makanan Sehat",
    products: [
      { name: "Paket Makanan Sehat Organik", quantity: 3, price: 85000 }
    ],
    total: 255000,
    status: "delivered",
    paymentStatus: "paid",
    shippingStatus: "delivered",
    orderDate: "2023-11-15T09:20:00",
    shippingAddress: "Jl. Pemuda No. 789, Surabaya",
    trackingNumber: "SICEPAT987654321",
    notes: "Pengiriman express"
  },
  {
    id: "ORD-004",
    customer: {
      name: "Maya Sari",
      email: "<EMAIL>",
      phone: "+62845678901"
    },
    store: "Buku & Alat Tulis",
    products: [
      { name: "Novel Bestseller Indonesia", quantity: 5, price: 75000 }
    ],
    total: 375000,
    status: "cancelled",
    paymentStatus: "refunded",
    shippingStatus: "cancelled",
    orderDate: "2023-11-12T16:45:00",
    shippingAddress: "Jl. Malioboro No. 321, Yogyakarta",
    trackingNumber: null,
    notes: "Dibatalkan oleh customer"
  },
  {
    id: "ORD-005",
    customer: {
      name: "Andi Pratama",
      email: "<EMAIL>",
      phone: "+62856789012"
    },
    store: "Olahraga & Fitness",
    products: [
      { name: "Sepatu Lari Professional", quantity: 1, price: 850000 }
    ],
    total: 850000,
    status: "processing",
    paymentStatus: "pending",
    shippingStatus: "waiting_payment",
    orderDate: "2023-11-21T11:00:00",
    shippingAddress: "Jl. Gatot Subroto No. 654, Medan",
    trackingNumber: null,
    notes: null
  }
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "pending":
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
    case "processing":
      return <Badge variant="default" className="bg-blue-100 text-blue-800">Processing</Badge>
    case "shipped":
      return <Badge variant="default" className="bg-purple-100 text-purple-800">Shipped</Badge>
    case "delivered":
      return <Badge variant="default" className="bg-green-100 text-green-800">Delivered</Badge>
    case "cancelled":
      return <Badge variant="destructive" className="bg-red-100 text-red-800">Cancelled</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const getPaymentBadge = (status: string) => {
  switch (status) {
    case "paid":
      return <Badge variant="default" className="bg-green-100 text-green-800">Paid</Badge>
    case "pending":
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
    case "failed":
      return <Badge variant="destructive" className="bg-red-100 text-red-800">Failed</Badge>
    case "refunded":
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Refunded</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const getShippingBadge = (status: string) => {
  switch (status) {
    case "preparing":
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Preparing</Badge>
    case "shipped":
      return <Badge variant="default" className="bg-blue-100 text-blue-800">Shipped</Badge>
    case "delivered":
      return <Badge variant="default" className="bg-green-100 text-green-800">Delivered</Badge>
    case "cancelled":
      return <Badge variant="destructive" className="bg-red-100 text-red-800">Cancelled</Badge>
    case "waiting_payment":
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Waiting Payment</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [paymentFilter, setPaymentFilter] = useState("all")
  const [storeFilter, setStoreFilter] = useState("all")

  const filteredOrders = ordersData.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.store.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || order.status === statusFilter
    const matchesPayment = paymentFilter === "all" || order.paymentStatus === paymentFilter
    const matchesStore = storeFilter === "all" || order.store === storeFilter
    return matchesSearch && matchesStatus && matchesPayment && matchesStore
  })

  const totalOrders = ordersData.length
  const pendingOrders = ordersData.filter(o => o.status === "pending").length
  const shippedOrders = ordersData.filter(o => o.status === "shipped").length
  const totalRevenue = ordersData
    .filter(o => o.paymentStatus === "paid")
    .reduce((sum, order) => sum + order.total, 0)

  const stores = [...new Set(ordersData.map(o => o.store))]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back</span>
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Kelola Pesanan</h1>
            <p className="text-muted-foreground">
              Monitor dan fulfill semua pesanan dalam marketplace
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pesanan</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              Semua pesanan
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingOrders}</div>
            <p className="text-xs text-muted-foreground">
              Perlu diproses
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shipped Orders</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{shippedOrders}</div>
            <p className="text-xs text-muted-foreground">
              Dalam pengiriman
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              Dari pesanan paid
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Daftar Pesanan</CardTitle>
              <CardDescription>
                Monitor dan fulfill semua pesanan dalam marketplace
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari pesanan..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-[250px]"
                />
              </div>
              
              <Select value={storeFilter} onValueChange={setStoreFilter}>
                <SelectTrigger className="w-[160px]">
                  <SelectValue placeholder="Store" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Store</SelectItem>
                  {stores.map(store => (
                    <SelectItem key={store} value={store}>{store}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={paymentFilter} onValueChange={setPaymentFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Payment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Payment</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order ID</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Store</TableHead>
                <TableHead>Produk</TableHead>
                <TableHead>Total</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Payment</TableHead>
                <TableHead>Shipping</TableHead>
                <TableHead>Tanggal</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>
                    <div className="font-medium">{order.id}</div>
                    {order.trackingNumber && (
                      <div className="text-sm text-muted-foreground">
                        Track: {order.trackingNumber}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{order.customer.name}</div>
                      <div className="text-sm text-muted-foreground">{order.customer.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{order.store}</div>
                  </TableCell>
                  <TableCell>
                    <div>
                      {order.products.map((product, index) => (
                        <div key={index} className="text-sm">
                          {product.name} x{product.quantity}
                        </div>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>{formatCurrency(order.total)}</TableCell>
                  <TableCell>{getStatusBadge(order.status)}</TableCell>
                  <TableCell>{getPaymentBadge(order.paymentStatus)}</TableCell>
                  <TableCell>{getShippingBadge(order.shippingStatus)}</TableCell>
                  <TableCell className="text-sm">
                    {formatDate(order.orderDate)}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        {order.status === "pending" && (
                          <DropdownMenuItem className="text-blue-600">
                            <Package className="h-4 w-4 mr-2" />
                            Process Order
                          </DropdownMenuItem>
                        )}
                        {order.status === "processing" && (
                          <DropdownMenuItem className="text-purple-600">
                            <Truck className="h-4 w-4 mr-2" />
                            Ship Order
                          </DropdownMenuItem>
                        )}
                        {order.status === "shipped" && (
                          <DropdownMenuItem className="text-green-600">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Mark Delivered
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem className="text-red-600">
                          <XCircle className="h-4 w-4 mr-2" />
                          Cancel Order
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredOrders.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Tidak ada pesanan yang ditemukan</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 