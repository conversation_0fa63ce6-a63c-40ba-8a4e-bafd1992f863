"use client"

import { useState } from "react"
import Image from "next/image"
import { Search, Filter, Grid, List, Eye, Edit, Trash2, ChevronDown, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Slider } from "@/components/ui/slider"

// Mock data for products
const mockProducts = [
  {
    id: "p1",
    name: "Premium Wireless Headphones",
    image: "/product-image-1.png",
    store: "TechGadgets",
    category: "Electronics",
    price: 129.99,
    stock: 45,
    sales: 230,
    status: "active",
  },
  {
    id: "p2",
    name: "Ergonomic Office Chair",
    image: "/product-image-2.png",
    store: "HomeOffice",
    category: "Furniture",
    price: 249.99,
    stock: 18,
    sales: 87,
    status: "active",
  },
  {
    id: "p3",
    name: "Organic Cotton T-Shirt",
    image: "/product-image-3.png",
    store: "EcoFashion",
    category: "Clothing",
    price: 34.99,
    stock: 120,
    sales: 345,
    status: "active",
  },
  {
    id: "p4",
    name: "Smart Home Security Camera",
    image: "/product-image-4.png",
    store: "TechGadgets",
    category: "Electronics",
    price: 89.99,
    stock: 32,
    sales: 156,
    status: "active",
  },
  {
    id: "p5",
    name: "Handcrafted Ceramic Mug",
    image: "/product-image-5.png",
    store: "ArtisanCrafts",
    category: "Home & Kitchen",
    price: 24.99,
    stock: 65,
    sales: 210,
    status: "active",
  },
  {
    id: "p6",
    name: "Fitness Tracker Watch",
    image: "/product-image-6.png",
    store: "FitGear",
    category: "Fitness",
    price: 79.99,
    stock: 0,
    sales: 178,
    status: "out_of_stock",
  },
  {
    id: "p7",
    name: "Bluetooth Portable Speaker",
    image: "/product-image-1.png",
    store: "TechGadgets",
    category: "Electronics",
    price: 59.99,
    stock: 5,
    sales: 132,
    status: "low_stock",
  },
  {
    id: "p8",
    name: "Organic Skincare Set",
    image: "/product-image-3.png",
    store: "NaturalBeauty",
    category: "Beauty",
    price: 49.99,
    stock: 28,
    sales: 95,
    status: "active",
  },
  {
    id: "p9",
    name: "Stainless Steel Water Bottle",
    image: "/product-image-5.png",
    store: "EcoLiving",
    category: "Home & Kitchen",
    price: 29.99,
    stock: 42,
    sales: 187,
    status: "active",
  },
  {
    id: "p10",
    name: "Wireless Gaming Mouse",
    image: "/product-image-4.png",
    store: "GamerZone",
    category: "Electronics",
    price: 69.99,
    stock: 0,
    sales: 145,
    status: "out_of_stock",
  },
]

// Categories for filter
const categories = ["All Categories", "Electronics", "Furniture", "Clothing", "Home & Kitchen", "Beauty", "Fitness"]

// Stores for filter
const stores = [
  "All Stores",
  "TechGadgets",
  "HomeOffice",
  "EcoFashion",
  "ArtisanCrafts",
  "FitGear",
  "NaturalBeauty",
  "EcoLiving",
  "GamerZone",
]

// Status options for filter
const statusOptions = ["All Status", "active", "out_of_stock", "low_stock"]

export function ProductCatalog() {
  const [viewMode, setViewMode] = useState<"grid" | "table">("table")
  const [searchQuery, setSearchQuery] = useState("")
  const [showFilters, setShowFilters] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [selectedStore, setSelectedStore] = useState("All Stores")
  const [selectedStatus, setSelectedStatus] = useState("All Status")
  const [priceRange, setPriceRange] = useState([0, 300])
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null)

  // Filter products based on search query and filters
  const filteredProducts = mockProducts.filter((product) => {
    const matchesSearch =
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.store.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = selectedCategory === "All Categories" || product.category === selectedCategory
    const matchesStore = selectedStore === "All Stores" || product.store === selectedStore
    const matchesStatus = selectedStatus === "All Status" || product.status === selectedStatus
    const matchesPriceRange = product.price >= priceRange[0] && product.price <= priceRange[1]

    return matchesSearch && matchesCategory && matchesStore && matchesStatus && matchesPriceRange
  })

  const handleDeleteClick = (productId: string) => {
    setSelectedProduct(productId)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    // In a real app, you would delete the product here
    console.log(`Deleting product ${selectedProduct}`)
    setDeleteDialogOpen(false)
    setSelectedProduct(null)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "out_of_stock":
        return <Badge variant="destructive">Out of Stock</Badge>
      case "low_stock":
        return (
          <Badge variant="outline" className="border-amber-500 text-amber-500">
            Low Stock
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex w-full max-w-sm items-center space-x-2">
          <Input
            placeholder="Search products..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
            prefix={<Search className="h-4 w-4 text-muted-foreground" />}
          />
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-1"
          >
            <Filter className="h-4 w-4" />
            Filters
            <ChevronDown className={`h-4 w-4 transition-transform ${showFilters ? "rotate-180" : ""}`} />
          </Button>
          <div className="flex items-center rounded-md border">
            <Button
              variant="ghost"
              size="icon"
              className={`rounded-none rounded-l-md ${viewMode === "table" ? "bg-muted" : ""}`}
              onClick={() => setViewMode("table")}
            >
              <List className="h-4 w-4" />
              <span className="sr-only">Table view</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`rounded-none rounded-r-md ${viewMode === "grid" ? "bg-muted" : ""}`}
              onClick={() => setViewMode("grid")}
            >
              <Grid className="h-4 w-4" />
              <span className="sr-only">Grid view</span>
            </Button>
          </div>
          <Button size="sm">Add Product</Button>
        </div>
      </div>

      {showFilters && (
        <Card>
          <CardContent className="p-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Store</label>
                <Select value={selectedStore} onValueChange={setSelectedStore}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select store" />
                  </SelectTrigger>
                  <SelectContent>
                    {stores.map((store) => (
                      <SelectItem key={store} value={store}>
                        {store}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status === "active"
                          ? "Active"
                          : status === "out_of_stock"
                            ? "Out of Stock"
                            : status === "low_stock"
                              ? "Low Stock"
                              : status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Price Range: ${priceRange[0]} - ${priceRange[1]}
                </label>
                <Slider value={priceRange} min={0} max={300} step={5} onValueChange={setPriceRange} className="py-4" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div>
        {viewMode === "table" ? (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Store</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                  <TableHead className="text-right">Stock</TableHead>
                  <TableHead className="text-right">Sales</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 overflow-hidden rounded-md">
                          <Image
                            src={product.image || "/placeholder.svg"}
                            alt={product.name}
                            width={40}
                            height={40}
                            className="h-full w-full object-cover"
                          />
                        </div>
                        <span>{product.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{product.store}</TableCell>
                    <TableCell>{product.category}</TableCell>
                    <TableCell className="text-right">${product.price.toFixed(2)}</TableCell>
                    <TableCell className="text-right">{product.stock}</TableCell>
                    <TableCell className="text-right">{product.sales}</TableCell>
                    <TableCell>{getStatusBadge(product.status)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">View</span>
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleDeleteClick(product.id)}>
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {filteredProducts.map((product) => (
              <Card key={product.id}>
                <CardContent className="p-0">
                  <div className="relative h-48 w-full overflow-hidden">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-cover transition-transform hover:scale-105"
                    />
                    <div className="absolute right-2 top-2">{getStatusBadge(product.status)}</div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold">{product.name}</h3>
                    <div className="mt-1 flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">{product.store}</span>
                      <span className="font-medium">${product.price.toFixed(2)}</span>
                    </div>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Stock: {product.stock}</span>
                      <span className="text-sm text-muted-foreground">Sales: {product.sales}</span>
                    </div>
                    <div className="mt-4 flex justify-end gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="mr-1 h-4 w-4" />
                        View
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => handleDeleteClick(product.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this product? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center gap-4 py-4">
            <div className="rounded-full bg-destructive/10 p-2 text-destructive">
              <AlertCircle className="h-6 w-6" />
            </div>
            <div>
              <p className="font-medium">This will permanently delete the product</p>
              <p className="text-sm text-muted-foreground">The product will be removed from all stores and catalogs.</p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete Product
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
