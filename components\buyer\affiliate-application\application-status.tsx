"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { CheckCircle, Clock, AlertCircle, ArrowRight } from "lucide-react"

interface ApplicationStatusProps {
  applicationId: string
}

export function ApplicationStatus({ applicationId }: ApplicationStatusProps) {
  const router = useRouter()
  const [notificationsEnabled, setNotificationsEnabled] = useState(true)

  // In a real app, this would come from the API
  const status = "under-review" // Options: under-review, approved, needs-info, rejected
  const submittedDate = new Date().toLocaleDateString()
  const estimatedCompletionDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString()

  const getStatusBadge = () => {
    switch (status) {
      case "under-review":
        return (
          <Badge className="bg-yellow-500/20 text-yellow-700 hover:bg-yellow-500/30 dark:bg-yellow-500/30 dark:text-yellow-300">
            <Clock className="mr-1 h-3 w-3" />
            Under Review
          </Badge>
        )
      case "approved":
        return (
          <Badge className="bg-green-500/20 text-green-700 hover:bg-green-500/30 dark:bg-green-500/30 dark:text-green-300">
            <CheckCircle className="mr-1 h-3 w-3" />
            Approved
          </Badge>
        )
      case "needs-info":
        return (
          <Badge className="bg-blue-500/20 text-blue-700 hover:bg-blue-500/30 dark:bg-blue-500/30 dark:text-blue-300">
            <AlertCircle className="mr-1 h-3 w-3" />
            Needs Information
          </Badge>
        )
      case "rejected":
        return (
          <Badge className="bg-red-500/20 text-red-700 hover:bg-red-500/30 dark:bg-red-500/30 dark:text-red-300">
            <AlertCircle className="mr-1 h-3 w-3" />
            Rejected
          </Badge>
        )
      default:
        return null
    }
  }

  const getStatusMessage = () => {
    switch (status) {
      case "under-review":
        return "Your application is currently being reviewed by our team. This process typically takes 2-3 business days. We'll notify you once a decision has been made."
      case "approved":
        return "Congratulations! Your application has been approved. You can now access your affiliate dashboard and start promoting products to earn commissions."
      case "needs-info":
        return "We need additional information to process your application. Please check the details below and provide the requested information as soon as possible."
      case "rejected":
        return "We're sorry, but your application has been rejected. Please review the feedback below for more information. You may reapply after 30 days."
      default:
        return ""
    }
  }

  const getProgressValue = () => {
    switch (status) {
      case "under-review":
        return 50
      case "approved":
        return 100
      case "needs-info":
        return 75
      case "rejected":
        return 100
      default:
        return 0
    }
  }

  const handleGoToDashboard = () => {
    router.push("/buyer/dashboard/affiliate")
  }

  return (
    <Card className="border-border/60">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle>Application Status</CardTitle>
          {getStatusBadge()}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">{getStatusMessage()}</p>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Application Progress</span>
            <span className="font-medium">{getProgressValue()}%</span>
          </div>
          <Progress value={getProgressValue()} className="h-2" />
        </div>

        <div className="rounded-md border border-border/60 bg-muted/30 p-4 space-y-3">
          <h3 className="text-sm font-medium">Application Details</h3>
          <div className="grid gap-2 text-sm sm:grid-cols-2">
            <div className="text-muted-foreground">Application ID:</div>
            <div className="font-medium">{applicationId}</div>
            <div className="text-muted-foreground">Submitted Date:</div>
            <div>{submittedDate}</div>
            <div className="text-muted-foreground">Estimated Completion:</div>
            <div>{estimatedCompletionDate}</div>
          </div>
        </div>

        {status === "needs-info" && (
          <div className="rounded-md border border-blue-200 bg-blue-50 p-4 dark:border-blue-900/50 dark:bg-blue-900/20">
            <h3 className="mb-2 text-sm font-medium text-blue-800 dark:text-blue-300">
              Additional Information Required
            </h3>
            <ul className="list-disc pl-5 text-sm text-blue-700 dark:text-blue-300">
              <li>Please provide more details about your promotion channels</li>
              <li>We need verification of your website ownership</li>
              <li>Additional sample content is required</li>
            </ul>
            <div className="mt-3">
              <Button variant="outline" className="w-full">
                Submit Additional Information
              </Button>
            </div>
          </div>
        )}

        {status === "rejected" && (
          <div className="rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-900/50 dark:bg-red-900/20">
            <h3 className="mb-2 text-sm font-medium text-red-800 dark:text-red-300">Rejection Reason</h3>
            <p className="text-sm text-red-700 dark:text-red-300">
              Your application did not meet our current requirements for the affiliate program. Common reasons include
              insufficient audience size, content quality concerns, or misalignment with our product categories.
            </p>
            <div className="mt-3">
              <Button variant="outline" className="w-full">
                Request Feedback
              </Button>
            </div>
          </div>
        )}

        <div className="flex items-center justify-between space-x-2">
          <div className="space-y-0.5">
            <Label htmlFor="notifications" className="text-sm">
              Email Notifications
            </Label>
            <p className="text-xs text-muted-foreground">Receive updates about your application status</p>
          </div>
          <Switch id="notifications" checked={notificationsEnabled} onCheckedChange={setNotificationsEnabled} />
        </div>
      </CardContent>
      <CardFooter className="border-t border-border/40 bg-muted/30 px-6 py-4">
        {status === "approved" ? (
          <Button onClick={handleGoToDashboard} className="ml-auto">
            Go to Affiliate Dashboard
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        ) : (
          <Button variant="outline" onClick={() => router.push("/buyer/dashboard")} className="ml-auto">
            Return to Dashboard
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
