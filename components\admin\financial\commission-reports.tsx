"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/charts"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Download, Filter } from "lucide-react"

// Mock data for commission transactions
const commissionTransactions = [
  {
    id: "TRX-12345",
    store: "Tech Haven",
    tenant: "Digital Marketplace",
    amount: "$1,250.00",
    commission: "$62.50",
    rate: "5%",
    date: "2023-10-15",
  },
  {
    id: "TRX-12346",
    store: "Fashion Forward",
    tenant: "Style Hub",
    amount: "$890.00",
    commission: "$44.50",
    rate: "5%",
    date: "2023-10-14",
  },
  {
    id: "TRX-12347",
    store: "Home Essentials",
    tenant: "Living Space",
    amount: "$2,100.00",
    commission: "$147.00",
    rate: "7%",
    date: "2023-10-14",
  },
  {
    id: "TRX-12348",
    store: "Gourmet Delights",
    tenant: "Food Paradise",
    amount: "$450.00",
    commission: "$22.50",
    rate: "5%",
    date: "2023-10-13",
  },
  {
    id: "TRX-12349",
    store: "Fitness Gear",
    tenant: "Health Zone",
    amount: "$780.00",
    commission: "$39.00",
    rate: "5%",
    date: "2023-10-13",
  },
  {
    id: "TRX-12350",
    store: "Book Nook",
    tenant: "Knowledge Hub",
    amount: "$320.00",
    commission: "$16.00",
    rate: "5%",
    date: "2023-10-12",
  },
  {
    id: "TRX-12351",
    store: "Tech Haven",
    tenant: "Digital Marketplace",
    amount: "$1,800.00",
    commission: "$90.00",
    rate: "5%",
    date: "2023-10-12",
  },
  {
    id: "TRX-12352",
    store: "Beauty Spot",
    tenant: "Glamour World",
    amount: "$560.00",
    commission: "$28.00",
    rate: "5%",
    date: "2023-10-11",
  },
  {
    id: "TRX-12353",
    store: "Pet Paradise",
    tenant: "Animal Kingdom",
    amount: "$420.00",
    commission: "$21.00",
    rate: "5%",
    date: "2023-10-11",
  },
  {
    id: "TRX-12354",
    store: "Toy World",
    tenant: "Kids Zone",
    amount: "$650.00",
    commission: "$32.50",
    rate: "5%",
    date: "2023-10-10",
  },
]

// Mock data for commission by tenant
const commissionByTenant = [
  { name: "Digital Marketplace", value: 35 },
  { name: "Style Hub", value: 20 },
  { name: "Living Space", value: 15 },
  { name: "Food Paradise", value: 10 },
  { name: "Health Zone", value: 8 },
  { name: "Others", value: 12 },
]

// Mock data for commission trend
const commissionTrend = [
  { name: "Jan", commission: 12500 },
  { name: "Feb", commission: 14200 },
  { name: "Mar", commission: 16800 },
  { name: "Apr", commission: 15600 },
  { name: "May", commission: 18400 },
  { name: "Jun", commission: 21000 },
  { name: "Jul", commission: 22800 },
  { name: "Aug", commission: 21600 },
  { name: "Sep", commission: 24000 },
  { name: "Oct", commission: 26400 },
  { name: "Nov", commission: 25000 },
  { name: "Dec", commission: 29000 },
]

// Mock data for commission rates
const commissionRates = [
  { name: "5%", value: 75 },
  { name: "7%", value: 15 },
  { name: "10%", value: 8 },
  { name: "Custom", value: 2 },
]

export function CommissionReports() {
  const [searchTerm, setSearchTerm] = useState("")
  const [dateRange, setDateRange] = useState("all")

  // Filter transactions based on search term
  const filteredTransactions = commissionTransactions.filter(
    (transaction) =>
      transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.store.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.tenant.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="grid gap-6">
      <Tabs defaultValue="transactions" className="w-full">
        <TabsList className="grid w-full grid-cols-4 lg:w-[600px]">
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="breakdown">Tenant Breakdown</TabsTrigger>
          <TabsTrigger value="rates">Commission Rates</TabsTrigger>
          <TabsTrigger value="trends">Trend Analysis</TabsTrigger>
        </TabsList>
        <TabsContent value="transactions" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div>
                  <CardTitle>Commission Transactions</CardTitle>
                  <CardDescription>Detailed list of all commission transactions</CardDescription>
                </div>
                <div className="flex flex-col gap-2 sm:flex-row">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search transactions..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select defaultValue={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                      <SelectItem value="quarter">This Quarter</SelectItem>
                      <SelectItem value="year">This Year</SelectItem>
                      <SelectItem value="all">All Time</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Transaction ID</TableHead>
                      <TableHead>Store</TableHead>
                      <TableHead>Tenant</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                      <TableHead className="text-right">Commission</TableHead>
                      <TableHead className="text-center">Rate</TableHead>
                      <TableHead className="text-right">Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTransactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell className="font-medium">{transaction.id}</TableCell>
                        <TableCell>{transaction.store}</TableCell>
                        <TableCell>{transaction.tenant}</TableCell>
                        <TableCell className="text-right">{transaction.amount}</TableCell>
                        <TableCell className="text-right">{transaction.commission}</TableCell>
                        <TableCell className="text-center">
                          <Badge variant="outline">{transaction.rate}</Badge>
                        </TableCell>
                        <TableCell className="text-right">{transaction.date}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <div className="flex items-center justify-end space-x-2 py-4">
                <Button variant="outline" size="sm">
                  Previous
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="breakdown" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Commission Breakdown by Tenant</CardTitle>
              <CardDescription>Distribution of commission revenue by tenant</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <PieChart
                  data={commissionByTenant}
                  index="name"
                  category="value"
                  valueFormatter={(value) => `${value}%`}
                  className="h-full"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="rates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Commission Rate Distribution</CardTitle>
              <CardDescription>Breakdown of transactions by commission rate</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <BarChart
                  data={commissionRates}
                  index="name"
                  categories={["value"]}
                  colors={["blue"]}
                  valueFormatter={(value) => `${value}%`}
                  className="h-full"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="trends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Commission Trend Analysis</CardTitle>
              <CardDescription>Monthly commission revenue trend</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <LineChart
                  data={commissionTrend}
                  index="name"
                  categories={["commission"]}
                  colors={["blue"]}
                  valueFormatter={(value) => `$${value.toLocaleString()}`}
                  className="h-full"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
