import { NextRequest, NextResponse } from 'next/server';
import { productModerationService } from '@/lib/services/product-moderation';

// GET - Mendapatkan product moderation berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    const moderation = await productModerationService.getModeration(id);
    
    if (!moderation) {
      return NextResponse.json(
        { error: 'Moderation not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(moderation);
  } catch (error) {
    console.error('Error fetching moderation:', error);
    return NextResponse.json(
      { error: 'Failed to fetch moderation' },
      { status: 500 }
    );
  }
}

// PUT - Update product moderation
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    
    const updatedModeration = await productModerationService.updateModeration(id, body);
    
    return NextResponse.json(updatedModeration);
  } catch (error) {
    console.error('Error updating moderation:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update moderation';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// PATCH - Specific moderation actions
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { action, reviewer_id, notes, reason, required_changes, flags } = body;
    
    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }
    
    let result;
    
    switch (action) {
      case 'approve':
        if (!reviewer_id) {
          return NextResponse.json(
            { error: 'reviewer_id is required for approval' },
            { status: 400 }
          );
        }
        result = await productModerationService.approveProduct(id, reviewer_id, notes);
        break;
        
      case 'reject':
        if (!reviewer_id || !reason) {
          return NextResponse.json(
            { error: 'reviewer_id and reason are required for rejection' },
            { status: 400 }
          );
        }
        result = await productModerationService.rejectProduct(id, reviewer_id, reason, required_changes);
        break;
        
      case 'request_changes':
        if (!reviewer_id || !required_changes || !Array.isArray(required_changes)) {
          return NextResponse.json(
            { error: 'reviewer_id and required_changes array are required' },
            { status: 400 }
          );
        }
        result = await productModerationService.requestChanges(id, reviewer_id, required_changes, notes);
        break;
        
      case 'set_under_review':
        if (!reviewer_id) {
          return NextResponse.json(
            { error: 'reviewer_id is required' },
            { status: 400 }
          );
        }
        result = await productModerationService.setUnderReview(id, reviewer_id, notes);
        break;
        
      case 'add_flags':
        if (!flags || !Array.isArray(flags)) {
          return NextResponse.json(
            { error: 'flags array is required' },
            { status: 400 }
          );
        }
        result = await productModerationService.addFlags(id, flags);
        break;
        
      case 'remove_flags':
        if (!flags || !Array.isArray(flags)) {
          return NextResponse.json(
            { error: 'flags array is required' },
            { status: 400 }
          );
        }
        result = await productModerationService.removeFlags(id, flags);
        break;
        
      case 'update_checklist':
        if (!body.checklist || typeof body.checklist !== 'object') {
          return NextResponse.json(
            { error: 'checklist object is required' },
            { status: 400 }
          );
        }
        result = await productModerationService.updateChecklist(id, body.checklist);
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('Error performing moderation action:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to perform action';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// DELETE - Hapus product moderation
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    await productModerationService.deleteModeration(id);
    
    return NextResponse.json({ message: 'Moderation deleted successfully' });
  } catch (error) {
    console.error('Error deleting moderation:', error);
    return NextResponse.json(
      { error: 'Failed to delete moderation' },
      { status: 500 }
    );
  }
}
