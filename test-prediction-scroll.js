// Test script untuk memverifikasi bahwa container prediksi tidak memiliki scroll internal
// Jalankan di browser console saat berada di halaman /sellzio

console.log('🧪 Testing Prediction Container Scroll Behavior...');

// Function to test scroll behavior
function testPredictionScroll() {
  const searchInput = document.querySelector('.search-input');
  const predictionContainer = document.querySelector('.keyword-predictions');
  
  if (!searchInput) {
    console.error('❌ Search input not found');
    return;
  }

  // Step 1: Focus and type to trigger predictions
  console.log('🔍 Step 1: Triggering predictions...');
  searchInput.focus();
  searchInput.value = 'tas';
  searchInput.dispatchEvent(new Event('input', { bubbles: true }));

  setTimeout(() => {
    if (!predictionContainer) {
      console.error('❌ Prediction container not found');
      return;
    }

    // Step 2: Check container styles
    const computedStyle = window.getComputedStyle(predictionContainer);
    console.log('📊 Step 2: Container Style Analysis:');
    console.log('  - Display:', computedStyle.display);
    console.log('  - Position:', computedStyle.position);
    console.log('  - Top:', computedStyle.top);
    console.log('  - Height:', computedStyle.height);
    console.log('  - Max-height:', computedStyle.maxHeight);
    console.log('  - Overflow:', computedStyle.overflow);
    console.log('  - Overflow-y:', computedStyle.overflowY);

    // Step 3: Check if container has internal scroll
    const hasInternalScroll = predictionContainer.scrollHeight > predictionContainer.clientHeight;
    console.log('📏 Step 3: Scroll Analysis:');
    console.log('  - Client Height:', predictionContainer.clientHeight);
    console.log('  - Scroll Height:', predictionContainer.scrollHeight);
    console.log('  - Has Internal Scroll:', hasInternalScroll);
    
    if (hasInternalScroll) {
      console.warn('⚠️ WARNING: Container has internal scroll!');
    } else {
      console.log('✅ GOOD: Container has no internal scroll');
    }

    // Step 4: Check body scroll behavior
    const bodyStyle = window.getComputedStyle(document.body);
    console.log('🌐 Step 4: Body Scroll Analysis:');
    console.log('  - Body Overflow:', bodyStyle.overflow);
    console.log('  - Body Overflow-y:', bodyStyle.overflowY);
    console.log('  - Body Position:', bodyStyle.position);
    
    // Step 5: Check if page can scroll
    const canPageScroll = document.body.scrollHeight > window.innerHeight;
    console.log('📄 Step 5: Page Scroll Analysis:');
    console.log('  - Window Height:', window.innerHeight);
    console.log('  - Body Scroll Height:', document.body.scrollHeight);
    console.log('  - Can Page Scroll:', canPageScroll);

    // Step 6: Test actual scrolling
    console.log('🔄 Step 6: Testing Scroll Behavior...');
    const initialScrollTop = window.pageYOffset;
    window.scrollBy(0, 100);
    
    setTimeout(() => {
      const newScrollTop = window.pageYOffset;
      const didPageScroll = newScrollTop !== initialScrollTop;
      console.log('  - Initial Scroll Position:', initialScrollTop);
      console.log('  - New Scroll Position:', newScrollTop);
      console.log('  - Did Page Scroll:', didPageScroll);
      
      if (didPageScroll) {
        console.log('✅ GOOD: Page can scroll when predictions are shown');
      } else {
        console.warn('⚠️ WARNING: Page cannot scroll when predictions are shown');
      }

      // Reset scroll position
      window.scrollTo(0, initialScrollTop);

      // Step 7: Check distance from header
      const headerHeight = 60; // Expected header height
      const containerTop = predictionContainer.getBoundingClientRect().top;
      const expectedTop = headerHeight;
      
      console.log('📐 Step 7: Container Position Analysis:');
      console.log('  - Container Top (from viewport):', containerTop);
      console.log('  - Expected Top:', expectedTop);
      console.log('  - Distance from Header:', Math.abs(containerTop - expectedTop));
      
      if (Math.abs(containerTop - expectedTop) <= 5) {
        console.log('✅ GOOD: Container is positioned correctly below header');
      } else {
        console.warn('⚠️ WARNING: Container position may not match docs/facet.html');
      }

      // Final summary
      console.log('\n🏁 SUMMARY:');
      console.log('  ✅ Container visible:', computedStyle.display !== 'none');
      console.log('  ✅ No internal scroll:', !hasInternalScroll);
      console.log('  ✅ Page can scroll:', didPageScroll);
      console.log('  ✅ Correct position:', Math.abs(containerTop - expectedTop) <= 5);
      
      const allGood = computedStyle.display !== 'none' && 
                     !hasInternalScroll && 
                     didPageScroll && 
                     Math.abs(containerTop - expectedTop) <= 5;
      
      if (allGood) {
        console.log('🎉 ALL TESTS PASSED! Prediction container behaves correctly.');
      } else {
        console.log('❌ Some tests failed. Check the warnings above.');
      }
    }, 100);
  }, 200);
}

// Run the test
testPredictionScroll();
