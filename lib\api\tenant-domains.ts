import { apiClient } from "@/lib/api"
import type { TenantDomain, CreateTenantDomainDTO, UpdateTenantDomainDTO } from "@/lib/models/tenant-domain"

// Mock data untuk tenant domains
const mockDomains: TenantDomain[] = [
  {
    id: '1',
    tenantId: '1',
    domain: 'example.com',
    status: 'verified',
    isPrimary: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    tenantId: '1',
    domain: 'store.example.com',
    status: 'pending',
    isPrimary: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

// Helper function untuk menghasilkan mock data yang disesuaikan
const getMockDomainsForTenant = (tenantId: string): TenantDomain[] => {
  return mockDomains.map(domain => ({
    ...domain,
    tenantId: tenantId
  }));
};

// Konstanta untuk mengontrol apakah kita menggunakan mock data atau API
const USE_MOCK_DATA = true;

export const tenantDomainsAPI = {
  // Mendapatkan semua domain untuk tenant tertentu
  getByTenantId: async (tenantId: string): Promise<TenantDomain[]> => {
    // Jika USE_MOCK_DATA true, langsung kembalikan mock data
    if (USE_MOCK_DATA) {
      console.info("Using mock data for tenant domains");
      return getMockDomainsForTenant(tenantId);
    }
    
    try {
      const response = await apiClient.get(`/tenant-domains?tenantId=${tenantId}`)
      return response.data
    } catch (error: any) {
      console.error("Error fetching tenant domains:", error)
      return getMockDomainsForTenant(tenantId);
    }
  },

  // Mendapatkan domain berdasarkan nama domain
  getByDomain: async (domain: string): Promise<TenantDomain | null> => {
    if (USE_MOCK_DATA) {
      return mockDomains.find(d => d.domain === domain) || null;
    }
    
    try {
      const response = await apiClient.get(`/tenant-domains/domain/${domain}`)
      return response.data
    } catch (error) {
      console.error("Error fetching tenant domain:", error)
      return mockDomains.find(d => d.domain === domain) || null
    }
  },

  // Membuat domain baru
  create: async (data: CreateTenantDomainDTO): Promise<TenantDomain | null> => {
    if (USE_MOCK_DATA) {
      const newDomain: TenantDomain = {
        id: Math.random().toString(36).substring(7),
        tenantId: data.tenantId,
        domain: data.domain,
        status: 'pending',
        isPrimary: data.isPrimary || false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
      return newDomain;
    }
    
    try {
      const response = await apiClient.post("/tenant-domains", data)
      return response.data
    } catch (error) {
      console.error("Error creating tenant domain:", error)
      const newDomain: TenantDomain = {
        id: Math.random().toString(36).substring(7),
        tenantId: data.tenantId,
        domain: data.domain,
        status: 'pending',
        isPrimary: data.isPrimary || false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
      return newDomain
    }
  },

  // Memperbarui domain
  update: async (id: string, data: UpdateTenantDomainDTO): Promise<TenantDomain | null> => {
    if (USE_MOCK_DATA) {
      const domainIndex = mockDomains.findIndex(d => d.id === id);
      if (domainIndex >= 0) {
        const updatedDomain = {
          ...mockDomains[domainIndex],
          ...data,
          updatedAt: new Date().toISOString()
        };
        return updatedDomain;
      }
      return null;
    }
    
    try {
      const response = await apiClient.patch(`/tenant-domains/${id}`, data)
      return response.data
    } catch (error) {
      console.error("Error updating tenant domain:", error)
      return null
    }
  },

  // Menghapus domain
  delete: async (id: string): Promise<boolean> => {
    if (USE_MOCK_DATA) {
      return true;
    }
    
    try {
      await apiClient.delete(`/tenant-domains/${id}`)
      return true
    } catch (error) {
      console.error("Error deleting tenant domain:", error)
      return false
    }
  },

  // Memverifikasi domain
  verify: async (id: string): Promise<TenantDomain | null> => {
    if (USE_MOCK_DATA) {
      const domain = mockDomains.find(d => d.id === id);
      if (domain) {
        return {
          ...domain,
          status: 'verified',
          isVerified: true,
          updatedAt: new Date().toISOString()
        };
      }
      return null;
    }
    
    try {
      const response = await apiClient.post(`/tenant-domains/${id}/verify`)
      return response.data
    } catch (error) {
      console.error("Error verifying tenant domain:", error)
      return null
    }
  },
}
