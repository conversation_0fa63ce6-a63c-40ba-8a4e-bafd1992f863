"use client"

import * as React from "react"
import { format, startOfDay, endOfDay, startOfMonth, endOfMonth, subDays, subMonths, setMonth, setYear } from "date-fns"
import { id } from "date-fns/locale"
import { CalendarIcon, ChevronDown, ChevronLeft, ChevronRight } from "lucide-react"
import type { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface DateRangePickerProps {
  dateRange: DateRange | undefined
  onDateRangeChange: (dateRange: DateRange | undefined) => void
  className?: string
}

export function DateRangePicker({ dateRange, onDateRangeChange, className }: DateRangePickerProps) {
  const [isPopoverOpen, setIsPopoverOpen] = React.useState(false)
  const [currentMonth, setCurrentMonth] = React.useState<Date>(new Date())

  // Nama bulan dalam bahasa Indonesia
  const monthNames = [
    "Januari",
    "Februari",
    "Maret",
    "April",
    "Mei",
    "Juni",
    "Juli",
    "Agustus",
    "September",
    "Oktober",
    "November",
    "Desember",
  ]

  // Generate array tahun (10 tahun ke belakang dan 10 tahun ke depan)
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 21 }, (_, i) => currentYear - 10 + i)

  // Predefined date ranges
  const handlePredefinedRange = (option: string) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    let from: Date
    let to: Date

    switch (option) {
      case "today":
        from = startOfDay(today)
        to = endOfDay(today)
        break
      case "yesterday":
        from = startOfDay(subDays(today, 1))
        to = endOfDay(subDays(today, 1))
        break
      case "last7days":
        from = startOfDay(subDays(today, 6))
        to = endOfDay(today)
        break
      case "last30days":
        from = startOfDay(subDays(today, 29))
        to = endOfDay(today)
        break
      case "thisMonth":
        from = startOfMonth(today)
        to = endOfDay(today)
        break
      case "lastMonth":
        const lastMonth = subMonths(today, 1)
        from = startOfMonth(lastMonth)
        to = endOfMonth(lastMonth)
        break
      default:
        return
    }

    onDateRangeChange({ from, to })
    setIsPopoverOpen(false)
  }

  // Handle month change
  const handleMonthChange = (monthIndex: number) => {
    const newDate = setMonth(currentMonth, monthIndex)
    setCurrentMonth(newDate)
  }

  // Handle year change
  const handleYearChange = (year: number) => {
    const newDate = setYear(currentMonth, year)
    setCurrentMonth(newDate)
  }

  // Custom day names in Indonesian
  const dayNames = ["Minggu", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu"]

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn("w-[300px] justify-start text-left font-normal", !dateRange && "text-muted-foreground")}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {dateRange?.from ? (
              dateRange.to ? (
                <>
                  {format(dateRange.from, "dd MMMM yyyy", { locale: id })} -{" "}
                  {format(dateRange.to, "dd MMMM yyyy", { locale: id })}
                </>
              ) : (
                format(dateRange.from, "dd MMMM yyyy", { locale: id })
              )
            ) : (
              <span>Pilih rentang tanggal</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3 border-b">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm">Rentang Tanggal</h4>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-8 px-2">
                      Pilih Periode <ChevronDown className="ml-1 h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[200px]">
                    <DropdownMenuGroup>
                      <DropdownMenuItem onClick={() => handlePredefinedRange("today")}>Hari Ini</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handlePredefinedRange("yesterday")}>Kemarin</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handlePredefinedRange("last7days")}>
                        7 Hari Terakhir
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handlePredefinedRange("last30days")}>
                        30 Hari Terakhir
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handlePredefinedRange("thisMonth")}>Bulan Ini</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handlePredefinedRange("lastMonth")}>Bulan Lalu</DropdownMenuItem>
                    </DropdownMenuGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>

          {/* Custom Calendar Header with Month/Year Selectors */}
          <div className="p-3 flex items-center justify-between">
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7"
              onClick={() => {
                const prevMonth = new Date(currentMonth)
                prevMonth.setMonth(prevMonth.getMonth() - 1)
                setCurrentMonth(prevMonth)
              }}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="flex items-center gap-2">
              <Select
                value={currentMonth.getMonth().toString()}
                onValueChange={(value) => handleMonthChange(Number.parseInt(value))}
              >
                <SelectTrigger className="h-8 w-[120px]">
                  <SelectValue>{monthNames[currentMonth.getMonth()]}</SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {monthNames.map((month, index) => (
                    <SelectItem key={index} value={index.toString()}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={currentMonth.getFullYear().toString()}
                onValueChange={(value) => handleYearChange(Number.parseInt(value))}
              >
                <SelectTrigger className="h-8 w-[90px]">
                  <SelectValue>{currentMonth.getFullYear()}</SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7"
              onClick={() => {
                const nextMonth = new Date(currentMonth)
                nextMonth.setMonth(nextMonth.getMonth() + 1)
                setCurrentMonth(nextMonth)
              }}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <Calendar
            initialFocus
            mode="range"
            defaultMonth={dateRange?.from}
            month={currentMonth}
            onMonthChange={setCurrentMonth}
            selected={dateRange}
            onSelect={onDateRangeChange}
            numberOfMonths={2}
            locale={id}
            weekStartsOn={1} // Start week on Monday
            classNames={{
              day_range_start: "rounded-l-md",
              day_range_end: "rounded-r-md",
              caption: "hidden", // Hide default caption since we have custom header
              nav: "hidden", // Hide default navigation
              table: "w-full border-collapse space-y-1",
              head_row: "flex",
              head_cell: "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
              row: "flex w-full mt-2",
              cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
              day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100",
              day_selected:
                "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
              day_today: "bg-accent text-accent-foreground",
              day_outside: "text-muted-foreground opacity-50",
              day_disabled: "text-muted-foreground opacity-50",
              day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
              day_hidden: "invisible",
            }}
            formatters={{
              formatWeekdayName: (day) => dayNames[day],
            }}
          />
          <div className="flex items-center justify-between p-3 border-t">
            <Button
              variant="ghost"
              onClick={() => {
                onDateRangeChange(undefined)
                setIsPopoverOpen(false)
              }}
              size="sm"
            >
              Reset
            </Button>
            <Button size="sm" onClick={() => setIsPopoverOpen(false)}>
              Terapkan
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
