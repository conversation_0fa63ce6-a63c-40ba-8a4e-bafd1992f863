"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Users,
  CheckCircle2,
  XCircle,
  Clock,
  Mail,
  Phone,
  User,
  Link as LinkIcon,
  MessageSquare,
  ExternalLink,
  MoreHorizontal,
  AlertCircle,
  Filter
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk aplikasi afiliasi
const affiliateApplications = [
  {
    id: "app-001",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "081234567890",
    website: "https://budiblog.com",
    socialMedia: ["Instagram: @budisantoso", "TikTok: @budireviews"],
    audience: 5000,
    niche: "Technology, Gadget Reviews",
    reason: "Saya telah menulis konten gadget dan teknologi selama 3 tahun dan memiliki audiens yang tertarik dengan produk elektronik terbaru.",
    status: "pending",
    dateSubmitted: "2024-01-15T08:30:00",
    notes: ""
  },
  {
    id: "app-002",
    name: "Siti Nuraini",
    email: "<EMAIL>",
    phone: "081256789012",
    website: "https://sitifashion.id",
    socialMedia: ["Instagram: @sitistyle", "YouTube: Siti Fashion Channel"],
    audience: 15000,
    niche: "Fashion, Beauty",
    reason: "Saya adalah influencer fashion dengan 15k followers dan ingin merekomendasikan produk fashion berkualitas kepada audiens saya.",
    status: "approved",
    dateSubmitted: "2024-01-10T14:20:00",
    dateReviewed: "2024-01-12T09:15:00",
    notes: "Influencer dengan engagement rate tinggi, cocok untuk produk fashion dan kecantikan."
  },
  {
    id: "app-003",
    name: "Ahmad Rizki",
    email: "<EMAIL>",
    phone: "081234567891",
    website: "https://foodieindonesia.com",
    socialMedia: ["Instagram: @ahmadfoodies", "TikTok: @foodieindonesia"],
    audience: 8000,
    niche: "Food, Culinary Reviews",
    reason: "Saya sering membagikan pengalaman kuliner dan resep makanan. Ingin mempromosikan produk makanan dan alat dapur kepada pengikut saya.",
    status: "rejected",
    dateSubmitted: "2024-01-08T11:45:00",
    dateReviewed: "2024-01-11T13:20:00",
    notes: "Niche produk tidak sesuai dengan kategori produk yang saat ini tersedia di marketplace."
  },
  {
    id: "app-004",
    name: "Maya Putri",
    email: "<EMAIL>",
    phone: "081290123456",
    website: "https://travelwithmaya.com",
    socialMedia: ["Instagram: @mayatravel", "YouTube: Maya's Travel Diaries"],
    audience: 25000,
    niche: "Travel, Lifestyle",
    reason: "Sebagai travel blogger, saya ingin merekomendasikan produk travel dan lifestyle kepada pengikut saya yang senang traveling.",
    status: "pending",
    dateSubmitted: "2024-01-14T16:30:00",
    notes: ""
  },
  {
    id: "app-005",
    name: "Deni Hermawan",
    email: "<EMAIL>",
    phone: "081278901234",
    website: "https://denitechgeek.com",
    socialMedia: ["Twitter: @denitechgeek", "LinkedIn: Deni Hermawan"],
    audience: 12000,
    niche: "Technology, Software Reviews",
    reason: "Saya adalah tech reviewer dan ingin bermitra untuk mengulas produk elektronik dan gadget terbaru.",
    status: "approved",
    dateSubmitted: "2024-01-05T10:15:00",
    dateReviewed: "2024-01-09T14:30:00",
    notes: "Bagus untuk kategori elektronik dengan audience yang relevan."
  }
]

// Fungsi untuk badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "pending":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>
    case "approved": 
      return <Badge variant="outline" className="bg-green-100 text-green-800">Approved</Badge>
    case "rejected":
      return <Badge variant="outline" className="bg-red-100 text-red-800">Rejected</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric'
  })
}

// Fungsi untuk menghitung waktu yang telah berlalu
function timeAgo(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(months / 12)

  if (years > 0) return `${years} tahun lalu`
  if (months > 0) return `${months} bulan lalu`
  if (days > 0) return `${days} hari lalu`
  if (hours > 0) return `${hours} jam lalu`
  if (minutes > 0) return `${minutes} menit lalu`
  return `${seconds} detik lalu`
}

export default function AffiliateApplicationsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Filter applications
  const filteredApplications = affiliateApplications.filter(app => {
    const matchesSearch = app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          app.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          app.niche.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || app.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Statistik
  const stats = {
    total: affiliateApplications.length,
    pending: affiliateApplications.filter(a => a.status === "pending").length,
    approved: affiliateApplications.filter(a => a.status === "approved").length,
    rejected: affiliateApplications.filter(a => a.status === "rejected").length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/affiliates">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Affiliate Applications</h1>
            <p className="text-muted-foreground">
              Kelola dan tinjau permintaan pendaftaran afiliasi baru
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari nama, email, atau niche..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Applications List */}
      <div className="space-y-4">
        {filteredApplications.map((app) => (
          <Card key={app.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex flex-col gap-4">
                <div className="flex flex-col md:flex-row justify-between gap-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-semibold text-lg">{app.name}</span>
                      {getStatusBadge(app.status)}
                    </div>
                    <div className="flex items-center gap-3 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Mail className="h-3.5 w-3.5" />
                        <span>{app.email}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Phone className="h-3.5 w-3.5" />
                        <span>{app.phone}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-1 justify-end">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <p className="text-sm">Submitted {timeAgo(app.dateSubmitted)}</p>
                    </div>
                    {app.dateReviewed && (
                      <div className="flex items-center gap-1 justify-end mt-1">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm">Reviewed {timeAgo(app.dateReviewed)}</p>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium mb-1">Website</p>
                    <div className="flex items-center gap-1">
                      <LinkIcon className="h-4 w-4 text-blue-600" />
                      <a href={app.website} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 hover:underline">
                        {app.website} <ExternalLink className="h-3 w-3 inline" />
                      </a>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium mb-1">Audience Size</p>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-purple-600" />
                      <span className="text-sm">{app.audience.toLocaleString()} followers</span>
                    </div>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-1">Social Media</p>
                  <div className="flex flex-wrap gap-2">
                    {app.socialMedia.map((social, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {social}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-1">Niche/Category</p>
                  <p className="text-sm">{app.niche}</p>
                </div>

                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-sm font-medium text-blue-800 mb-1">Why they want to join</p>
                  <p className="text-sm">{app.reason}</p>
                </div>

                {app.notes && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm font-medium text-yellow-800 mb-1">Review Notes</p>
                    <p className="text-sm">{app.notes}</p>
                  </div>
                )}

                <div className="flex gap-2 pt-2 border-t mt-2">
                  {app.status === "pending" && (
                    <>
                      <Button size="sm" variant="outline" className="text-green-600 hover:text-green-700">
                        <CheckCircle2 className="h-4 w-4 mr-2" />
                        Approve
                      </Button>
                      <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                        <XCircle className="h-4 w-4 mr-2" />
                        Reject
                      </Button>
                    </>
                  )}
                  <Button size="sm" variant="outline">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Send Message
                  </Button>
                  <Button size="sm" variant="outline">
                    <User className="h-4 w-4 mr-2" />
                    View Profile
                  </Button>
                  <Button size="sm" variant="ghost">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        {filteredApplications.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Tidak ada aplikasi ditemukan</h3>
              <p className="text-muted-foreground mb-4">
                Tidak ada aplikasi afiliasi yang cocok dengan filter Anda
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
} 