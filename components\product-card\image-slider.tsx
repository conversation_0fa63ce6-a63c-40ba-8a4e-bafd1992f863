"use client"

import { useState, useEffect } from "react"
import Image from "next/image"

interface ImageSliderProps {
  images: { src: string; alt: string }[]
}

export const ImageSlider = ({ images }: ImageSliderProps) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLoaded, setIsLoaded] = useState<boolean[]>(Array(images.length).fill(false))

  useEffect(() => {
    if (images.length <= 1) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [images.length])

  const handleDotClick = (index: number) => {
    setCurrentIndex(index)
  }

  const handleImageLoad = (index: number) => {
    setIsLoaded((prev) => {
      const newState = [...prev]
      newState[index] = true
      return newState
    })
  }

  return (
    <div className="relative pt-[100%] overflow-hidden bg-white border-b border-[#f2f2f2]">
      {images.map((image, index) => (
        <div
          key={index}
          className={`absolute top-0 left-0 w-full h-full transition-opacity duration-300 ${
            currentIndex === index ? "opacity-100 z-10" : "opacity-0 z-0"
          }`}
        >
          <Image
            src={image.src || "/placeholder.svg"}
            alt={image.alt}
            fill
            className={`object-contain p-0.5 transition-opacity duration-300 ${
              isLoaded[index] ? "opacity-100" : "opacity-0"
            }`}
            onLoad={() => handleImageLoad(index)}
            sizes="(max-width: 768px) 50vw, 33vw"
          />
          {!isLoaded[index] && <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>}
        </div>
      ))}

      {images.length > 1 && (
        <div className="absolute bottom-1 left-0 right-0 z-20 flex justify-center">
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => handleDotClick(index)}
              className={`w-1.5 h-1.5 rounded-full mx-0.5 ${currentIndex === index ? "bg-[#ee4d2d]" : "bg-gray-300"}`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  )
}
