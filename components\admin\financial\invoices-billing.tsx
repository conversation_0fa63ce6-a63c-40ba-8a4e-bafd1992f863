"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Search, Download, Filter, Plus, CheckCircle, AlertCircle, Clock, FileText } from "lucide-react"

// Mock data for invoices
const invoices = [
  {
    id: "INV-12345",
    tenant: "Digital Marketplace",
    amount: "$1,250.00",
    status: "Paid",
    dueDate: "2023-10-15",
    issueDate: "2023-10-01",
    type: "Subscription",
  },
  {
    id: "INV-12346",
    tenant: "Style Hub",
    amount: "$890.00",
    status: "Pending",
    dueDate: "2023-10-20",
    issueDate: "2023-10-05",
    type: "Transaction Fees",
  },
  {
    id: "INV-12347",
    tenant: "Living Space",
    amount: "$2,100.00",
    status: "Overdue",
    dueDate: "2023-10-10",
    issueDate: "2023-09-25",
    type: "Subscription",
  },
  {
    id: "INV-12348",
    tenant: "Food Paradise",
    amount: "$450.00",
    status: "Paid",
    dueDate: "2023-10-12",
    issueDate: "2023-09-28",
    type: "Transaction Fees",
  },
  {
    id: "INV-12349",
    tenant: "Health Zone",
    amount: "$780.00",
    status: "Pending",
    dueDate: "2023-10-25",
    issueDate: "2023-10-10",
    type: "Subscription",
  },
  {
    id: "INV-12350",
    tenant: "Knowledge Hub",
    amount: "$320.00",
    status: "Paid",
    dueDate: "2023-10-08",
    issueDate: "2023-09-23",
    type: "Transaction Fees",
  },
  {
    id: "INV-12351",
    tenant: "Digital Marketplace",
    amount: "$1,800.00",
    status: "Pending",
    dueDate: "2023-10-30",
    issueDate: "2023-10-15",
    type: "Premium Features",
  },
  {
    id: "INV-12352",
    tenant: "Glamour World",
    amount: "$560.00",
    status: "Overdue",
    dueDate: "2023-10-05",
    issueDate: "2023-09-20",
    type: "Subscription",
  },
  {
    id: "INV-12353",
    tenant: "Animal Kingdom",
    amount: "$420.00",
    status: "Paid",
    dueDate: "2023-10-18",
    issueDate: "2023-10-03",
    type: "Transaction Fees",
  },
  {
    id: "INV-12354",
    tenant: "Kids Zone",
    amount: "$650.00",
    status: "Pending",
    dueDate: "2023-10-28",
    issueDate: "2023-10-13",
    type: "Subscription",
  },
]

// Mock data for subscription renewals
const subscriptionRenewals = [
  {
    id: "SUB-12345",
    tenant: "Digital Marketplace",
    plan: "Enterprise",
    amount: "$1,250.00/month",
    renewalDate: "2023-11-01",
    status: "Active",
    autoRenew: true,
  },
  {
    id: "SUB-12346",
    tenant: "Style Hub",
    plan: "Professional",
    amount: "$890.00/month",
    renewalDate: "2023-11-05",
    status: "Active",
    autoRenew: true,
  },
  {
    id: "SUB-12347",
    tenant: "Living Space",
    plan: "Enterprise",
    amount: "$2,100.00/month",
    renewalDate: "2023-11-10",
    status: "Active",
    autoRenew: false,
  },
  {
    id: "SUB-12348",
    tenant: "Food Paradise",
    plan: "Basic",
    amount: "$450.00/month",
    renewalDate: "2023-11-12",
    status: "Active",
    autoRenew: true,
  },
  {
    id: "SUB-12349",
    tenant: "Health Zone",
    plan: "Professional",
    amount: "$780.00/month",
    renewalDate: "2023-11-25",
    status: "Active",
    autoRenew: true,
  },
]

// Mock data for dunning management
const dunningManagement = [
  {
    id: "DUN-12345",
    tenant: "Living Space",
    invoiceId: "INV-12347",
    amount: "$2,100.00",
    dueDate: "2023-10-10",
    status: "First Reminder Sent",
    daysPastDue: 5,
  },
  {
    id: "DUN-12346",
    tenant: "Glamour World",
    invoiceId: "INV-12352",
    amount: "$560.00",
    dueDate: "2023-10-05",
    status: "Second Reminder Sent",
    daysPastDue: 10,
  },
  {
    id: "DUN-12347",
    tenant: "Tech Solutions",
    invoiceId: "INV-12330",
    amount: "$1,450.00",
    dueDate: "2023-09-30",
    status: "Final Notice Sent",
    daysPastDue: 15,
  },
  {
    id: "DUN-12348",
    tenant: "Outdoor Adventures",
    invoiceId: "INV-12325",
    amount: "$920.00",
    dueDate: "2023-09-25",
    status: "Account Suspended",
    daysPastDue: 20,
  },
]

// Helper function to get badge variant based on status
function getStatusBadge(status: string) {
  switch (status) {
    case "Paid":
      return { variant: "success" as const, icon: CheckCircle }
    case "Pending":
      return { variant: "outline" as const, icon: Clock }
    case "Overdue":
      return { variant: "destructive" as const, icon: AlertCircle }
    case "Active":
      return { variant: "success" as const, icon: CheckCircle }
    case "First Reminder Sent":
    case "Second Reminder Sent":
      return { variant: "warning" as const, icon: AlertCircle }
    case "Final Notice Sent":
    case "Account Suspended":
      return { variant: "destructive" as const, icon: AlertCircle }
    default:
      return { variant: "outline" as const, icon: null }
  }
}

export function InvoicesBilling() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Filter invoices based on search term and status
  const filteredInvoices = invoices.filter(
    (invoice) =>
      (invoice.tenant.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.id.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (statusFilter === "all" || invoice.status === statusFilter),
  )

  return (
    <div className="grid gap-6">
      <Tabs defaultValue="invoices" className="w-full">
        <TabsList className="grid w-full grid-cols-3 lg:w-[600px]">
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscription Renewals</TabsTrigger>
          <TabsTrigger value="dunning">Dunning Management</TabsTrigger>
        </TabsList>
        <TabsContent value="invoices" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div>
                  <CardTitle>Invoice Management</CardTitle>
                  <CardDescription>Manage invoices for tenants and stores</CardDescription>
                </div>
                <div className="flex flex-col gap-2 sm:flex-row">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search invoices..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select defaultValue={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="Paid">Paid</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="Overdue">Overdue</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    New Invoice
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Invoice Number</TableHead>
                      <TableHead>Tenant</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Issue Date</TableHead>
                      <TableHead className="text-right">Due Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredInvoices.map((invoice) => {
                      const { variant, icon: StatusIcon } = getStatusBadge(invoice.status)
                      return (
                        <TableRow key={invoice.id}>
                          <TableCell className="font-medium">{invoice.id}</TableCell>
                          <TableCell>{invoice.tenant}</TableCell>
                          <TableCell>{invoice.type}</TableCell>
                          <TableCell className="text-right">{invoice.amount}</TableCell>
                          <TableCell>
                            <Badge variant={variant} className="flex w-24 justify-center">
                              {StatusIcon && <StatusIcon className="mr-1 h-3 w-3" />}
                              {invoice.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{invoice.issueDate}</TableCell>
                          <TableCell className="text-right">{invoice.dueDate}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="ghost" size="sm">
                                <FileText className="mr-1 h-3 w-3" />
                                View
                              </Button>
                              <Button variant="ghost" size="sm">
                                Send
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
              <div className="flex items-center justify-end space-x-2 py-4">
                <Button variant="outline" size="sm">
                  Previous
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="subscriptions" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle>Subscription Renewals</CardTitle>
                  <CardDescription>Manage subscription renewals for tenants</CardDescription>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  New Subscription
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Subscription ID</TableHead>
                      <TableHead>Tenant</TableHead>
                      <TableHead>Plan</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-center">Auto-Renew</TableHead>
                      <TableHead className="text-right">Renewal Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {subscriptionRenewals.map((subscription) => {
                      const { variant, icon: StatusIcon } = getStatusBadge(subscription.status)
                      return (
                        <TableRow key={subscription.id}>
                          <TableCell className="font-medium">{subscription.id}</TableCell>
                          <TableCell>{subscription.tenant}</TableCell>
                          <TableCell>{subscription.plan}</TableCell>
                          <TableCell className="text-right">{subscription.amount}</TableCell>
                          <TableCell>
                            <Badge variant={variant} className="flex w-24 justify-center">
                              {StatusIcon && <StatusIcon className="mr-1 h-3 w-3" />}
                              {subscription.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">
                            {subscription.autoRenew ? (
                              <Badge variant="outline" className="bg-green-50">
                                Yes
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-red-50">
                                No
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">{subscription.renewalDate}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="ghost" size="sm">
                                Edit
                              </Button>
                              <Button variant="ghost" size="sm">
                                Cancel
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="dunning" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle>Dunning Management</CardTitle>
                  <CardDescription>Manage overdue invoices and payment reminders</CardDescription>
                </div>
                <Button>Run Dunning Process</Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Dunning ID</TableHead>
                      <TableHead>Tenant</TableHead>
                      <TableHead>Invoice</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-center">Days Past Due</TableHead>
                      <TableHead className="text-right">Due Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dunningManagement.map((dunning) => {
                      const { variant, icon: StatusIcon } = getStatusBadge(dunning.status)
                      return (
                        <TableRow key={dunning.id}>
                          <TableCell className="font-medium">{dunning.id}</TableCell>
                          <TableCell>{dunning.tenant}</TableCell>
                          <TableCell>{dunning.invoiceId}</TableCell>
                          <TableCell className="text-right">{dunning.amount}</TableCell>
                          <TableCell>
                            <Badge variant={variant} className="flex w-40 justify-center">
                              {StatusIcon && <StatusIcon className="mr-1 h-3 w-3" />}
                              {dunning.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex flex-col items-center gap-1">
                              <span>{dunning.daysPastDue}</span>
                              <Progress
                                value={Math.min(dunning.daysPastDue * 5, 100)}
                                className="h-1 w-16"
                                indicatorClassName={
                                  dunning.daysPastDue > 15
                                    ? "bg-red-500"
                                    : dunning.daysPastDue > 7
                                      ? "bg-amber-500"
                                      : "bg-blue-500"
                                }
                              />
                            </div>
                          </TableCell>
                          <TableCell className="text-right">{dunning.dueDate}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="ghost" size="sm">
                                Send Reminder
                              </Button>
                              <Button variant="ghost" size="sm">
                                View
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                Last dunning process run: <span className="font-medium">2023-10-15 08:00 AM</span>
              </div>
              <div className="flex gap-2">
                <Button variant="outline">Configure Dunning</Button>
                <Button variant="outline">View Logs</Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
