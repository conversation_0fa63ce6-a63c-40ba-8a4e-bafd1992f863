"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  FileText,
  Image as ImageIcon,
  Link as LinkIcon,
  Copy,
  Download,
  Eye,
  PencilIcon,
  Trash,
  MoreHorizontal,
  BarChart2,
  Calendar,
  Plus,
  FileCode,
  Code,
  Video,
  Users
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk materi afiliasi
const affiliateMaterials = [
  {
    id: "mat-001",
    title: "Banner Promo Utama",
    type: "image",
    format: "JPG, PNG",
    category: "Banner",
    url: "/assets/affiliates/banner-promo-1.jpg",
    thumbnailUrl: "/api/placeholder/400/200",
    dimensions: "1200x628px",
    downloadCount: 78,
    clickCount: 450,
    conversionCount: 25,
    dateCreated: "2024-01-05T10:00:00",
    description: "Banner promosi utama untuk halaman depan website."
  },
  {
    id: "mat-002",
    title: "Kode Referral Tracking",
    type: "code",
    format: "HTML, JavaScript",
    category: "Tracking Code",
    url: "/assets/affiliates/tracking-code.js",
    downloadCount: 45,
    clickCount: 0,
    conversionCount: 0,
    dateCreated: "2024-01-06T11:30:00",
    description: "Kode JavaScript untuk melacak konversi pengunjung dari afiliasi."
  },
  {
    id: "mat-003",
    title: "Banner Side Promo",
    type: "image",
    format: "JPG, PNG",
    category: "Banner",
    url: "/assets/affiliates/side-banner.jpg",
    thumbnailUrl: "/api/placeholder/200/400",
    dimensions: "300x600px",
    downloadCount: 65,
    clickCount: 320,
    conversionCount: 18,
    dateCreated: "2024-01-07T14:20:00",
    description: "Banner untuk sidebar website. Cocok untuk ditampilkan di bagian samping konten."
  },
  {
    id: "mat-004",
    title: "Video Review Template",
    type: "video",
    format: "MP4",
    category: "Video",
    url: "/assets/affiliates/review-template.mp4",
    thumbnailUrl: "/api/placeholder/400/225",
    duration: "1:30",
    downloadCount: 32,
    clickCount: 180,
    conversionCount: 15,
    dateCreated: "2024-01-10T09:45:00",
    description: "Template video untuk digunakan dalam ulasan produk oleh afiliasi."
  },
  {
    id: "mat-005",
    title: "Contoh Artikel Review",
    type: "document",
    format: "PDF, DOCX",
    category: "Content",
    url: "/assets/affiliates/article-template.pdf",
    downloadCount: 53,
    clickCount: 0,
    conversionCount: 0,
    dateCreated: "2024-01-12T16:15:00",
    description: "Template artikel untuk review produk yang dapat diadaptasi oleh afiliasi."
  },
  {
    id: "mat-006",
    title: "Link Tracking Generator",
    type: "tool",
    format: "Web Tool",
    category: "Tool",
    url: "/affiliates/tools/link-generator",
    downloadCount: 0,
    clickCount: 210,
    conversionCount: 0,
    dateCreated: "2024-01-15T13:30:00",
    description: "Alat untuk membuat link tracking kustom untuk setiap afiliasi."
  }
]

// Fungsi untuk mendapatkan ikon berdasarkan tipe
function getTypeIcon(type: string) {
  switch (type) {
    case "image":
      return <ImageIcon className="h-6 w-6 text-blue-600" />
    case "code":
      return <Code className="h-6 w-6 text-purple-600" />
    case "document":
      return <FileText className="h-6 w-6 text-green-600" />
    case "video":
      return <Video className="h-6 w-6 text-red-600" />
    case "tool":
      return <BarChart2 className="h-6 w-6 text-yellow-600" />
    default:
      return <FileText className="h-6 w-6" />
  }
}

// Fungsi untuk mendapatkan badge tipe
function getTypeBadge(type: string) {
  switch (type) {
    case "image":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">Image</Badge>
    case "code":
      return <Badge variant="outline" className="bg-purple-100 text-purple-800">Code</Badge>
    case "document":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Document</Badge>
    case "video":
      return <Badge variant="outline" className="bg-red-100 text-red-800">Video</Badge>
    case "tool":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Tool</Badge>
    default:
      return <Badge variant="outline">{type}</Badge>
  }
}

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric'
  })
}

export default function AffiliateMaterialsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")

  // Mendapatkan kategori unik
  const categories = [...new Set(affiliateMaterials.map(mat => mat.category))]

  // Filter materi
  const filteredMaterials = affiliateMaterials.filter(material => {
    const matchesSearch = material.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          material.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === "all" || material.type === typeFilter
    const matchesCategory = categoryFilter === "all" || material.category === categoryFilter
    return matchesSearch && matchesType && matchesCategory
  })

  // Statistik
  const stats = {
    totalMaterials: affiliateMaterials.length,
    totalDownloads: affiliateMaterials.reduce((sum, m) => sum + m.downloadCount, 0),
    totalClicks: affiliateMaterials.reduce((sum, m) => sum + m.clickCount, 0),
    totalConversions: affiliateMaterials.reduce((sum, m) => sum + m.conversionCount, 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/affiliates">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Affiliate Materials</h1>
            <p className="text-muted-foreground">
              Kelola banner, kode, dan konten untuk program afiliasi
            </p>
          </div>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Upload Material
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Materials</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalMaterials}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Downloads</CardTitle>
            <Download className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.totalDownloads}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
            <Eye className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.totalClicks}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversions</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.totalConversions}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari materi afiliasi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Tipe</option>
              <option value="image">Image</option>
              <option value="code">Code</option>
              <option value="document">Document</option>
              <option value="video">Video</option>
              <option value="tool">Tool</option>
            </select>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Kategori</option>
              {categories.map((category, index) => (
                <option key={index} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Materials Grid */}
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
        {filteredMaterials.map((material) => (
          <Card key={material.id} className="overflow-hidden">
            {material.thumbnailUrl && (
              <div className="relative aspect-video bg-muted/50">
                <img 
                  src={material.thumbnailUrl}
                  alt={material.title}
                  className="object-cover w-full h-full"
                />
                <div className="absolute top-2 right-2">
                  {getTypeBadge(material.type)}
                </div>
              </div>
            )}
            <CardHeader className={material.thumbnailUrl ? "pt-3" : ""}>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  {!material.thumbnailUrl && getTypeIcon(material.type)}
                  <CardTitle className="text-base">{material.title}</CardTitle>
                </div>
              </div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                <Calendar className="h-3.5 w-3.5" />
                <span>{formatDate(material.dateCreated)}</span>
              </div>
            </CardHeader>
            <CardContent className="text-sm">
              <p className="line-clamp-2 text-muted-foreground">
                {material.description}
              </p>
              <div className="flex flex-wrap gap-2 mt-2">
                <div className="text-xs bg-muted/70 px-2 py-1 rounded">
                  Format: {material.format}
                </div>
                {material.dimensions && (
                  <div className="text-xs bg-muted/70 px-2 py-1 rounded">
                    {material.dimensions}
                  </div>
                )}
                {material.duration && (
                  <div className="text-xs bg-muted/70 px-2 py-1 rounded">
                    {material.duration}
                  </div>
                )}
              </div>
              <div className="grid grid-cols-3 gap-2 mt-3">
                <div className="text-center">
                  <div className="text-xs text-muted-foreground">Downloads</div>
                  <div className="font-medium">{material.downloadCount}</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-muted-foreground">Clicks</div>
                  <div className="font-medium">{material.clickCount}</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-muted-foreground">Conversions</div>
                  <div className="font-medium">{material.conversionCount}</div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t pt-4 flex gap-2">
              <Button size="sm" variant="outline" className="flex-1">
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button size="sm" variant="outline" className="flex-1">
                <Copy className="h-4 w-4 mr-2" />
                Copy Link
              </Button>
              <Button size="sm" variant="ghost">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {filteredMaterials.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Tidak ada materi ditemukan</h3>
            <p className="text-muted-foreground mb-4">
              Tidak ada materi afiliasi yang cocok dengan filter Anda
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Upload Material
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 