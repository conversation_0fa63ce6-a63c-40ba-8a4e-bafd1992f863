import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

export function AffiliateCustomerInsights() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Customer Insights</CardTitle>
        <CardDescription>Understand your audience and their purchasing behavior</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="demographics">Demographics</TabsTrigger>
            <TabsTrigger value="behavior">Behavior</TabsTrigger>
            <TabsTrigger value="loyalty">Loyalty</TabsTrigger>
          </TabsList>
          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">New vs Returning Customers</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                    <p className="text-sm text-gray-500 dark:text-gray-400">New vs returning customers chart</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Average Order Value</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Average order value chart</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Purchase Frequency</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Purchase frequency chart</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Customer Lifetime Value</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Customer lifetime value chart</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="demographics">
            <div className="flex items-center justify-center h-[400px] bg-gray-100 dark:bg-gray-800 rounded-md mt-4">
              <p className="text-gray-500 dark:text-gray-400">Demographics data visualization</p>
            </div>
          </TabsContent>
          <TabsContent value="behavior">
            <div className="flex items-center justify-center h-[400px] bg-gray-100 dark:bg-gray-800 rounded-md mt-4">
              <p className="text-gray-500 dark:text-gray-400">Behavior data visualization</p>
            </div>
          </TabsContent>
          <TabsContent value="loyalty">
            <div className="flex items-center justify-center h-[400px] bg-gray-100 dark:bg-gray-800 rounded-md mt-4">
              <p className="text-gray-500 dark:text-gray-400">Loyalty data visualization</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
