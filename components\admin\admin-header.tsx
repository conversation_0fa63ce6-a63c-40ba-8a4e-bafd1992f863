"use client"

import { useState } from "react"
import Link from "next/link"
import { Bell, Search, Settings, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { UserMenu } from "@/components/dashboard/user-menu"
import { Badge } from "@/components/ui/badge"
import { ModeToggle } from "@/components/mode-toggle"
import { RoleSwitcher } from "@/components/role-switcher"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"

export function AdminHeader() {
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  // Mock search results
  const searchResults = {
    users: [
      { id: "1", name: "<PERSON>", type: "Admin" },
      { id: "2", name: "<PERSON>", type: "Tenant Admin" },
    ],
    tenants: [
      { id: "1", name: "Acme Corp", type: "Enterprise" },
      { id: "2", name: "Startup Inc", type: "Startup" },
    ],
    stores: [
      { id: "1", name: "Fashion Store", type: "Clothing" },
      { id: "2", name: "Tech Gadgets", type: "Electronics" },
    ],
    products: [
      { id: "1", name: "Smartphone X", type: "Electronics" },
      { id: "2", name: "Designer Shirt", type: "Clothing" },
    ],
  }

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center justify-between border-b border-border/20 bg-background/80 px-6 backdrop-blur-md supports-[backdrop-filter]:bg-background/20">
      <div className="flex items-center gap-2">
        <Link href="/admin/dashboard" className="flex items-center gap-2">
          <div className="text-primary flex h-8 w-8 items-center justify-center rounded-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="glow-text-primary"
            >
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
            </svg>
          </div>
          <span className="font-bold tracking-tight text-primary glow-text-primary">SELLZIO OS</span>
        </Link>
      </div>

      <div className="flex-1 max-w-md mx-auto">
        <Dialog open={isSearchOpen} onOpenChange={setIsSearchOpen}>
          <DialogTrigger asChild>
            <div className="relative w-full cursor-pointer">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search systems..."
                className="w-full bg-background/30 border-border/30 pl-10 pr-4 h-9 rounded-full focus-visible:ring-primary/20"
                onClick={() => setIsSearchOpen(true)}
                readOnly
              />
            </div>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Global Search</DialogTitle>
            </DialogHeader>
            <div className="flex items-center border rounded-md">
              <Search className="ml-2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search for users, tenants, stores, products..."
                className="border-0 focus-visible:ring-0"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                autoFocus
              />
              {searchQuery && (
                <Button variant="ghost" size="icon" className="mr-1" onClick={() => setSearchQuery("")}>
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            {searchQuery && (
              <Tabs defaultValue="all">
                <TabsList className="w-full">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="users">Users</TabsTrigger>
                  <TabsTrigger value="tenants">Tenants</TabsTrigger>
                  <TabsTrigger value="stores">Stores</TabsTrigger>
                  <TabsTrigger value="products">Products</TabsTrigger>
                </TabsList>
                <ScrollArea className="h-[300px] mt-2">
                  <TabsContent value="all" className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium mb-2">Users</h3>
                      <div className="space-y-2">
                        {searchResults.users.map((result) => (
                          <Link
                            key={result.id}
                            href={`/admin/dashboard/users/${result.id}`}
                            className="flex items-center justify-between p-2 rounded-md hover:bg-accent"
                            onClick={() => setIsSearchOpen(false)}
                          >
                            <div className="flex items-center gap-2">
                              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                {result.name.charAt(0)}
                              </div>
                              <div>
                                <p className="text-sm font-medium">{result.name}</p>
                                <p className="text-xs text-muted-foreground">{result.type}</p>
                              </div>
                            </div>
                            <Badge variant="outline">{result.type}</Badge>
                          </Link>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-2">Tenants</h3>
                      <div className="space-y-2">
                        {searchResults.tenants.map((result) => (
                          <Link
                            key={result.id}
                            href={`/admin/dashboard/tenants/${result.id}`}
                            className="flex items-center justify-between p-2 rounded-md hover:bg-accent"
                            onClick={() => setIsSearchOpen(false)}
                          >
                            <div className="flex items-center gap-2">
                              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                {result.name.charAt(0)}
                              </div>
                              <div>
                                <p className="text-sm font-medium">{result.name}</p>
                                <p className="text-xs text-muted-foreground">{result.type}</p>
                              </div>
                            </div>
                            <Badge variant="outline">{result.type}</Badge>
                          </Link>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-2">Stores</h3>
                      <div className="space-y-2">
                        {searchResults.stores.map((result) => (
                          <Link
                            key={result.id}
                            href={`/admin/dashboard/stores/${result.id}`}
                            className="flex items-center justify-between p-2 rounded-md hover:bg-accent"
                            onClick={() => setIsSearchOpen(false)}
                          >
                            <div className="flex items-center gap-2">
                              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                {result.name.charAt(0)}
                              </div>
                              <div>
                                <p className="text-sm font-medium">{result.name}</p>
                                <p className="text-xs text-muted-foreground">{result.type}</p>
                              </div>
                            </div>
                            <Badge variant="outline">{result.type}</Badge>
                          </Link>
                        ))}
                      </div>
                    </div>
                  </TabsContent>
                  <TabsContent value="users">
                    <div className="space-y-2">
                      {searchResults.users.map((result) => (
                        <Link
                          key={result.id}
                          href={`/admin/dashboard/users/${result.id}`}
                          className="flex items-center justify-between p-2 rounded-md hover:bg-accent"
                          onClick={() => setIsSearchOpen(false)}
                        >
                          <div className="flex items-center gap-2">
                            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                              {result.name.charAt(0)}
                            </div>
                            <div>
                              <p className="text-sm font-medium">{result.name}</p>
                              <p className="text-xs text-muted-foreground">{result.type}</p>
                            </div>
                          </div>
                          <Badge variant="outline">{result.type}</Badge>
                        </Link>
                      ))}
                    </div>
                  </TabsContent>
                  {/* Similar content for other tabs */}
                </ScrollArea>
              </Tabs>
            )}
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center gap-4">
        <ModeToggle />
        <RoleSwitcher />
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          <Badge className="absolute -right-1 -top-1 h-4 w-4 p-0 flex items-center justify-center text-[10px]">3</Badge>
        </Button>
        <Button variant="ghost" size="icon">
          <Settings className="h-5 w-5" />
        </Button>
        <UserMenu />
      </div>
    </header>
  )
}
