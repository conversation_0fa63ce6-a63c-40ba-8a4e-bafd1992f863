"use client"

import { useState } from "react"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent } from "@/components/ui/card"
import { EventCard } from "./event-card"
import { mockEvents } from "./mock-data"

interface EventCalendarViewProps {
  searchQuery: string
  selectedCategories: string[]
}

export function EventCalendarView({ searchQuery, selectedCategories }: EventCalendarViewProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())

  // Filter events berdasarkan pencarian dan kategori
  const filteredEvents = mockEvents.filter((event) => {
    const matchesSearch = searchQuery
      ? event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.description.toLowerCase().includes(searchQuery.toLowerCase())
      : true

    const matchesCategory = selectedCategories.length > 0 ? selectedCategories.includes(event.category) : true

    return matchesSearch && matchesCategory
  })

  // Dapatkan events untuk tanggal yang dipilih
  const eventsOnSelectedDate = selectedDate
    ? filteredEvents.filter((event) => {
        const eventDate = new Date(event.date)
        return (
          eventDate.getDate() === selectedDate.getDate() &&
          eventDate.getMonth() === selectedDate.getMonth() &&
          eventDate.getFullYear() === selectedDate.getFullYear()
        )
      })
    : []

  // Dapatkan tanggal yang memiliki events
  const eventDates = filteredEvents.map((event) => new Date(event.date))

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-7">
      <Card className="lg:col-span-3">
        <CardContent className="p-3">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={setSelectedDate}
            className="rounded-md border"
            modifiers={{
              hasEvent: (date) =>
                eventDates.some(
                  (eventDate) =>
                    eventDate.getDate() === date.getDate() &&
                    eventDate.getMonth() === date.getMonth() &&
                    eventDate.getFullYear() === date.getFullYear(),
                ),
            }}
            modifiersStyles={{
              hasEvent: {
                fontWeight: "bold",
                backgroundColor: "hsl(var(--primary) / 0.1)",
                color: "hsl(var(--primary))",
              },
            }}
          />
        </CardContent>
      </Card>

      <div className="lg:col-span-4">
        <h2 className="mb-4 text-xl font-semibold">
          {selectedDate ? (
            <>
              Events pada{" "}
              {selectedDate.toLocaleDateString("id-ID", {
                weekday: "long",
                day: "numeric",
                month: "long",
                year: "numeric",
              })}
            </>
          ) : (
            "Pilih tanggal untuk melihat events"
          )}
        </h2>

        {eventsOnSelectedDate.length > 0 ? (
          <div className="space-y-4">
            {eventsOnSelectedDate.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center p-6">
              <p className="text-center text-muted-foreground">Tidak ada event pada tanggal ini</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
