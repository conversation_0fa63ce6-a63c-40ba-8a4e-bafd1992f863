import axios from "axios"
import type {
  AnalyticsDashboard,
  AnalyticsFilter,
  AnalyticsExportOptions,
  AnalyticsEvent,
} from "@/lib/models/analytics"

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"

// Fungsi untuk mendapatkan token dari localStorage
const getToken = () => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("token")
  }
  return null
}

// Fungsi untuk membuat header dengan token
const getHeaders = () => {
  const token = getToken()
  return {
    headers: {
      Authorization: token ? `Bearer ${token}` : "",
    },
  }
}

export const analyticsAPI = {
  // Mendapatkan dashboard analitik berdasarkan role dan ID
  getDashboard: async (
    role: "admin" | "tenant" | "store" | "buyer",
    id?: string,
    filter?: AnalyticsFilter,
  ): Promise<AnalyticsDashboard> => {
    let url = `${API_URL}/analytics/dashboard/${role}`
    if (id) {
      url += `/${id}`
    }

    const response = await axios.post(url, { filter }, getHeaders())
    return response.data
  },

  // Mendapatkan data metrik tertentu
  getMetric: async (metricId: string, filter?: AnalyticsFilter): Promise<any> => {
    const response = await axios.post(`${API_URL}/analytics/metrics/${metricId}`, { filter }, getHeaders())
    return response.data
  },

  // Mendapatkan data time series tertentu
  getTimeSeries: async (timeSeriesId: string, filter?: AnalyticsFilter): Promise<any> => {
    const response = await axios.post(`${API_URL}/analytics/time-series/${timeSeriesId}`, { filter }, getHeaders())
    return response.data
  },

  // Mendapatkan data pie chart tertentu
  getPieData: async (pieId: string, filter?: AnalyticsFilter): Promise<any> => {
    const response = await axios.post(`${API_URL}/analytics/pie/${pieId}`, { filter }, getHeaders())
    return response.data
  },

  // Mendapatkan data tabel tertentu
  getTableData: async (tableId: string, filter?: AnalyticsFilter): Promise<any> => {
    const response = await axios.post(`${API_URL}/analytics/table/${tableId}`, { filter }, getHeaders())
    return response.data
  },

  // Ekspor data analitik
  exportData: async (options: AnalyticsExportOptions, filter?: AnalyticsFilter): Promise<Blob> => {
    const response = await axios.post(
      `${API_URL}/analytics/export`,
      { options, filter },
      {
        ...getHeaders(),
        responseType: "blob",
      },
    )
    return response.data
  },

  // Melacak event analitik
  trackEvent: async (event: Omit<AnalyticsEvent, "id" | "timestamp">): Promise<void> => {
    await axios.post(`${API_URL}/analytics/events`, event, getHeaders())
  },

  // Mendapatkan laporan kustom
  getCustomReport: async (reportConfig: any, filter?: AnalyticsFilter): Promise<any> => {
    const response = await axios.post(`${API_URL}/analytics/custom-report`, { reportConfig, filter }, getHeaders())
    return response.data
  },
}
