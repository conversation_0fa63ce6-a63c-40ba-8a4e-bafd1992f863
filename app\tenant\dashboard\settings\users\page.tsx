"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  ArrowLeft,
  Search,
  Plus,
  MoreHorizontal,
  Mail,
  Lock,
  UserPlus,
  UserCog,
  UserX,
  Users,
  ShieldCheck,
  Pencil,
  Check,
  X,
  Clock,
  AlertCircle,
  Calendar,
  RefreshCw
} from "lucide-react"

export default function UsersPage() {
  const [activeTab, setActiveTab] = useState("members")
  const [searchQuery, setSearchQuery] = useState("")
  const [inviteEmail, setInviteEmail] = useState("")

  // Dummy data untuk anggota tim
  const teamMembers = [
    {
      id: "user-1",
      name: "Budi Santoso",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
      lastActive: "2024-05-22T14:30:00",
      avatar: "/avatars/budi.jpg",
      dateJoined: "2023-10-15T09:00:00"
    },
    {
      id: "user-2",
      name: "Siti Nurhaliza",
      email: "<EMAIL>",
      role: "manager",
      status: "active",
      lastActive: "2024-05-21T16:45:00",
      avatar: "/avatars/siti.jpg",
      dateJoined: "2023-11-20T10:30:00"
    },
    {
      id: "user-3",
      name: "Hendro Wijaya",
      email: "<EMAIL>",
      role: "editor",
      status: "active",
      lastActive: "2024-05-20T11:20:00",
      avatar: "/avatars/hendro.jpg",
      dateJoined: "2024-01-10T13:45:00"
    },
    {
      id: "user-4",
      name: "Anita Dewi",
      email: "<EMAIL>",
      role: "customer-service",
      status: "inactive",
      lastActive: "2024-05-01T09:15:00",
      avatar: "/avatars/anita.jpg",
      dateJoined: "2024-02-05T11:30:00"
    }
  ]

  // Dummy data untuk invitasi
  const pendingInvitations = [
    {
      id: "invite-1",
      email: "<EMAIL>",
      role: "editor",
      status: "pending",
      dateInvited: "2024-05-20T10:00:00",
      expiresOn: "2024-05-27T10:00:00",
      invitedBy: "Budi Santoso"
    },
    {
      id: "invite-2",
      email: "<EMAIL>",
      role: "customer-service",
      status: "expired",
      dateInvited: "2024-05-10T14:30:00",
      expiresOn: "2024-05-17T14:30:00",
      invitedBy: "Budi Santoso"
    }
  ]

  // Dummy data untuk roles
  const roles = [
    {
      id: "role-admin",
      name: "Admin",
      description: "Akses penuh ke semua fitur dan pengaturan",
      userCount: 1,
      permissions: ["all"]
    },
    {
      id: "role-manager",
      name: "Manajer",
      description: "Dapat mengelola produk, pesanan, dan konten",
      userCount: 1,
      permissions: ["manage-products", "manage-orders", "manage-content", "view-analytics"]
    },
    {
      id: "role-editor",
      name: "Editor",
      description: "Dapat mengedit konten dan produk",
      userCount: 1,
      permissions: ["edit-products", "edit-content", "view-analytics"]
    },
    {
      id: "role-customer-service",
      name: "Layanan Pelanggan",
      description: "Dapat mengelola pesanan dan dukungan pelanggan",
      userCount: 1,
      permissions: ["view-orders", "manage-support", "view-customers"]
    }
  ]

  // Format tanggal
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  // Status badge berdasarkan status user
  function getUserStatusBadge(status: string) {
    switch (status) {
      case "active":
        return <Badge variant="outline" className="bg-green-100 text-green-800"><Check className="h-3 w-3 mr-1" />Aktif</Badge>
      case "inactive":
        return <Badge variant="outline" className="bg-gray-100 text-gray-800"><X className="h-3 w-3 mr-1" />Tidak Aktif</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Status badge berdasarkan status invitation
  function getInviteStatusBadge(status: string) {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Menunggu</Badge>
      case "expired":
        return <Badge variant="outline" className="bg-red-100 text-red-800"><AlertCircle className="h-3 w-3 mr-1" />Kedaluwarsa</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Role badge berdasarkan role
  function getRoleBadge(role: string) {
    switch (role) {
      case "admin":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">Admin</Badge>
      case "manager":
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">Manajer</Badge>
      case "editor":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Editor</Badge>
      case "customer-service":
        return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200">Layanan Pelanggan</Badge>
      default:
        return <Badge variant="secondary">{role}</Badge>
    }
  }

  // Avatar fallback berdasarkan nama
  function getInitials(name: string) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  }

  // Filter team members berdasarkan pencarian
  const filteredMembers = teamMembers.filter(member => 
    member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.role.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/settings">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Pengaturan Pengguna</h1>
            <p className="text-muted-foreground">
              Kelola tim, peran, dan izin pengguna
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            Undang Anggota
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 md:w-auto">
          <TabsTrigger value="members">Anggota Tim</TabsTrigger>
          <TabsTrigger value="invitations">Undangan</TabsTrigger>
          <TabsTrigger value="roles">Peran & Izin</TabsTrigger>
        </TabsList>

        {/* Members Tab */}
        <TabsContent value="members" className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Cari anggota tim..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter berdasarkan peran" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Peran</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="manager">Manajer</SelectItem>
                <SelectItem value="editor">Editor</SelectItem>
                <SelectItem value="customer-service">Layanan Pelanggan</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Card>
            <CardHeader className="py-4">
              <div className="grid grid-cols-5 text-sm text-muted-foreground font-medium">
                <div className="col-span-2">Pengguna</div>
                <div>Peran</div>
                <div>Status</div>
                <div>Tindakan</div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y">
                {filteredMembers.map((member) => (
                  <div key={member.id} className="grid grid-cols-5 items-center py-4 px-6">
                    <div className="col-span-2 flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={member.avatar} alt={member.name} />
                        <AvatarFallback>{getInitials(member.name)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{member.name}</div>
                        <div className="text-sm text-muted-foreground">{member.email}</div>
                      </div>
                    </div>
                    <div>
                      {getRoleBadge(member.role)}
                    </div>
                    <div>
                      {getUserStatusBadge(member.status)}
                    </div>
                    <div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Opsi</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Tindakan</DropdownMenuLabel>
                          <DropdownMenuItem>
                            <Pencil className="h-4 w-4 mr-2" />
                            Edit Profil
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <UserCog className="h-4 w-4 mr-2" />
                            Ubah Peran
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Lock className="h-4 w-4 mr-2" />
                            Reset Password
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-destructive">
                            <UserX className="h-4 w-4 mr-2" />
                            Hapus Pengguna
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between py-4">
              <div className="text-sm text-muted-foreground">
                Menampilkan {filteredMembers.length} dari {teamMembers.length} anggota tim
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Invitations Tab */}
        <TabsContent value="invitations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Undang Anggota Tim</CardTitle>
              <CardDescription>
                Kirim undangan kepada orang untuk bergabung dengan tim Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label htmlFor="invite-email" className="text-sm font-medium">
                    Alamat Email
                  </label>
                  <Input
                    id="invite-email"
                    placeholder="<EMAIL>"
                    type="email"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="invite-role" className="text-sm font-medium">
                    Peran
                  </label>
                  <Select defaultValue="editor">
                    <SelectTrigger id="invite-role">
                      <SelectValue placeholder="Pilih peran" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="manager">Manajer</SelectItem>
                      <SelectItem value="editor">Editor</SelectItem>
                      <SelectItem value="customer-service">Layanan Pelanggan</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <label htmlFor="invite-message" className="text-sm font-medium">
                  Pesan Undangan (Opsional)
                </label>
                <Input
                  id="invite-message"
                  placeholder="Pesan singkat tentang mengapa Anda mengundang mereka"
                  onChange={() => {}}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <p className="text-sm text-muted-foreground">
                Undangan akan kedaluwarsa dalam 7 hari
              </p>
              <Button>
                <Mail className="h-4 w-4 mr-2" />
                Kirim Undangan
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Undangan yang Tertunda</CardTitle>
              <CardDescription>
                Undangan yang telah dikirim dan menunggu respons
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="divide-y">
                {pendingInvitations.map((invitation) => (
                  <div key={invitation.id} className="py-4 flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{invitation.email}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Diundang oleh {invitation.invitedBy} pada {formatDate(invitation.dateInvited)}
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex flex-col items-start md:items-center gap-1">
                        {getRoleBadge(invitation.role)}
                        {getInviteStatusBadge(invitation.status)}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        {invitation.status === "expired" ? (
                          <span>Kedaluwarsa pada {formatDate(invitation.expiresOn)}</span>
                        ) : (
                          <span>Kedaluwarsa dalam {Math.ceil((new Date(invitation.expiresOn).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} hari</span>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        {invitation.status === "pending" && (
                          <>
                            <Button variant="outline" size="sm">
                              <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                              Kirim Ulang
                            </Button>
                            <Button variant="outline" size="sm" className="text-destructive">
                              <X className="h-3.5 w-3.5 mr-1.5" />
                              Batalkan
                            </Button>
                          </>
                        )}
                        {invitation.status === "expired" && (
                          <Button variant="outline" size="sm">
                            <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                            Kirim Ulang
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {pendingInvitations.length === 0 && (
                  <div className="py-8 text-center text-muted-foreground">
                    Tidak ada undangan yang tertunda
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Roles Tab */}
        <TabsContent value="roles" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Peran dan Izin</CardTitle>
              <CardDescription>
                Kelola peran dan izin yang berbeda dalam tim Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4">
                {roles.map((role) => (
                  <div key={role.id} className="border rounded-lg p-4">
                    <div className="flex flex-col md:flex-row justify-between gap-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          {role.name === "Admin" ? (
                            <ShieldCheck className="h-4 w-4 text-blue-600" />
                          ) : role.name === "Manajer" ? (
                            <UserCog className="h-4 w-4 text-purple-600" />
                          ) : (
                            <Users className="h-4 w-4 text-primary" />
                          )}
                          <span className="font-medium">{role.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {role.userCount} pengguna
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {role.description}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Pencil className="h-3.5 w-3.5 mr-1.5" />
                          Edit Peran
                        </Button>
                        {role.name !== "Admin" && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm" className="text-destructive">
                                <X className="h-3.5 w-3.5 mr-1.5" />
                                Hapus
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Apakah Anda yakin?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Menghapus peran ini akan menghapus peran tersebut dari semua pengguna yang memilikinya.
                                  Tindakan ini tidak dapat dibatalkan.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Batal</AlertDialogCancel>
                                <AlertDialogAction className="bg-destructive text-destructive-foreground">
                                  Hapus Peran
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </div>
                    </div>
                    <Separator className="my-4" />
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Izin:</h4>
                      <div className="flex flex-wrap gap-2">
                        {role.permissions.map((permission, index) => (
                          <Badge key={index} variant="secondary">
                            {permission === "all" ? "Semua Izin" : permission}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Peran Baru
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 