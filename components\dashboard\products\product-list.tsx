"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON>, CardContent, Card<PERSON>ooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Edit, ExternalLink, Trash2 } from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { productsAPI, type Product } from "@/lib/api/products"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface ProductListProps {
  storeId?: string
}

export function ProductList({ storeId }: ProductListProps) {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [productToDelete, setProductToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const fetchProducts = async () => {
    try {
      const data = await productsAPI.getAll(storeId)
      setProducts(data)
      setLoading(false)
    } catch (err) {
      console.error("Error fetching products:", err)
      setError("Gagal memuat daftar produk. Silakan coba lagi.")
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProducts()
  }, [storeId])

  const handleDelete = async (id: string) => {
    setIsDeleting(true)
    try {
      await productsAPI.delete(id)
      setProducts(products.filter((product) => product.id !== id))
      setProductToDelete(null)
    } catch (err) {
      console.error("Error deleting product:", err)
      setError("Gagal menghapus produk. Silakan coba lagi.")
    } finally {
      setIsDeleting(false)
    }
  }

  if (loading) {
    return (
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <div className="h-48 bg-muted">
              <Skeleton className="h-full w-full" />
            </div>
            <CardContent className="p-6">
              <Skeleton className="h-6 w-2/3 mb-2" />
              <Skeleton className="h-4 w-full mb-4" />
              <Skeleton className="h-5 w-1/3" />
            </CardContent>
            <CardFooter className="flex justify-between p-6 pt-0">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </CardFooter>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (products.length === 0) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold mb-2">Belum ada produk</h3>
        <p className="text-muted-foreground mb-4">
          {storeId
            ? "Toko ini belum memiliki produk. Tambahkan produk pertama sekarang."
            : "Anda belum memiliki produk. Tambahkan produk pertama sekarang."}
        </p>
        <Button asChild>
          <Link href={storeId ? `/dashboard/products/create?storeId=${storeId}` : "/dashboard/products/create"}>
            Tambah Produk Baru
          </Link>
        </Button>
      </Card>
    )
  }

  return (
    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
      {products.map((product) => (
        <Card key={product.id} className="overflow-hidden">
          <div className="h-48 bg-muted flex items-center justify-center">
            {product.images && product.images.length > 0 ? (
              <img
                src={product.images[0] || "/placeholder.svg"}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="text-4xl font-bold text-muted-foreground">{product.name.charAt(0)}</div>
            )}
          </div>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-2">{product.name}</h3>
            <p className="text-muted-foreground text-sm line-clamp-2 mb-2">
              {product.description || "Tidak ada deskripsi"}
            </p>
            <p className="font-medium text-lg">{formatCurrency(product.price)}</p>
            {!storeId && product.store && (
              <p className="text-sm text-muted-foreground mt-2">Toko: {product.store.name}</p>
            )}
          </CardContent>
          <CardFooter className="flex justify-between p-6 pt-0">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/products/${product.id}`}>
                <ExternalLink className="mr-2 h-4 w-4" />
                Detail
              </Link>
            </Button>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/dashboard/products/${product.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </Button>
              <AlertDialog
                open={productToDelete === product.id}
                onOpenChange={(open) => !open && setProductToDelete(null)}
              >
                <AlertDialogTrigger asChild>
                  <Button variant="outline" size="sm" onClick={() => setProductToDelete(product.id)}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Hapus
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Hapus Produk</AlertDialogTitle>
                    <AlertDialogDescription>
                      Apakah Anda yakin ingin menghapus produk "{product.name}"? Tindakan ini tidak dapat dibatalkan.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Batal</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => handleDelete(product.id)}
                      disabled={isDeleting}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      {isDeleting ? "Menghapus..." : "Hapus"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
