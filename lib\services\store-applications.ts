import { getClient } from '@/lib/supabase'

export interface StoreApplication {
  id: string
  store_name: string
  owner_name: string
  email: string
  phone: string
  category: string
  location: string
  status: 'pending' | 'under_review' | 'approved' | 'rejected'
  submitted_date: string
  business_plan: string
  experience: string
  expected_revenue: string
  documents: string[]
  description?: string
  social_media: {
    instagram?: string
    website?: string
  }
  reject_reason?: string
  approve_reason?: string
  review_notes?: string
  reviewed_date?: string
  created_at: string
  updated_at: string
}

export interface StoreApplicationFilters {
  status?: string
  search?: string
}

export interface StoreApplicationUpdate {
  status?: string
  reject_reason?: string
  approve_reason?: string
  review_notes?: string
  reviewed_date?: string
}

class StoreApplicationService {
  private supabase = getClient()

  // Get all store applications with optional filters
  async getApplications(filters?: StoreApplicationFilters): Promise<StoreApplication[]> {
    let query = this.supabase
      .from('store_applications')
      .select('*')
      .order('created_at', { ascending: false })

    // Apply status filter
    if (filters?.status && filters.status !== 'all') {
      query = query.eq('status', filters.status)
    }

    // Apply search filter
    if (filters?.search) {
      const searchTerm = `%${filters.search}%`
      query = query.or(`store_name.ilike.${searchTerm},owner_name.ilike.${searchTerm},email.ilike.${searchTerm},category.ilike.${searchTerm}`)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch applications: ${error.message}`)
    }

    return data || []
  }

  // Get single application by ID
  async getApplication(id: string): Promise<StoreApplication | null> {
    const { data, error } = await this.supabase
      .from('store_applications')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch application: ${error.message}`)
    }

    return data
  }

  // Create new application
  async createApplication(application: Omit<StoreApplication, 'id' | 'created_at' | 'updated_at'>): Promise<StoreApplication> {
    const { data, error } = await this.supabase
      .from('store_applications')
      .insert([application])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create application: ${error.message}`)
    }

    return data
  }

  // Update application
  async updateApplication(id: string, updates: StoreApplicationUpdate): Promise<StoreApplication> {
    const { data, error } = await this.supabase
      .from('store_applications')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update application: ${error.message}`)
    }

    return data
  }

  // Approve application
  async approveApplication(id: string, reason?: string): Promise<StoreApplication> {
    const updates: StoreApplicationUpdate = {
      status: 'approved',
      reviewed_date: new Date().toISOString().split('T')[0],
    }

    if (reason) {
      updates.approve_reason = reason
    }

    return this.updateApplication(id, updates)
  }

  // Reject application
  async rejectApplication(id: string, reason: string): Promise<StoreApplication> {
    const updates: StoreApplicationUpdate = {
      status: 'rejected',
      reject_reason: reason,
      reviewed_date: new Date().toISOString().split('T')[0],
    }

    return this.updateApplication(id, updates)
  }

  // Set application under review
  async setUnderReview(id: string, notes?: string): Promise<StoreApplication> {
    const updates: StoreApplicationUpdate = {
      status: 'under_review',
      reviewed_date: new Date().toISOString().split('T')[0],
    }

    if (notes) {
      updates.review_notes = notes
    }

    return this.updateApplication(id, updates)
  }

  // Delete application
  async deleteApplication(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('store_applications')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete application: ${error.message}`)
    }
  }

  // Get application statistics
  async getApplicationStats(): Promise<{
    total: number
    pending: number
    approved: number
    rejected: number
    under_review: number
  }> {
    const { data, error } = await this.supabase
      .from('store_applications')
      .select('status')

    if (error) {
      throw new Error(`Failed to fetch application stats: ${error.message}`)
    }

    const stats = {
      total: data.length,
      pending: 0,
      approved: 0,
      rejected: 0,
      under_review: 0,
    }

    data.forEach((app) => {
      switch (app.status) {
        case 'pending':
          stats.pending++
          break
        case 'approved':
          stats.approved++
          break
        case 'rejected':
          stats.rejected++
          break
        case 'under_review':
          stats.under_review++
          break
      }
    })

    return stats
  }
}

export const storeApplicationService = new StoreApplicationService()
