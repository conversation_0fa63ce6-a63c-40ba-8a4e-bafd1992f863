import axios from "axios"
import type { TenantTheme, CreateTenantThemeDTO, UpdateTenantThemeDTO } from "@/lib/models/tenant-theme"
// Tambahkan import untuk tema Velozio
import { velozioTheme } from "@/lib/models/velozio-theme"

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"

// Fungsi untuk mendapatkan token dari localStorage
const getToken = () => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("token")
  }
  return null
}

// Fungsi untuk membuat header dengan token
const getHeaders = () => {
  const token = getToken()
  return {
    headers: {
      Authorization: token ? `Bearer ${token}` : "",
    },
  }
}

// Fungsi untuk menentukan apakah harus menggunakan data mock
const shouldUseMockData = () => {
  // Gunakan data mock jika dalam mode development atau preview
  if (process.env.NODE_ENV === "development" || process.env.VERCEL_ENV === "preview") {
    return true
  }

  // Gunakan data mock jika API_URL adalah localhost
  if (API_URL.includes("localhost")) {
    return true
  }

  return false
}

// Data mock untuk tema tenant
// Tambahkan tema Velozio ke mockTenantTheme
const mockTenantThemes = [
  {
    id: "mock-theme-1",
    tenantId: "mock-tenant-1",
    name: "Default Theme",
    isActive: true,
    colors: {
      primary: "#3b82f6",
      secondary: "#10b981",
      accent: "#8b5cf6",
      background: "#ffffff",
      foreground: "#0f172a",
      muted: "#f1f5f9",
      mutedForeground: "#64748b",
      border: "#e2e8f0",
      input: "#e2e8f0",
      card: "#ffffff",
      cardForeground: "#0f172a",
      destructive: "#ef4444",
      destructiveForeground: "#ffffff",
    },
    fonts: {
      heading: "Inter, sans-serif",
      body: "Inter, sans-serif",
    },
    logo: {
      main: "/your-logo.png",
      favicon: "/favicon.ico",
    },
    customCSS: "",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "velozio-theme-1",
    tenantId: "mock-tenant-1",
    name: "Velozio",
    isActive: false,
    colors: velozioTheme.colors,
    fonts: velozioTheme.fonts,
    layout: velozioTheme.layout,
    logo: {
      main: "/your-logo.png",
      favicon: "/favicon.ico",
    },
    customCSS: velozioTheme.customCSS,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

const mockTenantTheme = mockTenantThemes[0]

// Fungsi untuk menambahkan delay simulasi
const addDelay = async (ms = 300) => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export const tenantThemesAPI = {
  // Mendapatkan semua tema untuk tenant tertentu
  getByTenantId: async (tenantId: string): Promise<TenantTheme[]> => {
    try {
      // Jika harus menggunakan data mock
      if (shouldUseMockData()) {
        console.log("Using mock data for tenant themes")
        await addDelay()
        return mockTenantThemes
      }

      const response = await axios.get(`${API_URL}/tenant-themes?tenantId=${tenantId}`, getHeaders())
      return response.data
    } catch (error) {
      console.error("Error fetching tenant themes:", error)
      console.log("Falling back to mock data for tenant themes")
      return [mockTenantTheme]
    }
  },

  // Mendapatkan tema aktif untuk tenant tertentu
  getActiveTenantTheme: async (tenantId: string): Promise<TenantTheme | null> => {
    try {
      // Jika harus menggunakan data mock
      if (shouldUseMockData()) {
        console.log("Using mock data for active tenant theme")
        await addDelay()
        return mockTenantTheme
      }

      const response = await axios.get(`${API_URL}/tenant-themes/active?tenantId=${tenantId}`, getHeaders())
      return response.data
    } catch (error) {
      console.error("Error fetching active tenant theme:", error)
      console.log("Falling back to mock data for active tenant theme")
      return mockTenantTheme
    }
  },

  // Mendapatkan tema berdasarkan ID
  getById: async (id: string): Promise<TenantTheme | null> => {
    try {
      // Jika harus menggunakan data mock
      if (shouldUseMockData()) {
        console.log("Using mock data for tenant theme by ID")
        await addDelay()
        return mockTenantTheme
      }

      const response = await axios.get(`${API_URL}/tenant-themes/${id}`, getHeaders())
      return response.data
    } catch (error) {
      console.error("Error fetching tenant theme:", error)
      console.log("Falling back to mock data for tenant theme by ID")
      return mockTenantTheme
    }
  },

  // Membuat tema baru
  create: async (data: CreateTenantThemeDTO): Promise<TenantTheme | null> => {
    try {
      // Jika harus menggunakan data mock
      if (shouldUseMockData()) {
        console.log("Using mock data for creating tenant theme")
        await addDelay()
        return {
          ...mockTenantTheme,
          ...data,
          id: `mock-theme-${Date.now()}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      }

      const response = await axios.post(`${API_URL}/tenant-themes`, data, getHeaders())
      return response.data
    } catch (error) {
      console.error("Error creating tenant theme:", error)
      return null
    }
  },

  // Memperbarui tema
  update: async (id: string, data: UpdateTenantThemeDTO): Promise<TenantTheme | null> => {
    try {
      // Jika harus menggunakan data mock
      if (shouldUseMockData()) {
        console.log("Using mock data for updating tenant theme")
        await addDelay()
        return {
          ...mockTenantTheme,
          ...data,
          id,
          updatedAt: new Date().toISOString(),
        }
      }

      const response = await axios.patch(`${API_URL}/tenant-themes/${id}`, data, getHeaders())
      return response.data
    } catch (error) {
      console.error("Error updating tenant theme:", error)
      return null
    }
  },

  // Menghapus tema
  delete: async (id: string): Promise<boolean> => {
    try {
      // Jika harus menggunakan data mock
      if (shouldUseMockData()) {
        console.log("Using mock data for deleting tenant theme")
        await addDelay()
        return true
      }

      await axios.delete(`${API_URL}/tenant-themes/${id}`, getHeaders())
      return true
    } catch (error) {
      console.error("Error deleting tenant theme:", error)
      return false
    }
  },

  // Mengaktifkan tema
  activate: async (id: string): Promise<TenantTheme | null> => {
    try {
      // Jika harus menggunakan data mock
      if (shouldUseMockData()) {
        console.log("Using mock data for activating tenant theme")
        await addDelay()
        return {
          ...mockTenantTheme,
          id,
          isActive: true,
          updatedAt: new Date().toISOString(),
        }
      }

      const response = await axios.post(`${API_URL}/tenant-themes/${id}/activate`, {}, getHeaders())
      return response.data
    } catch (error) {
      console.error("Error activating tenant theme:", error)
      return null
    }
  },

  // Mendapatkan tema berdasarkan domain
  getByDomain: async (domain: string): Promise<TenantTheme | null> => {
    try {
      // Jika harus menggunakan data mock
      if (shouldUseMockData()) {
        console.log("Using mock data for tenant theme by domain")
        await addDelay()
        return mockTenantTheme
      }

      const response = await axios.get(`${API_URL}/tenant-themes/domain/${domain}`, getHeaders())
      return response.data
    } catch (error) {
      console.error("Error fetching tenant theme by domain:", error)
      console.log("Falling back to mock data for tenant theme by domain")
      return mockTenantTheme
    }
  },
}
