"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { useAuth } from "@/contexts/auth-context"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Lock } from "lucide-react"

// Mengubah nama export dari LoginForm menjadi AdminLoginForm
export function AdminLoginForm() {
  const { login } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      console.log("Attempting to login as admin with:", formData.email)

      // Untuk development, gunakan email dengan "admin" untuk login sebagai admin
      if (!formData.email.includes("admin")) {
        throw new Error("Email harus mengandung 'admin' untuk login sebagai admin")
      }

      await login(formData.email, formData.password)
      console.log("Admin login successful")
    } catch (err: any) {
      console.error("Admin login failed:", err)

      // Pesan error yang lebih user-friendly
      let errorMessage = "Login gagal. Periksa email dan password Anda."

      if (err.message) {
        if (err.message.includes("Network Error")) {
          errorMessage = "Tidak dapat terhubung ke server. Menggunakan mode offline."

          // Coba login lagi dengan mode offline
          try {
            await login(formData.email, formData.password)
            console.log("Admin login successful in offline mode")
            return
          } catch (offlineErr: any) {
            errorMessage = offlineErr.message || "Login gagal dalam mode offline."
          }
        } else {
          errorMessage = err.message
        }
      }

      setError(errorMessage)
      setIsLoading(false)
    }
  }

  return (
    <Card className="border-primary/20">
      <CardContent className="space-y-4 pt-6">
        <div className="flex justify-center mb-4">
          <div className="p-3 rounded-full bg-primary/10">
            <Lock className="h-6 w-6 text-primary" />
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={onSubmit}>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Admin</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                required
              />
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Memproses..." : "Login Admin"}
            </Button>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-center border-t p-4">
        <p className="text-sm text-muted-foreground">
          {process.env.NODE_ENV === "development" || process.env.VERCEL_ENV === "preview"
            ? "Mode Preview: Gunakan email dengan 'admin' untuk login"
            : "Akses hanya untuk administrator sistem"}
        </p>
      </CardFooter>
    </Card>
  )
}

// Tambahkan alias untuk backward compatibility jika diperlukan
export { AdminLoginForm as LoginForm }
