"use client"

import { useState, useEffect } from "react"
import { DataTable } from "@/components/ui/data-table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Eye, Edit, UserX, UserCog } from "lucide-react"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ErrorBoundary } from "@/components/error-boundary"

// Mock data for users
const users = [
  {
    id: "u1",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    tenant: "SaaS Corp",
    registrationDate: "2023-01-15",
    lastLogin: "2023-05-10",
    status: "Active",
  },
  {
    id: "u2",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Tenant Admin",
    tenant: "Tech Solutions",
    registrationDate: "2023-02-20",
    lastLogin: "2023-05-09",
    status: "Active",
  },
  {
    id: "u3",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Store Owner",
    tenant: "Fashion Hub",
    registrationDate: "2023-03-05",
    lastLogin: "2023-05-08",
    status: "Active",
  },
  {
    id: "u4",
    name: "Emily Davis",
    email: "<EMAIL>",
    role: "End User",
    tenant: "Tech Solutions",
    registrationDate: "2023-03-10",
    lastLogin: "2023-05-07",
    status: "Active",
  },
  {
    id: "u5",
    name: "Michael Wilson",
    email: "<EMAIL>",
    role: "End User",
    tenant: "Fashion Hub",
    registrationDate: "2023-03-15",
    lastLogin: "2023-05-06",
    status: "Suspended",
  },
  {
    id: "u6",
    name: "Sarah Brown",
    email: "<EMAIL>",
    role: "Store Owner",
    tenant: "SaaS Corp",
    registrationDate: "2023-03-20",
    lastLogin: "2023-05-05",
    status: "Active",
  },
  {
    id: "u7",
    name: "David Miller",
    email: "<EMAIL>",
    role: "Tenant Admin",
    tenant: "Food Delivery",
    registrationDate: "2023-03-25",
    lastLogin: "2023-05-04",
    status: "Active",
  },
  {
    id: "u8",
    name: "Lisa Taylor",
    email: "<EMAIL>",
    role: "End User",
    tenant: "SaaS Corp",
    registrationDate: "2023-04-01",
    lastLogin: "2023-05-03",
    status: "Inactive",
  },
  {
    id: "u9",
    name: "James Anderson",
    email: "<EMAIL>",
    role: "End User",
    tenant: "Tech Solutions",
    registrationDate: "2023-04-05",
    lastLogin: "2023-05-02",
    status: "Active",
  },
  {
    id: "u10",
    name: "Patricia Thomas",
    email: "<EMAIL>",
    role: "Store Owner",
    tenant: "Food Delivery",
    registrationDate: "2023-04-10",
    lastLogin: "2023-05-01",
    status: "Pending",
  },
]

// Ubah dari default export menjadi named export
export function UserList() {
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")
  const [tenantFilter, setTenantFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isClient, setIsClient] = useState(false)

  // Ensure component is mounted before rendering
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Filter users based on search term and filters
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesRole = roleFilter === "all" || user.role === roleFilter
    const matchesTenant = tenantFilter === "all" || user.tenant === tenantFilter
    const matchesStatus = statusFilter === "all" || user.status === statusFilter

    return matchesSearch && matchesRole && matchesTenant && matchesStatus
  })

  // Get unique values for filters
  const roles = [...new Set(users.map((user) => user.role))]
  const tenants = [...new Set(users.map((user) => user.tenant))]
  const statuses = [...new Set(users.map((user) => user.status))]

  const columns = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "role",
      header: "Role",
      cell: ({ row }) => {
        const role = row.getValue("role") as string
        return (
          <Badge
            variant={
              role === "Admin"
                ? "destructive"
                : role === "Tenant Admin"
                  ? "default"
                  : role === "Store Owner"
                    ? "secondary"
                    : "outline"
            }
          >
            {role}
          </Badge>
        )
      },
    },
    {
      accessorKey: "tenant",
      header: "Tenant",
    },
    {
      accessorKey: "registrationDate",
      header: "Registration Date",
    },
    {
      accessorKey: "lastLogin",
      header: "Last Login",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <Badge
            variant={
              status === "Active"
                ? "success"
                : status === "Suspended"
                  ? "destructive"
                  : status === "Pending"
                    ? "warning"
                    : "secondary"
            }
          >
            {status}
          </Badge>
        )
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const user = row.original as (typeof users)[0]
        return (
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" title="View User">
              <Eye className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Edit User">
              <Edit className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Suspend User">
              <UserX className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Login as User">
              <UserCog className="h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]

  if (!isClient) {
    return <div>Loading user list...</div>
  }

  return (
    <ErrorBoundary>
      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" className="mb-6">
            <TabsList>
              <TabsTrigger value="all">All Users</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="suspended">Suspended</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="inactive">Inactive</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  {roles.map((role) => (
                    <SelectItem key={role} value={role}>
                      {role}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={tenantFilter} onValueChange={setTenantFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by Tenant" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tenants</SelectItem>
                  {tenants.map((tenant) => (
                    <SelectItem key={tenant} value={tenant}>
                      {tenant}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {statuses.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DataTable columns={columns} data={filteredUsers} searchColumn="name" searchPlaceholder="Search users..." />
        </CardContent>
      </Card>
    </ErrorBoundary>
  )
}

// Tambahkan juga default export untuk backward compatibility
export default UserList
