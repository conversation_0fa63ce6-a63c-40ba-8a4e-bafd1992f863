"use client"

import { useState, useEffect } from "react"
import { StoreList } from "@/components/admin/stores/store-list"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { useRouter } from "next/navigation"
import { ErrorBoundary } from "@/components/error-boundary"

// Data stores (contoh)
const stores = [
  {
    id: "1",
    name: "Tech Gadgets Store",
    owner: "<PERSON>",
    products: 45,
    revenue: "$12,450",
    status: "Active",
    createdAt: "2023-01-15T10:00:00Z",
  },
  {
    id: "2",
    name: "Fashion Boutique",
    owner: "<PERSON>",
    products: 78,
    revenue: "$8,920",
    status: "Active",
    createdAt: "2023-02-20T14:30:00Z",
  },
  {
    id: "3",
    name: "Home Decor",
    owner: "<PERSON>",
    products: 32,
    revenue: "$5,670",
    status: "Pending",
    createdAt: "2023-03-10T09:45:00Z",
  },
  {
    id: "4",
    name: "Sports Equipment",
    owner: "<PERSON>",
    products: 56,
    revenue: "$7,890",
    status: "Active",
    createdAt: "2023-01-05T16:20:00Z",
  },
  {
    id: "5",
    name: "Organic Foods",
    owner: "Michael Brown",
    products: 23,
    revenue: "$3,450",
    status: "Inactive",
    createdAt: "2023-02-28T11:10:00Z",
  },
]

export default function StoresClient() {
  const router = useRouter()
  const [isLoaded, setIsLoaded] = useState(false)

  // Simulasi loading state untuk memastikan komponen dirender dengan benar
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  if (!isLoaded) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Stores</h1>
        <p>Loading stores list...</p>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Stores</h1>
          <Button onClick={() => router.push("/admin/dashboard/stores/create")}>
            <Plus className="mr-2 h-4 w-4" /> Add Store
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Stores</CardTitle>
          </CardHeader>
          <CardContent>
            <StoreListWrapper />
          </CardContent>
        </Card>
      </div>
    </ErrorBoundary>
  )
}

// Wrapper component untuk StoreList untuk mengisolasi error
function StoreListWrapper() {
  return (
    <ErrorBoundary
      fallback={
        <div className="p-4 border border-red-200 rounded bg-red-50 text-red-700">
          <h3 className="font-bold mb-2">Error loading store list</h3>
          <p>There was a problem loading the store list. Please try refreshing the page.</p>
          <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
            Refresh Page
          </Button>
        </div>
      }
    >
      <StoreList />
    </ErrorBoundary>
  )
}
