"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import type { Event } from "./types"
import { useState } from "react"

interface ReminderSetupProps {
  event: Event
  onClose: () => void
}

export function ReminderSetup({ event, onClose }: ReminderSetupProps) {
  const [reminderTime, setReminderTime] = useState("30")
  const [reminderMethods, setReminderMethods] = useState({
    email: true,
    push: true,
    sms: false,
  })

  const handleMethodChange = (method: keyof typeof reminderMethods) => {
    setReminderMethods((prev) => ({
      ...prev,
      [method]: !prev[method],
    }))
  }

  const handleSaveReminder = () => {
    // Implementasi untuk menyimpan pengingat
    console.log("Reminder settings:", {
      event,
      reminderTime,
      reminderMethods,
    })
    onClose()
  }

  return (
    <Card className="absolute bottom-full right-0 z-50 mb-2 w-64">
      <CardHeader className="p-3">
        <CardTitle className="text-sm">Atur Pengingat</CardTitle>
        <CardDescription className="text-xs">Dapatkan notifikasi sebelum event dimulai</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3 p-3">
        <div className="space-y-1">
          <Label htmlFor="reminder-time" className="text-xs">
            Ingatkan saya sebelum
          </Label>
          <Select value={reminderTime} onValueChange={setReminderTime}>
            <SelectTrigger id="reminder-time">
              <SelectValue placeholder="Pilih waktu" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10 menit</SelectItem>
              <SelectItem value="30">30 menit</SelectItem>
              <SelectItem value="60">1 jam</SelectItem>
              <SelectItem value="120">2 jam</SelectItem>
              <SelectItem value="1440">1 hari</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label className="text-xs">Metode notifikasi</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="reminder-email"
                checked={reminderMethods.email}
                onCheckedChange={() => handleMethodChange("email")}
              />
              <Label htmlFor="reminder-email" className="text-xs">
                Email
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="reminder-push"
                checked={reminderMethods.push}
                onCheckedChange={() => handleMethodChange("push")}
              />
              <Label htmlFor="reminder-push" className="text-xs">
                Notifikasi Push
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="reminder-sms"
                checked={reminderMethods.sms}
                onCheckedChange={() => handleMethodChange("sms")}
              />
              <Label htmlFor="reminder-sms" className="text-xs">
                SMS
              </Label>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between p-3 pt-0">
        <Button size="sm" variant="ghost" onClick={onClose}>
          Batal
        </Button>
        <Button size="sm" onClick={handleSaveReminder}>
          Simpan
        </Button>
      </CardFooter>
    </Card>
  )
}
