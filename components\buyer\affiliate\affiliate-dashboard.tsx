"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@/components/ui/charts"
import { DataTable } from "@/components/ui/data-table"
import { Badge } from "@/components/ui/badge"
import { formatCurrency } from "@/lib/utils"
import { affiliateAPI } from "@/lib/api/affiliate"
import type { AffiliateUser, AffiliateReferral, AffiliateCommission, AffiliatePayment } from "@/lib/models/affiliate"
import { Copy, AlertCircle, TrendingUp, Users, DollarSign, CreditCard } from "lucide-react"

export function AffiliateDashboard() {
  const [affiliateUser, setAffiliateUser] = useState<AffiliateUser | null>(null)
  const [referrals, setReferrals] = useState<AffiliateReferral[]>([])
  const [commissions, setCommissions] = useState<AffiliateCommission[]>([])
  const [payments, setPayments] = useState<AffiliatePayment[]>([])
  const [referralLink, setReferralLink] = useState<string>("")
  const [copied, setCopied] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [user, refs, comms, pays] = await Promise.all([
          affiliateAPI.getAffiliateUser(),
          affiliateAPI.getReferrals(),
          affiliateAPI.getCommissions(),
          affiliateAPI.getPayments(),
        ])

        setAffiliateUser(user)
        setReferrals(refs)
        setCommissions(comms)
        setPayments(pays)

        // Generate referral link
        const link = await affiliateAPI.generateReferralLink("https://sellzio.com")
        setReferralLink(link)

        setLoading(false)
      } catch (error) {
        console.error("Error fetching affiliate data:", error)
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const copyToClipboard = () => {
    navigator.clipboard.writeText(referralLink)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const pendingCommissions = commissions.filter((c) => c.status === "pending").reduce((sum, c) => sum + c.amount, 0)
  const approvedCommissions = commissions.filter((c) => c.status === "approved").reduce((sum, c) => sum + c.amount, 0)
  const paidCommissions = commissions.filter((c) => c.status === "paid").reduce((sum, c) => sum + c.amount, 0)

  const referralColumns = [
    {
      header: "Tanggal",
      accessorKey: "createdAt",
      cell: ({ row }: any) => new Date(row.original.createdAt).toLocaleDateString(),
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }: any) => {
        const status = row.original.status
        return (
          <Badge variant={status === "purchased" ? "success" : status === "registered" ? "outline" : "secondary"}>
            {status === "purchased" ? "Pembelian" : status === "registered" ? "Terdaftar" : "Pending"}
          </Badge>
        )
      },
    },
  ]

  const commissionColumns = [
    {
      header: "Tanggal",
      accessorKey: "createdAt",
      cell: ({ row }: any) => new Date(row.original.createdAt).toLocaleDateString(),
    },
    { header: "Order ID", accessorKey: "orderId" },
    { header: "Jumlah", accessorKey: "amount", cell: ({ row }: any) => formatCurrency(row.original.amount) },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }: any) => {
        const status = row.original.status
        return (
          <Badge
            variant={
              status === "approved"
                ? "outline"
                : status === "paid"
                  ? "success"
                  : status === "rejected"
                    ? "destructive"
                    : "secondary"
            }
          >
            {status === "approved"
              ? "Disetujui"
              : status === "paid"
                ? "Dibayar"
                : status === "rejected"
                  ? "Ditolak"
                  : "Pending"}
          </Badge>
        )
      },
    },
  ]

  const paymentColumns = [
    {
      header: "Tanggal",
      accessorKey: "createdAt",
      cell: ({ row }: any) => new Date(row.original.createdAt).toLocaleDateString(),
    },
    { header: "Jumlah", accessorKey: "amount", cell: ({ row }: any) => formatCurrency(row.original.amount) },
    {
      header: "Metode",
      accessorKey: "paymentMethod",
      cell: ({ row }: any) => {
        const method = row.original.paymentMethod
        return `${method.type === "bank_transfer" ? "Transfer Bank" : "E-Wallet"} - ${method.details.bankName || method.details.walletName}`
      },
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }: any) => {
        const status = row.original.status
        return (
          <Badge
            variant={
              status === "completed"
                ? "success"
                : status === "processing"
                  ? "outline"
                  : status === "failed"
                    ? "destructive"
                    : "secondary"
            }
          >
            {status === "completed"
              ? "Selesai"
              : status === "processing"
                ? "Diproses"
                : status === "failed"
                  ? "Gagal"
                  : "Pending"}
          </Badge>
        )
      },
    },
  ]

  if (loading) {
    return <div className="flex items-center justify-center h-96">Loading...</div>
  }

  if (!affiliateUser) {
    return (
      <div className="space-y-4 p-4">
        <Card>
          <CardHeader>
            <CardTitle>Program Affiliate</CardTitle>
            <CardDescription>Anda belum terdaftar sebagai affiliate</CardDescription>
          </CardHeader>
          <CardContent>
            <Button>Daftar Sekarang</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(affiliateUser.balance)}</div>
            <p className="text-xs text-muted-foreground">
              Total pendapatan: {formatCurrency(affiliateUser.totalEarnings)}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Referral</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{referrals.length}</div>
            <p className="text-xs text-muted-foreground">
              {referrals.filter((r) => r.status === "purchased").length} pembelian
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Komisi Pending</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(pendingCommissions + approvedCommissions)}</div>
            <p className="text-xs text-muted-foreground">
              {commissions.filter((c) => c.status === "pending" || c.status === "approved").length} transaksi
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Dibayarkan</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(paidCommissions)}</div>
            <p className="text-xs text-muted-foreground">
              {payments.filter((p) => p.status === "completed").length} pembayaran
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Link Referral Anda</CardTitle>
          <CardDescription>Bagikan link ini untuk mendapatkan komisi dari setiap pembelian</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            <Input value={referralLink} readOnly className="flex-1" />
            <Button onClick={copyToClipboard} variant="outline">
              {copied ? "Disalin!" : <Copy className="h-4 w-4" />}
            </Button>
          </div>
          <div className="mt-4 flex items-center space-x-2">
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Anda akan mendapatkan komisi {affiliateUser.programId === "1" ? "10%" : "15%"} dari setiap pembelian
            </p>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="referrals">Referral</TabsTrigger>
          <TabsTrigger value="commissions">Komisi</TabsTrigger>
          <TabsTrigger value="payments">Pembayaran</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performa Affiliate</CardTitle>
              <CardDescription>Statistik performa affiliate Anda dalam 30 hari terakhir</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <LineChart
                data={[
                  { name: "1 Mei", Referral: 2, Komisi: 50000 },
                  { name: "8 Mei", Referral: 3, Komisi: 75000 },
                  { name: "15 Mei", Referral: 5, Komisi: 125000 },
                  { name: "22 Mei", Referral: 7, Komisi: 175000 },
                  { name: "29 Mei", Referral: 10, Komisi: 250000 },
                ]}
                index="name"
                categories={["Referral", "Komisi"]}
                colors={["blue", "green"]}
                valueFormatter={(value) =>
                  typeof value === "number" && value > 1000 ? formatCurrency(value) : `${value}`
                }
                yAxisWidth={80}
              />
            </CardContent>
          </Card>
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Status Referral</CardTitle>
                <CardDescription>Distribusi status referral Anda</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <PieChart
                  data={[
                    { name: "Pending", value: referrals.filter((r) => r.status === "pending").length },
                    { name: "Terdaftar", value: referrals.filter((r) => r.status === "registered").length },
                    { name: "Pembelian", value: referrals.filter((r) => r.status === "purchased").length },
                  ]}
                  index="name"
                  category="value"
                  colors={["gray", "blue", "green"]}
                  valueFormatter={(value) => `${value} referral`}
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Status Komisi</CardTitle>
                <CardDescription>Distribusi status komisi Anda</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <BarChart
                  data={[
                    {
                      name: "Pending",
                      value: commissions.filter((c) => c.status === "pending").reduce((sum, c) => sum + c.amount, 0),
                    },
                    {
                      name: "Disetujui",
                      value: commissions.filter((c) => c.status === "approved").reduce((sum, c) => sum + c.amount, 0),
                    },
                    {
                      name: "Dibayar",
                      value: commissions.filter((c) => c.status === "paid").reduce((sum, c) => sum + c.amount, 0),
                    },
                    {
                      name: "Ditolak",
                      value: commissions.filter((c) => c.status === "rejected").reduce((sum, c) => sum + c.amount, 0),
                    },
                  ]}
                  index="name"
                  categories={["value"]}
                  colors={["blue"]}
                  valueFormatter={(value) => formatCurrency(value)}
                  yAxisWidth={80}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="referrals">
          <Card>
            <CardHeader>
              <CardTitle>Daftar Referral</CardTitle>
              <CardDescription>Daftar pengguna yang menggunakan link referral Anda</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable columns={referralColumns} data={referrals} />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="commissions">
          <Card>
            <CardHeader>
              <CardTitle>Daftar Komisi</CardTitle>
              <CardDescription>Daftar komisi dari pembelian referral Anda</CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable columns={commissionColumns} data={commissions} />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="payments">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Daftar Pembayaran</CardTitle>
                <CardDescription>Riwayat pembayaran komisi Anda</CardDescription>
              </div>
              <Button disabled={affiliateUser.balance < affiliateUser.programId === "1" ? 100000 : 200000}>
                Tarik Dana
              </Button>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Informasi Penarikan</AlertTitle>
                <AlertDescription>
                  Minimum penarikan: {formatCurrency(affiliateUser.programId === "1" ? 100000 : 200000)}. Penarikan
                  diproses dalam 1-3 hari kerja.
                </AlertDescription>
              </Alert>
              <DataTable columns={paymentColumns} data={payments} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
