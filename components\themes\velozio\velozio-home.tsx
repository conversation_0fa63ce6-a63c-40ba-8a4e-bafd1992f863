"use client"

import { useState } from "react"
import { VelozioSearchBar } from "./search-bar"
import { VelozioFilterTabs } from "./velozio-filter-tabs"
import { VelozioHeader } from "./velozio-header"
import { FeedCard, type FeedCardProps, type BadgeType } from "./feed-card/feed-card"

export interface Product extends Omit<FeedCardProps, 'type' | 'badgeType'> {
  id: number
  badgeType: BadgeType
}

interface VelozioHomeProps {
  products?: Product[]
}

export function VelozioHome({ products = [] }: VelozioHomeProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products)

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    // Implementasi pencarian bisa ditambahkan di sini
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <VelozioHeader />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold text-white mb-6">Temukan Produk Terbaik</h1>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto">
            <VelozioSearchBar onSearch={handleSearch} />
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Filter Tabs */}
        <div className="mb-8">
          <VelozioFilterTabs />
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <FeedCard
              key={product.id}
              type="standard"
              name={product.name}
              price={product.price}
              originalPrice={product.originalPrice}
              discount={product.discount}
              image={product.image}
              badgeType={product.badgeType}
              rating={product.rating}
              sold={product.sold}
              hasCod={product.hasCod}
              isLive={product.isLive}
              link={product.link}
            />
          ))}
        </div>

        {/* Pagination */}
        <div className="mt-8 flex justify-center">
          <button className="px-4 py-2 border rounded-md hover:bg-gray-100">
            Muat Lebih Banyak
          </button>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-100 py-8 mt-12">
        <div className="container mx-auto px-4 text-center">
          <p> 2025 Velozio Theme. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}
