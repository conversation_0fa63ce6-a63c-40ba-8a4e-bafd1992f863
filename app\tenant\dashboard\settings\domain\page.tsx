"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  ArrowLeft,
  Globe,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Plus,
  Shield,
  LinkIcon,
  Save,
  ExternalLink,
  Copy,
  Info,
  AlertCircle
} from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialog<PERSON>itle,
  <PERSON>ertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function DomainPage() {
  const [activeTab, setActiveTab] = useState("domains")
  
  // Dummy data untuk domains
  const domains = [
    {
      id: "domain-1",
      domain: "tokoku.example.com",
      type: "subdomain",
      status: "active",
      isDefault: true,
      ssl: true,
      dateAdded: "2024-01-15T10:30:00",
      lastVerified: "2024-05-20T14:20:00"
    },
    {
      id: "domain-2",
      domain: "toko-online.com",
      type: "custom",
      status: "pending",
      isDefault: false,
      ssl: false,
      dateAdded: "2024-05-18T09:45:00",
      verificationError: "DNS records not properly configured"
    },
    {
      id: "domain-3",
      domain: "my-store.shop",
      type: "custom",
      status: "failed",
      isDefault: false,
      ssl: false,
      dateAdded: "2024-05-10T11:20:00",
      verificationError: "Domain ownership verification failed"
    }
  ]

  // Dummy data untuk URL settings
  const urlSettings = {
    permalinkStructure: "product-name",
    useHttps: true,
    redirectWww: true,
    enableSEOUrls: true,
    customStorePath: "shop"
  }

  // Format tanggal
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  // Status badge berdasarkan status domain
  function getDomainStatusBadge(status: string) {
    switch (status) {
      case "active":
        return <Badge variant="outline" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Aktif</Badge>
      case "pending":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Menunggu Verifikasi</Badge>
      case "failed":
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Gagal</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/settings">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Pengaturan Domain</h1>
            <p className="text-muted-foreground">
              Kelola domain dan URL toko online Anda
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Tambah Domain
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 md:w-auto">
          <TabsTrigger value="domains">Domain</TabsTrigger>
          <TabsTrigger value="urls">Pengaturan URL</TabsTrigger>
        </TabsList>

        {/* Domains Tab */}
        <TabsContent value="domains" className="space-y-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Domain Default Tersedia</AlertTitle>
            <AlertDescription>
              Toko Anda secara otomatis tersedia di <strong>tokoku.example.com</strong>. Tambahkan domain kustom untuk meningkatkan branding Anda.
            </AlertDescription>
          </Alert>

          <Card>
            <CardHeader>
              <CardTitle>Domain Terdaftar</CardTitle>
              <CardDescription>
                Domain yang terhubung dengan toko online Anda
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {domains.map((domain) => (
                  <div key={domain.id} className="border rounded-lg p-4">
                    <div className="flex flex-col md:flex-row justify-between gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-primary" />
                          <span className="font-medium">{domain.domain}</span>
                          {domain.isDefault && (
                            <Badge variant="secondary">Default</Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span>Ditambahkan pada {formatDate(domain.dateAdded)}</span>
                          <span>•</span>
                          <span>{domain.type === "subdomain" ? "Subdomain" : "Domain Kustom"}</span>
                        </div>
                      </div>
                      <div className="flex flex-col md:items-end gap-2">
                        <div className="flex items-center gap-2">
                          {getDomainStatusBadge(domain.status)}
                          {domain.ssl && (
                            <Badge variant="outline" className="bg-blue-100 text-blue-800">
                              <Shield className="h-3 w-3 mr-1" />SSL
                            </Badge>
                          )}
                        </div>
                        {domain.status === "active" && domain.lastVerified && (
                          <span className="text-xs text-muted-foreground">
                            Terverifikasi pada {formatDate(domain.lastVerified)}
                          </span>
                        )}
                        {domain.verificationError && (
                          <span className="text-xs text-red-500">
                            Error: {domain.verificationError}
                          </span>
                        )}
                      </div>
                    </div>
                    <Separator className="my-4" />
                    <div className="flex flex-wrap gap-2 justify-end">
                      {domain.status === "active" && (
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`https://${domain.domain}`} target="_blank">
                            <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                            Kunjungi
                          </Link>
                        </Button>
                      )}
                      {domain.status === "pending" && (
                        <Button variant="outline" size="sm">
                          <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                          Cek Status
                        </Button>
                      )}
                      {domain.status === "failed" && (
                        <Button variant="outline" size="sm">
                          <AlertCircle className="h-3.5 w-3.5 mr-1.5" />
                          Lihat Masalah
                        </Button>
                      )}
                      {!domain.isDefault && (
                        <Button variant="outline" size="sm">
                          Jadikan Default
                        </Button>
                      )}
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm" className="text-destructive">
                            Hapus
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Apakah Anda yakin?</AlertDialogTitle>
                            <AlertDialogDescription>
                              Menghapus domain akan memutus koneksi domain tersebut dengan toko Anda.
                              Tindakan ini tidak dapat dibatalkan.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Batal</AlertDialogCancel>
                            <AlertDialogAction className="bg-destructive text-destructive-foreground">
                              Hapus Domain
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Tambah Domain Baru</CardTitle>
              <CardDescription>
                Tambahkan domain kustom untuk branding toko online Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="new-domain">Domain</Label>
                <Input id="new-domain" placeholder="contoh: tokosaya.com" />
                <p className="text-sm text-muted-foreground">
                  Masukkan domain lengkap tanpa http:// atau https://
                </p>
              </div>
              <Alert className="bg-amber-50">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Persiapan Domain</AlertTitle>
                <AlertDescription>
                  Pastikan Anda telah:
                  <ul className="list-disc pl-4 mt-2 space-y-1">
                    <li>Membeli domain dari penyedia domain</li>
                    <li>Memiliki akses untuk mengubah DNS records</li>
                    <li>Siap untuk menambahkan DNS records yang diperlukan</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </CardContent>
            <CardFooter>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambahkan Domain
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Panduan Konfigurasi DNS</CardTitle>
              <CardDescription>
                Petunjuk mengatur DNS records untuk domain kustom Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm">
                Untuk menghubungkan domain kustom, tambahkan DNS records berikut di panel kontrol penyedia domain Anda:
              </p>
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted">
                    <tr>
                      <th className="text-left p-3 text-sm">Tipe</th>
                      <th className="text-left p-3 text-sm">Nama</th>
                      <th className="text-left p-3 text-sm">Nilai</th>
                      <th className="text-left p-3 text-sm">TTL</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    <tr>
                      <td className="p-3 text-sm">A</td>
                      <td className="p-3 text-sm">@</td>
                      <td className="p-3 text-sm font-mono">*********</td>
                      <td className="p-3 text-sm">3600</td>
                    </tr>
                    <tr>
                      <td className="p-3 text-sm">CNAME</td>
                      <td className="p-3 text-sm">www</td>
                      <td className="p-3 text-sm font-mono">tokoku.example.com</td>
                      <td className="p-3 text-sm">3600</td>
                    </tr>
                    <tr>
                      <td className="p-3 text-sm">TXT</td>
                      <td className="p-3 text-sm">@</td>
                      <td className="p-3 text-sm font-mono">platform-verification=abc123def456</td>
                      <td className="p-3 text-sm">3600</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div className="flex justify-end">
                <Button variant="outline" size="sm">
                  <Copy className="h-3.5 w-3.5 mr-1.5" />
                  Salin Konfigurasi
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* URLs Tab */}
        <TabsContent value="urls" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Struktur URL</CardTitle>
              <CardDescription>
                Konfigurasi struktur URL untuk produk dan kategori
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="permalink-structure">Struktur Permalink Produk</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="product-name"
                        name="permalink-structure"
                        value="product-name"
                        defaultChecked={urlSettings.permalinkStructure === "product-name"}
                        className="h-4 w-4 border-primary text-primary focus:ring-primary"
                      />
                      <div className="grid gap-1.5">
                        <label
                          htmlFor="product-name"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Nama Produk
                        </label>
                        <p className="text-sm text-muted-foreground">
                          contoh: tokoku.com/product/nama-produk
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="product-id-name"
                        name="permalink-structure"
                        value="product-id-name"
                        defaultChecked={urlSettings.permalinkStructure === "product-id-name"}
                        className="h-4 w-4 border-primary text-primary focus:ring-primary"
                      />
                      <div className="grid gap-1.5">
                        <label
                          htmlFor="product-id-name"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          ID + Nama Produk
                        </label>
                        <p className="text-sm text-muted-foreground">
                          contoh: tokoku.com/product/123-nama-produk
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="category-product"
                        name="permalink-structure"
                        value="category-product"
                        defaultChecked={urlSettings.permalinkStructure === "category-product"}
                        className="h-4 w-4 border-primary text-primary focus:ring-primary"
                      />
                      <div className="grid gap-1.5">
                        <label
                          htmlFor="category-product"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Kategori/Nama Produk
                        </label>
                        <p className="text-sm text-muted-foreground">
                          contoh: tokoku.com/kategori/nama-produk
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="custom"
                        name="permalink-structure"
                        value="custom"
                        defaultChecked={urlSettings.permalinkStructure === "custom"}
                        className="h-4 w-4 border-primary text-primary focus:ring-primary"
                      />
                      <div className="grid gap-1.5">
                        <label
                          htmlFor="custom"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Kustom
                        </label>
                        <p className="text-sm text-muted-foreground">
                          Buat struktur URL kustom
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="custom-store-path">Path Toko Kustom</Label>
                  <div className="flex items-center">
                    <span className="text-sm text-muted-foreground mr-2">tokoku.com/</span>
                    <Input 
                      id="custom-store-path" 
                      defaultValue={urlSettings.customStorePath} 
                      className="max-w-[200px]"
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Path utama untuk halaman toko (default: shop)
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Pengaturan URL Lanjutan</CardTitle>
              <CardDescription>
                Konfigurasi pengalihan dan pengaturan URL lanjutan
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="use-https">Gunakan HTTPS</Label>
                  <p className="text-sm text-muted-foreground">
                    Paksa semua URL menggunakan HTTPS (disarankan)
                  </p>
                </div>
                <Switch 
                  id="use-https" 
                  defaultChecked={urlSettings.useHttps}
                  onCheckedChange={() => {}}
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="redirect-www">Pengalihan WWW</Label>
                  <p className="text-sm text-muted-foreground">
                    Alihkan traffic dari www ke non-www atau sebaliknya
                  </p>
                </div>
                <Switch 
                  id="redirect-www" 
                  defaultChecked={urlSettings.redirectWww}
                  onCheckedChange={() => {}}
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="seo-urls">URL SEO-Friendly</Label>
                  <p className="text-sm text-muted-foreground">
                    Gunakan URL yang ramah SEO untuk semua halaman
                  </p>
                </div>
                <Switch 
                  id="seo-urls" 
                  defaultChecked={urlSettings.enableSEOUrls}
                  onCheckedChange={() => {}}
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button>
                <Save className="h-4 w-4 mr-2" />
                Simpan Perubahan
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 