import { NextRequest, NextResponse } from 'next/server';

// Sample orders data (akan diganti dengan database nanti)
let orders = [
  {
    id: "ORD-12345",
    date: "2025-05-15",
    status: "processing",
    items: [
      { name: "Kemeja Casual", quantity: 1, price: 150000, image: "/placeholder-3vk2t.png" },
      { name: "<PERSON><PERSON>", quantity: 1, price: 250000, image: "/folded-denim-stack.png" },
    ],
    total: 400000,
    shipping: {
      method: "JNE Regular",
      status: "preparing",
      trackingNumber: "JNE123456789",
      eta: "2025-05-20",
    },
  },
  {
    id: "ORD-12344",
    date: "2025-05-10",
    status: "shipped",
    items: [{ name: "Sepatu Sneakers", quantity: 1, price: 350000, image: "/diverse-sneaker-collection.png" }],
    total: 350000,
    shipping: {
      method: "SiCepat",
      status: "in_transit",
      trackingNumber: "SC987654321",
      eta: "2025-05-17",
    },
  },
  {
    id: "ORD-12343",
    date: "2025-05-05",
    status: "delivered",
    items: [
      { name: "Tas Ra<PERSON>l", quantity: 1, price: 200000, image: "/colorful-backpack-on-wooden-table.png" },
      { name: "Topi Baseball", quantity: 2, price: 75000, image: "/baseball-cap-display.png" },
      { name: "Kaos Polos", quantity: 1, price: 100000, image: "/plain-white-tshirt.png" },
    ],
    total: 450000,
    shipping: {
      method: "J&T Express",
      status: "delivered",
      trackingNumber: "JT123456789",
      eta: "2025-05-08",
      deliveredAt: "2025-05-07",
    },
  },
  {
    id: "ORD-12342",
    date: "2025-04-28",
    status: "cancelled",
    items: [{ name: "Jam Tangan", quantity: 1, price: 450000, image: "/wrist-watch-close-up.png" }],
    total: 450000,
    shipping: {
      method: "JNE Regular",
      status: "cancelled",
      trackingNumber: null,
      eta: null,
    },
    cancellationReason: "Out of stock",
  },
  {
    id: "ORD-12341",
    date: "2025-04-20",
    status: "delivered",
    items: [
      { name: "Hoodie", quantity: 1, price: 300000, image: "/cozy-hoodie.png" },
      { name: "Syal", quantity: 1, price: 100000, image: "/cozy-knit-scarf.png" },
    ],
    total: 400000,
    shipping: {
      method: "AnterAja",
      status: "delivered",
      trackingNumber: "AA123456789",
      eta: "2025-04-25",
      deliveredAt: "2025-04-23",
    },
  },
];

// GET - Mendapatkan semua orders atau difilter berdasarkan query
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const status = searchParams.get('status');
  const query = searchParams.get('query');
  
  let filteredOrders = [...orders];
  
  // Filter berdasarkan status
  if (status && status !== 'all') {
    filteredOrders = filteredOrders.filter(order => order.status === status);
  }
  
  // Filter berdasarkan query pencarian
  if (query) {
    const lowerQuery = query.toLowerCase();
    filteredOrders = filteredOrders.filter(order => 
      order.id.toLowerCase().includes(lowerQuery) || 
      order.items.some(item => item.name.toLowerCase().includes(lowerQuery))
    );
  }
  
  return NextResponse.json(filteredOrders);
}

// POST - Membuat order baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validasi dasar
    if (!body.items || !body.items.length) {
      return NextResponse.json({ error: 'Items diperlukan' }, { status: 400 });
    }
    
    // Buat ID order baru
    const newOrderId = `ORD-${Math.floor(10000 + Math.random() * 90000)}`;
    
    // Hitung total
    const total = body.items.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
    
    // Buat order baru
    const newOrder = {
      id: newOrderId,
      date: new Date().toISOString().split('T')[0],
      status: 'processing',
      items: body.items,
      total,
      shipping: {
        method: body.shipping?.method || 'Standard',
        status: 'preparing',
        trackingNumber: body.shipping?.trackingNumber || null,
        eta: body.shipping?.eta || null,
      }
    };
    
    // Tambahkan ke array orders
    orders.unshift(newOrder);
    
    return NextResponse.json(newOrder, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: 'Terjadi kesalahan server' }, { status: 500 });
  }
} 