"use client"

import { useState, useEffect } from "react"
import { Heart } from "lucide-react"
import { Button } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { DeleteConfirmationDialog } from "@/components/buyer/delete-confirmation-dialog"

interface AddToWishlistProps {
  productId: string
  productName: string
  productPrice: number
  productImage: string
  storeId: string
  storeName: string
  inStock?: boolean
  originalPrice?: number
  size?: "icon" | "sm" | "default" | "lg"
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  className?: string
}

export function AddToWishlist({
  productId,
  productName,
  productPrice,
  productImage,
  storeId,
  storeName,
  inStock = true,
  originalPrice,
  size = "default",
  variant = "outline",
  className = "",
}: AddToWishlistProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isInWishlist, setIsInWishlist] = useState(false)

  // Fungsi untuk memeriksa apakah produk sudah ada di wishlist
  const checkWishlist = async () => {
    try {
      const response = await fetch(`/api/wishlist?productId=${productId}`)
      if (!response.ok) {
        return false
      }
      const data = await response.json()
      return data.length > 0
    } catch (error) {
      console.error("Error checking wishlist:", error)
      return false
    }
  }

  // Fungsi untuk menambahkan produk ke wishlist
  const addToWishlist = async () => {
    setIsLoading(true)
    try {
      // Cek dulu apakah produk sudah ada di wishlist
      const alreadyInWishlist = await checkWishlist()
      if (alreadyInWishlist) {
        setIsInWishlist(true)
        toast({
          title: "Info",
          description: "Produk sudah ada di wishlist Anda",
        })
        return
      }

      // Tambahkan ke wishlist
      const response = await fetch('/api/wishlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          name: productName,
          price: productPrice,
          originalPrice: originalPrice || productPrice,
          image: productImage,
          inStock,
          store: storeName,
          storeId,
          priceDropped: originalPrice ? originalPrice > productPrice : false,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        if (response.status === 409) {
          // Produk sudah ada di wishlist
          setIsInWishlist(true)
          toast({
            title: "Info",
            description: "Produk sudah ada di wishlist Anda",
          })
          return
        }
        throw new Error(data.error || 'Gagal menambahkan ke wishlist')
      }

      // Berhasil ditambahkan
      setIsInWishlist(true)
      toast({
        title: "Berhasil",
        description: "Produk berhasil ditambahkan ke wishlist",
      })
    } catch (error) {
      console.error("Error adding to wishlist:", error)
      toast({
        title: "Gagal",
        description: "Gagal menambahkan produk ke wishlist",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fungsi untuk menghapus produk dari wishlist
  const removeFromWishlist = async () => {
    setIsLoading(true)
    try {
      // Cari ID item di wishlist
      const response = await fetch(`/api/wishlist?productId=${productId}`)
      if (!response.ok) {
        throw new Error('Gagal mencari produk di wishlist')
      }
      
      const items = await response.json()
      if (items.length === 0) {
        setIsInWishlist(false)
        return
      }
      
      // Hapus item dari wishlist
      const wishlistItem = items[0]
      const deleteResponse = await fetch(`/api/wishlist/${wishlistItem.id}`, {
        method: 'DELETE',
      })
      
      if (!deleteResponse.ok) {
        throw new Error('Gagal menghapus produk dari wishlist')
      }
      
      // Berhasil dihapus
      setIsInWishlist(false)
      toast({
        title: "Berhasil",
        description: "Produk berhasil dihapus dari wishlist",
      })
    } catch (error) {
      console.error("Error removing from wishlist:", error)
      toast({
        title: "Gagal",
        description: "Gagal menghapus produk dari wishlist",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Periksa status wishlist saat komponen dimuat
  useEffect(() => {
    checkWishlist().then(status => setIsInWishlist(status))
  }, [])

  if (isInWishlist) {
    return (
      <DeleteConfirmationDialog
        title="Hapus dari Wishlist"
        description={`Apakah Anda yakin ingin menghapus ${productName} dari wishlist?`}
        onConfirm={removeFromWishlist}
      >
        <Button
          variant="default"
          size={size}
          className={className}
          disabled={isLoading}
        >
          <Heart className={`${size === "icon" ? "" : "mr-2"} h-4 w-4 fill-current`} />
          {size !== "icon" && "Hapus dari Wishlist"}
        </Button>
      </DeleteConfirmationDialog>
    )
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={addToWishlist}
      disabled={isLoading}
    >
      <Heart className={`${size === "icon" ? "" : "mr-2"} h-4 w-4`} />
      {size !== "icon" && "Tambah ke Wishlist"}
    </Button>
  )
} 