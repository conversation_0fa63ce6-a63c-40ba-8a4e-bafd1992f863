"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from "recharts"
import { useState } from "react"

// Data untuk revenue chart
const revenueData = [
  { name: "<PERSON>", revenue: 5500000, orders: 245 },
  { name: "Feb", revenue: 6300000, orders: 289 },
  { name: "<PERSON>", revenue: 5900000, orders: 267 },
  { name: "<PERSON>", revenue: 7200000, orders: 324 },
  { name: "<PERSON>", revenue: 6800000, orders: 298 },
  { name: "<PERSON>", revenue: 8800000, orders: 389 },
  { name: "Jul", revenue: 9100000, orders: 412 },
  { name: "Agu", revenue: 8500000, orders: 378 },
  { name: "<PERSON>", revenue: 9800000, orders: 445 },
  { name: "<PERSON><PERSON>", revenue: 10200000, orders: 467 },
  { name: "Nov", revenue: 11500000, orders: 523 },
  { name: "<PERSON>", revenue: 12800000, orders: 578 },
]

// Data untuk store performance
const storePerformanceData = [
  { name: "Fashion Store", revenue: 2500000, orders: 156, rating: 4.8 },
  { name: "Electronics Hub", revenue: 2200000, orders: 134, rating: 4.6 },
  { name: "Home & Garden", revenue: 1800000, orders: 98, rating: 4.7 },
  { name: "Sports Corner", revenue: 1600000, orders: 87, rating: 4.5 },
  { name: "Beauty Shop", revenue: 1400000, orders: 76, rating: 4.9 },
  { name: "Books & More", revenue: 1200000, orders: 65, rating: 4.4 },
  { name: "Toys World", revenue: 1000000, orders: 54, rating: 4.6 },
  { name: "Food Market", revenue: 900000, orders: 48, rating: 4.3 },
]

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
  }).format(value)
}

const formatNumber = (value: number) => {
  return new Intl.NumberFormat("id-ID").format(value)
}

export function RevenueChart() {
  const [period, setPeriod] = useState("12months")
  const [metric, setMetric] = useState("revenue")

  return (
    <Card className="col-span-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Revenue Overview</CardTitle>
            <CardDescription>
              Grafik revenue dan pesanan dari waktu ke waktu
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Select value={metric} onValueChange={setMetric}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="revenue">Revenue</SelectItem>
                <SelectItem value="orders">Orders</SelectItem>
              </SelectContent>
            </Select>
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7days">7 Hari</SelectItem>
                <SelectItem value="30days">30 Hari</SelectItem>
                <SelectItem value="3months">3 Bulan</SelectItem>
                <SelectItem value="12months">12 Bulan</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pl-2">
        <ResponsiveContainer width="100%" height={350}>
          <LineChart data={revenueData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="name" 
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => 
                metric === "revenue" ? `${(value / 1000000).toFixed(0)}M` : formatNumber(value)
              }
            />
            <Tooltip
              content={({ active, payload, label }) => {
                if (active && payload && payload.length) {
                  return (
                    <div className="rounded-lg border bg-background p-2 shadow-sm">
                      <div className="grid grid-cols-2 gap-2">
                        <div className="flex flex-col">
                          <span className="text-[0.70rem] uppercase text-muted-foreground">
                            {label}
                          </span>
                          <span className="font-bold text-muted-foreground">
                            {metric === "revenue" 
                              ? formatCurrency(payload[0].value as number)
                              : `${formatNumber(payload[0].value as number)} pesanan`
                            }
                          </span>
                        </div>
                      </div>
                    </div>
                  )
                }
                return null
              }}
            />
            <Line
              type="monotone"
              dataKey={metric}
              strokeWidth={2}
              stroke="hsl(var(--primary))"
              dot={{
                fill: "hsl(var(--primary))",
                strokeWidth: 2,
                r: 4
              }}
              activeDot={{
                r: 6,
                stroke: "hsl(var(--primary))",
                strokeWidth: 2
              }}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

export function StorePerformanceChart() {
  const [sortBy, setSortBy] = useState("revenue")

  const sortedData = [...storePerformanceData].sort((a, b) => {
    if (sortBy === "revenue") return b.revenue - a.revenue
    if (sortBy === "orders") return b.orders - a.orders
    if (sortBy === "rating") return b.rating - a.rating
    return 0
  })

  return (
    <Card className="col-span-3">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Top Store Performance</CardTitle>
            <CardDescription>
              Performa toko terbaik berdasarkan berbagai metrik
            </CardDescription>
          </div>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="revenue">Revenue</SelectItem>
              <SelectItem value="orders">Orders</SelectItem>
              <SelectItem value="rating">Rating</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={350}>
          <BarChart data={sortedData.slice(0, 6)} layout="horizontal">
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              type="number"
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => {
                if (sortBy === "revenue") return `${(value / 1000000).toFixed(1)}M`
                if (sortBy === "rating") return value.toFixed(1)
                return formatNumber(value)
              }}
            />
            <YAxis
              dataKey="name"
              type="category"
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              width={100}
            />
            <Tooltip
              content={({ active, payload, label }) => {
                if (active && payload && payload.length) {
                  const data = payload[0].payload
                  return (
                    <div className="rounded-lg border bg-background p-3 shadow-sm">
                      <div className="grid gap-2">
                        <div className="font-medium">{label}</div>
                        <div className="grid grid-cols-1 gap-1 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Revenue:</span>
                            <span className="font-medium">{formatCurrency(data.revenue)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Orders:</span>
                            <span className="font-medium">{formatNumber(data.orders)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Rating:</span>
                            <span className="font-medium">{data.rating}/5.0</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                }
                return null
              }}
            />
            <Bar
              dataKey={sortBy}
              fill="hsl(var(--primary))"
              radius={[0, 4, 4, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
} 