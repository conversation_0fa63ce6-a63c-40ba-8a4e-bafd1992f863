"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>, Share2, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  <PERSON>alog<PERSON><PERSON>le,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>bs<PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"

interface ShareCollectionProps {
  collectionId: string
  collectionName: string
  isPublic: boolean
  children: React.ReactNode
}

export function ShareCollection({
  collectionId,
  collectionName,
  isPublic,
  children,
}: ShareCollectionProps) {
  const [open, setOpen] = useState(false)
  const [copied, setCopied] = useState(false)
  const [activeTab, setActiveTab] = useState<string>("link")

  const shareUrl = `${window.location.origin}/shared/collections/${collectionId}`

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    
    setTimeout(() => {
      setCopied(false)
    }, 2000)
    
    toast({
      title: "Berhasil disalin",
      description: "Link koleksi telah disalin ke clipboard",
    })
  }

  const handleShareViaEmail = () => {
    const subject = encodeURIComponent(`Koleksi: ${collectionName}`)
    const body = encodeURIComponent(`Lihat koleksi "${collectionName}" di sini: ${shareUrl}`)
    window.open(`mailto:?subject=${subject}&body=${body}`)
    setOpen(false)
  }

  const handleShareViaSocial = (platform: string) => {
    let url = ""
    const text = encodeURIComponent(`Lihat koleksi "${collectionName}"`)
    
    switch (platform) {
      case "whatsapp":
        url = `https://api.whatsapp.com/send?text=${text} ${encodeURIComponent(shareUrl)}`
        break
      case "telegram":
        url = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${text}`
        break
      case "twitter":
        url = `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${text}`
        break
      case "facebook":
        url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`
        break
    }
    
    if (url) {
      window.open(url, "_blank")
      setOpen(false)
    }
  }

  if (!isPublic) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Koleksi Privat</DialogTitle>
            <DialogDescription>
              Koleksi ini bersifat privat dan tidak dapat dibagikan. 
              Ubah pengaturan koleksi menjadi publik untuk membagikannya.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-center">
            <Button variant="outline" onClick={() => setOpen(false)}>
              <X className="mr-2 h-4 w-4" />
              Tutup
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Bagikan Koleksi</DialogTitle>
          <DialogDescription>
            Bagikan koleksi "{collectionName}" dengan teman Anda.
          </DialogDescription>
        </DialogHeader>
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="link">Link</TabsTrigger>
            <TabsTrigger value="social">Media Sosial</TabsTrigger>
          </TabsList>
          <TabsContent value="link" className="mt-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="grid flex-1 gap-2">
                  <Label htmlFor="link" className="sr-only">
                    Link
                  </Label>
                  <Input
                    id="link"
                    defaultValue={shareUrl}
                    readOnly
                    className="h-9"
                  />
                </div>
                <Button 
                  size="sm" 
                  className="px-3" 
                  onClick={() => handleCopy(shareUrl)}
                >
                  {copied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <Button 
                variant="outline" 
                className="w-full" 
                onClick={handleShareViaEmail}
              >
                Bagikan via Email
              </Button>
            </div>
          </TabsContent>
          <TabsContent value="social" className="mt-4">
            <div className="grid grid-cols-2 gap-2">
              <Button 
                variant="outline" 
                className="justify-start" 
                onClick={() => handleShareViaSocial("whatsapp")}
              >
                <svg viewBox="0 0 24 24" className="mr-2 h-4 w-4">
                  <path 
                    fill="currentColor" 
                    d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967c-.273-.099-.471-.148-.67.15c-.197.297-.767.966-.94 1.164c-.173.199-.347.223-.644.075c-.297-.15-1.255-.463-2.39-1.475c-.883-.788-1.48-1.761-1.653-2.059c-.173-.297-.018-.458.13-.606c.134-.133.298-.347.446-.52c.149-.174.198-.298.298-.497c.099-.198.05-.371-.025-.52c-.075-.149-.669-1.612-.916-2.207c-.242-.579-.487-.5-.669-.51a12.8 12.8 0 0 0-.57-.01c-.198 0-.52.074-.792.372c-.272.297-1.04 1.016-1.04 2.479c0 1.462 1.065 2.875 1.213 3.074c.149.198 2.096 3.2 5.077 4.487c.709.306 1.262.489 1.694.625c.712.227 1.36.195 1.871.118c.571-.085 1.758-.719 2.006-1.413c.248-.694.248-1.289.173-1.413c-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 0 1-5.031-1.378l-.361-.214l-3.741.982l.998-3.648l-.235-.374a9.86 9.86 0 0 1-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884c2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 0 1 2.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0 0 12.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 0 0 5.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 0 0-3.48-8.413Z"
                  />
                </svg>
                WhatsApp
              </Button>
              <Button 
                variant="outline" 
                className="justify-start" 
                onClick={() => handleShareViaSocial("telegram")}
              >
                <svg viewBox="0 0 24 24" className="mr-2 h-4 w-4">
                  <path 
                    fill="currentColor" 
                    d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12a12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472c-.18 1.898-.962 6.502-1.36 8.627c-.168.9-.499 1.201-.82 1.23c-.696.065-1.225-.46-1.9-.902c-1.056-.693-1.653-1.124-2.678-1.8c-1.185-.78-.417-1.21.258-1.91c.177-.184 3.247-2.977 3.307-3.23c.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345c-.48.33-.913.49-1.302.48c-.428-.008-1.252-.241-1.865-.44c-.752-.245-1.349-.374-1.297-.789c.027-.216.325-.437.893-.663c3.498-1.524 5.83-2.529 6.998-3.014c3.332-1.386 4.025-1.627 4.476-1.635z"
                  />
                </svg>
                Telegram
              </Button>
              <Button 
                variant="outline" 
                className="justify-start" 
                onClick={() => handleShareViaSocial("twitter")}
              >
                <svg viewBox="0 0 24 24" className="mr-2 h-4 w-4">
                  <path 
                    fill="currentColor" 
                    d="M21.543 7.104c.015.211.015.423.015.636c0 6.507-4.954 14.01-14.01 14.01v-.003A13.94 13.94 0 0 1 0 19.539a9.88 9.88 0 0 0 7.287-2.041a4.93 4.93 0 0 1-4.6-3.42a4.916 4.916 0 0 0 2.223-.084A4.926 4.926 0 0 1 .96 9.167v-.062a4.887 4.887 0 0 0 2.235.616A4.928 4.928 0 0 1 1.67 3.148a13.98 13.98 0 0 0 10.15 5.144a4.929 4.929 0 0 1 8.39-4.49a9.868 9.868 0 0 0 3.128-1.196a4.941 4.941 0 0 1-2.165 2.724A9.828 9.828 0 0 0 24 4.555a10.019 10.019 0 0 1-2.457 2.549z"
                  />
                </svg>
                Twitter
              </Button>
              <Button 
                variant="outline" 
                className="justify-start" 
                onClick={() => handleShareViaSocial("facebook")}
              >
                <svg viewBox="0 0 24 24" className="mr-2 h-4 w-4">
                  <path 
                    fill="currentColor" 
                    d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669c1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                  />
                </svg>
                Facebook
              </Button>
            </div>
          </TabsContent>
        </Tabs>
        <DialogFooter className="sm:justify-start">
          <Button variant="outline" onClick={() => setOpen(false)}>
            <X className="mr-2 h-4 w-4" />
            Tutup
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 