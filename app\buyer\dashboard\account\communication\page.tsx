"use client"

import { useState } from "react"
import Link from "next/link"
import { Bell, Mail, MessageSquare, Globe, Clock, Calendar, Info } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"

// Sample email history data
const emailHistory = [
  {
    id: "1",
    subject: "Your Order #12345 has been shipped",
    date: "2023-05-15T10:30:00",
    type: "order_update",
    read: true,
  },
  {
    id: "2",
    subject: "Flash Sale: 50% off on selected items",
    date: "2023-05-10T14:20:00",
    type: "promotion",
    read: true,
  },
  {
    id: "3",
    subject: "New arrivals for summer collection",
    date: "2023-05-05T09:15:00",
    type: "new_product",
    read: false,
  },
  {
    id: "4",
    subject: "Your weekly personalized recommendations",
    date: "2023-04-28T11:45:00",
    type: "recommendation",
    read: true,
  },
]

export default function CommunicationPreferencesPage() {
  const [emailPreferences, setEmailPreferences] = useState({
    orderUpdates: true,
    promotions: true,
    newProducts: false,
    recommendations: true,
  })
  const [pushPreferences, setPushPreferences] = useState({
    orderUpdates: true,
    promotions: false,
    newProducts: false,
    recommendations: false,
  })
  const [smsPreferences, setSmsPreferences] = useState({
    orderUpdates: true,
    promotions: false,
    newProducts: false,
    recommendations: false,
  })
  const [frequency, setFrequency] = useState("weekly")
  const [language, setLanguage] = useState("english")

  const handleEmailToggle = (key: keyof typeof emailPreferences) => {
    setEmailPreferences((prev) => ({ ...prev, [key]: !prev[key] }))
  }

  const handlePushToggle = (key: keyof typeof pushPreferences) => {
    setPushPreferences((prev) => ({ ...prev, [key]: !prev[key] }))
  }

  const handleSmsToggle = (key: keyof typeof smsPreferences) => {
    setSmsPreferences((prev) => ({ ...prev, [key]: !prev[key] }))
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Account Settings</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/buyer/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="communication" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6 lg:w-auto">
          <TabsTrigger value="profile" asChild>
            <Link href="/buyer/dashboard/account/profile">Profile</Link>
          </TabsTrigger>
          <TabsTrigger value="addresses" asChild>
            <Link href="/buyer/dashboard/account/addresses">Addresses</Link>
          </TabsTrigger>
          <TabsTrigger value="payment" asChild>
            <Link href="/buyer/dashboard/account/payment">Payment Methods</Link>
          </TabsTrigger>
          <TabsTrigger value="communication">Communication</TabsTrigger>
          <TabsTrigger value="security" asChild>
            <Link href="/buyer/dashboard/account/security">Security</Link>
          </TabsTrigger>
          <TabsTrigger value="connected" asChild>
            <Link href="/buyer/dashboard/account/connected">Connected Accounts</Link>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="communication" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold">Communication Preferences</h2>
            <p className="text-sm text-muted-foreground">
              Manage how and when you receive notifications, updates, and marketing communications.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {/* Email Preferences */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Mail className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Email Notifications</CardTitle>
                </div>
                <CardDescription>Choose which emails you'd like to receive</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-order-updates">Order Updates</Label>
                    <p className="text-xs text-muted-foreground">
                      Shipping confirmations, delivery updates, and order status changes
                    </p>
                  </div>
                  <Switch
                    id="email-order-updates"
                    checked={emailPreferences.orderUpdates}
                    onCheckedChange={() => handleEmailToggle("orderUpdates")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-promotions">Promotions and Sales</Label>
                    <p className="text-xs text-muted-foreground">
                      Special offers, discounts, and limited-time promotions
                    </p>
                  </div>
                  <Switch
                    id="email-promotions"
                    checked={emailPreferences.promotions}
                    onCheckedChange={() => handleEmailToggle("promotions")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-new-products">New Product Announcements</Label>
                    <p className="text-xs text-muted-foreground">
                      Updates about new products, collections, and features
                    </p>
                  </div>
                  <Switch
                    id="email-new-products"
                    checked={emailPreferences.newProducts}
                    onCheckedChange={() => handleEmailToggle("newProducts")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-recommendations">Personalized Recommendations</Label>
                    <p className="text-xs text-muted-foreground">
                      Product suggestions based on your browsing and purchase history
                    </p>
                  </div>
                  <Switch
                    id="email-recommendations"
                    checked={emailPreferences.recommendations}
                    onCheckedChange={() => handleEmailToggle("recommendations")}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Push Notifications */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Bell className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Push Notifications</CardTitle>
                </div>
                <CardDescription>Manage notifications on your devices</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="push-order-updates">Order Updates</Label>
                    <p className="text-xs text-muted-foreground">
                      Get notified about changes to your orders in real-time
                    </p>
                  </div>
                  <Switch
                    id="push-order-updates"
                    checked={pushPreferences.orderUpdates}
                    onCheckedChange={() => handlePushToggle("orderUpdates")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="push-promotions">Promotions and Sales</Label>
                    <p className="text-xs text-muted-foreground">Be the first to know about special offers and deals</p>
                  </div>
                  <Switch
                    id="push-promotions"
                    checked={pushPreferences.promotions}
                    onCheckedChange={() => handlePushToggle("promotions")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="push-new-products">New Product Announcements</Label>
                    <p className="text-xs text-muted-foreground">Get alerts when new products are available</p>
                  </div>
                  <Switch
                    id="push-new-products"
                    checked={pushPreferences.newProducts}
                    onCheckedChange={() => handlePushToggle("newProducts")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="push-recommendations">Personalized Recommendations</Label>
                    <p className="text-xs text-muted-foreground">Receive suggestions tailored to your interests</p>
                  </div>
                  <Switch
                    id="push-recommendations"
                    checked={pushPreferences.recommendations}
                    onCheckedChange={() => handlePushToggle("recommendations")}
                  />
                </div>
              </CardContent>
            </Card>

            {/* SMS Notifications */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">SMS Notifications</CardTitle>
                </div>
                <CardDescription>Manage text message alerts</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms-order-updates">Order Updates</Label>
                    <p className="text-xs text-muted-foreground">Receive text messages about your order status</p>
                  </div>
                  <Switch
                    id="sms-order-updates"
                    checked={smsPreferences.orderUpdates}
                    onCheckedChange={() => handleSmsToggle("orderUpdates")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms-promotions">Promotions and Sales</Label>
                    <p className="text-xs text-muted-foreground">Get text alerts about special offers and deals</p>
                  </div>
                  <Switch
                    id="sms-promotions"
                    checked={smsPreferences.promotions}
                    onCheckedChange={() => handleSmsToggle("promotions")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms-new-products">New Product Announcements</Label>
                    <p className="text-xs text-muted-foreground">Receive texts about new product launches</p>
                  </div>
                  <Switch
                    id="sms-new-products"
                    checked={smsPreferences.newProducts}
                    onCheckedChange={() => handleSmsToggle("newProducts")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms-recommendations">Personalized Recommendations</Label>
                    <p className="text-xs text-muted-foreground">Get text messages with product recommendations</p>
                  </div>
                  <Switch
                    id="sms-recommendations"
                    checked={smsPreferences.recommendations}
                    onCheckedChange={() => handleSmsToggle("recommendations")}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Communication Settings */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Info className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">General Settings</CardTitle>
                </div>
                <CardDescription>Adjust your communication preferences</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="frequency">Communication Frequency</Label>
                  <div className="space-y-2">
                    <RadioGroup
                      defaultValue={frequency}
                      onValueChange={setFrequency}
                      className="flex flex-col space-y-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="daily" id="daily" />
                        <Label htmlFor="daily" className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          Daily
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="weekly" id="weekly" />
                        <Label htmlFor="weekly" className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          Weekly
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="monthly" id="monthly" />
                        <Label htmlFor="monthly" className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          Monthly
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="language">Preferred Language</Label>
                  <Select defaultValue={language} onValueChange={setLanguage}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="english">
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4" />
                          English
                        </div>
                      </SelectItem>
                      <SelectItem value="indonesian">
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4" />
                          Indonesian
                        </div>
                      </SelectItem>
                      <SelectItem value="chinese">
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4" />
                          Chinese
                        </div>
                      </SelectItem>
                      <SelectItem value="japanese">
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4" />
                          Japanese
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between pt-2">
                  <div className="space-y-0.5">
                    <Label htmlFor="newsletter">Newsletter Subscription</Label>
                    <p className="text-xs text-muted-foreground">Receive our weekly newsletter with curated content</p>
                  </div>
                  <Switch id="newsletter" defaultChecked />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Email History */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Mail className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Email History</CardTitle>
              </div>
              <CardDescription>View your recent email communications</CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
                {emailHistory.map((email) => (
                  <AccordionItem key={email.id} value={email.id}>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex w-full items-center justify-between pr-4 text-left">
                        <div className="flex items-center gap-2">
                          <div className="font-medium">{email.subject}</div>
                          {!email.read && (
                            <Badge variant="default" className="ml-2">
                              New
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(email.date).toLocaleDateString("id-ID")}
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="rounded-md bg-muted p-4">
                        <p className="text-sm">
                          This is a preview of the email content. Click the button below to view the full email in your
                          browser.
                        </p>
                        <Button variant="outline" size="sm" className="mt-2">
                          View Full Email
                        </Button>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button>Save Preferences</Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
