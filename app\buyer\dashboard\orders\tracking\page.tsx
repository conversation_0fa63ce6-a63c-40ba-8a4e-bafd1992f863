"use client"

import { useState, useEffect } from "react"
import { ArrowLeft, Search, Truck, Package, CheckCircle, XCircle, Clock } from "lucide-react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"

export default function OrderTrackingPage() {
  const router = useRouter()
  const [trackingNumber, setTrackingNumber] = useState("")
  const [trackingResult, setTrackingResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  // Mengambil nomor tracking dari query parameter
  useEffect(() => {
    // Mendapatkan query parameters
    const params = new URLSearchParams(window.location.search);
    const trackingParam = params.get('tracking');
    
    if (trackingParam) {
      setTrackingNumber(trackingParam);
      // Otomatis mencari tracking jika ada parameter
      searchTracking(trackingParam);
    }
  }, []);

  // Fungsi untuk mencari pesanan berdasarkan nomor resi
  const searchTracking = async (number?: string) => {
    const trackingToSearch = number || trackingNumber;
    
    if (!trackingToSearch.trim()) {
      toast({
        title: "Error",
        description: "Silakan masukkan nomor resi",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/orders/tracking?tracking_number=${encodeURIComponent(trackingToSearch)}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal melacak pesanan');
      }
      
      const data = await response.json();
      setTrackingResult(data);
    } catch (error) {
      console.error("Error tracking order:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Gagal melacak pesanan. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="capitalize">
            <Clock className="mr-1 h-3 w-3" />
            Menunggu
          </Badge>
        )
      case "processing":
        return (
          <Badge variant="default" className="capitalize">
            <Package className="mr-1 h-3 w-3" />
            Diproses
          </Badge>
        )
      case "in_transit":
        return (
          <Badge variant="secondary" className="capitalize">
            <Truck className="mr-1 h-3 w-3" />
            Dalam Pengiriman
          </Badge>
        )
      case "delivered":
        return (
          <Badge variant="outline" className="border-green-500 text-green-500 capitalize">
            <CheckCircle className="mr-1 h-3 w-3" />
            Terkirim
          </Badge>
        )
      case "cancelled":
        return (
          <Badge variant="destructive" className="capitalize">
            <XCircle className="mr-1 h-3 w-3" />
            Dibatalkan
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="capitalize">
            {status}
          </Badge>
        )
    }
  }

  // Format tanggal ke format Indonesia
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      day: "numeric",
      month: "long",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }
    return new Date(dateString).toLocaleDateString("id-ID", options)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Lacak Pesanan</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Masukkan Nomor Resi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="Masukkan nomor resi pengiriman"
              value={trackingNumber}
              onChange={(e) => setTrackingNumber(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && searchTracking()}
            />
            <Button onClick={() => searchTracking()} disabled={loading}>
              {loading ? (
                <span className="flex items-center gap-1">
                  <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Mencari...
                </span>
              ) : (
                <span className="flex items-center gap-1">
                  <Search className="h-4 w-4" />
                  Lacak
                </span>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {trackingResult && (
        <Card>
          <CardHeader>
            <CardTitle>Hasil Pelacakan</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Nomor Resi</p>
                <p className="font-medium">{trackingResult.trackingNumber}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Kurir</p>
                <p className="font-medium">{trackingResult.courier}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Status</p>
                <div>{renderStatusBadge(trackingResult.status)}</div>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Asal</p>
                <p className="font-medium">{trackingResult.origin}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Tujuan</p>
                <p className="font-medium">{trackingResult.destination}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Estimasi Tiba</p>
                <p className="font-medium">
                  {trackingResult.estimatedDelivery
                    ? new Date(trackingResult.estimatedDelivery).toLocaleDateString("id-ID")
                    : "Tidak tersedia"}
                </p>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="mb-4 text-lg font-medium">Riwayat Status</h3>
              <div className="relative space-y-6 pl-6 before:absolute before:left-2 before:top-2 before:h-[calc(100%-16px)] before:w-0.5 before:bg-muted">
                {trackingResult.timeline.map((item: any, index: number) => (
                  <div key={index} className="relative">
                    <div className="absolute -left-6 top-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary" />
                    <div className="space-y-1">
                      <div className="flex flex-wrap items-center gap-2">
                        <p className="font-medium">{item.status}</p>
                        <p className="text-sm text-muted-foreground">{formatDate(item.date)}</p>
                      </div>
                      <p className="text-sm text-muted-foreground">Lokasi: {item.location}</p>
                      <p className="text-sm">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 