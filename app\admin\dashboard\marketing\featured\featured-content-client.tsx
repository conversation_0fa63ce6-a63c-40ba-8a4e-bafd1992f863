"use client"
import dynamic from "next/dynamic"

// Import the component with client-side only rendering
const FeaturedContent = dynamic(() => import("@/components/admin/marketing/featured-content"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center min-h-[60vh]">
      <div className="text-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
        <p className="text-muted-foreground">Loading featured content...</p>
      </div>
    </div>
  ),
})

export default function FeaturedContentClient() {
  return <FeaturedContent />
}
