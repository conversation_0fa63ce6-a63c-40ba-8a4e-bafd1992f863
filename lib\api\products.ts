import axios from "axios"

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"

// Fungsi untuk mendapatkan token dari localStorage
const getToken = () => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("token")
  }
  return null
}

// Fungsi untuk membuat header dengan token
const getHeaders = () => {
  const token = getToken()
  return {
    headers: {
      Authorization: token ? `Bearer ${token}` : "",
    },
  }
}

// Interface untuk tipe data produk
export interface Product {
  id: string
  name: string
  description: string | null
  price: number
  images: string[]
  storeId: string
  creatorId: string
  createdAt: string
  updatedAt: string
  store?: {
    id: string
    name: string
    slug: string
  }
  creator?: {
    id: string
    name: string
    email: string
  }
}

// Interface untuk data yang diperlukan saat membuat produk
export interface CreateProductData {
  name: string
  description?: string
  price: number
  images?: string[]
  storeId: string
}

// Interface untuk data yang diperlukan saat mengupdate produk
export interface UpdateProductData {
  name?: string
  description?: string
  price?: number
  images?: string[]
  storeId?: string
}

// API untuk produk
export const productsAPI = {
  // Mendapatkan semua produk
  getAll: async (storeId?: string): Promise<Product[]> => {
    let url = `${API_URL}/products`
    if (storeId) {
      url += `?storeId=${storeId}`
    }
    const response = await axios.get(url, getHeaders())
    return response.data
  },

  // Mendapatkan produk berdasarkan ID
  getById: async (id: string): Promise<Product> => {
    const response = await axios.get(`${API_URL}/products/${id}`, getHeaders())
    return response.data
  },

  // Membuat produk baru
  create: async (data: CreateProductData): Promise<Product> => {
    const response = await axios.post(`${API_URL}/products`, data, getHeaders())
    return response.data
  },

  // Mengupdate produk
  update: async (id: string, data: UpdateProductData): Promise<Product> => {
    const response = await axios.patch(`${API_URL}/products/${id}`, data, getHeaders())
    return response.data
  },

  // Menghapus produk
  delete: async (id: string): Promise<void> => {
    await axios.delete(`${API_URL}/products/${id}`, getHeaders())
  },
}
