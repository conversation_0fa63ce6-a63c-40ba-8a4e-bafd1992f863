"use client"

import { useState } from "react"
import { DataTable } from "@/components/ui/data-table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Co<PERSON>, Trash, Edit, ExternalLink, <PERSON><PERSON><PERSON> } from "lucide-react"
import { formatCurrency } from "@/lib/utils"

export function AffiliateLinksTable() {
  const [searchTerm, setSearchTerm] = useState("")

  // Data dummy untuk tabel link
  const links = [
    {
      id: "1",
      name: "Sepatu Running Nike Air Zoom",
      url: "https://sellzio.com/product/sepatu-running-nike-air-zoom?ref=USER123",
      shortUrl: "https://szio.co/a1b2c3",
      type: "product",
      createdAt: "2023-05-01",
      clicks: 120,
      conversions: 12,
      revenue: 4200000,
      commission: 420000,
      conversionRate: 10,
      status: "active",
    },
    {
      id: "2",
      name: "T-Shirt Adidas Original",
      url: "https://sellzio.com/product/t-shirt-adidas-original?ref=USER123",
      shortUrl: "https://szio.co/d4e5f6",
      type: "product",
      createdAt: "2023-05-05",
      clicks: 85,
      conversions: 7,
      revenue: 1750000,
      commission: 175000,
      conversionRate: 8.2,
      status: "active",
    },
    {
      id: "3",
      name: "Fashion Store",
      url: "https://sellzio.com/store/fashion-store?ref=USER123",
      shortUrl: "https://szio.co/g7h8i9",
      type: "store",
      createdAt: "2023-05-10",
      clicks: 210,
      conversions: 18,
      revenue: 5400000,
      commission: 540000,
      conversionRate: 8.6,
      status: "active",
    },
    {
      id: "4",
      name: "Promo Lebaran",
      url: "https://sellzio.com/promo/lebaran?ref=USER123",
      shortUrl: "https://szio.co/j0k1l2",
      type: "custom",
      createdAt: "2023-05-15",
      clicks: 320,
      conversions: 28,
      revenue: 8400000,
      commission: 840000,
      conversionRate: 8.8,
      status: "active",
    },
    {
      id: "5",
      name: "Jam Tangan Casio G-Shock",
      url: "https://sellzio.com/product/jam-tangan-casio-g-shock?ref=USER123",
      shortUrl: "https://szio.co/m3n4o5",
      type: "product",
      createdAt: "2023-05-20",
      clicks: 65,
      conversions: 5,
      revenue: 2500000,
      commission: 250000,
      conversionRate: 7.7,
      status: "inactive",
    },
  ]

  // Filter links berdasarkan search term
  const filteredLinks = links.filter(
    (link) =>
      link.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      link.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
      link.shortUrl.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  // Definisikan kolom untuk tabel
  const columns = [
    {
      header: "Nama",
      accessorKey: "name",
      cell: ({ row }) => (
        <div className="flex flex-col">
          <span className="font-medium">{row.original.name}</span>
          <span className="text-xs text-muted-foreground">{row.original.shortUrl}</span>
        </div>
      ),
    },
    {
      header: "Tipe",
      accessorKey: "type",
      cell: ({ row }) => (
        <Badge variant="outline" className="capitalize">
          {row.original.type}
        </Badge>
      ),
    },
    {
      header: "Klik",
      accessorKey: "clicks",
    },
    {
      header: "Konversi",
      accessorKey: "conversions",
    },
    {
      header: "Rate",
      accessorKey: "conversionRate",
      cell: ({ row }) => `${row.original.conversionRate}%`,
    },
    {
      header: "Revenue",
      accessorKey: "revenue",
      cell: ({ row }) => formatCurrency(row.original.revenue),
    },
    {
      header: "Komisi",
      accessorKey: "commission",
      cell: ({ row }) => formatCurrency(row.original.commission),
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }) => (
        <Badge variant={row.original.status === "active" ? "default" : "secondary"} className="capitalize">
          {row.original.status}
        </Badge>
      ),
    },
    {
      header: "Aksi",
      id: "actions",
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <Button variant="ghost" size="icon" onClick={() => copyToClipboard(row.original.shortUrl)}>
            <Copy className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" asChild>
            <a href={row.original.url} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4" />
            </a>
          </Button>
          <Button variant="ghost" size="icon">
            <BarChart className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <Trash className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ]

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // Bisa tambahkan toast notification di sini
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Input
          placeholder="Cari link..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
        <Button>Tambah Link Baru</Button>
      </div>

      <DataTable columns={columns} data={filteredLinks} searchColumn="name" searchPlaceholder="Cari link..." />
    </div>
  )
}
