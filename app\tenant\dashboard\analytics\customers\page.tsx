"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  BarChart3,
  LineChart,
  PieChart,
  Users,
  UserPlus,
  TrendingUp,
  TrendingDown,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Download,
  Filter,
  CalendarDays,
  MapPin,
  Globe,
  Smartphone,
  ShoppingBag,
  Heart,
  Timer
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Data dummy untuk metrik pelanggan
const customerMetrics = {
  totalCustomers: 8750,
  newCustomers: 345,
  returningCustomers: 685,
  customerGrowth: 8.2,
  churnRate: 2.5,
  averageOrderValue: 520000,
  customerLifetimeValue: 2450000,
  acquisitionCost: 85000
}

// Data dummy untuk tren pelanggan
const customerTrend = {
  months: ["Jan", "Feb", "<PERSON>", "Apr", "<PERSON>", "<PERSON>"],
  total: [7850, 8050, 8250, 8450, 8650, 8750],
  new: [220, 235, 270, 225, 310, 345],
  returning: [580, 610, 635, 650, 670, 685]
}

// Data dummy untuk pelanggan setia
const loyalCustomers = [
  {
    id: "cust-001",
    name: "Budi <PERSON>o",
    email: "<EMAIL>",
    orders: 24,
    totalSpent: 12500000,
    lastPurchase: "2024-05-18T09:30:00",
    joinedDate: "2022-06-10T14:20:00",
    averageOrderValue: 520833
  },
  {
    id: "cust-002",
    name: "Siti Rahma",
    email: "<EMAIL>",
    orders: 18,
    totalSpent: 9650000,
    lastPurchase: "2024-05-15T16:45:00",
    joinedDate: "2022-08-05T11:10:00",
    averageOrderValue: 536111
  },
  {
    id: "cust-003",
    name: "Andi Wijaya",
    email: "<EMAIL>",
    orders: 15,
    totalSpent: 8750000,
    lastPurchase: "2024-05-12T14:20:00",
    joinedDate: "2022-10-12T09:45:00",
    averageOrderValue: 583333
  },
  {
    id: "cust-004",
    name: "Dewi Lestari",
    email: "<EMAIL>",
    orders: 12,
    totalSpent: 7250000,
    lastPurchase: "2024-05-09T10:15:00",
    joinedDate: "2023-01-15T13:30:00",
    averageOrderValue: 604167
  },
  {
    id: "cust-005",
    name: "Rudi Hartono",
    email: "<EMAIL>",
    orders: 10,
    totalSpent: 5450000,
    lastPurchase: "2024-05-05T15:30:00",
    joinedDate: "2023-03-22T16:15:00",
    averageOrderValue: 545000
  }
]

// Data dummy untuk segmentasi pelanggan
const customerSegments = [
  {
    name: "VIP",
    count: 120,
    percentage: 1.4,
    averageOrderValue: 1250000,
    averageOrderFrequency: 5.2
  },
  {
    name: "Loyal",
    count: 850,
    percentage: 9.7,
    averageOrderValue: 750000,
    averageOrderFrequency: 3.8
  },
  {
    name: "Reguler",
    count: 2350,
    percentage: 26.9,
    averageOrderValue: 450000,
    averageOrderFrequency: 2.1
  },
  {
    name: "Sesekali",
    count: 3850,
    percentage: 44.0,
    averageOrderValue: 320000,
    averageOrderFrequency: 1.2
  },
  {
    name: "Baru",
    count: 1580,
    percentage: 18.0,
    averageOrderValue: 480000,
    averageOrderFrequency: 1.0
  }
]

// Data dummy untuk demografi pelanggan
const customerDemographics = {
  age: [
    { label: "18-24", value: 22 },
    { label: "25-34", value: 38 },
    { label: "35-44", value: 25 },
    { label: "45-54", value: 10 },
    { label: "55+", value: 5 }
  ],
  gender: [
    { label: "Pria", value: 45 },
    { label: "Wanita", value: 54 },
    { label: "Lainnya", value: 1 }
  ],
  location: [
    { label: "Jakarta", value: 35 },
    { label: "Surabaya", value: 18 },
    { label: "Bandung", value: 15 },
    { label: "Medan", value: 8 },
    { label: "Lainnya", value: 24 }
  ],
  device: [
    { label: "Mobile", value: 68 },
    { label: "Desktop", value: 28 },
    { label: "Tablet", value: 4 }
  ]
}

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Format mata uang
function formatCurrency(number: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(number)
}

// Format persentase
function formatPercentage(number: number) {
  return `${number.toFixed(1)}%`
}

// Format angka dengan pemisah ribuan
function formatNumber(number: number) {
  return number.toLocaleString('id-ID')
}

// Fungsi untuk menghitung hari sejak tanggal tertentu
function daysSince(dateString: string) {
  const date = new Date(dateString)
  const today = new Date()
  const diffTime = Math.abs(today.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

export default function CustomerAnalyticsPage() {
  const [dateFilter, setDateFilter] = useState("last-6-months")
  const [selectedTab, setSelectedTab] = useState("overview")

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/analytics">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analitik Pelanggan</h1>
            <p className="text-muted-foreground">
              Analisis perilaku dan demografi pelanggan Anda
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Select
            value={dateFilter}
            onValueChange={setDateFilter}
          >
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <CalendarDays className="h-4 w-4 mr-2" />
                <span>Rentang Waktu</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last-7-days">7 Hari Terakhir</SelectItem>
              <SelectItem value="last-30-days">30 Hari Terakhir</SelectItem>
              <SelectItem value="last-3-months">3 Bulan Terakhir</SelectItem>
              <SelectItem value="last-6-months">6 Bulan Terakhir</SelectItem>
              <SelectItem value="last-year">1 Tahun Terakhir</SelectItem>
              <SelectItem value="all-time">Sepanjang Waktu</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Ekspor Data
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="overview" value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="w-full md:w-auto">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="segments">Segmentasi</TabsTrigger>
          <TabsTrigger value="demographics">Demografi</TabsTrigger>
          <TabsTrigger value="loyalty">Loyalitas</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab Content */}
        <TabsContent value="overview" className="space-y-6">
          {/* Metrik Utama */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Pelanggan</CardTitle>
                <Users className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{formatNumber(customerMetrics.totalCustomers)}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <TrendingUp className="mr-1 h-3.5 w-3.5 text-green-600" />
                  <span className="text-green-600 font-medium">{formatPercentage(customerMetrics.customerGrowth)}</span>
                  <span className="ml-1">dibanding periode sebelumnya</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pelanggan Baru</CardTitle>
                <UserPlus className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{formatNumber(customerMetrics.newCustomers)}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <span>dalam {dateFilter === "last-6-months" ? "6 bulan" : dateFilter === "last-30-days" ? "30 hari" : "periode ini"}</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tingkat Churn</CardTitle>
                <TrendingDown className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{formatPercentage(customerMetrics.churnRate)}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <ArrowDownRight className="mr-1 h-3.5 w-3.5 text-green-600" />
                  <span className="text-green-600 font-medium">0.3%</span>
                  <span className="ml-1">dibanding periode sebelumnya</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Nilai Pelanggan</CardTitle>
                <BarChart3 className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{formatCurrency(customerMetrics.customerLifetimeValue)}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <span>LTV rata-rata per pelanggan</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Grafik Tren Pelanggan dan Nilai Akuisisi */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Tren Pelanggan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <LineChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                    <p>Grafik tren pelanggan akan ditampilkan di sini</p>
                    <p className="text-xs">Data untuk {customerTrend.months.length} bulan terakhir</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Nilai & Biaya Pelanggan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Customer Lifetime Value (LTV)</h3>
                    <div className="flex justify-between items-center">
                      <div className="text-2xl font-bold">{formatCurrency(customerMetrics.customerLifetimeValue)}</div>
                      <div className="flex items-center text-xs">
                        <TrendingUp className="mr-1 h-3.5 w-3.5 text-green-600" />
                        <span className="text-green-600 font-medium">7.5%</span>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Nilai rata-rata yang dihasilkan selama hubungan dengan pelanggan
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Customer Acquisition Cost (CAC)</h3>
                    <div className="flex justify-between items-center">
                      <div className="text-2xl font-bold">{formatCurrency(customerMetrics.acquisitionCost)}</div>
                      <div className="flex items-center text-xs">
                        <TrendingDown className="mr-1 h-3.5 w-3.5 text-green-600" />
                        <span className="text-green-600 font-medium">3.2%</span>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Biaya rata-rata untuk mendapatkan satu pelanggan baru
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">LTV:CAC Ratio</h3>
                    <div className="flex justify-between items-center">
                      <div className="text-2xl font-bold">{(customerMetrics.customerLifetimeValue / customerMetrics.acquisitionCost).toFixed(1)}:1</div>
                      <div className="flex items-center text-xs">
                        <TrendingUp className="mr-1 h-3.5 w-3.5 text-green-600" />
                        <span className="text-green-600 font-medium">0.8</span>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Rasio yang menunjukkan keuntungan dari investasi akuisisi pelanggan
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Segments Tab Content */}
        <TabsContent value="segments" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Segmentasi Pelanggan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative overflow-x-auto">
                  <table className="w-full text-sm text-left">
                    <thead className="text-xs uppercase bg-muted/50">
                      <tr>
                        <th scope="col" className="px-6 py-3">Segmen</th>
                        <th scope="col" className="px-6 py-3">Jumlah</th>
                        <th scope="col" className="px-6 py-3">Persentase</th>
                        <th scope="col" className="px-6 py-3">Nilai Order</th>
                        <th scope="col" className="px-6 py-3">Frekuensi</th>
                      </tr>
                    </thead>
                    <tbody>
                      {customerSegments.map(segment => (
                        <tr key={segment.name} className="border-b">
                          <td className="px-6 py-4 font-medium">
                            <Link href={`/tenant/dashboard/customers?segment=${segment.name}`} className="hover:underline">
                              {segment.name}
                            </Link>
                          </td>
                          <td className="px-6 py-4">{formatNumber(segment.count)}</td>
                          <td className="px-6 py-4">{formatPercentage(segment.percentage)}</td>
                          <td className="px-6 py-4">{formatCurrency(segment.averageOrderValue)}</td>
                          <td className="px-6 py-4">{segment.averageOrderFrequency.toFixed(1)} / bulan</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Distribusi Segmen</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[250px] flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <PieChart className="h-16 w-16 mx-auto mb-2 opacity-50" />
                    <p>Grafik distribusi segmen akan ditampilkan di sini</p>
                    <p className="text-xs">Persentase pelanggan per segmen</p>
                  </div>
                </div>
                
                <div className="space-y-2 mt-4">
                  {customerSegments.map((segment, index) => {
                    const colors = ["bg-purple-500", "bg-blue-500", "bg-green-500", "bg-yellow-500", "bg-red-500"]
                    return (
                      <div key={segment.name} className="flex items-center">
                        <div className={`w-3 h-3 rounded-full mr-2 ${colors[index]}`}></div>
                        <span className="text-sm">{segment.name}</span>
                        <div className="flex-1 mx-2">
                          <div className="h-2 rounded-full bg-muted overflow-hidden">
                            <div 
                              className={`h-full rounded-full ${colors[index]}`} 
                              style={{ width: `${segment.percentage}%` }}
                            />
                          </div>
                        </div>
                        <span className="text-xs font-medium">
                          {formatPercentage(segment.percentage)}
                        </span>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Performa Segmen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <BarChart3 className="h-16 w-16 mx-auto mb-2 opacity-50" />
                  <p>Grafik performa segmen akan ditampilkan di sini</p>
                  <p className="text-xs">Nilai order rata-rata dan frekuensi belanja per segmen</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Demographics Tab Content */}
        <TabsContent value="demographics" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Demografi Pelanggan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium mb-3">Distribusi Usia</h3>
                    <div className="space-y-2">
                      {customerDemographics.age.map(item => (
                        <div key={item.label} className="flex items-center">
                          <span className="w-10 text-xs">{item.label}</span>
                          <div className="flex-1 mx-2">
                            <div className="h-2 rounded-full bg-muted overflow-hidden">
                              <div 
                                className="h-full bg-blue-600 rounded-full" 
                                style={{ width: `${item.value}%` }}
                              />
                            </div>
                          </div>
                          <span className="text-xs">{item.value}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium mb-3">Distribusi Gender</h3>
                    <div className="space-y-2">
                      {customerDemographics.gender.map(item => (
                        <div key={item.label} className="flex items-center">
                          <span className="w-16 text-xs">{item.label}</span>
                          <div className="flex-1 mx-2">
                            <div className="h-2 rounded-full bg-muted overflow-hidden">
                              <div 
                                className="h-full bg-purple-600 rounded-full" 
                                style={{ width: `${item.value}%` }}
                              />
                            </div>
                          </div>
                          <span className="text-xs">{item.value}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Lokasi dan Perangkat</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium mb-3">Lokasi Teratas</h3>
                    <div className="space-y-2">
                      {customerDemographics.location.map(item => (
                        <div key={item.label} className="flex items-center">
                          <span className="w-20 text-xs">{item.label}</span>
                          <div className="flex-1 mx-2">
                            <div className="h-2 rounded-full bg-muted overflow-hidden">
                              <div 
                                className="h-full bg-green-600 rounded-full" 
                                style={{ width: `${item.value}%` }}
                              />
                            </div>
                          </div>
                          <span className="text-xs">{item.value}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium mb-3">Jenis Perangkat</h3>
                    <div className="space-y-2">
                      {customerDemographics.device.map(item => (
                        <div key={item.label} className="flex items-center">
                          <span className="w-20 text-xs">{item.label}</span>
                          <div className="flex-1 mx-2">
                            <div className="h-2 rounded-full bg-muted overflow-hidden">
                              <div 
                                className="h-full bg-orange-600 rounded-full" 
                                style={{ width: `${item.value}%` }}
                              />
                            </div>
                          </div>
                          <span className="text-xs">{item.value}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Peta Pelanggan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <Globe className="h-16 w-16 mx-auto mb-2 opacity-50" />
                  <p>Peta distribusi pelanggan akan ditampilkan di sini</p>
                  <p className="text-xs">Visualisasi geografis dari basis pelanggan Anda</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Loyalty Tab Content */}
        <TabsContent value="loyalty" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tingkat Retensi</CardTitle>
                <Heart className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">76.8%</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <ArrowUpRight className="mr-1 h-3.5 w-3.5 text-green-600" />
                  <span className="text-green-600 font-medium">2.5%</span>
                  <span className="ml-1">dibanding periode sebelumnya</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pelanggan Berulang</CardTitle>
                <ShoppingBag className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{formatNumber(customerMetrics.returningCustomers)}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <span>{formatPercentage((customerMetrics.returningCustomers / customerMetrics.totalCustomers) * 100)} dari total pelanggan</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Frekuensi Pembelian</CardTitle>
                <Timer className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">1.8x</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  <span>Rata-rata pembelian per pelanggan per bulan</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Pelanggan Setia</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative overflow-x-auto">
                <table className="w-full text-sm text-left">
                  <thead className="text-xs uppercase bg-muted/50">
                    <tr>
                      <th scope="col" className="px-6 py-3">Pelanggan</th>
                      <th scope="col" className="px-6 py-3">Total Pesanan</th>
                      <th scope="col" className="px-6 py-3">Total Belanja</th>
                      <th scope="col" className="px-6 py-3">Nilai Rata-rata</th>
                      <th scope="col" className="px-6 py-3">Keanggotaan</th>
                      <th scope="col" className="px-6 py-3">Terakhir Belanja</th>
                    </tr>
                  </thead>
                  <tbody>
                    {loyalCustomers.map(customer => (
                      <tr key={customer.id} className="border-b">
                        <td className="px-6 py-4 font-medium">
                          <Link href={`/tenant/dashboard/customers/${customer.id}`} className="hover:underline">
                            {customer.name}
                          </Link>
                          <div className="text-xs text-muted-foreground">{customer.email}</div>
                        </td>
                        <td className="px-6 py-4">{customer.orders}</td>
                        <td className="px-6 py-4">{formatCurrency(customer.totalSpent)}</td>
                        <td className="px-6 py-4">{formatCurrency(customer.averageOrderValue)}</td>
                        <td className="px-6 py-4">{daysSince(customer.joinedDate)} hari</td>
                        <td className="px-6 py-4">{formatDate(customer.lastPurchase)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Analisis RFM (Recency, Frequency, Monetary)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <BarChart3 className="h-16 w-16 mx-auto mb-2 opacity-50" />
                  <p>Grafik analisis RFM akan ditampilkan di sini</p>
                  <p className="text-xs">Segmentasi pelanggan berdasarkan kebaruan, frekuensi, dan nilai moneter</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 