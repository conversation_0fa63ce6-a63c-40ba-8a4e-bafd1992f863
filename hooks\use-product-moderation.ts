import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface ProductModeration {
  id: string;
  product_id: string;
  status: 'pending' | 'approved' | 'rejected' | 'under_review' | 'requires_changes';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  reviewer_id?: string;
  review_notes?: string;
  rejection_reason?: string;
  required_changes: string[];
  flags: string[];
  auto_flags: string[];
  review_checklist: Record<string, boolean>;
  submitted_at: string;
  reviewed_at?: string;
  approved_at?: string;
  rejected_at?: string;
  created_at: string;
  updated_at: string;
  // Relations
  product?: {
    id: string;
    name: string;
    sku: string;
    price: number;
    featured_image?: string;
    status: string;
    description?: string;
    images?: string[];
    inventory_quantity?: number;
    tags?: string[];
    store?: {
      id: string;
      name: string;
      owner_name?: string;
    };
  };
  reviewer?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface ProductModerationFilters {
  status?: string;
  priority?: string;
  reviewer_id?: string;
  product_id?: string;
  date_from?: string;
  date_to?: string;
  has_flags?: boolean;
}

export interface ProductModerationCreate {
  product_id: string;
  priority?: ProductModeration['priority'];
  flags?: string[];
  auto_flags?: string[];
  review_checklist?: Record<string, boolean>;
}

export function useProductModeration() {
  const [moderations, setModerations] = useState<ProductModeration[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch moderations with optional filters
  const fetchModerations = useCallback(async (filters?: ProductModerationFilters) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (filters?.status) params.append('status', filters.status);
      if (filters?.priority) params.append('priority', filters.priority);
      if (filters?.reviewer_id) params.append('reviewer_id', filters.reviewer_id);
      if (filters?.product_id) params.append('product_id', filters.product_id);
      if (filters?.date_from) params.append('date_from', filters.date_from);
      if (filters?.date_to) params.append('date_to', filters.date_to);
      if (filters?.has_flags !== undefined) params.append('has_flags', filters.has_flags.toString());

      const response = await fetch(`/api/product-moderation?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch moderations');
      }

      const data = await response.json();
      setModerations(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Get single moderation
  const getModeration = useCallback(async (id: string): Promise<ProductModeration | null> => {
    try {
      const response = await fetch(`/api/product-moderation/${id}`);

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch moderation');
      }

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Create moderation
  const createModeration = useCallback(async (moderationData: ProductModerationCreate): Promise<boolean> => {
    try {
      const response = await fetch('/api/product-moderation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(moderationData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create moderation');
      }

      const newModeration = await response.json();

      // Update local state
      setModerations(prev => [newModeration, ...prev]);

      toast.success('Moderation created successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Update moderation
  const updateModeration = useCallback(async (id: string, updates: Partial<ProductModeration>): Promise<boolean> => {
    try {
      const response = await fetch(`/api/product-moderation/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update moderation');
      }

      const updatedModeration = await response.json();

      // Update local state
      setModerations(prev =>
        prev.map(moderation => moderation.id === id ? updatedModeration : moderation)
      );

      toast.success('Moderation updated successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Approve product
  const approveProduct = useCallback(async (id: string, reviewerId: string, notes?: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/product-moderation/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'approve',
          reviewer_id: reviewerId,
          notes
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve product');
      }

      const updatedModeration = await response.json();

      // Update local state
      setModerations(prev =>
        prev.map(moderation => moderation.id === id ? updatedModeration : moderation)
      );

      toast.success('Product approved successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Reject product
  const rejectProduct = useCallback(async (id: string, reviewerId: string, reason: string, requiredChanges?: string[]): Promise<boolean> => {
    try {
      const response = await fetch(`/api/product-moderation/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'reject',
          reviewer_id: reviewerId,
          reason,
          required_changes: requiredChanges
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject product');
      }

      const updatedModeration = await response.json();

      // Update local state
      setModerations(prev =>
        prev.map(moderation => moderation.id === id ? updatedModeration : moderation)
      );

      toast.success('Product rejected');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Set under review
  const setUnderReview = useCallback(async (id: string, reviewerId: string, notes?: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/product-moderation/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'set_under_review',
          reviewer_id: reviewerId,
          notes
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to set under review');
      }

      const updatedModeration = await response.json();

      // Update local state
      setModerations(prev =>
        prev.map(moderation => moderation.id === id ? updatedModeration : moderation)
      );

      toast.success('Product set under review');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Get moderation statistics
  const getModerationStats = useCallback(async () => {
    try {
      const response = await fetch('/api/product-moderation/stats');

      if (!response.ok) {
        throw new Error('Failed to fetch moderation stats');
      }

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Delete moderation
  const deleteModeration = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/product-moderation/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete moderation');
      }

      // Update local state
      setModerations(prev => prev.filter(moderation => moderation.id !== id));

      toast.success('Moderation deleted successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Refresh moderations (re-fetch with current filters)
  const refreshModerations = useCallback(async () => {
    await fetchModerations();
  }, [fetchModerations]);

  // Initial fetch on mount
  useEffect(() => {
    fetchModerations();
  }, [fetchModerations]);

  return {
    moderations,
    loading,
    error,
    fetchModerations,
    getModeration,
    createModeration,
    updateModeration,
    approveProduct,
    rejectProduct,
    setUnderReview,
    getModerationStats,
    deleteModeration,
    refreshModerations,
  };
}
