import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PlusCircle } from "lucide-react"
import { ProductList } from "@/components/dashboard/products/product-list"

export default function ProductsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Produk</h1>
        <Button asChild>
          <Link href="/dashboard/products/create">
            <PlusCircle className="mr-2 h-4 w-4" />
            Tambah Produk Baru
          </Link>
        </Button>
      </div>

      <ProductList />
    </div>
  )
}
