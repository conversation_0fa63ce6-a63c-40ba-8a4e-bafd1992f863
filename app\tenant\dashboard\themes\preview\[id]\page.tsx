import { Suspense } from "react"
import { notFound } from "next/navigation"
import { ThemePreview } from "@/components/tenant/theme-preview"

interface ThemePreviewPageProps {
  params: {
    id: string
  }
}

export default function ThemePreviewPage({ params }: ThemePreviewPageProps) {
  const { id } = params

  if (!id) {
    notFound()
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Theme Preview: {id}</h1>
      <Suspense fallback={<div>Loading theme preview...</div>}>
        <ThemePreview themeId={id} />
      </Suspense>
    </div>
  )
}
