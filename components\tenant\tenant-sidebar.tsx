"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Store,
  ShoppingBag,
  FileText,
  Users,
  Settings,
  CreditCard,
  BarChart,
  Tag,
  MessageSquare,
  Globe,
  UserCheck,
  Megaphone,
  FileImage,
  Video,
  ChevronDown,
  ChevronRight,
  Palette,
  PaintBucket,
  Layers,
  GanttChartSquare,
  ClipboardList,
  Truck,
  PackageOpen,
  Box,
  BadgePercent,
  Mails,
  Sparkles,
  Contact,
  PieChart,
  ArrowDownUp,
  Receipt,
  Building,
  Newspaper,
  Database,
  Command,
  LifeBuoy,
  Coins,
  CalendarRange,
  Star,
  Home,
  BookOpen,
  Share2
} from "lucide-react"
import { useSidebar } from "@/components/ui/sidebar"

export function TenantSidebar() {
  const pathname = usePathname()
  const router = useRouter()
  const { state: sidebarState } = useSidebar()
  const [isNavigating, setIsNavigating] = useState(false)
  const [isTablet, setIsTablet] = useState(false)
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null)
  const submenuRefs = useRef<{ [key: string]: HTMLDivElement | null }>({})

  useEffect(() => {
    // Deteksi apakah tampilan tablet (768px-1024px)
    const checkIsTablet = () => {
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024)
    }
    
    // Cek saat load pertama
    checkIsTablet()
    
    // Tambahkan event listener untuk resize
    window.addEventListener('resize', checkIsTablet)
    
    // Cleanup listener
    return () => window.removeEventListener('resize', checkIsTablet)
  }, [])

  // Tutup submenu saat klik di luar
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (activeSubmenu && submenuRefs.current[activeSubmenu]) {
        const submenuEl = submenuRefs.current[activeSubmenu];
        if (submenuEl && !submenuEl.contains(event.target as Node)) {
          setActiveSubmenu(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeSubmenu]);

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`)
  }

  const handleNavigation = (href: string) => (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (isNavigating) {
      e.preventDefault()
      return
    }

    // Jika navigasi tidak menggunakan Link (misalnya karena onClick custom)
    if (!e.defaultPrevented) {
      e.preventDefault()
      setIsNavigating(true)

      // Gunakan timeout untuk mencegah multiple clicks
      setTimeout(() => {
        router.push(href)
        setIsNavigating(false)
      }, 100)
    }
  }

  const toggleSubmenu = (title: string) => {
    if (activeSubmenu === title) {
      setActiveSubmenu(null);
    } else {
      setActiveSubmenu(title);
    }
  };

  const menuItems = [
    {
      title: "Dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
      href: "/tenant/dashboard",
      active: isActive("/tenant/dashboard") && !pathname.includes("/tenant/dashboard/"),
    },
    {
      title: "Stores",
      icon: <Store className="h-5 w-5" />,
      href: "/tenant/dashboard/stores",
      active: isActive("/tenant/dashboard/stores"),
      submenu: [
        { 
          title: "All Stores", 
          href: "/tenant/dashboard/stores", 
          active: pathname === "/tenant/dashboard/stores",
          icon: <Store className="h-4 w-4" />
        },
        { 
          title: "Store Applications", 
          href: "/tenant/dashboard/stores/applications", 
          active: pathname === "/tenant/dashboard/stores/applications",
          icon: <ClipboardList className="h-4 w-4" />
        },
        { 
          title: "Store Categories", 
          href: "/tenant/dashboard/stores/categories", 
          active: pathname === "/tenant/dashboard/stores/categories",
          icon: <Tag className="h-4 w-4" />
        },
        { 
          title: "Store Settings", 
          href: "/tenant/dashboard/stores/settings", 
          active: pathname === "/tenant/dashboard/settings/settings",
          icon: <Settings className="h-4 w-4" />
        },
      ]
    },
    {
      title: "Products",
      icon: <ShoppingBag className="h-5 w-5" />,
      href: "/tenant/dashboard/products",
      active: isActive("/tenant/dashboard/products"),
      submenu: [
        { 
          title: "All Products", 
          href: "/tenant/dashboard/products", 
          active: pathname === "/tenant/dashboard/products",
          icon: <ShoppingBag className="h-4 w-4" />
        },
        { 
          title: "Categories", 
          href: "/tenant/dashboard/products/categories", 
          active: pathname === "/tenant/dashboard/products/categories",
          icon: <Tag className="h-4 w-4" />
        },
        { 
          title: "Brands", 
          href: "/tenant/dashboard/products/brands", 
          active: pathname === "/tenant/dashboard/products/brands",
          icon: <Star className="h-4 w-4" />
        },
        { 
          title: "Import/Export", 
          href: "/tenant/dashboard/products/import-export", 
          active: pathname === "/tenant/dashboard/products/import-export",
          icon: <ArrowDownUp className="h-4 w-4" />
        },
        { 
          title: "Product Moderation", 
          href: "/tenant/dashboard/products/moderation", 
          active: pathname === "/tenant/dashboard/products/moderation",
          icon: <Box className="h-4 w-4" />
        },
      ]
    },
    {
      title: "Orders",
      icon: <FileText className="h-5 w-5" />,
      href: "/tenant/dashboard/orders",
      active: isActive("/tenant/dashboard/orders"),
      submenu: [
        { 
          title: "All Orders", 
          href: "/tenant/dashboard/orders", 
          active: pathname === "/tenant/dashboard/orders",
          icon: <FileText className="h-4 w-4" />
        },
        { 
          title: "Order Fulfillment", 
          href: "/tenant/dashboard/orders/fulfillment", 
          active: pathname === "/tenant/dashboard/orders/fulfillment",
          icon: <PackageOpen className="h-4 w-4" />
        },
        { 
          title: "Returns & Refunds", 
          href: "/tenant/dashboard/orders/returns", 
          active: pathname === "/tenant/dashboard/orders/returns",
          icon: <ArrowDownUp className="h-4 w-4" />
        },
        { 
          title: "Shipping Settings", 
          href: "/tenant/dashboard/orders/shipping", 
          active: pathname === "/tenant/dashboard/orders/shipping",
          icon: <Truck className="h-4 w-4" />
        },
      ]
    },
    {
      title: "Customers",
      icon: <Users className="h-5 w-5" />,
      href: "/tenant/dashboard/customers",
      active: isActive("/tenant/dashboard/customers"),
      submenu: [
        { 
          title: "All Customers", 
          href: "/tenant/dashboard/customers", 
          active: pathname === "/tenant/dashboard/customers",
          icon: <Users className="h-4 w-4" />
        },
        { 
          title: "Customer Groups", 
          href: "/tenant/dashboard/customers/groups", 
          active: pathname === "/tenant/dashboard/customers/groups",
          icon: <GanttChartSquare className="h-4 w-4" />
        },
        { 
          title: "Customer Reviews", 
          href: "/tenant/dashboard/customers/reviews", 
          active: pathname === "/tenant/dashboard/customers/reviews",
          icon: <MessageSquare className="h-4 w-4" />
        },
      ]
    },
    {
      title: "Affiliates",
      icon: <UserCheck className="h-5 w-5" />,
      href: "/tenant/dashboard/affiliates",
      active: isActive("/tenant/dashboard/affiliates"),
      submenu: [
        { 
          title: "All Affiliates", 
          href: "/tenant/dashboard/affiliates", 
          active: pathname === "/tenant/dashboard/affiliates",
          icon: <UserCheck className="h-4 w-4" />
        },
        { 
          title: "Affiliate Applications", 
          href: "/tenant/dashboard/affiliates/applications", 
          active: pathname === "/tenant/dashboard/affiliates/applications",
          icon: <ClipboardList className="h-4 w-4" />
        },
        { 
          title: "Commission Settings", 
          href: "/tenant/dashboard/affiliates/commission", 
          active: pathname === "/tenant/dashboard/affiliates/commission",
          icon: <Coins className="h-4 w-4" />
        },
        { 
          title: "Marketing Materials", 
          href: "/tenant/dashboard/affiliates/materials", 
          active: pathname === "/tenant/dashboard/affiliates/materials",
          icon: <FileImage className="h-4 w-4" />
        },
      ]
    },
    {
      title: "Marketing",
      icon: <Megaphone className="h-5 w-5" />,
      href: "/tenant/dashboard/marketing",
      active: isActive("/tenant/dashboard/marketing"),
      submenu: [
        { 
          title: "Campaigns", 
          href: "/tenant/dashboard/marketing/campaigns", 
          active: pathname === "/tenant/dashboard/marketing/campaigns",
          icon: <Megaphone className="h-4 w-4" />
        },
        { 
          title: "Promotions & Coupons", 
          href: "/tenant/dashboard/marketing/promotions", 
          active: pathname === "/tenant/dashboard/marketing/promotions",
          icon: <BadgePercent className="h-4 w-4" />
        },
        { 
          title: "Flash Sales", 
          href: "/tenant/dashboard/marketing/flash-sales", 
          active: pathname === "/tenant/dashboard/marketing/flash-sales",
          icon: <Sparkles className="h-4 w-4" />
        },
        { 
          title: "Email Campaigns", 
          href: "/tenant/dashboard/marketing/email", 
          active: pathname === "/tenant/dashboard/marketing/email",
          icon: <Mails className="h-4 w-4" />
        },
      ]
    },
    {
      title: "Content",
      icon: <FileImage className="h-5 w-5" />,
      href: "/tenant/dashboard/content",
      active: isActive("/tenant/dashboard/content"),
      submenu: [
        { 
          title: "Pages", 
          href: "/tenant/dashboard/content/pages", 
          active: pathname === "/tenant/dashboard/content/pages",
          icon: <Newspaper className="h-4 w-4" />
        },
        { 
          title: "Blog", 
          href: "/tenant/dashboard/content/blog", 
          active: pathname === "/tenant/dashboard/content/blog",
          icon: <BookOpen className="h-4 w-4" />
        },
        { 
          title: "Media", 
          href: "/tenant/dashboard/content/media", 
          active: pathname === "/tenant/dashboard/content/media",
          icon: <FileImage className="h-4 w-4" />
        },
        { 
          title: "Navigation", 
          href: "/tenant/dashboard/content/navigation", 
          active: pathname === "/tenant/dashboard/content/navigation",
          icon: <Layers className="h-4 w-4" />
        },
        { 
          title: "SEO", 
          href: "/tenant/dashboard/content/seo", 
          active: pathname === "/tenant/dashboard/content/seo",
          icon: <Globe className="h-4 w-4" />
        },
      ]
    },
    {
      title: "Live",
      icon: <Video className="h-5 w-5" />,
      href: "/tenant/dashboard/live",
      active: isActive("/tenant/dashboard/live"),
      submenu: [
        { 
          title: "Schedule", 
          href: "/tenant/dashboard/live/schedule", 
          active: pathname === "/tenant/dashboard/live/schedule",
          icon: <CalendarRange className="h-4 w-4" />
        },
        { 
          title: "Studio", 
          href: "/tenant/dashboard/live/studio", 
          active: pathname === "/tenant/dashboard/live/studio",
          icon: <Video className="h-4 w-4" />
        },
        { 
          title: "Recordings", 
          href: "/tenant/dashboard/live/recordings", 
          active: pathname === "/tenant/dashboard/live/recordings",
          icon: <Database className="h-4 w-4" />
        },
        { 
          title: "Analytics", 
          href: "/tenant/dashboard/live/analytics", 
          active: pathname === "/tenant/dashboard/live/analytics",
          icon: <BarChart className="h-4 w-4" />
        },
      ]
    },
    {
      title: "Analytics",
      icon: <BarChart className="h-5 w-5" />,
      href: "/tenant/dashboard/analytics",
      active: isActive("/tenant/dashboard/analytics"),
      submenu: [
        { 
          title: "Overview", 
          href: "/tenant/dashboard/analytics", 
          active: pathname === "/tenant/dashboard/analytics",
          icon: <Home className="h-4 w-4" />
        },
        { 
          title: "Sales Analytics", 
          href: "/tenant/dashboard/analytics/sales", 
          active: pathname === "/tenant/dashboard/analytics/sales",
          icon: <Receipt className="h-4 w-4" />
        },
        { 
          title: "Customer Analytics", 
          href: "/tenant/dashboard/analytics/customers", 
          active: pathname === "/tenant/dashboard/analytics/customers",
          icon: <PieChart className="h-4 w-4" />
        },
        { 
          title: "Reports", 
          href: "/tenant/dashboard/analytics/reports", 
          active: pathname === "/tenant/dashboard/analytics/reports",
          icon: <FileText className="h-4 w-4" />
        },
        { 
          title: "Export", 
          href: "/tenant/dashboard/analytics/export", 
          active: pathname === "/tenant/dashboard/analytics/export",
          icon: <ArrowDownUp className="h-4 w-4" />
        },
      ]
    },
    {
      title: "Financial",
      icon: <CreditCard className="h-5 w-5" />,
      href: "/tenant/dashboard/financial",
      active: isActive("/tenant/dashboard/financial"),
      submenu: [
        { 
          title: "Overview", 
          href: "/tenant/dashboard/financial", 
          active: pathname === "/tenant/dashboard/financial",
          icon: <Home className="h-4 w-4" />
        },
        { 
          title: "Payouts", 
          href: "/tenant/dashboard/financial/payouts", 
          active: pathname === "/tenant/dashboard/financial/payouts",
          icon: <Coins className="h-4 w-4" />
        },
        { 
          title: "Transactions", 
          href: "/tenant/dashboard/financial/transactions", 
          active: pathname === "/tenant/dashboard/financial/transactions",
          icon: <ArrowDownUp className="h-4 w-4" />
        },
        { 
          title: "Statements", 
          href: "/tenant/dashboard/financial/statements", 
          active: pathname === "/tenant/dashboard/financial/statements",
          icon: <Receipt className="h-4 w-4" />
        },
      ]
    },
    {
      title: "Domains",
      icon: <Globe className="h-5 w-5" />,
      href: "/tenant/dashboard/domains",
      active: isActive("/tenant/dashboard/domains"),
    },
    {
      title: "Themes",
      icon: <Palette className="h-5 w-5" />,
      href: "/tenant/dashboard/themes",
      active: isActive("/tenant/dashboard/themes"),
    },
    {
      title: "Settings",
      icon: <Settings className="h-5 w-5" />,
      href: "/tenant/dashboard/settings",
      active: isActive("/tenant/dashboard/settings"),
      submenu: [
        { 
          title: "Profile", 
          href: "/tenant/dashboard/settings/profile", 
          active: pathname === "/tenant/dashboard/settings/profile",
          icon: <Users className="h-4 w-4" />
        },
        { 
          title: "Branding", 
          href: "/tenant/dashboard/settings/branding", 
          active: pathname === "/tenant/dashboard/settings/branding",
          icon: <PaintBucket className="h-4 w-4" />
        },
        { 
          title: "Domain", 
          href: "/tenant/dashboard/settings/domain", 
          active: pathname === "/tenant/dashboard/settings/domain",
          icon: <Globe className="h-4 w-4" />
        },
        { 
          title: "Users", 
          href: "/tenant/dashboard/settings/users", 
          active: pathname === "/tenant/dashboard/settings/users",
          icon: <Users className="h-4 w-4" />
        },
        { 
          title: "Integrations", 
          href: "/tenant/dashboard/settings/integrations", 
          active: pathname === "/tenant/dashboard/settings/integrations",
          icon: <Command className="h-4 w-4" />
        },
        { 
          title: "Notifications", 
          href: "/tenant/dashboard/settings/notifications", 
          active: pathname === "/tenant/dashboard/settings/notifications",
          icon: <MessageSquare className="h-4 w-4" />
        },
        { 
          title: "Security", 
          href: "/tenant/dashboard/settings/security", 
          active: pathname === "/tenant/dashboard/settings/security",
          icon: <LifeBuoy className="h-4 w-4" />
        },
      ]
    },
  ]

  // Menentukan lebar sidebar berdasarkan mode
  const sidebarWidth = isTablet ? "w-16" : "w-64"

  return (
    <div
      className={cn(
        "z-20 flex flex-col border-r bg-background transition-all",
        sidebarWidth,
        // Tidak lagi menggunakan fixed di tablet, tapi berubah menjadi static
        isTablet ? "static" : "fixed inset-y-0 left-0 lg:static",
        sidebarState === "collapsed" ? "-translate-x-full" : "translate-x-0"
      )}
    >
      <div className="flex h-14 items-center border-b px-4">
        <Link href="/tenant/dashboard" className="flex items-center gap-2 font-semibold">
          <span className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-xs font-bold text-primary-foreground">
            TN
          </span>
          {!isTablet && <span>Tenant Portal</span>}
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-2">
        {!isTablet && (
          <div className="px-3 py-2">
            <div className="flex items-center gap-2 rounded-md bg-muted px-3 py-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-xs font-bold text-primary-foreground">
                TA
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-medium">Tenant Admin</span>
                <span className="text-xs text-muted-foreground"><EMAIL></span>
              </div>
            </div>
          </div>
        )}
        <nav className={cn("space-y-1", isTablet ? "px-1 py-2" : "px-2 py-2")}>
          {menuItems.map((item, index) => (
            <div key={index} className="relative">
              {/* Render menu berbeda untuk tablet vs desktop */}
              {isTablet ? (
                <div>
                  {/* Untuk tablet - gunakan button agar bisa toggle submenu */}
                  <button
                    onClick={() => {
                      if (item.submenu) {
                        toggleSubmenu(item.title);
                      } else {
                        router.push(item.href);
                      }
                    }}
                    className={cn(
                      "flex items-center rounded-md transition-colors",
                      "justify-center px-2 py-2",
                      "text-sm font-medium w-full",
                      item.active
                        ? "bg-muted text-foreground"
                        : "text-muted-foreground hover:bg-muted hover:text-foreground"
                    )}
                    title={item.title}
                  >
                    {item.icon}
                  </button>
                  
                  {/* Submenu untuk tablet */}
                  {item.submenu && activeSubmenu === item.title && (
                    <div 
                      ref={(el) => {
                        submenuRefs.current[item.title] = el;
                        return undefined;
                      }}
                      className="fixed ml-16 mt-1 w-48 bg-background border border-border rounded-md shadow-md p-1 z-50"
                    >
                      {item.submenu.map((subitem, subindex) => (
                        <Link
                          key={subindex}
                          href={subitem.href}
                          onClick={handleNavigation(subitem.href)}
                          className={cn(
                            "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                            subitem.active
                              ? "bg-muted text-foreground"
                              : "text-muted-foreground hover:bg-muted hover:text-foreground"
                          )}
                        >
                          {subitem.icon && <span className="mr-1">{subitem.icon}</span>}
                          {subitem.title}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                /* Untuk desktop - tetap gunakan tampilan admin-like */
                <>
                  <Link
                    href={item.href}
                    onClick={handleNavigation(item.href)}
                    className={cn(
                      "flex items-center rounded-md transition-colors",
                      "gap-3 px-3 py-2",
                      "text-sm font-medium",
                      item.active
                        ? "bg-muted text-foreground" 
                        : "text-muted-foreground hover:bg-muted hover:text-foreground"
                    )}
                  >
                    {item.icon}
                    <span className="flex-1">{item.title}</span>
                  </Link>
                  
                  {/* Submenu desktop */}
                  {item.submenu && item.active && (
                    <div className="ml-4 mt-1 space-y-1 border-l pl-3">
                      {item.submenu.map((subitem, subindex) => (
                        <Link
                          key={subindex}
                          href={subitem.href}
                          onClick={handleNavigation(subitem.href)}
                          className={cn(
                            "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                            subitem.active
                              ? "bg-muted text-foreground"
                              : "text-muted-foreground hover:bg-muted hover:text-foreground"
                          )}
                        >
                          {subitem.icon && <span className="mr-1">{subitem.icon}</span>}
                          {subitem.title}
                        </Link>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          ))}
        </nav>
      </div>
    </div>
  )
}
