import { NextRequest, NextResponse } from 'next/server';
import { productCategoryService } from '@/lib/services/product-categories';

// GET - Mendapatkan semua product categories atau difilter berdasarkan query
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const tree = searchParams.get('tree') === 'true';
    const with_counts = searchParams.get('with_counts') === 'true';
    
    const filters = {
      search: searchParams.get('search') || undefined,
      parent_id: searchParams.get('parent_id') || undefined,
      is_active: searchParams.get('is_active') ? searchParams.get('is_active') === 'true' : undefined,
    };
    
    let categories;
    
    if (tree) {
      categories = await productCategoryService.getCategoriesTree();
    } else if (with_counts) {
      categories = await productCategoryService.getCategoriesWithProductCounts();
    } else {
      categories = await productCategoryService.getCategories(filters);
    }
    
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST - Membuat product category baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newCategory = await productCategoryService.createCategory(body);
    
    return NextResponse.json(newCategory, { status: 201 });
  } catch (error) {
    console.error('Error creating category:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to create category';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// PUT - Update multiple categories (untuk reordering)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;
    
    if (action === 'reorder') {
      await productCategoryService.reorderCategories(data);
      return NextResponse.json({ message: 'Categories reordered successfully' });
    }
    
    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error updating categories:', error);
    return NextResponse.json(
      { error: 'Failed to update categories' },
      { status: 500 }
    );
  }
}
