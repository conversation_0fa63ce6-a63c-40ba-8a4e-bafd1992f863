"use client"

import { useState, useEffect } from "react"
import { MessageList } from "@/components/buyer/messages/message-list"
import { Conversation } from "@/components/buyer/messages/conversation"
import { MessageEmpty } from "@/components/buyer/messages/message-empty"
import { useAuth } from "@/contexts/auth-context"
import { Skeleton } from "@/components/ui/skeleton"

export default function MessagesClient() {
  const [activeConversation, setActiveConversation] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { user } = useAuth()

  // Simulasi loading data
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] w-full">
        <div className="w-1/3 border-r border-border">
          <div className="p-4 border-b border-border">
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="p-4 space-y-4">
            {Array(5)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[150px]" />
                    <Skeleton className="h-4 w-[100px]" />
                  </div>
                </div>
              ))}
          </div>
        </div>
        <div className="flex-1 flex flex-col">
          <div className="p-4 border-b border-border">
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="flex-1 p-4">
            <Skeleton className="h-[calc(100vh-12rem)] w-full" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-[calc(100vh-4rem)] w-full">
      <MessageList activeConversation={activeConversation} setActiveConversation={setActiveConversation} />
      {activeConversation ? <Conversation conversationId={activeConversation} /> : <MessageEmpty />}
    </div>
  )
}
