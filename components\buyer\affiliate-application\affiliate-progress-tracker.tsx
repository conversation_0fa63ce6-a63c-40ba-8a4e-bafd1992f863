"use client"

import { cn } from "@/lib/utils"
import { CheckCircle } from "lucide-react"

interface AffiliateProgressTrackerProps {
  currentStep: number
  totalSteps: number
  applicationId: string
}

export function AffiliateProgressTracker({ currentStep, totalSteps, applicationId }: AffiliateProgressTrackerProps) {
  const steps = ["Personal Information", "Promotion Channels", "Payment Information", "Terms & Conditions"]

  return (
    <div className="rounded-lg border border-border/60 bg-card p-4 shadow-sm">
      <div className="mb-4 flex items-center justify-between">
        <div className="text-sm font-medium">Application Progress</div>
        <div className="text-xs text-muted-foreground">Application ID: {applicationId}</div>
      </div>

      {/* Progress Bar */}
      <div className="mb-4 h-2 w-full overflow-hidden rounded-full bg-muted">
        <div
          className="h-full rounded-full bg-primary transition-all duration-300 ease-in-out"
          style={{ width: `${(currentStep / totalSteps) * 100}%` }}
        />
      </div>

      {/* Step Indicators */}
      <div className="grid grid-cols-4 gap-2">
        {steps.map((step, index) => (
          <div key={index} className="flex flex-col items-center">
            <div
              className={cn(
                "flex h-8 w-8 items-center justify-center rounded-full text-xs font-medium",
                index < currentStep
                  ? "bg-primary text-primary-foreground"
                  : index === currentStep
                    ? "border-2 border-primary bg-background text-foreground"
                    : "border border-border bg-muted/50 text-muted-foreground",
              )}
            >
              {index < currentStep ? <CheckCircle className="h-4 w-4" /> : index + 1}
            </div>
            <span className="mt-1 text-center text-xs font-medium">{step}</span>
          </div>
        ))}
      </div>
    </div>
  )
}
