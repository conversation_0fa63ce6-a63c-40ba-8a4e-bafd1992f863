import axios from "axios"

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"

// Konfigurasi axios dengan timeout
const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 10000, // 10 detik timeout
})

// Fungsi untuk mendapatkan token dari localStorage
const getToken = () => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("token")
  }
  return null
}

// Fungsi untuk membuat header dengan token
const getHeaders = () => {
  const token = getToken()
  return {
    headers: {
      Authorization: token ? `Bearer ${token}` : "",
    },
  }
}

// Interface untuk tipe data toko
export interface Store {
  id: string
  name: string
  description: string | null
  slug: string
  logo: string | null
  banner: string | null
  ownerId: string
  tenantId: string | null
  createdAt: string
  updatedAt: string
  owner?: {
    id: string
    name: string
    email: string
  }
  tenant?: {
    id: string
    name: string
    slug: string
  } | null
}

// Interface untuk data yang diperlukan saat membuat toko
export interface CreateStoreData {
  name: string
  description?: string
  slug: string
  logo?: string
  banner?: string
  tenantId?: string
}

// Interface untuk data yang diperlukan saat mengupdate toko
export interface UpdateStoreData {
  name?: string
  description?: string
  slug?: string
  logo?: string
  banner?: string
  tenantId?: string
}

// API untuk toko
export const storesAPI = {
  // Mendapatkan semua toko
  getAll: async (userId?: string, tenantId?: string): Promise<Store[]> => {
    try {
      let url = `/stores`
      const params = new URLSearchParams()

      if (userId) params.append("userId", userId)
      if (tenantId) params.append("tenantId", tenantId)

      if (params.toString()) {
        url += `?${params.toString()}`
      }

      const response = await axiosInstance.get(url, getHeaders())
      return response.data
    } catch (error) {
      console.error("Error fetching stores:", error)

      // Jika dalam mode development, kembalikan data dummy
      if (process.env.NEXT_PUBLIC_NODE_ENV === "development" || process.env.NEXT_PUBLIC_VERCEL_ENV === "preview") {
        console.log("Returning mock data in development/preview mode")
        return []
      }

      throw error
    }
  },

  // Mendapatkan toko berdasarkan ID
  getById: async (id: string): Promise<Store> => {
    try {
      const response = await axiosInstance.get(`/stores/${id}`, getHeaders())
      return response.data
    } catch (error) {
      console.error(`Error fetching store with ID ${id}:`, error)
      throw error
    }
  },

  // Mendapatkan toko berdasarkan slug
  getBySlug: async (slug: string): Promise<Store> => {
    try {
      const response = await axiosInstance.get(`/stores/slug/${slug}`, getHeaders())
      return response.data
    } catch (error) {
      console.error(`Error fetching store with slug ${slug}:`, error)
      throw error
    }
  },

  // Membuat toko baru
  create: async (data: CreateStoreData): Promise<Store> => {
    try {
      const response = await axiosInstance.post(`/stores`, data, getHeaders())
      return response.data
    } catch (error) {
      console.error("Error creating store:", error)
      throw error
    }
  },

  // Mengupdate toko
  update: async (id: string, data: UpdateStoreData): Promise<Store> => {
    try {
      const response = await axiosInstance.patch(`/stores/${id}`, data, getHeaders())
      return response.data
    } catch (error) {
      console.error(`Error updating store with ID ${id}:`, error)
      throw error
    }
  },

  // Menghapus toko
  delete: async (id: string): Promise<void> => {
    try {
      await axiosInstance.delete(`/stores/${id}`, getHeaders())
    } catch (error) {
      console.error(`Error deleting store with ID ${id}:`, error)
      throw error
    }
  },
}
