<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <title>Shopee UI</title>
    <!-- Resource hints untuk koneksi lebih cepat -->
    <link rel="preconnect" href="https://cdn.scalev.id">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="https://i3.ytimg.com">
    <link rel="dns-prefetch" href="https://unpkg.com">
    
    <!-- Preload font awesome untuk meningkatkan performance -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"></noscript>
    
    <!-- Preload critical fonts -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- Preload critical JavaScript -->
    <link rel="preload" href="https://unpkg.com/masonry-layout@4/dist/masonry.pkgd.min.js" as="script">
    <link rel="preload" href="https://unpkg.com/imagesloaded@5/imagesloaded.pkgd.min.js" as="script">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto', Arial, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            padding: 0px;
            margin: 0px 0px 0px 0px;
            -webkit-font-smoothing: antialiased;
        }

        /* CSS untuk Kategori */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto', Arial, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            padding: 0px;
        }
        
        .categories-section {
            position: relative;
            background-color: white;
            padding: 5px 10px;
            border-radius: 8px;
            border: 1px solid #ececec;
            box-shadow: 0 0px 0px rgba(0, 0, 0, 0.1);
            margin: 10px;
            max-width: 1200px;
            
        }
        
        /* Menghilangkan header kategori dan garis di bawahnya */
        .categories-header {
            display: none;
        }
        
        /* Memindahkan tombol ekspand ke bawah dan tengah */
        .expand-button-container {
            display: none !important; /* Sembunyikan tombol panah bawah */
        }
        
        .categories-container {
            display: flex;
            overflow-x: auto;
            gap: 6px;
            padding: 5px 0;
            padding-bottom: 15px;
            scrollbar-width: none;
            transition: opacity 0.6s ease; /* Menambahkan transisi untuk opasitas */
        }
        
        .categories-container::-webkit-scrollbar {
            display: none;
        }
        
        /* Mengubah kategori menjadi link pada seluruh card */
        .category-item {
    text-decoration: none;
    display: block;
    min-width: 65px;
    text-align: center;
    border-radius: 8px;
    padding: 6px 5px;
    padding-bottom: 1px;
    transition: background-color 0.2s;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    background-color: #ffffff;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}
        
        .category-item:hover {
            background-color: #f5f5f5;
        }
        
        .category-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        /* Memastikan tidak ada link di nama kategori */
        .category-name {
            font-size: 10px;
            color: #222;
            margin-top: 8px;
            text-decoration: none;
        }
        
        /* Gaya khusus untuk card "Lihat Semua" */
        .see-all-item {
            text-decoration: none;
            display: block;
            min-width: 70px;
            text-align: center;
            border-radius: 8px;
            padding: 8px 5px;
            padding-bottom: 10px;
            transition: background-color 0.2s;
            border: 1px solid #f0f0f0;
            background-color: #FFF5F0;
            cursor: pointer;
        }
        
        .see-all-item:hover {
            background-color: #FFE8E0;
        }
        
        .close-all-item {
            text-decoration: none;
            display: block;
            min-width: 70px;
            text-align: center;
            border-radius: 8px;
            padding: 8px 5px;
            padding-bottom: 10px;
            transition: background-color 0.2s;
            border: 1px solid #f0f0f0;
            background-color: #F5F5F5;
            cursor: pointer;
        }
        
        .close-all-item:hover {
            background-color: #E0E0E0;
        }
        
        .expanded-categories {
            height: 0;
            overflow: hidden;
            transition: height 0.3s ease;
        }
        
        .expanded-categories.active {
            overflow: visible;
        }
        
        .expanded-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            padding: 5px 10px 10px 10px;
            padding-bottom: 15px;
        }
        
        .expanded-item {
            text-decoration: none;
            display: block;
            text-align: center;
            border-radius: 8px;
            padding: 8px 5px;
            padding-bottom: 12px;
            transition: background-color 0.2s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
            cursor: pointer;
        }
        
        .expanded-item:hover {
            background-color: #f5f5f5;
        }
        
        .expanded-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        /* Memastikan tidak ada gaya khusus pada nama kategori expanded */
        .expanded-name {
            font-size: 12px;
            color: #222;
            margin-top: 8px;
            text-decoration: none;
        }
        
        /* Animation untuk transisi icon */
        @keyframes fadeRotate {
            0% { opacity: 0; transform: rotate(0deg); }
            100% { opacity: 1; transform: rotate(360deg); }
        }
        
        @keyframes fadeRotateOut {
            0% { opacity: 1; transform: rotate(0deg); }
            100% { opacity: 0; transform: rotate(-90deg); }
        }
        
        .animate-in {
            animation: fadeRotate 0.5s forwards;
        }
        
        .animate-out {
            animation: fadeRotateOut 0.3s forwards;
        }
        
        /* Styles for desktop */
        @media (min-width: 768px) {
            .body {
                padding: 20px;
            }
            
            .categories-section {
                padding: 15px;
                padding-bottom: 15px;
            }
            
            .desktop-wrapper {
                display: block;
            }
            
            .categories-container {
                display: none; /* Hide mobile category list on desktop */
            }
            
            .expand-button-container {
                display: none !important; /* Hide expand button on desktop */
            }
            
            .expanded-categories {
                height: auto !important;
                overflow: visible;
            }
            
            .expanded-grid {
                display: grid;
                grid-template-columns: repeat(7, 1fr);
                gap: 15px;
                padding: 10px;
            }
            
            .desktop-grid {
                display: grid;
                grid-template-columns: repeat(7, 1fr);
                gap: 15px;
                padding: 10px;
            }
            
            .extra-categories {
                height: 0;
                overflow: hidden;
                transition: height 0.3s ease;
            }
            
            .extra-categories.active {
                height: auto !important;
                overflow: visible;
            }
            
            .expanded-item {
                padding: 12px 10px;
                transition: transform 0.2s, box-shadow 0.2s;
            }
            
            .expanded-item:hover {
                transform: translateY(-5px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }
            
            .expanded-name {
                font-size: 13px;
                font-weight: 500;
                margin-top: 10px;
            }
            
            /* Menghapus header desktop */
            .desktop-header {
                display: none !important;
            }
            
            /* Sembunyikan tombol tutup di desktop */
            .close-button-container {
                display: none !important;
            }
            
            /* Sembunyikan tombol View All beranimasi di desktop */
            .floating-arrow {
                display: none !important;
            }
            
            .categories-container-wrapper::after {
                display: none !important;
            }
        }
        
        /* Tambahan untuk responsif mobile/desktop */
        @media (max-width: 767px) {
            .desktop-only {
                display: none !important;
            }
        }
        
        @media (min-width: 768px) {
            .mobile-only {
                display: none !important;
            }
        }
        
        /* Tambahan CSS untuk mengecilkan card kategori di tampilan mobile horizontal */
        @media (max-width: 767px) {
            /* Mengecilkan kategori dalam tampilan horizontal saja */
            .categories-container .category-item {
                min-width: 55px; /* Mengecilkan lebar minimum */
                padding: 4px 3px; /* Mengecilkan padding */
                padding-bottom: 0px; /* Hapus padding bawah */
                border-width: 1px; /* Mengecilkan border */
            }
            
            .categories-container .category-icon svg {
                width: 24px; /* Mengecilkan ukuran icon */
                height: 24px;
            }
            
            .categories-container .category-name {
                font-size: 9px; /* Mengecilkan font */
                margin-top: 4px; /* Mengurangi margin */
            }
            
            /* Mengecilkan card Lihat Semua */
            .categories-container .see-all-item {
                min-width: 60px;
                padding: 4px 3px;
                padding-bottom: 6px;
            }
            
            .categories-container .see-all-item .category-icon svg {
                width: 28px;
                height: 28px;
            }
            
            /* Mengurangi gap antar item */
            .categories-container {
                gap: 10px;
                padding-bottom: 10px;
            }
        }
        
        /* CSS untuk tombol panah atas (tombol tutup) */
        .close-button-container {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-top: 10px;
            margin-bottom: 10px;
        }
        
        .close-button {
            background: #ff5722;
            border: none;
            cursor: pointer;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            transform: rotate(180deg); /* Arahkan panah ke atas */
        }
        
        .close-button:hover {
            background: #e64a19;
        }
        
        /* CSS untuk icon floating berbentuk panah dengan teks */
        .floating-arrow {
            position: absolute;
            right: 5px;
            top: 45%;
            transform: translateY(-50%);
            height: 36px;
            background-color: rgba(255, 245, 240, 0.95);
            border: 1px solid #FFDFD1;
            border-radius: 18px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            padding: 0 15px 0 12px;
            cursor: pointer;
            z-index: 10;
            transition: opacity 0.8s ease, transform 0.8s ease; /* Memperlambat transisi */
            opacity: 1;
        }
        
        .floating-arrow.hidden {
            opacity: 0;
            transform: translateY(-50%) translateX(10px);
            pointer-events: none;
        }
        
        .floating-arrow:active {
            transform: translateY(-50%) scale(0.97);
        }
        
        .floating-arrow-text {
            font-size: 12px;
            font-weight: 600;
            color: #FF5722;
            margin-right: 6px;
        }
        
        .floating-arrow-icon {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Animasi panah bergerak */
        @keyframes arrowMove {
            0% { transform: translateX(0); }
            50% { transform: translateX(3px); }
            100% { transform: translateX(0); }
        }
        
        .floating-arrow-icon svg {
            width: 15px;
            height: 15px;
            color: #FF5722;
            animation: arrowMove 1.5s infinite ease-in-out;
        }
        
        /* Gradient untuk efek fade di sisi kanan */
        .categories-container-wrapper::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50px;
            height: 100%;
            background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9));
            pointer-events: none;
        }
        
        /* Tambahkan style untuk container yang memiliki posisi relative */
        .categories-container-wrapper {
            position: relative;
            overflow: hidden;
        }
        
        /* CSS untuk Shopee Live dan Video */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        body {
            padding: 0px;
            background-color: #f0f0f0;
            width: 100%;
        }
        
        .container1 {
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 10px;
            gap: 4px; /* Gap antara card */
            max-width: 1200px; /* Maksimum lebar untuk desktop */
            margin: 0 auto; /* Tengahkan container */
       
 }
        
        .section {
            background-color: white;
            border-radius: 5px;
            padding: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            width: 50%;
            cursor: pointer;
            transition: transform 0.2s;
            border: 1px solid #eaeaea;
        }
        
        .section:hover {
            transform: scale(1.01);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .section-title {
            margin-left: 10px;
            font-size: 1rem;
            color: #ee4d2d;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .title-icon {
            margin-left: 5px;
            font-size: 20px;
            color: #999;
            font-weight: normal;
        }
        
        .videos-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2px;
        }
        
        .video-card {
            position: relative;
            border-radius: 6px;
            overflow: hidden;
            aspect-ratio: 9/16;
            background-color: #f8f8f8;
            cursor: pointer;
            border: 1px solid #e0e0e0; /* Tambahkan bingkai untuk video card */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* Tambahkan bayangan halus */
        }
        
        .video-thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
        }
        
        .label {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: #ee4d2d;
            color: white;
            padding: 1px 5px;
            border-radius: 50px;
            font-size: 0.6rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            z-index: 2;
        }
        
        .view-count {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 1px 5px;
            border-radius: 50px;
            font-size: 0.6rem;
            display: flex;
            align-items: center;
            z-index: 2;
        }
        
        .dot {
            height: 5px;
            width: 5px;
            background-color: white;
            border-radius: 50%;
            display: inline-block;
            margin-right: 3px;
        }
        
        .video-title {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            color: white;
            padding: 12px 5px 5px;
            font-size: 0.65rem;
            font-weight: bold;
        }
        
        .view-icon {
            height: 8px;
            width: 8px;
            margin-right: 3px;
            display: inline-block;
        }
        
        .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 30px;
            opacity: 0.8;
        }
        
        /* Media queries untuk responsivitas */
        @media screen and (min-width: 768px) {
            .videos-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            
            .section {
                padding: 15px;
                border-radius: 12px;
            }
            
            .section-title {
                font-size: 30px;
                margin-left: 50px;
                margin-bottom: 5px;
            }
            
            .section-header {
                margin-bottom: 15px;
            }
            
            .video-title {
                font-size: 14px;
                padding: 20px 12px 10px;
            }
            
            .label, .view-count {
                font-size: 0.7rem;
                padding: 2px 8px;
                top: 8px;
                left: 8px;
            }
        }
        
        @media screen and (min-width: 1024px) {
            .container {
                padding: 20px;
                gap: 35px;
            }
            
            .section {
                padding: 20px;
                border-radius: 16px;
            }
            
            .section-title {
                font-size: 22px;
                margin-left: 10px;
                margin-bottom: 8px;
            }
            
            .title-icon {
                font-size: 50px;
                margin-left: 8px;
            }
            
            .videos-grid {
                gap: 20px;
            }
            
            .video-card {
                max-height: 350px;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
            }
            
            .video-title {
                font-size: 16px;
                padding: 25px 15px 12px;
            }
            
            .play-icon {
                width: 40px;
                height: 40px;
            }
            
            .label, .view-count {
                font-size: 0.8rem;
                padding: 3px 10px;
                top: 12px;
                left: 12px;
                border-radius: 50px;
            }
            
            .dot {
                height: 6px;
                width: 6px;
            }
        }
  /* PRODUK STYLES */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0px ;
            width: 100%;
            padding: 0px 5px 5px 5px;
        }

        /* Masonry grid styles */
        .masonry-grid {
            width: 100%;
            padding: 0px 0px 0px 5px;
            contain: layout size style;
        }

        .product-card {
            background-color: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            width: 100%;
            margin-bottom: 10px; /* Margin untuk spacing antar card dalam masonry */
            break-inside: avoid; /* Mencegah card terpotong */
            contain: content;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.15);
        }

        .product-card a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .product-img-container {
            position: relative;
            width: 100%;
            padding-top: 100%;
            overflow: hidden;
            background-color: #fff;
            border-bottom: 1px solid #f2f2f2;
            contain: strict;
        }

        .product-img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: block;
            padding: 2px;
            will-change: transform;
            backface-visibility: hidden;
        }

        .product-info {
            padding: 8px;
            padding-bottom: 12px; /* Jarak bawah lebih lega */
        }

        .shopee-star-icon {
            display: inline-flex;
            align-items: center;
            background-color: #ee4d2d;
            color: white;
            font-size: 9px;
            font-weight: bold;
            padding: 1px 3px;
            border-radius: 2px;
            margin-right: 3px;
            vertical-align: middle;
        }

        .product-name {
            font-size: 12px;
            color: #333;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.4;
        }
    /* Label Live dengan Gelombang Suara */
.live-discount-label {
  display: flex;
  height: 20px; /* lebih kecil dari 28px */
  width: fit-content;
  font-family: Arial, sans-serif;
  margin-bottom: 5px;
}

.live-label {
  background-color: #FF4D4D;
  color: white;
  font-weight: bold;
  font-size: 10px; /* lebih kecil dari 14px */
  padding: 0 8px 0 22px; /* padding lebih kecil */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.sound-wave {
  position: absolute;
  left: 6px; /* lebih kecil dari 10px */
  display: flex;
  gap: 1.5px; /* lebih kecil dari 2.5px */
  height: 10px; /* lebih kecil dari 14px */
  align-items: center;
}

.wave-bar {
  width: 1px; /* lebih kecil dari 1.5px */
  background-color: white;
  border-radius: 0.5px;
}

.wave-bar:nth-child(1) {
  height: 6px; /* lebih kecil dari 10px */
  animation: sound-wave 1s infinite alternate;
}

.wave-bar:nth-child(2) {
  height: 9px; /* lebih kecil dari 14px */
  animation: sound-wave 1s infinite alternate 0.2s;
}

.wave-bar:nth-child(3) {
  height: 5px; /* lebih kecil dari 8px */
  animation: sound-wave 1s infinite alternate 0.4s;
}

@keyframes sound-wave {
  0% {
    height: 2px; /* lebih kecil dari 3px */
    transform: translateY(3.5px); /* lebih kecil dari 5.5px */
  }
  100% {
    height: 100%;
    transform: translateY(0);
  }
}

.discount-label {
  background-color: white;
  color: #FF4D4D;
  font-weight: bold;
  font-size: 10px; /* lebih kecil dari 14px */
  padding: 0 10px; /* lebih kecil dari 15px */
  border: 1px solid #FF4D4D;
  border-left: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
  /* Label Live dengan Sound Wave */
.live-shopee-3 {
  display: inline-flex;
  align-items: center;
  background-color: #ee4d2d;
  color: white;
  font-size: 8px;
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 2px;
  margin-right: 5px;
  vertical-align: middle;
  position: relative;
}

.live-shopee-3 .sound-wave {
  display: inline-flex;
  align-items: center;
  height: 7px;
  margin-right: 4px; /* Memperbesar margin di sini */
}

.live-shopee-3 .wave-bar {
  width: 1px;
  background-color: white;
  margin-right: 1px;
}

/* Tambahkan margin kiri pada teks setelah gelombang suara */
.live-shopee-3 .live-text {
  margin-left: 12px; /* Tambahkan ini */
}

.live-shopee-3 .wave-bar:nth-child(1) {
  height: 3px;
  animation: soundwave 0.8s infinite alternate;
}

.live-shopee-3 .wave-bar:nth-child(2) {
  height: 5px;
  animation: soundwave 0.7s infinite alternate 0.1s;
}

.live-shopee-3 .wave-bar:nth-child(3) {
  height: 3px;
  animation: soundwave 0.6s infinite alternate 0.2s;
}

@keyframes soundwave {
  0% { height: 2px; }
  100% { height: 6px; }
}
  /* Label Terlaris dengan accent border - ukuran lebih kecil */
.terlaris-label {
    display: inline-flex;
    align-items: center;
    background-color: white;
    color: #ee4d2d;
    font-size: 10px; /* Ukuran font lebih kecil */
    font-weight: 600;
    padding: 2px 6px; /* Padding lebih kecil */
    border-radius: 3px; /* Border radius lebih kecil */
    border: 1px solid #ee4d2d;
    border-left: 3px solid #ee4d2d; /* Border kiri sedikit lebih kecil */
    margin-bottom: 4px;
}

.terlaris-label:before {
    content: "\f091"; /* Icon Trophy dari Font Awesome */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    margin-right: 3px; /* Margin lebih kecil */
    font-size: 9px; /* Ukuran icon lebih kecil */
}

/* Label Komisi Xtra dengan spasi - ukuran lebih kecil */
.komisi-xtra-label {
    display: inline-flex;
    align-items: center;
    background-color: white;
    border: 1px solid #ee4d2d;
    border-radius: 3px; /* Border radius lebih kecil */
    padding: 2px 6px; /* Padding lebih kecil */
    font-size: 10px; /* Ukuran font lebih kecil */
    font-weight: 600;
    margin-bottom: 4px;
}

.komisi-xtra-label .komisi {
    color: #ee4d2d;
}

.komisi-xtra-label .xtra {
    color: #005bac;
    margin-left: 3px; /* Spasi lebih kecil antara KOMISI dan XTRA */
}
  /* Star plus label style */
.star-label {
    display: inline-flex;
    align-items: center;
    border-radius: 3px; 
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0,0,0,0.08);
    font-size: 9px;
    font-weight: 700;
    height: 16px;
    margin-right: 4px;
    margin-bottom: 2px;
    vertical-align: middle;
}

.star-primary {
    background-color: #ee4d2d; /* Warna orange-merah khas Shopee */
    color: white;
    padding: 0 5px;
    height: 100%;
    display: flex;
    align-items: center;
    letter-spacing: -0.1px;
}

.star-secondary {
    background-color: #fff0e5; /* Warna orange muda */
    color: #ee4d2d; /* Warna teks sesuai dengan warna primary */
    padding: 0 5px;
    height: 100%;
    display: flex;
    align-items: center;
    letter-spacing: -0.1px;
}
  /* Mall label style */
        .mall-label {
    display: inline-flex;
    align-items: center;
    border-radius: 3px; /* Kurangi sedikit border radius */
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0,0,0,0.08); /* Kurangi shadow */
    font-size: 9px; /* Sesuaikan ukuran font */
    font-weight: 700; /* Font weight lebih tebal */
    height: 15px; /* Kurangi sedikit tinggi */
    margin-right: 4px;
    margin-bottom: 2px; /* Tambah margin bottom untuk jarak dengan teks */
    vertical-align: middle;
}

.mall-primary {
    background-color: #ee4d2d; /* Warna merah Shopee */
    color: white;
    padding: 0 5px; /* Sesuaikan padding */
    height: 100%;
    display: flex;
    align-items: center;
    letter-spacing: -0.1px; /* Kurangi sedikit jarak antar huruf */
}

.mall-secondary {
    background-color: #fff7f7; /* Warna latar belakang sedikit lebih pink */
    color: #ee4d2d; /* Warna teks sesuai dengan warna merah Shopee */
    padding: 0 5px; /* Sesuaikan padding */
    height: 100%;
    display: flex;
    align-items: center;
    letter-spacing: -0.1px; /* Kurangi sedikit jarak antar huruf */
}
/* label termurah: Badge dengan Jempol Overlap */
.termurah-label-9 {
    position: relative;
    display: inline-flex;
    align-items: center;
    background-color: #ee4d2d;
    color: white;
    font-size: 10px; /* Ukuran font sangat kecil */
    font-weight: 600;
    margin-bottom: 6px;
    padding: 2px 6px 2px 24px; /* Padding sangat kecil */
    border-radius: 2px; /* Border radius sangat kecil */
    box-shadow: 0 1px 2px rgba(238, 77, 45, 0.3);
}

.termurah-label-9:before {
    content: "\f164";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    left: -3px;
    top: 50%;
    transform: translateY(-50%);
    background-color: white;
    color: #ee4d2d;
    width: 24px; /* Ukuran ikon sangat kecil */
    height: 24px; /* Ukuran ikon sangat kecil */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px; /* Ukuran ikon jempol sangat kecil */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    border: 1px solid #ee4d2d;
}
        /* Label Termurah */
        .termurah-label {
            display: inline-flex;
            align-items: center;
            background-color: #ffeee8;
            color: #ee4d2d;
            font-size: 9px;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 2px;
            margin-bottom: 4px;
            border: 1px solid #ee4d2d;
        }
        
        .termurah-label:before {
            content: "\f164"; /* Ikon jempol dari Font Awesome */
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            margin-right: 3px;
            font-size: 8px;
        }
         .product-rating-sold {
            display: flex;
            align-items: center;
            font-size: 10px;
            color: #666;
             margin-top: 6px;
            margin-bottom: 6px;
            line-height: 1.2;
            position: relative;
            white-space: nowrap;
        }

        .product-rating {
            color: #ee4d2d;
            margin-right: 4px;
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }

        .product-rating i {
            font-size: 9px;
            margin-right: 1px;
        }

        .product-sold {
            color: #666;
            position: relative;
            padding-left: 4px;
            flex-shrink: 0;
        }

        .product-sold:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            height: 8px;
            width: 1px;
            background-color: #ccc;
        }

        .cod-icon {
            display: inline-flex;
            align-items: center;
            background-color: #ee4d2d;
            color: white;
            font-size: 9px;
            font-weight: bold;
            padding: 1px 2px;
            border-radius: 2px;
            height: 16px;
            width: 40px; 
            position: absolute;
            right: 0;
            top: 0;
        }

        .cod-icon i {
            font-size: 10px;
            margin-right: 4px;
        }

        .product-shipping {
            display: flex;
            align-items: center;
            font-size: 10px;
            color: #666;
            margin-bottom: 6px;
        }

        .product-shipping i {
            color: #00bfa5;
            margin-right: 2px;
            font-size: 10px;
        }

        .product-price-container {
            display: flex;
            align-items: center;
            margin-bottom: 3px;
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
        }

        .product-price {
            font-size: 14px;
            color: #ee4d2d;
            font-weight: bold;
            flex-shrink: 0;
            margin-right: 3px;
        }

        .product-price-original {
            font-size: 10px;
            color: #999;
            text-decoration: line-through;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 40%;
            display: inline-block;
        }

        .product-discount {
            font-size: 8px;
            color: #ee4d2d;
            background-color: #ffeee8;
            padding: 1px 2px;
            border-radius: 2px;
            margin-left: 3px;
            flex-shrink: 0;
        }

        /* Video container styles untuk Short Video (9:16 ratio) */
        .video-container {
    position: relative;
    width: 100%;
    padding-top: 177.78%; /* 16:9 = 56.25%, 9:16 = 177.78% untuk Short video */
    overflow: hidden;
    background-color: #000;
    border-bottom: 1px solid #f2f2f2; /* Menambahkan bingkai video */
    border: 3px solid white;
    border-radius: 6px;
    contain: layout size;
   }

/* Memperbaiki rasio video di desktop agar sama dengan mobile */
@media (min-width: 768px) {
    .video-container {
        padding-top: 177.78% !important; /* Mempertahankan rasio 9:16 di desktop */
    }
}

.video-thumbnail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
    will-change: transform;
}

        .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            background: rgba(0,0,0,0.5);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
            pointer-events: none; /* Penting! Agar tombol play tidak menangkap event */
        }

        .play-icon i {
            font-size: 24px;
        }

        .youtube-badge {
            /* Menghapus badge YouTube Short */
            display: none;
        }

        /* Layout khusus untuk produk video */
        .video-product-info {
            padding: 8px;
            padding-bottom: 12px;
        }

        .video-product-flex {
            display: flex;
            margin-bottom: 5px;
        }

        .mini-product-img {
            width: 55px; /* Memperbesar ukuran image bawah video */
            height: 55px; /* Memperbesar ukuran image bawah video */
            border-radius: 3px;
            overflow: hidden;
            margin-right: 6px;
            flex-shrink: 0;
        }

        .mini-product-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            padding-bottom: 5px;
            will-change: transform;
        }

        .product-details {
            flex: 1;
        }

        .video-product-name {
            font-size: 12px;
            color: #333;
            font-weight: 500;
            margin-bottom: 4px;
            /* Membuat teks 2 baris dengan efek terpotong */
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.3;
            max-height: 2.6em;
        }

        .live-badge {
            display: inline-flex;
            align-items: center;
            background-color: #ee4d2d;
            color: white;
            font-size: 9px;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 2px;
            margin-bottom: 6px;
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 3;
        }

        .live-badge .dot {
            width: 6px;
            height: 6px;
            background-color: white;
            border-radius: 50%;
            margin-right: 3px;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(0.8); opacity: 0.7; }
            50% { transform: scale(1.2); opacity: 1; }
            100% { transform: scale(0.8); opacity: 0.7; }
        }

        .video-price {
            font-size: 14px;
            color: #ee4d2d;
            font-weight: bold;
        }
        
        .video-price-container {
            display: flex;
            align-items: center;
        }
        
        .video-price-original {
            font-size: 10px;
            color: #999;
            text-decoration: line-through;
            margin-left: 5px;
        }

        .video-product-rating-sold {
            display: flex;
            align-items: center;
            font-size: 10px;
            color: #666;
            margin-bottom: 6px;
        }

        .video-product-rating {
            color: #ee4d2d;
            margin-right: 4px;
            display: flex;
            align-items: center;
        }

        .video-product-rating i {
            font-size: 9px;
            margin-right: 1px;
        }

        .video-product-sold {
            color: #666;
            position: relative;
            padding-left: 4px;
        }

        .video-product-sold:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            height: 8px;
            width: 1px;
            background-color: #ccc;
        }

        /* Youtube embed */
        .youtube-embed {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            border: none;
            display: none;
        }
        
        .youtube-embed.active {
            z-index: 3;
            display: block;
        }

        /* Tidak menampilkan elemen secara default */
        .product-card {
            opacity: 0;
            transform: translateY(15px);
            transition: opacity 0.5s ease, transform 0.5s ease;
            will-change: opacity, transform;
        }

        .product-card.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Grid sizer untuk Masonry - Hidden */
        .grid-sizer {
            width: 50%; /* 2 kolom di mobile */
        }

        /* NEW SUPER COMPACT FLASH SALE */
        .flash-compact {
            margin: 0 5px 1px 5px;
            display: flex;
            background-color: #fff0f0;
            border: 1px solid #ffccc7;
            border-radius: 4px;
            padding: 5px 3px;
            align-items: center;
        }
        
        .flash-compact-left {
            flex: 1;
            min-width: 0;
            margin-right: 4px;
        }
        
        .flash-compact-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2px;
        }
        
        .flash-compact-title {
            color: #ff4d4f;
            font-size: 9px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .flash-compact-title i {
            margin-right: 2px;
            transform: translateX(0px); /* Geser ke kiri tanpa mempengaruhi elemen lain */
            transform: translatey(-3px);
            font-size: 8px;
        }
        
        .flash-compact-timer {
            display: flex;
            align-items: center;
            font-size: 8px;
            color: #666;
        }
        
        .flash-compact-time {
            background-color: #ff4d4f;
            color: white;
            font-size: 8px;
            font-weight: bold;
            padding: 0px 0px;
            margin-right: 0px;
            border-radius: 2px;
            min-width: 12px;
            text-align: center;
            display: inline-block;
           transform: translateX(2px); /* Geser ke kanan sejauh 5 piksel */
        }
        
        .flash-compact-price {
            display: flex;
            align-items: baseline;
        }
        
        .flash-compact-current {
            font-size: 12px;
            font-weight: bold;
            color: #ff4d4f;
            margin-right: 3px;
            white-space: nowrap;
        }
        
        .flash-compact-original {
            font-size: 9px;
            color: #999;
            text-decoration: line-through;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 45px;
        }
        
        .flash-compact-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }
        
        .flash-compact-remaining {
            font-size: 8px;
            font-weight: bold;
            color: #ff4d4f;
            margin-bottom: 3px;
            transform: translateX(-3px); /* Geser ke kanan sejauh 5 piksel */
        }
        
        .flash-compact-progress {
            width: 40px;
            height: 3px;
            background-color: #ffe8e6;
            border-radius: 2px;
            overflow: hidden;
            position: relative;
        }
        
        .flash-compact-fill {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 75%;
            background: linear-gradient(90deg, #ffbb96, #ff4d4f);
            border-radius: 2px;
            background-size: 200% 100%;
            animation: shimmer 2s infinite linear;
            will-change: transform;
        }
        
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Image Sled Styles - NEW */
        .image-sled-container {
            position: relative;
            width: 100%;
            padding-top: 177.78%; /* Sama seperti video container (9:16 ratio) */
            overflow: hidden;
            background-color: #fff;
            border: 3px solid white;
            border-radius: 6px;
            contain: layout size;
        }

        .image-sled {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            display: flex;
            transition: transform 0.5s ease;
            will-change: transform;
        }

        .image-sled-item {
            height: 100%;
            flex-shrink: 0;
            position: relative;
        }

        .image-sled-item a {
            display: block;
            width: 100%;
            height: 100%;
        }

        .image-sled-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
            will-change: transform;
        }

        .image-sled-nav {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 5px;
            z-index: 2;
        }

        .image-sled-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .image-sled-dot.active {
            background-color: white;
            transform: scale(1.2);
        }

        /* Media query untuk tampilan desktop - 3 kolom lebih lebar */
        @media (min-width: 768px) {
            .container {
                padding: 15px;
                max-width: 1200px; /* Memperlebar container di desktop */
                margin: 0 auto;
            }
            
            .grid-sizer {
                width: 33.333%; /* 3 kolom di desktop */
            }
            
            .product-card {
                margin-bottom: 15px;
            }
            
            .product-info {
                padding: 12px; /* Padding lebih besar di desktop */
                padding-bottom: 16px; /* Padding bawah lebih besar */
            }
            
            .product-rating-sold {
                font-size: 12px;
            }
            
            .product-rating i {
                font-size: 11px;
                margin-right: 2px;
            }
            
            .product-sold {
                padding-left: 8px;
            }
            
            .termurah-label {
                font-size: 11px;
                padding: 3px 6px;
            }
            
            .termurah-label:before {
                font-size: 10px;
            }
            
            .cod-icon {
                font-size: 9px;
                padding: 2px 4px;
                height: 16px;
            }
            
            .cod-icon i {
                font-size: 10px;
                margin-right: 2px;
              }
            
            .product-shipping {
                font-size: 11px;
                margin-bottom: 5px;
            }
            
            .product-shipping i {
                margin-right: 4px;
                font-size: 12px;
            }
            
            .product-price {
                font-size: 14px;
            }
            
            .product-price-original {
                font-size: 12px;
                margin-left: 5px;
                max-width: 45%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            
            .product-discount {
                font-size: 10px;
                padding: 1px 4px;
                margin-left: 5px;
            }
            
            .product-name {
                font-size: 14px;
            }
            
            /* Memperlebar kartu produk */
            .product-card {
                border-radius: 6px; /* Radius border lebih besar */
            }
            
            .product-img-container {
                height: 0;
                padding-top: 100%; /* Mempertahankan rasio aspek 1:1 */
            }
            
            .play-icon {
                width: 60px;
                height: 60px;
            }
            
            .play-icon i {
                font-size: 32px;
            }
            
            .youtube-badge {
                font-size: 12px;
                padding: 3px 5px;
            }
            
            .youtube-badge i {
                font-size: 14px;
            }
            
            /* Video product layout */
            .video-product-info {
                padding: 12px;
                padding-bottom: 16px;
            }
            
            .mini-product-img {
                width: 56px; /* Memperbesar ukuran image bawah video di desktop */
                height: 56px; /* Memperbesar ukuran image bawah video di desktop */
                margin-right: 10px;
            }
            
            .video-product-name {
                font-size: 14px;
            }
            
            .live-badge {
                font-size: 11px;
                padding: 3px 8px;
            }
            
            .live-badge .dot {
                width: 8px;
                height: 8px;
            }
            
            .video-price {
                font-size: 16px;
            }
            
            .video-price-original {
                font-size: 12px;
            }
            
            .video-product-rating-sold {
                font-size: 12px;
            }
            
            .video-product-rating i {
                font-size: 11px;
            }
            
            /* Super Compact Flash Sale Desktop */
            .flash-compact {
                margin: 0 8px 10px 8px;
                padding: 9px 2px;
            }
            
            .flash-compact-title {
                font-size: 11px;
               transform: translateX(5px)
            }
            
            .flash-compact-title i {
                margin-right: 2px;
                transform: translateX(-2px); /* Geser ke kiri tanpa mempengaruhi elemen lain */
                font-size: 11px;
            }
            
            .flash-compact-timer {
                font-size: 10px;
            }
            
            .flash-compact-time {
                font-size: 8px;
                padding: 1px 2px;
                min-width: 16px;
            }
            
            .flash-compact-current {
                font-size: 14px;
                margin-right: 5px;
            }
            
            .flash-compact-original {
                font-size: 11px;
                max-width: 60px;
            }
            
            .flash-compact-remaining {
                font-size: 10px;
                margin-bottom: 4px;
            }
            
            .flash-compact-progress {
                width: 50px;
                height: 4px;
            }

            /* Image Sled untuk Desktop */
            .image-sled-nav {
                bottom: 15px;
            }

            .image-sled-dot {
                width: 8px;
                height: 8px;
                gap: 8px;
            }
        }

        /* Grid item width style untuk Masonry */
        .grid-item {
            width: calc(50% - 7px); /* 2 kolom dengan jarak total 10px */
            margin-bottom: 10px;
        }

        @media (min-width: 768px) {
            .grid-item {
                width: calc(33.333% - 10px); /* 3 kolom dengan jarak total 15px */
                margin-bottom: 15px;
            }
        }
        
        /* Optimasi untuk animation di devices dengan preferensi reduced motion */
        @media (prefers-reduced-motion: reduce) {
            .live-shopee-3 .wave-bar,
            .termurah-label-9:before,
            .flash-compact-fill,
            .live-badge .dot {
                animation: none !important;
            }
            .product-card {
                transition: none !important;
            }
            .image-sled {
                transition: none !important;
            }
        }
		/* Tambahan CSS untuk tampilan subkategori */
.subcategory-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: white;
    z-index: 1000;
    display: none;
    flex-direction: column;
    overflow: hidden;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    left: 0;
    right: 0;
}

.subcategory-header {
    background-color: white;
    padding: 15px;
    display: flex;
    align-items: center;
    box-shadow: none;
    z-index: 10;
}

.back-button {
    color: #ee4d2d;
    font-size: 20px;
    margin-right: 15px;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    cursor: pointer;
}

.back-button i {
    transform: scaleX(1.3);
}

.subcategory-header h1 {
    font-size: 18px;
    font-weight: 500;
}

.subcategory-container {
    display: flex;
    height: calc(100% - 70px);
    overflow: hidden;
    position: relative;
}

.category-sidebar {
    border-top: 1px solid #eee;
    width: 100px;
    background-color: #f9f9f9;
    overflow-y: auto;
    flex-shrink: 0;
    box-shadow: 3px 0 5px -2px rgba(0, 0, 0, 0.1);
    height: 100%;
    position: relative;
    z-index: 1;
}

.category-sidebar::-webkit-scrollbar {
    display: none;
}

.sidebar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    text-align: center;
}

.sidebar-item.active {
    background-color: white;
}

.sidebar-icon {
    color: #FF5722;
    margin-bottom: 6px;
}

.sidebar-item.active .sidebar-icon {
    color: #FF5722;
}

.sidebar-text {
    font-size: 11px;
    color: #555;
    text-align: center;
}

.sidebar-item.active .sidebar-text {
    color: #333;
    font-weight: 500;
}

.subcategory-content {
    flex-grow: 1;
    padding: 15px;
    background-color: white;
    overflow-y: auto;
    margin-left: 1px;
}

.subcategory-content::-webkit-scrollbar {
    display: none;
}

.subcategory-banner {
    width: 100%;
    margin-bottom: 15px;
    border-radius: 8px;
    overflow: hidden;
}

.subcategory-banner img {
    width: 100%;
    height: auto;
    display: block;
}

.subcategory-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.subcategory-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    height: 100px;
    width: 100%;
}

.subcategory-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.subcategory-title {
    font-size: 12px;
    font-weight: normal;
    color: #333;
    text-align: center;
    line-height: 1.3;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

@media (min-width: 768px) {
    .category-sidebar {
        width: 150px;
    }
    
    .sidebar-item {
        padding: 15px 10px;
    }
    
    .sidebar-text {
        font-size: 12px;
    }
    
    .subcategory-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .subcategory-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.category-icon svg,
.expanded-icon svg,
.subcategory-icon svg {
    color: #FF5722 !important;
}

.category-item {
    /* Pastikan menggunakan style dari kode 2 */
    border: 1px solid #e0e0e0; 
    background-color: #ffffff;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.expanded-item {
    /* Tambahkan style box-shadow yang lebih baik */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
		
	@media (min-width: 768px) {
    .desktop-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr) !important;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 12px;
    }
    
    .desktop-grid .expanded-item {
        height: 80px;
        width: 100%;
        border: 1px solid #f0f0f0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
}

.category-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #FF5722; /* Warna oranye default untuk semua ikon */
}	
	
@media (min-width: 768px) {
    .category-sidebar {
        width: 150px;
    }
    
    .sidebar-item {
        padding: 15px 10px;
    }
    
    .sidebar-text {
        font-size: 12px;
    }
    
    .subcategory-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .subcategory-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

body.subcategory-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
}

@media (min-width: 768px) {
    .subcategory-view {
     
    max-width: 900px;
  }
}

@media (min-width: 1200px) {
    .subcategory-view {
     
    max-width: 700px;
  }
}
	
    </style>
    
    <!-- Masonry Layout Library akan dimuat secara asynchronous di bagian script -->
</head>
<body>
    <div class="categories-section">
        <!-- Mobile view -->
        <div id="categoryContainer" class="categories-container">
            <!-- Kategori akan diisi melalui JavaScript -->
        </div>
        
        <!-- Desktop grid view for first 13 items + "Lihat Semua" button -->
        <div id="desktopGrid" class="desktop-grid">
            <!-- Desktop grid akan diisi melalui JavaScript -->
        </div>
        
        <!-- Extra categories that show after clicking "Lihat Semua" in desktop -->
        <div id="extraCategories" class="extra-categories">
            <!-- Extra categories akan diisi melalui JavaScript -->
        </div>
        
        <!-- Expanded Categories (for mobile) -->
        <div id="expandedCategories" class="expanded-categories">
            <!-- Kategori yang diperluas untuk mobile akan diisi melalui JavaScript -->
        </div>
    </div>
	<!-- Tampilan Subkategori -->
<div id="subcategoryView" class="subcategory-view">
    <div class="subcategory-header">
        <div id="backButton" class="back-button"><i class="fa fa-arrow-left"></i></div>
        <h1>Kategori</h1>
    </div>
    
    <div class="subcategory-container">
        <div id="categorySidebar" class="category-sidebar">
            <!-- Sidebar kategori akan diisi oleh JavaScript -->
        </div>
        
        <div id="subcategoryContent" class="subcategory-content">
            <!-- Konten subkategori akan diisi oleh JavaScript -->
        </div>
    </div>
</div>
    
    <div class="container1">
        <!-- Shopee Live Section -->
        <div class="section" onclick="window.location.href='https://www.facebook.com'">
            <div class="section-header">
                <div class="section-title">
                    Shopee Live
                    <span class="title-icon">›</span>
                </div>
            </div>
            
            <div class="videos-grid">
                <!-- Live Video 1 -->
                <div class="video-card" onclick="window.location.href='https://www.facebook.com'">
                    <div class="video-thumbnail" style="background-image: url('https://i3.ytimg.com/vi/dfkkd-Byc8k/maxresdefault.jpg');"></div>
                    <div class="label"><span class="dot"></span>LIVE</div>
                    <div class="video-title">Diskon sembako</div>
                </div>
                
                <!-- Live Video 2 -->
                <div class="video-card" onclick="window.location.href='https://www.facebook.com'">
                    <div class="video-thumbnail" style="background-image: url('https://i3.ytimg.com/vi/dfkkd-Byc8k/maxresdefault.jpg');"></div>
                    <div class="label"><span class="dot"></span>LIVE</div>
                    <div class="video-title">SPESIAL SALE!! APLIKASI</div>
                </div>
            </div>
        </div>
        
        <!-- Shopee Video Section -->
        <div class="section" onclick="window.location.href='https://www.facebook.com'">
            <div class="section-header">
                <div class="section-title">
                    Shopee Video
                    <span class="title-icon">›</span>
                </div>
            </div>
            
            <div class="videos-grid">
                <!-- Video 1 -->
                <div class="video-card" onclick="window.location.href='https://www.facebook.com'">
                    <div class="video-thumbnail" style="background-image: url('https://i3.ytimg.com/vi/dfkkd-Byc8k/maxresdefault.jpg');"></div>
                    <div class="view-count">
                        <svg class="view-icon" viewBox="0 0 24 24" fill="white">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                        </svg>
                        26,5RB
                    </div>
                    <svg class="play-icon" viewBox="0 0 24 24" fill="white">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                </div>
                
                <!-- Video 2 -->
                <div class="video-card" onclick="window.location.href='https://www.facebook.com'">
                    <div class="video-thumbnail" style="background-image: url('https://i3.ytimg.com/vi/dfkkd-Byc8k/maxresdefault.jpg');"></div>
                    <div class="view-count">
                        <svg class="view-icon" viewBox="0 0 24 24" fill="white">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                        </svg>
                        379,4RB
                    </div>
                    <svg class="play-icon" viewBox="0 0 24 24" fill="white">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <!-- PRODUK SECTION - Semua produk dalam satu grid -->
    <div class="container">
        <div class="masonry-grid">
            <div class="grid-sizer"></div>
            
            <!-- Produk 1 (Diganti dengan Image Sled Murni) -->
            <div class="product-card grid-item" id="product1">
                <div class="image-sled-container">
                    <div class="image-sled">
                        <!-- 6 Slides -->
                        <div class="image-sled-item">
                            <a href="https://sellzio.com/image1">
                                <img src="https://cdn.scalev.id/Image/shdBj8gwgQ9tppXObDzEkxvHOA4Hgv7op51P9qiiaho/1741573273658-Merah_Kuning_Putih_Sede_9J5Kipp.webp" alt="Slide 1" loading="eager" fetchpriority="high" width="400" height="711">
                            </a>
                        </div>
                        <div class="image-sled-item">
                            <a href="https://sellzio.com/image2">
                                <img src="https://cdn.scalev.id/Image/j91VbIHCt69a2I5Pp1TBJea5y6PE8i9Uca_1T6x8Ur8/1741573302866-Merah_dan_Cokelat_Moder_YZwHzct.webp" alt="Slide 2" loading="lazy" fetchpriority="low" width="400" height="711">
                            </a>
                        </div>
                        <div class="image-sled-item">
                            <a href="https://sellzio.com/image3">
                                <img src="https://cdn.scalev.id/Image/YJqhog_FTXvqb0-gUqGWiKhXP7zVpJJYBrR83jqeZA0/1741573417648-Coklat_Putih_Minimalis__twyWd3l.webp" alt="Slide 3" loading="lazy" fetchpriority="low" width="400" height="711">
                            </a>
                        </div>
                        <div class="image-sled-item">
                            <a href="https://sellzio.com/image4">
                                <img src="https://cdn.scalev.id/Image/shdBj8gwgQ9tppXObDzEkxvHOA4Hgv7op51P9qiiaho/1741573273658-Merah_Kuning_Putih_Sede_9J5Kipp.webp" alt="Slide 4" loading="lazy" fetchpriority="low" width="400" height="711">
                            </a>
                        </div>
                        <div class="image-sled-item">
                            <a href="https://sellzio.com/image5">
                                <img src="https://cdn.scalev.id/Image/j91VbIHCt69a2I5Pp1TBJea5y6PE8i9Uca_1T6x8Ur8/1741573302866-Merah_dan_Cokelat_Moder_YZwHzct.webp" alt="Slide 5" loading="lazy" fetchpriority="low" width="400" height="711">
                            </a>
                        </div>
                        <div class="image-sled-item">
                            <a href="https://sellzio.com/image6">
                                <img src="https://cdn.scalev.id/Image/YJqhog_FTXvqb0-gUqGWiKhXP7zVpJJYBrR83jqeZA0/1741573417648-Coklat_Putih_Minimalis__twyWd3l.webp" alt="Slide 6" loading="lazy" fetchpriority="low" width="400" height="711">
                            </a>
                        </div>
                        <!-- Clone slide untuk infinite loop akan ditambahkan melalui JS -->
                    </div>
                    <div class="image-sled-nav">
                        <div class="image-sled-dot active" data-index="0"></div>
                        <div class="image-sled-dot" data-index="1"></div>
                        <div class="image-sled-dot" data-index="2"></div>
                        <div class="image-sled-dot" data-index="3"></div>
                        <div class="image-sled-dot" data-index="4"></div>
                        <div class="image-sled-dot" data-index="5"></div>
                    </div>
                </div>
            </div>

            <!-- Produk 2 -->
            <div class="product-card grid-item" id="product2">
                <a href="https://www.example.com/product2">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                          <div class="mall-label">
                <div class="mall-primary">Mall</div>
                <div class="mall-secondary">ORI</div>
            </div>
              
                            Smartphone Samsung Galaxy A54 RAM 8GB 
                        </div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.8</div>
                            <div class="product-sold">Terjual 98</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 5.499.000</div>
                            <div class="product-price-original">Rp 6.299.000 Rp 6.299.000 Rp 6.299.000</div>
                            <div class="product-discount">-13%</div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Produk 3 -->
            <div class="product-card grid-item" id="product3">
                <a href="https://www.example.com/product3">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <!-- Label Live dengan Sound Wave di sini -->
                           <div class="live-shopee-3">
                    <div class="sound-wave">
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                     <div class="wave-bar"></div>
                    </div>
                     <span class="live-text">LIVE</span>
                    </div>  
                            Smartphone Samsung Galaxy S23 Ultra RAM 12GB ROM 512GB Snapdragon 8 Gen 2 Original
                        </div>
                      <div class="termurah-label-9">Termurah di Toko</div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.9</div>
                            <div class="product-sold">Terjual 42</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 17.999.000</div>
                            <div class="product-price-original">Rp 21.999.000</div>
                            <div class="product-discount">-18%</div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Produk 4 (Video dengan Flash Sale baru) -->
            <div class="product-card grid-item" id="product4">
                <a href="https://www.example.com/product4" class="product-link">
                    <div class="product-img-container video-container">
                        <!-- Thumbnail YouTube -->
                        <img src="https://i3.ytimg.com/vi/dfkkd-Byc8k/maxresdefault.jpg" alt="Video Preview" class="video-thumbnail" loading="lazy" fetchpriority="low" width="360" height="640">
                        
                        <!-- Ikon play untuk menunjukkan bahwa ini adalah video -->
                        <div class="play-icon">
                            <i class="fa fa-play"></i>
                        </div>
                        
                        <!-- Live badge dipindah ke pojok kanan atas -->
                        <div class="live-badge">
                            <div class="dot"></div> LIVE
                        </div>
                        
                        <!-- YouTube embed iframe (hidden by default) -->
                        <!-- iframe akan di-load hanya ketika thumbnail diklik -->
                        <div class="youtube-embed-placeholder" data-embed-src="https://www.youtube.com/embed/dfkkd-Byc8k?rel=0&modestbranding=1"></div>
                        
                        <!-- NEW FLASH SALE COMPACT STYLE (Overlay) -->
                        <div style="position: absolute; bottom: 0%; left: 0; width: 100%; z-index: 10; padding: 4px;">
                            <div class="flash-compact" style="margin: 0;">
                                <div class="flash-compact-left">
                                    <div class="flash-compact-header">
                                      <div class="flash-compact-title"><i class="fa fa-bolt"></i>FLASH SALE</div>
                                        <div class="flash-compact-timer">
                                            <span class="flash-compact-time hours" data-hours="01">01</span>:
                                            <span class="flash-compact-time minutes" data-minutes="15">15</span>:
                                            <span class="flash-compact-time seconds" data-seconds="22">22</span>
                                        </div>
                                    </div>
                                    <div class="flash-compact-price">
                                        <span class="flash-compact-current">Rp 1.299.000</span>
                                        <span class="flash-compact-original">Rp 1.599.000</span>
                                    </div>
                                </div>
                                <div class="flash-compact-right">
                                    <div class="flash-compact-remaining">Sisa 25</div>
                                    <div class="flash-compact-progress">
                                        <div class="flash-compact-fill"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Format info produk yang diminta -->
                    <div class="video-product-info" style="padding-top: 5px;">
                        <!-- TAMBAHKAN LABEL LIVE+DISKON DI SINI -->
            <div class="live-discount-label">
                <div class="live-label">
                    <div class="sound-wave">
                        <span class="wave-bar"></span>
                        <span class="wave-bar"></span>
                        <span class="wave-bar"></span>
                    </div>
                    <span>Live</span>
                </div>
                <div class="discount-label">Diskon 12%</div>
            </div>
            
                        <div class="video-product-flex">
                            <!-- Gambar produk kecil di kiri -->
                            <div class="mini-product-img" style="width: 45px; height: 45px;">
                                <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Headphone" loading="lazy" fetchpriority="low" width="48" height="48">
                            </div>
                            
                            <!-- Detail produk di kanan -->
                            <div class="product-details">
                                <!-- Nama produk dengan icon Star -->
                                <div class="video-product-name" style="margin-bottom: 2px;">
                                    <span class="shopee-star-icon">Star</span>
                                    Headphone JBL
                                </div>
                                
                                <!-- Rating dan jumlah terjual -->
                                <div class="video-product-rating-sold" style="margin-bottom: 3px;">
                                    <div class="video-product-rating"><i class="fa fa-star"></i>4.8</div>
                                    <div class="video-product-sold">Terjual 156</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Harga dengan harga coret di sebelahnya -->
                        <div class="video-price-container">
                            
                        </div>
                    </div>
                </a>
            </div>

            <!-- Produk 5 -->
            <div class="product-card grid-item" id="product5">
                <a href="https://www.example.com/product5">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                          <div class="star-label">
                    <div class="star-primary">Star</div>
                    <div class="star-secondary">Plus</div>
                </div>
                            Tas Selempang Wanita Model Korea
                        </div>
                        <div class="termurah-label">TERMURAH</div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.8</div>
                            <div class="product-sold">Terjual 215</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 129.000</div>
                            <div class="product-price-original">Rp 199.000</div>
                            <div class="product-discount">-35%</div>
                        </div>
                    </div>
                </a>
            </div>
          <!-- Produk 6 -->
            <div class="product-card grid-item" id="product6">
                <a href="https://www.example.com/product6">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <span class="shopee-star-icon">Star+</span>
                            Power Bank Xiaomi 10000mAh Fast Charging 33W Original
                        </div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.9</div>
                            <div class="product-sold">Terjual 352</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 349.000</div>
                            <div class="product-price-original">Rp 399.000</div>
                            <div class="product-discount">-13%</div>
                        </div>
                    </div>
                </a>
            </div>
            <!-- Produk 7 -->
            <div class="product-card grid-item" id="product7">
                <a href="https://www.example.com/product7">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    
                    <!-- NEW FLASH SALE COMPACT STYLE -->
                    <div class="flash-compact"style="margin-top: 5px;">
                        <div class="flash-compact-left">
                            <div class="flash-compact-header">
                                <div class="flash-compact-title"><i class="fa fa-bolt"></i>FLASH SALE</div>
                                <div class="flash-compact-timer">
                                    <span class="flash-compact-time hours" data-hours="02">02</span>:
                                    <span class="flash-compact-time minutes" data-minutes="30">30</span>:
                                    <span class="flash-compact-time seconds" data-seconds="45">45</span>
                                </div>
                            </div>
                            <div class="flash-compact-price">
                                <span class="flash-compact-current">Rp 229.000</span>
                                <span class="flash-compact-original">Rp 299.000</span>
                            </div>
                        </div>
                        <div class="flash-compact-right">
                            <div class="flash-compact-remaining">Sisa 15</div>
                            <div class="flash-compact-progress">
                                <div class="flash-compact-fill"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-info">
                        <div class="product-name">
                            Sepatu Sneakers Pria Casual
                        </div>
                        <div class="termurah-label">TERMURAH</div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.6</div>
                            <div class="product-sold">Terjual 189</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Reguler</div>
                        <!-- Harga asli disembunyikan karena sudah ditampilkan di flash sale -->
                        <div class="product-price-container" style="display: none;">
                            <div class="product-price">Rp 229.000</div>
                            <div class="product-price-original">Rp 299.000 Rp 299.000 Rp 299.000</div>
                            <div class="product-discount">-24%</div>
                        </div>
                    </div>
                </a>
            </div>
            <!-- Produk 8 -->
            <div class="product-card grid-item" id="product8">
                <a href="https://www.example.com/product8">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <span class="shopee-star-icon">Star</span>
                            Smart TV LED 43 inch 4K UHD Android TV
                        </div>
                        <div class="termurah-label">TERMURAH</div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.8</div>
                            <div class="product-sold">Terjual 56</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Reguler</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 4.299.000</div>
                            <div class="product-price-original">Rp 5.299.000 Rp 5.299.000</div>
                            <div class="product-discount">-19%</div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Produk 9 -->
            <div class="product-card grid-item" id="product9">
                <a href="https://www.example.com/product9">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <span class="shopee-star-icon">Star+</span>
                            Laptop Gaming 15.6 inch Core i7 RTX 3060 16GB RAM
                        </div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.9</div>
                            <div class="product-sold">Terjual 37</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 15.999.000</div>
                            <div class="product-price-original">Rp 18.999.000 Rp 18.999.000 Rp 18.999.000 Rp 18.999.000</div>
                            <div class="product-discount">-16%</div>
                        </div>
                    </div>
                </a>
            </div>
          <!-- Produk 10 (Video) -->
            <div class="product-card grid-item" id="product10">
                <a href="https://www.example.com/product10" class="product-link">
                    <div class="product-img-container video-container"style="clip-path: inset(0 0 23% 0);">
                        <!-- Thumbnail YouTube -->
                        <img src="https://i3.ytimg.com/vi/dfkkd-Byc8k/maxresdefault.jpg" alt="Video Preview" class="video-thumbnail" loading="lazy" fetchpriority="low" width="360" height="640">
                        
                        <!-- Ikon play untuk menunjukkan bahwa ini adalah video -->
                        <div class="play-icon">
                            <i class="fa fa-play"></i>
                        </div>
                        
                        <!-- Live badge dipindah ke pojok kanan atas -->
                        <div class="live-badge">
                            <div class="dot"></div> LIVE
                        </div>
                        
                        <!-- YouTube embed iframe (hidden by default) -->
                        <div class="youtube-embed-placeholder" data-embed-src="https://www.youtube.com/embed/dfkkd-Byc8k?rel=0&modestbranding=1"></div>
                    </div>
                    
                    <!-- Format info produk yang diminta -->
                    <div class="video-product-info"style="margin-top: -40%; position: relative; z-index: 1;">
                        <div class="video-product-flex">
                            <!-- Gambar produk kecil di kiri -->
                            <div class="mini-product-img">
                                <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Jam Tangan" loading="lazy" fetchpriority="low" width="48" height="48">
                            </div>
                            
                            <!-- Detail produk di kanan -->
                            <div class="product-details">
                                <!-- Nama produk dengan icon Star -->
                                <div class="video-product-name">
                                    <span class="shopee-star-icon">Star+</span>
                                    Jam Digital
                                </div>
                              <!-- LABEL LIVE+DISKON DENGAN UKURAN LEBIH KECIL -->
<div class="live-discount-label" style="height: 18px; font-size: 8px; margin-top: 2px; margin-bottom: 5px;">
    <div class="live-label" style="padding: 0 4px 0 12px; font-size: 9px;">
        <div class="sound-wave" style="left: 4px; height: 9px; gap: 1px;">
            <span class="wave-bar" style="width: 1px; height: 6px;"></span>
            <span class="wave-bar" style="width: 1px; height: 9px;"></span>
            <span class="wave-bar" style="width: 1px; height: 4px;"></span>
        </div>
        <span>Live</span>
    </div>
    <div class="discount-label" style="padding: 0 4px; font-size: 9px;">Diskon 12%</div>
</div>
                              
                            </div>
                        </div>
                        
                        <!-- Harga dengan harga coret di sebelahnya -->
                        <div class="video-price-container">
                            <div class="video-price">Rp 169.000</div>
                            <div class="video-price-original">Rp 249.000</div>
                        </div>
                    </div>
                </a>
            </div>
			
            <!-- Produk 11 (Video) -->
            <div class="product-card grid-item" id="product11">
                <a href="https://www.example.com/product11" class="product-link">
                    <div class="product-img-container video-container"style="clip-path: inset(0 0 23% 0);">
                        <!-- Thumbnail YouTube -->
                        <img src="https://i3.ytimg.com/vi/dfkkd-Byc8k/maxresdefault.jpg" alt="Video Preview" class="video-thumbnail" loading="lazy" fetchpriority="low" width="360" height="640">
                        
                        <!-- Ikon play untuk menunjukkan bahwa ini adalah video -->
                        <div class="play-icon">
                            <i class="fa fa-play"></i>
                        </div>
                        
                        <!-- Live badge dipindah ke pojok kanan atas -->
                        <div class="live-badge">
                            <div class="dot"></div> LIVE
                        </div>
                        
                        <!-- YouTube embed iframe (hidden by default) -->
                        <!-- iframe akan di-load hanya ketika thumbnail diklik -->
                        <div class="youtube-embed-placeholder" data-embed-src="https://www.youtube.com/embed/dfkkd-Byc8k?rel=0&modestbranding=1"></div>
                    <!-- NEW FLASH SALE COMPACT STYLE (Overlay) -->
                        <div style="position: absolute; bottom: 23%; left: 0; width: 100%; z-index: 10; padding: 4px;">
                            <div class="flash-compact" style="margin: 0;">
                                <div class="flash-compact-left">
                                    <div class="flash-compact-header">
                                      <div class="flash-compact-title"><i class="fa fa-bolt"></i>FLASH SALE</div>
                                        <div class="flash-compact-timer">
                                            <span class="flash-compact-time hours" data-hours="00">00</span>:
                                            <span class="flash-compact-time minutes" data-minutes="15">15</span>:
                                            <span class="flash-compact-time seconds" data-seconds="22">22</span>
                                        </div>
                                    </div>
                                    <div class="flash-compact-price">
                                        <span class="flash-compact-current">Rp 1.299.000</span>
                                        <span class="flash-compact-original">Rp 1.599.000</span>
                                    </div>
                                </div>
                                <div class="flash-compact-right">
                                    <div class="flash-compact-remaining">Sisa 25</div>
                                    <div class="flash-compact-progress">
                                        <div class="flash-compact-fill"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Format info produk yang diminta -->
                    <div class="video-product-info"style="margin-top: -40%; position: relative; z-index: 1;">
                        <div class="video-product-flex">
                            <!-- Gambar produk kecil di kiri -->
                            <div class="mini-product-img">
                                <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Headphone" loading="lazy" fetchpriority="low" width="48" height="48">
                            </div>
                            
                            <!-- Detail produk di kanan -->
                            <div class="product-details">
                                <!-- Nama produk dengan icon Star -->
                                <div class="video-product-name">
                                    <span class="shopee-star-icon">Star+</span>
                                    Headphone JBL Premium Wireless Bluetooth Noise Cancelling
                                </div>
                                
                                <!-- Rating dan jumlah terjual -->
                                <div class="video-product-rating-sold">
                                    <div class="video-product-rating"><i class="fa fa-star"></i>4.8</div>
                                    <div class="video-product-sold">Terjual 156</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Harga dengan harga coret di sebelahnya -->
                        <div class="video-price-container">
                            <div class="video-price">Rp 1.299.000</div>
                            <div class="video-price-original">Rp 1.599.000</div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Produk 12 -->
            <div class="product-card grid-item" id="product12">
                <a href="https://www.example.com/product12">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <span class="shopee-star-icon">Star+</span>
                            Smartphone Android Terbaru 2023
                        </div>
                        
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.9</div>
                            <div class="product-sold">Terjual 125</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 2.999.000</div>
                            <div class="product-price-original">Rp 3.499.000</div>
                            <div class="product-discount">-15%</div>
                        </div>
                    </div>
                </a>
            </div>
          <!-- Produk 13 -->
            <div class="product-card grid-item" id="product13">
                <a href="https://www.example.com/product13">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <span class="shopee-star-icon">Star+</span>
                            Tablet Samsung Terbaru
                        </div>
                      <!-- Badge KOMISI XTRA dengan spasi -->
<div class="komisi-xtra-label" style="margin-bottom: 8px;>
    <span class="komisi">KOMISI</span><span class="xtra">XTRA</span>
</div>
                      <!-- Badge Terlaris dengan accent border -->
<div class="terlaris-label">Terlaris</div>

                      
                      
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.8</div>
                            <div class="product-sold">Terjual 98</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 5.499.000</div>
                            <div class="product-price-original">Rp 6.299.000</div>
                            <div class="product-discount">-13%</div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Produk 14 -->
            <div class="product-card grid-item" id="product14">
                <a href="https://www.example.com/product14">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <span class="shopee-star-icon">Star+</span>
                            Smartphone Flagship Premium
                        </div>
                        <div class="termurah-label">TERMURAH</div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.9</div>
                            <div class="product-sold">Terjual 42</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 17.999.000</div>
                            <div class="product-price-original">Rp 21.999.000</div>
                            <div class="product-discount">-18%</div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Produk 15 -->
            <div class="product-card grid-item" id="product15">
                <a href="https://www.example.com/product15">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            Tas Ransel Pria Keren
                        </div>
                        <div class="termurah-label">TERMURAH</div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.8</div>
                            <div class="product-sold">Terjual 215</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 129.000</div>
                            <div class="product-price-original">Rp 199.000</div>
                            <div class="product-discount">-35%</div>
                        </div>
                    </div>
                </a>
            </div>
			 
            <!-- Produk 16 (Video) -->
            <div class="product-card grid-item" id="product16">
                <a href="https://www.example.com/product16" class="product-link">
                    <div class="product-img-container video-container">
                        <!-- Thumbnail YouTube -->
                        <img src="https://i3.ytimg.com/vi/dfkkd-Byc8k/maxresdefault.jpg" alt="Video Preview" class="video-thumbnail" loading="lazy" fetchpriority="low" width="360" height="640">
                        
                        <!-- Ikon play untuk menunjukkan bahwa ini adalah video -->
                        <div class="play-icon">
                            <i class="fa fa-play"></i>
                        </div>
                        
                        <!-- Live badge dipindah ke pojok kanan atas -->
                        <div class="live-badge">
                            <div class="dot"></div> LIVE
                        </div>
                        
                        <!-- YouTube embed iframe (hidden by default) -->
                        <div class="youtube-embed-placeholder" data-embed-src="https://www.youtube.com/embed/dfkkd-Byc8k?rel=0&modestbranding=1"></div>
                    </div>
                    
                    <!-- Format info produk yang diminta -->
                    <div class="video-product-info">
                        <div class="video-product-flex">
                            <!-- Gambar produk kecil di kiri -->
                            <div class="mini-product-img">
                                <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Jam Tangan" loading="lazy" fetchpriority="low" width="48" height="48">
                            </div>
                            
                            <!-- Detail produk di kanan -->
                            <div class="product-details">
                                <!-- Nama produk dengan icon Star -->
                                <div class="video-product-name">
                                    <span class="shopee-star-icon">Star</span>
                                    Jam Tangan Pria Sporty
                                </div>
                                
                                <!-- Rating dan jumlah terjual -->
                                <div class="video-product-rating-sold">
                                    <div class="video-product-rating"><i class="fa fa-star"></i>4.7</div>
                                    <div class="video-product-sold">Terjual 89</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Harga dengan harga coret di sebelahnya -->
                        <div class="video-price-container">
                            <div class="video-price">Rp 169.000</div>
                            <div class="video-price-original">Rp 249.000</div>
                        </div>
                    </div>
                </a>
            </div>
            
            <!-- Produk 17 -->
            <div class="product-card grid-item" id="product17">
                <a href="https://www.example.com/product17">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <span class="shopee-star-icon">Star</span>
                            Powerbank Fast Charging
                        </div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.9</div>
                            <div class="product-sold">Terjual 352</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 349.000</div>
                            <div class="product-price-original">Rp 399.000</div>
                            <div class="product-discount">-13%</div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Produk 18 -->
            <div class="product-card grid-item" id="product18">
                <a href="https://www.example.com/product18">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            Sneakers Running Sport
                        </div>
                        <div class="termurah-label">TERMURAH</div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.6</div>
                            <div class="product-sold">Terjual 189</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Reguler</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 229.000</div>
                            <div class="product-price-original">Rp 299.000</div>
                            <div class="product-discount">-24%</div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Produk 19 -->
            <div class="product-card grid-item" id="product19">
                <a href="https://www.example.com/product19">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <span class="shopee-star-icon">Star</span>
                            Tas Cantik Merona
                        </div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.9</div>
                            <div class="product-sold">Terjual 37</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Instan</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 15.999.000</div>
                            <div class="product-price-original">Rp 18.999.000</div>
                            <div class="product-discount">-16%</div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Produk 20 -->
            <div class="product-card grid-item" id="product20">
                <a href="https://www.example.com/product20">
                    <div class="product-img-container">
                        <img src="https://cdn.scalev.id/Image/2ebcb98892ab4a5ea33a4cb49f42c73c.webp" alt="Contoh Produk" class="product-img" loading="lazy" fetchpriority="low" width="300" height="300">
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <span class="shopee-star-icon">Star+</span>
                            Smart TV 4K 55 inch
                        </div>
                        <div class="termurah-label">TERMURAH</div>
                        <div class="product-rating-sold">
                            <div class="product-rating"><i class="fa fa-star"></i>4.8</div>
                            <div class="product-sold">Terjual 56</div>
                            <div class="cod-icon"><i class="fa fa-money-bill"></i>COD</div>
                        </div>
                        <div class="product-shipping"><i class="fa fa-truck"></i>Pengiriman Reguler</div>
                        <div class="product-price-container">
                            <div class="product-price">Rp 4.299.000</div>
                            <div class="product-price-original">Rp 5.299.000</div>
                            <div class="product-discount">-19%</div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <script>
	
	
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('categoryContainer');
            const expandedCategories = document.getElementById('expandedCategories');
            const desktopGrid = document.getElementById('desktopGrid');
            const extraCategories = document.getElementById('extraCategories');
            let isExpanded = false;
            let isDesktopExpanded = false;
            
            // All categories data with updated links
            const allCategories = [
                { 
                    name: 'Elektronik', 
                    icon: 'device-mobile', 
                    color: '#1BA0E2', 
                    link: 'https://facebook.com',
                    subkategori: [
                        { title: 'Konsol Game', icon: 'gamepad' },
                        { title: 'Aksesoris Konsol', icon: 'controller' },
                        { title: 'Alat Casting', icon: 'cast' },
                        { title: 'Foot Bath & Spa', icon: 'droplet' },
                        { title: 'Mesin Jahit & Aksesoris', icon: 'scissors' },
                        { title: 'Setrika & Mesin Uap', icon: 'iron' },
                        { title: 'Purifier & Humidifier', icon: 'wind' },
                        { title: 'Penyedot Debu', icon: 'vacuum' },
                        { title: 'Telepon', icon: 'phone' },
                        { title: 'Mesin Cuci & Pengering', icon: 'washer' },
                        { title: 'Water Heater', icon: 'thermometer' },
                        { title: 'Pendingin Ruangan', icon: 'snowflake' }
                    ]
                },
                { 
                    name: 'Makanan', 
                    icon: 'utensils', 
                    color: '#F25C05', 
                    link: 'https://instagram.com',
                    subkategori: [
                        { title: 'Makanan Ringan', icon: 'cookie' },
                        { title: 'Minuman', icon: 'coffee' },
                        { title: 'Kue & Biskuit', icon: 'cake' },
                        { title: 'Bahan Masakan', icon: 'package' }
                    ]
                },
                { 
                    name: 'Belanja', 
                    icon: 'shopping-cart', 
                    color: '#E74C3C', 
                    link: 'https://twitter.com',
                    subkategori: [
                        { title: 'Belanja Bulanan', icon: 'shopping-bag' },
                        { title: 'Kebutuhan Harian', icon: 'shopping-cart' },
                        { title: 'Promo Spesial', icon: 'tag' }
                    ]
                },
                { 
                    name: 'Game', 
                    icon: 'gamepad', 
                    color: '#3498DB', 
                    link: 'https://linkedin.com',
                    subkategori: [
                        { title: 'Game Mobile', icon: 'smartphone' },
                        { title: 'Game PC', icon: 'monitor' },
                        { title: 'Aksesori Gaming', icon: 'headphones' }
                    ]
                },
                { 
                    name: 'Lokal', 
                    icon: 'map-pin', 
                    color: '#9B59B6', 
                    link: 'https://youtube.com',
                    subkategori: [
                        { title: 'Produk UMKM', icon: 'home' },
                        { title: 'Kerajinan Lokal', icon: 'tool' },
                        { title: 'Makanan Khas', icon: 'utensils' }
                    ]
                },
                { 
                    name: 'Fashion', 
                    icon: 'shirt', 
                    color: '#F1C40F', 
                    link: 'https://tiktok.com',
                    subkategori: [
                        { title: 'Pakaian Pria', icon: 'user' },
                        { title: 'Pakaian Wanita', icon: 'user-female' },
                        { title: 'Sepatu', icon: 'boot' },
                        { title: 'Tas', icon: 'briefcase' }
                    ]
                },
                { 
                    name: 'Kecantikan', 
                    icon: 'list', 
                    color: '#E84393', 
                    link: 'https://pinterest.com',
                    subkategori: [
                        { title: 'Skincare', icon: 'droplet' },
                        { title: 'Makeup', icon: 'smile' },
                        { title: 'Perawatan Rambut', icon: 'scissors' }
                    ]
                },
                { 
                    name: 'Otomotif', 
                    icon: 'car', 
                    color: '#2C3E50', 
                    link: 'https://whatsapp.com',
                    subkategori: [
                        { title: 'Aksesoris Mobil', icon: 'car' },
                        { title: 'Aksesoris Motor', icon: 'activity' },
                        { title: 'Perawatan Kendaraan', icon: 'tool' }
                    ]
                },
                { 
                    name: 'Komputer', 
                    icon: 'monitor', 
                    color: '#5D6D7E', 
                    link: 'https://telegram.org',
                    subkategori: [
                        { title: 'Laptop', icon: 'laptop' },
                        { title: 'PC Desktop', icon: 'monitor' },
                        { title: 'Aksesoris Komputer', icon: 'keyboard' }
                    ]
                },
                { 
                    name: 'Hobi', 
                    icon: 'camera', 
                    color: '#1ABC9C', 
                    link: 'https://reddit.com',
                    subkategori: [
                        { title: 'Fotografi', icon: 'camera' },
                        { title: 'Musik', icon: 'music' },
                        { title: 'Koleksi', icon: 'archive' }
                    ]
                },
                { 
                    name: 'Rumah', 
                    icon: 'home', 
                    color: '#8E44AD', 
                    link: 'https://shopee.co.id',
                    subkategori: [
                        { title: 'Furniture', icon: 'inbox' },
                        { title: 'Dekorasi', icon: 'image' },
                        { title: 'Perlengkapan Dapur', icon: 'coffee' }
                    ]
                },
                { 
                    name: 'Kesehatan', 
                    icon: 'activity', 
                    color: '#2ECC71', 
                    link: 'https://tokopedia.com',
                    subkategori: [
                        { title: 'Obat-obatan', icon: 'thermometer' },
                        { title: 'Suplemen', icon: 'package' },
                        { title: 'Alat Kesehatan', icon: 'activity' }
                    ]
                },
                { 
                    name: 'Olahraga', 
                    icon: 'activity', 
                    color: '#E67E22', 
                    link: 'https://bukalapak.com',
                    subkategori: [
                        { title: 'Pakaian Olahraga', icon: 'shirt' },
                        { title: 'Alat Olahraga', icon: 'dumbbell' },
                        { title: 'Sepatu Olahraga', icon: 'boot' }
                    ]
                },
                { 
                    name: 'Mainan', 
                    icon: 'gift', 
                    color: '#FF6B81', 
                    link: 'https://github.com',
                    subkategori: [
                        { title: 'Mainan Anak', icon: 'truck' },
                        { title: 'Mainan Edukatif', icon: 'book' },
                        { title: 'Action Figure', icon: 'star' }
                    ]
                },
                { 
                    name: 'Bayi', 
                    icon: 'smile', 
                    color: '#FDA7DF', 
                    link: 'https://stackoverflow.com',
                    subkategori: [
                        { title: 'Pakaian Bayi', icon: 'shirt' },
                        { title: 'Perlengkapan Bayi', icon: 'package' },
                        { title: 'Makanan Bayi', icon: 'coffee' }
                    ]
                },
                { 
                    name: 'Pendidikan', 
                    icon: 'book', 
                    color: '#4834DF', 
                    link: 'https://medium.com',
                    subkategori: [
                        { title: 'Buku', icon: 'book' },
                        { title: 'Alat Tulis', icon: 'edit' },
                        { title: 'Kursus Online', icon: 'monitor' }
                    ]
                }
            ];
             // Letakkan kode ini tepat di sini
    window.allCategories = allCategories;
	
	
	// Function untuk mendapatkan ikon SVG subkategori berdasarkan nama ikon
            function getSubCategoryIconSVG(iconName) {
                switch(iconName) {
                    case 'gamepad':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="6" width="20" height="12" rx="2"></rect><path d="M12 12h.01"></path><path d="M17 10V8"></path><path d="M19 12h.01"></path><path d="M17 14v2"></path><path d="M7 12h.01"></path><path d="M5 10v2"></path><path d="M5 14v-2"></path></svg>';
                        
                    case 'controller':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="6" y1="12" x2="10" y2="12"></line><line x1="8" y1="10" x2="8" y2="14"></line><circle cx="15" cy="13" r="1"></circle><circle cx="18" cy="11" r="1"></circle><rect x="2" y="6" width="20" height="12" rx="2"></rect></svg>';
                        
                    case 'cast':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6"></path><line x1="2" y1="20" x2="2.01" y2="20"></line></svg>';
                        
                    case 'droplet':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path></svg>';
                        
                    case 'scissors':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="6" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><line x1="20" y1="4" x2="8.12" y2="15.88"></line><line x1="14.47" y1="14.48" x2="20" y2="20"></line><line x1="8.12" y1="8.12" x2="12" y2="12"></line></svg>';
                        
                    case 'iron':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zM4 16v-6c0-1.1.9-2 2-2h12c1.1 0 2 .9 2 2v6c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2z"></path><path d="M15 8v3"></path><path d="M17 10h-4"></path></svg>';
                        
                    case 'wind':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2"></path></svg>';
                        
                    case 'vacuum':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 6h5a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1h-5"></path><circle cx="8" cy="10" r="6"></circle><path d="M12 10a2 2 0 0 0-2-2"></path><path d="M8 16v3"></path></svg>';
                        
                    case 'phone':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72c.127.96.362 1.903.7 2.81a2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45c.907.338 1.85.573 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>';
                        
                    case 'washer':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="4" y="2" width="16" height="20" rx="2"></rect><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle><line x1="12" y1="6" x2="12" y2="6.01"></line><line x1="16" y1="12" x2="16" y2="12.01"></line><line x1="8" y1="12" x2="8" y2="12.01"></line><line x1="12" y1="18" x2="12" y2="18.01"></line></svg>';
                        
                    case 'thermometer':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"></path></svg>';
                        
                    case 'snowflake':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="2" y1="12" x2="22" y2="12"></line><line x1="12" y1="2" x2="12" y2="22"></line><path d="M20 16l-4-4 4-4"></path><path d="M4 8l4 4-4 4"></path><path d="M16 4l-4 4-4-4"></path><path d="M8 20l4-4 4 4"></path></svg>';
                    
                    case 'cookie':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><circle cx="8" cy="8" r="1"></circle><circle cx="16" cy="8" r="1"></circle><circle cx="12" cy="12" r="1"></circle><circle cx="16" cy="16" r="1"></circle><circle cx="8" cy="16" r="1"></circle></svg>';
                    
                    case 'coffee':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8h1a4 4 0 0 1 0 8h-1"></path><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path><line x1="6" y1="1" x2="6" y2="4"></line><line x1="10" y1="1" x2="10" y2="4"></line><line x1="14" y1="1" x2="14" y2="4"></line></svg>';
                    
                    case 'cake':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v8"></path><path d="M4 16s.5-1 2-1 2.5 2 4 2 2.5-2 4-2 2.5 2 4 2 2-1 2-1"></path><path d="M2 21h20"></path><path d="M7 8V7c0-2 2-3 5-3s5 1 5 3v1"></path><path d="M7 11v-1"></path><path d="M17 11v-1"></path></svg>';
                    
                    case 'package':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="16.5" y1="9.4" x2="7.5" y2="4.21"></line><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>';
                    
                    case 'shopping-bag':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path></svg>';
                    
                    case 'tag':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path><line x1="7" y1="7" x2="7.01" y2="7"></line></svg>';
                    
                    case 'smartphone':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line></svg>';
                    
                    case 'headphones':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 18v-6a9 9 0 0 1 18 0v6"></path><path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"></path></svg>';
                    
                    case 'tool':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path></svg>';
                    
                    case 'user':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>';
                    
                    case 'user-female':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle><path d="M12 11v4"></path><path d="M9 15h6"></path></svg>';
                    
                    case 'boot':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 11V4h16v7"></path><path d="M4 11a4 4 0 0 0 4 4h12"></path><path d="M4 11V4h16v7"></path><path d="M8 15v3h12v-3"></path><path d="M12 15v3"></path></svg>';
                    
                    case 'briefcase':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path></svg>';
                    
                    case 'laptop':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16"></path></svg>';
                    
                    case 'keyboard':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="4" width="20" height="16" rx="2" ry="2"></rect><path d="M6 8h.01"></path><path d="M10 8h.01"></path><path d="M14 8h.01"></path><path d="M18 8h.01"></path><path d="M8 12h.01"></path><path d="M12 12h.01"></path><path d="M16 12h.01"></path><path d="M7 16h10"></path></svg>';
                    
                    case 'music':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18V5l12-2v13"></path><circle cx="6" cy="18" r="3"></circle><circle cx="18" cy="16" r="3"></circle></svg>';
                    
                    case 'archive':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="21 8 21 21 3 21 3 8"></polyline><rect x="1" y="3" width="22" height="5"></rect><line x1="10" y1="12" x2="14" y2="12"></line></svg>';
                    
                    case 'inbox':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 12 16 12 14 15 10 15 8 12 2 12"></polyline><path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path></svg>';
                    
                    case 'image':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>';
                    
                    case 'dumbbell':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 5h12l3 5-3 5H6l-3-5 3-5Z"></path><path d="M7 15a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"></path><path d="M17 15a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"></path></svg>';
                    
                    case 'truck':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 3h15v13H1z"></path><path d="M16 8h4l3 3v5h-7V8z"></path><circle cx="5.5" cy="18.5" r="2.5"></circle><circle cx="18.5" cy="18.5" r="2.5"></circle></svg>';
                    
                    case 'star':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>';
                    
                    case 'edit':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>';
                        
                    default:
                        // Jika ikon tidak ditemukan, gunakan ikon dari kategori utama
                        return getIconSVG('device-mobile');
                }
            }
			
			// Fungsi untuk membuka tampilan subkategori
function openSubcategoryView(category, resetOrder = true) {
    const subcategoryView = document.getElementById('subcategoryView');
    const categorySidebar = document.getElementById('categorySidebar');
    const subcategoryContent = document.getElementById('subcategoryContent');
    
    // Hapus pengambilan referensi ke categories-section
    
    // Ubah ikon back button
    document.querySelector('.back-button').innerHTML = '<i class="fa fa-arrow-left"></i>';
    
    // Hapus pengaturan maxWidth berdasarkan categories-section
    // subcategoryView menggunakan maxWidth dari CSS-nya sendiri
    
    // Tampilkan tampilan subkategori
    subcategoryView.style.display = 'flex';
            
            
    // Tampilkan tampilan subkategori
    subcategoryView.style.display = 'flex';
    // Hanya atur ulang sidebar jika parameter resetOrder true
    if (resetOrder) {
        // Buat array kategori baru dengan kategori yang dipilih di atas
        let sortedCategories = [...allCategories];
        
        // Hapus kategori yang dipilih dari array
        sortedCategories = sortedCategories.filter(cat => cat.name !== category.name);
        
        // Tambahkan kategori yang dipilih di awal array
        sortedCategories.unshift(category);
        
        // Reset sidebar dan isi dengan kategori yang telah diurutkan
        categorySidebar.innerHTML = '';
        // Di dalam fungsi openSubcategoryView(), pada bagian yang membuat sidebar items:
// Di dalam fungsi openSubcategoryView(), pada bagian yang membuat sidebar items:
sortedCategories.forEach(cat => {
    const sidebarItem = document.createElement('div');
    sidebarItem.className = "sidebar-item" + (cat.name === category.name ? " active" : "");
    sidebarItem.innerHTML = `
        <div class="sidebar-icon" style="color: ${cat.name === category.name ? '#FF5722' : '#888'}">${getIconSVG(cat.icon)}</div>
        <div class="sidebar-text">${cat.name}</div>
    `;
    
    // Tambahkan event listener untuk beralih kategori
    sidebarItem.addEventListener('click', function() {
        // Hapus kelas active dari semua item
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.classList.remove('active');
            // Reset warna semua ikon ke abu-abu
            item.querySelector('.sidebar-icon').style.color = '#888';
        });
        
        // Tambahkan kelas active ke item yang diklik
        sidebarItem.classList.add('active');
        // Set warna ikon ke oranye
        sidebarItem.querySelector('.sidebar-icon').style.color = '#FF5722';
        
        // Update konten subkategori tanpa mengubah urutan sidebar
        updateSubcategoryContent(cat);
    });
    
    categorySidebar.appendChild(sidebarItem);
});
    } else {
        // Hanya update status active pada sidebar yang sudah ada
        document.querySelectorAll('.sidebar-item').forEach(item => {
            const itemName = item.querySelector('.sidebar-text').textContent;
            if (itemName === category.name) {
                item.classList.add('active');
                item.querySelector('.sidebar-icon').style.color = category.color;
            } else {
                item.classList.remove('active');
                item.querySelector('.sidebar-icon').style.color = '';
            }
        });
    }
    
    // Isi konten subkategori dengan kategori yang dipilih
    updateSubcategoryContent(category);
    
    // Tambahkan event listener pada tombol kembali jika belum ada
    const backButton = document.getElementById('backButton');
    if (backButton) {
        backButton.removeEventListener('click', closeSubcategoryView);
        backButton.addEventListener('click', closeSubcategoryView);
    }
    
    // Disable scroll pada body
    document.body.style.overflow = 'hidden';
}


            // Fungsi untuk memperbarui konten subkategori
            function updateSubcategoryContent(category) {
                const subcategoryContent = document.getElementById('subcategoryContent');
                
                // Banner kategori
let contentHTML = `
    <div class="subcategory-banner">
        <img src="https://via.placeholder.com/800x200" alt="${category.name} Banner">
    </div>
`;
                
                // Grid subkategori
                contentHTML += '<div class="subcategory-grid">';
                
                // Isi dengan subkategori dari kategori yang dipilih
                if (category.subkategori && category.subkategori.length > 0) {
        category.subkategori.forEach(subcat => {
            contentHTML += `
    <div class="subcategory-item">
        <div class="subcategory-icon" style="color: #FF5722">
            ${subcat.icon ? getSubCategoryIconSVG(subcat.icon) : getSubCategoryIconSVG('default')}
        </div>
        <div class="subcategory-title">${subcat.title}</div>
    </div>
`;
                    });
                } else {
                    // Jika tidak ada subkategori, tampilkan pesan
                    contentHTML += `
                        <div style="grid-column: 1 / -1; text-align: center; padding: 20px;">
                            Belum ada subkategori untuk kategori ini.
                        </div>
                    `;
                }
                
                contentHTML += '</div>';
                subcategoryContent.innerHTML = contentHTML;
                
                // Tambahkan event listener untuk item subkategori
                document.querySelectorAll('.subcategory-item').forEach(item => {
                    item.addEventListener('click', function() {
                        // Di sini Anda bisa menambahkan kode untuk navigasi ke halaman produk subkategori
                        alert('Navigasi ke subkategori: ' + item.querySelector('.subcategory-title').textContent);
                    });
                });
            }
			
			// Fungsi untuk menutup tampilan subkategori
            function closeSubcategoryView() {
                const subcategoryView = document.getElementById('subcategoryView');
                subcategoryView.style.display = 'none';
                
                // Enable kembali scroll pada body
                document.body.style.overflow = '';
            }
			
			// Fungsi untuk membuka tampilan subkategori dari index (untuk diakses dari onclick inline)
window.openSubcategoryFromIndex = function(index, resetOrder = false) {
    // Pastikan allCategories telah didefinisikan saat fungsi dipanggil
    if (window.allCategories && window.allCategories[index]) {
        const category = window.allCategories[index];
        openSubcategoryView(category, resetOrder);
    } else {
        console.error("Category not found at index:", index);
    }
};
	
            // Function to create icon SVG
            function getIconSVG(iconName) {
                switch(iconName) {
                    case 'device-mobile':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><path d="M8 6h.01M16 6h.01M12 6h.01M8 10h.01M16 10h.01M12 10h.01M8 14h.01M16 14h.01M12 14h.01M8 18h.01M16 18h.01M12 18h.01"></path></svg>';
                    case 'utensils':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path><path d="M7 2v20"></path><path d="M21 15V2"></path><path d="M18 15V2"></path><path d="M15 15V2"></path><path d="M21 15a3 3 0 1 1-6 0"></path></svg>';
                    case 'shopping-cart':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>';
                    case 'gamepad':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="6" width="20" height="12" rx="2"></rect><path d="M12 12h.01"></path><path d="M17 10V8"></path><path d="M19 12h.01"></path><path d="M17 14v2"></path><path d="M7 12h.01"></path><path d="M5 10v2"></path><path d="M5 14v-2"></path></svg>';
                    case 'map-pin':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>';
                    case 'shirt':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z"></path></svg>';
                    case 'list':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M7 21a4 4 0 0 1-4-4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v12a4 4 0 0 1-4 4H7z"></path><line x1="12" y1="7" x2="20" y2="7"></line><line x1="12" y1="11" x2="20" y2="11"></line><line x1="12" y1="15" x2="20" y2="15"></line><line x1="8" y1="7" x2="8" y2="7.01"></line><line x1="8" y1="11" x2="8" y2="11.01"></line><line x1="8" y1="15" x2="8" y2="15.01"></line></svg>';
                    case 'car':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="1" y="6" width="22" height="12" rx="6"></rect><circle cx="7" cy="12" r="3"></circle><circle cx="17" cy="12" r="3"></circle></svg>';
                    case 'monitor':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect><line x1="8" y1="21" x2="16" y2="21"></line><line x1="12" y1="17" x2="12" y2="21"></line></svg>';
                    case 'camera':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path><circle cx="12" cy="13" r="3"></circle></svg>';
                    case 'home':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>';
                    case 'activity':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline></svg>';
                    case 'gift':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 12v10H4V12"></path><path d="M2 7h20v5H2z"></path><path d="M12 22V7"></path><path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"></path><path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"></path></svg>';
                    case 'smile':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M8 14s1.5 2 4 2 4-2 4-2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line></svg>';
                    case 'book':
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path></svg>';
                    default:
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>';
                }
            }

            
            // Function untuk icon "Lihat Semua"
            function getSeeAllIconSVG() {
                return `
                <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 40 40">
                    <rect x="2" y="2" width="36" height="36" rx="10" fill="#FFF5F0" stroke="#FFDFD1" stroke-width="2"/>
                    <rect x="6" y="6" width="12" height="12" rx="3" fill="#FF5722"/>
                    <rect x="22" y="6" width="12" height="12" rx="3" fill="#FF5722"/>
                    <rect x="6" y="22" width="12" height="12" rx="3" fill="#FF5722"/>
                    <rect x="22" y="22" width="12" height="12" rx="3" fill="#FF5722"/>
                </svg>`;
            }
            
            // Function untuk icon "Tutup Semua"
            function getCloseAllIconSVG() {
                return `
                <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 40 40">
                    <rect x="2" y="2" width="36" height="36" rx="10" fill="#F5F5F5" stroke="#E0E0E0" stroke-width="2"/>
                    <path d="M13 13L27 27M13 27L27 13" stroke="#555" stroke-width="3" stroke-linecap="round"/>
                </svg>`;
            }
            
            // Fungsi untuk icon arrow SVG
            function getArrowRightSVG() {
                return `
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M5 12h14"></path>
                    <path d="M12 5l7 7-7 7"></path>
                </svg>`;
            }
            
            // Fungsi untuk membuat tombol tutup
            function createCloseButton() {
                // Buat container untuk tombol tutup
                const closeButtonContainer = document.createElement('div');
                closeButtonContainer.className = 'close-button-container';
                closeButtonContainer.style.display = 'none'; // Sembunyikan dulu
                
                // Buat tombol tutup
                const closeButton = document.createElement('button');
                closeButton.className = 'close-button';
                closeButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                `;
                
                // Tambahkan event listener untuk menutup kategori
                closeButton.addEventListener('click', function() {
                    toggleExpandedCategories();
                });
                
                // Tambahkan tombol ke container
                closeButtonContainer.appendChild(closeButton);
                
                // Tambahkan container ke DOM
                const expandedCategoriesEl = document.getElementById('expandedCategories');
                expandedCategoriesEl.parentNode.insertBefore(closeButtonContainer, expandedCategoriesEl.nextSibling);
                
                return closeButtonContainer;
            }
            
            // Modifikasi pada HTML untuk wrapper
            function setupCategoryContainer() {
                // Ambil container
                const container = document.getElementById('categoryContainer');
                
                // Buat wrapper baru
                const wrapper = document.createElement('div');
                wrapper.className = 'categories-container-wrapper';
                
                // Pindahkan container ke dalam wrapper
                container.parentNode.insertBefore(wrapper, container);
                wrapper.appendChild(container);
                
                // Buat floating button di dalam wrapper
                if (window.innerWidth < 768) {
                    createFloatingArrow(wrapper);
                }
            }
            
            // Fungsi untuk membuat floating arrow dengan timer
            function createFloatingArrow(wrapper) {
                const floatingArrow = document.createElement('div');
                floatingArrow.className = 'floating-arrow hidden'; // Mulai dengan status hidden
                floatingArrow.innerHTML = `
                    <span class="floating-arrow-text">View All</span>
                    <div class="floating-arrow-icon">${getArrowRightSVG()}</div>
                `;
                
                // Tambahkan event listener untuk klik
                floatingArrow.addEventListener('click', toggleExpandedCategories);
                
                // Tambahkan ke wrapper
                wrapper.appendChild(floatingArrow);
                
                // Tambahkan event listener untuk scroll horizontal pada container
                const categoriesContainer = document.getElementById('categoryContainer');
                let scrollTimeout;
                let isScrolling = false;
                let hideTimeout; // Timeout untuk menyembunyikan tombol setelah 5 detik
                let cycleInterval; // Interval untuk siklus tampil-hilang
                let inactivityTimer; // Timer untuk menampilkan kembali setelah 5 menit tidak aktif
                
                // Fungsi untuk memeriksa apakah scroll sudah di ujung kanan
                function isScrollAtEnd() {
                    // Hitung dengan toleransi 5px untuk mengatasi perbedaan perhitungan di beberapa browser
                    return categoriesContainer.scrollLeft + categoriesContainer.clientWidth >= categoriesContainer.scrollWidth - 5;
                }
                
                // Fungsi untuk menampilkan tombol floating dengan transisi smooth
                function showFloatingButton() {
                    // Cek apakah container sudah di-scroll ke ujung
                    const atEnd = isScrollAtEnd();
                    
                    // Jika belum di ujung dan tidak dalam keadaan expanded
                    if (!atEnd && !isExpanded) {
                        // Pastikan transisi dimulai dari hidden state
                        setTimeout(() => {
                            floatingArrow.classList.remove('hidden');
                        }, 50);
                        
                        // Clear timeout yang ada jika ada
                        if (hideTimeout) clearTimeout(hideTimeout);
                        
                        // Set timeout baru untuk menyembunyikan tombol setelah 5 detik
                        hideTimeout = setTimeout(() => {
                            floatingArrow.classList.add('hidden');
                        }, 5000); // Tampilkan selama 5 detik
                    } else {
                        // Jika di ujung, pastikan tombol tersembunyi
                        floatingArrow.classList.add('hidden');
                    }
                }
                
                // Fungsi untuk menghilangkan elemen dengan transisi smooth
                function fadeOutContainer() {
                    // Mulai transisi opacity
                    categoriesContainer.style.transition = 'opacity 0.8s ease';
                    categoriesContainer.style.opacity = '0.7';
                    
                    // Sembunyikan floating button dengan smooth transition
                    floatingArrow.classList.add('hidden');
                }
                
                // Fungsi untuk menampilkan kembali elemen dengan transisi smooth
                function fadeInContainer() {
                    // Mulai transisi opacity kembali
                    categoriesContainer.style.transition = 'opacity 0.8s ease';
                    categoriesContainer.style.opacity = '1';
                }
                
                // Event listener untuk scroll
                categoriesContainer.addEventListener('scroll', function() {
                    // Tandai bahwa sedang scrolling
                    isScrolling = true;
                    
                    // Clear timeout sebelumnya
                    clearTimeout(scrollTimeout);
                    
                    // Menghilangkan elemen saat digeser dengan transisi smooth
                    fadeOutContainer();
                    
                    // Clear inactivity timer yang mungkin sedang berjalan
                    if (inactivityTimer) clearTimeout(inactivityTimer);
                    
                    // Set timeout untuk menandai bahwa scrolling telah berhenti
                    scrollTimeout = setTimeout(function() {
                        isScrolling = false;
                        
                        // Kembalikan opasitas container saat berhenti scroll dengan transisi smooth
                        fadeInContainer();
                        
                        // Set inactivity timer untuk menampilkan kembali setelah 5 menit tidak aktif
                        inactivityTimer = setTimeout(function() {
                            // Perbarui status tombol floating jika tidak di ujung dan tidak expanded
                            if (!isScrollAtEnd() && !isExpanded) {
                                showFloatingButton();
                                startFloatingButtonCycle();
                            }
                        }, 300000); // 5 menit = 300000 ms
                    }, 500); // Waktu lebih lama untuk deteksi berhenti scroll agar lebih smooth
                });
                
                // Jalankan siklus tampil-hilang
                function startFloatingButtonCycle() {
                    // Hentikan interval yang mungkin sedang berjalan
                    if (cycleInterval) clearInterval(cycleInterval);
                    
                    // Mulai interval baru
                    cycleInterval = setInterval(() => {
                        // Cek dulu apakah sudah di ujung kanan
                        if (!isScrollAtEnd() && !isScrolling && !isExpanded) {
                            // Tampilkan tombol hanya jika tidak di ujung kanan dan tidak sedang scroll
                            showFloatingButton();
                        }
                    }, 10000); // Siklus setiap 10 detik (5 detik tampil + 5 detik hilang)
                }
                
                // Mulai siklus tombol floating setelah 5 detik halaman dimuat
                setTimeout(() => {
                    // Cek dulu apakah sudah di ujung kanan
                    if (!isScrollAtEnd()) {
                        // Tampilkan tombol pertama kali hanya jika tidak di ujung kanan
                        showFloatingButton();
                    }
                    
                    // Mulai siklus
                    startFloatingButtonCycle();
                }, 5000);
                
                // Tambahkan event listener untuk expanded state
                document.addEventListener('expandedStateChanged', function(e) {
                    if (e.detail.expanded) {
                        // Jika expanded, sembunyikan tombol
                        floatingArrow.classList.add('hidden');
                        
                        // Clear semua timer
                        if (hideTimeout) clearTimeout(hideTimeout);
                        if (cycleInterval) clearInterval(cycleInterval);
                        if (inactivityTimer) clearTimeout(inactivityTimer);
                    } else {
                        // Jika kembali ke tampilan normal, mulai siklus lagi
                        setTimeout(() => {
                            // Cek dulu apakah sudah di ujung kanan
                            if (!isScrollAtEnd()) {
                                showFloatingButton();
                            }
                            startFloatingButtonCycle();
                        }, 300);
                    }
                });
                
                // Hentikan siklus saat halaman tidak terlihat
                document.addEventListener('visibilitychange', function() {
                    if (document.hidden) {
                        // Clear semua timer saat halaman tidak terlihat
                        if (hideTimeout) clearTimeout(hideTimeout);
                        if (cycleInterval) clearInterval(cycleInterval);
                        if (inactivityTimer) clearTimeout(inactivityTimer);
                    } else {
                        // Mulai ulang siklus saat halaman terlihat kembali
                        if (!isScrollAtEnd() && !isScrolling && !isExpanded) {
                            showFloatingButton();
                        }
                        startFloatingButtonCycle();
                        
                        // Set inactivity timer baru
                        if (inactivityTimer) clearTimeout(inactivityTimer);
                        inactivityTimer = setTimeout(function() {
                            if (!isScrollAtEnd() && !isExpanded) {
                                showFloatingButton();
                                startFloatingButtonCycle();
                            }
                        }, 300000); // 5 menit
                    }
                });
                
                return floatingArrow;
            }
            
            // MOBILE VIEW SETUP
            // Populate initial categories (first 9 categories only) for mobile view
            const visibleCategories = allCategories.slice(0, 9);
            visibleCategories.forEach(category => {
                // Buat div container untuk kategori
                const categoryItem = document.createElement('div');
                categoryItem.className = "category-item";
                categoryItem.innerHTML = `
                    <div class="category-icon" style="color: ${category.color};">
                        ${getIconSVG(category.icon)}
                        <div class="category-name">${category.name}</div>
                    </div>
                `;
                
                // Tambahkan event click untuk navigasi
categoryItem.addEventListener('click', function() {
    // Tampilkan tampilan subkategori dan inisialisasi dengan kategori yang dipilih
    openSubcategoryView(category, true);
});
                
                container.appendChild(categoryItem);
            });
            
            // Add "Lihat Semua" card as the last item in the row for mobile view
            const seeAllItem = document.createElement('div');
            seeAllItem.className = "see-all-item";
            seeAllItem.innerHTML = `
                <div class="category-icon">
                    ${getSeeAllIconSVG()}
                    <div class="category-name">Lihat Semua</div>
                </div>
            `;
            
            // Tambahkan event click untuk "Lihat Semua"
seeAllItem.addEventListener('click', function() {
    // Jika di mobile, toggle expanded categories
    if (window.innerWidth < 768) {
        toggleExpandedCategories();
    } else {
        // Buka tampilan subkategori dengan kategori pertama
        openSubcategoryView(allCategories[0]);
    }
});
            container.appendChild(seeAllItem);
            
            // Create mobile expanded view
let expandedHTML = '<div class="expanded-grid">';
allCategories.forEach((category, i) => {
    expandedHTML += `
    <div class="expanded-item" onclick="openSubcategoryFromIndex(${i}, true)">
        <div class="expanded-icon" style="color: ${category.color}">
            ${getIconSVG(category.icon)}
        </div>
        <div class="expanded-name">${category.name}</div>
    </div>
`;
});
expandedHTML += '</div>';
expandedCategories.innerHTML = expandedHTML;
            
            // DESKTOP VIEW SETUP
            
           // Create desktop grid view for first 13 categories
const desktopVisibleCategories = allCategories.slice(0, 13);
desktopVisibleCategories.forEach(category => {
    const desktopItem = document.createElement('div');
    desktopItem.className = "expanded-item";
    desktopItem.innerHTML = `
        <div class="expanded-icon" style="color: ${category.color}">
            ${getIconSVG(category.icon)}
        </div>
        <div class="expanded-name">${category.name}</div>
    `;
    
    desktopItem.addEventListener('click', function() {
        if (window.innerWidth >= 768) {
            openSubcategoryView(category);
        } else {
            window.location.href = category.link;
        }
    });
    
    desktopGrid.appendChild(desktopItem);
});

// Add "View All" as the 14th item for desktop
const desktopViewAllItem = document.createElement('div');
desktopViewAllItem.className = "expanded-item see-all-item";
            desktopViewAllItem.id = "desktopViewAllItem";
            desktopViewAllItem.innerHTML = `
                <div class="expanded-icon">
                    ${getSeeAllIconSVG()}
                </div>
                <div class="expanded-name">View All</div>
            `;
            
            desktopViewAllItem.addEventListener('click', function() {
    if (window.innerWidth >= 768) {
        toggleDesktopExtraCategories();
    } else {
        // Buka tampilan subkategori dengan kategori pertama
        openSubcategoryView(allCategories[0]);
    }
});
            desktopGrid.appendChild(desktopViewAllItem);
            
            // Create extra categories view for desktop
const extraCategoriesList = allCategories.slice(13);
let extraHTML = '<div class="desktop-grid">';
extraCategoriesList.forEach((category, i) => {
    // Ubah kode ini untuk menggunakan index global dari allCategories
    const globalIndex = i + 13; // 13 kategori sudah ditampilkan sebelumnya
    extraHTML += `
        <div class="expanded-item" onclick="openSubcategoryFromIndex(${globalIndex}, true)">
            <div class="expanded-icon" style="color: ${category.color}">
                ${getIconSVG(category.icon)}
            </div>
            <div class="expanded-name">${category.name}</div>
        </div>
    `;
});
extraHTML += '</div>';
extraCategories.innerHTML = extraHTML;
            
            // Function to toggle expanded categories for mobile view
            function toggleExpandedCategories() {
                isExpanded = !isExpanded;
                
                // Ambil atau buat tombol tutup jika belum ada
                let closeButtonContainer = document.querySelector('.close-button-container');
                if (!closeButtonContainer) {
                    closeButtonContainer = createCloseButton();
                }
                
                if (isExpanded) {
                    // Hide the default categories
                    container.style.display = 'none';
                    
                    // Show expanded categories
                    expandedCategories.classList.add('active');
                    
                    // Get the height of the expanded content for animation
                    const expandedContent = expandedCategories.querySelector('.expanded-grid');
                    const expandedHeight = expandedContent.offsetHeight;
                    
                    // Set height for animation
                    expandedCategories.style.height = expandedHeight + 'px';
                    
                    // Tampilkan tombol tutup
                    closeButtonContainer.style.display = 'flex';
                    
                    // Dispatch event bahwa expanded state berubah
                    document.dispatchEvent(new CustomEvent('expandedStateChanged', {
                        detail: { expanded: true }
                    }));
                } else {
                    // Show the default categories
                    container.style.display = 'flex';
                    
                    // Collapse expanded categories
                    expandedCategories.style.height = '0';
                    
                    // Sembunyikan tombol tutup
                    closeButtonContainer.style.display = 'none';
                    
                    // Remove active class after animation completes
                    setTimeout(() => {
                        expandedCategories.classList.remove('active');
                    }, 300);
                    
                    // Dispatch event bahwa expanded state berubah
                    document.dispatchEvent(new CustomEvent('expandedStateChanged', {
                        detail: { expanded: false }
                    }));
                }
            }
            
            // Function to toggle extra categories for desktop view
            function toggleDesktopExtraCategories() {
                isDesktopExpanded = !isDesktopExpanded;
                const viewAllButton = document.getElementById('desktopViewAllItem');
                
                if (isDesktopExpanded) {
                    // Show extra categories
                    extraCategories.classList.add('active');
                    
                    // Set height for animation
                    const extraContent = extraCategories.querySelector('.desktop-grid');
                    const extraHeight = extraContent.offsetHeight;
                    extraCategories.style.height = extraHeight + 'px';
                    
                    // Change "View All" to "Close"
                    viewAllButton.className = "expanded-item close-all-item";
                    viewAllButton.innerHTML = `
                        <div class="expanded-icon animate-in">
                            ${getCloseAllIconSVG()}
                        </div>
                        <div class="expanded-name">Close</div>
                    `;
                } else {
                    // Hide extra categories
                    extraCategories.style.height = '0';
                    
                    // Change "Close" back to "View All"
                    viewAllButton.className = "expanded-item see-all-item";
                    viewAllButton.innerHTML = `
                        <div class="expanded-icon animate-in">
                            ${getSeeAllIconSVG()}
                        </div>
                        <div class="expanded-name">View All</div>
                    `;
                    
                    // Remove active class after animation completes
                    setTimeout(() => {
                        extraCategories.classList.remove('active');
                    }, 300);
                }
            }
            
            // Setup desktop/mobile view based on initial screen size
            function setupResponsiveView() {
                if (window.innerWidth >= 768) {
                    // Desktop view
                    desktopGrid.style.display = 'grid';
                    container.style.display = 'none';
                    expandedCategories.style.display = 'none';
                    
                    // Sembunyikan tombol tutup di desktop
                    const closeButtonContainer = document.querySelector('.close-button-container');
                    if (closeButtonContainer) {
                        closeButtonContainer.style.display = 'none';
                    }
                } else {
                    // Mobile view
                    desktopGrid.style.display = 'none';
                    extraCategories.style.display = 'none';
                    container.style.display = 'flex';
                    expandedCategories.style.height = '0';
                    
                    // Tampilkan tombol tutup hanya jika kategori diperluas
                    const closeButtonContainer = document.querySelector('.close-button-container');
                    if (closeButtonContainer) {
                        closeButtonContainer.style.display = isExpanded ? 'flex' : 'none';
                    }
                }
            }
            
            // Setup category container wrapper & floating button
            setupCategoryContainer();
            
            // Initialize view
            setupResponsiveView();
            
            // Update on window resize
            window.addEventListener('resize', setupResponsiveView);
        });
    </script>
    
    <!-- Script section - Optimized for performance -->
    <script>
    (function() {
        // Performance monitoring function
        function logPerformance(label, startTime) {
            const duration = performance.now() - startTime;
            console.log(`${label}: ${duration.toFixed(2)}ms`);
        }
        
        // Load scripts asynchronously with performance tracking
        function loadScript(src, callback) {
            const startTime = performance.now();
            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            
            script.onload = function() {
                logPerformance(`Script loaded: ${src}`, startTime);
                if (callback) callback();
            };
            
            script.onerror = function() {
                console.error(`Failed to load script: ${src}`);
            };
            
            document.body.appendChild(script);
        }
        
        // Preload critical images for better performance
        function preloadCriticalImages() {
            const criticalImages = [
                'https://cdn.scalev.id/Image/shdBj8gwgQ9tppXObDzEkxvHOA4Hgv7op51P9qiiaho/1741573273658-Merah_Kuning_Putih_Sede_9J5Kipp.webp'
            ];
            
            criticalImages.forEach(src => {
                const img = new Image();
                img.src = src;
            });
        }
        
        // Throttle function to limit function calls
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
        
        // Main initialization function
        function initSite() {
            let msnry, grid;
            const startTime = performance.now();
            
            // RequestIdleCallback polyfill
            window.requestIdleCallback = window.requestIdleCallback || function(cb) {
                return setTimeout(function() {
                    const start = Date.now();
                    cb({
                        didTimeout: false,
                        timeRemaining: function() {
                            return Math.max(0, 50 - (Date.now() - start));
                        }
                    });
                }, 1);
            };
            
            // Initialize Masonry in an idle callback for better performance
            window.requestIdleCallback(function() {
                grid = document.querySelector('.masonry-grid');
                
                // Initialize masonry with optimized options
                msnry = new Masonry(grid, {
                    itemSelector: '.grid-item',
                    columnWidth: '.grid-sizer',
                    percentPosition: true,
                    horizontalOrder: true,
                    gutter: 0,
                    initLayout: false, // Manual control for layout
                    transitionDuration: 0, // Initial transitions off for better performance
                    stagger: 30,
                    resize: false // Handle resize manually
                });
                
                // Efficient image loading with imagesLoaded
                imagesLoaded(grid).on('progress', function(instance, image) {
                    // Throttle layout updates to reduce layout thrashing
                    if (!msnry.layoutThrottleTimeout) {
                        msnry.layoutThrottleTimeout = setTimeout(function() {
                            msnry.layout();
                            delete msnry.layoutThrottleTimeout;
                        }, 100);
                    }
                }).on('always', function() {
                    // Final layout after all images are loaded
                    msnry.layout();
                    
                    // Restore transition for subsequent updates
                    setTimeout(function() {
                        msnry.options.transitionDuration = '0.4s';
                    }, 500);
                    
                    logPerformance('Masonry fully initialized', startTime);
                });
                
                // Handle window resize with throttling for better performance
                const throttledResize = throttle(function() {
                    msnry.layout();
                }, 150);
                
                window.addEventListener('resize', throttledResize);
                
                // Initialize components
                handleLazyLoadImproved();
                handleVideoClick();
                setupImageSled();
                initializeFlashSaleTimers();
            }, { timeout: 500 });
            
            // Optimized lazy loading with batching and priority
            function handleLazyLoadImproved() {
                const productCards = document.querySelectorAll('.product-card');
                const initialLoadCount = 8;
                const initialVisibilityStart = performance.now();
                
                // Function to show initial products with better performance
                const initialVisibility = () => {
                    const initialBatch = Array.from(productCards).slice(0, initialLoadCount);
                    let processed = 0;
                    
                    // Process initial batch in chunks for better performance
                    const processChunk = (startIndex) => {
                        const endIndex = Math.min(startIndex + 3, initialBatch.length);
                        
                        for (let i = startIndex; i < endIndex; i++) {
                            initialBatch[i].classList.add('visible');
                            processed++;
                        }
                        
                        if (processed >= initialBatch.length) {
                            // All initial products visible, update layout
                            msnry.layout();
                            logPerformance('Initial products loaded', initialVisibilityStart);
                            // Setup observer for remaining products
                            setupLazyLoadObserver();
                        } else {
                            // Process next chunk in next frame
                            requestAnimationFrame(() => {
                                processChunk(endIndex);
                            });
                        }
                    };
                    
                    // Start processing chunks
                    requestAnimationFrame(() => {
                        processChunk(0);
                    });
                };
                
                // Setup observer for remaining products
                const setupLazyLoadObserver = () => {
                    const observerStart = performance.now();
                    
                    // Use IntersectionObserver to lazy load products
                    const observer = new IntersectionObserver((entries, observer) => {
                        // Batch multiple entries for better performance
                        let visibleEntries = entries.filter(entry => entry.isIntersecting);
                        
                        if (visibleEntries.length > 0) {
                            requestAnimationFrame(() => {
                                visibleEntries.forEach(entry => {
                                    entry.target.classList.add('visible');
                                    observer.unobserve(entry.target);
                                });
                                
                                // Throttle layout updates for better performance
                                if (msnry.layoutThrottleTimeout) {
                                    clearTimeout(msnry.layoutThrottleTimeout);
                                }
                                msnry.layoutThrottleTimeout = setTimeout(() => {
                                    msnry.layout();
                                }, 100);
                            });
                        }
                    }, {
                        rootMargin: '300px 0px', // Load earlier for smoother experience
                        threshold: 0.1
                    });
                    
                    // Start observing remaining products
                    Array.from(productCards).slice(initialLoadCount).forEach(card => {
                        observer.observe(card);
                    });
                    
                    logPerformance('Lazy load observer setup', observerStart);
                };
                
                // Start with initial visibility
                initialVisibility();
            }
            
            // Video click handler with optimization
            function handleVideoClick() {
                const videoContainers = document.querySelectorAll('.video-container');
                
                // Use event delegation for better performance
                document.addEventListener('click', function(e) {
                    const container = e.target.closest('.video-container');
                    if (!container) return;
                    
                    e.preventDefault();
                    
                    const thumbnail = container.querySelector('.video-thumbnail');
                    const playIcon = container.querySelector('.play-icon');
                    const placeholder = container.querySelector('.youtube-embed-placeholder');
                    
                    if (placeholder && !container.querySelector('.youtube-embed')) {
                        // Create iframe only when needed
                        const iframe = document.createElement('iframe');
                        iframe.className = 'youtube-embed';
                        iframe.src = placeholder.getAttribute('data-embed-src') + '&autoplay=1';
                        iframe.title = 'YouTube Short';
                        iframe.loading = 'lazy';
                        iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share';
                        iframe.allowFullscreen = true;
                        
                        // Add iframe to container
                        container.appendChild(iframe);
                        
                        // Show iframe with slight delay for better performance
                        requestAnimationFrame(() => {
                            iframe.classList.add('active');
                            
                            // Hide thumbnail and play icon
                            if (thumbnail) thumbnail.style.display = 'none';
                            if (playIcon) playIcon.style.display = 'none';
                            
                            // Update layout
                            if (msnry) msnry.layout();
                        });
                    }
                    
                    // Prevent event bubbling
                    e.stopPropagation();
                });
            }
            
            // Image slider optimization
            function setupImageSled() {
                const sledContainers = document.querySelectorAll('.image-sled-container');
                
                sledContainers.forEach(container => {
                    const sled = container.querySelector('.image-sled');
                    if (!sled) return;
                    
                    const dots = container.querySelectorAll('.image-sled-dot');
                    const items = sled.querySelectorAll('.image-sled-item');
                    let currentIndex = 0;
                    const totalSlides = dots.length;
                    let slideInterval;
                    let startX, moveX;
                    
                    // Calculate slide width with better performance
                    const slideWidth = 100 / (totalSlides + 1);
                    
                    // Setup slides with proper width
                    sled.style.width = ((totalSlides + 1) * 100) + '%';
                    sled.style.transition = 'transform 0.5s ease';
                    
                    // Use DocumentFragment for better performance
                    const fragment = document.createDocumentFragment();
                    const firstSlide = items[0].cloneNode(true);
                    fragment.appendChild(firstSlide);
                    sled.appendChild(fragment);
                    
                    // Set width for all slides efficiently
                    const allSlides = sled.querySelectorAll('.image-sled-item');
                    allSlides.forEach(slide => {
                        slide.style.width = slideWidth + '%';
                    });
                    
                    // Go to slide with requestAnimationFrame for smooth performance
                    function goToSlide(index) {
                        requestAnimationFrame(() => {
                            // Update dots
                            dots.forEach(d => d.classList.remove('active'));
                            dots[index].classList.add('active');
                            
                            // Update current index & slide position
                            currentIndex = index;
                            sled.style.transition = 'transform 0.5s ease';
                            sled.style.transform = `translateX(-${currentIndex * slideWidth}%)`;
                        });
                    }
                    
                    // Slide handler with infinite loop and better performance
                    function nextSlide() {
                        requestAnimationFrame(() => {
                            if (currentIndex >= totalSlides) {
                                // Reset to first slide (no animation)
                                sled.style.transition = 'none';
                                sled.style.transform = 'translateX(0)';
                                currentIndex = 0;
                                
                                // Force reflow
                                sled.offsetHeight;
                                
                                // Animate to next slide
                                requestAnimationFrame(() => {
                                    sled.style.transition = 'transform 0.5s ease';
                                    currentIndex = 1;
                                    sled.style.transform = `translateX(-${slideWidth}%)`;
                                    
                                    // Update dots
                                    dots.forEach(d => d.classList.remove('active'));
                                    dots[0].classList.add('active');
                                });
                            } else {
                                // Regular next slide
                                currentIndex++;
                                sled.style.transform = `translateX(-${currentIndex * slideWidth}%)`;
                                
                                // Update dots
                                const dotIndex = currentIndex >= totalSlides ? 0 : currentIndex;
                                dots.forEach(d => d.classList.remove('active'));
                                dots[dotIndex].classList.add('active');
                            }
                        });
                    }
                    
                    // Event delegation for dot clicks
                    container.addEventListener('click', (e) => {
                        const dot = e.target.closest('.image-sled-dot');
                        if (dot) {
                            e.preventDefault();
                            e.stopPropagation();
                            const index = parseInt(dot.getAttribute('data-index'));
                            goToSlide(index);
                            
                            // Reset interval
                            clearInterval(slideInterval);
                            slideInterval = setInterval(nextSlide, 3000);
                        }
                    });
                    
                    // Touch handling with passive listeners for better performance
                    sled.addEventListener('touchstart', (e) => {
                        startX = e.touches[0].clientX;
                        clearInterval(slideInterval);
                    }, { passive: true });
                    
                    sled.addEventListener('touchmove', (e) => {
                        if (!startX) return;
                        
                        moveX = e.touches[0].clientX;
                        const diff = startX - moveX;
                        const currentOffset = currentIndex * slideWidth;
                        
                        // Limit transform for better UX
                        const offset = Math.max(-slideWidth, Math.min(slideWidth * totalSlides, 
                            -(currentOffset + (diff / container.offsetWidth * slideWidth))
                        ));
                        
                        requestAnimationFrame(() => {
                            sled.style.transition = 'none';
                            sled.style.transform = `translateX(${offset}%)`;
                        });
                    }, { passive: true });
                    
                    sled.addEventListener('touchend', (e) => {
                        if (!startX || !moveX) return;
                        
                        const diff = startX - moveX;
                        const threshold = container.offsetWidth * 0.15; // 15% of container width
                        
                        requestAnimationFrame(() => {
                            sled.style.transition = 'transform 0.5s ease';
                            
                            if (Math.abs(diff) > threshold) {
                                if (diff > 0) {
                                    nextSlide();
                                } else {
                                    if (currentIndex === 0) {
                                        currentIndex = totalSlides - 1;
                                    } else {
                                        currentIndex--;
                                    }
                                    
                                    sled.style.transform = `translateX(-${currentIndex * slideWidth}%)`;
                                    
                                    // Update dots
                                    dots.forEach(d => d.classList.remove('active'));
                                    dots[currentIndex].classList.add('active');
                                }
                            } else {
                                // Return to original position if swipe not far enough
                                sled.style.transform = `translateX(-${currentIndex * slideWidth}%)`;
                            }
                            
                            // Restart autoplay
                            clearInterval(slideInterval);
                            slideInterval = setInterval(nextSlide, 3000);
                            
                            // Reset variables
                            startX = null;
                            moveX = null;
                        });
                    }, { passive: true });
                    
                    // Initial setup and start autoplay
                    requestAnimationFrame(() => {
                        sled.style.transform = 'translateX(0)';
                        dots[0].classList.add('active');
                    });
                    
                    // Start autoplay with idle callback for better performance
                    window.requestIdleCallback(() => {
                        slideInterval = setInterval(nextSlide, 3000);
                    });
                });
            }
            
            // Flash sale timer optimization
            function initializeFlashSaleTimers() {
                const allTimers = document.querySelectorAll('.flash-compact-timer');
                if (!allTimers.length) return;
                
                // Use requestAnimationFrame for better performance
                function updateAllFlashSaleTimers() {
                    requestAnimationFrame(() => {
                        // Batch query selectors for better performance
                        const allHours = document.querySelectorAll('.hours');
                        const allMinutes = document.querySelectorAll('.minutes');
                        const allSeconds = document.querySelectorAll('.seconds');
                        
                        for (let i = 0; i < allHours.length; i++) {
                            if (allHours[i] && allMinutes[i] && allSeconds[i]) {
                                // Get current values
                                let hours = parseInt(allHours[i].innerText);
                                let minutes = parseInt(allMinutes[i].innerText);
                                let seconds = parseInt(allSeconds[i].innerText);
                                
                                // Decrement one second
                                seconds--;
                                
                                if (seconds < 0) {
                                    seconds = 59;
                                    minutes--;
                                    
                                    if (minutes < 0) {
                                        minutes = 59;
                                        hours--;
                                        
                                        if (hours < 0) {
                                            hours = 0;
                                            minutes = 0;
                                            seconds = 0;
                                        }
                                    }
                                }
                                
                                // Update display with cached values
                                allHours[i].innerText = hours.toString().padStart(2, '0');
                                allMinutes[i].innerText = minutes.toString().padStart(2, '0');
                                allSeconds[i].innerText = seconds.toString().padStart(2, '0');
                            }
                        }
                        
                        // Call this function again after 1 second
                        setTimeout(updateAllFlashSaleTimers, 1000);
                    });
                }
                
                // Start timer updates
                updateAllFlashSaleTimers();
            }
        }
        
        // Preload critical assets
        preloadCriticalImages();
        
        // Loading libraries with progressive enhancement
        loadScript('https://unpkg.com/masonry-layout@4/dist/masonry.pkgd.min.js', function() {
            loadScript('https://unpkg.com/imagesloaded@5/imagesloaded.pkgd.min.js', initSite);
        });
    })();
    </script>
</body>
</html>