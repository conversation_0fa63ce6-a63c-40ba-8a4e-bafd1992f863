import { supabase } from "./supabase"

// Fungsi untuk mengupload gambar ke Supabase Storage
export async function uploadImage(file: File, folder = "misc"): Promise<string> {
  try {
    // Membuat nama file unik dengan timestamp dan random string
    const fileExt = file.name.split(".").pop()
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`
    const filePath = `${folder}/${fileName}`

    // Upload file ke Supabase Storage
    const { data, error } = await supabase.storage.from("sellzio-media").upload(filePath, file, {
      cacheControl: "3600",
      upsert: false,
    })

    if (error) {
      throw error
    }

    // Mendapatkan URL publik dari file yang diupload
    const { data: urlData } = supabase.storage.from("sellzio-media").getPublicUrl(filePath)

    return urlData.publicUrl
  } catch (error) {
    console.error("Error uploading image:", error)
    throw error
  }
}

// Fungsi untuk menghapus gambar dari Supabase Storage
export async function deleteImage(url: string): Promise<void> {
  try {
    // Ekstrak path file dari URL
    const urlObj = new URL(url)
    const pathParts = urlObj.pathname.split("/")
    const bucketName = pathParts[1]
    const filePath = pathParts.slice(2).join("/")

    // Hapus file dari Supabase Storage
    const { error } = await supabase.storage.from(bucketName).remove([filePath])

    if (error) {
      throw error
    }
  } catch (error) {
    console.error("Error deleting image:", error)
    throw error
  }
}
