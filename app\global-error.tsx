"use client"

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <html>
      <body>
        <div className="flex min-h-screen flex-col items-center justify-center p-4">
          <div className="w-full max-w-md text-center">
            <h2 className="text-2xl font-bold mb-4"><PERSON><PERSON><PERSON><PERSON></h2>
            <p className="mb-6"><PERSON><PERSON>, terjadi kesalahan yang tidak terduga.</p>
            <button onClick={() => reset()} className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
              Coba <PERSON>gi
            </button>
          </div>
        </div>
      </body>
    </html>
  )
}
