import { NextRequest, NextResponse } from 'next/server';
import { productImportExportService } from '@/lib/services/product-import-export';

// GET - Mendapatkan job berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    const job = await productImportExportService.getJob(id);
    
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(job);
  } catch (error) {
    console.error('Error fetching job:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job' },
      { status: 500 }
    );
  }
}

// PUT - Update job
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    
    const updatedJob = await productImportExportService.updateJob(id, body);
    
    return NextResponse.json(updatedJob);
  } catch (error) {
    console.error('Error updating job:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update job';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// DELETE - Hapus job
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    await productImportExportService.deleteJob(id);
    
    return NextResponse.json({ message: 'Job deleted successfully' });
  } catch (error) {
    console.error('Error deleting job:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to delete job';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// PATCH - Update job status atau actions
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { action, ...data } = body;
    
    let result;
    
    switch (action) {
      case 'cancel':
        result = await productImportExportService.cancelJob(id);
        break;
      case 'retry':
        result = await productImportExportService.retryJob(id);
        break;
      case 'update_status':
        result = await productImportExportService.updateJobStatus(id, data.status, data.progress);
        break;
      default:
        result = await productImportExportService.updateJob(id, data);
        break;
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating job:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update job';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
