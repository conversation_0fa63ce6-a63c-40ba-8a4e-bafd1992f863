"use client"

import { NotificationsProvider } from "@/components/providers/notifications-provider"
import { ConfirmationProvider } from "@/components/admin/ui/confirmation-dialog"
import { ErrorBoundary } from "@/components/error-boundary"
import CreateStoreClient from "./client"

export default function CreateStoreWrapper() {
  return (
    <ErrorBoundary>
      <ConfirmationProvider>
        <NotificationsProvider>
          <CreateStoreClient />
        </NotificationsProvider>
      </ConfirmationProvider>
    </ErrorBoundary>
  )
}
