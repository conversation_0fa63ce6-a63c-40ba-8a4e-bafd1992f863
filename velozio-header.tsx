"use client"

import { useState, useEffect } from "react"
import { Search, ShoppingCart, MessageSquare } from "lucide-react"
import "./velozio-styles.css"

export default function VelozioHeader() {
  const [placeholderIndex, setPlaceholderIndex] = useState(0)

  const placeholders = [
    "Handphone Samsung",
    "Sepatu Pria",
    "Tas Wanita",
    "Promo Elektronik",
    "Laptop Gaming",
    "Kamera Mirrorless",
    "Smart TV",
    "Headphone Bluetooth",
    "Mainan Anak",
    "Perabotan Rumah",
    "Kosmetik",
    "Buku Terlaris",
    "Alat Olahraga",
    "Aksesoris Fashion",
    "Voucher Game",
  ]

  // Animation for placeholder text - with longer duration
  useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prevIndex) => (prevIndex + 1) % placeholders.length)
    }, 3000) // 45 seconds for 15 items would be 3 seconds per item
    return () => clearInterval(interval)
  }, [])

  return (
    <header className="bg-[#ee4d2d] py-3 px-4 font-['Roboto',Arial,sans-serif] w-full z-50">
      <div className="max-w-7xl mx-auto flex justify-center items-center relative">
        {/* Search Container - Centered in the header */}
        <div className="search-container relative" style={{ maxWidth: "800px", width: "100%" }}>
          <div className="relative">
            <input
              type="text"
              className="search-input w-full py-2 px-4 rounded-lg border-0 focus:outline-none focus:ring-0"
              style={{
                borderRadius: "8px",
                paddingRight: "40px",
                backgroundColor: "#FFFFFF",
                boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
              }}
              placeholder={placeholders[placeholderIndex]}
            />

            {/* Search icon */}
            <div
              className="absolute top-1/2 transform -translate-y-1/2 text-[#ee4d2d] cursor-pointer"
              style={{ right: "15px" }}
            >
              <Search className="h-5 w-5" />
            </div>
          </div>
        </div>

        {/* Header Icons */}
        <div className="header-icons flex items-center absolute z-10" style={{ right: "8px" }}>
          <div className="cart-icon relative">
            <ShoppingCart
              className="h-[22px] w-[22px] text-transparent cursor-pointer"
              style={{ WebkitTextStroke: "1.3px white" }}
            />
            <span className="cart-badge absolute -top-2 -right-2 bg-white text-[#ee4d2d] text-xs font-bold rounded-full h-[18px] w-[18px] flex items-center justify-center">
              5
            </span>
          </div>

          <div className="chat-icon relative ml-6">
            <MessageSquare
              className="h-[22px] w-[22px] text-transparent cursor-pointer"
              style={{ WebkitTextStroke: "2px white" }}
            />
            {/* Custom chat dots */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex justify-between w-2.5 pointer-events-none">
              <div className="w-0.5 h-0.5 bg-white rounded-full"></div>
              <div className="w-0.5 h-0.5 bg-white rounded-full"></div>
              <div className="w-0.5 h-0.5 bg-white rounded-full"></div>
            </div>
            <span className="chat-badge absolute -top-2 -right-2 bg-white text-[#ee4d2d] text-xs font-bold rounded-full h-[18px] w-[18px] flex items-center justify-center">
              3
            </span>
          </div>
        </div>
      </div>
    </header>
  )
}
