"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  CreditCard,
  DollarSign,
  Calendar,
  Clock,
  Filter,
  Download,
  Search,
  ChevronDown,
  CheckCircle,
  XCircle,
  AlertCircle,
  ArrowDownToLine,
  Plus,
  FileText,
  CalendarDays
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Data dummy untuk ringkasan payouts
const payoutsSummary = {
  totalPendingAmount: 7850000,
  totalProcessedAmount: ********,
  nextPayoutDate: "2024-05-30T00:00:00",
  nextEstimatedAmount: 4500000,
  pendingPayouts: 3,
  completedPayouts: 18,
  failedPayouts: 1,
  payoutCycle: "Bulanan (setiap tanggal 15 & 30)",
  defaultPayoutMethod: "Transfer Bank",
  balance: {
    available: 4500000,
    pending: 3350000,
    onHold: 0
  }
}

// Data dummy untuk riwayat payouts
const payoutsHistory = [
  {
    id: "PYT-001",
    amount: 4500000,
    status: "completed",
    method: "Transfer Bank",
    bankAccount: "Bank Mandiri ****1234",
    initiatedAt: "2024-05-15T10:00:00",
    completedAt: "2024-05-15T16:30:00",
    reference: "INV-********-001",
    description: "Pembayaran periode 1-15 Mei 2024"
  },
  {
    id: "PYT-002",
    amount: 6200000,
    status: "completed",
    method: "Transfer Bank",
    bankAccount: "Bank Mandiri ****1234",
    initiatedAt: "2024-04-30T10:00:00",
    completedAt: "2024-04-30T15:45:00",
    reference: "INV-********-001",
    description: "Pembayaran periode 16-30 April 2024"
  },
  {
    id: "PYT-003",
    amount: 5450000,
    status: "completed",
    method: "Transfer Bank",
    bankAccount: "Bank Mandiri ****1234",
    initiatedAt: "2024-04-15T10:00:00",
    completedAt: "2024-04-15T16:20:00",
    reference: "INV-********-001",
    description: "Pembayaran periode 1-15 April 2024"
  },
  {
    id: "PYT-004",
    amount: 3350000,
    status: "pending",
    method: "Transfer Bank",
    bankAccount: "Bank Mandiri ****1234",
    initiatedAt: "2024-05-25T10:00:00",
    reference: "INV-********-001",
    description: "Pembayaran tambahan untuk promosi Mei"
  },
  {
    id: "PYT-005",
    amount: 2500000,
    status: "failed",
    method: "Transfer Bank",
    bankAccount: "Bank Mandiri ****1234",
    initiatedAt: "2024-05-20T10:00:00",
    failedAt: "2024-05-20T10:15:00",
    failReason: "Informasi rekening tidak valid",
    reference: "INV-********-001",
    description: "Pembayaran tambahan untuk komisi afiliasi"
  },
  {
    id: "PYT-006",
    amount: 4800000,
    status: "pending",
    method: "Transfer Bank",
    bankAccount: "Bank Mandiri ****1234",
    initiatedAt: "2024-05-22T11:30:00",
    reference: "INV-********-001",
    description: "Pembayaran tambahan untuk komisi penjualan"
  },
  {
    id: "PYT-007",
    amount: 6750000,
    status: "completed",
    method: "Transfer Bank",
    bankAccount: "Bank Mandiri ****1234",
    initiatedAt: "2024-03-30T10:00:00",
    completedAt: "2024-03-30T15:30:00",
    reference: "INV-********-001",
    description: "Pembayaran periode 16-30 Maret 2024"
  }
]

// Data dummy untuk metode pembayaran
const paymentMethods = [
  {
    id: "PM-001",
    type: "bank_transfer",
    bankName: "Bank Mandiri",
    accountNumber: "**********",
    accountName: "PT Contoh Bisnis",
    branchName: "Jakarta Pusat",
    isDefault: true
  },
  {
    id: "PM-002",
    type: "bank_transfer",
    bankName: "Bank BCA",
    accountNumber: "**********",
    accountName: "PT Contoh Bisnis",
    branchName: "Jakarta Selatan",
    isDefault: false
  }
]

// Format mata uang
function formatCurrency(number: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(number)
}

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Format waktu
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format tanggal dan waktu
function formatDateTime(dateString: string) {
  return `${formatDate(dateString)}, ${formatTime(dateString)}`
}

export default function PayoutsPage() {
  const [selectedTab, setSelectedTab] = useState("overview")
  const [filterStatus, setFilterStatus] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  
  // Filter payouts berdasarkan status dan pencarian
  const filteredPayouts = payoutsHistory.filter(payout => {
    // Filter berdasarkan status
    if (filterStatus !== "all" && payout.status !== filterStatus) {
      return false
    }
    
    // Filter berdasarkan pencarian
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        payout.id.toLowerCase().includes(query) ||
        payout.description.toLowerCase().includes(query) ||
        payout.reference.toLowerCase().includes(query)
      )
    }
    
    return true
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/financial">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Pembayaran</h1>
            <p className="text-muted-foreground">
              Kelola pembayaran dan metode penarikan dana
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Ajukan Penarikan
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="overview" value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="w-full md:w-auto">
          <TabsTrigger value="overview">Ringkasan</TabsTrigger>
          <TabsTrigger value="history">Riwayat</TabsTrigger>
          <TabsTrigger value="methods">Metode Pembayaran</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab Content */}
        <TabsContent value="overview" className="space-y-6">
          {/* Saldo Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Saldo Tersedia</CardTitle>
                <DollarSign className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{formatCurrency(payoutsSummary.balance.available)}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Dapat ditarik kapan saja
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Saldo Tertahan</CardTitle>
                <Clock className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{formatCurrency(payoutsSummary.balance.pending)}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Dalam masa tunggu
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pembayaran Berikutnya</CardTitle>
                <Calendar className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{formatCurrency(payoutsSummary.nextEstimatedAmount)}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Pada {formatDate(payoutsSummary.nextPayoutDate)}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Payout Info */}
          <Card>
            <CardHeader>
              <CardTitle>Informasi Pembayaran</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Siklus Pembayaran</div>
                    <div className="flex items-center">
                      <CalendarDays className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{payoutsSummary.payoutCycle}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Metode Pembayaran Default</div>
                    <div className="flex items-center">
                      <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{payoutsSummary.defaultPayoutMethod}</span>
                    </div>
                  </div>
                </div>
                
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="border rounded-md p-4">
                    <div className="text-sm font-medium mb-2">Pembayaran Tertunda</div>
                    <div className="text-2xl font-bold">{payoutsSummary.pendingPayouts}</div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {formatCurrency(payoutsSummary.totalPendingAmount)}
                    </div>
                  </div>
                  <div className="border rounded-md p-4">
                    <div className="text-sm font-medium mb-2">Pembayaran Selesai</div>
                    <div className="text-2xl font-bold">{payoutsSummary.completedPayouts}</div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {formatCurrency(payoutsSummary.totalProcessedAmount)}
                    </div>
                  </div>
                  <div className="border rounded-md p-4">
                    <div className="text-sm font-medium mb-2">Pembayaran Gagal</div>
                    <div className="text-2xl font-bold">{payoutsSummary.failedPayouts}</div>
                    <div className="text-sm text-muted-foreground mt-1">
                      Hubungi dukungan untuk bantuan
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Recent Payouts */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Pembayaran Terbaru</CardTitle>
              <Button variant="outline" size="sm" asChild>
                <Link href="#" onClick={() => setSelectedTab("history")}>
                  Lihat Semua
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              <div className="relative overflow-x-auto">
                <table className="w-full text-sm text-left">
                  <thead className="text-xs uppercase bg-muted/50">
                    <tr>
                      <th scope="col" className="px-6 py-3">ID</th>
                      <th scope="col" className="px-6 py-3">Tanggal</th>
                      <th scope="col" className="px-6 py-3">Jumlah</th>
                      <th scope="col" className="px-6 py-3">Metode</th>
                      <th scope="col" className="px-6 py-3">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {payoutsHistory.slice(0, 5).map(payout => (
                      <tr key={payout.id} className="border-b">
                        <td className="px-6 py-4 font-medium">
                          <Link href={`/tenant/dashboard/financial/payouts/${payout.id}`} className="hover:underline">
                            {payout.id}
                          </Link>
                        </td>
                        <td className="px-6 py-4">{formatDate(payout.initiatedAt)}</td>
                        <td className="px-6 py-4">{formatCurrency(payout.amount)}</td>
                        <td className="px-6 py-4">{payout.method}</td>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            {payout.status === "completed" ? (
                              <Badge className="bg-green-100 text-green-800">Selesai</Badge>
                            ) : payout.status === "pending" ? (
                              <Badge className="bg-yellow-100 text-yellow-800">Diproses</Badge>
                            ) : (
                              <Badge className="bg-red-100 text-red-800">Gagal</Badge>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* History Tab Content */}
        <TabsContent value="history" className="space-y-6">
          {/* Filters */}
          <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
            <div className="flex flex-col md:flex-row gap-4 md:items-center">
              <div className="w-full md:w-[300px]">
                <Input 
                  placeholder="Cari berdasarkan ID atau deskripsi..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-[180px]">
                  <div className="flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    <span>Status</span>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="completed">Selesai</SelectItem>
                  <SelectItem value="pending">Diproses</SelectItem>
                  <SelectItem value="failed">Gagal</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Ekspor
            </Button>
          </div>
          
          {/* Payouts Table */}
          <Card>
            <CardContent className="p-0">
              <div className="relative overflow-x-auto">
                <table className="w-full text-sm text-left">
                  <thead className="text-xs uppercase bg-muted/50">
                    <tr>
                      <th scope="col" className="px-6 py-3">ID</th>
                      <th scope="col" className="px-6 py-3">Tanggal</th>
                      <th scope="col" className="px-6 py-3">Jumlah</th>
                      <th scope="col" className="px-6 py-3">Deskripsi</th>
                      <th scope="col" className="px-6 py-3">Metode</th>
                      <th scope="col" className="px-6 py-3">Status</th>
                      <th scope="col" className="px-6 py-3">Aksi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredPayouts.map(payout => (
                      <tr key={payout.id} className="border-b">
                        <td className="px-6 py-4 font-medium">
                          <Link href={`/tenant/dashboard/financial/payouts/${payout.id}`} className="hover:underline">
                            {payout.id}
                          </Link>
                        </td>
                        <td className="px-6 py-4">{formatDateTime(payout.initiatedAt)}</td>
                        <td className="px-6 py-4">{formatCurrency(payout.amount)}</td>
                        <td className="px-6 py-4">
                          <div className="max-w-[200px] truncate">{payout.description}</div>
                          <div className="text-xs text-muted-foreground">Ref: {payout.reference}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div>{payout.method}</div>
                          <div className="text-xs text-muted-foreground">{payout.bankAccount}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            {payout.status === "completed" ? (
                              <>
                                <CheckCircle className="h-4 w-4 text-green-600 mr-1.5" />
                                <span className="text-green-600">Selesai</span>
                              </>
                            ) : payout.status === "pending" ? (
                              <>
                                <Clock className="h-4 w-4 text-yellow-600 mr-1.5" />
                                <span className="text-yellow-600">Diproses</span>
                              </>
                            ) : (
                              <>
                                <XCircle className="h-4 w-4 text-red-600 mr-1.5" />
                                <span className="text-red-600">Gagal</span>
                              </>
                            )}
                          </div>
                          {payout.failReason && (
                            <div className="text-xs text-red-600 mt-1">
                              {payout.failReason}
                            </div>
                          )}
                          {payout.completedAt && (
                            <div className="text-xs text-muted-foreground mt-1">
                              Selesai pada {formatTime(payout.completedAt)}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <span className="sr-only">Buka menu</span>
                                <ChevronDown className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <FileText className="h-4 w-4 mr-2" />
                                Lihat Detail
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <ArrowDownToLine className="h-4 w-4 mr-2" />
                                Unduh Bukti
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {filteredPayouts.length === 0 && (
                <div className="p-8 text-center">
                  <p className="text-muted-foreground">Tidak ada data pembayaran yang sesuai dengan filter</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Payment Methods Tab Content */}
        <TabsContent value="methods" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">Metode Pembayaran Terdaftar</h2>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Tambah Metode Baru
            </Button>
          </div>
          
          <div className="grid gap-4 md:grid-cols-2">
            {paymentMethods.map(method => (
              <Card key={method.id} className={`${method.isDefault ? 'border-primary' : ''}`}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-base">
                      {method.bankName}
                      {method.isDefault && (
                        <Badge className="ml-2 bg-primary/20 text-primary">Default</Badge>
                      )}
                    </CardTitle>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <span className="sr-only">Buka menu</span>
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {!method.isDefault && (
                          <DropdownMenuItem>
                            Jadikan Default
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem>
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-1">
                      <div className="text-sm text-muted-foreground">Nama Akun</div>
                      <div className="text-sm font-medium">{method.accountName}</div>
                    </div>
                    <div className="grid grid-cols-2 gap-1">
                      <div className="text-sm text-muted-foreground">Nomor Rekening</div>
                      <div className="text-sm font-medium">{method.accountNumber}</div>
                    </div>
                    <div className="grid grid-cols-2 gap-1">
                      <div className="text-sm text-muted-foreground">Cabang</div>
                      <div className="text-sm font-medium">{method.branchName}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Informasi Penting</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm">
                    Pembayaran diproses sesuai jadwal pembayaran regular. Pastikan informasi rekening bank Anda akurat untuk menghindari keterlambatan pembayaran.
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm">
                    Pembayaran minimum yang dapat ditarik adalah Rp 50.000. Saldo di bawah jumlah ini akan diakumulasikan ke periode pembayaran berikutnya.
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm">
                    Jika Anda ingin mengubah metode pembayaran default, pastikan untuk melakukannya minimal 3 hari sebelum tanggal pembayaran berikutnya.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 