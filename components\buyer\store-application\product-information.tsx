"use client"

import type React from "react"

import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Upload, X } from "lucide-react"
import { useState } from "react"

interface ProductInformationProps {
  formData: any
  onChange: (field: string, value: any) => void
}

export function ProductInformation({ formData, onChange }: ProductInformationProps) {
  const [sampleImagePreviews, setSampleImagePreviews] = useState<string[]>([])

  const handleCategoryChange = (category: string, checked: boolean) => {
    const currentCategories = [...(formData.productCategories || [])]

    if (checked) {
      if (!currentCategories.includes(category)) {
        onChange("productCategories", [...currentCategories, category])
      }
    } else {
      onChange(
        "productCategories",
        currentCategories.filter((c) => c !== category),
      )
    }
  }

  const handleImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files)
      const updatedFiles = [...(formData.sampleImages || []), ...newFiles]
      onChange("sampleImages", updatedFiles)

      // Create previews
      const newPreviews: string[] = []
      newFiles.forEach((file) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          if (e.target?.result) {
            newPreviews.push(e.target.result as string)
            if (newPreviews.length === newFiles.length) {
              setSampleImagePreviews((prev) => [...prev, ...newPreviews])
            }
          }
        }
        reader.readAsDataURL(file)
      })
    }
  }

  const removeImage = (index: number) => {
    const updatedFiles = [...(formData.sampleImages || [])]
    updatedFiles.splice(index, 1)
    onChange("sampleImages", updatedFiles)

    const updatedPreviews = [...sampleImagePreviews]
    updatedPreviews.splice(index, 1)
    setSampleImagePreviews(updatedPreviews)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Informasi Produk</h2>
        <p className="text-gray-500">Berikan informasi detail tentang produk yang akan Anda jual di platform kami.</p>
      </div>

      <div className="space-y-6">
        <div className="space-y-3">
          <Label className="text-base">
            Kategori Produk <span className="text-red-500">*</span>
          </Label>
          <p className="text-sm text-gray-500 mb-2">
            Pilih kategori produk yang akan Anda jual (bisa lebih dari satu).
          </p>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {productCategories.map((category) => (
              <div key={category.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category.value}`}
                  checked={(formData.productCategories || []).includes(category.value)}
                  onCheckedChange={(checked) => handleCategoryChange(category.value, checked as boolean)}
                />
                <label
                  htmlFor={`category-${category.value}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {category.label}
                </label>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <Label htmlFor="estimatedProducts">
              Perkiraan Jumlah Produk <span className="text-red-500">*</span>
            </Label>
            <Select value={formData.estimatedProducts} onValueChange={(value) => onChange("estimatedProducts", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih perkiraan jumlah" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1-10">1-10 produk</SelectItem>
                <SelectItem value="11-50">11-50 produk</SelectItem>
                <SelectItem value="51-100">51-100 produk</SelectItem>
                <SelectItem value="101-500">101-500 produk</SelectItem>
                <SelectItem value="500+">Lebih dari 500 produk</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <Label htmlFor="productSource">
              Sumber Produk <span className="text-red-500">*</span>
            </Label>
            <Select value={formData.productSource} onValueChange={(value) => onChange("productSource", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih sumber produk" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="handmade">Buatan Sendiri (Handmade)</SelectItem>
                <SelectItem value="dropship">Dropship</SelectItem>
                <SelectItem value="reseller">Reseller</SelectItem>
                <SelectItem value="wholesale">Grosir/Wholesale</SelectItem>
                <SelectItem value="manufacturer">Produsen Langsung</SelectItem>
                <SelectItem value="mixed">Campuran Beberapa Sumber</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3 md:col-span-2">
            <Label htmlFor="sampleDescription">
              Contoh Deskripsi Produk <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="sampleDescription"
              value={formData.sampleDescription}
              onChange={(e) => onChange("sampleDescription", e.target.value)}
              placeholder="Berikan contoh deskripsi untuk salah satu produk yang akan Anda jual..."
              rows={4}
            />
            <p className="text-xs text-gray-500">
              Contoh deskripsi ini akan membantu kami memahami bagaimana Anda mempresentasikan produk Anda.
            </p>
          </div>

          <div className="space-y-3">
            <Label htmlFor="priceRangeMin">
              Rentang Harga Produk (Rp) <span className="text-red-500">*</span>
            </Label>
            <div className="flex items-center gap-3">
              <Input
                id="priceRangeMin"
                type="number"
                placeholder="Harga minimum"
                value={formData.priceRange?.min || ""}
                onChange={(e) => onChange("priceRange", { ...formData.priceRange, min: e.target.value })}
              />
              <span>hingga</span>
              <Input
                id="priceRangeMax"
                type="number"
                placeholder="Harga maksimum"
                value={formData.priceRange?.max || ""}
                onChange={(e) => onChange("priceRange", { ...formData.priceRange, max: e.target.value })}
              />
            </div>
          </div>

          <div className="space-y-3">
            <Label className="text-base">
              Kemampuan Pengiriman <span className="text-red-500">*</span>
            </Label>
            <div className="grid grid-cols-2 gap-3">
              {shippingOptions.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`shipping-${option.value}`}
                    checked={(formData.shippingCapabilities || []).includes(option.value)}
                    onCheckedChange={(checked) => {
                      const current = [...(formData.shippingCapabilities || [])]
                      if (checked) {
                        if (!current.includes(option.value)) {
                          onChange("shippingCapabilities", [...current, option.value])
                        }
                      } else {
                        onChange(
                          "shippingCapabilities",
                          current.filter((o) => o !== option.value),
                        )
                      }
                    }}
                  />
                  <label
                    htmlFor={`shipping-${option.value}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {option.label}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-3 md:col-span-2">
            <Label>
              Contoh Gambar Produk <span className="text-red-500">*</span>
            </Label>
            <p className="text-sm text-gray-500 mb-2">
              Upload 2-5 gambar contoh produk yang akan Anda jual (format JPG atau PNG, maks. 2MB per file).
            </p>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
              {sampleImagePreviews.map((preview, index) => (
                <Card key={index} className="overflow-hidden">
                  <div className="relative aspect-square">
                    <img
                      src={preview || "/placeholder.svg"}
                      alt={`Product Sample ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-1 right-1 w-6 h-6 p-0 rounded-full"
                      onClick={() => removeImage(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </Card>
              ))}

              {sampleImagePreviews.length < 5 && (
                <Card className="border border-dashed">
                  <CardContent className="p-4 h-full flex flex-col items-center justify-center">
                    <Upload className="h-8 w-8 text-gray-400 mb-2" />
                    <p className="text-xs text-gray-500 text-center">Upload Gambar</p>
                    <input
                      type="file"
                      id="sampleImages"
                      multiple
                      accept="image/png, image/jpeg"
                      className="hidden"
                      onChange={handleImagesChange}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="mt-2"
                      onClick={() => document.getElementById("sampleImages")?.click()}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Tambah
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const productCategories = [
  { value: "clothing", label: "Pakaian & Aksesoris" },
  { value: "shoes", label: "Sepatu" },
  { value: "bags", label: "Tas & Koper" },
  { value: "electronics", label: "Elektronik" },
  { value: "computers", label: "Komputer & Laptop" },
  { value: "smartphones", label: "Smartphone & Tablet" },
  { value: "cameras", label: "Kamera & Foto" },
  { value: "home", label: "Rumah Tangga" },
  { value: "furniture", label: "Furnitur" },
  { value: "kitchen", label: "Dapur" },
  { value: "beauty", label: "Kecantikan" },
  { value: "health", label: "Kesehatan" },
  { value: "food", label: "Makanan & Minuman" },
  { value: "toys", label: "Mainan & Hobi" },
  { value: "books", label: "Buku & Alat Tulis" },
  { value: "sports", label: "Olahraga & Outdoor" },
  { value: "automotive", label: "Otomotif" },
  { value: "jewelry", label: "Perhiasan" },
  { value: "baby", label: "Perlengkapan Bayi" },
  { value: "digital", label: "Produk Digital" },
  { value: "other", label: "Lainnya" },
]

const shippingOptions = [
  { value: "domestic", label: "Pengiriman Domestik" },
  { value: "international", label: "Pengiriman Internasional" },
  { value: "sameday", label: "Same Day Delivery" },
  { value: "pickup", label: "Self Pickup" },
  { value: "free", label: "Free Shipping" },
  { value: "cod", label: "Cash on Delivery" },
]
