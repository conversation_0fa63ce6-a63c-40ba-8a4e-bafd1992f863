import { NextRequest, NextResponse } from 'next/server';
import { productModerationService } from '@/lib/services/product-moderation';

// GET - Mendapatkan statistik product moderation
export async function GET(request: NextRequest) {
  try {
    const stats = await productModerationService.getModerationStats();
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching moderation stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch moderation stats' },
      { status: 500 }
    );
  }
}
