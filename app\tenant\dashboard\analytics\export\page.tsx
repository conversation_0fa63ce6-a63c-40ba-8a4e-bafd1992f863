"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  FileText,
  Download,
  Database,
  CalendarDays,
  Settings,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  FileDown,
  Filter,
  RefreshCw,
  Calendar,
  Sliders
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

// Data dummy untuk riwayat ekspor
const exportHistory = [
  {
    id: "export-001",
    name: "Ekspor Data Penjualan Q2",
    type: "Sales",
    format: "CSV",
    createdAt: "2024-05-18T10:30:00",
    status: "completed",
    size: "4.2 MB",
    records: 1245,
    downloadUrl: "#"
  },
  {
    id: "export-002",
    name: "Ekspor Data Pelanggan Lengkap",
    type: "Customers",
    format: "XLSX",
    createdAt: "2024-05-15T14:20:00",
    status: "completed",
    size: "8.7 MB",
    records: 3456,
    downloadUrl: "#"
  },
  {
    id: "export-003",
    name: "Ekspor Katalog Produk",
    type: "Products",
    format: "JSON",
    createdAt: "2024-05-12T09:15:00",
    status: "completed",
    size: "2.8 MB",
    records: 845,
    downloadUrl: "#"
  },
  {
    id: "export-004",
    name: "Ekspor Data Transaksi Mei",
    type: "Transactions",
    format: "CSV",
    createdAt: "2024-05-10T16:45:00",
    status: "completed",
    size: "12.5 MB",
    records: 5230,
    downloadUrl: "#"
  },
  {
    id: "export-005",
    name: "Ekspor Data Marketing",
    type: "Marketing",
    format: "XLSX",
    createdAt: "2024-05-05T11:30:00",
    status: "failed",
    error: "Data terlalu besar untuk diproses",
    downloadUrl: "#"
  }
]

// Data dummy untuk tipe data yang dapat diekspor
const exportableData = [
  {
    id: "data-sales",
    name: "Data Penjualan",
    description: "Ekspor data penjualan, transaksi, dan pesanan",
    icon: "sales",
    formats: ["CSV", "XLSX", "JSON"],
    filters: [
      { name: "Rentang Tanggal", type: "date-range" },
      { name: "Status Pesanan", type: "select", options: ["Semua", "Selesai", "Diproses", "Dibatalkan"] },
      { name: "Metode Pembayaran", type: "select", options: ["Semua", "Transfer Bank", "Kartu Kredit", "E-Wallet", "QRIS"] }
    ]
  },
  {
    id: "data-customers",
    name: "Data Pelanggan",
    description: "Ekspor data pelanggan, profil, dan riwayat pembelian",
    icon: "customers",
    formats: ["CSV", "XLSX", "JSON"],
    filters: [
      { name: "Segmen Pelanggan", type: "select", options: ["Semua", "VIP", "Loyal", "Reguler", "Baru"] },
      { name: "Status Pelanggan", type: "select", options: ["Semua", "Aktif", "Tidak Aktif"] },
      { name: "Lokasi", type: "select", options: ["Semua", "Jakarta", "Surabaya", "Bandung", "Medan", "Lainnya"] }
    ]
  },
  {
    id: "data-products",
    name: "Data Produk",
    description: "Ekspor data produk, kategori, dan inventaris",
    icon: "products",
    formats: ["CSV", "XLSX", "JSON"],
    filters: [
      { name: "Kategori", type: "select", options: ["Semua", "Pakaian", "Sepatu", "Tas", "Aksesoris", "Elektronik"] },
      { name: "Status Stok", type: "select", options: ["Semua", "Tersedia", "Terbatas", "Habis"] }
    ]
  },
  {
    id: "data-marketing",
    name: "Data Marketing",
    description: "Ekspor data kampanye, promosi, dan performa iklan",
    icon: "marketing",
    formats: ["CSV", "XLSX", "JSON"],
    filters: [
      { name: "Rentang Tanggal", type: "date-range" },
      { name: "Tipe Kampanye", type: "select", options: ["Semua", "Email", "Social Media", "Diskon", "Event"] },
      { name: "Status", type: "select", options: ["Semua", "Aktif", "Selesai", "Draft"] }
    ]
  },
  {
    id: "data-finance",
    name: "Data Keuangan",
    description: "Ekspor data keuangan, pendapatan, dan pengeluaran",
    icon: "finance",
    formats: ["CSV", "XLSX"],
    filters: [
      { name: "Rentang Tanggal", type: "date-range" },
      { name: "Kategori", type: "select", options: ["Semua", "Pendapatan", "Pengeluaran", "Pajak", "Lainnya"] }
    ]
  }
]

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Format waktu
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format tanggal dan waktu
function formatDateTime(dateString: string) {
  return `${formatDate(dateString)}, ${formatTime(dateString)}`
}

// Format angka dengan pemisah ribuan
function formatNumber(number: number) {
  return number.toLocaleString('id-ID')
}

export default function ExportPage() {
  const [selectedTab, setSelectedTab] = useState("new")
  const [selectedDataType, setSelectedDataType] = useState("")
  const [selectedFormat, setSelectedFormat] = useState("CSV")
  const [searchQuery, setSearchQuery] = useState("")

  // Filter riwayat ekspor berdasarkan pencarian
  const filteredExportHistory = exportHistory.filter(item => 
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.type.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Temukan data tipe yang dipilih
  const selectedData = exportableData.find(item => item.id === selectedDataType)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/analytics">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Ekspor Data</h1>
            <p className="text-muted-foreground">
              Ekspor data bisnis Anda dalam berbagai format
            </p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="new" value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="w-full md:w-auto">
          <TabsTrigger value="new">Ekspor Baru</TabsTrigger>
          <TabsTrigger value="history">Riwayat Ekspor</TabsTrigger>
        </TabsList>
        
        {/* New Export Tab Content */}
        <TabsContent value="new" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Left Column - Data Types */}
            <div>
              <h2 className="text-lg font-medium mb-4">Pilih Tipe Data</h2>
              <div className="space-y-4">
                {exportableData.map(dataType => (
                  <Card 
                    key={dataType.id}
                    className={`cursor-pointer transition-colors ${selectedDataType === dataType.id ? 'border-primary' : ''}`}
                    onClick={() => setSelectedDataType(dataType.id)}
                  >
                    <CardHeader className="flex flex-row items-start space-y-0 pb-2">
                      <div className="flex-1">
                        <CardTitle className="text-base">{dataType.name}</CardTitle>
                        <CardDescription>{dataType.description}</CardDescription>
                      </div>
                      <div className="w-5 h-5 rounded-full flex items-center justify-center">
                        {selectedDataType === dataType.id && <CheckCircle className="h-5 w-5 text-primary" />}
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex flex-wrap gap-1">
                        {dataType.formats.map(format => (
                          <Badge key={format} variant="secondary" className="text-xs">
                            {format}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
            
            {/* Right Column - Export Settings */}
            <div>
              <h2 className="text-lg font-medium mb-4">Konfigurasi Ekspor</h2>
              
              {selectedDataType ? (
                <Card>
                  <CardHeader>
                    <CardTitle>Pengaturan Ekspor {selectedData?.name}</CardTitle>
                    <CardDescription>
                      Atur pengaturan dan filter untuk ekspor data
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Format Selection */}
                    <div className="space-y-2">
                      <Label>Format</Label>
                      <RadioGroup 
                        value={selectedFormat} 
                        onValueChange={setSelectedFormat}
                        className="flex space-x-4"
                      >
                        {selectedData?.formats.map(format => (
                          <div key={format} className="flex items-center space-x-2">
                            <RadioGroupItem value={format} id={`format-${format}`} />
                            <Label htmlFor={`format-${format}`}>{format}</Label>
                          </div>
                        ))}
                      </RadioGroup>
                    </div>
                    
                    {/* Filters */}
                    <div className="space-y-4">
                      <h3 className="text-sm font-medium">Filter</h3>
                      
                      {selectedData?.filters.map(filter => (
                        <div key={filter.name} className="space-y-2">
                          <Label>{filter.name}</Label>
                          
                          {filter.type === 'date-range' && (
                            <div className="grid grid-cols-2 gap-2">
                              <div className="flex flex-col space-y-1">
                                <Label className="text-xs">Dari</Label>
                                <Input type="date" className="h-9" />
                              </div>
                              <div className="flex flex-col space-y-1">
                                <Label className="text-xs">Sampai</Label>
                                <Input type="date" className="h-9" />
                              </div>
                            </div>
                          )}
                          
                          {filter.type === 'select' && (
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Pilih..." />
                              </SelectTrigger>
                              <SelectContent>
                                {filter.options?.map(option => (
                                  <SelectItem key={option} value={option}>
                                    {option}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                        </div>
                      ))}
                    </div>
                    
                    {/* Include Fields */}
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Sertakan</h3>
                      
                      <div className="grid grid-cols-2 gap-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="include-fields" defaultChecked />
                          <Label htmlFor="include-fields">Semua Kolom</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="include-headers" defaultChecked />
                          <Label htmlFor="include-headers">Header Kolom</Label>
                        </div>
                      </div>
                    </div>
                    
                    {/* Actions */}
                    <div className="pt-4">
                      <Button className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        Ekspor Data
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="border rounded-md p-8 text-center h-full flex flex-col items-center justify-center">
                  <Database className="h-12 w-12 mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-medium mb-2">Pilih Tipe Data</h3>
                  <p className="text-muted-foreground">
                    Pilih tipe data yang ingin Anda ekspor dari panel sebelah kiri
                  </p>
                </div>
              )}
            </div>
          </div>
        </TabsContent>
        
        {/* Export History Tab Content */}
        <TabsContent value="history" className="space-y-6">
          {/* Search */}
          <div className="flex w-full max-w-sm items-center space-x-2">
            <Input 
              type="text" 
              placeholder="Cari riwayat ekspor..." 
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button type="submit" variant="outline">
              Cari
            </Button>
          </div>
          
          {/* History Table */}
          <div className="rounded-md border">
            <div className="relative overflow-x-auto">
              <table className="w-full text-sm text-left">
                <thead className="text-xs uppercase bg-muted/50">
                  <tr>
                    <th scope="col" className="px-6 py-3">Nama Ekspor</th>
                    <th scope="col" className="px-6 py-3">Tipe Data</th>
                    <th scope="col" className="px-6 py-3">Format</th>
                    <th scope="col" className="px-6 py-3">Tanggal</th>
                    <th scope="col" className="px-6 py-3">Status</th>
                    <th scope="col" className="px-6 py-3">Ukuran</th>
                    <th scope="col" className="px-6 py-3">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredExportHistory.map(item => (
                    <tr key={item.id} className="border-b">
                      <td className="px-6 py-4 font-medium">
                        {item.name}
                        {item.records && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {formatNumber(item.records)} records
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4">{item.type}</td>
                      <td className="px-6 py-4">
                        <Badge variant="outline">{item.format}</Badge>
                      </td>
                      <td className="px-6 py-4">{formatDateTime(item.createdAt)}</td>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          {item.status === "completed" ? (
                            <>
                              <CheckCircle className="h-4 w-4 text-green-600 mr-1.5" />
                              <span className="text-green-600">Selesai</span>
                            </>
                          ) : item.status === "processing" ? (
                            <>
                              <Clock className="h-4 w-4 text-blue-600 mr-1.5" />
                              <span className="text-blue-600">Diproses</span>
                            </>
                          ) : (
                            <>
                              <XCircle className="h-4 w-4 text-red-600 mr-1.5" />
                              <span className="text-red-600">Gagal</span>
                            </>
                          )}
                        </div>
                        {item.error && (
                          <div className="text-xs text-red-600 mt-1 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {item.error}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4">{item.size}</td>
                      <td className="px-6 py-4">
                        <div className="flex space-x-2">
                          {item.status === "completed" && (
                            <Button variant="outline" size="sm" asChild>
                              <Link href={item.downloadUrl}>
                                <Download className="h-3.5 w-3.5 mr-1" />
                                Unduh
                              </Link>
                            </Button>
                          )}
                          <Button variant="outline" size="sm">
                            <RefreshCw className="h-3.5 w-3.5 mr-1" />
                            Ulangi
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          
          {filteredExportHistory.length === 0 && (
            <div className="border rounded-md p-8 text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">Tidak ada riwayat ekspor</h3>
              <p className="text-muted-foreground mb-4">
                Anda belum melakukan ekspor data apapun, atau tidak ada hasil yang sesuai dengan pencarian Anda.
              </p>
              <Button onClick={() => setSelectedTab("new")}>
                <Download className="h-4 w-4 mr-2" />
                Buat Ekspor Baru
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
} 