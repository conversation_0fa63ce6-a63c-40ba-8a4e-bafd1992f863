"use client"

import { useState } from "react"
import {
  Download,
  Calendar,
  Filter,
  RefreshCw,
  Plus,
  Building,
  ArrowUpRight,
  ArrowDownRight,
  Users,
  ShoppingCart,
  CreditCard,
  TrendingUp,
  ChevronDown,
  ChevronUp,
  Share2,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

// Sample data for tenants
const tenants = [
  {
    id: "TEN-001",
    name: "Fashion Marketplace",
    stores: 48,
    users: 12500,
    revenue: 1250000,
    growth: 12.5,
    health: "Excellent",
  },
  {
    id: "TEN-002",
    name: "Tech Gadgets Hub",
    stores: 32,
    users: 8700,
    revenue: 980000,
    growth: 8.2,
    health: "Good",
  },
  {
    id: "TEN-003",
    name: "Home Decor Collective",
    stores: 27,
    users: 6300,
    revenue: 720000,
    growth: -2.1,
    health: "Fair",
  },
  {
    id: "TEN-004",
    name: "Sports Equipment Network",
    stores: 19,
    users: 4200,
    revenue: 560000,
    growth: 5.7,
    health: "Good",
  },
  {
    id: "TEN-005",
    name: "Artisan Crafts Marketplace",
    stores: 64,
    users: 15800,
    revenue: 1450000,
    growth: 18.3,
    health: "Excellent",
  },
]

// Sample data for tenant activity
const tenantActivity = [
  {
    id: "ACT-001",
    tenant: "Fashion Marketplace",
    action: "New store added",
    date: "2023-05-10T09:30:00",
    impact: "Positive",
  },
  {
    id: "ACT-002",
    tenant: "Tech Gadgets Hub",
    action: "Subscription upgraded",
    date: "2023-05-09T14:15:00",
    impact: "Positive",
  },
  {
    id: "ACT-003",
    tenant: "Home Decor Collective",
    action: "Multiple store closures",
    date: "2023-05-08T11:45:00",
    impact: "Negative",
  },
  {
    id: "ACT-004",
    tenant: "Sports Equipment Network",
    action: "New feature adoption",
    date: "2023-05-07T16:20:00",
    impact: "Positive",
  },
  {
    id: "ACT-005",
    tenant: "Artisan Crafts Marketplace",
    action: "User growth spike",
    date: "2023-05-06T10:00:00",
    impact: "Positive",
  },
]

// Sample data for tenant health indicators
const healthIndicators = [
  {
    tenant: "Fashion Marketplace",
    userGrowth: 12.5,
    storeRetention: 98.2,
    revenueGrowth: 15.7,
    featureAdoption: 87.3,
    supportTickets: 12,
  },
  {
    tenant: "Tech Gadgets Hub",
    userGrowth: 8.2,
    storeRetention: 95.6,
    revenueGrowth: 10.3,
    featureAdoption: 76.8,
    supportTickets: 18,
  },
  {
    tenant: "Home Decor Collective",
    userGrowth: -2.1,
    storeRetention: 91.4,
    revenueGrowth: -1.5,
    featureAdoption: 62.1,
    supportTickets: 27,
  },
  {
    tenant: "Sports Equipment Network",
    userGrowth: 5.7,
    storeRetention: 94.8,
    revenueGrowth: 7.2,
    featureAdoption: 71.5,
    supportTickets: 15,
  },
  {
    tenant: "Artisan Crafts Marketplace",
    userGrowth: 18.3,
    storeRetention: 99.1,
    revenueGrowth: 21.6,
    featureAdoption: 92.7,
    supportTickets: 8,
  },
]

export function TenantReports() {
  const [activeTab, setActiveTab] = useState("performance")
  const [selectedTenant, setSelectedTenant] = useState<string | null>(null)

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date)
  }

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    }).format(date)
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount)
  }

  // Get health badge color
  const getHealthBadge = (health: string) => {
    switch (health) {
      case "Excellent":
        return <Badge className="bg-green-500">Excellent</Badge>
      case "Good":
        return <Badge className="bg-blue-500">Good</Badge>
      case "Fair":
        return <Badge className="bg-yellow-500">Fair</Badge>
      case "Poor":
        return <Badge className="bg-red-500">Poor</Badge>
      default:
        return <Badge>{health}</Badge>
    }
  }

  // Get growth indicator
  const getGrowthIndicator = (growth: number) => {
    if (growth > 0) {
      return (
        <div className="flex items-center text-green-500">
          <ArrowUpRight className="mr-1 h-4 w-4" />
          <span>{growth}%</span>
        </div>
      )
    } else {
      return (
        <div className="flex items-center text-red-500">
          <ArrowDownRight className="mr-1 h-4 w-4" />
          <span>{Math.abs(growth)}%</span>
        </div>
      )
    }
  }

  // Get impact badge
  const getImpactBadge = (impact: string) => {
    switch (impact) {
      case "Positive":
        return <Badge className="bg-green-500">Positive</Badge>
      case "Negative":
        return <Badge className="bg-red-500">Negative</Badge>
      case "Neutral":
        return <Badge className="bg-gray-500">Neutral</Badge>
      default:
        return <Badge>{impact}</Badge>
    }
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tenant Reports</h1>
          <p className="text-muted-foreground">Analyze and compare tenant performance metrics</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="default">
            <Plus className="mr-2 h-4 w-4" />
            New Report
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Total Tenants</CardTitle>
            <CardDescription>Active tenants on platform</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">127</div>
            <p className="text-xs text-muted-foreground">+5 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Average Stores</CardTitle>
            <CardDescription>Stores per tenant</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">+2.5% increase</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>User Base</CardTitle>
            <CardDescription>Total users across tenants</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">1.2M</div>
            <p className="text-xs text-muted-foreground">+8.3% growth</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Platform Revenue</CardTitle>
            <CardDescription>From tenant subscriptions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">$4.8M</div>
            <p className="text-xs text-muted-foreground">+12.7% increase</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="performance">Performance Comparison</TabsTrigger>
          <TabsTrigger value="growth">Growth Metrics</TabsTrigger>
          <TabsTrigger value="health">Health Indicators</TabsTrigger>
          <TabsTrigger value="activity">Activity Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Tenant Performance Comparison</CardTitle>
                  <CardDescription>Compare key metrics across tenants</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Input placeholder="Search tenants..." className="h-8 w-[200px]" />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[200px]">Tenant</TableHead>
                    <TableHead>Stores</TableHead>
                    <TableHead>Users</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>Growth</TableHead>
                    <TableHead>Health</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tenants.map((tenant) => (
                    <TableRow key={tenant.id} className={selectedTenant === tenant.id ? "bg-accent/50" : ""}>
                      <TableCell className="font-medium">{tenant.name}</TableCell>
                      <TableCell>{tenant.stores}</TableCell>
                      <TableCell>{tenant.users.toLocaleString()}</TableCell>
                      <TableCell>{formatCurrency(tenant.revenue)}</TableCell>
                      <TableCell>{getGrowthIndicator(tenant.growth)}</TableCell>
                      <TableCell>{getHealthBadge(tenant.health)}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => setSelectedTenant(tenant.id)}>
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">Showing 5 of 127 tenants</div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  Previous
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="growth" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Tenant Growth Metrics</CardTitle>
                  <CardDescription>Analyze growth patterns across tenants</CardDescription>
                </div>
                <Select defaultValue="6months">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select time period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30days">Last 30 days</SelectItem>
                    <SelectItem value="3months">Last 3 months</SelectItem>
                    <SelectItem value="6months">Last 6 months</SelectItem>
                    <SelectItem value="1year">Last year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">User Growth by Tenant</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      {tenants.map((tenant) => (
                        <div key={`user-${tenant.id}`} className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{tenant.name}</span>
                            <span className={`text-sm ${tenant.growth >= 0 ? "text-green-500" : "text-red-500"}`}>
                              {tenant.growth}%
                            </span>
                          </div>
                          <Progress value={tenant.growth > 0 ? tenant.growth * 5 : 0} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Store Growth by Tenant</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      {tenants.map((tenant, index) => {
                        // Simulate different store growth rates
                        const storeGrowth = tenant.growth * (0.8 + Math.random() * 0.4)
                        return (
                          <div key={`store-${tenant.id}`} className="space-y-1">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">{tenant.name}</span>
                              <span className={`text-sm ${storeGrowth >= 0 ? "text-green-500" : "text-red-500"}`}>
                                {storeGrowth.toFixed(1)}%
                              </span>
                            </div>
                            <Progress value={storeGrowth > 0 ? storeGrowth * 5 : 0} className="h-2" />
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Revenue Growth by Tenant</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      {tenants.map((tenant, index) => {
                        // Simulate different revenue growth rates
                        const revenueGrowth = tenant.growth * (1.1 + Math.random() * 0.3)
                        return (
                          <div key={`revenue-${tenant.id}`} className="space-y-1">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">{tenant.name}</span>
                              <span className={`text-sm ${revenueGrowth >= 0 ? "text-green-500" : "text-red-500"}`}>
                                {revenueGrowth.toFixed(1)}%
                              </span>
                            </div>
                            <Progress value={revenueGrowth > 0 ? revenueGrowth * 4 : 0} className="h-2" />
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Subscription Tier Changes</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      {tenants.map((tenant, index) => {
                        // Simulate subscription changes
                        const isUpgrade = Math.random() > 0.3
                        const changePercent = (Math.random() * 15).toFixed(1)
                        return (
                          <div key={`subscription-${tenant.id}`} className="space-y-1">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">{tenant.name}</span>
                              <span className={`text-sm ${isUpgrade ? "text-green-500" : "text-red-500"}`}>
                                {isUpgrade ? "+" : "-"}
                                {changePercent}%
                              </span>
                            </div>
                            <Progress value={isUpgrade ? Number.parseFloat(changePercent) * 5 : 0} className="h-2" />
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                <Download className="mr-2 h-4 w-4" />
                Export Growth Report
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="health" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Tenant Health Indicators</CardTitle>
                  <CardDescription>Monitor the overall health of your tenants</CardDescription>
                </div>
                <Button variant="outline">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh Data
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[200px]">Tenant</TableHead>
                    <TableHead>User Growth</TableHead>
                    <TableHead>Store Retention</TableHead>
                    <TableHead>Revenue Growth</TableHead>
                    <TableHead>Feature Adoption</TableHead>
                    <TableHead>Support Tickets</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {healthIndicators.map((indicator) => (
                    <TableRow key={indicator.tenant}>
                      <TableCell className="font-medium">{indicator.tenant}</TableCell>
                      <TableCell>
                        <div
                          className={`flex items-center ${indicator.userGrowth >= 0 ? "text-green-500" : "text-red-500"}`}
                        >
                          {indicator.userGrowth >= 0 ? (
                            <ChevronUp className="mr-1 h-4 w-4" />
                          ) : (
                            <ChevronDown className="mr-1 h-4 w-4" />
                          )}
                          <span>{Math.abs(indicator.userGrowth)}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span
                            className={
                              indicator.storeRetention >= 95
                                ? "text-green-500"
                                : indicator.storeRetention >= 90
                                  ? "text-yellow-500"
                                  : "text-red-500"
                            }
                          >
                            {indicator.storeRetention}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div
                          className={`flex items-center ${indicator.revenueGrowth >= 0 ? "text-green-500" : "text-red-500"}`}
                        >
                          {indicator.revenueGrowth >= 0 ? (
                            <ChevronUp className="mr-1 h-4 w-4" />
                          ) : (
                            <ChevronDown className="mr-1 h-4 w-4" />
                          )}
                          <span>{Math.abs(indicator.revenueGrowth)}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span
                            className={
                              indicator.featureAdoption >= 80
                                ? "text-green-500"
                                : indicator.featureAdoption >= 70
                                  ? "text-yellow-500"
                                  : "text-red-500"
                            }
                          >
                            {indicator.featureAdoption}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span
                            className={
                              indicator.supportTickets <= 10
                                ? "text-green-500"
                                : indicator.supportTickets <= 20
                                  ? "text-yellow-500"
                                  : "text-red-500"
                            }
                          >
                            {indicator.supportTickets}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <Share2 className="mr-2 h-4 w-4" />
                Share Report
              </Button>
              <Button>
                <Download className="mr-2 h-4 w-4" />
                Export Health Report
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Tenant Activity Reports</CardTitle>
                  <CardDescription>Track significant tenant activities</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Filter by impact" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Activities</SelectItem>
                      <SelectItem value="positive">Positive Impact</SelectItem>
                      <SelectItem value="negative">Negative Impact</SelectItem>
                      <SelectItem value="neutral">Neutral Impact</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="sm">
                    <Calendar className="mr-2 h-4 w-4" />
                    Date Range
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[200px]">Tenant</TableHead>
                    <TableHead>Activity</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Impact</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tenantActivity.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell className="font-medium">{activity.tenant}</TableCell>
                      <TableCell>{activity.action}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-xs">{formatDate(activity.date)}</span>
                          <span className="text-xs text-muted-foreground">{formatTime(activity.date)}</span>
                        </div>
                      </TableCell>
                      <TableCell>{getImpactBadge(activity.impact)}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">Showing 5 of 127 activities</div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  Previous
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Subscription Analytics</CardTitle>
            <CardDescription>Tenant subscription distribution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center justify-center">
                <div className="h-40 w-40 rounded-full border-8 border-primary/20 relative">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-2xl font-bold">127</div>
                      <div className="text-xs text-muted-foreground">Total Tenants</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full bg-blue-500 mr-2"></div>
                      <span className="text-sm">Basic</span>
                    </div>
                    <span className="text-sm font-medium">42 (33%)</span>
                  </div>
                  <Progress value={33} className="h-2" />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                      <span className="text-sm">Standard</span>
                    </div>
                    <span className="text-sm font-medium">53 (42%)</span>
                  </div>
                  <Progress value={42} className="h-2" />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full bg-purple-500 mr-2"></div>
                      <span className="text-sm">Premium</span>
                    </div>
                    <span className="text-sm font-medium">24 (19%)</span>
                  </div>
                  <Progress value={19} className="h-2" />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full bg-yellow-500 mr-2"></div>
                      <span className="text-sm">Enterprise</span>
                    </div>
                    <span className="text-sm font-medium">8 (6%)</span>
                  </div>
                  <Progress value={6} className="h-2" />
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full" variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export Subscription Data
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tenant Onboarding Status</CardTitle>
            <CardDescription>Track tenant setup progress</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div className="space-y-1">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
                    <Building className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-medium">New Tenants</h3>
                  <div className="text-2xl font-bold">12</div>
                  <p className="text-xs text-muted-foreground">Last 30 days</p>
                </div>

                <div className="space-y-1">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
                    <Users className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-medium">Pending Setup</h3>
                  <div className="text-2xl font-bold">5</div>
                  <p className="text-xs text-muted-foreground">Require attention</p>
                </div>

                <div className="space-y-1">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
                    <ShoppingCart className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-medium">Store Creation</h3>
                  <div className="text-2xl font-bold">87%</div>
                  <p className="text-xs text-muted-foreground">Completion rate</p>
                </div>

                <div className="space-y-1">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
                    <CreditCard className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-medium">Billing Setup</h3>
                  <div className="text-2xl font-bold">92%</div>
                  <p className="text-xs text-muted-foreground">Completion rate</p>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Recent Onboarding</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Outdoor Adventure Market</span>
                    <Badge>In Progress</Badge>
                  </div>
                  <Progress value={65} className="h-2" />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Started: May 8, 2023</span>
                    <span>65% Complete</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Gourmet Food Collective</span>
                    <Badge>In Progress</Badge>
                  </div>
                  <Progress value={40} className="h-2" />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Started: May 10, 2023</span>
                    <span>40% Complete</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full">
              <TrendingUp className="mr-2 h-4 w-4" />
              View All Onboarding
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
