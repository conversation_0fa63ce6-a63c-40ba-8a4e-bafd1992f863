"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import {
  ArrowLeft,
  Plug,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Plus,
  ExternalLink,
  Search,
  ShoppingCart,
  CreditCard,
  BarChart,
  Mail,
  MessageSquare,
  Share2,
  Package,
  Truck,
  Settings,
  AlertCircle,
  Info
} from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertD<PERSON>ogHeader,
  <PERSON>ertDialog<PERSON>itle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function IntegrationsPage() {
  const [activeTab, setActiveTab] = useState("marketplace")
  const [searchQuery, setSearchQuery] = useState("")

  // Dummy data untuk integrasi yang terpasang
  const installedIntegrations = [
    {
      id: "int-001",
      name: "PaymentGateway",
      description: "Pemrosesan pembayaran online dengan berbagai metode",
      category: "payment",
      status: "active",
      icon: "/integrations/payment-gateway.svg",
      installedAt: "2024-01-15T10:30:00",
      lastUsed: "2024-05-20T14:20:00"
    },
    {
      id: "int-002",
      name: "ShippingProvider",
      description: "Layanan pengiriman dan pelacakan paket",
      category: "shipping",
      status: "active",
      icon: "/integrations/shipping-provider.svg",
      installedAt: "2024-02-18T09:45:00",
      lastUsed: "2024-05-19T11:20:00"
    },
    {
      id: "int-003",
      name: "SocialMedia",
      description: "Integrasi dengan platform media sosial",
      category: "marketing",
      status: "inactive",
      icon: "/integrations/social-media.svg",
      installedAt: "2024-03-10T11:20:00",
      lastUsed: "2024-04-15T08:30:00"
    }
  ]

  // Dummy data untuk integrasi yang tersedia di marketplace
  const marketplaceIntegrations = [
    {
      id: "market-001",
      name: "GoogleAnalytics",
      description: "Pantau lalu lintas dan konversi toko online Anda",
      category: "analytics",
      icon: "/integrations/google-analytics.svg",
      price: "Gratis",
      rating: 4.8,
      reviewCount: 1250,
      isPopular: true
    },
    {
      id: "market-002",
      name: "FacebookAds",
      description: "Promosikan produk Anda melalui iklan Facebook",
      category: "marketing",
      icon: "/integrations/facebook-ads.svg",
      price: "Berbayar",
      rating: 4.6,
      reviewCount: 980,
      isPopular: true
    },
    {
      id: "market-003",
      name: "LiveChat",
      description: "Layanan obrolan langsung untuk dukungan pelanggan",
      category: "customer-support",
      icon: "/integrations/live-chat.svg",
      price: "Mulai dari Rp 200.000/bulan",
      rating: 4.7,
      reviewCount: 820,
      isPopular: true
    },
    {
      id: "market-004",
      name: "EmailMarketing",
      description: "Otomatisasi email dan kampanye pemasaran",
      category: "marketing",
      icon: "/integrations/email-marketing.svg",
      price: "Mulai dari Rp 150.000/bulan",
      rating: 4.5,
      reviewCount: 750,
      isPopular: false
    },
    {
      id: "market-005",
      name: "Marketplace",
      description: "Integrasi dengan marketplace populer",
      category: "sales-channel",
      icon: "/integrations/marketplace.svg",
      price: "Berbayar",
      rating: 4.4,
      reviewCount: 620,
      isPopular: false
    },
    {
      id: "market-006",
      name: "InventoryManagement",
      description: "Kelola stok dan inventaris produk Anda",
      category: "inventory",
      icon: "/integrations/inventory.svg",
      price: "Mulai dari Rp 300.000/bulan",
      rating: 4.3,
      reviewCount: 550,
      isPopular: false
    }
  ]

  // Dummy data untuk kategori integrasi
  const integrationCategories = [
    { id: "all", name: "Semua", icon: Plug, count: marketplaceIntegrations.length },
    { id: "payment", name: "Pembayaran", icon: CreditCard, count: 12 },
    { id: "shipping", name: "Pengiriman", icon: Truck, count: 8 },
    { id: "marketing", name: "Pemasaran", icon: BarChart, count: 15 },
    { id: "analytics", name: "Analitik", icon: BarChart, count: 6 },
    { id: "customer-support", name: "Dukungan Pelanggan", icon: MessageSquare, count: 7 },
    { id: "sales-channel", name: "Saluran Penjualan", icon: ShoppingCart, count: 9 },
    { id: "inventory", name: "Inventaris", icon: Package, count: 5 }
  ]

  // Dummy data untuk webhooks
  const webhooks = [
    {
      id: "webhook-001",
      name: "Notifikasi Pesanan Baru",
      endpoint: "https://api.example.com/webhooks/orders",
      events: ["order.created"],
      status: "active",
      createdAt: "2024-03-15T10:30:00",
      lastTriggered: "2024-05-20T14:20:00",
      failureCount: 0
    },
    {
      id: "webhook-002",
      name: "Update Stok Produk",
      endpoint: "https://inventory.example.com/update",
      events: ["product.updated", "inventory.changed"],
      status: "active",
      createdAt: "2024-04-10T09:45:00",
      lastTriggered: "2024-05-19T11:20:00",
      failureCount: 0
    },
    {
      id: "webhook-003",
      name: "Integrasi CRM",
      endpoint: "https://crm.example.com/webhook",
      events: ["customer.created", "customer.updated"],
      status: "inactive",
      createdAt: "2024-02-20T11:20:00",
      lastTriggered: "2024-04-15T08:30:00",
      failureCount: 3
    }
  ]

  // Format tanggal
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  // Status badge berdasarkan status integrasi
  function getIntegrationStatusBadge(status: string) {
    switch (status) {
      case "active":
        return <Badge variant="outline" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Aktif</Badge>
      case "inactive":
        return <Badge variant="outline" className="bg-gray-100 text-gray-800"><XCircle className="h-3 w-3 mr-1" />Tidak Aktif</Badge>
      case "pending":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Menunggu</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Filter integrasi berdasarkan pencarian
  const filteredMarketplace = marketplaceIntegrations.filter(integration => 
    integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    integration.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    integration.category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/settings">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Pengaturan Integrasi</h1>
            <p className="text-muted-foreground">
              Hubungkan toko online Anda dengan layanan dan aplikasi pihak ketiga
            </p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 md:w-auto">
          <TabsTrigger value="marketplace">Marketplace</TabsTrigger>
          <TabsTrigger value="installed">Terpasang</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
        </TabsList>

        {/* Marketplace Tab */}
        <TabsContent value="marketplace" className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Cari integrasi..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>Kategori</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {integrationCategories.map((category) => (
                  <Button 
                    key={category.id} 
                    variant={category.id === "all" ? "default" : "ghost"} 
                    className="w-full justify-start"
                  >
                    <category.icon className="h-4 w-4 mr-2" />
                    {category.name}
                    <Badge variant="secondary" className="ml-auto">{category.count}</Badge>
                  </Button>
                ))}
              </CardContent>
            </Card>

            <div className="md:col-span-3 space-y-6">
              <h2 className="text-xl font-semibold">Integrasi Populer</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredMarketplace
                  .filter(integration => integration.isPopular)
                  .map((integration) => (
                    <Card key={integration.id} className="overflow-hidden">
                      <CardHeader className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 rounded-md bg-primary/10 flex items-center justify-center">
                            <Plug className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <CardTitle className="text-base">{integration.name}</CardTitle>
                            <p className="text-xs text-muted-foreground">{integration.price}</p>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <p className="text-sm text-muted-foreground mb-3">
                          {integration.description}
                        </p>
                        <div className="flex items-center gap-1 text-amber-500 text-sm mb-4">
                          <span>★</span>
                          <span className="font-medium">{integration.rating}</span>
                          <span className="text-muted-foreground">({integration.reviewCount})</span>
                        </div>
                      </CardContent>
                      <CardFooter className="p-4 pt-0 flex justify-between">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`#`}>
                            <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                            Detail
                          </Link>
                        </Button>
                        <Button size="sm">
                          <Plus className="h-3.5 w-3.5 mr-1.5" />
                          Pasang
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
              </div>

              <h2 className="text-xl font-semibold">Semua Integrasi</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredMarketplace
                  .filter(integration => !integration.isPopular)
                  .map((integration) => (
                    <Card key={integration.id} className="overflow-hidden">
                      <CardHeader className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 rounded-md bg-primary/10 flex items-center justify-center">
                            <Plug className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <CardTitle className="text-base">{integration.name}</CardTitle>
                            <p className="text-xs text-muted-foreground">{integration.price}</p>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <p className="text-sm text-muted-foreground mb-3">
                          {integration.description}
                        </p>
                        <div className="flex items-center gap-1 text-amber-500 text-sm mb-4">
                          <span>★</span>
                          <span className="font-medium">{integration.rating}</span>
                          <span className="text-muted-foreground">({integration.reviewCount})</span>
                        </div>
                      </CardContent>
                      <CardFooter className="p-4 pt-0 flex justify-between">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`#`}>
                            <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                            Detail
                          </Link>
                        </Button>
                        <Button size="sm">
                          <Plus className="h-3.5 w-3.5 mr-1.5" />
                          Pasang
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Installed Tab */}
        <TabsContent value="installed" className="space-y-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Pengelolaan Integrasi</AlertTitle>
            <AlertDescription>
              Setiap integrasi dapat memiliki pengaturan dan izin yang berbeda. Pastikan untuk meninjau izin dan keamanan setiap integrasi yang dipasang.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            {installedIntegrations.map((integration) => (
              <Card key={integration.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row justify-between gap-6">
                    <div className="flex items-start gap-4">
                      <div className="h-12 w-12 rounded-md bg-primary/10 flex items-center justify-center">
                        <Plug className="h-6 w-6 text-primary" />
                      </div>
                      <div className="space-y-1">
                        <h3 className="font-medium text-lg">{integration.name}</h3>
                        <p className="text-sm text-muted-foreground">{integration.description}</p>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                          <span>Terpasang pada {formatDate(integration.installedAt)}</span>
                          <span>•</span>
                          <span>Terakhir digunakan {formatDate(integration.lastUsed)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-start md:items-end gap-2">
                      <div className="flex items-center gap-2">
                        {getIntegrationStatusBadge(integration.status)}
                        <Badge variant="outline">{integration.category}</Badge>
                      </div>
                      <div className="flex items-center gap-2 mt-auto">
                        <Button variant="outline" size="sm">
                          <Settings className="h-3.5 w-3.5 mr-1.5" />
                          Konfigurasi
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="sm" className="text-destructive">
                              <XCircle className="h-3.5 w-3.5 mr-1.5" />
                              Hapus
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Apakah Anda yakin?</AlertDialogTitle>
                              <AlertDialogDescription>
                                Menghapus integrasi ini akan menghentikan semua fungsi terkait dan menghapus data konfigurasinya.
                                Tindakan ini tidak dapat dibatalkan.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Batal</AlertDialogCancel>
                              <AlertDialogAction className="bg-destructive text-destructive-foreground">
                                Hapus Integrasi
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Webhooks Tab */}
        <TabsContent value="webhooks" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Webhooks</h2>
              <p className="text-sm text-muted-foreground">
                Kelola notifikasi berbasis webhook untuk peristiwa di toko Anda
              </p>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Buat Webhook
            </Button>
          </div>

          <div className="space-y-4">
            {webhooks.map((webhook) => (
              <Card key={webhook.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row justify-between gap-6">
                    <div className="space-y-2">
                      <h3 className="font-medium text-lg">{webhook.name}</h3>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">URL Endpoint</Badge>
                        <code className="text-xs bg-muted px-2 py-1 rounded">{webhook.endpoint}</code>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-2">
                        <span className="text-sm font-medium">Peristiwa:</span>
                        {webhook.events.map((event, index) => (
                          <Badge key={index} variant="secondary">{event}</Badge>
                        ))}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                        <span>Dibuat pada {formatDate(webhook.createdAt)}</span>
                        {webhook.lastTriggered && (
                          <>
                            <span>•</span>
                            <span>Terakhir dipicu {formatDate(webhook.lastTriggered)}</span>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="flex flex-col items-start md:items-end gap-3">
                      <div className="flex items-center gap-2">
                        {webhook.status === "active" ? (
                          <Badge variant="outline" className="bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />Aktif
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-gray-100 text-gray-800">
                            <XCircle className="h-3 w-3 mr-1" />Tidak Aktif
                          </Badge>
                        )}
                        {webhook.failureCount > 0 && (
                          <Badge variant="destructive">
                            <AlertCircle className="h-3 w-3 mr-1" />{webhook.failureCount} Kegagalan
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2 mt-auto">
                        <Button variant="outline" size="sm">
                          <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                          Uji Webhook
                        </Button>
                        {webhook.status === "active" ? (
                          <Button variant="outline" size="sm">
                            <XCircle className="h-3.5 w-3.5 mr-1.5" />
                            Nonaktifkan
                          </Button>
                        ) : (
                          <Button variant="outline" size="sm">
                            <CheckCircle className="h-3.5 w-3.5 mr-1.5" />
                            Aktifkan
                          </Button>
                        )}
                        <Button variant="outline" size="sm" className="text-destructive">
                          Hapus
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Tentang Webhooks</CardTitle>
              <CardDescription>
                Webhooks memungkinkan aplikasi Anda untuk menerima notifikasi secara real-time
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm">
                Webhook adalah cara aplikasi untuk menyediakan notifikasi real-time kepada aplikasi lain ketika peristiwa tertentu terjadi. 
                Saat peristiwa terjadi, kami mengirimkan permintaan HTTP POST ke URL endpoint yang Anda tentukan.
              </p>
              <div className="bg-muted p-4 rounded-md">
                <h4 className="text-sm font-medium mb-2">Contoh payload webhook:</h4>
                <pre className="text-xs overflow-auto">
                  {JSON.stringify({
                    event: "order.created",
                    created_at: "2024-05-20T14:30:00Z",
                    data: {
                      order_id: "ORD-12345",
                      customer_id: "CUST-6789",
                      amount: 850000,
                      currency: "IDR",
                      status: "pending"
                    }
                  }, null, 2)}
                </pre>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" asChild>
                <Link href="#" target="_blank">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Dokumentasi Webhook
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 