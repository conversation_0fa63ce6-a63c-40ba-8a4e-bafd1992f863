"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { ArrowUpIcon, ArrowDownIcon, DollarSign, ShoppingCart, Users, Store, TrendingUp, Target } from "lucide-react"
import { cn } from "@/lib/utils"

interface KPICardProps {
  title: string
  value: string | number
  change?: number
  changeType?: "increase" | "decrease"
  icon: React.ElementType
  description?: string
  format?: "currency" | "number" | "percentage"
}

function KPICard({ title, value, change, changeType, icon: Icon, description, format = "number" }: KPICardProps) {
  const formatValue = (val: string | number) => {
    if (format === "currency") {
      return new Intl.NumberFormat("id-ID", {
        style: "currency",
        currency: "IDR",
        minimumFractionDigits: 0,
      }).format(Number(val))
    }
    if (format === "percentage") {
      return `${val}%`
    }
    return new Intl.NumberFormat("id-ID").format(Number(val))
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatValue(value)}</div>
        {change !== undefined && (
          <div className="flex items-center text-xs text-muted-foreground">
            {changeType === "increase" ? (
              <ArrowUpIcon className="mr-1 h-3 w-3 text-green-500" />
            ) : (
              <ArrowDownIcon className="mr-1 h-3 w-3 text-red-500" />
            )}
            <span className={cn(
              changeType === "increase" ? "text-green-500" : "text-red-500"
            )}>
              {Math.abs(change)}%
            </span>
            <span className="ml-1">dari bulan lalu</span>
          </div>
        )}
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
      </CardContent>
    </Card>
  )
}

export function KPICards() {
  const kpiData = [
    {
      title: "Total Revenue",
      value: 45230000,
      change: 20.1,
      changeType: "increase" as const,
      icon: DollarSign,
      format: "currency" as const,
      description: "Revenue dari semua transaksi"
    },
    {
      title: "Total Orders",
      value: 2350,
      change: 15.3,
      changeType: "increase" as const,
      icon: ShoppingCart,
      description: "Jumlah pesanan yang masuk"
    },
    {
      title: "Active Customers",
      value: 12543,
      change: 8.2,
      changeType: "increase" as const,
      icon: Users,
      description: "Customer yang aktif bulan ini"
    },
    {
      title: "Active Stores",
      value: 89,
      change: 5.4,
      changeType: "increase" as const,
      icon: Store,
      description: "Toko yang aktif berjualan"
    },
    {
      title: "GMV (Gross Merchandise Value)",
      value: 125400000,
      change: 12.5,
      changeType: "increase" as const,
      icon: TrendingUp,
      format: "currency" as const,
      description: "Total nilai barang yang dijual"
    },
    {
      title: "Conversion Rate",
      value: 3.24,
      change: -0.5,
      changeType: "decrease" as const,
      icon: Target,
      format: "percentage" as const,
      description: "Persentase visitor yang melakukan pembelian"
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {kpiData.map((kpi, index) => (
        <KPICard key={index} {...kpi} />
      ))}
    </div>
  )
} 