import { NextRequest, NextResponse } from 'next/server';
import { productModerationService } from '@/lib/services/product-moderation';

// GET - Mendapatkan semua product moderations atau difilter berdasarkan query
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    
    const filters = {
      status: searchParams.get('status') || undefined,
      priority: searchParams.get('priority') || undefined,
      reviewer_id: searchParams.get('reviewer_id') || undefined,
      product_id: searchParams.get('product_id') || undefined,
      date_from: searchParams.get('date_from') || undefined,
      date_to: searchParams.get('date_to') || undefined,
      has_flags: searchParams.get('has_flags') === 'true' || undefined,
    };
    
    const moderations = await productModerationService.getModerations(filters);
    
    return NextResponse.json(moderations);
  } catch (error) {
    console.error('Error fetching moderations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch moderations' },
      { status: 500 }
    );
  }
}

// POST - Membuat product moderation baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newModeration = await productModerationService.createModeration(body);
    
    return NextResponse.json(newModeration, { status: 201 });
  } catch (error) {
    console.error('Error creating moderation:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to create moderation';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// PUT - Bulk operations
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ids, reviewer_id, notes, reason, required_changes } = body;
    
    if (!action || !ids || !Array.isArray(ids)) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }
    
    let results;
    
    switch (action) {
      case 'bulk_approve':
        if (!reviewer_id) {
          return NextResponse.json(
            { error: 'reviewer_id is required for bulk approve' },
            { status: 400 }
          );
        }
        results = await productModerationService.bulkApprove(ids, reviewer_id, notes);
        break;
        
      case 'bulk_reject':
        if (!reviewer_id || !reason) {
          return NextResponse.json(
            { error: 'reviewer_id and reason are required for bulk reject' },
            { status: 400 }
          );
        }
        results = await productModerationService.bulkReject(ids, reviewer_id, reason);
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
    
    return NextResponse.json({
      message: `Bulk ${action} completed`,
      processed: results.length,
      results
    });
    
  } catch (error) {
    console.error('Error in bulk operation:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}
