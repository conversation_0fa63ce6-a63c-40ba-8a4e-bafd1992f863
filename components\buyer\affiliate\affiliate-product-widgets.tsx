"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Copy, Code, Settings } from "lucide-react"
import Image from "next/image"

export function AffiliateProductWidgets() {
  const [widgetType, setWidgetType] = useState<"carousel" | "featured" | "new" | "sale">("carousel")
  const [widgetCategory, setWidgetCategory] = useState<"all" | "fashion" | "electronics" | "home" | "beauty">("all")
  const [widgetSize, setWidgetSize] = useState<"small" | "medium" | "large">("medium")

  // Data dummy untuk preview widget
  const widgetPreviews = {
    carousel: {
      title: "Product Carousel Widget",
      description: "<PERSON><PERSON><PERSON><PERSON> beberapa produk dalam format carousel yang menarik",
      imageUrl: "/placeholder.svg?height=300&width=600&query=Product%20Carousel%20Widget",
      htmlCode:
        '<div class="sellzio-widget" data-type="carousel" data-category="all" data-size="medium" data-ref="USER123"></div><script src="https://sellzio.com/widgets/loader.js"></script>',
    },
    featured: {
      title: "Featured Product Widget",
      description: "Tampilkan produk unggulan dengan tampilan yang menonjol",
      imageUrl: "/placeholder.svg?height=300&width=600&query=Featured%20Product%20Widget",
      htmlCode:
        '<div class="sellzio-widget" data-type="featured" data-category="all" data-size="medium" data-ref="USER123"></div><script src="https://sellzio.com/widgets/loader.js"></script>',
    },
    new: {
      title: "New Arrivals Widget",
      description: "Tampilkan produk terbaru untuk menarik perhatian pengunjung",
      imageUrl: "/placeholder.svg?height=300&width=600&query=New%20Arrivals%20Widget",
      htmlCode:
        '<div class="sellzio-widget" data-type="new" data-category="all" data-size="medium" data-ref="USER123"></div><script src="https://sellzio.com/widgets/loader.js"></script>',
    },
    sale: {
      title: "Sale Items Widget",
      description: "Tampilkan produk diskon untuk mendorong pembelian",
      imageUrl: "/placeholder.svg?height=300&width=600&query=Sale%20Items%20Widget",
      htmlCode:
        '<div class="sellzio-widget" data-type="sale" data-category="all" data-size="medium" data-ref="USER123"></div><script src="https://sellzio.com/widgets/loader.js"></script>',
    },
  }

  const currentWidget = widgetPreviews[widgetType]

  // Generate HTML code berdasarkan pilihan user
  const generateHtmlCode = () => {
    return `<div class="sellzio-widget" data-type="${widgetType}" data-category="${widgetCategory}" data-size="${widgetSize}" data-ref="USER123"></div><script src="https://sellzio.com/widgets/loader.js"></script>`
  }

  const copyHtmlCode = () => {
    navigator.clipboard.writeText(generateHtmlCode())
    // Bisa tambahkan toast notification di sini
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-3">
        <div className="space-y-2">
          <Label htmlFor="widget-type">Tipe Widget</Label>
          <Select value={widgetType} onValueChange={setWidgetType as any}>
            <SelectTrigger id="widget-type">
              <SelectValue placeholder="Pilih tipe widget" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="carousel">Product Carousel</SelectItem>
              <SelectItem value="featured">Featured Product</SelectItem>
              <SelectItem value="new">New Arrivals</SelectItem>
              <SelectItem value="sale">Sale Items</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="widget-category">Kategori</Label>
          <Select value={widgetCategory} onValueChange={setWidgetCategory as any}>
            <SelectTrigger id="widget-category">
              <SelectValue placeholder="Pilih kategori" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Kategori</SelectItem>
              <SelectItem value="fashion">Fashion</SelectItem>
              <SelectItem value="electronics">Elektronik</SelectItem>
              <SelectItem value="home">Rumah & Dapur</SelectItem>
              <SelectItem value="beauty">Kecantikan</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="widget-size">Ukuran Widget</Label>
          <Select value={widgetSize} onValueChange={setWidgetSize as any}>
            <SelectTrigger id="widget-size">
              <SelectValue placeholder="Pilih ukuran" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="small">Kecil</SelectItem>
              <SelectItem value="medium">Sedang</SelectItem>
              <SelectItem value="large">Besar</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">{currentWidget.title}</h3>
              <p className="text-sm text-muted-foreground">{currentWidget.description}</p>
            </div>

            <div className="relative overflow-hidden rounded-md border">
              <Image
                src={currentWidget.imageUrl || "/placeholder.svg"}
                alt={currentWidget.title}
                width={600}
                height={300}
                className="w-full object-cover"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="html-code">HTML Code</Label>
              <div className="relative">
                <Input id="html-code" value={generateHtmlCode()} readOnly className="font-mono text-xs pr-24" />
                <Button size="sm" className="absolute right-1 top-1" onClick={copyHtmlCode}>
                  <Copy className="mr-2 h-4 w-4" />
                  Copy
                </Button>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button variant="outline" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Kustomisasi Lanjutan
              </Button>
              <Button variant="outline" size="sm">
                <Code className="mr-2 h-4 w-4" />
                Preview Live
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="rounded-md border p-4">
        <h3 className="font-medium">Cara Menggunakan Widget</h3>
        <ol className="mt-2 space-y-2 text-sm">
          <li>1. Pilih tipe, kategori, dan ukuran widget yang diinginkan</li>
          <li>2. Salin kode HTML yang dihasilkan</li>
          <li>3. Tempelkan kode tersebut ke dalam HTML website Anda</li>
          <li>4. Widget akan otomatis dimuat dengan produk-produk terbaru</li>
          <li>5. Komisi akan dihitung otomatis untuk setiap pembelian melalui widget</li>
        </ol>
      </div>
    </div>
  )
}
