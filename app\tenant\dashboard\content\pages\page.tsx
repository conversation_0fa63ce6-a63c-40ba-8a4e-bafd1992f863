"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowLeft,
  Search,
  Plus,
  FileText,
  Eye,
  PencilIcon,
  Trash,
  MoreHorizontal,
  Copy,
  Filter,
  ChevronDown,
  Globe,
  Calendar,
  Clock,
  LayoutGrid,
  LayoutList,
  Settings,
  CheckCircle2,
  AlertCircle,
  XCircle
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk halaman
const pages = [
  {
    id: "page-001",
    title: "Beranda",
    slug: "home",
    status: "published",
    template: "homepage",
    author: "Admin",
    createdAt: "2024-01-01T10:00:00",
    updatedAt: "2024-01-15T11:30:00",
    publishedAt: "2024-01-15T12:00:00",
    views: 12500,
    seo: {
      title: "Marketplace Terbaik untuk Belanja Online",
      description: "Temukan berbagai produk berkualitas dengan harga terbaik di marketplace kami.",
      keywords: "marketplace, belanja online, produk, diskon"
    }
  },
  {
    id: "page-002",
    title: "Tentang Kami",
    slug: "about-us",
    status: "published",
    template: "standard",
    author: "Admin",
    createdAt: "2024-01-02T09:15:00",
    updatedAt: "2024-01-10T14:20:00",
    publishedAt: "2024-01-10T15:00:00",
    views: 3200,
    seo: {
      title: "Tentang Kami | Sejarah dan Visi Misi",
      description: "Kenali lebih dalam tentang marketplace kami, sejarah, dan visi misi perusahaan.",
      keywords: "tentang kami, sejarah, visi misi, marketplace"
    }
  },
  {
    id: "page-003",
    title: "Kebijakan Privasi",
    slug: "privacy-policy",
    status: "published",
    template: "legal",
    author: "Admin",
    createdAt: "2024-01-03T11:20:00",
    updatedAt: "2024-01-20T09:45:00",
    publishedAt: "2024-01-20T10:00:00",
    views: 1850,
    seo: {
      title: "Kebijakan Privasi | Perlindungan Data Pengguna",
      description: "Informasi tentang bagaimana kami melindungi data dan privasi pengguna di marketplace.",
      keywords: "kebijakan privasi, data pengguna, perlindungan data"
    }
  },
  {
    id: "page-004",
    title: "Syarat dan Ketentuan",
    slug: "terms-and-conditions",
    status: "published",
    template: "legal",
    author: "Admin",
    createdAt: "2024-01-03T13:00:00",
    updatedAt: "2024-01-20T10:30:00",
    publishedAt: "2024-01-20T11:00:00",
    views: 1650,
    seo: {
      title: "Syarat dan Ketentuan Layanan",
      description: "Ketentuan penggunaan layanan marketplace kami untuk semua pengguna.",
      keywords: "syarat ketentuan, ketentuan layanan, aturan penggunaan"
    }
  },
  {
    id: "page-005",
    title: "FAQ",
    slug: "faq",
    status: "published",
    template: "faq",
    author: "Admin",
    createdAt: "2024-01-05T09:00:00",
    updatedAt: "2024-01-18T15:40:00",
    publishedAt: "2024-01-18T16:00:00",
    views: 4300,
    seo: {
      title: "FAQ | Pertanyaan yang Sering Diajukan",
      description: "Temukan jawaban untuk pertanyaan yang sering diajukan tentang marketplace kami.",
      keywords: "faq, pertanyaan, jawaban, bantuan, panduan"
    }
  },
  {
    id: "page-006",
    title: "Hubungi Kami",
    slug: "contact-us",
    status: "published",
    template: "contact",
    author: "Admin",
    createdAt: "2024-01-06T10:30:00",
    updatedAt: "2024-01-15T13:20:00",
    publishedAt: "2024-01-15T14:00:00",
    views: 3800,
    seo: {
      title: "Hubungi Kami | Bantuan dan Dukungan",
      description: "Hubungi tim dukungan kami untuk pertanyaan dan bantuan seputar marketplace.",
      keywords: "hubungi kami, kontak, bantuan, dukungan, customer service"
    }
  },
  {
    id: "page-007",
    title: "Halaman Promo Spesial",
    slug: "special-promo",
    status: "draft",
    template: "promo",
    author: "Marketing",
    createdAt: "2024-01-25T09:00:00",
    updatedAt: "2024-01-25T15:30:00",
    publishedAt: null,
    views: 0,
    seo: {
      title: "Promo Spesial Awal Tahun 2024",
      description: "Dapatkan penawaran spesial dan diskon besar di awal tahun 2024.",
      keywords: "promo, diskon, penawaran spesial, sale"
    }
  },
  {
    id: "page-008",
    title: "Partner & Sponsorship",
    slug: "partnership",
    status: "scheduled",
    template: "standard",
    author: "Partnership",
    createdAt: "2024-01-26T11:15:00",
    updatedAt: "2024-01-27T10:45:00",
    publishedAt: "2024-02-01T08:00:00",
    views: 0,
    seo: {
      title: "Program Partnership dan Sponsorship",
      description: "Informasi tentang program kemitraan dan sponsorship dengan marketplace kami.",
      keywords: "partnership, kemitraan, sponsorship, kolaborasi"
    }
  }
]

// Fungsi untuk badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "published":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Dipublikasikan</Badge>
    case "draft":
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>
    case "scheduled":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">Terjadwal</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk format tanggal
function formatDate(dateString: string | null) {
  if (!dateString) return "-"
  
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Fungsi untuk format views
function formatViews(views: number) {
  return views.toLocaleString('id-ID')
}

export default function ContentPagesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("list")
  
  // Filter halaman
  const filteredPages = pages.filter(page => {
    const matchesSearch = 
      page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.template.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || page.status === statusFilter
    
    return matchesSearch && matchesStatus
  })
  
  // Statistik
  const stats = {
    total: pages.length,
    published: pages.filter(p => p.status === "published").length,
    draft: pages.filter(p => p.status === "draft").length,
    scheduled: pages.filter(p => p.status === "scheduled").length,
    totalViews: pages.reduce((sum, p) => sum + p.views, 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/content">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Halaman</h1>
            <p className="text-muted-foreground">
              Kelola konten halaman statis website
            </p>
          </div>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Buat Halaman Baru
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Halaman</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dipublikasikan</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.published}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Draft</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.draft}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatViews(stats.totalViews)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search & Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari halaman..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border rounded-md bg-background"
              >
                <option value="all">Semua Status</option>
                <option value="published">Dipublikasikan</option>
                <option value="draft">Draft</option>
                <option value="scheduled">Terjadwal</option>
              </select>
              <div className="flex border rounded-md overflow-hidden">
                <Button 
                  variant={viewMode === "list" ? "default" : "ghost"} 
                  size="sm"
                  className="rounded-none"
                  onClick={() => setViewMode("list")}
                >
                  <LayoutList className="h-4 w-4" />
                </Button>
                <Button 
                  variant={viewMode === "grid" ? "default" : "ghost"} 
                  size="sm"
                  className="rounded-none"
                  onClick={() => setViewMode("grid")}
                >
                  <LayoutGrid className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Pages List */}
      {viewMode === "list" ? (
        <div className="space-y-4">
          {filteredPages.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada halaman ditemukan</h3>
                <p className="text-muted-foreground mb-4 text-center">
                  Tidak ada halaman yang cocok dengan filter atau pencarian Anda
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Buat Halaman Baru
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredPages.map((page) => (
              <Card key={page.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row justify-between gap-4">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-semibold text-lg">{page.title}</span>
                          {getStatusBadge(page.status)}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Globe className="h-3.5 w-3.5" />
                          <span>/{page.slug}</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                          <Calendar className="h-3.5 w-3.5" />
                          <span>
                            {page.status === "published" 
                              ? `Dipublikasikan: ${formatDate(page.publishedAt)}`
                              : page.status === "scheduled" 
                                ? `Dijadwalkan: ${formatDate(page.publishedAt)}`
                                : `Terakhir diupdate: ${formatDate(page.updatedAt)}`}
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="bg-purple-100 text-purple-800">
                            {page.template}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1 text-sm">
                          <Eye className="h-4 w-4 text-blue-600" />
                          <span>{formatViews(page.views)} views</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-3 bg-muted/50 rounded-lg text-sm">
                      <p className="text-xs text-muted-foreground mb-1">SEO Metadata:</p>
                      <p className="text-xs font-medium mb-1">Title: {page.seo.title}</p>
                      <p className="text-xs text-muted-foreground">{page.seo.description}</p>
                    </div>
                    
                    <div className="flex gap-2 pt-2 border-t mt-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        Lihat
                      </Button>
                      <Button size="sm" variant="outline">
                        <PencilIcon className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button size="sm" variant="outline">
                        <Settings className="h-4 w-4 mr-2" />
                        SEO
                      </Button>
                      <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                        <Trash className="h-4 w-4 mr-2" />
                        Hapus
                      </Button>
                      <Button size="sm" variant="outline">
                        <Copy className="h-4 w-4 mr-2" />
                        Duplikat
                      </Button>
                      <Button size="sm" variant="ghost">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      ) : (
        // Grid View
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredPages.length === 0 ? (
            <Card className="col-span-full">
              <CardContent className="flex flex-col items-center justify-center py-8">
                <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada halaman ditemukan</h3>
                <p className="text-muted-foreground mb-4 text-center">
                  Tidak ada halaman yang cocok dengan filter atau pencarian Anda
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Buat Halaman Baru
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredPages.map((page) => (
              <Card key={page.id} className="hover:shadow-md transition-shadow overflow-hidden">
                <div className="bg-muted/50 p-4 border-b flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <span className="font-medium">{page.title}</span>
                  </div>
                  {getStatusBadge(page.status)}
                </div>
                <CardContent className="p-4">
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Globe className="h-3.5 w-3.5" />
                      <span>/{page.slug}</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Calendar className="h-3.5 w-3.5" />
                      <span>{formatDate(page.updatedAt)}</span>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <Badge variant="outline" className="bg-purple-100 text-purple-800">
                        {page.template}
                      </Badge>
                      <div className="flex items-center gap-1 text-sm">
                        <Eye className="h-3.5 w-3.5 text-blue-600" />
                        <span>{formatViews(page.views)}</span>
                      </div>
                    </div>
                    <div className="flex gap-2 pt-2 mt-2 border-t">
                      <Button size="sm" variant="outline" className="w-full">
                        <PencilIcon className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button size="sm" variant="ghost">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}
      
      {filteredPages.length > 0 && (
        <div className="flex justify-center mt-4">
          <Button variant="outline" className="mr-2" size="sm">
            Sebelumnya
          </Button>
          <Button variant="outline" size="sm">
            Selanjutnya
          </Button>
        </div>
      )}
    </div>
  )
} 