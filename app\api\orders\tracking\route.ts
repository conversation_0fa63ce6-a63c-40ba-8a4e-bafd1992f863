import { NextRequest, NextResponse } from 'next/server';

// Sample tracking data
const trackingData = {
  "JNE123456789": {
    trackingNumber: "JNE123456789",
    courier: "JNE Express",
    status: "in_transit",
    estimatedDelivery: "2023-06-20",
    origin: "Jakarta",
    destination: "Surabaya",
    timeline: [
      {
        date: "2023-06-15T09:30:00",
        status: "Order dibuat",
        location: "Jakarta",
        description: "Pesanan telah dibuat dan sedang diproses",
      },
      {
        date: "2023-06-16T14:20:00",
        status: "Paket dikemas",
        location: "Jakarta",
        description: "Paket telah dikemas dan siap dikirim",
      },
      {
        date: "2023-06-17T08:15:00",
        status: "Paket dikirim",
        location: "Jakarta Sorting Center",
        description: "Paket telah dikirim dari gudang pusat",
      },
      {
        date: "2023-06-18T19:45:00",
        status: "Dalam perjalanan",
        location: "Semarang",
        description: "Paket sedang dalam perjalanan menuju tujuan",
      },
    ],
  },
  "SC987654321": {
    trackingNumber: "SC987654321",
    courier: "SiCepat",
    status: "delivered",
    estimatedDelivery: "2023-06-18",
    origin: "Bandung",
    destination: "Jakarta",
    timeline: [
      {
        date: "2023-06-14T10:15:00",
        status: "Order dibuat",
        location: "Bandung",
        description: "Pesanan telah dibuat dan sedang diproses",
      },
      {
        date: "2023-06-15T11:30:00",
        status: "Paket dikemas",
        location: "Bandung",
        description: "Paket telah dikemas dan siap dikirim",
      },
      {
        date: "2023-06-16T08:45:00",
        status: "Paket dikirim",
        location: "Bandung Sorting Center",
        description: "Paket telah dikirim dari gudang pusat",
      },
      {
        date: "2023-06-17T14:20:00",
        status: "Dalam perjalanan",
        location: "Bekasi",
        description: "Paket sedang dalam perjalanan menuju tujuan",
      },
      {
        date: "2023-06-18T09:10:00",
        status: "Terkirim",
        location: "Jakarta",
        description: "Paket telah diterima oleh penerima",
      },
    ],
  },
  "JT123456789": {
    trackingNumber: "JT123456789",
    courier: "J&T Express",
    status: "processing",
    estimatedDelivery: "2023-06-25",
    origin: "Surabaya",
    destination: "Makassar",
    timeline: [
      {
        date: "2023-06-19T13:45:00",
        status: "Order dibuat",
        location: "Surabaya",
        description: "Pesanan telah dibuat dan sedang diproses",
      },
      {
        date: "2023-06-20T10:30:00",
        status: "Paket dikemas",
        location: "Surabaya",
        description: "Paket telah dikemas dan siap dikirim",
      },
    ],
  },
};

// GET endpoint untuk tracking pesanan
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const trackingNumber = searchParams.get('tracking_number');
  
  if (!trackingNumber) {
    return NextResponse.json(
      { error: 'Nomor resi diperlukan' },
      { status: 400 }
    );
  }
  
  // Cari data tracking berdasarkan nomor resi
  const tracking = trackingData[trackingNumber as keyof typeof trackingData];
  
  if (!tracking) {
    return NextResponse.json(
      { 
        error: 'Nomor resi tidak ditemukan',
        found: false,
        trackingNumber,
      },
      { status: 404 }
    );
  }
  
  return NextResponse.json({
    found: true,
    ...tracking,
  });
} 