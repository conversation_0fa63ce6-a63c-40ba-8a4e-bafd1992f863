import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface Product {
  id: string;
  sku: string;
  name: string;
  slug: string;
  description?: string;
  short_description?: string;
  price: number;
  compare_price?: number;
  cost_price?: number;
  track_inventory: boolean;
  inventory_quantity: number;
  low_stock_threshold: number;
  weight?: number;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };
  category_id?: string;
  brand_id?: string;
  tags: string[];
  images: string[];
  featured_image?: string;
  gallery: string[];
  variants: any[];
  attributes: Record<string, any>;
  seo_title?: string;
  seo_description?: string;
  seo_keywords: string[];
  status: 'draft' | 'active' | 'inactive' | 'archived';
  visibility: 'visible' | 'hidden' | 'catalog_only';
  featured: boolean;
  digital: boolean;
  downloadable: boolean;
  download_files: string[];
  requires_shipping: boolean;
  tax_status: 'taxable' | 'none';
  tax_class: string;
  meta_data: Record<string, any>;
  created_by?: string;
  updated_by?: string;
  published_at?: string;
  created_at: string;
  updated_at: string;
  // Relations
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  brand?: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface ProductFilters {
  search?: string;
  category_id?: string;
  brand_id?: string;
  status?: string;
  visibility?: string;
  featured?: boolean;
  min_price?: number;
  max_price?: number;
  tags?: string[];
  in_stock?: boolean;
}

export interface ProductCreate {
  sku: string;
  name: string;
  slug: string;
  description?: string;
  short_description?: string;
  price: number;
  compare_price?: number;
  cost_price?: number;
  track_inventory?: boolean;
  inventory_quantity?: number;
  low_stock_threshold?: number;
  weight?: number;
  dimensions?: object;
  category_id?: string;
  brand_id?: string;
  tags?: string[];
  images?: string[];
  featured_image?: string;
  gallery?: string[];
  variants?: any[];
  attributes?: object;
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
  status?: Product['status'];
  visibility?: Product['visibility'];
  featured?: boolean;
  digital?: boolean;
  downloadable?: boolean;
  download_files?: string[];
  requires_shipping?: boolean;
  tax_status?: Product['tax_status'];
  tax_class?: string;
  meta_data?: object;
}

export function useProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch products with optional filters
  const fetchProducts = useCallback(async (filters?: ProductFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      
      if (filters?.search) params.append('search', filters.search);
      if (filters?.category_id) params.append('category_id', filters.category_id);
      if (filters?.brand_id) params.append('brand_id', filters.brand_id);
      if (filters?.status) params.append('status', filters.status);
      if (filters?.visibility) params.append('visibility', filters.visibility);
      if (filters?.featured !== undefined) params.append('featured', filters.featured.toString());
      if (filters?.min_price !== undefined) params.append('min_price', filters.min_price.toString());
      if (filters?.max_price !== undefined) params.append('max_price', filters.max_price.toString());
      if (filters?.tags) params.append('tags', filters.tags.join(','));
      if (filters?.in_stock !== undefined) params.append('in_stock', filters.in_stock.toString());
      
      const response = await fetch(`/api/products?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      
      const data = await response.json();
      setProducts(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Get single product
  const getProduct = useCallback(async (id: string): Promise<Product | null> => {
    try {
      const response = await fetch(`/api/products/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch product');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Create product
  const createProduct = useCallback(async (productData: ProductCreate): Promise<boolean> => {
    try {
      console.log('Mengirim data produk:', productData);
      
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });
      
      const responseData = await response.json();
      console.log('Response dari server:', { status: response.status, data: responseData });
      
      if (!response.ok) {
        throw new Error(responseData.error || responseData.message || 'Gagal membuat produk');
      }
      
      const newProduct = responseData.data || responseData;
      
      // Update local state
      setProducts(prev => [newProduct, ...prev]);
      
      toast.success('Produk berhasil dibuat');
      return true;
    } catch (err) {
      console.error('Error saat membuat produk:', err);
      const errorMessage = err instanceof Error ? err.message : 'Terjadi kesalahan saat membuat produk';
      toast.error(errorMessage);
      return false;
    }
  }, []);

  // Update product
  const updateProduct = useCallback(async (id: string, updates: Partial<ProductCreate>): Promise<boolean> => {
    try {
      console.log('Sending update request for product:', id, updates);

      const response = await fetch(`/api/products/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Update failed with error:', errorData);
        throw new Error(errorData.error || 'Failed to update product');
      }

      const updatedProduct = await response.json();
      console.log('Product updated successfully:', updatedProduct);

      // Update local state
      setProducts(prev =>
        prev.map(product => product.id === id ? updatedProduct : product)
      );

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      console.error('Error updating product:', err);
      throw new Error(errorMessage);
    }
  }, []);

  // Delete product
  const deleteProduct = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/products/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete product');
      }
      
      // Update local state
      setProducts(prev => prev.filter(product => product.id !== id));
      
      toast.success('Produk berhasil dihapus');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Update product status
  const updateProductStatus = useCallback(async (id: string, status: Product['status']): Promise<boolean> => {
    try {
      console.log('Updating product status:', { id, status });

      const response = await fetch(`/api/products/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update_status',
          status,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Status update failed:', errorData);
        throw new Error(errorData.error || 'Failed to update product status');
      }

      const updatedProduct = await response.json();
      console.log('Product status updated successfully:', updatedProduct);

      // Update local state
      setProducts(prev =>
        prev.map(product => product.id === id ? updatedProduct : product)
      );

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      console.error('Error updating product status:', err);
      throw new Error(errorMessage);
    }
  }, []);

  // Toggle featured status
  const toggleFeatured = useCallback(async (id: string): Promise<boolean> => {
    try {
      console.log('Toggling featured status for product:', id);

      const response = await fetch(`/api/products/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'toggle_featured',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Toggle featured failed:', errorData);
        throw new Error(errorData.error || 'Failed to toggle featured status');
      }

      const updatedProduct = await response.json();
      console.log('Featured status toggled successfully:', updatedProduct);

      // Update local state
      setProducts(prev =>
        prev.map(product => product.id === id ? updatedProduct : product)
      );

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      console.error('Error toggling featured status:', err);
      throw new Error(errorMessage);
    }
  }, []);

  // Update inventory
  const updateInventory = useCallback(async (id: string, quantity: number): Promise<boolean> => {
    try {
      const response = await fetch(`/api/products/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update_inventory',
          quantity,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update inventory');
      }
      
      const updatedProduct = await response.json();
      
      // Update local state
      setProducts(prev => 
        prev.map(product => product.id === id ? updatedProduct : product)
      );
      
      toast.success('Stok produk berhasil diperbarui');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return false;
    }
  }, []);

  // Get product stats
  const getProductStats = useCallback(async () => {
    try {
      const response = await fetch('/api/products/stats');
      
      if (!response.ok) {
        throw new Error('Failed to fetch product stats');
      }
      
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${errorMessage}`);
      return null;
    }
  }, []);

  // Refresh products (re-fetch with current filters)
  const refreshProducts = useCallback(async () => {
    await fetchProducts();
  }, [fetchProducts]);

  // Initial fetch on mount
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  return {
    products,
    loading,
    error,
    fetchProducts,
    getProduct,
    createProduct,
    updateProduct,
    deleteProduct,
    updateProductStatus,
    toggleFeatured,
    updateInventory,
    getProductStats,
    refreshProducts,
  };
}
