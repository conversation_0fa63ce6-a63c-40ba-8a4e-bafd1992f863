"use client"

import { useState } from "react"
import { DataTable } from "@/components/ui/data-table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Eye, Mail, UserX, KeyRound } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Mock data for tenant admins
const tenantAdmins = [
  {
    id: "ta1",
    name: "<PERSON>",
    email: "<EMAIL>",
    tenant: "Tech Solutions",
    role: "Tenant Owner",
    lastLogin: "2023-05-09",
    status: "Active",
    permissions: ["tenant.full"],
  },
  {
    id: "ta2",
    name: "<PERSON>",
    email: "<EMAIL>",
    tenant: "Fashion Hub",
    role: "Tenant Admin",
    lastLogin: "2023-05-08",
    status: "Active",
    permissions: ["tenant.stores", "tenant.products", "tenant.users"],
  },
  {
    id: "ta3",
    name: "<PERSON>",
    email: "<EMAIL>",
    tenant: "Food Delivery",
    role: "Tenant Admin",
    lastLogin: "2023-05-04",
    status: "Active",
    permissions: ["tenant.stores", "tenant.products", "tenant.marketing"],
  },
  {
    id: "ta4",
    name: "Sarah Brown",
    email: "<EMAIL>",
    tenant: "SaaS Corp",
    role: "Tenant Owner",
    lastLogin: "2023-05-05",
    status: "Active",
    permissions: ["tenant.full"],
  },
  {
    id: "ta5",
    name: "Michael Wilson",
    email: "<EMAIL>",
    tenant: "Fashion Hub",
    role: "Tenant Support",
    lastLogin: "2023-05-06",
    status: "Active",
    permissions: ["tenant.support", "tenant.users.view"],
  },
  {
    id: "ta6",
    name: "Emily Davis",
    email: "<EMAIL>",
    tenant: "Tech Solutions",
    role: "Tenant Marketing",
    lastLogin: "2023-05-07",
    status: "Active",
    permissions: ["tenant.marketing", "tenant.content"],
  },
  {
    id: "ta7",
    name: "James Anderson",
    email: "<EMAIL>",
    tenant: "Food Delivery",
    role: "Tenant Finance",
    lastLogin: "2023-05-02",
    status: "Active",
    permissions: ["tenant.finance", "tenant.reports"],
  },
]

// Tenant permission scopes
const tenantPermissionScopes = [
  { name: "Full Access", value: "tenant.full", description: "Complete control over tenant" },
  { name: "Stores Management", value: "tenant.stores", description: "Manage all stores within tenant" },
  { name: "Products Management", value: "tenant.products", description: "Manage all products within tenant" },
  { name: "User Management", value: "tenant.users", description: "Manage all users within tenant" },
  { name: "Marketing", value: "tenant.marketing", description: "Manage marketing campaigns and promotions" },
  { name: "Content", value: "tenant.content", description: "Manage content and pages" },
  { name: "Support", value: "tenant.support", description: "Handle support tickets and customer service" },
  { name: "Finance", value: "tenant.finance", description: "Manage financial operations and transactions" },
  { name: "Reports", value: "tenant.reports", description: "Access to analytics and reporting" },
]

export default function TenantAdmins() {
  const [searchTerm, setSearchTerm] = useState("")
  const [tenantFilter, setTenantFilter] = useState("all")
  const [roleFilter, setRoleFilter] = useState("all")
  const [selectedAdmin, setSelectedAdmin] = useState<(typeof tenantAdmins)[0] | null>(null)

  // Get unique values for filters
  const tenants = [...new Set(tenantAdmins.map((admin) => admin.tenant))]
  const roles = [...new Set(tenantAdmins.map((admin) => admin.role))]

  // Filter tenant admins based on search term and filters
  const filteredAdmins = tenantAdmins.filter((admin) => {
    const matchesSearch =
      admin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admin.email.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesTenant = tenantFilter === "all" || admin.tenant === tenantFilter
    const matchesRole = roleFilter === "all" || admin.role === roleFilter

    return matchesSearch && matchesTenant && matchesRole
  })

  const columns = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "tenant",
      header: "Tenant",
    },
    {
      accessorKey: "role",
      header: "Role",
      cell: ({ row }) => {
        const role = row.getValue("role") as string
        return (
          <Badge variant={role === "Tenant Owner" ? "destructive" : role === "Tenant Admin" ? "default" : "secondary"}>
            {role}
          </Badge>
        )
      },
    },
    {
      accessorKey: "lastLogin",
      header: "Last Login",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return <Badge variant={status === "Active" ? "success" : "secondary"}>{status}</Badge>
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const admin = row.original as (typeof tenantAdmins)[0]
        return (
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" title="View Admin" onClick={() => setSelectedAdmin(admin)}>
              <Eye className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Contact Admin">
              <Mail className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Suspend Admin">
              <UserX className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Reset Password">
              <KeyRound className="h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tenant Administrators</CardTitle>
        <CardDescription>Manage users with administrative access to tenants</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="Search by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Select value={tenantFilter} onValueChange={setTenantFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by Tenant" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tenants</SelectItem>
                {tenants.map((tenant) => (
                  <SelectItem key={tenant} value={tenant}>
                    {tenant}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by Role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                {roles.map((role) => (
                  <SelectItem key={role} value={role}>
                    {role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {selectedAdmin ? (
          <div className="mb-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{selectedAdmin.name}</CardTitle>
                    <CardDescription>{selectedAdmin.email}</CardDescription>
                  </div>
                  <Button variant="outline" onClick={() => setSelectedAdmin(null)}>
                    Back to List
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Admin Details</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Tenant:</div>
                        <div className="col-span-2">{selectedAdmin.tenant}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Role:</div>
                        <div className="col-span-2">
                          <Badge
                            variant={
                              selectedAdmin.role === "Tenant Owner"
                                ? "destructive"
                                : selectedAdmin.role === "Tenant Admin"
                                  ? "default"
                                  : "secondary"
                            }
                          >
                            {selectedAdmin.role}
                          </Badge>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Last Login:</div>
                        <div className="col-span-2">{selectedAdmin.lastLogin}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="font-medium">Status:</div>
                        <div className="col-span-2">
                          <Badge variant={selectedAdmin.status === "Active" ? "success" : "secondary"}>
                            {selectedAdmin.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium mb-4">Permission Scopes</h3>
                    <div className="space-y-2">
                      {tenantPermissionScopes.map((scope) => (
                        <div key={scope.value} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <div className="font-medium">{scope.name}</div>
                            <div className="text-sm text-muted-foreground">{scope.description}</div>
                          </div>
                          <Badge
                            variant={
                              selectedAdmin.permissions.includes("tenant.full") ||
                              selectedAdmin.permissions.includes(scope.value)
                                ? "success"
                                : "outline"
                            }
                          >
                            {selectedAdmin.permissions.includes("tenant.full") ||
                            selectedAdmin.permissions.includes(scope.value)
                              ? "Granted"
                              : "Not Granted"}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="mt-6 flex justify-end space-x-2">
                  <Button variant="outline">Contact Admin</Button>
                  <Button variant="outline">Reset Password</Button>
                  <Button variant="destructive">Suspend Admin</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <DataTable
            columns={columns}
            data={filteredAdmins}
            searchColumn="name"
            searchPlaceholder="Search tenant admins..."
          />
        )}
      </CardContent>
    </Card>
  )
}
