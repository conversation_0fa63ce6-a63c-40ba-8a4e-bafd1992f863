# Panduan GitHub & Deployment

Dokumen ini berisi panduan untuk: ok
1. Dasar-dasar Git & GitHub
2. Migrasi repository GitHub
3. Deploy ke Vercel

## Daftar Isi
- [Dasar-dasar Git & GitHub](#dasar-dasar-git--github)
- [Migrasi Repository GitHub](#migrasi-repository-github)
- [Deploy ke Vercel](#deploy-ke-vercel)

## Dasar-dasar Git & GitHub

### Persiapan Awal
1. **Install Git**
   - Unduh dari [git-scm.com](https://git-scm.com/)
   - Ikuti langkah instalasi default

2. **Konfigurasi Git**
   ```bash
   git config --global user.name "<PERSON>a Anda"
   git config --global user.email "<EMAIL>"
   ```

### Workflow Dasar Git

1. **Inisialisasi Repository**
   ```bash
   git init
   ```

2. **Menambahkan File ke Staging Area**
   ```bash
   git add nama_file        # File tertentu
   git add .                # Semua file yang berubah
   git add *.js             # Semua file .js
   ```

3. **Membuat Commit**
   ```bash
   git commit -m "Pesan commit yang deskriptif"
   ```

4. **Mengecek Status**
   ```bash
   git status
   ```

5. **Melihat Riwayat Commit**
   ```bash
   git log
   git log --oneline       # Tampilan lebih ringkas
   ```

### Bekerja dengan GitHub

1. **Clone Repository**
   ```bash
   git clone https://github.com/username/repository.git
   ```

2. **Menambahkan Remote**
   ```bash
   git remote add origin https://github.com/username/repository.git
   ```

3. **Push ke GitHub**
   ```bash
   git push -u origin main  # Untuk pertama kali
   git push                 # Setelahnya cukup seperti ini
   ```

4. **Mengambil Perubahan**
   ```bash
   git pull origin main
   ```

5. **Membuat Branch Baru**
   ```bash
   git checkout -b nama-branch
   ```

### Workflow Standar Push ke GitHub

1. Periksa perubahan yang ada:
   ```bash
   git status
   ```

2. Tambahkan file yang ingin di-commit:
   ```bash
   git add .
   ```
   git add docs/TUTORIAL_GITHUB.md (misal )

3. Buat commit:
   ```bash
   git commit -m "Deskripsi perubahan"
   ```



4. Push ke GitHub:
   ```bash
   git push origin main
   ```
5. . Tarik perubahan terbaru (jika ada):
   ```bash
   git pull origin main
   ```

### Tips Penting
- Selalu lakukan `git pull` sebelum `git push`
- Gunakan pesan commit yang deskriptif
- Buat branch baru untuk fitur besar
- Jangan push file sensitif (seperti .env) ke GitHub

## Migrasi Repository GitHub

Langkah-langkah untuk memindahkan repository GitHub ke repository baru sambil mempertahankan riwayat commit.

## Langkah 1: Clone Repository Asli
```bash
git clone https://github.com/original-owner/original-repo.git
cd original-repo
```

## Langkah 2: Hapus Koneksi ke Repository Lama
```bash
git remote remove origin
```

## Langkah 3: Tambahkan Repository Baru
```bash
git remote add origin https://github.com/username/new-repo.git
```

## Langkah 4: Push ke Repository Baru
```bash
git push -u origin main
```

## Langkah 5: Verifikasi
```bash
git remote -v
git status
```

## Contoh Nyata
Berikut contoh nyata yang telah dilakukan:

```bash
# Clone repository asli
git clone https://github.com/sellzio/v0-Sellzio-Saas.git
cd v0-Sellzio-Saas

# Hapus koneksi lama
git remote remove origin

# Tambahkan repository baru
git remote add origin https://github.com/sellzio/windsuf-sellzio

# Push ke repository baru
git push -u origin main
```

## Tips
- Pastikan Anda memiliki akses ke repository baru
- Jika terjadi error saat push, coba perintah dengan flag `--force` (hati-hati):
  ```bash
  git push -f origin main
  ```
- Selalu backup repository lokal sebelum melakukan operasi penting

## Troubleshooting
- **Error: Repository not found**
  Pastikan URL repository benar dan Anda memiliki akses
  
- **Error: Permission denied**
  Pastikan Anda sudah login ke GitHub di komputer tersebut
  
- **Error: Updates were rejected**
  Gunakan `git pull --rebase` terlebih dahulu, atau gunakan `--force` jika yakin

## Deploy ke Vercel

### Prasyarat
1. Akun Vercel (terdaftar dengan GitHub)
2. Proyek Next.js yang sudah siap deploy
3. Vercel CLI terinstall

### Langkah-langkah Deploy

1. **Login ke Vercel**
   ```bash
   vercel login
   ```
   
2. **Masuk ke direktori proyek**
   ```bash
   cd nama-proyek-anda
   ```

3. **Link ke proyek Vercel yang sudah ada**
   ```bash
   vercel link
   ```
   - Pilih scope/workspace yang sesuai
   - Pilih "Link to existing project"
   - Masukkan nama proyek yang sudah ada di Vercel

4. **Deploy ke produksi**
   ```bash
   vercel --prod
   ```

### Setelah Deploy
- Aplikasi akan tersedia di URL yang diberikan Vercel
- Semua perubahan yang di-push ke branch utama akan otomatis dideploy

### Konfigurasi Environment Variables
Pastikan untuk mengatur environment variables di pengaturan proyek Vercel, seperti:
- NEXT_PUBLIC_SUPABASE_URL
- NEXT_PUBLIC_SUPABASE_ANON_KEY
- Dan variabel lain yang dibutuhkan

### Troubleshooting
- **Build gagal**: Periksa log build di dashboard Vercel
- **Environment variables tidak terbaca**: Pastikan sudah diatur di pengaturan proyek Vercel
- **Error saat link proyek**: Pastikan nama proyek benar dan Anda memiliki akses

## Referensi
- [GitHub Docs: Adding a remote](https://docs.github.com/en/get-started/getting-started-with-git/managing-remote-repositories)
- [GitHub Docs: Renaming a remote](https://docs.github.com/en/get-started/getting-started-with-git/managing-remote-repositories#switching-remote-urls-from-https-to-ssh)
- [Vercel CLI Documentation](https://vercel.com/docs/cli)
- [Vercel Environment Variables](https://vercel.com/docs/projects/environment-variables)


