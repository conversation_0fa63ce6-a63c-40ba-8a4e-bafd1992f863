# Dashboard Tenant (Tenant) Sellzio SaaS: Menu & Submenu Detail

Berikut adalah penjelasan komprehensif mengenai setiap menu dan submenu di Dashboard Tenant (Tenant) Sellzio SaaS, termasuk apa yang ditampilkan ketika menu atau submenu tersebut diklik.

## 1. Dashboard

**Saat diklik**: Menampilkan halaman beranda dengan ringkasan marketplace tenant.

**Tampilan**:
- KPI cards dengan metrik utama: Total revenue, order count, user count, store count, GMV, conversion rate
- Grafik revenue dari waktu ke waktu dengan filter periode
- Store performance chart menampilkan top 5-10 stores berdasarkan revenue
- Activity feed dengan aktivitas terbaru di marketplace
- Alert dashboard untuk item yang memerlukan perhatian
- Quick action buttons untuk tugas umum tenant

## 2. Stores

**Saat diklik**: Menampilkan halaman daftar semua store di bawah tenant dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel/grid store dengan kolom: Nama Store, Owner, Product Count, Order Count, Revenue, Status, Rating
- Filter panel untuk memfilter berdasarkan status, kate<PERSON>i, dan tanggal pembuatan
- Search box untuk mencari store berdasarkan nama atau owner
- Action buttons untuk setiap store: View, Edit, Suspend, Message Owner

**Submenu**:

### 2.1. All Stores
- **Saat diklik**: Sama dengan menu Stores utama.

### 2.2. Store Applications
- **Saat diklik**: Menampilkan daftar aplikasi store yang belum diproses/pending.
- **Tampilan**:
  - Tabel aplikasi dengan kolom: Nama Store, Owner, Tanggal Aplikasi, Kategori, Status
  - Preview detail aplikasi (produk yang akan dijual, deskripsi, dll.)
  - Approval workflow dengan checklist persyaratan dan tombol Approve/Reject
  - Form untuk memberikan feedback ke aplikant

### 2.3. Store Categories
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola kategori store.
- **Tampilan**:
  - Tabel atau hierarki kategori dengan kolom: Nama, Deskripsi, Store Count, Status
  - Form untuk membuat/mengedit kategori
  - Opsi untuk mengatur featured categories
  - Drag-and-drop untuk mengurutkan kategori

### 2.4. Store Settings
- **Saat diklik**: Menampilkan pengaturan global untuk stores di marketplace.
- **Tampilan**:
  - Form pengaturan dengan berbagai section:
    - Commission rates (global dan per kategori)
    - Store approval requirements
    - Store dashboard customization
    - Policy templates (return, shipping, etc.)
    - Rating & review settings

## 3. Products

**Saat diklik**: Menampilkan daftar semua produk di marketplace dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel/grid produk dengan kolom: Gambar, Nama, Store, Kategori, Harga, Stok, Sales, Status
- Filter panel untuk memfilter berdasarkan kategori, store, price range, status
- Search box untuk mencari produk berdasarkan nama atau deskripsi
- Action buttons untuk setiap produk: View, Edit, Feature, Remove

**Submenu**:

### 3.1. All Products
- **Saat diklik**: Sama dengan menu Products utama.

### 3.2. Categories
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola kategori produk.
- **Tampilan**:
  - Hierarchical tree view untuk kategori dan subkategori
  - Form untuk membuat/mengedit kategori
  - Attribute management untuk setiap kategori
  - Category arrangement tools (ordering, nesting)
  - Category analytics (product count, views, conversions)

### 3.3. Brands
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola brand produk.
- **Tampilan**:
  - Tabel brand dengan kolom: Logo, Nama, Deskripsi, Product Count, Featured Status
  - Form untuk menambah/mengedit brand
  - Brand verification settings
  - Brand page customization options

### 3.4. Import/Export
- **Saat diklik**: Menampilkan tool untuk import dan export produk massal.
- **Tampilan**:
  - Import panel dengan file upload area
  - Template downloaders untuk berbagai format (CSV, Excel)
  - Import history log
  - Export tool dengan filter options
  - Validation settings dan error handling

### 3.5. Product Moderation
- **Saat diklik**: Menampilkan produk yang memerlukan review moderasi.
- **Tampilan**:
  - Queue produk dengan status moderasi
  - Content review interface
  - Policy compliance checklist
  - Moderation action buttons (Approve, Reject, Request Changes)
  - Moderation history log

## 4. Orders

**Saat diklik**: Menampilkan daftar semua order di marketplace dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel order dengan kolom: Order ID, Customer, Store, Total, Date, Status, Payment Status
- Filter panel untuk memfilter berdasarkan status, store, tanggal, payment method
- Search box untuk mencari order berdasarkan ID atau customer name
- Action buttons untuk setiap order: View, Update Status, Message Customer/Store

**Submenu**:

### 4.1. All Orders
- **Saat diklik**: Sama dengan menu Orders utama.

### 4.2. Order Fulfillment
- **Saat diklik**: Menampilkan panel monitoring untuk order fulfillment.
- **Tampilan**:
  - Pipeline view dengan kolom status (New, Processing, Shipped, Delivered, Completed)
  - Order cards yang dapat di-drag antar kolom status
  - Order count dan total value per status
  - Batch action tools untuk multiple orders
  - Time metrics (average processing time, shipping time, etc.)

### 4.3. Returns & Refunds
- **Saat diklik**: Menampilkan daftar return dan refund requests.
- **Tampilan**:
  - Tabel returns dengan kolom: Order ID, Customer, Store, Return Reason, Date, Status
  - Return details panel dengan item dan alasan return
  - Refund calculator
  - Approval workflow dengan review steps
  - Return policy reference panel

### 4.4. Shipping Settings
- **Saat diklik**: Menampilkan pengaturan pengiriman marketplace.
- **Tampilan**:
  - Shipping method management
  - Shipping rate tables (by weight, dimension, destination)
  - Shipping carrier integration settings
  - Shipping label generation options
  - Shipping zones configuration

## 5. Customers

**Saat diklik**: Menampilkan daftar semua customer di marketplace dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel customer dengan kolom: Nama, Email, Registration Date, Orders, Total Spent, Last Active
- Filter panel untuk memfilter berdasarkan activity, purchase history, registration date
- Search box untuk mencari customer berdasarkan nama atau email
- Action buttons untuk setiap customer: View Profile, Message, Manage Access

**Submenu**:

### 5.1. All Customers
- **Saat diklik**: Sama dengan menu Customers utama.

### 5.2. Customer Groups
- **Saat diklik**: Menampilkan dan memungkinkan tenant membuat segment customer.
- **Tampilan**:
  - Tabel customer group dengan kolom: Nama, Deskripsi, Customer Count, Created Date
  - Group builder dengan rule-based segmentation
  - Group analytics (spending patterns, preferences)
  - Group-specific marketing tools
  - Customer import/assignment to groups

### 5.3. Customer Reviews
- **Saat diklik**: Menampilkan daftar semua review yang diberikan customer.
- **Tampilan**:
  - Tabel review dengan kolom: Customer, Product, Store, Rating, Date, Status
  - Review moderation interface
  - Response management tool
  - Review analytics (average rating, trend over time)
  - Review policy settings

## 6. Affiliates

**Saat diklik**: Menampilkan dashboard program affiliate marketplace.

**Tampilan**:
- Affiliate program performance overview
- KPI cards: Total affiliates, affiliate-generated sales, commission paid, conversion rate
- Top affiliates leaderboard
- Affiliate growth chart
- Recent affiliate signups

**Submenu**:

### 6.1. All Affiliates
- **Saat diklik**: Menampilkan daftar semua affiliate di marketplace.
- **Tampilan**:
  - Tabel affiliate dengan kolom: Nama, Email, Join Date, Generated Sales, Commission Earned, Status
  - Filter dan search tools
  - Performance metrics untuk setiap affiliate
  - Action buttons: View Profile, Message, Adjust Commission, Suspend

### 6.2. Affiliate Applications
- **Saat diklik**: Menampilkan daftar aplikasi affiliate yang pending.
- **Tampilan**:
  - Application review interface
  - Applicant information (channel, audience size, experience)
  - Application approval workflow
  - Policy agreement verification
  - Custom commission offer tool

### 6.3. Commission Settings
- **Saat diklik**: Menampilkan pengaturan komisı untuk affiliate program.
- **Tampilan**:
  - Global commission rate settings
  - Category-specific rate settings
  - Store-specific rate overrides
  - Volume-based tier configuration
  - Special promotion commission rates
  - Commission cap settings

### 6.4. Marketing Materials
- **Saat diklik**: Menampilkan dan mengelola marketing materials untuk affiliates.
- **Tampilan**:
  - Material library dengan preview
  - Upload new materials interface
  - Material usage analytics
  - Material categorization
  - Custom material request management

## 7. Marketing

**Saat diklik**: Menampilkan dashboard marketing marketplace.

**Tampilan**:
- Marketing performance overview
- Campaign analytics dashboard
- Promotion calendar
- Marketing channel performance comparison
- Recent campaign results

**Submenu**:

### 7.1. Campaigns
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola kampanye marketing.
- **Tampilan**:
  - Tabel campaign dengan kolom: Nama, Type, Start Date, End Date, Budget, Status, Performance
  - Campaign builder interface
  - Performance analytics per campaign
  - Campaign scheduling tools
  - A/B testing configuration

### 7.2. Promotions & Coupons
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola promosi.
- **Tampilan**:
  - Tabel promotion dengan kolom: Nama, Type, Discount, Start/End Date, Usage Count, Status
  - Promotion builder interface
    - Discount type (percentage, fixed, free shipping, BOGO)
    - Applicable products/categories
    - Usage limits
    - Customer eligibility
  - Coupon code generator
  - Promotion performance analytics
  - Bulk promotion tools

### 7.3. Flash Sales
- **Saat diklik**: Menampilkan dan memungkinkan tenant membuat flash sales.
- **Tampilan**:
  - Flash sale planner dengan timeline view
  - Product selection interface
  - Time-limited discount settings
  - Inventory allocation tools
  - Flash sale performance analytics
  - Countdown timer configuration
  - Promotion tools for flash sales

### 7.4. Email Campaigns
- **Saat diklik**: Menampilkan tools untuk email marketing.
- **Tampilan**:
  - Email campaign manager
  - Email template builder dengan drag-and-drop interface
  - Audience selection tools
  - Email scheduling
  - A/B testing for subject lines and content
  - Email analytics (open rate, click rate, conversion)
  - Automated email flow builder

## 8. Content

**Saat diklik**: Menampilkan content management system marketplace.

**Tampilan**:
- Content overview dashboard
- Content calendar
- Content performance analytics
- Recent published content
- Pending content items

**Submenu**:

### 8.1. Pages
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola halaman statis.
- **Tampilan**:
  - Tabel pages dengan kolom: Title, URL, Last Updated, Status, Views
  - Page builder dengan visual editor
  - Page template selection
  - SEO settings per page
  - Page version history
  - Page preview tools

### 8.2. Blog
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola blog.
- **Tampilan**:
  - Tabel article dengan kolom: Title, Author, Category, Published Date, Status, Views
  - Rich text editor untuk artikel
  - Media gallery integration
  - Article categorization
  - SEO tools for article
  - Publishing schedule options
  - Featured article settings

### 8.3. Media
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola media library.
- **Tampilan**:
  - Grid view media gallery
  - Folder organization for media
  - Upload interface with drag-and-drop
  - Image editing tools
  - Media usage tracking
  - Bulk media operations
  - Media metadata editor

### 8.4. Navigation
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi navigasi situs.
- **Tampilan**:
  - Menu builder dengan drag-and-drop interface
  - Menu item editor (link, label, icon)
  - Menu structure manager (nested menus)
  - Menu position settings
  - Mobile navigation configurator
  - Menu preview tools
  - Menu analytics (click rates on menu items)

### 8.5. SEO
- **Saat diklik**: Menampilkan SEO tools untuk marketplace.
- **Tampilan**:
  - SEO dashboard dengan performance metrics
  - Keyword ranking tracker
  - On-page SEO analyzer
  - Meta tag manager
  - URL structure settings
  - Sitemap configurator
  - Structured data tools

## 9. Live

**Saat diklik**: Menampilkan dashboard live shopping dan video commerce.

**Tampilan**:
- Live shopping performance overview
- Upcoming scheduled streams
- Past stream analytics
- Top performing products in live streams
- Quick start streaming button

**Submenu**:

### 9.1. Schedule
- **Saat diklik**: Menampilkan dan memungkinkan tenant menjadwalkan live streams.
- **Tampilan**:
  - Calendar view dengan scheduled streams
  - Stream scheduler form
    - Title & description
    - Date and time selector
    - Duration setting
    - Featured products selection
    - Host assignment
    - Promotional settings
  - Stream template selection
  - Notification settings for followers
  - Stream preparation checklist

### 9.2. Studio
- **Saat diklik**: Menampilkan interface untuk melakukan live stream.
- **Tampilan**:
  - Broadcasting interface dengan preview window
  - Stream control panel (start, pause, end)
  - Camera and mic settings
  - Background and filter options
  - Product showcase panel
  - Viewer comment display
  - Live metrics (viewers, likes, shares)
  - Call-to-action button creator

### 9.3. Recordings
- **Saat diklik**: Menampilkan daftar past live streams.
- **Tampilan**:
  - Tabel recordings dengan kolom: Title, Date, Duration, Views, Sales Generated, Status
  - Recording player
  - Recording editor untuk trim dan edit
  - Performance analytics per recording
  - Highlight creator tool
  - Distribution settings (share, embed, feature)
  - Product tagging for recordings

### 9.4. Analytics
- **Saat diklik**: Menampilkan analytics khusus untuk live dan video commerce.
- **Tampilan**:
  - Performance dashboard untuk live program
  - Viewership metrics (peak viewers, average watch time)
  - Engagement metrics (comments, likes, shares)
  - Conversion metrics (CTR, add-to-cart, purchase)
  - Product performance in live sessions
  - Host performance analytics
  - Best time to stream analysis

## 10. Analytics

**Saat diklik**: Menampilkan dashboard analytics marketplace.

**Tampilan**:
- Analytics overview dengan KPI utama
- Performance trend charts
- User behavior visualizations
- Sales and revenue analytics
- Traffic source breakdown

**Submenu**:

### 10.1. Overview
- **Saat diklik**: Menampilkan dashboard analytics komprehensif.
- **Tampilan**:
  - High-level KPI dashboard
  - Period comparison tools (MoM, YoY)
  - Multiple chart types showing different metrics
  - Analytics segments (sales, users, products, traffic)
  - Custom dashboard builder
  - Saved views and layouts

### 10.2. Sales Analytics
- **Saat diklik**: Menampilkan analisis detail tentang penjualan.
- **Tampilan**:
  - Sales breakdown by various dimensions
    - By store
    - By category
    - By product
    - By customer segment
    - By time period
  - Sales funnel visualization
  - Conversion rate analysis
  - Average order value trends
  - Sales forecast tools
  - Profitability analysis

### 10.3. Customer Analytics
- **Saat diklik**: Menampilkan analisis detail tentang customer.
- **Tampilan**:
  - Customer acquisition analysis
  - Customer retention metrics
  - Customer lifetime value calculator
  - Customer journey mapping
  - Engagement metrics
  - Segmentation analysis
  - Churn prediction
  - Customer behavior patterns

### 10.4. Reports
- **Saat diklik**: Menampilkan dan memungkinkan tenant membuat custom reports.
- **Tampilan**:
  - Report builder interface
  - Saved report templates
  - Scheduled reports manager
  - Export options (PDF, Excel, CSV)
  - Visualization customization
  - Data source selection
  - Report sharing tools

### 10.5. Export
- **Saat diklik**: Menampilkan tools untuk data export.
- **Tampilan**:
  - Data export interface
  - Dataset selection
  - Export format options
  - Scheduling options
  - File management for exports
  - API access for data

## 11. Financial

**Saat diklik**: Menampilkan dashboard keuangan marketplace.

**Tampilan**:
- Financial overview dengan KPI utama
- Revenue breakdown chart
- Balance and payout information
- Transaction volume metrics
- Fee structure summary

**Submenu**:

### 11.1. Overview
- **Saat diklik**: Menampilkan dashboard keuangan komprehensif.
- **Tampilan**:
  - Financial KPI dashboard
  - Revenue streams visualization
  - Fee collection analytics
  - Balance summary
  - Upcoming payout information
  - Transaction volume trends
  - Profit margin analysis

### 11.2. Payouts
- **Saat diklik**: Menampilkan dan mengelola payouts untuk stores dan affiliates.
- **Tampilan**:
  - Tabel payouts dengan kolom: Recipient, Type, Amount, Status, Scheduled Date
  - Payout calendar
  - Payout approval workflow
  - Payment method management
  - Payout history
  - Payout calculation details

### 11.3. Transactions
- **Saat diklik**: Menampilkan log detail semua transaksi keuangan.
- **Tampilan**:
  - Tabel transaction dengan kolom: Transaction ID, Type, Amount, Source, Destination, Date, Status
  - Transaction detail viewer
  - Transaction search and filter tools
  - Transaction reconciliation tools
  - Export options for accounting

### 11.4. Statements
- **Saat diklik**: Menampilkan dan generate laporan keuangan.
- **Tampilan**:
  - Statement generator interface
  - Statement history
  - Statement period selector
  - Statement detail viewer
  - Export and download options
  - Tax document generator
  - Statement distribution tools

## 12. Settings

**Saat diklik**: Menampilkan pengaturan marketplace.

**Tampilan**:
- Settings overview dengan kategori pengaturan
- Quick settings search
- Recent setting changes
- Setting validation status

**Submenu**:

### 12.1. Profile
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengedit profil marketplace.
- **Tampilan**:
  - Business information form
  - Contact details
  - Legal information
  - Business hours
  - Social media links
  - About us editor
  - Geographic settings

### 12.2. Branding
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi branding.
- **Tampilan**:
  - Logo upload area (multiple sizes)
  - Favicon upload
  - Color scheme picker
  - Typography settings
  - Button style configurator
  - Email branding settings
  - Invoice and document branding

### 12.3. Domain
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi domain.
- **Tampilan**:
  - Current domain status
  - Domain change request form
  - DNS settings information
  - SSL certificate status
  - Domain verification tools
  - Subdomain management
  - Domain history

### 12.4. Users
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola user staff marketplace.
- **Tampilan**:
  - Tabel staff dengan kolom: Name, Email, Role, Last Login, Status
  - User invitation interface
  - Role assignment tools
  - Permission management
  - Account security settings
  - Activity logs per user
  - Bulk user operations

### 12.5. Integrations
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi integrasi pihak ketiga.
- **Tampilan**:
  - Integration directory dengan status
  - Integration setup wizards
  - API key management
  - Webhook configuration
  - OAuth connection manager
  - Integration logs
  - Integration health monitoring

### 12.6. Notifications
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi notifikasi.
- **Tampilan**:
  - Notification type list dengan toggle switches
  - Email template editor
  - Push notification settings
  - Notification schedule preferences
  - Event-based notification rules
  - Notification preview tools
  - Notification test tools

### 12.7. Security
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi keamanan.
- **Tampilan**:
  - Security overview dashboard
  - Password policy settings
  - Two-factor authentication settings
  - Session management
  - IP restriction tools
  - Access log viewer
  - Security alert configurations

## Fitur Umum di Setiap Halaman

1. **Navigasi Global**:
   - Sidebar dengan menu utama dan submenu
   - Breadcrumbs untuk navigasi 
   - Quick action buttons
   - Back button ke halaman sebelumnya

2. **Search and Filter**:
   - Global search di header
   - Advanced filter panel di halaman list
   - Saved filters
   - Sorting options

3. **Data View Controls**:
   - Toggle between list/grid/card views
   - Pagination controls
   - Items per page selector
   - Column visibility toggles

4. **Action Controls**:
   - Bulk action menus
   - Export options
   - Refresh button
   - Context menu untuk item-specific actions

5. **Notifications**:
   - System alerts
   - Action confirmations
   - Error messages
   - Success indicators

6. **White Label Elements**:
   - Brand logo dan color scheme khusus tenant
   - Custom domain di URL
   - Branded elements (email, notifications, etc.)
   - Custom terminology sesuai preferensi tenant

Dashboard Tenant Sellzio SaaS dirancang untuk memberikan tenant (Tenant) kontrol penuh atas marketplace mereka, 
dengan fokus pada pengelolaan stores, produk, dan semua aspek operasional marketplace.
 Struktur menu dirancang secara intuitif, mengelompokkan fitur berdasarkan fungsi dan memastikan tenant dapat dengan mudah mengelola seluruh ekosistem e-commerce mereka.
 