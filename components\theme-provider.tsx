"use client"

import { Theme<PERSON>rovider as NextThemesProvider } from "next-themes"
import type { ThemeProviderProps } from "next-themes"

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  // Hapus kode yang memaksa tema dark
  // useEffect(() => {
  //   document.documentElement.classList.add("dark")
  // }, [])

  return (
    <NextThemesProvider attribute="class" defaultTheme="dark" enableSystem {...props}>
      {children}
    </NextThemesProvider>
  )
}
