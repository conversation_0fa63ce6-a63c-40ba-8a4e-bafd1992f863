import { Suspense } from "react"
import { ProductCatalog } from "@/components/admin/products/product-catalog"
import { ConfirmationProvider } from "@/components/admin/ui/confirmation-dialog"

export default function ProductsPage() {
  return (
    <ConfirmationProvider>
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">Products Management</h1>
        <Suspense fallback={<div>Loading products...</div>}>
          <ProductCatalog />
        </Suspense>
      </div>
    </ConfirmationProvider>
  )
}
