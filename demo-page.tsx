"use client"

import VelozioHeader from "./velozio-header"
import "./velozio-styles.css"

export default function DemoPage() {
  return (
    <div className="min-h-screen bg-gray-100">
      <VelozioHeader />

      <main className="max-w-7xl mx-auto p-4 md:p-6 mt-16">
        <div className="bg-white rounded-lg p-6 mt-6 shadow-sm">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Velozio E-commerce Theme</h1>
          <p className="text-gray-600">
            Header komponen Velozio dengan spesifikasi yang telah diperbarui:
          </p>
          <ul className="list-disc pl-5 mt-3 text-gray-600">
            <li>Border radius 8px untuk kolom pencarian</li>
            <li>Lebar maksimal 800px dengan width 100%</li>
            <li>Background putih dengan border 2px solid #ee4d2d</li>
            <li>Box-shadow: 0 1px 3px rgba(0,0,0,0.1)</li>
            <li>Posisi icon yang tepat sesuai spesifikasi</li>
            <li>Badge notifikasi dengan posisi yang tepat</li>
            <li>Mode expanded search dengan tombol back dan clear</li>
          </ul>
        </div>
      </main>
    </div>
  )
}
