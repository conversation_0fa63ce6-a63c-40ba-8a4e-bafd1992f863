"use client"

import type React from "react"
import { useState } from "react"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminHeader } from "@/components/admin/admin-header"
import { ProviderWrapper } from "@/components/providers/provider-wrapper"
import { ErrorBoundary } from "react-error-boundary"

// Nonaktifkan static generation
export const dynamic = "force-dynamic"

export default function AdminDashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(true)

  return (
    <ProviderWrapper>
      <div className="flex min-h-screen flex-col">
        {/* Konten layout yang sudah ada */}
        <AdminHeader sidebarOpen={sidebarOpen} onSidebarOpenChange={setSidebarOpen} />
        <div className="flex flex-1">
          <AdminSidebar open={sidebarOpen} onOpenChange={setSidebarOpen} />
          <main className="flex-1 p-6 overflow-auto">
            <ErrorBoundary fallback={<div>Something went wrong</div>}>{children}</ErrorBoundary>
          </main>
        </div>
      </div>
    </ProviderWrapper>
  )
}
