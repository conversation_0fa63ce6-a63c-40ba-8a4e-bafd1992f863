"use client"

import { useEffect, useState } from "react"

interface FlashSaleProps {
  endTime: Date
  originalPrice: string
  currentPrice: string
  remaining: number
  total: number
}

export const FlashSale = ({ endTime, originalPrice, currentPrice, remaining, total }: FlashSaleProps) => {
  const [timeLeft, setTimeLeft] = useState({
    hours: "00",
    minutes: "00",
    seconds: "00",
  })

  useEffect(() => {
    const calculateTimeLeft = () => {
      const difference = endTime.getTime() - new Date().getTime()

      if (difference > 0) {
        const hours = Math.floor(difference / (1000 * 60 * 60))
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((difference % (1000 * 60)) / 1000)

        setTimeLeft({
          hours: hours.toString().padStart(2, "0"),
          minutes: minutes.toString().padStart(2, "0"),
          seconds: seconds.toString().padStart(2, "0"),
        })
      }
    }

    calculateTimeLeft()
    const timer = setInterval(calculateTimeLeft, 1000)

    return () => clearInterval(timer)
  }, [endTime])

  const percentRemaining = (remaining / total) * 100

  return (
    <div className="flex bg-[#fff0f0] border border-[#ffccc7] rounded-sm p-1 items-center mt-1 mb-1">
      <div className="flex-1 min-w-0 mr-1">
        <div className="flex justify-between items-center mb-0.5">
          <div className="text-[#ff4d4f] text-[9px] font-bold flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-2.5 w-2.5 mr-0.5 -translate-y-[1px]"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
                clipRule="evenodd"
              />
            </svg>
            FLASH SALE
          </div>
          <div className="flex items-center text-[8px] text-gray-600">
            <span className="bg-[#ff4d4f] text-white text-[8px] font-bold px-0.5 min-w-3 text-center inline-block mx-0.5">
              {timeLeft.hours}
            </span>
            :
            <span className="bg-[#ff4d4f] text-white text-[8px] font-bold px-0.5 min-w-3 text-center inline-block mx-0.5">
              {timeLeft.minutes}
            </span>
            :
            <span className="bg-[#ff4d4f] text-white text-[8px] font-bold px-0.5 min-w-3 text-center inline-block mx-0.5">
              {timeLeft.seconds}
            </span>
          </div>
        </div>
        <div className="flex items-baseline">
          <div className="text-[12px] font-bold text-[#ff4d4f] mr-1 whitespace-nowrap">{currentPrice}</div>
          <div className="text-[9px] text-gray-400 line-through whitespace-nowrap overflow-hidden text-ellipsis max-w-[45px]">
            {originalPrice}
          </div>
        </div>
      </div>
      <div className="flex flex-col items-end">
        <div className="text-[8px] font-bold text-[#ff4d4f] mb-0.5 -translate-x-0.5">Sisa {remaining}</div>
        <div className="w-10 h-0.5 bg-[#ffe8e6] rounded-sm overflow-hidden relative">
          <div
            className="absolute left-0 top-0 h-full bg-gradient-to-r from-[#ffbb96] to-[#ff4d4f] rounded-sm animate-shimmer"
            style={{ width: `${percentRemaining}%`, backgroundSize: "200% 100%" }}
          ></div>
        </div>
      </div>
    </div>
  )
}
