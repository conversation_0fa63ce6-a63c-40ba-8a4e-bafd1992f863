# Rangkuman Detail Bahasa Program & Teknologi Sellzio SaaS di Setiap Tahap dengan NestJS dari Awal

Berikut rangkuman mendetail stack teknologi dengan implementasi NestJS sebagai backend dari tahap awal:

## Tahap Gratis (Pemula)

### Bahasa Pemrograman & Framework
- **Frontend**: TypeScript dengan Next.js
- **Backend**: TypeScript dengan NestJS



### Database
- **Tipe**: PostgreSQL
- **Provider**: Supabase (500MB gratis)
- **ORM**: Prisma

### Implementasi NestJS Sederhana
- **Modules**: Basic modules (Auth, Users, Stores, Products)
- **Controllers**: REST endpoints dasar
- **Services**: Business logic sederhana
- **Schema**: Prisma schema dengan relasi dasar
- **Authentication**: JWT-based auth dengan roles sederhana

### Deployment
- **Frontend**: Vercel (hobby plan gratis)
- **Backend**: Render.com/Railway (free tier) atau Fly.io (free tier)
- **Database**: Supabase
- **Storage**: Cloudinary (free tier - 25GB)


### Kapasitas & Keterbatasan
- **User aktif**: 1.000 - 5.000
- **Pageviews**: 50.000 - 100.000/bulan
- **Transaksi**: 100 - 500/hari
- **Toko**: 100 - 200
- **Keterbatasan**: Rate limits pada free tiers, cold starts, koneksi database terbatas

## Tahap Hemat (Startup)

### Bahasa Pemrograman & Framework
- **Frontend**: TypeScript dengan Next.js
- **Backend**: TypeScript dengan NestJS (struktur lebih advanced)


### Database
- **Tipe**: PostgreSQL
- **Provider**: 
  - Digital Ocean Managed Postgres ($15/bulan) - UI lebih sederhana
  - AWS RDS PostgreSQL ($15-30/bulan) - Performa lebih baik
- **ORM**: Prisma dengan query optimization
- **Caching**: Redis Cloud ($10 plan)

### Implementasi NestJS (Advanced)
- **Modules**: Lebih terstruktur dengan domain separation
- **Architecture**: Enhanced dengan repository pattern
- **DTO**: Validasi yang komprehensif dengan class-validator
- **Pipes**: Custom transformation pipes
- **Guards**: Enhanced auth guards untuk multi-tenancy
- **Interceptors**: Logging, data transformation, caching
- **Exception Filters**: Centralized error handling
- **Events**: Event-based system untuk beberapa operasi (order status, dll)

### Deployment
- **Frontend**: Vercel Pro ($20) atau similar
- **Backend**: Digital Ocean App Platform ($12) atau AWS Elastic Beanstalk
- **Database**: PostgreSQL terkelola
- **Cache**: Redis Cloud
- **CI/CD**: GitHub Actions basic pipeline
- **Monitoring**: Sentry free tier + custom logging

### Contoh Implementasi Advanced
```typescript
// store.module.ts
@Module({
  imports: [
    ProductsModule,
    OrdersModule,
    ConfigModule.forFeature(storeConfig),
  ],
  controllers: [StoreController],
  providers: [
    StoreService,
    {
      provide: 'STORE_REPOSITORY',
      useClass: StoreRepository,
    },
    CommissionCalculatorService,
  ],
  exports: [StoreService],
})
export class StoreModule {}

// tenant.guard.ts
@Injectable()
export class TenantGuard implements CanActivate {
  constructor(
    private tenantService: TenantService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const tenantId = request.params.tenantId || request.headers['x-tenant-id'];
    const user = request.user;

    const allowedTenants = this.reflector.get<string[]>(
      'allowedTenants',
      context.getHandler(),
    ) || [];

    return this.tenantService.validateUserAccess(user, tenantId, allowedTenants);
  }
}



### Kapasitas & Scaling
- **User aktif**: 10.000 - 50.000
- **Pageviews**: 500.000 - 1.000.000/bulan
- **Transaksi**: 1.000 - 5.000/hari
- **Toko**: 500 - 2.000
- **Chat**: Hingga 1.000 koneksi simultan
- **Optimasi**: Database indexing, query caching, connection pooling

## Tahap Berkembang (Scale-up)

### Bahasa Pemrograman & Framework
- **Frontend**: TypeScript dengan Next.js (distributed across multiple apps)
- **Backend**: TypeScript dengan NestJS Microservices



### Database & Storage
- **Primary Database**: AWS Aurora PostgreSQL (multi-AZ) или AWS RDS PostgreSQL с replica
- **Caching**: Redis Enterprise multi-zone ($500+)
- **Search**: Elasticsearch или AWS OpenSearch
- **Message Broker**: Apache Kafka или AWS MSK
- **Object Storage**: AWS S3 for media & assets
- **CDN**: CloudFront/Cloudflare Enterprise

### Implementasi Microservices NestJS
- **Transports**: Kafka, RabbitMQ, gRPC untuk komunikasi antar-service
- **API Gateway**: Custom NestJS gateway dengan rate limiting, caching
- **Service Discovery**: Kubernetes-native atau Consul
- **Circuit Breaker**: Resilience patterns dengan retry, timeout, fallback
- **CQRS Pattern**: Untuk services dengan high read/write separation
- **Event Sourcing**: Untuk critical business transactions
- **Data Consistency**: Saga pattern untuk transaction across services

### Infrastructure & DevOps
- **Container Orchestration**: Kubernetes (EKS/GKE)
- **Service Mesh**: Istio/Linkerd
- **Infrastructure as Code**: Terraform
- **CI/CD**: GitLab CI atau GitHub Actions enterprise-grade
- **Monitoring**: ELK Stack + Prometheus + Grafana
- **APM**: Datadog atau New Relic
- **Security**: WAF, SAST/DAST scans, Secret Management



### Kapasitas & Performa
- **User aktif**: 100.000+ pengguna
- **Pageviews**: 10 juta+/bulan
- **Transaksi**: 10.000+/hari
- **Toko**: Tidak terbatas (horizontal scaling)
- **Chat**: Puluhan ribu koneksi simultan
- **Video Streaming**: Dukungan untuk ribuan concurrent viewers
- **Global Distribution**: Multi-region deployment dengan data replication

## Evolusi Teknologi di Setiap Tahap

### 1. Authentication & Authorization

**Gratis**:
- JWT-based auth dengan basic roles
- Simple cookie atau local storage management

**Hemat**:
- JWT dengan refresh tokens
- Role-based authorization yang lebih granular
- Tenant-scoped permissions

**Berkembang**:
- Microservice-based auth dengan OAuth2/OpenID Connect
- Federated identity (social logins)
- Fine-grained permission system (RBAC+ABAC)
- Multi-factor authentication (MFA)

### 2. Database Access

**Gratis**:
- Prisma basic queries
- Single database dengan basic transactions

**Hemat**:
- Repository pattern dengan query optimization
- Read/write separation
- Connection pooling
- Migrations management

**Berkembang**:
- Database per service (polyglot persistence)
- Sharding untuk high-volume data
- CQRS untuk read-heavy services
- Data replication across regions
- Domain-driven repository design

### 3. API Design

**Gratis**:
- Simple REST API
- Basic CRUD operations

**Hemat**:
- REST API with advanced filtering, pagination
- OpenAPI/Swagger documentation
- GraphQL for complex queries

**Berkembang**:
- API Gateway dengan versioning, throttling
- BFF (Backend For Frontend) pattern
- Event-driven APIs via Kafka/RabbitMQ
- GraphQL federation
- gRPC untuk inter-service communication

### 4. Testing

**Gratis**:
- Basic unit tests
- Simple integration tests

**Hemat**:
- Unit testing dengan mocking
- Integration testing dengan test databases
- E2E testing untuk critical flows
- API contract testing

**Berkembang**:
- Comprehensive test suite (unit, integration, e2e)
- Performance testing
- Chaos engineering
- Contract testing between microservices
- Automated regression testing
- Load and stress testing

### 5. Deployment & CI/CD

**Gratis**:
- Manual deployment
- Basic GitHub Actions

**Hemat**:
- Automated CI/CD pipelines
- Staging and production environments
- Blue/green deployments

**Berkembang**:
- GitOps with ArgoCD/Flux
- Canary deployments
- Feature flags
- A/B testing infrastructure
- Multi-environment deployments
- Compliance & security auditing in pipeline

## Kesimpulan

Dengan pendekatan ini, Sellzio SaaS memulai dengan NestJS sebagai framework backend dari tahap awal, memungkinkan transisi yang mulus ke arsitektur yang lebih kompleks seiring pertumbuhan platform. Bahasa TypeScript tetap konsisten di semua tahap, memberikan type safety dan pengalaman developer yang unggul, sementara arsitektur berevolusi dari monolitik sederhana menjadi sistem microservices yang terdistribusi dan tangguh.

Perhatikan bahwa meskipun NestJS digunakan sejak awal, kompleksitas implementasinya tetap disesuaikan dengan tahap perkembangan platform, memastikan tidak ada over-engineering di tahap awal, namun tetap mempertahankan jalur migrasi yang jelas untuk ekspansi di masa depan.


# Struktur Domain dan Folder untuk Sellzio SaaS Multi-level E-commerce

Berdasarkan kebutuhan platform multi-level Anda, saya akan merancang struktur domain dan folder yang sesuai untuk menangani:
1. Admin platform (pemilik SaaS)
2. Tenant (Tenant) dengan domain/subdomain sendiri
3. Store yang beroperasi di bawah Tenant
4. Pembeli/Customer yang bisa menjadi affiliate

## Struktur Domain & Routing

### 1. Admin Platform (Anda sebagai pemilik)

**Domain**: `sellzio.com` (domain utama)

**Endpoint Admin**:
- Admin Panel: `admin.sellzio.com` atau `sellzio.com/admin`
- Admin Login: `admin.sellzio.com/login` atau `sellzio.com/admin/login`
- Admin Dashboard: `admin.sellzio.com/dashboard` atau `sellzio.com/admin/dashboard`

**Endpoint Tenant (di domain utama)**:
- Tenant Registration: `app.sellzio.com/register` atau `sellzio.com/Tenant/register`
- Tenant Login: `app.sellzio.com/login` atau `sellzio.com/Tenant/login`
- Tenant Subscription Plans: `sellzio.com/pricing`

### 2. Tenant (Tenant)

**Domain Options**:
1. **Custom Domain**: `Tenant-brand.com` (domain sendiri yang di-point ke platform Anda)
2. **Subdomain**: `Tenant-brand.sellzio.com` (subdomain dari domain utama Anda)

**Endpoint Tenant Dashboard**:
- Dashboard: `admin.Tenant-brand.com` atau `Tenant-brand.com/admin`
- Login Panel: `admin.Tenant-brand.com/login` atau `Tenant-brand.com/admin/login`

**Storefront/E-commerce**:
- Homepage: `Tenant-brand.com` atau `Tenant-brand.sellzio.com`
- Product Listing: `Tenant-brand.com/products` atau `Tenant-brand.sellzio.com/products`
- Product Detail: `Tenant-brand.com/products/[product-id]`

### 3. Store (User Tenant)

Stores beroperasi di bawah tenant, sehingga mereka menggunakan domain tenant.

**Store Management**:
- Store Registration: `Tenant-brand.com/store/register`
- Store Login: `Tenant-brand.com/store/login`
- Store Dashboard: `Tenant-brand.com/store/dashboard`

**Store Frontend**:
- Store Page: `Tenant-brand.com/stores/[store-id]`
- Store Products: `Tenant-brand.com/stores/[store-id]/products`

### 4. Pembeli/Customer

Pembeli juga beroperasi di level tenant (e-commerce).

**Customer Management**:
- Registration: `Tenant-brand.com/register` atau `Tenant-brand.com/customer/register`
- Login: `Tenant-brand.com/login` atau `Tenant-brand.com/customer/login`
- Dashboard: `Tenant-brand.com/account` atau `Tenant-brand.com/customer/dashboard`

**Affiliate Management**:
- Affiliate Registration: `Tenant-brand.com/affiliate/register`
- Affiliate Dashboard: `Tenant-brand.com/affiliate/dashboard`

## Struktur Folder NestJS (Backend)

Berdasarkan model multi-tenant di atas, berikut struktur folder yang direkomendasikan untuk backend NestJS:

```
src/
├── main.ts                       # Entry point
├── app.module.ts                 # Root module
├── common/                       # Shared utilities
│   ├── decorators/               # Custom decorators
│   │   └── tenant.decorator.ts   # Tenant context decorator
│   ├── guards/                   # Guards
│   │   ├── auth.guard.ts         # Authentication guard
│   │   ├── roles.guard.ts        # Role-based guard
│   │   └── tenant.guard.ts       # Tenant-specific guard
│   ├── interceptors/             # Interceptors
│   │   └── tenant.interceptor.ts # Tenant context interceptor
│   └── middleware/               # Middleware
│       ├── domain-parser.middleware.ts # Domain resolution
│       └── tenant-resolver.middleware.ts # Tenant identification
├── config/                       # Configuration
│   ├── app.config.ts             # App configuration
│   └── tenant.config.ts          # Tenant configuration
├── database/                     # Database modules
│   ├── prisma.service.ts         # Prisma service
│   └── tenant-connection.service.ts # Multi-tenant DB connection
├── admin/                        # Admin area modules
│   ├── admin.module.ts           # Admin module
│   ├── admin.controller.ts       # Admin controller
│   ├── admin.service.ts          # Admin service
│   ├── tenants/                  # Tenant management (by admin)
│   │   ├── tenants.controller.ts
│   │   └── tenants.service.ts
│   ├── dashboard/                # Admin dashboard
│   │   ├── dashboard.controller.ts
│   │   └── dashboard.service.ts
│   └── settings/                 # Admin settings
│       ├── settings.controller.ts
│       └── settings.service.ts
├── auth/                         # Authentication
│   ├── auth.module.ts            # Auth module
│   ├── strategies/               # Auth strategies
│   │   ├── jwt.strategy.ts       # JWT strategy
│   │   └── local.strategy.ts     # Local strategy
│   ├── guards/                   # Auth-specific guards
│   │   └── tenant-auth.guard.ts  # Tenant auth guard
│   └── tenant-auth.service.ts    # Multi-tenant auth service
├── tenant/                       # Tenant (Tenant) modules
│   ├── tenant.module.ts          # Tenant module
│   ├── tenant.controller.ts      # Tenant controller
│   ├── tenant.service.ts         # Tenant service
│   ├── dashboard/                # Tenant dashboard
│   │   ├── dashboard.controller.ts
│   │   └── dashboard.service.ts
│   ├── stores/                   # Store management
│   │   ├── stores.controller.ts
│   │   └── stores.service.ts
│   ├── products/                 # Product management
│   │   ├── products.controller.ts
│   │   └── products.service.ts
│   └── settings/                 # Tenant settings
│       ├── settings.controller.ts
│       └── settings.service.ts
├── store/                        # Store modules
│   ├── store.module.ts           # Store module
│   ├── store.controller.ts       # Store controller
│   ├── store.service.ts          # Store service
│   ├── dashboard/                # Store dashboard
│   │   ├── dashboard.controller.ts
│   │   └── dashboard.service.ts
│   ├── products/                 # Store products
│   │   ├── products.controller.ts
│   │   └── products.service.ts
│   └── orders/                   # Store orders
│       ├── orders.controller.ts
│       └── orders.service.ts
├── customer/                     # Customer modules
│   ├── customer.module.ts        # Customer module
│   ├── customer.controller.ts    # Customer controller 
│   ├── customer.service.ts       # Customer service
│   ├── account/                  # Customer account
│   │   ├── account.controller.ts
│   │   └── account.service.ts
│   ├── orders/                   # Customer orders
│   │   ├── orders.controller.ts
│   │   └── orders.service.ts
│   └── affiliate/                # Affiliate program
│       ├── affiliate.controller.ts
│       └── affiliate.service.ts
└── storefront/                   # Public storefront APIs
    ├── storefront.module.ts      # Storefront module
    ├── storefront.controller.ts  # Storefront controller
    ├── storefront.service.ts     # Storefront service
    ├── products/                 # Product listing/details
    │   ├── products.controller.ts
    │   └── products.service.ts
    ├── cart/                     # Shopping cart
    │   ├── cart.controller.ts
    │   └── cart.service.ts
    └── checkout/                 # Checkout process
        ├── checkout.controller.ts
        └── checkout.service.ts
```

## Implementasi Multi-Tenant di NestJS

Kunci utama dalam implementasi multi-tenant adalah kemampuan untuk:
1. Mendeteksi domain/subdomain
2. Mengidentifikasi tenant berdasarkan domain
3. Mengatur konteks tenant untuk setiap request
4. Memfilter data berdasarkan tenant saat ini

### Domain Parser Middleware

```typescript
// common/middleware/domain-parser.middleware.ts
@Injectable()
export class DomainParserMiddleware implements NestMiddleware {
  constructor(private configService: ConfigService) {}

  use(req: Request, res: Response, next: Function) {
    const hostname = req.headers.host || '';
    
    // Mengecualikan admin panel
    if (hostname.startsWith('admin.')) {
      req.isAdminPanel = true;
      next();
      return;
    }
    
    // Mengecualikan app panel (Tenant registration/login)
    if (hostname.startsWith('app.')) {
      req.isAppPanel = true;
      next();
      return;
    }
    
    // Mengecek apakah menggunakan custom domain atau subdomain
    const mainDomain = this.configService.get('MAIN_DOMAIN');
    
    if (hostname.endsWith(`.${mainDomain}`)) {
      // Subdomain tenant
      const subdomain = hostname.replace(`.${mainDomain}`, '');
      req.tenantIdentifier = subdomain;
      req.tenantIdentifierType = 'subdomain';
    } else if (hostname !== mainDomain) {
      // Custom domain tenant
      req.tenantIdentifier = hostname;
      req.tenantIdentifierType = 'domain';
    }
    
    next();
  }
}
```

### Tenant Resolver Middleware

```typescript
// common/middleware/tenant-resolver.middleware.ts
@Injectable()
export class TenantResolverMiddleware implements NestMiddleware {
  constructor(private tenantService: TenantService) {}

  async use(req: Request, res: Response, next: Function) {
    // Skip untuk admin panel
    if (req.isAdminPanel || req.isAppPanel) {
      next();
      return;
    }
    
    if (req.tenantIdentifier) {
      try {
        // Mencari tenant berdasarkan domain/subdomain
        const tenant = await this.tenantService.findByIdentifier(
          req.tenantIdentifier,
          req.tenantIdentifierType,
        );
        
        if (tenant) {
          // Menyimpan info tenant ke request object
          req.tenant = tenant;
          
          // Menyimpan tenant context untuk digunakan di service
          TenantContext.setCurrentTenant(tenant);
        } else {
          // Tenant tidak ditemukan
          res.status(404).send('Tenant not found');
          return;
        }
      } catch (error) {
        // Handle error
        res.status(500).send('Error resolving tenant');
        return;
      }
    }
    
    next();
  }
}
```

### Tenant Context Service

```typescript
// common/services/tenant-context.service.ts
@Injectable({ scope: Scope.REQUEST })
export class TenantContextService {
  private tenant: Tenant | null = null;

  constructor(@Inject(REQUEST) private request: Request) {
    // Mengambil tenant dari request object yang diset oleh middleware
    this.tenant = request.tenant || null;
  }

  getTenant(): Tenant | null {
    return this.tenant;
  }

  getTenantId(): string | null {
    return this.tenant?.id || null;
  }
}
```

### Tenant Guard untuk Endpoint Protection

```typescript
// common/guards/tenant.guard.ts
@Injectable()
export class TenantGuard implements CanActivate {
  constructor(
    private tenantContextService: TenantContextService,
    private reflector: Reflector,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const currentTenant = this.tenantContextService.getTenant();
    
    // Jika tidak ada tenant yang terdeteksi, tolak akses
    if (!currentTenant) {
      return false;
    }
    
    // Bisa menambahkan logic tambahan:
    // - Cek tenant status (active/inactive)
    // - Cek tenant subscription valid
    // - dll.
    
    return true;
  }
}
```

## Frontend Routing dengan Next.js

Untuk Next.js frontend, saya akan menunjukkan struktur folder untuk mendukung multi-tenant:

```
src/
├── pages/                       # Next.js pages
│   ├── _app.tsx                 # Custom App component
│   ├── _document.tsx            # Custom Document
│   ├── index.tsx                # Main landing page
│   ├── admin/                   # Admin portal pages
│   │   ├── index.tsx            # Admin dashboard
│   │   ├── login.tsx            # Admin login
│   │   ├── tenants/             # Tenant management
│   │   │   ├── index.tsx        # List tenants
│   │   │   ├── [id]/            # Tenant details
│   │   │   │   ├── index.tsx    # Tenant overview
│   │   │   │   └── settings.tsx # Tenant settings
│   ├── app/                     # Tenant portal
│   │   ├── register.tsx         # Tenant registration
│   │   ├── login.tsx            # Tenant login
│   │   └── pricing.tsx          # Subscription plans
│   ├── tenant/                  # Tenant dashboard (via middleware)
│   │   ├── index.tsx            # Tenant dashboard
│   │   ├── login.tsx            # Tenant login
│   │   ├── stores/              # Store management
│   │   ├── products/            # Product management
│   │   └── settings/            # Tenant settings
│   ├── store/                   # Store dashboard (via middleware)
│   │   ├── register.tsx         # Store registration
│   │   ├── login.tsx            # Store login 
│   │   ├── dashboard/           # Store dashboard
│   │   │   ├── index.tsx        # Dashboard overview
│   │   │   ├── products/        # Product management
│   │   │   └── orders/          # Order management
│   ├── customer/                # Customer pages (via middleware)
│   │   ├── register.tsx         # Customer registration
│   │   ├── login.tsx            # Customer login
│   │   ├── account/             # Customer account
│   │   ├── orders/              # Customer orders
│   │   └── affiliate/           # Affiliate dashboard
│   ├── [tenant]/                # Dynamic tenant storefront (custom domain)
│   │   ├── index.tsx            # Tenant homepage
│   │   ├── products/            # Product listings
│   │   │   ├── index.tsx        # All products
│   │   │   └── [id].tsx         # Product details
│   │   ├── stores/              # Store listings
│   │   │   ├── index.tsx        # All stores
│   │   │   └── [id]/            # Store details
│   │   │       ├── index.tsx    # Store homepage
│   │   │       └── products.tsx # Store products
│   │   ├── cart.tsx             # Shopping cart
│   │   └── checkout.tsx         # Checkout
├── middleware.ts                # Next.js middleware for tenant routing
```

### Next.js Middleware

```typescript
// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

const MAIN_DOMAIN = 'sellzio.com';
const ADMIN_SUBDOMAIN = 'admin';
const APP_SUBDOMAIN = 'app';

export async function middleware(request: NextRequest) {
  const { pathname, hostname } = request.nextUrl;
  
  // Handle admin panel (admin.sellzio.com)
  if (hostname.startsWith(`${ADMIN_SUBDOMAIN}.`)) {
    // Redirect admin routes
    if (!pathname.startsWith('/admin')) {
      return NextResponse.rewrite(
        new URL(`/admin${pathname === '/' ? '' : pathname}`, request.url)
      );
    }
    return NextResponse.next();
  }
  
  // Handle app panel (app.sellzio.com) for Tenant registration/login
  if (hostname.startsWith(`${APP_SUBDOMAIN}.`)) {
    // Redirect app routes
    if (!pathname.startsWith('/app')) {
      return NextResponse.rewrite(
        new URL(`/app${pathname === '/' ? '' : pathname}`, request.url)
      );
    }
    return NextResponse.next();
  }
  
  // Handle tenant subdomains (tenant-name.sellzio.com)
  if (hostname.endsWith(`.${MAIN_DOMAIN}`) && !hostname.startsWith(`${ADMIN_SUBDOMAIN}.`) && !hostname.startsWith(`${APP_SUBDOMAIN}.`)) {
    const subdomain = hostname.replace(`.${MAIN_DOMAIN}`, '');
    
    // Handle tenant dashboard routes
    if (pathname.startsWith('/admin')) {
      return NextResponse.rewrite(
        new URL(`/tenant${pathname === '/admin' ? '' : pathname.substring(6)}`, request.url)
      );
    }
    
    // Handle store dashboard routes
    if (pathname.startsWith('/store')) {
      return NextResponse.rewrite(
        new URL(pathname, request.url)
      );
    }
    
    // Handle customer routes
    if (pathname.startsWith('/customer')) {
      return NextResponse.rewrite(
        new URL(pathname, request.url)
      );
    }
    
    // Handle tenant storefront (all other routes)
    return NextResponse.rewrite(
      new URL(`/${subdomain}${pathname}`, request.url)
    );
  }
  
  // Handle custom domains
  if (hostname !== MAIN_DOMAIN && !hostname.endsWith(`.${MAIN_DOMAIN}`)) {
    // Here you would need to look up the tenant by custom domain
    // For simplicity, we'll use a placeholder
    const tenantId = 'custom-domain-tenant';
    
    // Similar logic as subdomains
    if (pathname.startsWith('/admin')) {
      return NextResponse.rewrite(
        new URL(`/tenant${pathname === '/admin' ? '' : pathname.substring(6)}`, request.url)
      );
    }
    
    // All other routes go to tenant storefront
    return NextResponse.rewrite(
      new URL(`/${tenantId}${pathname}`, request.url)
    );
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    // Apply to all paths except static files, API routes, etc.
    '/((?!_next/|api/|favicon.ico).*)',
  ],
};
```

## Konfigurasi Domain untuk Prod Environment

Untuk implementasi produksi, Anda memerlukan:

1. **DNS Configuration**:
   - Untuk domain utama (`sellzio.com`) - A/AAAA record ke server Anda
   - Untuk subdomain admin (`admin.sellzio.com`) - CNAME ke server Anda
   - Untuk subdomain app (`app.sellzio.com`) - CNAME ke server Anda
   - Wildcard subdomain (`*.sellzio.com`) - CNAME ke server Anda untuk tenant subdomains

2. **Custom Domain Tenants**:
   - Tenant perlu mengatur A/CNAME record dari domain mereka ke IP/domain server Anda
   - Implementasi verifikasi domain ownership di panel admin

3. **SSL Certificate**:
   - Wildcard SSL certificate (*.sellzio.com)
   - Dynamic SSL provisioning untuk custom domains (Let's Encrypt)

## Kesimpulan & Rekomendasi

Untuk Sellzio SaaS dengan model multi-level (Admin → Tenant → Store → Customer), struktur yang saya rekomendasikan adalah:

1. **Admin Platform**: `admin.sellzio.com` atau `sellzio.com/admin`
   - Mengelola semua tenant (Tenant)
   - Mengatur konfigurasi global platform

2. **Tenant Registration/Login**: `app.sellzio.com`
   - Panel khusus untuk onboarding Tenant baru
   - Registration, login, dan dashboard awal

3. **Tenant E-commerce**: `tenant-name.sellzio.com` atau `tenant-custom-domain.com`
   - Storefront untuk setiap Tenant
   - Admin panel: `tenant-name.sellzio.com/admin` atau `admin.tenant-custom-domain.com`

4. **Store Portal**: `tenant-name.sellzio.com/store` atau `tenant-custom-domain.com/store`
   - Registration, login, dan dashboard untuk store owners
   - Store pages: `tenant-name.sellzio.com/stores/[store-id]`

5. **Customer Portal**: `tenant-name.sellzio.com/customer` atau `tenant-custom-domain.com/customer`
   - Registration, login, dan account management untuk customers
   - Affiliate dashboard: `tenant-name.sellzio.com/affiliate`

Pendekatan ini memberikan:
- Pemisahan yang jelas antara platform admin dan tenant storefronts
- Fleksibilitas untuk tenant menggunakan subdomain atau custom domain
- Path routing yang intuitif untuk setiap user type (admin, Tenant, store, customer)
- Struktur backend yang modular dan scalable dengan NestJS

Implementasi ini mendukung penuh model bisnis e-commerce multi-level Anda, dimana Anda sebagai pemilik platform menyediakan infrastruktur untuk Tenant, yang kemudian dapat memiliki Store dan Customer mereka sendiri, termasuk program affiliate.

# Dashboard Comprehensive Design untuk Sellzio SaaS Multi-level E-commerce

Berikut adalah desain komprehensif untuk dashboard di setiap level platform Sellzio SaaS:

## 1. Dashboard Admin (Platform Owner)

### Sidebar Menu
- **Dashboard** (Overview)
- **Tenants** (Tenants)
  - All Tenants
  - Add New Tenant
  - Subscription Plans
  - Pending Approvals
- **Stores** (Cross-tenant view)
  - All Stores
  - Store Categories
  - Store Verification
- **Products** (Cross-tenant view)
  - All Products
  - Categories
  - Tags
- **Users** (Cross-tenant view)
  - All Users
  - User Roles
- **Transactions**
  - Revenue Overview
  - Commission Reports
  - Payouts
  - Invoices
- **Marketplace Settings**
  - General Settings
  - Payment Gateways
  - Shipping Integrations
  - Tax Settings
- **Platform Content**
  - Landing Pages
  - Blog
  - Help Center
- **Design & Themes**
  - Theme Gallery
  - Theme Editor
  - Custom CSS
- **Developer**
  - API Keys
  - Webhooks
  - Logs
- **Settings**
  - Platform Settings
  - Admin Users
  - Security
  - Audit Logs

### Dashboard Overview Page
- **Quick Stats**
  - Total Tenants (Tenants)
  - Total Stores
  - Total Products
  - Total End Users
  - Platform GMV (Gross Merchandise Value)
  - Platform Revenue
  - New Tenants This Month
  - New Stores This Month

- **Performance Metrics**
  - Revenue Growth Chart (Monthly)
  - User Acquisition Chart
  - Top-performing Tenants
  - Commission Distribution Chart

- **Health Monitoring**
  - System Status
  - Pending Approvals
  - Support Tickets
  - Failed Transactions

- **Recent Activity**
  - New Tenant Registrations
  - Recent Transactions
  - Support Requests
  - System Logs

### Tenant Management
- **Tenant List** with:
  - Tenant Name/Brand
  - Domain/Subdomain
  - Subscription Plan
  - Store Count
  - User Count
  - GMV
  - Status (Active/Inactive)
  - Revenue Share
  - Actions (Edit, Suspend, Login as)

- **Tenant Details**:
  - Profile & Settings
  - Performance Metrics
  - Revenue Sharing Settings
  - Commission Structure
  - Stores Under This Tenant
  - User Management
  - Subscription Management
  - Billing History
  - White Label Settings
  - Theme Configuration
  - Domain Settings

### Platform Settings
- **Subscription Plans**:
  - Plan Creation & Management
  - Feature Matrix
  - Pricing Structure
  - Commission Rates per Plan
  - Storage Limits
  - API Limits

- **Global Settings**:
  - Platform Fees
  - Commission Tiers
  - Default Policies
  - Email Templates
  - Notification Settings

## 2. Dashboard Tenant (Tenant)

### Sidebar Menu
- **Dashboard** (Overview)
- **Stores**
  - All Stores
  - Add New Store
  - Store Categories
  - Store Applications
  - Store Settings
- **Products**
  - All Products
  - Categories
  - Brands
  - Product Attributes
  - Import/Export
- **Orders**
  - All Orders
  - Order Fulfillment
  - Returns & Refunds
  - Shipping Settings
- **Users**
  - Customers
  - Store Owners
  - Affiliates
  - Staff Members
- **Marketing**
  - Promotions & Coupons
  - Flash Sales
  - Email Campaigns
  - SEO Settings
- **Content**
  - Homepage Layout
  - Banners & Sliders
  - Pages
  - Navigation Menus
  - Blog
- **Live Commerce**
  - Scheduled Live Sessions
  - Past Recordings
  - Live Settings
- **Affiliate Program**
  - Program Settings
  - Affiliate Tiers
  - Commission Rules
  - Affiliate Applications
  - Payouts
- **Reports**
  - Sales Reports
  - Store Performance
  - Product Performance
  - Customer Analytics
  - Affiliate Performance
- **Financial**
  - Revenue Overview
  - Transactions
  - Fee Settings
  - Payouts
  - Invoices
- **Settings**
  - General Settings
  - White Label
  - Domain Settings
  - Payment Methods
  - Email Templates
  - Notification Settings
  - Staff Accounts

### Dashboard Overview Page
- **Quick Stats**
  - Total Revenue
  - Total Orders
  - Total Stores
  - Total Customers
  - Active Affiliates
  - Conversion Rate
  - Average Order Value
  - Return Rate

- **Performance Charts**
  - Sales Trend (Daily/Weekly/Monthly)
  - Top Selling Products
  - Top Performing Stores
  - Traffic Sources
  - Order Status Distribution

- **Recent Activity**
  - New Store Applications
  - Recent Orders
  - New Customer Registrations
  - Recent Reviews
  - Latest Affiliate Signups

- **Attention Required**
  - Low Stock Alerts
  - Pending Store Approvals
  - Pending Refunds
  - Flagged Products
  - Support Tickets

### Store Management
- **Store List** with:
  - Store Name
  - Owner
  - Products Count
  - Sales
  - Commission Rate
  - Status
  - Performance Rating
  - Actions (Edit, Login as, Suspend)

- **Store Approval Process**:
  - Application Review
  - Store Information
  - Product Samples
  - Documentation
  - Agreement Terms
  - Approve/Reject with Comments

### Affiliate Program Settings
- **Commission Structure**:
  - Global Commission Rates
  - Category-specific Rates
  - Store-specific Rates
  - Tiered Commission Structure
  - Performance Bonuses
  - Payout Schedule
  - Minimum Payout Amount

- **Affiliate Rules**:
  - Approval Requirements
  - Performance Metrics
  - Forbidden Products/Categories
  - Link Usage Guidelines
  - Content Guidelines

### White Label & Branding
- **Domain Settings**:
  - Custom Domain Setup
  - SSL Configuration
  - Subdomain Management
  - Domain Verification

- **Branding Settings**:
  - Logo Upload
  - Color Scheme
  - Typography
  - Favicon
  - Email Branding
  - Invoice Branding

- **Theme Settings**:
  - Theme Selection
  - Layout Customization
  - Mobile Optimization
  - Custom CSS/JS

## 3. Dashboard Store (Store Owner)

### Sidebar Menu
- **Dashboard** (Overview)
- **Products**
  - All Products
  - Add New Product
  - Categories
  - Collections
  - Inventory
  - Digital Products
- **Orders**
  - All Orders
  - Processing
  - Shipped
  - Completed
  - Cancelled
  - Returns
- **Customers**
  - All Customers
  - Customer Groups
  - Customer Reviews
- **Marketing**
  - Discounts & Promotions
  - Flash Sales
  - Bundle Deals
  - Loyalty Program
- **Affiliate Program**
  - Overview
  - Manage Affiliates
  - Commission Settings
  - Affiliate Performance
  - Payouts
- **Live Selling**
  - Schedule Live Session
  - Past Live Sessions
  - Live Selling Settings
- **Analytics**
  - Sales Reports
  - Product Performance
  - Customer Insights
  - Affiliate Conversions
- **Financials**
  - Revenue Overview
  - Transaction History
  - Payout History
  - Fee Breakdown
- **Store Settings**
  - Store Profile
  - Shipping Methods
  - Payment Methods
  - Staff Accounts
  - Notifications
  - API Integration

### Dashboard Overview Page
- **Quick Stats**
  - Today's Sales
  - Total Orders
  - Average Order Value
  - Conversion Rate
  - Total Products
  - Products Sold
  - Affiliate Revenue
  - Net Profit

- **Performance Charts**
  - Sales Trend
  - Top Selling Products
  - Customer Acquisition
  - Traffic Sources
  - Order Status Distribution
  - Affiliate Performance

- **Recent Activity**
  - Latest Orders
  - Recent Customer Reviews
  - New Affiliate Applications
  - Product Stock Alerts
  - Return Requests

- **To-Do List**
  - Orders to Fulfill
  - Reviews to Respond
  - Low Stock Items
  - Expiring Promotions
  - Affiliate Payouts Due

### Product Management
- **Product List** with:
  - Product Name
  - SKU
  - Category
  - Price
  - Inventory
  - Status
  - Sales Count
  - Actions (Edit, Delete, Duplicate)

- **Product Details Form**:
  - Basic Information
  - Pricing
  - Inventory
  - Images & Media
  - Categories & Tags
  - Attributes & Variations
  - SEO Settings
  - Shipping Information
  - Affiliate Commission Rate

### Discount & Promotion Management
- **Discount Types**:
  - Percentage Off
  - Fixed Amount Off
  - Buy X Get Y
  - Volume Discounts
  - Free Shipping
  - Gift with Purchase

- **Promotion Settings**:
  - Start/End Dates
  - Usage Limits
  - Minimum Purchase Requirements
  - Customer Group Targeting
  - Coupon Codes
  - Stackability Rules

### Affiliate Commission Settings
- **Commission Structure**:
  - Default Commission Rate
  - Product-specific Rates
  - Category-specific Rates
  - Tiered Commission (based on sales volume)
  - Performance Bonuses
  - Special Promotions

- **Affiliate Rules**:
  - Affiliate Approval Settings
  - Prohibited Marketing Methods
  - Cookie Duration
  - Attribution Rules
  - Payout Thresholds
  - Terms & Conditions

## 4. Dashboard User (Customer/Affiliate/Store Applicant)

### Sidebar Menu (Basic Customer)
- **Dashboard** (Overview)
- **Orders**
  - Order History
  - Ongoing Orders
  - Returns & Refunds
- **Wishlist**
- **Reviews**
- **Address Book**
- **Payment Methods**
- **Account Settings**
- **Notifications**

### Additional Sidebar Items (For Affiliate)
- **Affiliate Dashboard**
  - Performance Overview
  - Marketing Materials
  - Commission History
  - Payout Settings
  - Affiliate Links
  - Promotional Content
  - Reports & Analytics

### Additional Sidebar Items (For Store Applicant)
- **Store Application**
  - Application Status
  - Store Setup
  - Product Management
  - Settings
  - Launch Checklist

### Dashboard Overview Page (Basic Customer)
- **Quick Access**
  - Recent Orders
  - Saved Products
  - Track Packages
  - Support Tickets
  - Account Balance

- **Order Status**
  - Processing Orders
  - Shipped Orders
  - Delivered Recently
  - Return Status

- **Personalized Recommendations**
  - Based on Past Purchases
  - Wishlist Items on Sale
  - New Arrivals in Favorite Categories

- **Account Activity**
  - Recent Logins
  - Recent Reviews
  - Recent Purchases
  - Profile Completion

### Affiliate Dashboard (When Registered as Affiliate)
- **Performance Metrics**
  - Total Earnings
  - Pending Commissions
  - Conversion Rate
  - Click-through Rate
  - Top Performing Products
  - Traffic Sources

- **Commission Details**
  - Earnings by Store
  - Earnings by Product
  - Commission Rates
  - Payment History
  - Upcoming Payments

- **Marketing Tools**
  - Affiliate Links Generator
  - Banner Ads
  - Product Widgets
  - Email Templates
  - Social Media Content
  - QR Codes

- **Promotional Content**
  - Product Descriptions
  - Product Images
  - Video Content
  - Discount Codes
  - Special Offers
  - Limited Time Deals

### Store Application (When Applied to be a Store)
- **Application Status**
  - Current Stage
  - Requirements Checklist
  - Verification Status
  - Reviewer Comments
  - Expected Timeline

- **Store Setup**
  - Store Profile
  - Brand Assets
  - Policies & Terms
  - Shipping Settings
  - Payment Settings
  - Tax Information

- **Product Management**
  - Product Drafts
  - Sample Products
  - Product Categories
  - Pricing Strategy
  - Inventory Planning

- **Launch Checklist**
  - Required Documents
  - Store Branding
  - Initial Products (Minimum Required)
  - Shipping Methods Setup
  - Payment Methods Setup
  - Commission Agreement

## Recommended Additional Features

### 1. For Admin Dashboard
- **Fraud Detection System**
  - Suspicious Activity Monitoring
  - Transaction Flagging
  - IP Tracking
  - User Behavior Analysis
  - Automatic Alerts

- **Business Intelligence**
  - Advanced Analytics
  - Market Trends
  - Platform Benchmarks
  - Growth Opportunities
  - User Behavior Patterns

- **Multi-currency & Multi-language Support**
  - Currency Management
  - Exchange Rate Settings
  - Language Pack Management
  - Translation Interface

- **Compliance & Legal**
  - GDPR Compliance Tools
  - Tax Compliance
  - Terms & Conditions Templates
  - Privacy Policy Templates
  - Cookie Consent Management

### 2. For Tenant Dashboard
- **AI-powered Analytics**
  - Predictive Sales Forecasting
  - Customer Segmentation
  - Product Recommendations
  - Price Optimization
  - Trend Detection

- **Omnichannel Integration**
  - Social Media Integrations
  - Marketplace Connections (Amazon, eBay, etc.)
  - POS System Integration
  - Mobile App Companion

- **Content Marketing Suite**
  - Blog Management
  - SEO Optimization
  - Social Media Scheduling
  - Email Marketing Campaigns
  - Content Analytics

- **Vendor Management**
  - Supplier Directory
  - Purchase Orders
  - Inventory Integration
  - Performance Metrics
  - Communication Portal

### 3. For Store Dashboard
- **Inventory Management Enhanced**
  - Automated Restock Alerts
  - Supplier Management
  - Batch Tracking
  - Serial Number Tracking
  - Inventory Forecasting

- **Customer Relationship Management**
  - Customer Segmentation
  - Personalized Marketing
  - Customer Support Portal
  - Loyalty Program Management
  - Customer Lifetime Value Analysis

- **Tax & Accounting**
  - Sales Tax Automation
  - Financial Reporting
  - Expense Tracking
  - Invoice Management
  - Profit & Loss Statements

- **Mobile Selling App**
  - Order Management on the Go
  - Mobile Inventory Checking
  - Push Notifications
  - Mobile POS
  - Customer Profile Access

### 4. For User Dashboard
- **Loyalty & Rewards Program**
  - Points Balance
  - Rewards Catalog
  - Achievement Badges
  - Redemption History
  - Referral Program

- **Social Commerce Features**
  - Share Purchases
  - Group Buying
  - Social Media Integration
  - User-generated Content
  - Friend Recommendations

- **Enhanced Support System**
  - Live Chat
  - Ticket System
  - Knowledge Base
  - Video Tutorials
  - Community Forum

- **Personalization Engine**
  - Custom Product Collections
  - Saved Searches
  - Style Preferences
  - Size Profiles
  - Purchase History Analysis

## Key Integration Points

To ensure smooth operation between these dashboards:

### 1. Authentication & Authorization
- Single Sign-On (SSO) capability
- Role-based Access Control (RBAC)
- Permission inheritance (Admin → Tenant → Store)
- Multi-factor Authentication (MFA) for sensitive operations

### 2. Data Flow
- Real-time data synchronization
- Event-driven architecture for status updates
- Caching strategies for performance
- Data segregation per tenant

### 3. Notification System
- Cross-dashboard notification center
- Email, SMS, and push notification options
- Notification preferences per user role
- Action-required vs. informational notifications

### 4. Reporting & Analytics
- Roll-up reporting (Store → Tenant → Admin)
- Custom report builders at each level
- Exportable reports (CSV, Excel, PDF)
- Scheduled report delivery

### 5. White Labeling
- Cascading theme inheritance
- Brand asset management
- CSS/Design override capabilities
- Custom domain handling

## Dashboard Implementation Recommendations

For implementing these dashboards efficiently:

1. **Component-Based Architecture**
   - Build reusable UI components for common elements
   - Implement design system for consistency
   - Create shared widget library for stats, charts, etc.

2. **Responsive Design**
   - All dashboards should be fully responsive
   - Mobile-optimized views for key actions
   - Progressive Web App capabilities

3. **Permission System**
   - Granular permission settings
   - Feature visibility based on subscription plan
   - Contextual UI that adapts to user permissions

4. **Performance Optimization**
   - Lazy loading of dashboard sections
   - Data pagination and infinite scrolling
   - Background data fetching and caching
   - Optimistic UI updates

5. **Developer Experience**
   - Comprehensive API documentation
   - Webhook system for external integrations
   - Sandbox environment for testing
   - Plugin architecture for extendability

This comprehensive dashboard structure covers all the requirements for your multi-level e-commerce platform while providing room for growth and additional features as your platform evolves.

# Modern & Responsive UI Design for Sellzio SaaS Multi-level E-commerce Platform

Berikut adalah konsep UI yang modern dan responsif untuk platform Sellzio SaaS di berbagai perangkat (desktop, tablet, dan mobile). Saya akan fokus pada desain yang clean, intuitif, dan adaptif dengan tren UI/UX terkini.

## Design System Foundation

Sebelum masuk ke tampilan spesifik, mari tetapkan design system foundation:

### 1. Color Palette

**Primary Colors:**
- Primary: `#4F46E5` (Indigo yang vibrant)
- Secondary: `#14B8A6` (Teal yang menyegarkan)

**Neutral Colors:**
- Background Light: `#F9FAFB`
- Background Dark: `#1E293B`
- Text Primary: `#1E293B`
- Text Secondary: `#64748B`

**Accent Colors:**
- Success: `#10B981`
- Warning: `#FBBF24`
- Error: `#EF4444`
- Info: `#3B82F6`

### 2. Typography

- **Headings**: Inter (sans-serif)
- **Body**: Inter (sans-serif)
- **Monospace**: JetBrains Mono (untuk code atau data teknis)

**Scale:**
- H1: 32px (mobile: 24px)
- H2: 24px (mobile: 20px)
- H3: 20px (mobile: 18px)
- Body: 16px (mobile: 14px)
- Small: 14px (mobile: 12px)

### 3. Layout System

- **Container Spacing**: 24px (desktop), 16px (tablet), 12px (mobile)
- **Grid System**: 12-column responsive grid
- **Component Spacing**: 8px increments (8px, 16px, 24px, 32px, etc.)
- **Border Radius**: 8px (cards), 12px (buttons), 4px (small elements)
- **Elevation**: Subtle shadow system with 4 levels

## 1. Admin Dashboard Modern UI

### Desktop Version

**Header & Navigation:**
- **Top Bar**: Clean, minimal header with platform logo (left), search bar (center), and profile menu with notifications (right)
- **Side Navigation**: Collapsed by default on very large screens, expanded on regular desktop showing icons and text
- **Dark/Light Mode Toggle**: In the bottom corner of side navigation

**Dashboard Overview:**
- **Header Section**: Large welcome message with date, quick actions buttons
- **Stats Cards**: Row of 4-5 cards with key metrics, subtle gradient backgrounds, and trend indicators
- **Main Content Area**: 2-column layout with:
  - Left (wider): Main data visualization (chart showing platform GMV over time)
  - Right (narrower): Activity feed and quick tasks list
- **Data Visualization**: Modern, interactive charts with clean axes, tooltip overlays, and animation
- **Recent Activity**: Timeline-style interface with color-coded activity types

**Tenant Management:**
- **Data Table**: Modern borderless table with:
  - Sticky header
  - Column sorting/filtering controls
  - Inline actions (dropdown)
  - Bulk selection
  - Pagination controls as minimal "load more" button
- **Card View Option**: Toggle between table and card view
- **Search & Filter**: Expandable advanced filter panel that slides down

**Responsive Behaviors:**
- Sticky header that minimizes on scroll
- Side navigation that auto-collapses when content needs more space
- Data tables that horizontally scroll within their container while keeping first column visible

### Tablet Version

- Side navigation collapses to icon-only by default
- Stats cards rearrange to 2×2 grid
- 2-column layout becomes 1-column stacked layout
- Data tables gain horizontal scroll with pinned first column
- Filter panels convert to modal dialogs

### Mobile Version

- Side navigation becomes a bottom tab bar with 5 main sections
- Less frequently used menu items move to a "More" menu
- Stats cards stack vertically as full-width cards
- Data tables redesign to card-based list views optimized for smaller screens
- Action menus consolidate into floating action buttons (FAB)
- Key actions remain accessible; secondary actions move to overflow menus

## 2. Tenant (Tenant) Dashboard Modern UI

### Desktop Version

**Branding & Customization:**
- **White Label Header**: Customizable with tenant's logo, colors, and brand elements
- **Theme Controls**: Visual theme editor with live preview
- **Custom Domain Indicator**: Subtle indication of current domain/subdomain

**Dashboard Overview:**
- **Metrics Cards**: Clean, modern cards with primary KPIs and micro-charts
- **Revenue Chart**: Large, interactive area chart with date range selector
- **Store Performance**: Horizontal bar chart showing top performing stores
- **Activity & Alerts**: Smart notification area with categorized alerts

**Store Management:**
- **Store Directory**: Grid view with:
  - Store cards showing thumbnail, rating, product count
  - Quick stats overlay on hover
  - Filter sidebar with instant results
  - Sort controls with visual indicators
- **Store Approval Interface**: Split-screen design:
  - Left: Application details
  - Right: Preview of store as it would appear to customers

**Affiliate Program Management:**
- **Commission Structure Builder**: Visual, drag-and-drop interface for creating commission tiers
- **Performance Dashboard**: Leaderboard of top affiliates with performance metrics
- **Rule Configuration**: Step-by-step wizard for setting up program rules

### Tablet Version

- Store directory switches to 2-column grid
- Split-screen interfaces convert to tabbed interfaces
- Side filters become modal filters accessed via button
- Charts scale to full width with simplified data points

### Mobile Version

- Critical KPIs remain as cards at top
- Secondary metrics accessible via horizontal scroll
- Store directory becomes 1-column scrollable list
- Multi-step processes convert to carousel/stepper pattern
- Bottom sheet dialogs for quick actions
- Fixed position FAB for primary actions

## 3. Store Dashboard Modern UI

### Desktop Version

**Product Management:**
- **Product Gallery**: Masonry grid layout showing product images, stock status, and pricing
- **Quick Edit**: Inline editing for common fields directly from gallery view
- **Drag-and-Drop Categorization**: Visual interface for organizing products into categories/collections
- **Bulk Actions**: Floating action panel appears when multiple items selected

**Order Management:**
- **Order Pipeline**: Kanban-style board showing orders by status (New, Processing, Shipped, Delivered)
- **Order Details**: Slide-in panel showing order details without leaving main view
- **Action Buttons**: Contextual actions based on order status
- **Customer Info**: Quick access to customer details and order history

**Affiliate Management:**
- **Affiliate Dashboard**: Clean layout with:
  - Top sections showing affiliate stats and earnings
  - Middle section with product performance analytics
  - Bottom section with affiliate activity feed
- **Commission Control Panel**: Visual slider controls for setting rates

**Live Selling Interface:**
- **Pre-Stream Setup**: Checklist-style preparation panel
- **Stream Control**: Streamlined broadcasting controls
- **Product Showcasing**: Drag-and-drop interface to queue products for showcase
- **Chat Monitoring**: Side panel with live chat, reactions, and purchase indicators

### Tablet Version

- Product gallery adjusts to 3-column grid
- Kanban board converts to horizontally scrollable lanes
- Side panels become full-screen overlays with back button
- Controls consolidate where appropriate

### Mobile Version

- Product gallery becomes scrollable 2-column grid
- Order pipeline converts to tabbed interface by status
- Affiliate stats stack vertically with swipeable card interface
- Stream controls redesigned for one-handed operation
- Important actions remain visible; secondary actions in overflow menu

## 4. User (Customer/Affiliate/Store) Dashboard Modern UI

### Desktop Version

**Customer Dashboard:**
- **Order Tracking**: Visual timeline showing package journey with map integration
- **Wishlist & Saved Items**: Visual gallery with hover actions (add to cart, remove)
- **Recommendations**: Carousel of personalized product suggestions with reasoning

**Affiliate Dashboard:**
- **Earnings Overview**: Visual earnings graph with overlay filters
- **Marketing Center**: Gallery of marketing materials with preview and copy functions
- **Performance Analytics**: Interactive dashboard with detailed click/conversion data

**Store Application Dashboard:**
- **Application Status**: Visual progress tracker with completion percentage
- **Document Upload**: Drag-and-drop interface with validation indicators
- **Setup Guide**: Interactive checklist with contextual help

### Tablet Version

- Dashboards maintain most desktop layouts but with adjusted spacing
- Multi-column grids reduce to 2-columns
- Side panels convert to modal overlays
- Touch-friendly controls with larger tap targets

### Mobile Version

- Critical information prioritized at top of scroll
- Swipeable cards for related content sections
- Vertical stacking of previously horizontal elements
- Bottom navigation for switching between major sections
- Pull-to-refresh for data updates

## Modern UI Elements & Interactions

### 1. Micro-Interactions

- **Subtle Animation**: Loading states, transitions between views
- **Feedback Animations**: Success/error states with motion
- **Interactive Charts**: Hover/touch reveals detailed information
- **Progress Indicators**: Visual feedback for multi-step processes

### 2. Card Components

- **Elevated Cards**: Subtle shadows with hover effects
- **Interactive Cards**: Cards that expand to reveal more information
- **Action Cards**: Cards with prominent call-to-action buttons
- **Stat Cards**: Contextual colors based on performance (green for positive, red for negative)

### 3. Navigation Patterns

- **Contextual Navigation**: Changes based on user role and current task
- **Breadcrumbs**: Clear indication of location within nested interfaces
- **Search Enhancements**: Type-ahead suggestions, recent searches, and filters
- **Back/Forward Navigation**: History support for complex workflows

### 4. Form Elements

- **Progressive Disclosure**: Forms that reveal fields as needed
- **Inline Validation**: Real-time feedback as users complete forms
- **Smart Defaults**: Pre-filled values based on context
- **Accessible Inputs**: High-contrast, properly sized form controls

## Design Implementation Ideas

### 1. Glassmorphism (Selective Use)

For key UI elements like control panels or important stats, use subtle glassmorphism effects:
- Semi-transparent backgrounds
- Subtle blur effects
- Light borders
- Inner glow shadows

### 2. Dark Mode Support

- Full dark mode support across all dashboards
- Automatic detection of system preference
- User toggle option
- Preserved brand colors with adjusted contrast

### 3. Skeleton Loading States

Instead of traditional loading spinners:
- Placeholder UI that mirrors the expected content layout
- Subtle animation while data loads
- Progressive loading of components

### 4. Data Visualization

- **Custom Chart Components**: Bespoke charts styled to match the design system
- **Simplified Visualization**: Focus on clarity over complexity
- **Interactive Exploration**: Drill-down capabilities on charts
- **Export Options**: Save visualizations as images or data

### 5. Responsive Layout Techniques

- **CSS Grid Layout**: For complex page structures
- **Flex Box**: For component-level layouts
- **Container Queries**: For components that adapt based on their container size
- **Strategic Breakpoints**: Design for transitions between device sizes

## Specific UI Components to Consider

### 1. Global Command Bar

A keyboard-accessible command bar (K+Cmd/Ctrl) that allows quick navigation and actions:
- Search across the platform
- Jump to specific sections
- Execute common actions
- Access recent items

### 2. Notification Center

A unified notification hub that:
- Groups related notifications
- Allows batch actions (mark all as read)
- Filters by type/priority
- Links directly to relevant section

### 3. AI Assistant

An AI-powered assistant that:
- Helps with common tasks
- Suggests optimizations
- Answers questions about platform features
- Provides contextual help

### 4. Customizable Dashboard

Allow users at each level to:
- Arrange widgets via drag-and-drop
- Show/hide metrics based on preference
- Save multiple dashboard layouts
- Set default views

## Mockup Ideas

Here are some specific UI mockup ideas you could implement:

### Admin Dashboard (Desktop)

```
┌────────────────────────────────────────────────────────────────┐
│ [Logo] □ Search                              🔔 👤 [Admin Name] │
├──────────┬─────────────────────────────────────────────────────┤
│          │                                                     │
│  [Home]  │  Welcome back, Admin                  May 11, 2025  │
│          │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│ [Tenants]│  │ Tenants │ │ Revenue │ │  Stores │ │  Users  │   │
│          │  │   124   │ │ $1.2M   │ │  2,156  │ │ 45,238  │   │
│ [Stores] │  │  +12%   │ │  +8%    │ │  +15%   │ │  +22%   │   │
│          │  └─────────┘ └─────────┘ └─────────┘ └─────────┘   │
│[Products]│                                                     │
│          │  ┌─────────────────────────┐ ┌─────────────────┐   │
│  [Users] │  │                         │ │ Recent Activity │   │
│          │  │                         │ │                 │   │
│ [Reports]│  │                         │ │ • New tenant    │   │
│          │  │       Platform GMV      │ │   Fashion Co.   │   │
│[Settings]│  │                         │ │                 │   │
│          │  │                         │ │ • Large order   │   │
│          │  │                         │ │   $12,500       │   │
│          │  └─────────────────────────┘ │                 │   │
│          │                              │ • System alert  │   │
│          │  ┌─────────────────────────┐ │   Storage 85%   │   │
│          │  │   Top Tenant Revenue    │ │                 │   │
│          │  │                         │ └─────────────────┘   │
│          │  │  [Bar chart]            │                       │
│          │  │                         │ ┌─────────────────┐   │
│          │  │                         │ │ Quick Actions   │   │
│          │  └─────────────────────────┘ │                 │   │
│          │                              │ [Add Tenant]    │   │
│          │                              │ [Run Reports]   │   │
│          │                              │ [System Status] │   │
│          │                              └─────────────────┘   │
└──────────┴─────────────────────────────────────────────────────┘
```

### Tenant Dashboard (Tablet)

```
┌────────────────────────────────────────────────┐
│ [≡] [Logo]                       🔔 👤 [Name]  │
├────────────────────────────────────────────────┤
│                                                │
│  Dashboard > Stores                            │
│                                                │
│  Stores (24)                     [+ Add Store] │
│  ┌─────────┐ ┌─────────────────────────────┐  │
│  │         │ │                             │  │
│  │ Filters │ │  ┌──────────┐ ┌──────────┐  │  │
│  │         │ │  │ Store 1  │ │ Store 2  │  │  │
│  │ □ Active│ │  │ [Image]  │ │ [Image]  │  │  │
│  │ □ Pending│ │ │          │ │          │  │  │
│  │ □ Paused│ │  │ $1,240   │ │ $5,639   │  │  │
│  │         │ │  │ 24 items │ │ 128 items│  │  │
│  │ Category│ │  └──────────┘ └──────────┘  │  │
│  │ ▼       │ │                             │  │
│  │         │ │  ┌──────────┐ ┌──────────┐  │  │
│  │ Rating  │ │  │ Store 3  │ │ Store 4  │  │  │
│  │ ▼       │ │  │ [Image]  │ │ [Image]  │  │  │
│  │         │ │  │          │ │          │  │  │
│  │         │ │  │ $3,840   │ │ $7,120   │  │  │
│  │         │ │  │ 56 items │ │ 98 items │  │  │
│  │         │ │  └──────────┘ └──────────┘  │  │
│  │[Apply]  │ │                             │  │
│  └─────────┘ └─────────────────────────────┘  │
│                                                │
│                            [Load More Stores]  │
└────────────────────────────────────────────────┘
```

### Store Dashboard (Mobile)

```
┌──────────────────────────┐
│ [≡] Store Name      👤   │
├──────────────────────────┤
│                          │
│ Today's Revenue          │
│ $1,245                   │
│ ↑ 15% from yesterday     │
│                          │
│ New Orders               │
│ 24                       │
│ ↑ 8% from yesterday      │
│                          │
│ ┌──────────────────────┐ │
│ │ Sales This Week      │ │
│ │                      │ │
│ │ [Line chart]         │ │
│ │                      │ │
│ └──────────────────────┘ │
│                          │
│ Recent Orders            │
│ ┌──────────────────────┐ │
│ │ Order #1242          │ │
│ │ $129 - Jane Smith    │ │
│ │ 3 items - Processing │ │
│ │ [Fulfill] [Details]  │ │
│ └──────────────────────┘ │
│                          │
│ ┌──────────────────────┐ │
│ │ Order #1241          │ │
│ │ $85 - John Doe       │ │
│ │ 1 item - Shipped     │ │
│ │ [Track] [Details]    │ │
│ └──────────────────────┘ │
│                          │
│ [See All Orders]         │
│                          │
├──────────────────────────┤
│ [🏠] [📦] [📊] [👥] [⚙️] │
└──────────────────────────┘
```

### User Dashboard (Desktop) - Affiliate View

```
┌────────────────────────────────────────────────────────────────┐
│ [Store Logo]                          🔍 🔔 👤 [User Name]     │
├────────────────────────────────────────────────────────────────┤
│ Affiliate Dashboard                                            │
│                                                                │
│ ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐           │
│ │ Earnings │ │ Clicks   │ │ Conversion│ │ Average  │           │
│ │ $1,245   │ │ 2,456    │ │ Rate      │ │ Commission│          │
│ │ This Month│ │ This Week│ │ 4.8%     │ │ $12.40   │           │
│ └──────────┘ └──────────┘ └──────────┘ └──────────┘           │
│                                                                │
│ ┌───────────────────────────────────┐ ┌──────────────────────┐│
│ │ Earnings Over Time                │ │ Top Performing Links ││
│ │                                   │ │                      ││
│ │ [Area chart showing earnings]     │ │ 1. Summer Sale       ││
│ │                                   │ │    $245 earned       ││
│ │                                   │ │                      ││
│ │                                   │ │ 2. Product Review    ││
│ │                                   │ │    $189 earned       ││
│ │                                   │ │                      ││
│ │                                   │ │ 3. Instagram Post    ││
│ │                                   │ │    $156 earned       ││
│ │                                   │ │                      ││
│ │ [Last 7 Days] [Last 30] [Custom]  │ │ 4. Email Campaign    ││
│ └───────────────────────────────────┘ │    $87 earned        ││
│                                       └──────────────────────┘│
│ ┌───────────────────────────────────┐ ┌──────────────────────┐│
│ │ Marketing Materials               │ │ Recent Activity      ││
│ │                                   │ │                      ││
│ │ ┌─────────┐ ┌─────────┐ ┌─────────┐│ │ • Commission earned ││
│ │ │ Banner 1│ │ Banner 2│ │ Banner 3││ │   $24.50 - 2h ago   ││
│ │ │         │ │         │ │         ││ │                      ││
│ │ │ [Image] │ │ [Image] │ │ [Image] ││ │ • New click         ││
│ │ │         │ │         │ │         ││ │   Product #1242     ││
│ │ │ [Copy]  │ │ [Copy]  │ │ [Copy]  ││ │   45m ago           ││
│ │ └─────────┘ └─────────┘ └─────────┘│ │                      ││
│ │                                   │ │ • Link generated     ││
│ │ [See All Marketing Materials]     │ │   15m ago            ││
│ └───────────────────────────────────┘ └──────────────────────┘│
└────────────────────────────────────────────────────────────────┘
```

## Modern UI Trends to Incorporate

1. **Neomorphism (Subtly)**: For key interaction elements like buttons and cards, giving them a soft, raised appearance.

2. **Micro-interactions**: Small animations that provide feedback when users interact with elements.

3. **Custom Illustrations**: Unique illustrations that reflect your brand identity and communicate complex concepts.

4. **Frosted Glass Effects**: For overlay panels and modals, creating depth without hiding content completely.

5. **3D Elements**: Subtle 3D graphics for empty states or onboarding.

6. **Data Visualization**: Interactive, animated charts that make data exploration intuitive.

7. **Contextual Help**: Tool tips and hints that appear based on user behavior.

8. **Progressive Disclosure**: Showing information gradually to avoid overwhelming users.

9. **Dynamic Color Theming**: Colors that adjust based on content, time of day, or user preference.

10. **Voice & Chat Interfaces**: AI assistants accessible through text or voice commands.

## Implementation Recommendations

1. **Framework**: Use a component-based framework like React with Next.js for best performance and developer experience.

2. **Component Library**: Consider using a system like Tailwind CSS for responsive design coupled with a component library like Shadcn/UI or Radix UI.

3. **Animation**: Use Framer Motion or React Spring for smooth, purposeful animations.

4. **Charts**: Recharts or Chart.js for data visualization.

5. **State Management**: Redux Toolkit or React Query for complex state.

6. **Theme Switching**: Use CSS variables and a theme provider for easy light/dark mode switching.

7. **Testing**: Ensure components are tested for accessibility and responsive behavior.

8. **Design System Documentation**: Maintain a living design system with Storybook.

By implementing these design recommendations, your Sellzio SaaS platform will not only look modern and professional but also provide an excellent user experience across all devices, helping users at every level (Admin, Tenant, Store, Customer) efficiently manage their activities.

# Detail Lengkap Dasbor Admin Sellzio SaaS

Berikut adalah desain komprehensif dan detail untuk dasbor admin Sellzio SaaS. Saya akan menjelaskan setiap aspek desain tanpa menyertakan kode.

## 1. Header & Navigasi Global

### Header Bar
- **Logo Area**: Logo Sellzio di pojok kiri atas, berukuran proporsional dan memiliki versi collapsed untuk mode mobile
- **Search Bar**: Search bar global di tengah header dengan dropdown hasil yang mengelompokkan hasil berdasarkan kategori (Tenants, Stores, Products, Users, Orders)
- **Global Actions**: Di sebelah kanan terdapat:
  - Icon notifikasi dengan counter badge (menampilkan angka notifikasi yang belum dibaca)
  - Icon shortcut menu untuk akses cepat ke fitur utama
  - Help center icon yang menampilkan menu bantuan kontekstual
  - Profile dropdown dengan foto admin, nama, dan role

### Status Indicator
- Strip tipis di bawah header menunjukkan status platform:
  - Hijau: Semua sistem normal
  - Kuning: Peringatan ringan (misalnya storage hampir penuh)
  - Merah: Masalah kritis yang memerlukan perhatian segera

### Navigation Sidebar
- **Layout Adaptif**: Sidebar yang dapat di-collapse ke mode icon-only
- **Visual Indicators**: Highlight pada menu aktif dengan bar warna aksen di sebelah kiri
- **Grouping**: Menu dikelompokkan berdasarkan fungsi dengan header subtle untuk setiap grup
- **Nested Navigation**: Child menu yang muncul sebagai dropdown atau expandable section
- **Quick Action**: Tombol "+" di bagian bawah sidebar untuk akses cepat membuat entitas baru

### Struktur Menu Detail:
1. **Dashboard**
   - Overview
   - Analytics
   - Reports

2. **Tenant Management**
   - All Tenants
   - Pending Applications
   - Subscription Plans
   - Domain Management

3. **Store Management**
   - All Stores
   - Verification Queue
   - Store Categories
   - Store Templates

4. **Product Management**
   - Product Catalog
   - Categories & Tags
   - Attributes
   - Brand Management

5. **User Management**
   - Admins
   - Tenant Accounts
   - Store Owners
   - End Users
   - Affiliate Users

6. **Financial**
   - Revenue Overview
   - Commission Reports
   - Payouts
   - Invoices & Billing
   - Tax Management

7. **Content**
   - Theme Manager
   - Landing Pages
   - Blog Articles
   - Help Center
   - Email Templates

8. **Systems**
   - Platform Settings
   - Integration Hub
   - API Management
   - Logs & Monitoring
   - Backup & Restore

9. **Account**
   - My Profile
   - Notifications
   - Security
   - Team Management

## 2. Dashboard Overview Page

### Welcome Section
- **Personalized Welcome**: "Good morning/afternoon/evening, [Admin Name]" berdasarkan waktu lokal
- **Date & Time**: Tanggal dan waktu saat ini dengan format yang dapat dikonfigurasi
- **System Status Summary**: Indikator visual kesehatan platform keseluruhan
- **Quick Stats Banner**: KPI kunci dalam banner horizontal dengan logo metric dan trend indicator

### Key Performance Indicators
Set kartu metrik dengan layout responsif:

- **Tenants**
  - Total tenants aktif
  - Perubahan persentase (7 hari)
  - Micro-chart menunjukkan tren
  - Color coding (hijau jika meningkat, merah jika menurun)

- **Revenue**
  - Total pendapatan platform (MTD)
  - Perubahan persentase dibanding periode sebelumnya
  - Proyeksi bulan berjalan (dengan garis dotted)
  - Split revenue berdasarkan subscription vs commission

- **Stores**
  - Total stores aktif
  - Perubahan persentase (7 hari)
  - Breakdown stores per tenant (mini donut chart)
  - Indicator jumlah stores menunggu verifikasi

- **GMV (Gross Merchandise Value)**
  - Total nilai transaksi di platform
  - Perubahan persentase (7 hari)
  - Daily trend mini-chart
  - Projected vs target indicator

- **User Metrics**
  - Total pengguna aktif
  - User growth rate
  - Split by role (pie chart mini)
  - New user count (24 jam terakhir)

### Data Visualization Section
Area utama dashboard dengan visualisasi interaktif:

- **Chart Controls**
  - Date range selector (dengan preset: Today, This Week, This Month, This Quarter, This Year, Custom)
  - Comparison toggle (compare dengan periode sebelumnya)
  - Metric selector (Revenue, GMV, User Growth, Store Growth)
  - Export/share button (PDF, CSV, direct link)

- **Primary Chart**
  - Area chart dengan gradient fill
  - Smooth animation saat data berubah
  - Tooltip komprehensif saat hover
  - Drill-down capability (klik untuk detail harian atau per tenant)
  - Data comparison overlay (dotted line atau area semi-transparan)
  - Annotations untuk event penting (misal: promo besar, update platform)

- **Secondary Charts**
  - **Top Performing Tenants**
    - Horizontal bar chart dengan tenant branding
    - Metric options (Revenue, GMV, User Growth)
    - Quick-filter options (All, New, Growth Leaders)
    - Direct link ke tenant dashboard

  - **Category Distribution**
    - Donut chart menampilkan distribusi stores berdasarkan kategori
    - Interactive legend dengan hover highlight
    - Comparison vs previous period (inner ring)

  - **Platform Health**
    - Gauge chart menunjukkan status sistem utama
    - Metrik uptime, response time, error rate
    - Historical comparison dengan benchmark

### Activity & Alerts Section

- **Recent Activity Feed**
  - Timeline format dengan color-coded activity types
  - Timestamps relatif ("10 minutes ago")
  - Grouping untuk aktivitas serupa
  - Avatar untuk aktivitas user-initiated
  - Icon untuk system events
  - Click/tap untuk detail lengkap

- **Alert Panel**
  - Prioritized alerts dengan visual hierarchy
  - Critical alerts di bagian atas dengan background subtle red
  - Warning dengan yellow accents
  - Informational alerts dengan blue accents
  - Inline quick actions untuk mengatasi masalah
  - Dismiss & snooze controls

- **Pending Approvals**
  - Card showing items requiring admin attention
  - Badge menunjukkan jumlah item per kategori
  - Sortable by urgency or date
  - Thumbnail previews where applicable
  - Batch approval options

### Quick Actions Panel

- **Card Layout**
  - Grid 2x2 atau 3x2 dengan action cards
  - Icons yang menggambarkan setiap action
  - Micro animations pada hover

- **Common Actions**
  - Add New Tenant
  - Approve Pending Items
  - Generate Reports
  - View System Status
  - Manage Featured Content
  - Check Payouts

- **Contextual Actions**
  - Actions yang muncul berdasarkan status platform dan analytics
  - Misalnya "Review High-value Orders" saat ada transaksi besar

## 3. Tenant Management Interface

### Tenant Directory View

- **View Controls**
  - Toggle antara Table View dan Card View
  - Sort options (Name, Date, Revenue, Stores, Status)
  - Bulk action menu untuk operasi batch
  - Column visibility toggle pada table view

- **Table View**
  - Sticky header yang tetap visible saat scroll
  - Sortable columns dengan visual indicator
  - Inline actions (edit, disable, login as)
  - Expandable rows untuk quick preview tenant details
  - Pagination dengan options untuk items per page
  - Column personalization
  - Row hover dengan subtle highlight

- **Card View**
  - Grid layout dengan tenant cards
  - Tenant branding prominent (logo, color scheme)
  - Key metrics visible pada card face
  - Status indicator (color-coded dot or badge)
  - Quick actions accessible via dropdown or hover
  - Visual hierarchy berdasarkan tenant tier atau status

### Search & Filter Panel

- **Advanced Search**
  - Type-ahead suggestions
  - Search history
  - Field-specific search (name, domain, ID)
  - Natural language capabilities ("tenants created last month")

- **Filter Panel**
  - Collapsible group filters
  - Multi-select capabilities
  - Date range selectors
  - Numeric range sliders
  - Save filter preset feature
  - Clear all/Clear individual filter controls
  - Filter tags showing active filters

- **Categories & Tags**
  - Hierarchical category browser
  - Tag cloud for popular tags
  - Saved searches section

### Tenant Detail View

- **Tenant Header**
  - Cover image area (tenant branded)
  - Logo and tenant name
  - Status badge (Active, Pending, Suspended)
  - Tenant tier indicator
  - Quick stats summary
  - Action buttons (Edit, Suspend, Delete, Login as)

- **Information Tabs**
  - Overview
  - Stores
  - Products
  - Users
  - Orders
  - Financials
  - Settings
  - Activity

- **Overview Panel**
  - Tenant vital information
  - Performance scorecards
  - Recent activity
  - Alert panel
  - Compliance status
  - Health indicators

- **Performance Metrics**
  - Revenue breakdown
  - Store performance
  - User growth
  - Conversion metrics
  - Comparison vs platform average
  - Historical trends

- **Domain Management**
  - Current domain/subdomain
  - Domain verification status
  - SSL certificate information
  - Domain history
  - Add/Change domain flow

## 4. Tenant Onboarding & Management

### New Tenant Creation

- **Multi-step Wizard**
  - Progress indicator showing steps
  - Clear navigation between steps
  - Ability to save draft
  - Context-sensitive help
  - Validation feedback in real-time

- **Steps Detail**
  1. **Basic Information**
     - Tenant name
     - Business details
     - Contact information
     - Profile image/logo upload
     - Business category selection

  2. **Subscription Selection**
     - Plan comparison table
     - Highlighted recommended plan
     - Custom plan builder
     - Billing cycle options
     - Promo code field

  3. **Domain Setup**
     - Subdomain creation
     - Custom domain instructions
     - Domain verification process
     - Preview tenant URL

  4. **Theme & Branding**
     - Theme gallery
     - Color customization
     - Typography selection
     - Layout options
     - Mobile preview

  5. **Admin Accounts**
     - Primary admin setup
     - Additional admin invitations
     - Role assignment
     - Permission settings

  6. **Review & Launch**
     - Summary of all settings
     - Terms acceptance
     - Launch button
     - Post-launch checklist

### Tenant Settings Management

- **General Settings**
  - Business information
  - Contact details
  - Operational hours
  - Localization settings
  - Legal information

- **Branding Controls**
  - Logo management (different sizes/contexts)
  - Color scheme editor
  - Font selection
  - Image guidelines
  - Brand asset library

- **Features & Permissions**
  - Module enablement toggles
  - Feature limitations based on plan
  - Custom permission sets
  - API access management
  - Third-party integrations

- **Billing & Subscription**
  - Current plan details
  - Usage metrics
  - Billing history
  - Payment methods
  - Upgrade/downgrade options
  - Custom billing arrangements

## 5. Analytics & Reporting

### Platform Overview Dashboard

- **Timeframe Controls**
  - Dynamic date range selector
  - Comparison period toggle
  - Granularity controls (hourly, daily, weekly, monthly)

- **High-level Metrics**
  - Platform Revenue
  - GMV
  - Active Users
  - Tenant Growth
  - Store Growth
  - Conversion Rate
  - Average Order Value

- **Trend Analysis**
  - Growth rates
  - Seasonality patterns
  - Year-over-year comparison
  - Moving averages
  - Anomaly detection

- **Geographic Distribution**
  - Interactive map visualization
  - Heat map overlays for different metrics
  - Drill-down by region/country/city
  - Comparative regional performance

### Report Builder

- **Template Library**
  - Pre-built report templates
  - Recently used reports
  - Saved custom reports
  - Featured reports

- **Report Configuration**
  - Drag and drop metrics
  - Filter configuration
  - Visualization selection
  - Scheduling options
  - Alert thresholds

- **Visualization Options**
  - Line charts
  - Bar/column charts
  - Pie/donut charts
  - Scatter plots
  - Heat maps
  - Data tables
  - Pivot tables
  - Funnel charts
  - Gauge charts

- **Export & Share**
  - PDF generation
  - CSV/Excel export
  - Direct link sharing
  - Email scheduling
  - Dashboard embedding

## 6. Financial Management

### Revenue Dashboard

- **Revenue Breakdown**
  - By source (subscription, commission, fees)
  - By tenant tier
  - By time period
  - By geographic region

- **Outstanding Balances**
  - Upcoming payments
  - Overdue accounts
  - Payment history
  - Write-off management

- **Commission Management**
  - Commission rule configuration
  - Override settings
  - Special rate periods
  - Multi-level commission visualization

### Payout Management

- **Payout Calendar**
  - Scheduled payouts view
  - Historical payouts
  - Payout frequency settings

- **Payout Processing**
  - Manual payout controls
  - Batch payment creation
  - Verification step
  - Payment confirmation

- **Payment Methods**
  - Bank accounts
  - PayPal
  - Other payment processors
  - Currency conversion settings

### Billing & Invoicing

- **Invoice Generator**
  - Template selection
  - Custom fields
  - Tax settings
  - Discount application
  - Preview & edit

- **Invoice Management**
  - Filter and search
  - Batch operations
  - Status tracking
  - Reminder scheduling
  - Dispute handling

- **Subscription Billing**
  - Recurring billing setup
  - Proration settings
  - Trial management
  - Cancellation flows
  - Upgrade/downgrade handling

## 7. System Administration

### Platform Settings

- **Global Configuration**
  - Platform name & branding
  - Default language & region
  - Currency settings
  - Date & time formats
  - Measurement units

- **Security Settings**
  - Password policies
  - Two-factor authentication
  - Session management
  - IP restrictions
  - Access logs

- **Email Configuration**
  - SMTP settings
  - Email templates
  - Signature management
  - Sending limits
  - Bounce handling

### Integration Hub

- **Available Integrations**
  - Payment gateways
  - Shipping providers
  - Marketing tools
  - Accounting software
  - CRM systems
  - Analytics platforms

- **Integration Management**
  - Status monitoring
  - Authentication
  - Sync settings
  - Error logs
  - Usage statistics

- **Webhook Configuration**
  - Event subscription
  - Endpoint management
  - Retry settings
  - Signature verification
  - Testing tools

### Developer Tools

- **API Management**
  - API key generation
  - Access control
  - Rate limits
  - Usage monitoring
  - Documentation

- **Sandbox Environment**
  - Test tenant creation
  - Sample data generation
  - Feature toggling
  - Version testing

## 8. Responsive Design Elements

### Desktop Optimization

- **Wide Layout**
  - Multi-column dashboard
  - Side-by-side panels
  - Expanded data tables
  - Full-featured charts
  - Keyboard shortcuts
  - Advanced data filters
  - Drag-and-drop interfaces

- **Productivity Features**
  - Split views
  - Pinned panels
  - Custom workspace layouts
  - Multiple monitors support
  - Cross-reference tools

### Tablet Adaptations

- **Medium Layout**
  - Collapsed sidebar (icon + text)
  - Streamlined data tables
  - Tabbed interfaces instead of side-by-side
  - Touch-optimized controls
  - Simplified filters
  - Responsive charts

- **Touch Optimizations**
  - Larger touch targets
  - Swipe gestures
  - Context menus
  - Split keyboard support

### Mobile Optimizations

- **Compact Layout**
  - Bottom navigation
  - Stacked card interfaces
  - Progressive disclosure
  - Simplified data visualization
  - Essential metrics only
  - Focused workflows

- **Mobile-specific Features**
  - Pull-to-refresh
  - Bottom sheets
  - Floating action button
  - Simplified forms
  - Voice input options

## 9. Design System Elements

### Typography Implementation

- **Heading Hierarchy**
  - H1: 28px/32px (page titles)
  - H2: 24px/28px (section headings)
  - H3: 20px/24px (card titles)
  - H4: 18px/22px (subsection headings)
  - H5: 16px/20px (widget titles)

- **Body Text**
  - Regular: 16px/24px (primary content)
  - Small: 14px/20px (secondary content)
  - Micro: 12px/16px (metadata, timestamps)

- **Special Text**
  - Code: monospace for technical content
  - Quote: styled blockquotes for testimonials
  - Alert: emphasized text for important notices

### Color Application

- **Brand Colors**
  - Primary: #4F46E5 (used for primary actions, key UI elements)
  - Secondary: #14B8A6 (complementary accent)
  - Brand variations: lighter and darker shades for hierarchy

- **Interface Colors**
  - Backgrounds: Subtle neutral tones (#F9FAFB for light mode, #1E293B for dark mode)
  - Text: High contrast for readability (#1E293B on light, #F9FAFB on dark)
  - Borders: Subtle definition (#E5E7EB on light, #334155 on dark)

- **Functional Colors**
  - Success: #10B981 (positive actions, confirmations)
  - Warning: #FBBF24 (cautions, notifications)
  - Error: #EF4444 (errors, critical alerts)
  - Info: #3B82F6 (informational elements)

- **Chart & Data Colors**
  - Sequential palette for ordered data
  - Diverging palette for data with a meaningful midpoint
  - Categorical palette for unordered categories
  - Emphasis color for highlighting key data points

### Component Design

- **Cards**
  - Standard padding (16px/24px)
  - Consistent corner radius (8px)
  - Subtle shadows for elevation
  - Hover states for interactive cards
  - Optional header/footer sections
  - Loading states (skeleton loaders)

- **Buttons**
  - Primary (filled, high emphasis)
  - Secondary (outlined, medium emphasis)
  - Tertiary (text-only, low emphasis)
  - Consistent sizing (sm, md, lg)
  - Icon-only variants with tooltips
  - Loading states with subtle animation
  - Disabled states with reduced opacity

- **Form Elements**
  - Inputs with clear focus states
  - Helper text positioning
  - Validation states (success, error)
  - Custom checkboxes and radio buttons
  - Toggle switches for boolean options
  - Select dropdowns with search
  - Date pickers with range selection

- **Data Tables**
  - Sticky headers
  - Fixed height with virtualized scrolling
  - Row hover states
  - Selection controls
  - Expandable rows
  - Column resizing
  - Sorting indicators
  - Filter badges
  - Cell types (text, numeric, status, action)

## 10. Interactivity & Micro-interactions

### Navigation Interactions

- **Sidebar Behavior**
  - Smooth collapse/expand animation
  - Hover reveal for collapsed items
  - Active item highlight animation
  - Transition between navigation levels

- **Menu Transitions**
  - Dropdown menus with directional reveal
  - Context menus with fade-in appearance
  - Submenu intelligent positioning
  - Breadcrumb transition effects

### Feedback Animations

- **Action Confirmation**
  - Button press effect (subtle scaling)
  - Success checkmark animation
  - Progress indicators for multi-step actions
  - Toast notifications slide-in/out

- **State Changes**
  - Toggle switches with position animation
  - Checkbox custom transitions
  - Radio button selection animation
  - Accordion smooth expand/collapse

### Data Visualization Interactions

- **Chart Behaviors**
  - Animated data updates
  - Transition between chart types
  - Tooltip follow behavior
  - Highlight related data on hover
  - Zoom/pan controls with smooth animation
  - Drill-down transitions

- **Map Interactions**
  - Smooth zoom and pan
  - Region highlight on hover
  - Data overlay fade-in
  - Clustering animations
  - Custom marker bounces

### System Status Indicators

- **Loading States**
  - Skeleton screens for content loading
  - Progress bars for quantifiable processes
  - Spinner animations for indeterminate loading
  - Pulsing effects for active processes

- **Attention Getters**
  - Subtle pulse for new notifications
  - Badge count increment animation
  - Alert slide-in/shake for critical notices
  - Success celebration animations for achievements

## 11. UX Patterns & Features

### Dashboard Customization

- **Layout Personalization**
  - Drag-and-drop widget arrangement
  - Resize handles for widgets
  - Add/remove widgets
  - Save multiple layouts
  - Dashboard presets for different roles

- **Widget Settings**
  - Configure data sources
  - Set visualization preferences
  - Adjust time periods
  - Set refresh rates
  - Configure alerts

### Contextual Help System

- **Inline Guidance**
  - Tooltips for controls and terms
  - Contextual hints based on user action
  - Field-level help icons
  - First-time user guidance overlays

- **Documentation Access**
  - Contextual links to relevant documentation
  - Searchable help center
  - Video tutorials for complex features
  - Interactive guided tours

### Notification Center

- **Notification Types**
  - System alerts
  - User actions
  - Tenant activities
  - Scheduled reports
  - Security alerts

- **Organization & Management**
  - Categorized notifications
  - Read/unread status
  - Notification preferences
  - Batch actions (mark all read, clear)
  - Notification archive

### Productivity Enhancements

- **Keyboard Shortcuts**
  - Global navigation shortcuts
  - Action shortcuts
  - Context-sensitive shortcuts
  - Customizable shortcuts
  - Shortcut cheat sheet

- **Batch Operations**
  - Multi-select interfaces
  - Bulk edit capabilities
  - Mass approval/rejection
  - Batch import/export
  - Scheduled batch operations

## 12. Accessibility Features

### Visual Accessibility

- **Color Contrast**
  - WCAG 2.1 AA compliant contrast ratios
  - High contrast mode option
  - Color blindness safe palettes
  - Non-color dependent status indicators

- **Text Readability**
  - Minimum text size (14px body text)
  - Adjustable text sizing
  - Line spacing optimized for readability
  - Clear font with distinguishable characters

### Input Methods

- **Keyboard Navigation**
  - Focus indicators
  - Logical tab order
  - Skip navigation links
  - Keyboard shortcuts for common actions

- **Screen Reader Support**
  - ARIA labels
  - Semantic HTML structure
  - Hidden descriptive text for complex visuals
  - Status announcements for dynamic content

### Cognitive Considerations

- **Simplified Interfaces**
  - Progressive disclosure of complex features
  - Clear, concise language
  - Consistent UI patterns
  - Predictable interactions

- **Error Prevention & Recovery**
  - Confirmation for destructive actions
  - Clear error messages with solutions
  - Undo capability for critical actions
  - Auto-save for long forms

## 13. Dark Mode Implementation

### Dark Theme Design

- **Color Palette Adaptation**
  - Dark background hierarchy (#121212, #1E1E1E, #2D2D2D)
  - Text colors with appropriate contrast (#FFFFFF, #E0E0E0, #AEAEAE)
  - Accent colors adjusted for dark backgrounds
  - Reduced intensity for large color areas

- **UI Element Adaptation**
  - Reduced shadow usage
  - Border highlights for separation
  - Subtle gradient backgrounds
  - Adjusted icon visibility

### Theme Switching

- **Transition Effects**
  - Smooth color transitions
  - Synchronized element changes
  - No layout shifts during switch

- **Preference Management**
  - System preference detection
  - Manual override option
  - Per-session or persistent setting
  - Scheduled switching (light during day, dark at night)

## 14. Empty States & Error Handling

### Empty State Design

- **First-time User**
  - Welcoming illustrations
  - Getting started guidance
  - Tour offers
  - Sample data generation

- **No Data States**
  - Contextual illustrations
  - Clear explanations
  - Suggested actions
  - Import options

### Error & Edge Case Handling

- **Error Pages**
  - Friendly 404/500 pages
  - Clear explanation of issue
  - Suggested next steps
  - Contact support option

- **Partial Failures**
  - Graceful degradation
  - Inline error messaging
  - Retry options
  - Fallback content

## 15. Mobile App vs Responsive Web

While the entire admin experience is fully responsive, certain components are optimized differently for the dedicated mobile admin app versus mobile web:

### Mobile App Specific

- **Biometric Authentication**
  - Fingerprint/Face ID login
  - Secure credential storage

- **Push Notifications**
  - Critical alerts delivery
  - Actionable notifications
  - Custom alert sounds
  - Background notification polling

- **Offline Capabilities**
  - Data caching for key metrics
  - Queue actions for sync when online
  - Offline indicator with sync status
  - Background synchronization

### Mobile Web Focus

- **Complete Feature Parity**
  - All desktop features accessible
  - Reorganized for touch interfaces
  - Simplified workflows for key tasks

- **Progressive Web App Features**
  - Installable to home screen
  - Service worker for improved loading
  - Background sync for critical tasks
  - Local storage utilization

---

Desain dasbor admin ini dirancang untuk memberikan pengalaman yang kuat namun intuitif untuk mengelola platform e-commerce multi-level Sellzio SaaS. Dengan kombinasi struktur yang logis, visualisasi data yang kaya, dan interface responsif, admin dapat mengelola seluruh ekosistem dengan efisien terlepas dari perangkat yang digunakan.

# Detail Lengkap Dasbor Tenant (Tenant) Sellzio SaaS

Berikut adalah desain komprehensif untuk dasbor tenant (Tenant) pada platform Sellzio SaaS, yang menampilkan UI modern dan responsif untuk memungkinkan tenant mengelola marketplace mereka secara efektif.

## 1. Header & Navigasi Global

### Header Bar (Brand-able)
- **White-Label Area**: Area header yang dapat sepenuhnya dikustomisasi dengan branding tenant
- **Logo Tenant**: Posisi logo tenant yang menonjol di pojok kiri
- **Color Scheme**: Kemampuan untuk mengubah seluruh color scheme header sesuai brand tenant
- **Search Bar**: Search bar global dengan dropdown kategori pencarian (Products, Stores, Orders, Customers)
- **Global Actions**: 
  - Notification bell dengan counter badge
  - Quick create button (+ icon) untuk cepat membuat entitas baru
  - Help & support icon
  - Language selector (jika multi-bahasa aktif)
  - Profile dropdown menu

### Navigation Sidebar
- **Brand Area**: Menampilkan nama tenant dan info subscription di bagian atas
- **Main Navigation**:
  - Dashboard (Overview)
  - Stores
  - Products
  - Orders
  - Customers
  - Affiliates
  - Marketing
  - Content
  - Analytics
  - Settings
- **Visual Elements**:
  - Icon yang intuitif untuk setiap menu
  - Visual indicator untuk menu aktif (accent bar di sebelah kiri)
  - Badge counters untuk menu yang memerlukan perhatian
  - Collapse/expand toggle di bagian bawah
- **Status Indicators**:
  - Subscription tier badge
  - Storage usage meter
  - Performance score

### Responsive Behaviors
- **Desktop**: Full sidebar dengan text dan icon
- **Tablet**: Collapsible sidebar (icon-only default dengan hover expand)
- **Mobile**: Bottom navigation bar dengan 5 menu utama + "More" dropdown

## 2. Dashboard Overview (Homepage)

### Welcome Section
- **Personalized Header**: "Good morning/afternoon/evening, [Tenant Name]"
- **Date & Time**: Current date with upcoming important dates/events
- **Quick Stats Banner**: Horizontal banner showing 4-5 key metrics

### Performance Metrics
- **Revenue Cards**:
  - Total Revenue (MTD)
  - Comparison vs previous period
  - Mini trend chart
  - Projected monthly revenue

- **Order Statistics**:
  - Total Orders (Today/Week/Month toggle)
  - Average Order Value
  - Conversion Rate
  - Order Trend Chart

- **User Metrics**:
  - Total customers
  - New customers (Today/Week/Month)
  - Customer retention rate
  - Visitor to customer conversion

- **Inventory Health**:
  - Total active products
  - Low stock alerts
  - Top selling products
  - Out of stock items

### Visualization Area
- **Revenue Chart**:
  - Interactive area chart with revenue breakdown
  - Filter controls (daily/weekly/monthly view)
  - Comparison period toggle
  - Segmentation by store, product category
  - Revenue source breakdown (direct sales vs. affiliate)

- **Performance Breakdown**:
  - Store performance comparison (top 5 stores)
  - Category performance
  - Traffic sources
  - Device breakdown

- **Heat Map Calendar**:
  - Visual representation of sales by day/hour
  - Peak sales time identification
  - Customizable color scale
  - Hover details with specific metrics

### Activity Center
- **Recent Orders**:
  - Latest orders with status
  - Order value
  - Customer name
  - Quick action buttons (view, process)

- **Store Applications**:
  - Pending store approvals
  - Recently approved stores
  - Store verification progress

- **Affiliate Activity**:
  - New affiliate signups
  - Top performing affiliates
  - Recent affiliate commissions
  - Affiliate-generated revenue

- **System Notifications**:
  - Platform updates
  - Feature announcements
  - Maintenance alerts
  - Tips & tutorials

## 3. Store Management

### Stores Overview
- **Store Directory**:
  - Grid/list toggle view
  - Store cards with:
    * Store logo/thumbnail
    * Store name
    * Owner name
    * Products count
    * Sales metrics
    * Status indicator (Active, Pending, Suspended)
  - Sorting options (Newest, Most Revenue, Most Products)

- **Filter Panel**:
  - Status filter
  - Category filter
  - Date range (created)
  - Performance filter
  - Search by name/owner

- **Bulk Actions**:
  - Approve multiple stores
  - Suspend/unsuspend
  - Change categories
  - Send mass communication

### Store Approval Interface
- **Application Queue**:
  - List of pending applications
  - Application date
  - Store category
  - Completeness score

- **Verification Workflow**:
  - Multi-step verification process
  - Required documents checklist
  - Store policy review
  - Product sample approval
  - Compliance verification

- **Preview Mode**:
  - Store preview as it will appear to customers
  - Mobile/desktop toggle view
  - Product listing review
  - Policy page review

### Store Detail View
- **Store Header**:
  - Store banner/logo
  - Basic info (name, owner, date created)
  - Performance score
  - Quick action buttons (Edit, Suspend, Message)

- **Performance Dashboard**:
  - Sales metrics
  - Traffic stats
  - Conversion rates
  - Compare to platform average

- **Products Overview**:
  - Total products
  - Category breakdown
  - Top sellers
  - Inventory status

- **Store Settings**:
  - Commission structure
  - Payment settings
  - Shipping options
  - Return policy
  - Store-specific rules

- **Communication Log**:
  - Message history with store owner
  - System notifications sent
  - Policy updates
  - Important alerts

## 4. Product Management

### Product Catalog
- **Browse Interface**:
  - Grid/list toggle view
  - Product cards with:
    * Product image
    * Name
    * Price
    * Store
    * Stock status
    * Sales count
  - Visual indicators for featured/special products

- **Advanced Filters**:
  - Category/subcategory
  - Price range
  - Stock status
  - Store filter
  - Rating filter
  - Date added
  - Custom attributes

- **Bulk Product Management**:
  - Mass edit capabilities
  - Category assignment
  - Status changes
  - Price adjustments
  - Featured status

- **Import/Export Tools**:
  - CSV/Excel import
  - Template download
  - Validation before import
  - Export filtered results
  - Scheduled imports

### Category Management
- **Category Tree**:
  - Hierarchical category structure
  - Drag-and-drop reordering
  - Expand/collapse sections
  - Product count per category

- **Category Editor**:
  - Name and description
  - Parent category selection
  - Category image upload
  - SEO settings
  - Display options
  - Commission rate settings

- **Attribute Sets**:
  - Custom attributes by category
  - Required vs optional fields
  - Attribute types (text, number, select, etc.)
  - Filterable/searchable settings

## 5. Order Management

### Order List View
- **Comprehensive Table**:
  - Order number
  - Date/time
  - Customer name
  - Store name
  - Total amount
  - Payment status
  - Fulfillment status
  - Actions menu

- **Smart Filters**:
  - Status filter
  - Date range
  - Store filter
  - Price range
  - Payment method
  - Source (direct/affiliate)

- **Status Pipeline**:
  - Visual pipeline view
  - Orders grouped by status
  - Drag and drop between status columns
  - Count and value by status

### Order Detail View
- **Order Summary**:
  - Order number and date
  - Customer information
  - Payment method and status
  - Shipping method and status
  - Total amount with breakdown

- **Line Items**:
  - Product details
  - Quantity
  - Unit price
  - Discounts applied
  - Subtotal
  - Product thumbnail

- **Financial Details**:
  - Subtotal
  - Tax breakdown
  - Shipping cost
  - Discounts
  - Platform fee
  - Store payout amount
  - Affiliate commission (if applicable)

- **Timeline & History**:
  - Status change history
  - User actions log
  - Customer communications
  - Notes and comments

- **Action Panel**:
  - Update status buttons
  - Refund/cancel options
  - Send notification to customer
  - Print order/invoice
  - Add internal note

## 6. Customer Management

### Customer Directory
- **Customer List**:
  - Name and contact info
  - Registration date
  - Orders count
  - Total spent
  - Last activity
  - Customer tier/status

- **Segmentation Tools**:
  - Purchase history filters
  - Engagement level
  - Custom segments creator
  - Saved segments
  - Behavioral filters

- **Customer Acquisition**:
  - Source tracking
  - Referral monitoring
  - Conversion path analysis
  - First purchase details

### Customer Profile
- **Customer Overview**:
  - Contact information
  - Account creation date
  - Activity summary
  - Lifetime value
  - Loyalty points/rewards

- **Order History**:
  - Complete order list
  - Order status
  - Repeat purchase patterns
  - Average order value trend

- **Browsing History**:
  - Recently viewed products
  - Abandoned cart items
  - Wishlist items
  - Product interests

- **Communication Log**:
  - Email history
  - Chat transcripts
  - Support tickets
  - Marketing campaign exposure

- **Customer Insights**:
  - Purchase preferences
  - Category interests
  - Price sensitivity
  - Engagement schedule (time/day patterns)

## 7. Affiliate Program Management

### Affiliate Dashboard
- **Program Overview**:
  - Total affiliates
  - Active vs inactive
  - Total affiliate sales
  - Commission paid
  - Conversion rate
  - Traffic generated

- **Top Performers**:
  - Leaderboard of top affiliates
  - Performance metrics
  - Commission earned
  - Traffic quality score
  - Growth indicators

- **Affiliate Applications**:
  - Pending applications
  - Approval workflow
  - Background verification
  - Content review
  - Terms acceptance

### Commission Management
- **Commission Structure Builder**:
  - Global rate settings
  - Category-specific rates
  - Store-specific overrides
  - Volume-based tiers
  - Special promotion rates

- **Commission Rules**:
  - First-click vs last-click attribution
  - Cookie duration
  - Multi-level commission (if enabled)
  - Excluded products/categories
  - Minimum payout thresholds

- **Payout Management**:
  - Scheduled payouts
  - Payment methods
  - Verification process
  - Payout history
  - Tax document management

### Marketing Materials
- **Content Library**:
  - Banner templates
  - Product widgets
  - Text links
  - Email templates
  - Social media content

- **Tracking Tools**:
  - Link generator
  - QR code creator
  - Coupon code system
  - Deep linking options
  - UTM parameter management

- **Performance Analytics**:
  - Conversion tracking
  - Click tracking
  - Commission reporting
  - Traffic source analysis
  - Content effectiveness measurement

## 8. Marketing & Promotion

### Campaign Management
- **Campaign Creator**:
  - Campaign type selection
  - Target audience
  - Duration settings
  - Budget allocation
  - Goals and KPIs

- **Promotion Types**:
  - Percentage discounts
  - Fixed amount off
  - Buy X get Y free
  - Bundle deals
  - Free shipping
  - Gift with purchase

- **Coupon System**:
  - Coupon code generator
  - Usage limitations
  - Expiration settings
  - Discount rules
  - Stackability settings

### Flash Sales Manager
- **Event Scheduler**:
  - Time-limited sales planner
  - Countdown timer settings
  - Pre-sale notifications
  - Post-sale follow-up

- **Product Selection**:
  - Product picker interface
  - Discount percentage/amount
  - Inventory allocation
  - Limit per customer
  - Visibility settings

- **Live Monitoring**:
  - Real-time sales tracking
  - Inventory levels
  - Traffic monitoring
  - Conversion rate
  - Revenue generated

### Email Marketing
- **Campaign Builder**:
  - Drag & drop email editor
  - Template library
  - Mobile preview
  - A/B testing
  - Personalization tokens

- **Audience Segmentation**:
  - Contact list management
  - Dynamic segments
  - Behavioral targeting
  - Engagement-based targeting
  - Purchase history segmentation

- **Automation Flows**:
  - Welcome series
  - Abandoned cart recovery
  - Post-purchase follow-up
  - Re-engagement campaigns
  - Birthday/anniversary messages

## 9. Content Management

### Storefront Builder
- **Page Editor**:
  - Visual page builder
  - Section templates
  - Content blocks
  - Mobile-responsive design
  - Preview mode

- **Homepage Designer**:
  - Hero section editor
  - Featured products selection
  - Category showcases
  - Testimonial displays
  - Promotional banners

- **Navigation Editor**:
  - Menu structure builder
  - Category linking
  - Custom page links
  - Mega menu designer
  - Mobile menu settings

### Blog & Content Management
- **Article Editor**:
  - Rich text editor
  - Media library
  - SEO settings
  - Category assignment
  - Scheduling options

- **Content Calendar**:
  - Editorial calendar view
  - Content status tracking
  - Author assignment
  - Publication scheduling
  - Content recycling planner

- **Media Library**:
  - Image organization
  - Video management
  - Search and filters
  - Usage tracking
  - Bulk upload

### SEO Tools
- **Page Optimization**:
  - Title and meta description editor
  - URL structure management
  - Heading analysis
  - Content suggestions
  - Mobile optimization

- **Keyword Management**:
  - Keyword research tools
  - Ranking tracker
  - Competitive analysis
  - Content gap finder
  - Optimization suggestions

- **Sitemap & Structure**:
  - Automated sitemap generation
  - Structured data markup
  - Internal linking suggestions
  - Broken link checker
  - Redirect management

## 10. Live Commerce & Video Management

### Live Stream Scheduler
- **Stream Planning**:
  - Stream title and description
  - Schedule date and time
  - Duration estimation
  - Featured products selection
  - Promotional settings

- **Presenter Management**:
  - Host profile settings
  - Co-host invitation
  - Guest speakers
  - Roles and permissions
  - Training resources

- **Audience Settings**:
  - Visibility controls
  - Registration requirements
  - Capacity limits
  - Notification settings
  - Access restrictions

### Live Broadcast Studio
- **Stream Controls**:
  - Go live button
  - Stream quality selector
  - Camera/microphone settings
  - Screen sharing
  - Background options

- **Interactive Features**:
  - Live chat moderation
  - Q&A management
  - Poll creation
  - Viewer count
  - Engagement metrics

- **Product Showcase**:
  - Product carousel
  - Featured product highlighting
  - Price and discount display
  - One-click add to cart
  - Limited-time offers

### Video Content Management
- **Video Library**:
  - Uploaded videos
  - Stream recordings
  - Categorization
  - Search functionality
  - Analytics overview

- **Video Editor**:
  - Trimming and cutting
  - Thumbnail selection
  - Product tagging
  - Caption addition
  - Calls-to-action overlay

- **Distribution Settings**:
  - Publish locations
  - Scheduling
  - Promotion options
  - Embedding settings
  - Social sharing

## 11. Analytics & Insights

### Overview Dashboard
- **Key Performance Indicators**:
  - GMV (Gross Merchandise Value)
  - Net revenue
  - Order count
  - Average order value
  - Customer acquisition cost
  - Customer lifetime value

- **Growth Metrics**:
  - Month-over-month growth
  - Year-over-year comparison
  - Projected growth
  - Seasonal patterns
  - Benchmark comparison

- **Health Indicators**:
  - Conversion rate
  - Cart abandonment rate
  - Return rate
  - Churn rate
  - Repeat purchase rate

### Sales Analytics
- **Sales Breakdown**:
  - By product
  - By category
  - By store
  - By customer segment
  - By traffic source
  - By device type

- **Time Analysis**:
  - Hourly sales pattern
  - Day of week performance
  - Monthly trends
  - Seasonal analysis
  - Year-over-year comparison

- **Geographic Performance**:
  - Sales by region
  - Heat maps
  - International sales
  - Region-specific conversion
  - Shipping cost analysis

### Customer Insights
- **Acquisition Analysis**:
  - Traffic sources
  - Conversion by source
  - Acquisition cost
  - Landing page performance
  - First-time vs returning visitors

- **Behavior Analysis**:
  - Browse patterns
  - Search keywords
  - Session duration
  - Page views per session
  - Category interests

- **Retention Metrics**:
  - Repeat purchase rate
  - Time between purchases
  - Loyalty program engagement
  - Churn prediction
  - Lifetime value projection

### Report Builder
- **Custom Reports**:
  - Metric selection
  - Dimension configuration
  - Date range settings
  - Comparison options
  - Visualization selection

- **Scheduling**:
  - Automated report generation
  - Email delivery
  - Export formats
  - Recipient management
  - Frequency settings

- **Data Export**:
  - CSV/Excel export
  - PDF generation
  - API access
  - Raw data extraction
  - Automated data feeds

## 12. Financial Management

### Revenue Overview
- **Revenue Dashboard**:
  - Gross sales
  - Net sales after returns
  - Platform fees breakdown
  - Transaction fees
  - Affiliate commissions
  - Net profit

- **Revenue Sources**:
  - Direct sales
  - Affiliate-generated sales
  - Subscription fees from stores
  - Premium feature fees
  - Advertising revenue

- **Fee Management**:
  - Platform fee settings
  - Transaction fee structure
  - Subscription pricing tiers
  - Volume discount rules
  - Special rate agreements

### Payout Management
- **Store Payouts**:
  - Scheduled payouts
  - Manual payout controls
  - Payment method management
  - Minimum threshold settings
  - Tax withholding

- **Affiliate Payouts**:
  - Commission calculation
  - Payment schedule
  - Approval workflow
  - Payment methods
  - Tax documentation

- **Reconciliation Tools**:
  - Transaction matching
  - Dispute handling
  - Adjustment creation
  - Balance management
  - Audit trail

### Financial Reporting
- **Statement Generator**:
  - Income statement
  - Balance sheet
  - Cash flow statement
  - Tax reports
  - Store-specific statements

- **Transaction History**:
  - Detailed transaction log
  - Filter and search
  - Export capabilities
  - Transaction linking
  - Audit trail

- **Tax Management**:
  - Tax calculation rules
  - Tax rate tables
  - Tax report generation
  - Tax document management
  - Compliance tools

## 13. Settings & Configuration

### General Settings
- **Tenant Profile**:
  - Business information
  - Contact details
  - Legal entity information
  - Business hours
  - Currency settings

- **Localization**:
  - Language settings
  - Date and time format
  - Number formatting
  - Unit of measurement
  - Geographic adaptation

- **Notifications**:
  - Email notification preferences
  - Push notification settings
  - SMS alert configuration
  - In-app notification rules
  - Scheduled reports

### Branding & Appearance
- **White Label Settings**:
  - Logo management
  - Favicon upload
  - Color scheme customization
  - Typography settings
  - Custom CSS options

- **Theme Management**:
  - Theme selection
  - Theme editor
  - Mobile appearance
  - Layout options
  - Navigation structure

- **Custom Domain**:
  - Domain setup
  - SSL certificate management
  - Domain verification
  - DNS configuration help
  - Domain history

### User & Permissions
- **Staff Management**:
  - Staff account creation
  - Role assignment
  - Permission configuration
  - Access logs
  - Activity monitoring

- **Role Editor**:
  - Custom role creation
  - Permission matrix
  - Feature access control
  - Data access restrictions
  - Multi-level approval workflows

- **Security Settings**:
  - Password policies
  - Two-factor authentication
  - Session management
  - IP restrictions
  - Login attempt monitoring

### Integration Hub
- **Payment Gateways**:
  - Payment provider setup
  - Fee configuration
  - Currency settings
  - Checkout flow customization
  - Testing tools

- **Shipping Providers**:
  - Carrier integration
  - Shipping rate tables
  - Packaging options
  - Tracking integration
  - Label printing

- **Marketing Integrations**:
  - Email marketing platforms
  - Social media connections
  - Analytics tools
  - Advertising networks
  - CRM systems

- **Third-party Apps**:
  - App marketplace
  - Installation workflow
  - Configuration panels
  - Performance monitoring
  - Troubleshooting tools

## 14. Mobile Responsiveness

### Dashboard Adaptation
- **Mobile Dashboard View**:
  - Simplified KPI cards
  - Critical metrics prioritized
  - Touch-optimized charts
  - Swipeable content sections
  - "View full" options for detailed data

- **Tablet Optimizations**:
  - Two-column layout
  - Collapsible sidebar
  - Touch targets sized appropriately
  - Landscape/portrait adaptations
  - Split-screen compatibility

### Mobile-Specific UI Elements
- **Touch Controls**:
  - Swipe actions
  - Pull-to-refresh
  - Long-press menus
  - Tap targets (minimum 44x44px)
  - Bottom sheets for additional options

- **Mobile Navigation**:
  - Bottom tab bar for primary navigation
  - Expandable categories
  - Back button consistency
  - Breadcrumb alternatives
  - Floating action button for primary actions

- **Form Adaptations**:
  - Single column layouts
  - Simplified inputs
  - Native date/time pickers
  - Keyboard optimization
  - Progress indication for multi-step forms

### On-The-Go Features
- **Quick Actions**:
  - Fast approve/deny flows
  - Review notifications
  - Order status updates
  - Simple report views
  - Critical alerts management

- **Offline Capabilities**:
  - Data caching
  - Pending changes queue
  - Sync status indicator
  - Background synchronization
  - Offline mode toggle

## 15. Design System Elements

### Typography System
- **Heading Hierarchy**:
  - H1: 28px/32px (page titles)
  - H2: 24px/28px (section headings)
  - H3: 20px/24px (card titles)
  - H4: 18px/22px (subsection headings)
  - H5: 16px/20px (widget titles)

- **Body Text**:
  - Regular: 16px/24px (primary content)
  - Small: 14px/20px (secondary content)
  - Micro: 12px/16px (metadata, timestamps)

### Color Scheme
- **Primary Brand Colors**:
  - Customizable based on tenant branding
  - Default: Indigo (#4F46E5) with teal accent (#14B8A6)
  - Dark/light variations for hierarchy

- **Semantic Colors**:
  - Success: Green (#10B981)
  - Warning: Amber (#FBBF24)
  - Error: Red (#EF4444)
  - Info: Blue (#3B82F6)

- **Interface Colors**:
  - Backgrounds: Whites and light grays
  - Text: Dark grays to black
  - Borders: Mid-tone grays
  - Highlights: Brand colors at varying opacities

### Component Design
- **Card Design**:
  - Consistent padding (16px/24px)
  - Subtle shadow (2-4px blur)
  - Optional header/footer
  - Hover states for interactive cards
  - Status indicators in consistent positions

- **Button Hierarchy**:
  - Primary (filled with brand color)
  - Secondary (outlined)
  - Tertiary (text only)
  - Icon buttons with tooltips
  - Consistent sizing (sm, md, lg)

- **Form Elements**:
  - Text inputs with clear focus states
  - Dropdowns with search
  - Checkboxes and toggles
  - Radio buttons
  - Multi-select components
  - Date pickers
  - File uploaders

- **Data Visualization**:
  - Consistent chart types
  - Branded color palette for data
  - Clear legends
  - Tooltips on hover/touch
  - Empty state designs
  - Loading states

### Interaction Patterns
- **Hover Effects**:
  - Subtle scaling (1.01-1.02x)
  - Background color shifts
  - Shadow intensification
  - Content reveal animations

- **Click/Tap Feedback**:
  - Button press effect
  - Ripple effect for material design
  - Color state changes
  - Loading indicators

- **Transitions**:
  - Page transitions (slide, fade)
  - Component mounting/unmounting
  - Expanding/collapsing sections
  - Modal appearances
  - Sidebar slide animations

## 16. Additional Features

### Notifications Center
- **Notification Types**:
  - System alerts
  - New orders
  - Store applications
  - Affiliate signups
  - Platform announcements
  - Task reminders

- **Organization**:
  - Categorized tabs
  - Priority indicators
  - Read/unread status
  - Chronological sorting
  - Batch actions

- **Delivery Preferences**:
  - In-app notifications
  - Email notifications
  - Push notifications (mobile)
  - SMS alerts for critical items
  - Digest scheduling

### Help & Support
- **Contextual Help**:
  - Inline help tooltips
  - Feature walkthroughs
  - Video tutorials
  - Knowledge base links
  - Best practice suggestions

- **Support Channels**:
  - Live chat support
  - Ticket submission
  - Phone support access
  - Community forum
  - Documentation access

- **Onboarding Tools**:
  - Setup checklists
  - Progress tracking
  - Guided tours
  - Sample data generation
  - Configuration wizards

### Accessibility Features
- **Keyboard Navigation**:
  - Focus indicators
  - Logical tab order
  - Keyboard shortcuts
  - Skip navigation links

- **Screen Reader Support**:
  - ARIA attributes
  - Semantic HTML
  - Alternative text
  - Form labels
  - Announcement of dynamic content

- **Visual Accessibility**:
  - High contrast mode
  - Text size adjustment
  - Color blindness considerations
  - Motion reduction option

## 17. Mobile App Specific Features

### App-specific Navigation
- **Bottom Navigation Bar**:
  - Dashboard icon (home)
  - Orders icon
  - Products icon
  - Customers icon
  - More menu (additional options)

- **Gesture Navigation**:
  - Swipe between related screens
  - Pull down to refresh
  - Long press for context menus
  - Swipe to dismiss/archive

### Mobile-only Features
- **Push Notifications**:
  - Order alerts
  - New store applications
  - Revenue milestone alerts
  - System notifications
  - Actionable notification types

- **Offline Mode**:
  - Cached data access
  - Offline actions queue
  - Background sync when online
  - Conflict resolution
  - Sync status indicator

- **Mobile Dashboard**:
  - Simplified KPI cards
  - Today's summary view
  - Quick action buttons
  - Recent activity feed
  - Urgent attention items

## 18. Implementation Recommendations

### Next.js Implementation

```
pages/tenant/
├── index.tsx                          # Tenant dashboard overview
├── stores/
│   ├── index.tsx                      # Store listing
│   ├── [id]/                          # Store details
│   │   ├── index.tsx                  # Store overview
│   │   ├── products.tsx               # Store products
│   │   ├── orders.tsx                 # Store orders
│   │   └── settings.tsx               # Store settings
│   ├── categories.tsx                 # Store categories management
│   └── applications.tsx               # Store applications & approval
├── products/
│   ├── index.tsx                      # Product listing
│   ├── [id]/                          # Product details
│   │   ├── index.tsx                  # Product overview
│   │   ├── variants.tsx               # Product variants
│   │   ├── images.tsx                 # Product images
│   │   └── analytics.tsx              # Product performance
│   ├── categories/                    # Product categories
│   └── import.tsx                     # Import/export tools
├── orders/
│   ├── index.tsx                      # Order listing
│   ├── [id].tsx                       # Order details
│   └── analytics.tsx                  # Order analytics
├── customers/
│   ├── index.tsx                      # Customer listing
│   ├── [id].tsx                       # Customer profile
│   ├── segments.tsx                   # Customer segmentation
│   └── acquisition.tsx                # Acquisition analytics
├── affiliates/
│   ├── index.tsx                      # Affiliate dashboard
│   ├── [id].tsx                       # Affiliate profile
│   ├── commissions.tsx                # Commission management
│   ├── marketing.tsx                  # Marketing materials
│   └── applications.tsx               # Affiliate applications
├── marketing/
│   ├── campaigns/                     # Marketing campaigns
│   ├── promotions.tsx                 # Promotions & coupons
│   ├── flash-sales.tsx                # Flash sales management
│   └── email.tsx                      # Email marketing
├── content/
│   ├── pages/                         # Content pages
│   ├── blog/                          # Blog management
│   ├── media.tsx                      # Media library
│   └── seo.tsx                        # SEO tools
├── live/
│   ├── schedule.tsx                   # Live stream scheduling
│   ├── studio.tsx                     # Broadcast studio
│   ├── recordings.tsx                 # Past recordings
│   └── analytics.tsx                  # Live performance
├── analytics/
│   ├── overview.tsx                   # Analytics overview
│   ├── sales.tsx                      # Sales analytics
│   ├── customers.tsx                  # Customer analytics
│   ├── reports.tsx                    # Report builder
│   └── export.tsx                     # Data export
├── financial/
│   ├── overview.tsx                   # Financial overview
│   ├── payouts.tsx                    # Payout management
│   ├── transactions.tsx               # Transaction history
│   └── statements.tsx                 # Financial statements
└── settings/
    ├── profile.tsx                    # Tenant profile
    ├── branding.tsx                   # Branding & appearance
    ├── domain.tsx                     # Domain settings
    ├── users.tsx                      # User management
    ├── integrations/                  # Integration settings
    ├── notifications.tsx              # Notification preferences
    └── security.tsx                   # Security settings
```

### UI Component Structure

```
components/tenant/
├── layout/
│   ├── TenantLayout.tsx              # Main layout with sidebar/header
│   ├── TenantHeader.tsx              # Header component
│   ├── TenantSidebar.tsx             # Sidebar navigation
│   ├── TenantFooter.tsx              # Footer component
│   └── MobileNavigation.tsx          # Mobile navigation
├── dashboard/
│   ├── WelcomeSection.tsx            # Welcome header
│   ├── StatsCards.tsx                # KPI stat cards
│   ├── RevenueChart.tsx              # Revenue visualization
│   ├── ActivityFeed.tsx              # Recent activity
│   └── QuickActions.tsx              # Action buttons
├── stores/
│   ├── StoreCard.tsx                 # Store card component
│   ├── StoreTable.tsx                # Store table view
│   ├── StoreFilter.tsx               # Store filtering
│   ├── ApprovalWorkflow.tsx          # Approval process
│   └── StoreMetrics.tsx              # Store performance
├── products/
│   ├── ProductGrid.tsx               # Product grid view
│   ├── ProductTable.tsx              # Product list view
│   ├── CategoryTree.tsx              # Category hierarchy
│   ├── ProductForm.tsx               # Product editor
│   └── BulkActions.tsx               # Bulk editing tools
# Dashboard Tenant Sellzio SaaS (Continued)

## Continuing UI Component Structure

```
components/tenant/ (continued)
├── orders/
│   ├── OrderTable.tsx                # Order listing table
│   ├── OrderDetail.tsx               # Order detail view
│   ├── StatusPipeline.tsx            # Order status workflow
│   ├── PaymentInfo.tsx               # Payment information
│   └── OrderTimeline.tsx             # Order history timeline
├── customers/
│   ├── CustomerDirectory.tsx         # Customer listing
│   ├── CustomerProfile.tsx           # Customer profile
│   ├── SegmentBuilder.tsx            # Segment creation tool
│   ├── CustomerMetrics.tsx           # Customer metrics
│   └── ActivityLog.tsx               # Customer activity
├── affiliates/
│   ├── AffiliateList.tsx             # Affiliate directory
│   ├── CommissionEditor.tsx          # Commission rule editor
│   ├── MarketingMaterials.tsx        # Marketing assets
│   ├── PayoutManagement.tsx          # Payout interface
│   └── PerformanceMetrics.tsx        # Performance tracking
├── marketing/
│   ├── CampaignBuilder.tsx           # Campaign creation
│   ├── PromotionCard.tsx             # Promotion display
│   ├── CouponGenerator.tsx           # Coupon creation
│   ├── FlashSalePlanner.tsx          # Flash sale interface
│   └── EmailBuilder.tsx              # Email campaign editor
├── content/
│   ├── PageEditor.tsx                # Content page editor
│   ├── BlogManager.tsx               # Blog management
│   ├── MediaLibrary.tsx              # Media organization
│   ├── SeoTools.tsx                  # SEO optimization
│   └── NavigationEditor.tsx          # Menu structure editor
├── live/
│   ├── StreamScheduler.tsx           # Stream planning
│   ├── BroadcastControls.tsx         # Broadcast interface
│   ├── ProductShowcase.tsx           # Live product display
│   ├── InteractionPanel.tsx          # Viewer interaction
│   └── VideoLibrary.tsx              # Video management
├── analytics/
│   ├── MetricsDashboard.tsx          # Analytics overview
│   ├── PerformanceCharts.tsx         # Data visualization
│   ├── ReportBuilder.tsx             # Custom reporting
│   ├── ExportTools.tsx               # Data export
│   └── InsightCards.tsx              # Insights display
├── financial/
│   ├── RevenueDashboard.tsx          # Revenue tracking
│   ├── PayoutInterface.tsx           # Payout management
│   ├── TransactionLog.tsx            # Transaction history
│   └── FinancialReports.tsx          # Statement generation
├── settings/
│   ├── ProfileSettings.tsx           # Business profile
│   ├── BrandingControls.tsx          # Brand customization
│   ├── DomainManager.tsx             # Domain settings
│   ├── UserPermissions.tsx           # User management
│   ├── IntegrationHub.tsx            # Integration center
│   └── SecuritySettings.tsx          # Security controls
└── ui/
    ├── Notifications.tsx             # Notification center
    ├── HelpCenter.tsx                # Support access
    ├── WizardBuilder.tsx             # Multi-step wizard
    ├── FilterPanel.tsx               # Advanced filtering
    └── SearchBar.tsx                 # Global search
```

## 19. Detailed Interface Mockups

### Dashboard Overview (Desktop)

```
┌────────────────────────────────────────────────────────────────────┐
│ [Logo]   □ Search                           🔔 [+] ⚙️ 👤 [Tenant] │
├──────────┬─────────────────────────────────────────────────────────┤
│          │                                                         │
│  [Home]  │  Good morning, Fashion Marketplace          May 11, 2025│
│          │                                                         │
│ [Stores] │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │
│          │  │ Revenue │ │ Orders  │ │ Visitors│ │  Stores │       │
│[Products]│  │ $14,586 │ │   234   │ │  3,459  │ │    28   │       │
│          │  │  +8.5%  │ │  +12%   │ │  +5.2%  │ │   +2    │       │
│ [Orders] │  └─────────┘ └─────────┘ └─────────┘ └─────────┘       │
│          │                                                         │
│[Customers]  ┌─────────────────────────────┐ ┌─────────────────────┐│
│          │  │                             │ │ Store Performance   ││
│[Affiliates] │                             │ │                     ││
│          │  │                             │ │ 1. Fashion Corner   ││
│[Marketing]  │                             │ │    $5,238  ↑ 12%    ││
│          │  │        Revenue Over Time    │ │                     ││
│ [Content]│  │                             │ │ 2. Trendy Apparel   ││
│          │  │                             │ │    $3,495  ↑ 5%     ││
│  [Live]  │  │                             │ │                     ││
│          │  │                             │ │ 3. Style Hub        ││
│[Analytics]  │                             │ │    $2,872  ↓ 3%     ││
│          │  │                             │ │                     ││
│[Financial]  └─────────────────────────────┘ │ 4. Fashion Express  ││
│          │                                  │    $1,893  ↑ 9%     ││
│[Settings]│  ┌─────────────────────────────┐ └─────────────────────┘│
│          │  │ Recent Activity             │ ┌─────────────────────┐│
│          │  │                             │ │ Quick Actions       ││
│          │  │ • New order #1242           │ │                     ││
│          │  │   $129 - 10m ago            │ │ [Approve Stores]    ││
│          │  │                             │ │                     ││
│          │  │ • Store application         │ │ [Create Campaign]   ││
│          │  │   "Chic Boutique" - 25m ago │ │                     ││
│          │  │                             │ │ [Schedule Live]     ││
│          │  │ • New affiliate signup      │ │                     ││
│          │  │   Jane Smith - 1h ago       │ │ [View Reports]      ││
│          │  │                             │ │                     ││
│          │  │ • Payment received          │ └─────────────────────┘│
│          │  │   $1,450 - 2h ago           │                       │
│          │  │                             │                       │
│          │  └─────────────────────────────┘                       │
└──────────┴─────────────────────────────────────────────────────────┘
```

### Stores Management (Tablet)

```
┌──────────────────────────────────────────────────┐
│ [≡] [Logo]                     🔔 [+] 👤 [Tenant]│
├──────────────────────────────────────────────────┤
│                                                  │
│ Stores > All Stores                              │
│                                                  │
│ ┌────────────┐  ┌─────────────────────────────┐  │
│ │            │  │ ┌────────┐   ┌────────┐     │  │
│ │ Filters    │  │ │  Grid  │   │  List  │     │  │
│ │            │  │ └────────┘   └────────┘     │  │
│ │ Status     │  │                             │  │
│ │ □ Active   │  │ 28 stores     [+ Add Store] │  │
│ │ □ Pending  │  │                             │  │
│ │ □ Inactive │  │ ┌────────────┐ ┌────────────┐  │
│ │            │  │ │            │ │            │  │
│ │ Category   │  │ │Fashion     │ │Accessories │  │
│ │ ▼ Fashion  │  │ │Corner      │ │Boutique    │  │
│ │   □ Apparel│  │ │            │ │            │  │
│ │   □ Shoes  │  │ │$5,238      │ │$1,256      │  │
│ │   □ Access.│  │ │124 products│ │45 products │  │
│ │            │  │ │            │ │            │  │
│ │ Date Added │  │ │[View] [Edit]│ │[View] [Edit]│  │
│ │ ▼          │  │ └────────────┘ └────────────┘  │
│ │ ○ All time │  │                             │  │
│ │ ○ This month│ │ ┌────────────┐ ┌────────────┐  │
│ │ ○ Last 30d │  │ │            │ │            │  │
│ │ ○ Custom   │  │ │Trendy      │ │Style Hub   │  │
│ │            │  │ │Apparel     │ │            │  │
│ │ Performance│  │ │            │ │            │  │
│ │ ▼          │  │ │$3,495      │ │$2,872      │  │
│ │ □ Top 25%  │  │ │87 products │ │62 products │  │
│ │ □ Growing  │  │ │            │ │            │  │
│ │ □ Declining│  │ │[View] [Edit]│ │[View] [Edit]│  │
│ │            │  │ └────────────┘ └────────────┘  │
│ │ [Apply]    │  │                             │  │
│ └────────────┘  └─────────────────────────────┘  │
└──────────────────────────────────────────────────┘
```

### Store Detail View (Desktop)

```
┌────────────────────────────────────────────────────────────────────┐
│ [Logo]   □ Search                           🔔 [+] ⚙️ 👤 [Tenant] │
├────────────────────────────────────────────────────────────────────┤
│                                                                    │
│ Stores > Fashion Corner                                            │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │                        [Store Banner Image]                     │ │
│ │                                                                 │ │
│ │ Fashion Corner                               [Edit] [Suspend]   │ │
│ │ Owned by: Sarah Johnson • Created: Jan 15, 2025 • Status: Active│ │
│ │                                                                 │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌─────────────┐┌─────────────┐┌─────────────┐┌─────────────┐      │
│ │ Revenue     ││ Products    ││ Orders      ││ Conversion  │      │
│ │ $5,238      ││ 124         ││ 89          ││ Rate        │      │
│ │ This Month  ││ 12 out of   ││ This Month  ││ 3.8%        │      │
│ │ ↑ 12% MoM   ││ stock       ││ ↑ 8% MoM    ││ ↑ 0.5%      │      │
│ └─────────────┘└─────────────┘└─────────────┘└─────────────┘      │
│                                                                    │
│ ┌───────────┬────────────────────────────────────────────────────┐ │
│ │Overview   │                                                     │ │
│ │Products   │                                                     │ │
│ │Orders     │                                                     │ │
│ │Customers  │                                                     │ │
│ │Performance│                Sales Over Time                      │ │
│ │Settings   │                                                     │ │
│ │           │                                                     │ │
│ │           │                                                     │ │
│ │           │                                                     │ │
│ │           │                                                     │ │
│ └───────────┴────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────┐┌────────────────────────────────┐  │
│ │ Top Selling Products       ││ Recent Orders                   │  │
│ │                            ││                                 │  │
│ │ 1. Designer Handbag        ││ #1242 - May 11, 2025           │  │
│ │    $249 • 12 sold this month││ Emily Parker • $129            │  │
│ │                            ││ Status: Processing              │  │
│ │ 2. Premium Leather Belt    ││                                 │  │
│ │    $89 • 8 sold this month ││ #1238 - May 10, 2025           │  │
│ │                            ││ Michael Davis • $175            │  │
│ │ 3. Fashion Sunglasses      ││ Status: Shipped                 │  │
│ │    $75 • 6 sold this month ││                                 │  │
│ │                            ││ #1231 - May 9, 2025            │  │
│ │ [View All Products]        ││ Sofia Martinez • $68            │  │
│ │                            ││ Status: Delivered               │  │
│ └────────────────────────────┘└────────────────────────────────┘  │
└────────────────────────────────────────────────────────────────────┘
```

### Affiliate Management (Mobile)

```
┌──────────────────────────┐
│ [≡] Tenant Name     👤   │
├──────────────────────────┤
│                          │
│ Affiliates               │
│                          │
│ ┌────────────────────┐   │
│ │ Affiliate Overview │   │
│ │                    │   │
│ │ 54 Active Affiliates   │
│ │ ↑ 8% from last month   │
│ │                    │   │
│ │ $8,495 Generated   │   │
│ │ 18% of total revenue   │
│ └────────────────────┘   │
│                          │
│ Commission Structure     │
│                          │
│ ┌────────────────────┐   │
│ │ Default Commission │   │
│ │                    │   │
│ │ 10%  [Edit]        │   │
│ │                    │   │
│ │ Category Rates:    │   │
│ │ Fashion:    12%    │   │
│ │ Accessories: 8%    │   │
│ │ Shoes:       8%    │   │
│ │                    │   │
│ │ [Manage Rates]     │   │
│ └────────────────────┘   │
│                          │
│ Top Affiliates           │
│                          │
│ ┌────────────────────┐   │
│ │ 1. John Smith      │   │
│ │    $1,245 generated│   │
│ │    32 conversions  │   │
│ │    [View Profile]  │   │
│ └────────────────────┘   │
│                          │
│ ┌────────────────────┐   │
│ │ 2. Emma Johnson    │   │
│ │    $958 generated  │   │
│ │    24 conversions  │   │
│ │    [View Profile]  │   │
│ └────────────────────┘   │
│                          │
│ ┌────────────────────┐   │
│ │ 3. David Williams  │   │
│ │    $745 generated  │   │
│ │    19 conversions  │   │
│ │    [View Profile]  │   │
│ └────────────────────┘   │
│                          │
│ [View All Affiliates]    │
│                          │
├──────────────────────────┤
│ [🏠] [📦] [📊] [👥] [⚙️] │
└──────────────────────────┘
```

### Live Shopping Management (Desktop)

```
┌────────────────────────────────────────────────────────────────────┐
│ [Logo]   □ Search                           🔔 [+] ⚙️ 👤 [Tenant] │
├────────────────────────────────────────────────────────────────────┤
│                                                                    │
│ Live Shopping > Schedule Stream                                    │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Schedule a New Live Shopping Event                [Save Draft] │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌───────────────┐ ┌──────────────────────────────────────────────┐ │
│ │ 1. Basic Info │ │ Event Title                                  │ │
│ │ 2. Products   │ │ ┌──────────────────────────────────────────┐ │ │
│ │ 3. Settings   │ │ │Summer Fashion Collection Launch          │ │ │
│ │ 4. Promotion  │ │ └──────────────────────────────────────────┘ │ │
│ │ 5. Review     │ │                                              │ │
│ │               │ │ Description                                  │ │
│ │               │ │ ┌──────────────────────────────────────────┐ │ │
│ │               │ │ │Join us for the exclusive launch of our   │ │ │
│ │               │ │ │Summer Fashion Collection! We'll showcase │ │ │
│ │               │ │ │the latest trends and offer special       │ │ │
│ │               │ │ │discounts for live viewers.               │ │ │
│ │               │ │ └──────────────────────────────────────────┘ │ │
│ │               │ │                                              │ │
│ │               │ │ Date and Time                               │ │
│ │               │ │ ┌─────────────┐  ┌─────────────┐            │ │
│ │               │ │ │05/20/2025   │  │14:00        │            │ │
│ │               │ │ └─────────────┘  └─────────────┘            │ │
│ │               │ │                                              │ │
│ │               │ │ Duration                                     │ │
│ │               │ │ ┌─────────────┐                             │ │
│ │               │ │ │60 minutes   ▼│                             │ │
│ │               │ │ └─────────────┘                             │ │
│ │               │ │                                              │ │
│ │               │ │ Host                                         │ │
│ │               │ │ ┌─────────────────┐                         │ │
│ │               │ │ │Sarah Johnson   ▼│                         │ │
│ │               │ │ └─────────────────┘                         │ │
│ │               │ │                                              │ │
│ │               │ │ Cover Image                                  │ │
│ │               │ │ ┌──────────────────────────────────────────┐ │ │
│ │               │ │ │[Drop image here or click to upload]       │ │ │
│ │               │ │ │                                          │ │ │
│ │               │ │ │                                          │ │ │
│ │               │ │ └──────────────────────────────────────────┘ │ │
│ │               │ │                                              │ │
│ │               │ │ [Next: Select Products]                      │ │
│ └───────────────┘ └──────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────────────────┘
```

## 20. Custom Domain & White Label Implementation

### Domain Management System

The tenant dashboard includes a robust domain management system allowing tenants to use either a subdomain on the platform's main domain or their own custom domain.

#### Subdomain Setup
- **Automated Provisioning**: Instant subdomain creation (e.g., tenant-name.sellzio.com)
- **Subdomain Validation**: Checking for availability and reserved names
- **SSL Certificate**: Automatic SSL issuance via platform wildcard certificate

#### Custom Domain Integration
- **Domain Connection Wizard**:
  1. Domain entry form
  2. DNS settings instruction
  3. Verification methods (DNS TXT record, HTML file upload)
  4. Propagation checker
  5. SSL certificate issuance

- **Domain Status Monitoring**:
  - Health checks
  - SSL expiration monitoring
  - DNS configuration validation
  - Troubleshooting guides

### White-Label Customization

The white-label system allows tenants to fully customize the appearance of their e-commerce platform.

#### Visual Branding Elements
- **Logo Management**:
  - Multiple logo size upload (standard, small, favicon)
  - Logo placement options
  - Light/dark mode variants
  - Responsive sizing controls

- **Color Scheme Customization**:
  - Primary brand color
  - Secondary/accent color
  - UI element colors (buttons, links, icons)
  - Background colors
  - Text colors
  - Custom CSS options for advanced styling

- **Typography Settings**:
  - Font selection (system, Google Fonts, custom font upload)
  - Heading styles
  - Body text styles
  - Button text styles
  - Size and spacing controls

#### Theme System
- **Theme Gallery**:
  - Pre-designed themes
  - Industry-specific templates
  - Seasonal themes
  - Premium theme marketplace

- **Theme Editor**:
  - Visual theme customization
  - Layout adjustments
  - Component styling
  - Mobile appearance settings
  - Save custom themes as presets

#### White-Label Settings
- **Platform Branding Removal**:
  - "Powered by" footer toggle
  - Email template branding controls
  - Invoice and document branding
  - App notification branding

- **Custom Terminology**:
  - Ability to rename system terms (e.g., "Stores" to "Boutiques")
  - Custom welcome messages
  - Error and notification text customization

## 21. Mobile App Specific Features

### Native Mobile Experience

The tenant mobile app provides a native mobile experience with specific features optimized for on-the-go management:

#### Push Notification System
- **Critical Alerts**:
  - High-value order notifications
  - Store application alerts
  - Platform status updates
  - Security alerts

- **Performance Notifications**:
  - Sales milestone achievements
  - Traffic spike alerts
  - Conversion rate changes
  - Revenue goal updates

- **Operational Notifications**:
  - Inventory alerts (low stock, out of stock)
  - Order status updates requiring attention
  - Support ticket escalations
  - Scheduled maintenance reminders

#### Offline Capabilities
- **Data Caching**:
  - Dashboard metrics
  - Recent orders
  - Customer profiles
  - Product information

- **Offline Actions**:
  - Order status updates
  - Comment/note additions
  - Basic product edits
  - Approval/rejection decisions

- **Synchronization**:
  - Background data syncing
  - Conflict resolution system
  - Sync status indicators
  - Data prioritization (critical data syncs first)

#### Mobile-specific Tools
- **Quick Capture**:
  - Product photo capture with auto-enhancement
  - Receipt scanning
  - Document digitization
  - Voice notes and dictation

- **Mobile Scanner**:
  - QR code scanner for quick access
  - Barcode scanner for inventory
  - Document scanner for paperwork
  - Order tracking code scanner

- **Location Features**:
  - Store location mapping
  - Customer geographic insights
  - Delivery tracking
  - Regional performance visualization

### Progressive Web App Alternative

For platforms without a dedicated native app, a Progressive Web App (PWA) version offers many similar capabilities:

- **Installable Experience**: Add to home screen functionality
- **Offline Access**: Service worker-based caching
- **Push Notifications**: Web Push API integration (where supported)
- **Device Features**: Camera access for product photos (with permission)
- **Background Sync**: Deferred actions when offline

## 22. Integration Hub

The Integration Hub in the tenant dashboard provides a central location to connect with third-party services:

### Payment Gateway Integrations
- **Multiple Provider Support**:
  - Major payment processors (Stripe, PayPal, etc.)
  - Local payment methods
  - Alternative payment options (crypto, BNPL)
  - Manual payment tracking

- **Configuration Interface**:
  - API key management
  - Webhook setup
  - Testing tools
  - Transaction log viewer

### Shipping & Logistics
- **Carrier Integrations**:
  - Major shipping carriers
  - Local delivery services
  - Fulfillment partners
  - Dropshipping suppliers

- **Shipping Tools**:
  - Rate calculator
  - Label printing
  - Package tracking
  - Fulfillment automation

### Marketing Integrations
- **Email Marketing**:
  - Mailchimp, SendGrid, etc.
  - Contact synchronization
  - Campaign tracking
  - Automation triggers

- **Social Media**:
  - Facebook, Instagram, TikTok, etc.
  - Product catalog sync
  - Social selling tools
  - Content scheduling

- **Advertising Platforms**:
  - Google Ads
  - Facebook Ads
  - Remarketing tools
  - Conversion tracking

### Analytics & Reporting
- **Enhanced Analytics**:
  - Google Analytics
  - Facebook Pixel
  - Hotjar/heatmapping tools
  - Custom conversion tracking

- **Business Intelligence**:
  - Data warehouse connections
  - BI tool integrations
  - Custom reporting
  - Automated data export

## 23. Design System Implementation

The tenant dashboard's design system ensures consistency, usability, and aesthetic appeal across all interfaces:

### Component Library
- **Foundation Elements**:
  - Color system with accessible combinations
  - Typography scale with responsive adjustments
  - Spacing system based on 4px grid
  - Shadow system for elevation

- **UI Components**:
  - Buttons with variants (primary, secondary, tertiary, icon)
  - Form elements (inputs, selects, checkboxes, toggles)
  - Cards with multiple layouts
  - Tables with sorting, filtering, and pagination
  - Navigation elements (tabs, breadcrumbs, sidebar)
  - Modals and dialogs
  - Alerts and notifications

### Interactive Elements
- **State Variations**:
  - Default state
  - Hover state
  - Active/pressed state
  - Focus state
  - Disabled state
  - Loading state
  - Error state

- **Animation System**:
  - Transition timing standards
  - Animation curves
  - Micro-interaction patterns
  - Loading and progress indicators

### Responsive Behaviors
- **Breakpoint System**:
  - Desktop (1200px+)
  - Small desktop/large tablet (992px-1199px)
  - Tablet (768px-991px)
  - Mobile landscape (576px-767px)
  - Mobile portrait (<576px)

- **Layout Adaptations**:
  - Content reflow strategies
  - Component size adjustments
  - Touch-friendly adaptations
  - Visibility rules (show/hide by breakpoint)

## 24. Accessibility Features

The tenant dashboard prioritizes accessibility with these key features:

### Visual Accessibility
- **Color Contrast**:
  - All text meets WCAG 2.1 AA standards (4.5:1 for normal text, 3:1 for large text)
  - Non-text elements meet 3:1 contrast ratio
  - Color is not the sole means of conveying information

- **Text Legibility**:
  - Minimum text size (14px for body text)
  - Line height optimized for readability (1.5 for body text)
  - Font weight options for better visibility
  - Adjustable text size options

- **Focus Indicators**:
  - High-visibility focus states
  - Keyboard focus indicators that remain visible
  - Focus order that follows logical sequence

### Keyboard Navigation
- **Full Keyboard Access**:
  - All interactive elements accessible via keyboard
  - Logical tab order following visual layout
  - Keyboard shortcuts for common actions
  - Skip links for bypassing navigation

- **Enhanced Keyboard Interaction**:
  - Arrow key navigation in complex components
  - Enter/Space key activation
  - Escape key for closing modals/dropdowns
  - Keyboard trapping in modals

### Screen Reader Support
- **Semantic Structure**:
  - Proper heading hierarchy
  - Landmark regions (header, main, navigation, etc.)
  - Lists used appropriately
  - Tables with proper headers

- **ARIA Implementation**:
  - ARIA labels for unlabeled controls
  - ARIA roles for custom components
  - ARIA states for dynamic content
  - ARIA live regions for important updates

- **Alternative Text**:
  - All images have descriptive alt text
  - Decorative images marked appropriately
  - SVG elements with accessible names
  - Icon-only buttons with accessible labels

## Conclusion

This comprehensive tenant dashboard design for Sellzio SaaS provides a feature-rich, adaptable, and user-friendly interface for tenants to manage their e-commerce operations. The design balances functionality with usability, ensuring that tenants of all technical levels can effectively manage their marketplace.

The multi-device responsive approach ensures that tenants can access and use critical features regardless of device, while the white-label capabilities allow them to create a branded experience for their customers.

With a focus on modern UI principles, accessibility, and performance, this dashboard design creates a foundation for successful e-commerce management that can scale with the tenant's business growth.

# Detail Lengkap Dashboard Pembeli (User) Sellzio SaaS

Berikut adalah desain komprehensif untuk dashboard pembeli (user) pada platform Sellzio SaaS, termasuk pengaturan khusus jika mereka mendaftar sebagai store atau affiliate.

## 1. Header & Navigasi Global

### Header Bar
- **Logo Tenant**: Logo marketplace tenant yang sedang diakses
- **Search Bar**: Search bar global dengan autocomplete dan filter kategori
- **User Navigation**:
  - Notification bell dengan counter badge
  - Wishlist icon dengan counter
  - Shopping cart icon dengan counter dan mini dropdown
  - User profile menu dropdown

### Primary Navigation
- **Main Menu**:
  - Home
  - Categories (dropdown/mega menu)
  - Deals & Promotions
  - New Arrivals
  - Featured Stores
  - Live Shopping

### User Account Navigation (When Logged In)
- **Account Section**:
  - My Account
  - My Orders
  - Wishlist
  - Saved Addresses
  - Payment Methods
  - Notifications
  - Help & Support

### Responsive Behaviors
- **Desktop**: Full horizontal navigation with dropdowns
- **Tablet**: Collapsible main menu with slide-out navigation
- **Mobile**: Bottom navigation for main sections + hamburger menu

## 2. Dashboard Overview (My Account)

### Welcome Section
- **Personalized Greeting**: "Hello, [Customer Name]"
- **Account Status**: Membership level, points, or loyalty status
- **Quick Stats**: Orders pending, points balance, available credit

### Account Summary
- **Profile Completion Meter**:
  - Visual progress bar showing profile completion percentage
  - Prompts to complete missing information
  - Rewards for completing profile

- **Loyalty Program Status**:
  - Current tier/level
  - Progress to next tier
  - Available rewards/benefits
  - Points expiration alerts

- **Recent Activity**:
  - Latest orders with status
  - Recent reviews
  - Recent points earned
  - Recent interactions

### Quick Access Tiles
- **Order Management**:
  - Track packages
  - View order history
  - Manage returns
  - Repeat orders

- **Wishlist & Saved Items**:
  - Recently saved items
  - Items on sale from wishlist
  - Share wishlist
  - Move to cart

- **Payment & Address**:
  - Saved payment methods
  - Default shipping address
  - Billing addresses
  - Quick edit options

### Personalized Recommendations
- **Recommended Products**:
  - Based on browse/purchase history
  - "You might also like" section
  - Recently viewed items
  - Trending in your categories

- **Personalized Offers**:
  - Custom coupons
  - Limited-time offers
  - Bundle suggestions
  - Loyalty rewards

## 3. Order Management

### Order List View
- **Order Cards/Table**:
  - Order number
  - Date placed
  - Order total
  - Items count
  - Order status with visual indicator
  - Shipping status
  - Quick actions (track, review, return, reorder)

- **Filter & Search**:
  - Date range
  - Order status
  - Order type
  - Search by product/order number

- **Order Grouping**:
  - Active orders
  - Completed orders
  - Cancelled orders
  - Returned orders

### Order Detail View
- **Order Summary**:
  - Order number and date
  - Payment information
  - Shipping method and tracking
  - Order status with timeline
  - Return eligibility and deadline

- **Product List**:
  - Product images
  - Product names with variants
  - Quantity
  - Individual prices
  - Subtotal
  - Product-specific actions (review, return, repurchase)

- **Price Breakdown**:
  - Subtotal
  - Shipping costs
  - Taxes
  - Discounts applied
  - Total paid

- **Order Actions**:
  - Track shipment
  - Cancel order (if eligible)
  - Return items
  - Contact seller/support
  - Leave review
  - Report issue

### Package Tracking
- **Shipment Status**:
  - Visual timeline of package journey
  - Current location
  - Estimated delivery date
  - Carrier information and tracking number
  - Delivery instructions

- **Delivery Map**:
  - Interactive map showing package location
  - Delivery route
  - Delivery zone
  - Nearest pickup locations (if applicable)

## 4. User Profile Management

### Personal Information
- **Profile Details**:
  - Name
  - Email (with verification status)
  - Phone number (with verification status)
  - Date of birth
  - Gender (optional)
  - Profile picture

- **Account Security**:
  - Password management
  - Two-factor authentication
  - Login history
  - Connected devices
  - Third-party app connections

### Address Book
- **Multiple Address Support**:
  - Saved addresses list
  - Default shipping address
  - Default billing address
  - Address labels (Home, Work, etc.)

- **Address Form**:
  - Contact name
  - Street address
  - Apartment/Suite/Unit
  - City, State/Province
  - Postal code
  - Country
  - Phone number
  - Delivery instructions

### Payment Methods
- **Saved Payment Methods**:
  - Credit/debit cards (with masked numbers)
  - Digital wallets (PayPal, Apple Pay, etc.)
  - Buy Now Pay Later options
  - Bank accounts
  - Store credit/gift cards

- **Payment Preferences**:
  - Default payment method
  - Automatic payment settings
  - Payment security settings
  - Billing address connections

## 5. Communication Preferences

### Notification Settings
- **Notification Types**:
  - Order updates
  - Shipping alerts
  - Price drop alerts for wishlist items
  - Back in stock notifications
  - Deal alerts
  - Store updates
  - Platform announcements
  - Recommended products

- **Delivery Channels**:
  - Email preferences
  - Push notification settings
  - SMS settings
  - In-app notification preferences

### Marketing Preferences
- **Subscription Management**:
  - Newsletter subscription
  - Promotional emails
  - Product recommendations
  - Special events and live shopping
  - Surveys and feedback requests

- **Frequency Settings**:
  - Daily, weekly, monthly options
  - Quiet hours
  - Do not disturb days
  - Consolidated vs. individual messages

## 6. Store Application Module

### Store Discovery Section
- **Become a Seller**:
  - Benefits of opening a store
  - Success stories
  - Commission structure
  - Tools and resources
  - FAQ section

- **Application CTA**:
  - Prominent "Open Your Store" button
  - Eligibility checker
  - Quick application preview

### Store Application Process
- **Multi-step Application Wizard**:
  1. **Basic Information**:
     - Store name
     - Store description
     - Store category/niche
     - Seller experience level
     - Business type (individual, registered business)

  2. **Product Information**:
     - Product categories
     - Estimated number of products
     - Product price range
     - Sample product descriptions
     - Product image samples

  3. **Business Details**:
     - Business registration (if applicable)
     - Tax ID
     - Legal name
     - Business address
     - Contact information

  4. **Store Branding**:
     - Logo upload
     - Banner image
     - Brand color selection
     - Store policy outline
     - Social media links

  5. **Payment Setup**:
     - Bank account information
     - Payment processing preferences
     - Tax information
     - Payout schedule selection

  6. **Terms Acceptance**:
     - Marketplace seller agreement
     - Commission structure acknowledgment
     - Platform guidelines acceptance
     - Prohibited items policy
     - Privacy policy consent

- **Application Status Tracker**:
  - Visual progress indicator
  - Current stage highlight
  - Estimated processing time
  - Notification settings
  - Application ID for reference

### Store Dashboard (Once Approved)

#### Store Dashboard Overview
- **Performance Snapshot**:
  - Total sales (daily, weekly, monthly)
  - Order count
  - Visitor statistics
  - Conversion rate
  - Commission rate and earnings

- **Quick Actions**:
  - Add new product
  - View open orders
  - Respond to customer messages
  - Update inventory
  - Promote store

#### Product Management
- **Product Catalog**:
  - List/grid view toggle
  - Product name, image, price, inventory
  - Status indicator (active, inactive, out of stock)
  - Quick edit options
  - Bulk actions

- **Add/Edit Product Form**:
  - Basic info (name, description, price)
  - Category and tags
  - Images and media
  - Inventory and variants
  - Shipping information
  - SEO settings

- **Inventory Management**:
  - Stock levels
  - Low stock alerts
  - Restock functionality
  - Variant tracking
  - Inventory history

#### Order Management
- **Order Processing**:
  - New orders section
  - Processing orders
  - Shipped orders
  - Completed orders
  - Cancelled/returned orders

- **Order Fulfillment**:
  - Order details review
  - Shipping label generation
  - Tracking number input
  - Order status updates
  - Customer communication

#### Store Settings
- **Store Profile**:
  - Basic information
  - Store description
  - Contact information
  - Social media links
  - Policy pages

- **Shipping Settings**:
  - Shipping methods
  - Shipping rates
  - Shipping regions
  - Free shipping thresholds
  - Handling times

- **Return Policy**:
  - Return window
  - Return conditions
  - Return shipping
  - Refund methods
  - Automated vs. manual approval

- **Store Appearance**:
  - Logo and banner
  - Featured products
  - Store layout
  - Theme customization
  - Mobile appearance

- **Commission Settings**:
  - Commission structure view
  - Commission calculator
  - Volume-based tiers
  - Category-specific rates
  - Promotional periods

## 7. Affiliate Program Module

### Affiliate Discovery Section
- **Become an Affiliate**:
  - Benefits of becoming an affiliate
  - Earning potential
  - Success stories
  - Commission structure
  - How it works

- **Application CTA**:
  - Prominent "Join Affiliate Program" button
  - Eligibility requirements
  - Quick application preview

### Affiliate Application Process
- **Multi-step Application Wizard**:
  1. **Personal Information**:
     - Full name
     - Email address
     - Phone number
     - Location
     - Professional background

  2. **Promotion Channels**:
     - Website/blog
     - Social media accounts
     - Email newsletter
     - YouTube channel
     - Other promotion methods

  3. **Marketing Experience**:
     - Experience level
     - Audience size
     - Content types
     - Similar products promoted
     - Marketing approaches

  4. **Payment Information**:
     - Bank account details
     - Payment method preference
     - Tax information
     - Minimum payout threshold
     - Payout frequency

  5. **Terms Acceptance**:
     - Affiliate agreement
     - Commission structure
     - Content guidelines
     - Prohibited practices
     - Privacy policy consent

- **Application Status Tracker**:
  - Visual progress indicator
  - Current stage highlight
  - Estimated approval time
  - Notification preferences
  - Application ID for reference

### Affiliate Dashboard (Once Approved)

#### Affiliate Dashboard Overview
- **Performance Snapshot**:
  - Total earnings (daily, weekly, monthly)
  - Clicks generated
  - Conversion rate
  - Average order value
  - Pending commissions

- **Quick Actions**:
  - Generate affiliate links
  - Create promotional content
  - View analytics
  - Request payout
  - Access marketing materials

#### Link Management
- **Link Generator**:
  - Product selection
  - Custom URL parameters
  - Link preview
  - Short URL options
  - QR code generation

- **Active Links**:
  - Link listing
  - Performance statistics
  - Click-through rate
  - Conversion rate
  - Last click date

- **Deep Linking**:
  - Category links
  - Search result links
  - Custom landing page links
  - Sale and promotion links
  - Store-specific links

#### Marketing Materials
- **Promotional Content**:
  - Banner ads in various sizes
  - Product images
  - Promotional graphics
  - Email templates
  - Social media copy

- **Discount Codes**:
  - Personal affiliate code
  - Special offer codes
  - Limited-time promotions
  - Custom discount creation
  - Code performance tracking

#### Performance Analytics
- **Traffic Analysis**:
  - Click sources
  - Visitor demographics
  - Device types
  - Visit duration
  - Page navigation

- **Conversion Metrics**:
  - Conversion by product
  - Conversion by time
  - Abandoned cart rate
  - Average order value
  - Repeat purchase rate

- **Earnings Breakdown**:
  - Earnings by product
  - Earnings by store
  - Earnings by time period
  - Pending vs. approved commissions
  - Lifetime earnings

#### Commission Settings
- **Commission Structure**:
  - Base commission rates
  - Product category rates
  - Store-specific rates
  - Tiered commission levels
  - Special promotion rates

- **Payout Management**:
  - Available balance
  - Pending balance
  - Payout history
  - Request payout
  - Payment methods

- **Performance Goals**:
  - Monthly targets
  - Tier advancement requirements
  - Bonus opportunities
  - Performance badges
  - Leaderboard position

#### Content & Strategy
- **Content Suggestions**:
  - Top-performing products
  - Trending items
  - Seasonal recommendations
  - High-commission opportunities
  - Customer favorites

- **Strategy Tools**:
  - Best time to post
  - Top-converting content types
  - Audience matching
  - A/B testing suggestions
  - Performance forecasts

## 8. User Dashboard UI Elements

### Card Components
- **Order Card**:
  - Order number and date
  - Thumbnail of primary product
  - Status with color-coding
  - Total amount
  - Quick action buttons

- **Product Card**:
  - Product image
  - Product name
  - Price with any discounts
  - Rating stars
  - Add to cart/wishlist buttons

- **Wishlist Item Card**:
  - Product image
  - Product name
  - Current price
  - Stock status
  - Move to cart/remove buttons

- **Address Card**:
  - Address nickname
  - Recipient name
  - Formatted address
  - Default indicator (if applicable)
  - Edit/delete buttons

- **Payment Method Card**:
  - Card icon (Visa, Mastercard, etc.)
  - Last 4 digits
  - Expiration date
  - Default indicator
  - Edit/delete buttons

### Progress Indicators
- **Order Status Timeline**:
  - Placed → Processing → Shipped → Delivered
  - Current status highlighted
  - Completed steps with checkmarks
  - Estimated time for next step
  - Visual progress bar

- **Profile Completion Bar**:
  - Percentage complete
  - Missing items list
  - Benefits of completion
  - Quick links to complete sections

- **Loyalty Program Tier Progress**:
  - Current tier indicator
  - Points to next tier
  - Visual progress bar
  - Benefits of current tier
  - Preview of next tier benefits

### Notification System
- **Notification Center**:
  - Categorized tabs (Orders, Alerts, Messages, etc.)
  - Unread indicator
  - Time stamps
  - Action buttons
  - Mark all as read option

- **Toast Notifications**:
  - Success confirmations
  - Error alerts
  - Information notices
  - Progress updates
  - Auto-dismiss option

- **Banner Alerts**:
  - Important announcements
  - Time-sensitive offers
  - Account alerts
  - System maintenance notices
  - Dismissible with "don't show again" option

## 9. Mobile Responsive Design

### Mobile Dashboard Home
- **Bottom Navigation**:
  - Home
  - Orders
  - Wishlist
  - Account
  - More

- **Mobile-optimized Cards**:
  - Full-width cards
  - Simplified information
  - Touch-optimized actions
  - Swipeable content sections

- **Pull-to-refresh**:
  - Update dashboard data
  - Sync latest orders
  - Refresh notifications
  - Update points balance

### Mobile Order Management
- **Order List**:
  - Compact order cards
  - Essential information only
  - Status indicators
  - Horizontal swipe for quick actions

- **Order Details**:
  - Clear section hierarchy
  - Collapsible sections
  - Large touch targets
  - Fixed action bar at bottom

### Mobile Navigation Patterns
- **Slide-out Menu**:
  - Account section links
  - Settings
  - Help & support
  - Log out option

- **Bottom Sheets**:
  - Filtering options
  - Quick actions
  - Selection menus
  - Confirmation dialogs

- **Floating Action Button**:
  - Context-aware primary action
  - Tap to expand secondary actions
  - Scrolls with content
  - Disappears when keyboard appears

## 10. Store Application Interface (Detailed)

### Store Application Dashboard

```
┌────────────────────────────────────────────────────────────────────┐
│ [Logo]   □ Search                      🔔 ❤️ 🛒 👤 [Customer Name] │
├────────────────────────────────────────────────────────────────────┤
│                                                                    │
│ My Account > Store Application                                     │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Store Application Status: Under Review                         │ │
│ │                                                                │ │
│ │ Application ID: ST-12458          Submitted: May 8, 2025       │ │
│ │ Estimated Review Time: 2-3 business days                       │ │
│ │                                                                │ │
│ │ [Track Application Progress]                                   │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Application Progress                                           │ │
│ │                                                                │ │
│ │ ⬤───⬤───⬤───○───○                                            │ │
│ │ [1]  [2]  [3]  [4]  [5]                                        │ │
│ │ Info  Products Business Branding Terms                          │ │
│ │                                                                │ │
│ │ Current Stage: Waiting for Business Verification               │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Store Information Summary                                      │ │
│ │                                                                │ │
│ │ Store Name: Urban Style Boutique                              │ │
│ │ Category: Fashion & Accessories                               │ │
│ │ Products: Apparel, Jewelry, Handbags                          │ │
│ │ Business Type: Individual Seller                              │ │
│ │                                                                │ │
│ │ [View Complete Application]                                    │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Next Steps                                                     │ │
│ │                                                                │ │
│ │ ☑ Complete store information                                  │ │
│ │ ☑ Provide product details                                     │ │
│ │ ☑ Submit business information                                 │ │
│ │ ☐ Complete branding section                                   │ │
│ │ ☐ Accept terms and conditions                                 │ │
│ │                                                                │ │
│ │ [Continue Application]                                         │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Have Questions?                                                │ │
│ │                                                                │ │
│ │ Check our [Seller Guide] for information about the application │ │
│ │ process or [Contact Support] if you need assistance.           │ │
│ └────────────────────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────────────────┘
```

### Store Dashboard (Post-Approval)

```
┌────────────────────────────────────────────────────────────────────┐
│ [Logo]   □ Search                      🔔 ❤️ 🛒 👤 [Customer Name] │
├────────────────────────────────────────────────────────────────────┤
│                                                                    │
│ My Account > My Store                                              │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Welcome to Your Store Dashboard                                │ │
│ │                                                                │ │
│ │ Store: Urban Style Boutique                          [Settings]│ │
│ │ Status: Active                              [View Store Front] │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │
│ │ Sales       │ │ Orders      │ │ Visitors    │ │ Conversion  │   │
│ │ $1,245      │ │ 18          │ │ 342         │ │ Rate        │   │
│ │ This Month  │ │ Processing: 3│ │ Today: 42   │ │ 5.2%        │   │
│ │ ↑ 15% from  │ │ Completed: 15│ │ ↑ 8% from   │ │ ↑ 0.8% from │   │
│ │ last month  │ │             │ │ yesterday   │ │ last week   │   │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘   │
│                                                                    │
│ ┌───────────┬────────────────────────────────────────────────────┐ │
│ │Dashboard  │                                                     │ │
│ │Products   │                                                     │ │
│ │Orders     │                                                     │ │
│ │Customers  │            Sales Performance Chart                  │ │
│ │Marketing  │                                                     │ │
│ │Analytics  │                                                     │ │
│ │Settings   │                                                     │ │
│ │           │                                                     │ │
│ │           │                                                     │ │
│ │           │                                                     │ │
│ └───────────┴────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────┐ ┌────────────────────────────────┐ │
│ │ Recent Orders              │ │ Top Selling Products           │ │
│ │                            │ │                                │ │
│ │ #1234 - May 11, 2025       │ │ 1. Designer T-shirt            │ │
│ │ Emily Parker • $78.50      │ │    $49.99 • 8 sold this month  │ │
│ │ Status: Processing         │ │                                │ │
│ │                            │ │ 2. Premium Jeans               │ │
│ │ #1228 - May 10, 2025       │ │    $89.99 • 5 sold this month  │ │
│ │ James Wilson • $125.75     │ │                                │ │
│ │ Status: Shipped            │ │ 3. Fashion Sunglasses          │ │
│ │                            │ │    $34.99 • 4 sold this month  │ │
│ │ #1225 - May 9, 2025        │ │                                │ │
│ │ Sophia Rodriguez • $64.25  │ │ [View All Products]            │ │
│ │ Status: Delivered          │ │                                │ │
│ │                            │ │ [+ Add New Product]            │ │
│ │ [View All Orders]          │ │                                │ │
│ └────────────────────────────┘ └────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Store Health                                                   │ │
│ │                                                                │ │
│ │ ⬤ 4 products low in stock                                     │ │
│ │ ⬤ 2 customer messages waiting for response                    │ │
│ │ ⬤ 85% positive customer reviews                               │ │
│ │ ⬤ 3 orders awaiting shipment                                  │ │
│ │                                                                │ │
│ │ [View Store Health Report]                                     │ │
│ └────────────────────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────────────────┘
```

## 11. Affiliate Program Interface (Detailed)

### Affiliate Application Dashboard

```
┌────────────────────────────────────────────────────────────────────┐
│ [Logo]   □ Search                      🔔 ❤️ 🛒 👤 [Customer Name] │
├────────────────────────────────────────────────────────────────────┤
│                                                                    │
│ My Account > Affiliate Application                                 │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Affiliate Application Status: Approved                         │ │
│ │                                                                │ │
│ │ Application ID: AF-78542         Submitted: May 5, 2025        │ │
│ │ Approved On: May 7, 2025                                       │ │
│ │                                                                │ │
│ │ [Go To Affiliate Dashboard]                                    │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Welcome to the Affiliate Program!                              │ │
│ │                                                                │ │
│ │ Congratulations on becoming an affiliate for Fashion Marketplace.│ │
│ │ You can now earn commissions by promoting products from our    │ │
│ │ marketplace. Here's how to get started:                        │ │
│ │                                                                │ │
│ │ 1. Create affiliate links for products you want to promote     │ │
│ │ 2. Share these links with your audience                        │ │
│ │ 3. Earn commissions when people make purchases through your links│ │
│ │ 4. Track your performance and optimize your strategy           │ │
│ │                                                                │ │
│ │ [Watch Affiliate Tutorial]                                     │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Your Commission Structure                                      │ │
│ │                                                                │ │
│ │ Base Commission Rate: 8%                                       │ │
│ │                                                                │ │
│ │ Category-Specific Rates:                                       │ │
│ │ • Fashion & Apparel: 10%                                       │ │
│ │ • Accessories: 8%                                              │ │
│ │ • Beauty Products: 12%                                         │ │
│ │ • Home Decor: 6%                                               │ │
│ │                                                                │ │
│ │ Performance Tiers:                                             │ │
│ │ • Silver (Current): Base rates                                 │ │
│ │ • Gold: Base rates + 2% (Requires $1,000 monthly sales)        │ │
│ │ • Platinum: Base rates + 5% (Requires $5,000 monthly sales)    │ │
│ │                                                                │ │
│ │ [View Full Commission Details]                                 │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Need Help?                                                     │ │
│ │                                                                │ │
│ │ Check our [Affiliate Guide] or [Contact Affiliate Support]     │ │
│ │ if you need assistance.                                        │ │
│ └────────────────────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────────────────┘
```

### Affiliate Dashboard (Active)

```
┌────────────────────────────────────────────────────────────────────┐
│ [Logo]   □ Search                      🔔 ❤️ 🛒 👤 [Customer Name] │
├────────────────────────────────────────────────────────────────────┤
│                                                                    │
│ My Account > Affiliate Dashboard                                   │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Affiliate Dashboard                                            │ │
│ │                                                                │ │
│ │ Affiliate ID: AF-78542                 Status: Active (Silver) │ │
│ │ Unique Referral Code: JANE15                                   │ │
│ └────────────────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │
│ │ Earnings    │ │ Clicks      │ │ Conversions │ │ Conversion  │   │
│ │ $342.85     │ │ 864         │ │ 28          │ │ Rate        │   │
│ │ This Month  │ │ This Week: 96│ │ This Month  │ │ 3.2%        │   │
│ │ Pending:    │ │ ↑ 12% from  │ │ ↑ 4 from    │ │ Avg Rate:   │   │
│ │ $128.50     │ │ last week   │ │ last month  │ │ 2.8%        │   │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘   │
│                                                                    │
│ ┌───────────┬────────────────────────────────────────────────────┐ │
│ │Dashboard  │                                                     │ │
│ │Links      │                                                     │ │
│ │Marketing  │                                                     │ │
│# Dashboard Pembeli (User) Sellzio SaaS (Lanjutan)

```
│ ┌───────────┬────────────────────────────────────────────────────┐ │
│ │Dashboard  │                                                     │ │
│ │Links      │                                                     │ │
│ │Marketing  │                                                     │ │
│ │Analytics  │            Earnings Performance Chart               │ │
│ │Payments   │                                                     │ │
│ │Settings   │                                                     │ │
│ │           │                                                     │ │
│ │           │                                                     │ │
│ │           │                                                     │ │
│ │           │                                                     │ │
│ └───────────┴────────────────────────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────┐ ┌────────────────────────────────┐ │
│ │ Top Performing Links       │ │ Recent Commissions             │ │
│ │                            │ │                                │ │
│ │ 1. Summer Dress Collection │ │ Order #4523 - May 11, 2025     │ │
│ │    495 clicks, 21 conversions│ │ Commission: $24.50              │ │
│ │    $245.85 generated       │ │ Status: Pending                │ │
│ │                            │ │                                │ │
│ │ 2. Designer Handbag Review │ │ Order #4498 - May 9, 2025      │ │
│ │    226 clicks, 4 conversions│ │ Commission: $18.75              │ │
│ │    $89.75 generated        │ │ Status: Approved               │ │
│ │                            │ │                                │ │
│ │ 3. Men's Fashion Guide     │ │ Order #4476 - May 8, 2025      │ │
│ │    143 clicks, 3 conversions│ │ Commission: $12.40              │ │
│ │    $65.25 generated        │ │ Status: Approved               │ │
│ │                            │ │                                │ │
│ │ [View All Links]           │ │ [View All Commissions]         │ │
│ └────────────────────────────┘ └────────────────────────────────┘ │
│                                                                    │
│ ┌────────────────────────────────────────────────────────────────┐ │
│ │ Create New Affiliate Link                                      │ │
│ │                                                                │ │
│ │ ┌────────────────────┐ ┌────────────────────────────────────┐ │ │
│ │ │ Product or URL    ▼│ │ https://fashion.sellzio.com/p/summer-dr│ │
│ │ └────────────────────┘ └────────────────────────────────────┘ │ │
│ │                                                                │ │
│ │ [Generate Link]                                                │ │
│ │                                                                │ │
│ │ Your affiliate links automatically include your unique code.   │ │
│ └────────────────────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────────────────┘
```

## 12. User Dashboard Mobile Views

### Mobile Dashboard Overview

```
┌──────────────────────────┐
│ Fashion Market      👤   │
├──────────────────────────┤
│                          │
│ Hello, Jane              │
│                          │
│ ┌────────────────────┐   │
│ │    Account Overview   │ │
│ │                      │ │
│ │ Orders: 5 Active     │ │
│ │ Wishlist: 12 items   │ │
│ │ Points: 450          │ │
│ │                      │ │
│ │ [View Details]       │ │
│ └────────────────────┘   │
│                          │
│ Quick Actions            │
│                          │
│ ┌──────┐ ┌──────┐ ┌──────┐│
│ │      │ │      │ │      ││
│ │Orders│ │Wishli│ │Track ││
│ │      │ │st    │ │Order ││
│ └──────┘ └──────┘ └──────┘│
│                          │
│ My Store                 │
│ ┌────────────────────┐   │
│ │                    │   │
│ │ Urban Style Boutique   │
│ │                    │   │
│ │ Sales: $1,245      │   │
│ │ Orders: 3 new      │   │
│ │                    │   │
│ │ [Go to Store]      │   │
│ └────────────────────┘   │
│                          │
│ My Affiliate Account     │
│ ┌────────────────────┐   │
│ │                    │   │
│ │ Earnings: $342.85  │   │
│ │ Clicks: 96 this week   │
│ │                    │   │
│ │ [Affiliate Dashboard]  │
│ └────────────────────┘   │
│                          │
│ Recently Viewed          │
│ ┌──────┐ ┌──────┐ ┌──────┐│
│ │      │ │      │ │      ││
│ │[img] │ │[img] │ │[img] ││
│ │$49.99│ │$89.99│ │$34.99││
│ └──────┘ └──────┘ └──────┘│
│                          │
│ [View All]               │
│                          │
├──────────────────────────┤
│ 🏠 📦 ❤️ 👤 ≡   │
└──────────────────────────┘
```

### Mobile Store Dashboard

```
┌──────────────────────────┐
│ My Store            👤   │
├──────────────────────────┤
│                          │
│ Urban Style Boutique     │
│ Status: Active           │
│                          │
│ Today's Summary          │
│ ┌────────────────────┐   │
│ │ Sales: $245        │   │
│ │ Orders: 3          │   │
│ │ Visitors: 42       │   │
│ │                    │   │
│ │ ↑ 18% from yesterday  │ │
│ └────────────────────┘   │
│                          │
│ Orders Requiring Action  │
│ ┌────────────────────┐   │
│ │ #1234 - May 11     │   │
│ │ $78.50             │   │
│ │ Status: Processing │   │
│ │                    │   │
│ │ [Process Order]    │   │
│ └────────────────────┘   │
│                          │
│ ┌────────────────────┐   │
│ │ #1228 - May 10     │   │
│ │ $125.75            │   │
│ │ Status: Processing │   │
│ │                    │   │
│ │ [Process Order]    │   │
│ └────────────────────┘   │
│                          │
│ Inventory Alerts         │
│ ┌────────────────────┐   │
│ │ 4 products low in stock│ │
│ │                    │   │
│ │ • Designer T-shirt │   │
│ │   2 left in stock  │   │
│ │                    │   │
│ │ • Premium Jeans    │   │
│ │   3 left in stock  │   │
│ │                    │   │
│ │ [Update Inventory] │   │
│ └────────────────────┘   │
│                          │
│ Quick Stats              │
│ ┌──────┐ ┌──────┐ ┌──────┐│
│ │Sales │ │Orders│ │Rating││
│ │$1,245│ │  18  │ │ 4.8★ ││
│ │+15%  │ │ +2   │ │ 25   ││
│ └──────┘ └──────┘ └──────┘│
│                          │
├──────────────────────────┤
│ 📊 📦 👥 💰 ≡   │
└──────────────────────────┘
```

### Mobile Affiliate Dashboard

```
┌──────────────────────────┐
│ My Affiliate        👤   │
├──────────────────────────┤
│                          │
│ Affiliate ID: AF-78542   │
│ Status: Active (Silver)  │
│                          │
│ Performance Overview     │
│ ┌────────────────────┐   │
│ │ Earnings: $342.85  │   │
│ │ Pending: $128.50   │   │
│ │ Clicks: 96 this week   │
│ │ Conversions: 28 (3.2%) │ │
│ └────────────────────┘   │
│                          │
│ Quick Link Generator     │
│ ┌────────────────────┐   │
│ │ [ Select Product ] │   │
│ │                    │   │
│ │ [Generate Link]    │   │
│ └────────────────────┘   │
│                          │
│ Recent Performance       │
│ ┌────────────────────┐   │
│ │                    │   │
│ │  [Line chart showing   │
│ │   earnings over time]  │
│ │                    │   │
│ │                    │   │
│ │ May 5-11, 2025     │   │
│ └────────────────────┘   │
│                          │
│ Top Products             │
│ ┌────────────────────┐   │
│ │ 1. Summer Dress    │   │
│ │    $245.85 generated   │
│ │    10% commission  │   │
│ │                    │   │
│ │ 2. Designer Handbag│   │
│ │    $89.75 generated│   │
│ │    8% commission   │   │
│ │                    │   │
│ │ [View All Products]│   │
│ └────────────────────┘   │
│                          │
│ Marketing Materials      │
│ ┌──────┐ ┌──────┐ ┌──────┐│
│ │      │ │      │ │      ││
│ │Banner│ │Social│ │Email ││
│ │Ads   │ │Posts │ │Temp  ││
│ └──────┘ └──────┘ └──────┘│
│                          │
│ [View All Materials]     │
│                          │
├──────────────────────────┤
│ 📊 🔗 📣 💰 ≡   │
└──────────────────────────┘
```

## 13. Advanced Profile & Account Management

### Profile Management

In addition to basic profile information, the user dashboard offers enhanced profile management:

- **Identity Verification**:
  - Multi-level verification system
  - ID verification (when required)
  - Address verification
  - Phone verification
  - Verification badges

- **Privacy Controls**:
  - Profile visibility settings
  - Data sharing preferences
  - Third-party app permissions
  - Data download option
  - Account deletion process

- **Linked Accounts**:
  - Social media connections
  - Other marketplace accounts
  - OAuth connections
  - Single sign-on options
  - Account linking benefits

### Account Security

- **Security Center**:
  - Overall security score
  - Security recommendations
  - Recent login activity
  - Suspicious activity alerts
  - Device management

- **Authentication Options**:
  - Password management with strength meter
  - Two-factor authentication methods:
    * SMS verification
    * Authentication apps
    * Email verification
    * Backup codes
  - Biometric login (fingerprint/face) for mobile

- **Access Management**:
  - Active sessions view
  - Remote logout capability
  - Login notifications
  - Geo-restriction options
  - Login attempt limits

## 14. Personalization & Preferences

### User Preferences

- **Interface Customization**:
  - Dashboard layout preferences
  - Default views (list vs grid)
  - Card/widget arrangement
  - Theme preferences (light/dark)
  - Content density settings

- **Shopping Preferences**:
  - Favorite categories
  - Size profiles for different product types
  - Style preferences
  - Budget ranges
  - Brand preferences

- **Communication Settings**:
  - Contact preferences by type
  - Time-of-day preferences
  - Frequency controls
  - Language preferences
  - Format preferences (HTML vs text email)

### Personalization Engine

- **Interest Management**:
  - Interest categories selection
  - Browse history insights
  - Search history management
  - Preference strength indicators
  - "Not interested" options

- **Recommendation Tuning**:
  - Feedback on recommendations
  - Recommendation sources (browse, purchase, similar users)
  - Discovery mode toggle
  - Recommendation diversity controls
  - Seasonal preference adjustments

## 15. Loyalty & Rewards

### Loyalty Program Interface

- **Program Overview**:
  - Current tier and benefits
  - Points balance and history
  - Points earning methods
  - Expiration warnings
  - Tier requirements

- **Points Activity**:
  - Points earning history
  - Points redemption history
  - Pending points
  - Points expiration timeline
  - Special earning opportunities

- **Rewards Catalog**:
  - Available rewards by category
  - Points required
  - Featured/new rewards
  - Limited-time offers
  - Wishlist for rewards

- **Achievement System**:
  - Badges and achievements
  - Milestone rewards
  - Progress trackers
  - Leaderboards (if applicable)
  - Achievement history

## 16. Design System Elements

### Typography Implementation

- **Heading Hierarchy**:
  - H1: 24px/28px (section titles)
  - H2: 20px/24px (card titles)
  - H3: 18px/22px (subsection headings)
  - H4: 16px/20px (widget titles)

- **Body Text**:
  - Regular: 16px/24px (primary content)
  - Small: 14px/20px (secondary content)
  - Micro: 12px/16px (metadata, timestamps)

### Color Application

- **Marketplace Colors**:
  - Inherits tenant's brand colors for main UI elements
  - Neutral backgrounds and borders for content areas
  - Consistent color usage for status indicators across roles

- **Functional Colors**:
  - Success: Green (#10B981)
  - Warning: Amber (#FBBF24)
  - Error: Red (#EF4444)
  - Info: Blue (#3B82F6)

- **Role-specific Colors**:
  - Store: Purple accents (#8B5CF6)
  - Affiliate: Orange accents (#F59E0B)
  - Regular user: Default brand colors

### Component Design

- **Cards & Containers**:
  - Consistent padding (16px/20px)
  - Subtle border radius (8px)
  - Light shadows for elevation
  - Clear visual hierarchy within cards
  - Responsive behavior (stack on mobile)

- **Navigation Elements**:
  - Tab navigation for related sections
  - Breadcrumbs for deep navigation paths
  - Sidebar navigation on desktop
  - Bottom navigation on mobile
  - Contextual back buttons

- **Data Visualization**:
  - Simple charts for performance data
  - Visual progress indicators
  - Status badges with color coding
  - Heat maps for temporal data
  - Comparative metrics with directional indicators

## 17. Integration Points

### Social Integration

- **Social Sharing**:
  - Share products with friends
  - Share wishlists
  - Social login options
  - Social profile connections
  - Achievement sharing

- **Social Commerce**:
  - Follow favorite stores
  - Join shopping groups
  - Participate in group deals
  - Share purchases
  - Social recommendations

### External Tools Integration

- **Content Creation**:
  - Connect to design tools for store content
  - Social media scheduling integration
  - Video creation/editing tools
  - Writing assistants
  - Image optimization

- **Analytics Connections**:
  - Google Analytics connection
  - Social media insights
  - Content performance tracking
  - Audience analytics
  - Conversion tracking

## 18. Advanced Store Features

### Store Inventory Management

- **Product Catalog Management**:
  - Bulk product upload
  - Inventory tracking
  - Variant management
  - Digital product delivery
  - Stock alerts

- **Product Content**:
  - Rich product descriptions
  - Multiple image management
  - Video product demonstrations
  - Specification tables
  - SEO optimization

### Store Promotion Tools

- **Discount Management**:
  - Coupon code generator
  - Sale pricing
  - Bundle discounts
  - Buy one get one
  - Limited-time offers

- **Marketing Integration**:
  - Email campaign connection
  - Social media post scheduler
  - Ad creation tools
  - Audience targeting
  - Performance tracking

### Store Analytics

- **Sales Analytics**:
  - Sales by time period
  - Sales by product/category
  - Revenue forecasting
  - Seasonal trend analysis
  - Comparative metrics

- **Customer Analytics**:
  - Customer acquisition source
  - Customer lifetime value
  - Repeat purchase rate
  - Average order value
  - Customer segmentation

## 19. Advanced Affiliate Features

### Affiliate Content Tools

- **Content Creation**:
  - Product review templates
  - Comparison chart generator
  - Buying guide templates
  - Social media post ideas
  - Email newsletter templates

- **Rich Media**:
  - Product image gallery
  - Video embeds
  - Interactive product displays
  - Size/color variant showcases
  - Price comparison widgets

### Affiliate Analytics

- **Traffic Analytics**:
  - Traffic source breakdown
  - User demographics
  - Device analytics
  - Behavior flow
  - Engagement metrics

- **Conversion Analytics**:
  - Conversion funnel visualization
  - Cart abandonment data
  - Product performance
  - Conversion by time
  - Audience segment performance

### Affiliate Strategy

- **Performance Optimization**:
  - Best-performing content types
  - Top converting products
  - Optimal posting times
  - A/B testing results
  - Seasonal strategy suggestions

- **Growth Tools**:
  - Performance projections
  - Goal setting
  - Strategy templates
  - Benchmark comparisons
  - Trend spotting

## 20. User Dashboard Implementation Considerations

### Technical Implementation

- **Progressive Enhancement**:
  - Core functionality works without JavaScript
  - Enhanced functionality with JavaScript
  - Fallbacks for unsupported features
  - Browser compatibility considerations
  - Performance optimization

- **Data Loading Strategies**:
  - Critical data loaded first
  - Progressive data loading
  - Lazy loading for off-screen content
  - Data prefetching for common paths
  - Cached data with refresh mechanism

### Performance Optimization

- **Resource Loading**:
  - Code splitting by dashboard section
  - Image optimization
  - Resource prioritization
  - Critical CSS extraction
  - Tree shaking for unused code

- **Rendering Strategy**:
  - Server-side rendering for initial load
  - Hydration for interactive elements
  - Client-side rendering for dynamic content
  - Static generation for fixed content
  - Incremental static regeneration for semi-dynamic content

### Offline Capabilities

- **Service Worker Implementation**:
  - Offline page caching
  - Data caching
  - Background sync for forms/actions
  - Offline indicator
  - Graceful degradation strategy

- **Data Persistence**:
  - Local storage for preferences
  - IndexedDB for larger datasets
  - Form data preservation
  - Draft saving
  - Conflict resolution for offline changes

## Conclusion

The User Dashboard in Sellzio SaaS provides a comprehensive and adaptable interface that allows users to manage their shopping experience, while also offering seamless transitions to store owner or affiliate roles when desired. The design emphasizes:

1. **Role Flexibility**: Users can easily navigate between being shoppers, store owners, and affiliates within a single account.

2. **Progressive Disclosure**: Advanced features appear only when relevant, preventing interface overload.

3. **Consistent Experience**: Core UI elements remain consistent across roles, with role-specific modules activated as needed.

4. **Mobile-First Design**: All functionality is accessible and usable on mobile devices, with desktop views offering expanded capabilities.

5. **Personalization**: The dashboard adapts to user behavior and preferences over time for a more relevant experience.

This comprehensive approach creates a unified ecosystem where users can seamlessly transition between consumer and business roles while maintaining a cohesive user experience throughout the platform.

# Struktur File Lengkap Sellzio SaaS Multi-level E-commerce

```
sellzio-saas/
├── frontend/ (Next.js)
│   ├── public/
│   │   ├── favicon.ico
│   │   ├── logo.svg
│   │   ├── images/
│   │   │   ├── placeholders/
│   │   │   ├── icons/
│   │   │   └── backgrounds/
│   │   └── fonts/
│   ├── src/
│   │   ├── app/
│   │   │   ├── (admin)/
│   │   │   │   ├── admin/
│   │   │   │   │   ├── dashboard/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   ├── loading.tsx
│   │   │   │   │   │   └── error.tsx
│   │   │   │   │   ├── tenants/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   ├── [id]/
│   │   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   │   ├── edit/
│   │   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   │   ├── stores/
│   │   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   │   └── settings/
│   │   │   │   │   │   │       └── page.tsx
│   │   │   │   │   │   └── new/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── stores/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   ├── [id]/
│   │   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   │   ├── edit/
│   │   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   │   ├── products/
│   │   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   │   └── analytics/
│   │   │   │   │   │   │       └── page.tsx
│   │   │   │   │   │   └── categories/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── users/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   └── [id]/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── products/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   └── [id]/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── financial/
│   │   │   │   │   │   ├── overview/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── transactions/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── payouts/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── content/
│   │   │   │   │   │   ├── themes/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── pages/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── email-templates/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── system/
│   │   │   │   │   │   ├── settings/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── integrations/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── logs/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   └── layout.tsx
│   │   │   │   └── login/
│   │   │   │       └── page.tsx
│   │   │   ├── (auth)/
│   │   │   │   ├── login/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── register/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── forgot-password/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── reset-password/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── layout.tsx
│   │   │   ├── (tenant)/
│   │   │   │   ├── tenant/
│   │   │   │   │   ├── dashboard/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── stores/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   ├── [id]/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── new/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── products/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   ├── [id]/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── categories/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── import/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── orders/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   └── [id]/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── customers/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   └── [id]/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── affiliates/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   ├── [id]/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── commissions/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── marketing/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── marketing/
│   │   │   │   │   │   ├── campaigns/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── promotions/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── email/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── content/
│   │   │   │   │   │   ├── pages/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── blog/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── navigation/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── live/
│   │   │   │   │   │   ├── schedule/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── studio/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── videos/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── analytics/
│   │   │   │   │   │   ├── overview/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── sales/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── customers/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── reports/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── financial/
│   │   │   │   │   │   ├── overview/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── payouts/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── transactions/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── settings/
│   │   │   │   │   │   ├── profile/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── branding/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── domain/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── users/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── integrations/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   └── layout.tsx
│   │   │   │   └── login/
│   │   │   │       └── page.tsx
│   │   │   ├── (store)/
│   │   │   │   ├── store/
│   │   │   │   │   ├── dashboard/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── products/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   ├── [id]/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── new/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── orders/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   └── [id]/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── customers/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   └── [id]/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── marketing/
│   │   │   │   │   │   ├── promotions/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── affiliate/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── analytics/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── settings/
│   │   │   │   │   │   ├── profile/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── shipping/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── policies/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   └── layout.tsx
│   │   │   │   ├── register/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── login/
│   │   │   │       └── page.tsx
│   │   │   ├── (user)/
│   │   │   │   ├── account/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   ├── orders/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   └── [id]/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── wishlist/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── addresses/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── payment-methods/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── store-application/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   └── wizard/
│   │   │   │   │   │       ├── step1/
│   │   │   │   │   │       │   └── page.tsx
│   │   │   │   │   │       ├── step2/
│   │   │   │   │   │       │   └── page.tsx
│   │   │   │   │   │       ├── step3/
│   │   │   │   │   │       │   └── page.tsx
│   │   │   │   │   │       ├── step4/
│   │   │   │   │   │       │   └── page.tsx
│   │   │   │   │   │       └── review/
│   │   │   │   │   │           └── page.tsx
│   │   │   │   │   ├── affiliate-application/
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   └── wizard/
│   │   │   │   │   │       ├── step1/
│   │   │   │   │   │       │   └── page.tsx
│   │   │   │   │   │       ├── step2/
│   │   │   │   │   │       │   └── page.tsx
│   │   │   │   │   │       ├── step3/
│   │   │   │   │   │       │   └── page.tsx
│   │   │   │   │   │       └── review/
│   │   │   │   │   │           └── page.tsx
│   │   │   │   │   ├── affiliate/
│   │   │   │   │   │   ├── dashboard/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── links/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── marketing/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── analytics/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── payments/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   ├── settings/
│   │   │   │   │   │   ├── profile/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   ├── security/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── notifications/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   └── layout.tsx
│   │   │   │   ├── register/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── login/
│   │   │   │       └── page.tsx
│   │   │   ├── api/
│   │   │   │   ├── auth/
│   │   │   │   │   ├── [...nextauth]/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── register/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── verify/
│   │   │   │   │       └── route.ts
│   │   │   │   ├── admin/
│   │   │   │   │   ├── tenants/
│   │   │   │   │   │   ├── route.ts
│   │   │   │   │   │   └── [id]/
│   │   │   │   │   │       └── route.ts
│   │   │   │   │   ├── stores/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── dashboard/
│   │   │   │   │       └── route.ts
│   │   │   │   ├── tenant/
│   │   │   │   │   ├── [id]/
│   │   │   │   │   │   ├── stores/
│   │   │   │   │   │   │   └── route.ts
│   │   │   │   │   │   ├── products/
│   │   │   │   │   │   │   └── route.ts
│   │   │   │   │   │   └── dashboard/
│   │   │   │   │   │       └── route.ts
│   │   │   │   │   └── domain/
│   │   │   │   │       └── route.ts
│   │   │   │   ├── store/
│   │   │   │   │   ├── [id]/
│   │   │   │   │   │   ├── products/
│   │   │   │   │   │   │   └── route.ts
│   │   │   │   │   │   ├── orders/
│   │   │   │   │   │   │   └── route.ts
│   │   │   │   │   │   └── dashboard/
│   │   │   │   │   │       └── route.ts
│   │   │   │   │   └── application/
│   │   │   │   │       └── route.ts
│   │   │   │   ├── user/
│   │   │   │   │   ├── profile/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── orders/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── wishlist/
│   │   │   │   │       └── route.ts
│   │   │   │   ├── affiliate/
│   │   │   │   │   ├── links/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── commissions/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── application/
│   │   │   │   │       └── route.ts
│   │   │   │   └── webhooks/
│   │   │   │       ├── payment/
│   │   │   │       │   └── route.ts
│   │   │   │       └── notification/
│   │   │   │           └── route.ts
│   │   │   ├── [tenant]/
│   │   │   │   ├── page.tsx
│   │   │   │   ├── products/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   └── [slug]/
│   │   │   │   │       └── page.tsx
│   │   │   │   ├── categories/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   └── [slug]/
│   │   │   │   │       └── page.tsx
│   │   │   │   ├── stores/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   └── [slug]/
│   │   │   │   │       └── page.tsx
│   │   │   │   ├── search/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── cart/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── checkout/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── live/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   └── [id]/
│   │   │   │   │       └── page.tsx
│   │   │   │   ├── blog/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   └── [slug]/
│   │   │   │   │       └── page.tsx
│   │   │   │   └── layout.tsx
│   │   │   ├── not-found.tsx
│   │   │   ├── error.tsx
│   │   │   ├── layout.tsx
│   │   │   ├── page.tsx
│   │   │   └── globals.css
│   │   ├── components/
│   │   │   ├── admin/
│   │   │   │   ├── dashboard/
│   │   │   │   │   ├── StatsCard.tsx
│   │   │   │   │   ├── RevenueChart.tsx
│   │   │   │   │   └── ActivityFeed.tsx
│   │   │   │   ├── tenants/
│   │   │   │   │   ├── TenantList.tsx
│   │   │   │   │   ├── TenantCard.tsx
│   │   │   │   │   └── TenantForm.tsx
│   │   │   │   ├── stores/
│   │   │   │   │   ├── StoreList.tsx
│   │   │   │   │   └── StoreCard.tsx
│   │   │   │   ├── users/
│   │   │   │   │   ├── UserList.tsx
│   │   │   │   │   └── UserCard.tsx
│   │   │   │   ├── layout/
│   │   │   │   │   ├── AdminHeader.tsx
│   │   │   │   │   ├── AdminSidebar.tsx
│   │   │   │   │   └── AdminMobileNav.tsx
│   │   │   │   └── shared/
│   │   │   │       ├── ActionBar.tsx
│   │   │   │       ├── FilterPanel.tsx
│   │   │   │       └── SearchBox.tsx
│   │   │   ├── tenant/
│   │   │   │   ├── dashboard/
│   │   │   │   │   ├── StatsCard.tsx
│   │   │   │   │   ├── RevenueChart.tsx
│   │   │   │   │   └── ActivityFeed.tsx
│   │   │   │   ├── stores/
│   │   │   │   │   ├── StoreList.tsx
│   │   │   │   │   ├── StoreCard.tsx
│   │   │   │   │   └── StoreForm.tsx
│   │   │   │   ├── products/
│   │   │   │   │   ├── ProductList.tsx
│   │   │   │   │   ├── ProductCard.tsx
│   │   │   │   │   └── ProductForm.tsx
│   │   │   │   ├── orders/
│   │   │   │   │   ├── OrderList.tsx
│   │   │   │   │   ├── OrderCard.tsx
│   │   │   │   │   └── OrderDetail.tsx
│   │   │   │   ├── affiliates/
│   │   │   │   │   ├── AffiliateList.tsx
│   │   │   │   │   ├── AffiliateCard.tsx
│   │   │   │   │   └── CommissionEditor.tsx
│   │   │   │   ├── live/
│   │   │   │   │   ├── StreamScheduler.tsx
│   │   │   │   │   ├── BroadcastControls.tsx
│   │   │   │   │   └── VideoPlayer.tsx
│   │   │   │   ├── layout/
│   │   │   │   │   ├── TenantHeader.tsx
│   │   │   │   │   ├── TenantSidebar.tsx
│   │   │   │   │   └── TenantMobileNav.tsx
│   │   │   │   └── shared/
│   │   │   │       ├── ActionBar.tsx
│   │   │   │       ├── FilterPanel.tsx
│   │   │   │       └── SearchBox.tsx
│   │   │   ├── store/
│   │   │   │   ├── dashboard/
│   │   │   │   │   ├── StatsCard.tsx
│   │   │   │   │   ├── SalesChart.tsx
│   │   │   │   │   └── RecentOrders.tsx
│   │   │   │   ├── products/
│   │   │   │   │   ├── ProductList.tsx
│   │   │   │   │   ├── ProductCard.tsx
│   │   │   │   │   └── ProductForm.tsx
│   │   │   │   ├── orders/
│   │   │   │   │   ├── OrderList.tsx
│   │   │   │   │   ├── OrderCard.tsx
│   │   │   │   │   └── OrderDetail.tsx
│   │   │   │   ├── marketing/
│   │   │   │   │   ├── PromotionForm.tsx
│   │   │   │   │   └── AffiliateSettings.tsx
│   │   │   │   ├── layout/
│   │   │   │   │   ├── StoreHeader.tsx
│   │   │   │   │   ├── StoreSidebar.tsx
│   │   │   │   │   └── StoreMobileNav.tsx
│   │   │   │   └── application/
│   │   │   │       ├── ApplicationForm.tsx
│   │   │   │       ├── StepIndicator.tsx
│   │   │   │       └── FormSections.tsx
│   │   │   ├── user/
│   │   │   │   ├── account/
│   │   │   │   │   ├── AccountOverview.tsx
│   │   │   │   │   ├── OrderList.tsx
│   │   │   │   │   └── OrderDetail.tsx
│   │   │   │   ├── profile/
│   │   │   │   │   ├── ProfileForm.tsx
│   │   │   │   │   ├── AddressBook.tsx
│   │   │   │   │   └── PaymentMethods.tsx
│   │   │   │   ├── wishlist/
│   │   │   │   │   ├── WishlistItem.tsx
│   │   │   │   │   └── WishlistGrid.tsx
│   │   │   │   ├── affiliate/
│   │   │   │   │   ├── AffiliateDashboard.tsx
│   │   │   │   │   ├── LinkGenerator.tsx
│   │   │   │   │   ├── CommissionTracker.tsx
│   │   │   │   │   └── MarketingMaterials.tsx
│   │   │   │   ├── store-application/
│   │   │   │   │   ├── ApplicationProgress.tsx
│   │   │   │   │   └── ApplicationSteps.tsx
│   │   │   │   └── layout/
│   │   │   │       ├── UserHeader.tsx
│   │   │   │       ├── UserSidebar.tsx
│   │   │   │       └── UserMobileNav.tsx
│   │   │   ├── storefront/
│   │   │   │   ├── navigation/
│   │   │   │   │   ├── MainNav.tsx
│   │   │   │   │   ├── CategoryMenu.tsx
│   │   │   │   │   └── MobileMenu.tsx
│   │   │   │   ├── product/
│   │   │   │   │   ├── ProductCard.tsx
│   │   │   │   │   ├── ProductGrid.tsx
│   │   │   │   │   ├── ProductDetail.tsx
│   │   │   │   │   └── ProductReviews.tsx
│   │   │   │   ├── store/
│   │   │   │   │   ├── StoreCard.tsx
│   │   │   │   │   ├── StoreGrid.tsx
│   │   │   │   │   └── StoreProfile.tsx
│   │   │   │   ├── cart/
│   │   │   │   │   ├── CartItem.tsx
│   │   │   │   │   ├── CartSummary.tsx
│# Struktur File Lengkap Sellzio SaaS Multi-level E-commerce (Lanjutan)

```
│   │   │   │   ├── cart/
│   │   │   │   │   ├── CartItem.tsx
│   │   │   │   │   ├── CartSummary.tsx
│   │   │   │   │   └── MiniCart.tsx
│   │   │   │   ├── checkout/
│   │   │   │   │   ├── CheckoutForm.tsx
│   │   │   │   │   ├── PaymentForm.tsx
│   │   │   │   │   ├── ShippingForm.tsx
│   │   │   │   │   └── OrderSummary.tsx
│   │   │   │   ├── live/
│   │   │   │   │   ├── LiveStream.tsx
│   │   │   │   │   ├── LiveChat.tsx
│   │   │   │   │   └── LiveProductCarousel.tsx
│   │   │   │   ├── search/
│   │   │   │   │   ├── SearchResults.tsx
│   │   │   │   │   ├── SearchFilters.tsx
│   │   │   │   │   └── NoResults.tsx
│   │   │   │   └── home/
│   │   │   │       ├── HeroBanner.tsx
│   │   │   │       ├── FeaturedProducts.tsx
│   │   │   │       ├── PopularStores.tsx
│   │   │   │       └── PromotionSection.tsx
│   │   │   ├── shared/
│   │   │   │   ├── layout/
│   │   │   │   │   ├── Header.tsx
│   │   │   │   │   ├── Footer.tsx
│   │   │   │   │   ├── Sidebar.tsx
│   │   │   │   │   └── Container.tsx
│   │   │   │   ├── ui/
│   │   │   │   │   ├── Button.tsx
│   │   │   │   │   ├── Card.tsx
│   │   │   │   │   ├── Badge.tsx
│   │   │   │   │   ├── Avatar.tsx
│   │   │   │   │   ├── Input.tsx
│   │   │   │   │   ├── Select.tsx
│   │   │   │   │   ├── Checkbox.tsx
│   │   │   │   │   ├── RadioGroup.tsx
│   │   │   │   │   ├── Switch.tsx
│   │   │   │   │   ├── Tabs.tsx
│   │   │   │   │   ├── Table.tsx
│   │   │   │   │   ├── Modal.tsx
│   │   │   │   │   ├── Dropdown.tsx
│   │   │   │   │   ├── Pagination.tsx
│   │   │   │   │   ├── Tooltip.tsx
│   │   │   │   │   ├── Toast.tsx
│   │   │   │   │   └── Skeleton.tsx
│   │   │   │   ├── forms/
│   │   │   │   │   ├── Form.tsx
│   │   │   │   │   ├── FormField.tsx
│   │   │   │   │   ├── FormItem.tsx
│   │   │   │   │   ├── FormLabel.tsx
│   │   │   │   │   ├── FormControl.tsx
│   │   │   │   │   ├── FormMessage.tsx
│   │   │   │   │   └── FormSection.tsx
│   │   │   │   ├── data-display/
│   │   │   │   │   ├── StatusBadge.tsx
│   │   │   │   │   ├── DataCard.tsx
│   │   │   │   │   ├── Stat.tsx
│   │   │   │   │   ├── Timeline.tsx
│   │   │   │   │   └── EmptyState.tsx
│   │   │   │   ├── feedback/
│   │   │   │   │   ├── Alert.tsx
│   │   │   │   │   ├── Progress.tsx
│   │   │   │   │   ├── Spinner.tsx
│   │   │   │   │   └── ErrorBoundary.tsx
│   │   │   │   └── utils/
│   │   │   │       ├── ThemeProvider.tsx
│   │   │   │       ├── MotionProvider.tsx
│   │   │   │       └── MediaQuery.tsx
│   │   │   └── auth/
│   │   │       ├── LoginForm.tsx
│   │   │       ├── RegisterForm.tsx
│   │   │       ├── ForgotPasswordForm.tsx
│   │   │       └── ResetPasswordForm.tsx
│   │   ├── hooks/
│   │   │   ├── common/
│   │   │   │   ├── useForm.ts
│   │   │   │   ├── useToast.ts
│   │   │   │   ├── useLocalStorage.ts
│   │   │   │   ├── useMediaQuery.ts
│   │   │   │   └── useOutsideClick.ts
│   │   │   ├── admin/
│   │   │   │   ├── useTenants.ts
│   │   │   │   ├── useStores.ts
│   │   │   │   ├── useUsers.ts
│   │   │   │   └── useStats.ts
│   │   │   ├── tenant/
│   │   │   │   ├── useStores.ts
│   │   │   │   ├── useProducts.ts
│   │   │   │   ├── useOrders.ts
│   │   │   │   ├── useCustomers.ts
│   │   │   │   ├── useAffiliates.ts
│   │   │   │   └── useRevenue.ts
│   │   │   ├── store/
│   │   │   │   ├── useProducts.ts
│   │   │   │   ├── useOrders.ts
│   │   │   │   ├── useCustomers.ts
│   │   │   │   └── useSales.ts
│   │   │   ├── user/
│   │   │   │   ├── useOrders.ts
│   │   │   │   ├── useWishlist.ts
│   │   │   │   ├── useAddresses.ts
│   │   │   │   ├── usePaymentMethods.ts
│   │   │   │   ├── useAffiliate.ts
│   │   │   │   └── useStoreApplication.ts
│   │   │   └── auth/
│   │   │       ├── useAuth.ts
│   │   │       └── usePermissions.ts
│   │   ├── lib/
│   │   │   ├── api/
│   │   │   │   ├── client.ts
│   │   │   │   ├── admin.ts
│   │   │   │   ├── tenant.ts
│   │   │   │   ├── store.ts
│   │   │   │   ├── user.ts
│   │   │   │   └── auth.ts
│   │   │   ├── utils/
│   │   │   │   ├── formatters.ts
│   │   │   │   ├── validators.ts
│   │   │   │   ├── helpers.ts
│   │   │   │   └── constants.ts
│   │   │   ├── multi-tenant/
│   │   │   │   ├── tenant-context.ts
│   │   │   │   ├── domain-parser.ts
│   │   │   │   └── tenant-resolver.ts
│   │   │   ├── auth/
│   │   │   │   ├── session.ts
│   │   │   │   ├── permissions.ts
│   │   │   │   └── roles.ts
│   │   │   └── db/
│   │   │       └── prisma.ts
│   │   ├── context/
│   │   │   ├── AuthContext.tsx
│   │   │   ├── TenantContext.tsx
│   │   │   ├── CartContext.tsx
│   │   │   ├── ThemeContext.tsx
│   │   │   └── NotificationContext.tsx
│   │   ├── types/
│   │   │   ├── admin.ts
│   │   │   ├── tenant.ts
│   │   │   ├── store.ts
│   │   │   ├── user.ts
│   │   │   ├── product.ts
│   │   │   ├── order.ts
│   │   │   ├── customer.ts
│   │   │   ├── affiliate.ts
│   │   │   └── common.ts
│   │   ├── config/
│   │   │   ├── site.ts
│   │   │   ├── auth.ts
│   │   │   ├── menu.ts
│   │   │   └── constants.ts
│   │   └── middleware.ts
│   ├── tailwind.config.js
│   ├── postcss.config.js
│   ├── next.config.js
│   ├── tsconfig.json
│   ├── package.json
│   └── README.md
├── backend/ (NestJS)
│   ├── src/
│   │   ├── main.ts
│   │   ├── app.module.ts
│   │   ├── config/
│   │   │   ├── configuration.ts
│   │   │   ├── database.config.ts
│   │   │   ├── auth.config.ts
│   │   │   └── environment.ts
│   │   ├── common/
│   │   │   ├── decorators/
│   │   │   │   ├── current-user.decorator.ts
│   │   │   │   ├── roles.decorator.ts
│   │   │   │   └── tenant.decorator.ts
│   │   │   ├── guards/
│   │   │   │   ├── auth.guard.ts
│   │   │   │   ├── roles.guard.ts
│   │   │   │   └── tenant.guard.ts
│   │   │   ├── interceptors/
│   │   │   │   ├── tenant.interceptor.ts
│   │   │   │   ├── transform.interceptor.ts
│   │   │   │   └── logging.interceptor.ts
│   │   │   ├── middleware/
│   │   │   │   ├── tenant-resolver.middleware.ts
│   │   │   │   └── logging.middleware.ts
│   │   │   ├── filters/
│   │   │   │   ├── http-exception.filter.ts
│   │   │   │   └── validation.filter.ts
│   │   │   ├── pipes/
│   │   │   │   └── validation.pipe.ts
│   │   │   └── utils/
│   │   │       ├── helpers.ts
│   │   │       ├── validators.ts
│   │   │       └── formatters.ts
│   │   ├── database/
│   │   │   ├── prisma.service.ts
│   │   │   ├── migrations/
│   │   │   └── seeds/
│   │   │       ├── seed.ts
│   │   │       ├── admin.seed.ts
│   │   │       ├── tenant.seed.ts
│   │   │       └── store.seed.ts
│   │   ├── auth/
│   │   │   ├── auth.module.ts
│   │   │   ├── auth.controller.ts
│   │   │   ├── auth.service.ts
│   │   │   ├── strategies/
│   │   │   │   ├── jwt.strategy.ts
│   │   │   │   └── local.strategy.ts
│   │   │   ├── guards/
│   │   │   │   ├── jwt-auth.guard.ts
│   │   │   │   └── local-auth.guard.ts
│   │   │   └── dto/
│   │   │       ├── login.dto.ts
│   │   │       ├── register.dto.ts
│   │   │       └── reset-password.dto.ts
│   │   ├── admin/
│   │   │   ├── admin.module.ts
│   │   │   ├── admin.controller.ts
│   │   │   ├── admin.service.ts
│   │   │   ├── dto/
│   │   │   │   ├── create-admin.dto.ts
│   │   │   │   └── update-admin.dto.ts
│   │   │   └── entities/
│   │   │       └── admin.entity.ts
│   │   ├── tenant/
│   │   │   ├── tenant.module.ts
│   │   │   ├── tenant.controller.ts
│   │   │   ├── tenant.service.ts
│   │   │   ├── dto/
│   │   │   │   ├── create-tenant.dto.ts
│   │   │   │   └── update-tenant.dto.ts
│   │   │   ├── entities/
│   │   │   │   └── tenant.entity.ts
│   │   │   └── resolver/
│   │   │       └── tenant-resolver.service.ts
│   │   ├── store/
│   │   │   ├── store.module.ts
│   │   │   ├── store.controller.ts
│   │   │   ├── store.service.ts
│   │   │   ├── dto/
│   │   │   │   ├── create-store.dto.ts
│   │   │   │   ├── update-store.dto.ts
│   │   │   │   └── store-application.dto.ts
│   │   │   └── entities/
│   │   │       └── store.entity.ts
│   │   ├── product/
│   │   │   ├── product.module.ts
│   │   │   ├── product.controller.ts
│   │   │   ├── product.service.ts
│   │   │   ├── dto/
│   │   │   │   ├── create-product.dto.ts
│   │   │   │   └── update-product.dto.ts
│   │   │   ├── entities/
│   │   │   │   └── product.entity.ts
│   │   │   └── category/
│   │   │       ├── category.controller.ts
│   │   │       ├── category.service.ts
│   │   │       └── entities/
│   │   │           └── category.entity.ts
│   │   ├── order/
│   │   │   ├── order.module.ts
│   │   │   ├── order.controller.ts
│   │   │   ├── order.service.ts
│   │   │   ├── dto/
│   │   │   │   ├── create-order.dto.ts
│   │   │   │   └── update-order.dto.ts
│   │   │   ├── entities/
│   │   │   │   ├── order.entity.ts
│   │   │   │   └── order-item.entity.ts
│   │   │   └── status/
│   │   │       └── order-status.enum.ts
│   │   ├── user/
│   │   │   ├── user.module.ts
│   │   │   ├── user.controller.ts
│   │   │   ├── user.service.ts
│   │   │   ├── dto/
│   │   │   │   ├── create-user.dto.ts
│   │   │   │   └── update-user.dto.ts
│   │   │   ├── entities/
│   │   │   │   ├── user.entity.ts
│   │   │   │   ├── address.entity.ts
│   │   │   │   └── payment-method.entity.ts
│   │   │   └── roles/
│   │   │       └── role.enum.ts
│   │   ├── affiliate/
│   │   │   ├── affiliate.module.ts
│   │   │   ├── affiliate.controller.ts
│   │   │   ├── affiliate.service.ts
│   │   │   ├── dto/
│   │   │   │   ├── create-affiliate.dto.ts
│   │   │   │   ├── update-affiliate.dto.ts
│   │   │   │   └── affiliate-application.dto.ts
│   │   │   ├── entities/
│   │   │   │   ├── affiliate.entity.ts
│   │   │   │   └── commission.entity.ts
│   │   │   └── links/
│   │   │       ├── link.controller.ts
│   │   │       ├── link.service.ts
│   │   │       └── entities/
│   │   │           └── link.entity.ts
│   │   ├── payment/
│   │   │   ├── payment.module.ts
│   │   │   ├── payment.controller.ts
│   │   │   ├── payment.service.ts
│   │   │   ├── providers/
│   │   │   │   ├── stripe.provider.ts
│   │   │   │   └── paypal.provider.ts
│   │   │   └── webhook/
│   │   │       └── webhook.controller.ts
│   │   ├── notification/
│   │   │   ├── notification.module.ts
│   │   │   ├── notification.controller.ts
│   │   │   ├── notification.service.ts
│   │   │   ├── providers/
│   │   │   │   ├── email.provider.ts
│   │   │   │   ├── push.provider.ts
│   │   │   │   └── sms.provider.ts
│   │   │   └── templates/
│   │   │       ├── email/
│   │   │       └── push/
│   │   ├── analytics/
│   │   │   ├── analytics.module.ts
│   │   │   ├── analytics.controller.ts
│   │   │   ├── analytics.service.ts
│   │   │   └── services/
│   │   │       ├── sales.service.ts
│   │   │       ├── user.service.ts
│   │   │       └── traffic.service.ts
│   │   ├── file/
│   │   │   ├── file.module.ts
│   │   │   ├── file.controller.ts
│   │   │   ├── file.service.ts
│   │   │   └── providers/
│   │   │       ├── cloudinary.provider.ts
│   │   │       └── s3.provider.ts
│   │   ├── live/
│   │   │   ├── live.module.ts
│   │   │   ├── live.controller.ts
│   │   │   ├── live.service.ts
│   │   │   └── providers/
│   │   │       └── streaming.provider.ts
│   │   └── multi-tenant/
│   │       ├── multi-tenant.module.ts
│   │       ├── tenant-context.service.ts
│   │       └── tenant-connection.service.ts
│   ├── prisma/
│   │   ├── schema.prisma
│   │   ├── migrations/
│   │   └── seed.ts
│   ├── test/
│   │   ├── app.e2e-spec.ts
│   │   ├── jest-e2e.json
│   │   └── auth/
│   │       └── auth.e2e-spec.ts
│   ├── nest-cli.json
│   ├── tsconfig.json
│   ├── tsconfig.build.json
│   ├── package.json
│   └── README.md
├── infrastructure/
│   ├── docker/
│   │   ├── frontend/
│   │   │   └── Dockerfile
│   │   ├── backend/
│   │   │   └── Dockerfile
│   │   └── docker-compose.yml
│   ├── kubernetes/
│   │   ├── frontend/
│   │   │   ├── deployment.yaml
│   │   │   └── service.yaml
│   │   ├── backend/
│   │   │   ├── deployment.yaml
│   │   │   └── service.yaml
│   │   ├── database/
│   │   │   ├── deployment.yaml
│   │   │   ├── service.yaml
│   │   │   └── persistent-volume.yaml
│   │   ├── redis/
│   │   │   ├── deployment.yaml
│   │   │   └── service.yaml
│   │   ├── ingress.yaml
│   │   └── config-map.yaml
│   ├── terraform/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   ├── outputs.tf
│   │   ├── modules/
│   │   │   ├── vpc/
│   │   │   ├── eks/
│   │   │   ├── rds/
│   │   │   └── s3/
│   │   └── environments/
│   │       ├── dev/
│   │       ├── staging/
│   │       └── prod/
│   └── scripts/
│       ├── setup.sh
│       ├── deploy.sh
│       ├── backup.sh
│       └── migrate.sh
├── documentation/
│   ├── api/
│   │   ├── admin.md
│   │   ├── tenant.md
│   │   ├── store.md
│   │   ├── user.md
│   │   └── affiliate.md
│   ├── development/
│   │   ├── getting-started.md
│   │   ├── architecture.md
│   │   ├── multi-tenant.md
│   │   └── authentication.md
│   ├── deployment/
│   │   ├── docker.md
│   │   ├── kubernetes.md
│   │   └── scaling.md
│   └── user-guides/
│       ├── admin-guide.md
│       ├── tenant-guide.md
│       ├── store-guide.md
│       └── user-guide.md
├── .github/
│   └── workflows/
│       ├── frontend-ci.yml
│       ├── backend-ci.yml
│       ├── frontend-deploy.yml
│       └── backend-deploy.yml
├── .gitignore
├── .env.example
├── package.json
└── README.md
```

Struktur file ini mencakup:

1. **Frontend (Next.js)**:
   - Organized by role/section (admin, tenant, store, user)
   - Shared components dan UI library
   - Hooks untuk data dan state management
   - Context providers untuk state global
   - API utilities

2. **Backend (NestJS)**:
   - Module-based architecture
   - Entity dan DTO definitions
   - Controllers untuk API endpoints
   - Services untuk business logic
   - Middleware, guards, dan interceptors untuk cross-cutting concerns
   - Multi-tenant implementation

3. **Infrastructure**:
   - Docker setup untuk local development
   - Kubernetes configurations untuk production deployment
   - Terraform scripts untuk cloud infrastructure provisioning
   - Deployment and maintenance scripts

4. **Documentation**:
   - API references
   - Development guides
   - Deployment instructions
   - User guides untuk berbagai roles

5. **CI/CD**:
   - GitHub Actions workflows untuk continuous integration dan deployment

Struktur ini mendukung aplikasi Sellzio SaaS yang komprehensif dengan pemisahan concerns yang jelas, mendukung multi-tenancy, dan didesain untuk scaling dari versi gratis hingga enterprise.

========================================================
# Dashboard Admin Sellzio SaaS: Menu & Submenu Detail

Berikut adalah penjelasan komprehensif tentang setiap menu dan submenu di Dashboard Admin Sellzio SaaS, termasuk apa yang ditampilkan ketika menu atau submenu tersebut diklik.

## 1. Dashboard

**Saat diklik**: Menampilkan halaman beranda dengan ringkasan platform secara keseluruhan.

**Tampilan**:
- KPI cards dengan metrik utama: Total tenants, total stores, total users, total GMV, platform revenue
- Grafik revenue platform dari waktu ke waktu dengan filter periode
- Bar chart tenant performance (top 10 berdasarkan revenue)
- Activity feed dengan aktivitas terbaru di platform
- Alert dashboard untuk item yang memerlukan perhatian
- Quick action buttons untuk tugas umum

## 2. Tenants

**Saat diklik**: Menampilkan halaman daftar semua tenant dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel tenant dengan kolom: Nama, Domain, Plan, Status, Store Count, User Count, Revenue, Created Date
- Filter panel untuk memfilter berdasarkan status, subscription plan, dan tanggal
- Search box untuk mencari tenant berdasarkan nama atau domain
- Action buttons untuk setiap tenant: View, Edit, Login as, Suspend

**Submenu**:

### 2.1. All Tenants
- **Saat diklik**: Sama dengan menu Tenants utama.

### 2.2. Tenant Applications
- **Saat diklik**: Menampilkan daftar aplikasi tenant yang belum diproses/pending.
- **Tampilan**:
  - Tabel aplikasi dengan kolom: Nama, Jenis Bisnis, Tanggal Aplikasi, Status
  - Preview form aplikasi
  - Approval process workflow dengan tombol Approve/Reject
  - Form untuk feedback ke aplikant

### 2.3. Subscription Plans
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola paket subscription platform.
- **Tampilan**:
  - Tabel paket subscription dengan kolom: Nama, Harga, Fitur, Tenant Count
  - Feature matrix comparison antar paket
  - Form untuk membuat/mengedit paket subscription
  - Statistik pendapatan per paket

### 2.4. Domain Management
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola domain dan subdomain tenant.
- **Tampilan**:
  - Tabel domain dengan kolom: Domain, Tenant, Status, SSL, Tanggal Verifikasi
  - Domain verification tracker
  - SSL certificate status dan expiration alerts
  - Custom domain setup guide

## 3. Stores

**Saat diklik**: Menampilkan daftar semua store di seluruh tenant dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel store dengan kolom: Nama, Tenant, Owner, Product Count, Order Count, Revenue, Status
- Filter panel untuk memfilter berdasarkan status, tenant, kategori
- Search box untuk mencari store berdasarkan nama atau owner
- Action buttons untuk setiap store: View, Edit, Suspend

**Submenu**:

### 3.1. All Stores
- **Saat diklik**: Sama dengan menu Stores utama.

### 3.2. Verification Queue
- **Saat diklik**: Menampilkan daftar store yang memerlukan verifikasi.
- **Tampilan**:
  - Tabel store dengan kolom: Nama, Tenant, Owner, Tanggal Aplikasi, Status
  - Preview detail store
  - Verification checklist dengan requirements
  - Approval workflow dengan tombol Approve/Reject

### 3.3. Store Categories
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola kategori store.
- **Tampilan**:
  - Hierarchical tree view untuk kategori dan subkategori
  - Form untuk membuat/mengedit kategori
  - Statistik store per kategori
  - Category assignment tool

### 3.4. Store Templates
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola template store yang tersedia.
- **Tampilan**:
  - Gallery template store dengan preview
  - Settings untuk setiap template (layouts, komponen, opsi)
  - Assignment template ke tenant plans
  - Template editor dengan drag-and-drop functionality

## 4. Products

**Saat diklik**: Menampilkan daftar semua produk di seluruh platform dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel/grid produk dengan kolom: Nama, Store, Category, Price, Stock, Sales, Status
- Filter panel untuk memfilter berdasarkan kategori, price range, store, status
- Search box untuk mencari produk berdasarkan nama atau deskripsi
- Action buttons untuk setiap produk: View, Edit, Remove

**Submenu**:

### 4.1. Product Catalog
- **Saat diklik**: Sama dengan menu Products utama.

### 4.2. Categories & Attributes
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola kategori produk dan attribute.
- **Tampilan**:
  - Hierarchical tree view untuk kategori produk
  - Attribute sets per kategori
  - Form untuk membuat/mengedit kategori dan attributes
  - Attribute templates untuk different product types

### 4.3. Brand Management
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola brand produk.
- **Tampilan**:
  - Tabel brand dengan kolom: Nama, Logo, Product Count, Store Count
  - Form untuk menambah/mengedit brand
  - Brand verification status
  - Brand assignment tools

### 4.4. Product Moderation
- **Saat diklik**: Menampilkan produk yang memerlukan review moderasi.
- **Tampilan**:
  - Tabel produk dengan kolom: Nama, Store, Reason for Moderation, Date Flagged
  - Content review interface
  - Policy violation checker
  - Moderation action buttons: Approve, Reject, Request Changes

## 5. Users

**Saat diklik**: Menampilkan daftar semua user di platform dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel user dengan kolom: Nama, Email, Role, Tenant, Registration Date, Last Login, Status
- Filter panel untuk memfilter berdasarkan role, tenant, status
- Search box untuk mencari user berdasarkan nama atau email
- Action buttons untuk setiap user: View, Edit, Suspend, Login as

**Submenu**:

### 5.1. All Users
- **Saat diklik**: Sama dengan menu Users utama.

### 5.2. Admin Users
- **Saat diklik**: Menampilkan dan mengelola user dengan role admin platform.
- **Tampilan**:
  - Tabel admin dengan kolom: Nama, Email, Role Level, Created Date, Last Login
  - Permission management interface
  - Form untuk menambah/mengedit admin
  - Activity logs untuk admin actions

### 5.3. Tenant Admins
- **Saat diklik**: Menampilkan user yang menjadi admin di tingkat tenant.
- **Tampilan**:
  - Tabel tenant admin dengan kolom: Nama, Email, Tenant, Role, Last Login
  - Permission overview for tenant scope
  - Action buttons: Contact, Suspend, Reset Password

### 5.4. Store Owners
- **Saat diklik**: Menampilkan semua store owner di platform.
- **Tampilan**:
  - Tabel store owner dengan kolom: Nama, Email, Store, Tenant, Registration Date, Status
  - Store performance snapshot
  - Verification status
  - Communication history

### 5.5. End Users
- **Saat diklik**: Menampilkan user reguler (pembeli) di platform.
- **Tampilan**:
  - Tabel user dengan kolom: Nama, Email, Tenant, Orders, Total Spent, Registration Date
  - User activity summary
  - Order history overview
  - Behavioral analytics

### 5.6. User Roles
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola role dan permission.
- **Tampilan**:
  - Matrix role dengan permission
  - Form untuk membuat/mengedit role
  - Permission assignment tool
  - Role hierarchy visualizer

## 6. Financial

**Saat diklik**: Menampilkan ringkasan keuangan platform.

**Tampilan**:
- Financial dashboard dengan KPI: Gross revenue, net revenue, transaction fees, subscription fees
- Revenue breakdown chart by source
- Monthly/yearly comparison charts
- Forecast projections

**Submenu**:

### 6.1. Revenue Overview
- **Saat diklik**: Sama dengan menu Financial utama.

### 6.2. Commission Reports
- **Saat diklik**: Menampilkan laporan detail tentang komisi dari semua transaksi.
- **Tampilan**:
  - Tabel commission dengan kolom: Transaction ID, Store, Tenant, Amount, Commission, Date
  - Commission breakdown by tenant
  - Commission rate visualization
  - Commission trend analysis

### 6.3. Payouts
- **Saat diklik**: Menampilkan dan mengelola payouts ke stores dan affiliates.
- **Tampilan**:
  - Tabel payouts dengan kolom: Recipient, Type, Amount, Status, Scheduled Date
  - Payout calendar
  - Payout approval workflow
  - Payment method management
  - Payout batch processing tools

### 6.4. Invoices & Billing
- **Saat diklik**: Menampilkan dan mengelola invoice dan billing.
- **Tampilan**:
  - Tabel invoice dengan kolom: Invoice Number, Tenant, Amount, Status, Due Date
  - Invoice generation tools
  - Payment tracking
  - Subscription renewal management
  - Dunning management

### 6.5. Tax Management
- **Saat diklik**: Menampilkan dan mengelola pengaturan pajak.
- **Tampilan**:
  - Tax rate tables by region
  - Tax category assignments
  - Tax report generator
  - Tax document management
  - Compliance status dashboard

## 7. Content

**Saat diklik**: Menampilkan dashboard pengelolaan konten platform.

**Tampilan**:
- Content overview dengan stats: Pages, blogs, email templates
- Recent content changes
- Scheduled content releases
- Content performance metrics

**Submenu**:

### 7.1. Theme Manager
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola tema untuk tenant.
- **Tampilan**:
  - Theme gallery dengan preview
  - Theme editor dengan live preview
  - Theme settings manager
  - Custom CSS/JS editor
  - Theme assignment to tenant plans

### 7.2. Landing Pages
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola landing page platform.
- **Tampilan**:
  - List landing pages dengan preview thumbnails
  - Visual page builder
  - SEO settings
  - A/B testing tools
  - Analytics per page

### 7.3. Blog Articles
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola blog platform.
- **Tampilan**:
  - Tabel article dengan kolom: Title, Author, Category, Published Date, Views
  - Rich text editor
  - Media gallery
  - SEO optimizer
  - Publishing schedule

### 7.4. Email Templates
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola template email.
- **Tampilan**:
  - Template library dengan preview
  - Email template editor
  - Variable/placeholder manager
  - Template testing tools
  - Template assignment to events

### 7.5. Help Center
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola konten help center.
- **Tampilan**:
  - Knowledge base article manager
  - FAQ editor
  - Category organization
  - User guide builder
  - Search analytics

## 8. Marketing

**Saat diklik**: Menampilkan dashboard marketing platform.

**Tampilan**:
- Marketing overview dengan metrics: Campaign performance, conversion rates
- Channel performance comparison
- Upcoming campaigns
- Promotional calendar

**Submenu**:

### 8.1. Platform Promotions
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola promosi platform-wide.
- **Tampilan**:
  - Tabel promotion dengan kolom: Name, Type, Start Date, End Date, Status, Performance
  - Promotion builder
  - Discount rule configuration
  - Target audience selector
  - Performance tracking

### 8.2. Featured Content
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola konten yang di-feature.
- **Tampilan**:
  - Featured carousel manager
  - Featured store selection
  - Featured product selection
  - Scheduling and rotation settings
  - Performance analytics

### 8.3. SEO Management
- **Saat diklik**: Menampilkan tools untuk pengelolaan SEO platform.
- **Tampilan**:
  - SEO health dashboard
  - Keyword performance tracker
  - Meta tag manager
  - Sitemap configuration
  - Structured data manager

### 8.4. Affiliate Program
- **Saat diklik**: Menampilkan pengaturan program affiliate platform-level.
- **Tampilan**:
  - Program settings and rules
  - Commission rate defaults
  - Affiliate tier configuration
  - Global marketing materials
  - Performance analytics

## 9. Systems

**Saat diklik**: Menampilkan dashboard sistem dan infrastruktur.

**Tampilan**:
- System health overview
- Resource utilization charts (CPU, memory, disk)
- Uptime statistics
- Error rate monitoring
- Service status indicators

**Submenu**:

### 9.1. Platform Settings
- **Saat diklik**: Menampilkan pengaturan umum platform.
- **Tampilan**:
  - General configuration (name, logo, contact info)
  - Regional settings (timezone, currency, language)
  - Security settings
  - Feature toggles
  - System limits configuration

### 9.2. Integration Hub
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola integrasi pihak ketiga.
- **Tampilan**:
  - Integration directory with status
  - API key management
  - Webhook configuration
  - OAuth connection manager
  - Integration logs

### 9.3. API Management
- **Saat diklik**: Menampilkan dan memungkinkan admin mengelola API platform.
- **Tampilan**:
  - API key management
  - Rate limiting configuration
  - API documentation
  - API usage analytics
  - Access control settings

### 9.4. Logs & Monitoring
- **Saat diklik**: Menampilkan sistem logging dan monitoring.
- **Tampilan**:
  - Application logs with filtering
  - Error logs with severity indicators
  - Audit logs for security events
  - Performance metrics
  - Real-time monitoring dashboard

### 9.5. Backup & Restore
- **Saat diklik**: Menampilkan tools untuk backup dan restore data.
- **Tampilan**:
  - Backup schedule configuration
  - Manual backup tools
  - Backup history with status
  - Restore functionality
  - Disaster recovery planning

### 9.6. Security Center
- **Saat diklik**: Menampilkan tools dan settings untuk keamanan platform.
- **Tampilan**:
  - Security overview dashboard
  - Suspicious activity monitoring
  - Password policy settings
  - Two-factor authentication settings
  - IP blocking tools
  - Compliance status

## 10. Reports

**Saat diklik**: Menampilkan dashboard reporting platform.

**Tampilan**:
- Report library dengan recently generated reports
- Report templates
- Scheduled reports
- Dashboard creation tools

**Submenu**:

### 10.1. Sales Reports
- **Saat diklik**: Menampilkan dan generate laporan penjualan.
- **Tampilan**:
  - Sales report builder
  - Preset report templates
  - Visualization options
  - Export tools (PDF, Excel, CSV)
  - Scheduling options

### 10.2. Tenant Reports
- **Saat diklik**: Menampilkan dan generate laporan performa tenant.
- **Tampilan**:
  - Tenant performance comparison
  - Growth metrics
  - Tenant health indicators
  - Tenant activity reports
  - Subscription analytics

### 10.3. User Reports
- **Saat diklik**: Menampilkan dan generate laporan user.
- **Tampilan**:
  - User growth analytics
  - Demographic reports
  - User engagement metrics
  - Retention analysis
  - Churn prediction

### 10.4. Custom Reports
- **Saat diklik**: Tools untuk membuat laporan kustom.
- **Tampilan**:
  - Report builder dengan drag-and-drop interface
  - Data source selector
  - Visualization picker
  - Filtering and aggregation tools
  - Saved report templates

## 11. Account

**Saat diklik**: Menampilkan pengaturan akun admin yang sedang login.

**Tampilan**:
- Account summary
- Profile information
- Notification settings
- Activity history

**Submenu**:

### 11.1. My Profile
- **Saat diklik**: Menampilkan dan memungkinkan edit profil admin.
- **Tampilan**:
  - Profile information form
  - Avatar/photo upload
  - Contact preferences
  - Interface preferences (theme, language)

### 11.2. Notifications
- **Saat diklik**: Menampilkan dan mengatur notifikasi admin.
- **Tampilan**:
  - Notification list with status
  - Notification preference settings
  - Notification channels (email, in-app, mobile)
  - Alert importance configuration

### 11.3. Security
- **Saat diklik**: Menampilkan pengaturan keamanan akun.
- **Tampilan**:
  - Password change form
  - Two-factor authentication setup
  - Login history
  - Session management
  - Security questions

### 11.4. Team Management
- **Saat diklik**: Menampilkan dan mengelola team admin.
- **Tampilan**:
  - Team member list
  - Role assignment
  - Permission configuration
  - Invitation management
  - Activity tracking by admin

## 12. Help & Support

**Saat diklik**: Menampilkan resource bantuan dan dukungan.

**Tampilan**:
- Support ticket manager
- Knowledge base
- Video tutorials
- Documentation links
- Contact support options

**Submenu**:

### 12.1. Support Tickets
- **Saat diklik**: Menampilkan dan mengelola tiket support.
- **Tampilan**:
  - Ticket list dengan status dan priority
  - Ticket detail view
  - Response interface
  - Escalation tools
  - Resolution tracking

### 12.2. Documentation
- **Saat diklik**: Menampilkan dokumentasi platform.
- **Tampilan**:
  - Documentation browser
  - Search functionality
  - Category navigation
  - Version history
  - Feedback mechanism

### 12.3. Release Notes
- **Saat diklik**: Menampilkan history update platform.
- **Tampilan**:
  - Chronological list of releases
  - Feature announcements
  - Bug fix reports
  - Upcoming releases preview
  - Changelog details

## Fitur Umum di Setiap Halaman

1. **Navigasi Global**:
   - Sidebar dengan menu utama dan submenu
   - Breadcrumbs untuk navigasi 
   - Quick action buttons
   - Back button ke halaman sebelumnya

2. **Search and Filter**:
   - Global search di header
   - Advanced filter panel di halaman list
   - Saved filters
   - Sorting options

3. **Data View Controls**:
   - Toggle between list/grid/card views
   - Pagination controls
   - Items per page selector
   - Column visibility toggles

4. **Action Controls**:
   - Bulk action menus
   - Export options
   - Refresh button
   - Context menu untuk item-specific actions

5. **Notifications**:
   - System alerts
   - Action confirmations
   - Error messages
   - Success indicators

Setiap halaman dirancang dengan fokus pada usability dan efisiensi,
 memungkinkan admin platform untuk mengelola seluruh aspek Sellzio SaaS dengan mudah dan efektif dari satu dashboard terpusat.
 
 ==========================
 # Dashboard Tenant (Tenant) Sellzio SaaS: Menu & Submenu Detail

Berikut adalah penjelasan komprehensif mengenai setiap menu dan submenu di Dashboard Tenant (Tenant) Sellzio SaaS, termasuk apa yang ditampilkan ketika menu atau submenu tersebut diklik.

## 1. Dashboard

**Saat diklik**: Menampilkan halaman beranda dengan ringkasan marketplace tenant.

**Tampilan**:
- KPI cards dengan metrik utama: Total revenue, order count, user count, store count, GMV, conversion rate
- Grafik revenue dari waktu ke waktu dengan filter periode
- Store performance chart menampilkan top 5-10 stores berdasarkan revenue
- Activity feed dengan aktivitas terbaru di marketplace
- Alert dashboard untuk item yang memerlukan perhatian
- Quick action buttons untuk tugas umum tenant

## 2. Stores

**Saat diklik**: Menampilkan halaman daftar semua store di bawah tenant dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel/grid store dengan kolom: Nama Store, Owner, Product Count, Order Count, Revenue, Status, Rating
- Filter panel untuk memfilter berdasarkan status, kategori, dan tanggal pembuatan
- Search box untuk mencari store berdasarkan nama atau owner
- Action buttons untuk setiap store: View, Edit, Suspend, Message Owner

**Submenu**:

### 2.1. All Stores
- **Saat diklik**: Sama dengan menu Stores utama.

### 2.2. Store Applications
- **Saat diklik**: Menampilkan daftar aplikasi store yang belum diproses/pending.
- **Tampilan**:
  - Tabel aplikasi dengan kolom: Nama Store, Owner, Tanggal Aplikasi, Kategori, Status
  - Preview detail aplikasi (produk yang akan dijual, deskripsi, dll.)
  - Approval workflow dengan checklist persyaratan dan tombol Approve/Reject
  - Form untuk memberikan feedback ke aplikant

### 2.3. Store Categories
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola kategori store.
- **Tampilan**:
  - Tabel atau hierarki kategori dengan kolom: Nama, Deskripsi, Store Count, Status
  - Form untuk membuat/mengedit kategori
  - Opsi untuk mengatur featured categories
  - Drag-and-drop untuk mengurutkan kategori

### 2.4. Store Settings
- **Saat diklik**: Menampilkan pengaturan global untuk stores di marketplace.
- **Tampilan**:
  - Form pengaturan dengan berbagai section:
    - Commission rates (global dan per kategori)
    - Store approval requirements
    - Store dashboard customization
    - Policy templates (return, shipping, etc.)
    - Rating & review settings

## 3. Products

**Saat diklik**: Menampilkan daftar semua produk di marketplace dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel/grid produk dengan kolom: Gambar, Nama, Store, Kategori, Harga, Stok, Sales, Status
- Filter panel untuk memfilter berdasarkan kategori, store, price range, status
- Search box untuk mencari produk berdasarkan nama atau deskripsi
- Action buttons untuk setiap produk: View, Edit, Feature, Remove

**Submenu**:

### 3.1. All Products
- **Saat diklik**: Sama dengan menu Products utama.

### 3.2. Categories
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola kategori produk.
- **Tampilan**:
  - Hierarchical tree view untuk kategori dan subkategori
  - Form untuk membuat/mengedit kategori
  - Attribute management untuk setiap kategori
  - Category arrangement tools (ordering, nesting)
  - Category analytics (product count, views, conversions)

### 3.3. Brands
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola brand produk.
- **Tampilan**:
  - Tabel brand dengan kolom: Logo, Nama, Deskripsi, Product Count, Featured Status
  - Form untuk menambah/mengedit brand
  - Brand verification settings
  - Brand page customization options

### 3.4. Import/Export
- **Saat diklik**: Menampilkan tool untuk import dan export produk massal.
- **Tampilan**:
  - Import panel dengan file upload area
  - Template downloaders untuk berbagai format (CSV, Excel)
  - Import history log
  - Export tool dengan filter options
  - Validation settings dan error handling

### 3.5. Product Moderation
- **Saat diklik**: Menampilkan produk yang memerlukan review moderasi.
- **Tampilan**:
  - Queue produk dengan status moderasi
  - Content review interface
  - Policy compliance checklist
  - Moderation action buttons (Approve, Reject, Request Changes)
  - Moderation history log

## 4. Orders

**Saat diklik**: Menampilkan daftar semua order di marketplace dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel order dengan kolom: Order ID, Customer, Store, Total, Date, Status, Payment Status
- Filter panel untuk memfilter berdasarkan status, store, tanggal, payment method
- Search box untuk mencari order berdasarkan ID atau customer name
- Action buttons untuk setiap order: View, Update Status, Message Customer/Store

**Submenu**:

### 4.1. All Orders
- **Saat diklik**: Sama dengan menu Orders utama.

### 4.2. Order Fulfillment
- **Saat diklik**: Menampilkan panel monitoring untuk order fulfillment.
- **Tampilan**:
  - Pipeline view dengan kolom status (New, Processing, Shipped, Delivered, Completed)
  - Order cards yang dapat di-drag antar kolom status
  - Order count dan total value per status
  - Batch action tools untuk multiple orders
  - Time metrics (average processing time, shipping time, etc.)

### 4.3. Returns & Refunds
- **Saat diklik**: Menampilkan daftar return dan refund requests.
- **Tampilan**:
  - Tabel returns dengan kolom: Order ID, Customer, Store, Return Reason, Date, Status
  - Return details panel dengan item dan alasan return
  - Refund calculator
  - Approval workflow dengan review steps
  - Return policy reference panel

### 4.4. Shipping Settings
- **Saat diklik**: Menampilkan pengaturan pengiriman marketplace.
- **Tampilan**:
  - Shipping method management
  - Shipping rate tables (by weight, dimension, destination)
  - Shipping carrier integration settings
  - Shipping label generation options
  - Shipping zones configuration

## 5. Customers

**Saat diklik**: Menampilkan daftar semua customer di marketplace dengan opsi filter dan pencarian.

**Tampilan**:
- Tabel customer dengan kolom: Nama, Email, Registration Date, Orders, Total Spent, Last Active
- Filter panel untuk memfilter berdasarkan activity, purchase history, registration date
- Search box untuk mencari customer berdasarkan nama atau email
- Action buttons untuk setiap customer: View Profile, Message, Manage Access

**Submenu**:

### 5.1. All Customers
- **Saat diklik**: Sama dengan menu Customers utama.

### 5.2. Customer Groups
- **Saat diklik**: Menampilkan dan memungkinkan tenant membuat segment customer.
- **Tampilan**:
  - Tabel customer group dengan kolom: Nama, Deskripsi, Customer Count, Created Date
  - Group builder dengan rule-based segmentation
  - Group analytics (spending patterns, preferences)
  - Group-specific marketing tools
  - Customer import/assignment to groups

### 5.3. Customer Reviews
- **Saat diklik**: Menampilkan daftar semua review yang diberikan customer.
- **Tampilan**:
  - Tabel review dengan kolom: Customer, Product, Store, Rating, Date, Status
  - Review moderation interface
  - Response management tool
  - Review analytics (average rating, trend over time)
  - Review policy settings

## 6. Affiliates

**Saat diklik**: Menampilkan dashboard program affiliate marketplace.

**Tampilan**:
- Affiliate program performance overview
- KPI cards: Total affiliates, affiliate-generated sales, commission paid, conversion rate
- Top affiliates leaderboard
- Affiliate growth chart
- Recent affiliate signups

**Submenu**:

### 6.1. All Affiliates
- **Saat diklik**: Menampilkan daftar semua affiliate di marketplace.
- **Tampilan**:
  - Tabel affiliate dengan kolom: Nama, Email, Join Date, Generated Sales, Commission Earned, Status
  - Filter dan search tools
  - Performance metrics untuk setiap affiliate
  - Action buttons: View Profile, Message, Adjust Commission, Suspend

### 6.2. Affiliate Applications
- **Saat diklik**: Menampilkan daftar aplikasi affiliate yang pending.
- **Tampilan**:
  - Application review interface
  - Applicant information (channel, audience size, experience)
  - Application approval workflow
  - Policy agreement verification
  - Custom commission offer tool

### 6.3. Commission Settings
- **Saat diklik**: Menampilkan pengaturan komisı untuk affiliate program.
- **Tampilan**:
  - Global commission rate settings
  - Category-specific rate settings
  - Store-specific rate overrides
  - Volume-based tier configuration
  - Special promotion commission rates
  - Commission cap settings

### 6.4. Marketing Materials
- **Saat diklik**: Menampilkan dan mengelola marketing materials untuk affiliates.
- **Tampilan**:
  - Material library dengan preview
  - Upload new materials interface
  - Material usage analytics
  - Material categorization
  - Custom material request management

## 7. Marketing

**Saat diklik**: Menampilkan dashboard marketing marketplace.

**Tampilan**:
- Marketing performance overview
- Campaign analytics dashboard
- Promotion calendar
- Marketing channel performance comparison
- Recent campaign results

**Submenu**:

### 7.1. Campaigns
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola kampanye marketing.
- **Tampilan**:
  - Tabel campaign dengan kolom: Nama, Type, Start Date, End Date, Budget, Status, Performance
  - Campaign builder interface
  - Performance analytics per campaign
  - Campaign scheduling tools
  - A/B testing configuration

### 7.2. Promotions & Coupons
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola promosi.
- **Tampilan**:
  - Tabel promotion dengan kolom: Nama, Type, Discount, Start/End Date, Usage Count, Status
  - Promotion builder interface
    - Discount type (percentage, fixed, free shipping, BOGO)
    - Applicable products/categories
    - Usage limits
    - Customer eligibility
  - Coupon code generator
  - Promotion performance analytics
  - Bulk promotion tools

### 7.3. Flash Sales
- **Saat diklik**: Menampilkan dan memungkinkan tenant membuat flash sales.
- **Tampilan**:
  - Flash sale planner dengan timeline view
  - Product selection interface
  - Time-limited discount settings
  - Inventory allocation tools
  - Flash sale performance analytics
  - Countdown timer configuration
  - Promotion tools for flash sales

### 7.4. Email Campaigns
- **Saat diklik**: Menampilkan tools untuk email marketing.
- **Tampilan**:
  - Email campaign manager
  - Email template builder dengan drag-and-drop interface
  - Audience selection tools
  - Email scheduling
  - A/B testing for subject lines and content
  - Email analytics (open rate, click rate, conversion)
  - Automated email flow builder

## 8. Content

**Saat diklik**: Menampilkan content management system marketplace.

**Tampilan**:
- Content overview dashboard
- Content calendar
- Content performance analytics
- Recent published content
- Pending content items

**Submenu**:

### 8.1. Pages
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola halaman statis.
- **Tampilan**:
  - Tabel pages dengan kolom: Title, URL, Last Updated, Status, Views
  - Page builder dengan visual editor
  - Page template selection
  - SEO settings per page
  - Page version history
  - Page preview tools

### 8.2. Blog
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola blog.
- **Tampilan**:
  - Tabel article dengan kolom: Title, Author, Category, Published Date, Status, Views
  - Rich text editor untuk artikel
  - Media gallery integration
  - Article categorization
  - SEO tools for article
  - Publishing schedule options
  - Featured article settings

### 8.3. Media
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola media library.
- **Tampilan**:
  - Grid view media gallery
  - Folder organization for media
  - Upload interface with drag-and-drop
  - Image editing tools
  - Media usage tracking
  - Bulk media operations
  - Media metadata editor

### 8.4. Navigation
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi navigasi situs.
- **Tampilan**:
  - Menu builder dengan drag-and-drop interface
  - Menu item editor (link, label, icon)
  - Menu structure manager (nested menus)
  - Menu position settings
  - Mobile navigation configurator
  - Menu preview tools
  - Menu analytics (click rates on menu items)

### 8.5. SEO
- **Saat diklik**: Menampilkan SEO tools untuk marketplace.
- **Tampilan**:
  - SEO dashboard dengan performance metrics
  - Keyword ranking tracker
  - On-page SEO analyzer
  - Meta tag manager
  - URL structure settings
  - Sitemap configurator
  - Structured data tools

## 9. Live

**Saat diklik**: Menampilkan dashboard live shopping dan video commerce.

**Tampilan**:
- Live shopping performance overview
- Upcoming scheduled streams
- Past stream analytics
- Top performing products in live streams
- Quick start streaming button

**Submenu**:

### 9.1. Schedule
- **Saat diklik**: Menampilkan dan memungkinkan tenant menjadwalkan live streams.
- **Tampilan**:
  - Calendar view dengan scheduled streams
  - Stream scheduler form
    - Title & description
    - Date and time selector
    - Duration setting
    - Featured products selection
    - Host assignment
    - Promotional settings
  - Stream template selection
  - Notification settings for followers
  - Stream preparation checklist

### 9.2. Studio
- **Saat diklik**: Menampilkan interface untuk melakukan live stream.
- **Tampilan**:
  - Broadcasting interface dengan preview window
  - Stream control panel (start, pause, end)
  - Camera and mic settings
  - Background and filter options
  - Product showcase panel
  - Viewer comment display
  - Live metrics (viewers, likes, shares)
  - Call-to-action button creator

### 9.3. Recordings
- **Saat diklik**: Menampilkan daftar past live streams.
- **Tampilan**:
  - Tabel recordings dengan kolom: Title, Date, Duration, Views, Sales Generated, Status
  - Recording player
  - Recording editor untuk trim dan edit
  - Performance analytics per recording
  - Highlight creator tool
  - Distribution settings (share, embed, feature)
  - Product tagging for recordings

### 9.4. Analytics
- **Saat diklik**: Menampilkan analytics khusus untuk live dan video commerce.
- **Tampilan**:
  - Performance dashboard untuk live program
  - Viewership metrics (peak viewers, average watch time)
  - Engagement metrics (comments, likes, shares)
  - Conversion metrics (CTR, add-to-cart, purchase)
  - Product performance in live sessions
  - Host performance analytics
  - Best time to stream analysis

## 10. Analytics

**Saat diklik**: Menampilkan dashboard analytics marketplace.

**Tampilan**:
- Analytics overview dengan KPI utama
- Performance trend charts
- User behavior visualizations
- Sales and revenue analytics
- Traffic source breakdown

**Submenu**:

### 10.1. Overview
- **Saat diklik**: Menampilkan dashboard analytics komprehensif.
- **Tampilan**:
  - High-level KPI dashboard
  - Period comparison tools (MoM, YoY)
  - Multiple chart types showing different metrics
  - Analytics segments (sales, users, products, traffic)
  - Custom dashboard builder
  - Saved views and layouts

### 10.2. Sales Analytics
- **Saat diklik**: Menampilkan analisis detail tentang penjualan.
- **Tampilan**:
  - Sales breakdown by various dimensions
    - By store
    - By category
    - By product
    - By customer segment
    - By time period
  - Sales funnel visualization
  - Conversion rate analysis
  - Average order value trends
  - Sales forecast tools
  - Profitability analysis

### 10.3. Customer Analytics
- **Saat diklik**: Menampilkan analisis detail tentang customer.
- **Tampilan**:
  - Customer acquisition analysis
  - Customer retention metrics
  - Customer lifetime value calculator
  - Customer journey mapping
  - Engagement metrics
  - Segmentation analysis
  - Churn prediction
  - Customer behavior patterns

### 10.4. Reports
- **Saat diklik**: Menampilkan dan memungkinkan tenant membuat custom reports.
- **Tampilan**:
  - Report builder interface
  - Saved report templates
  - Scheduled reports manager
  - Export options (PDF, Excel, CSV)
  - Visualization customization
  - Data source selection
  - Report sharing tools

### 10.5. Export
- **Saat diklik**: Menampilkan tools untuk data export.
- **Tampilan**:
  - Data export interface
  - Dataset selection
  - Export format options
  - Scheduling options
  - File management for exports
  - API access for data

## 11. Financial

**Saat diklik**: Menampilkan dashboard keuangan marketplace.

**Tampilan**:
- Financial overview dengan KPI utama
- Revenue breakdown chart
- Balance and payout information
- Transaction volume metrics
- Fee structure summary

**Submenu**:

### 11.1. Overview
- **Saat diklik**: Menampilkan dashboard keuangan komprehensif.
- **Tampilan**:
  - Financial KPI dashboard
  - Revenue streams visualization
  - Fee collection analytics
  - Balance summary
  - Upcoming payout information
  - Transaction volume trends
  - Profit margin analysis

### 11.2. Payouts
- **Saat diklik**: Menampilkan dan mengelola payouts untuk stores dan affiliates.
- **Tampilan**:
  - Tabel payouts dengan kolom: Recipient, Type, Amount, Status, Scheduled Date
  - Payout calendar
  - Payout approval workflow
  - Payment method management
  - Payout history
  - Payout calculation details

### 11.3. Transactions
- **Saat diklik**: Menampilkan log detail semua transaksi keuangan.
- **Tampilan**:
  - Tabel transaction dengan kolom: Transaction ID, Type, Amount, Source, Destination, Date, Status
  - Transaction detail viewer
  - Transaction search and filter tools
  - Transaction reconciliation tools
  - Export options for accounting

### 11.4. Statements
- **Saat diklik**: Menampilkan dan generate laporan keuangan.
- **Tampilan**:
  - Statement generator interface
  - Statement history
  - Statement period selector
  - Statement detail viewer
  - Export and download options
  - Tax document generator
  - Statement distribution tools

## 12. Settings

**Saat diklik**: Menampilkan pengaturan marketplace.

**Tampilan**:
- Settings overview dengan kategori pengaturan
- Quick settings search
- Recent setting changes
- Setting validation status

**Submenu**:

### 12.1. Profile
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengedit profil marketplace.
- **Tampilan**:
  - Business information form
  - Contact details
  - Legal information
  - Business hours
  - Social media links
  - About us editor
  - Geographic settings

### 12.2. Branding
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi branding.
- **Tampilan**:
  - Logo upload area (multiple sizes)
  - Favicon upload
  - Color scheme picker
  - Typography settings
  - Button style configurator
  - Email branding settings
  - Invoice and document branding

### 12.3. Domain
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi domain.
- **Tampilan**:
  - Current domain status
  - Domain change request form
  - DNS settings information
  - SSL certificate status
  - Domain verification tools
  - Subdomain management
  - Domain history

### 12.4. Users
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengelola user staff marketplace.
- **Tampilan**:
  - Tabel staff dengan kolom: Name, Email, Role, Last Login, Status
  - User invitation interface
  - Role assignment tools
  - Permission management
  - Account security settings
  - Activity logs per user
  - Bulk user operations

### 12.5. Integrations
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi integrasi pihak ketiga.
- **Tampilan**:
  - Integration directory dengan status
  - Integration setup wizards
  - API key management
  - Webhook configuration
  - OAuth connection manager
  - Integration logs
  - Integration health monitoring

### 12.6. Notifications
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi notifikasi.
- **Tampilan**:
  - Notification type list dengan toggle switches
  - Email template editor
  - Push notification settings
  - Notification schedule preferences
  - Event-based notification rules
  - Notification preview tools
  - Notification test tools

### 12.7. Security
- **Saat diklik**: Menampilkan dan memungkinkan tenant mengkonfigurasi keamanan.
- **Tampilan**:
  - Security overview dashboard
  - Password policy settings
  - Two-factor authentication settings
  - Session management
  - IP restriction tools
  - Access log viewer
  - Security alert configurations

## Fitur Umum di Setiap Halaman

1. **Navigasi Global**:
   - Sidebar dengan menu utama dan submenu
   - Breadcrumbs untuk navigasi 
   - Quick action buttons
   - Back button ke halaman sebelumnya

2. **Search and Filter**:
   - Global search di header
   - Advanced filter panel di halaman list
   - Saved filters
   - Sorting options

3. **Data View Controls**:
   - Toggle between list/grid/card views
   - Pagination controls
   - Items per page selector
   - Column visibility toggles

4. **Action Controls**:
   - Bulk action menus
   - Export options
   - Refresh button
   - Context menu untuk item-specific actions

5. **Notifications**:
   - System alerts
   - Action confirmations
   - Error messages
   - Success indicators

6. **White Label Elements**:
   - Brand logo dan color scheme khusus tenant
   - Custom domain di URL
   - Branded elements (email, notifications, etc.)
   - Custom terminology sesuai preferensi tenant

Dashboard Tenant Sellzio SaaS dirancang untuk memberikan tenant (Tenant) kontrol penuh atas marketplace mereka, 
dengan fokus pada pengelolaan stores, produk, dan semua aspek operasional marketplace.
 Struktur menu dirancang secara intuitif, mengelompokkan fitur berdasarkan fungsi dan memastikan tenant dapat dengan mudah mengelola seluruh ekosistem e-commerce mereka.
 
 
 ============================================
 # Dashboard Store Sellzio SaaS: Menu & Submenu Detail

Berikut adalah penjelasan komprehensif tentang setiap menu dan submenu di Dashboard Store Sellzio SaaS, beserta detail tampilan yang muncul ketika menu atau submenu tersebut diklik.

## 1. Dashboard

**Saat diklik**: Menampilkan halaman beranda dengan ringkasan performa store.

**Tampilan**:
- KPI cards dengan metrik utama: Revenue hari ini/minggu ini/bulan ini, jumlah order, jumlah pengunjung, conversion rate
- Grafik sales trend dengan opsi timeframe (daily, weekly, monthly, yearly)
- Top selling products dalam format grid atau list
- Recent orders dengan status dan action buttons
- Traffic sources dengan pie chart
- Inventory alerts (low stock, out of stock)
- Social media engagement metrics jika diaktifkan

## 2. Products

**Saat diklik**: Menampilkan halaman daftar produk dengan opsi filter dan pencarian.

**Tampilan**:
- Toggle view antara grid (dengan gambar produk) dan list (tabel detail)
- Filter panel dengan opsi: kategori, harga, status, stok, tanggal
- Search box dengan advanced search
- Bulk action menu untuk operasi batch
- Product statistics summary (total products, active products, out of stock)

**Submenu**:

### 2.1. All Products
- **Saat diklik**: Menampilkan daftar semua produk store.
- **Tampilan**:
  - Tabel produk dengan kolom: Gambar, Nama, SKU, Harga, Kategori, Stok, Status, Terjual, Tanggal Dibuat
  - Quick edit options untuk harga dan stok
  - Actions per produk: View, Edit, Duplicate, Delete
  - Sorting options berdasarkan berbagai parameter

### 2.2. Add New Product
- **Saat diklik**: Menampilkan form pembuatan produk baru.
- **Tampilan**:
  - Multi-tab form dengan sections:
    - **Basic Info**: Nama produk, deskripsi, brand, kategori, tags
    - **Pricing**: Harga reguler, harga diskon, schedule diskon, tax class
    - **Inventory**: SKU, stock management, stock quantity, backorder settings
    - **Images & Media**: Multiple image upload, drag to reorder, featured image
    - **Variants**: Opsi untuk menambah varian (ukuran, warna, dll)
    - **Shipping**: Berat, dimensi, shipping class
    - **Attributes**: Custom attributes yang dapat ditambahkan
    - **SEO**: Meta title, description, keywords, social media preview
    - **Advanced**: Visibility settings, purchase notes, affiliate settings

### 2.3. Categories
- **Saat diklik**: Menampilkan pengaturan kategori produk.
- **Tampilan**:
  - Hirarki kategori dalam tree view
  - Form untuk menambah/edit kategori:
    - Nama kategori
    - Parent kategori
    - Deskripsi
    - Image kategori
    - Display settings
    - SEO settings
  - Drag-and-drop untuk mengatur urutan
  - Statistik produk per kategori

### 2.4. Inventory
- **Saat diklik**: Menampilkan manajemen inventori.
- **Tampilan**:
  - Stock overview dengan alert levels
  - Bulk stock update tools
  - Low stock notifications settings
  - Stock history dengan log perubahan
  - Stock adjustment form
  - Batch import/export inventory

### 2.5. Import/Export
- **Saat diklik**: Menampilkan tools untuk import dan export produk.
- **Tampilan**:
  - Template download untuk CSV/Excel import
  - Mapping tool untuk kolom custom
  - File upload dengan progress bar
  - Import validation preview
  - Error log dan resolusi
  - Export dengan filtering options
  - Scheduled import/export options

## 3. Orders

**Saat diklik**: Menampilkan halaman daftar order dengan filtering dan sorting.

**Tampilan**:
- Tab untuk status order: All, Pending, Processing, Completed, Cancelled
- Filter panel dengan opsi: date range, payment status, customer
- Search box untuk order ID atau customer
- Order statistics summary (total orders, average value, fulfillment rate)

**Submenu**:

### 3.1. All Orders
- **Saat diklik**: Menampilkan daftar semua order.
- **Tampilan**:
  - Tabel order dengan kolom: Order ID, Tanggal, Customer, Items, Total, Status, Payment Status
  - Color-coded status indicators
  - Actions per order: View, Process, Cancel, Refund
  - Bulk action tools

### 3.2. Order Detail
- **Saat diklik dari list order**: Menampilkan detail lengkap order.
- **Tampilan**:
  - Order header dengan ID, tanggal, dan status
  - Customer info dengan order history link
  - Line items dengan gambar produk, kuantitas, harga
  - Payment details dengan metode dan status
  - Shipping info dan tracking
  - Order notes (public dan private)
  - Status workflow dengan action buttons
  - Order timeline dengan history aktivitas
  - Print/download invoice options

### 3.3. Returns & Refunds
- **Saat diklik**: Menampilkan daftar return request dan refund.
- **Tampilan**:
  - Tabel return dengan kolom: Return ID, Order ID, Customer, Items, Reason, Status, Date
  - Return policy settings
  - Refund processing tools
  - Approval workflow
  - Return labels generation

## 4. Customers

**Saat diklik**: Menampilkan daftar customer dengan filtering dan searching.

**Tampilan**:
- Customer list dengan basic info
- Segmentation tools
- Customer statistics overview
- Export options

**Submenu**:

### 4.1. All Customers
- **Saat diklik**: Menampilkan daftar semua customer.
- **Tampilan**:
  - Tabel customer dengan kolom: Nama, Email, Orders, Spent, Location, Registered Date, Last Activity
  - Filter panel untuk segmentation
  - Actions per customer: View, Email, Edit, Blacklist
  - Customer tagging system

### 4.2. Customer Detail
- **Saat diklik dari list customer**: Menampilkan profil lengkap customer.
- **Tampilan**:
  - Customer profile header dengan contact info
  - Purchase history dengan metrics
  - Order list dengan sorting
  - Addresses (shipping dan billing)
  - Notes dan tags
  - Communication history
  - Customer lifetime value calculation
  - Activity timeline

### 4.3. Customer Groups
- **Saat diklik**: Menampilkan manajemen group customer.
- **Tampilan**:
  - List customer groups
  - Group creation form:
    - Nama group
    - Description
    - Membership criteria (manual/automatic)
    - Special pricing
    - Discount rules
  - Customer assignment tools
  - Group analytics

## 5. Marketing

**Saat diklik**: Menampilkan dashboard marketing tools.

**Tampilan**:
- Marketing overview dengan campaign metrics
- Promotional calendar
- Conversion funnel visualization
- Budget dan spending overview

**Submenu**:

### 5.1. Discounts & Coupons
- **Saat diklik**: Menampilkan manajemen diskon dan kupon.
- **Tampilan**:
  - Tabel discount dengan kolom: Code, Type, Amount, Usage, Start Date, End Date, Status
  - Coupon generator dengan settings:
    - Code type (auto/manual)
    - Discount type (percentage/fixed)
    - Amount
    - Usage limits
    - Expiry settings
    - Product/category restrictions
    - Minimum spend
    - Customer restrictions
  - Performance metrics per coupon
  - Bulk generation tools

### 5.2. Promotions
- **Saat diklik**: Menampilkan manajemen promosi.
- **Tampilan**:
  - Promotion types:
    - Flash sales
    - Bundle deals
    - BOGO (buy one get one)
    - Free shipping threshold
  - Promo scheduler dengan calendar view
  - Targeting options
  - Performance tracking
  - A/B testing tools

### 5.3. Affiliate Settings
- **Saat diklik**: Menampilkan pengaturan program affiliate untuk store.
- **Tampilan**:
  - Affiliate program toggle (enable/disable)
  - Commission settings:
    - Global rate
    - Product-specific rates
    - Category rates
    - Volume-based tiers
  - Affiliate approval workflow
  - Marketing materials management
  - Payout settings and schedule
  - Affiliate performance metrics
  - Terms and conditions editor

### 5.4. Email Marketing
- **Saat diklik**: Menampilkan tools email marketing.
- **Tampilan**:
  - Email template library
  - Campaign builder dengan:
    - Recipient selection
    - Subject line & preview text
    - Email body editor (visual)
    - Scheduling options
    - A/B testing setup
  - Analytics dashboard dengan open rates, click rates, conversion
  - Subscriber management
  - Automation workflow builder
  - Integration dengan email service providers

## 6. Analytics

**Saat diklik**: Menampilkan dashboard analytics komprehensif.

**Tampilan**:
- Date range selector
- Comparison period option
- Overview metrics dengan trend indicators
- Customizable chart section
- Report export options

**Submenu**:

### 6.1. Sales Analytics
- **Saat diklik**: Menampilkan analytics fokus penjualan.
- **Tampilan**:
  - Revenue charts (daily, weekly, monthly trends)
  - Sales breakdown by:
    - Product
    - Category
    - Time of day
    - Day of week
    - Payment method
  - Average order value trend
  - Sales forecasting
  - Seasonal pattern identification

### 6.2. Product Performance
- **Saat diklik**: Menampilkan analytics performa produk.
- **Tampilan**:
  - Best/worst selling products
  - Product view to purchase ratio
  - Category performance comparison
  - Stock turnover rate
  - Price point analysis
  - Cross-sell performance
  - Search term to product correlation

### 6.3. Customer Analytics
- **Saat diklik**: Menampilkan analytics customer behavior.
- **Tampilan**:
  - Customer acquisition sources
  - Retention rates
  - Repeat purchase frequency
  - Customer cohort analysis
  - Lifetime value calculation
  - Churn prediction
  - RFM (Recency, Frequency, Monetary) segmentation
  - Customer journey visualization

### 6.4. Traffic Analytics
- **Saat diklik**: Menampilkan analytics traffic store.
- **Tampilan**:
  - Visitor trends
  - Traffic sources breakdown
  - Device and browser statistics
  - Page performance
  - Entry and exit pages
  - Bounce rate analysis
  - Conversion funnel
  - Heat maps integration (if available)

## 7. Inventory

**Saat diklik**: Menampilkan dashboard manajemen inventori komprehensif.

**Tampilan**:
- Inventory health overview
- Low stock alerts
- Stock value calculation
- Inventory turnover metrics

**Submenu**:

### 7.1. Stock Management
- **Saat diklik**: Menampilkan tools manajemen stok.
- **Tampilan**:
  - Tabel produk dengan stock levels
  - Bulk stock adjustment tools
  - Stock movement history
  - Stock take functionality
  - Variance reporting
  - Safety stock calculator

### 7.2. Suppliers
- **Saat diklik**: Menampilkan manajemen supplier.
- **Tampilan**:
  - Supplier directory
  - Supplier form dengan:
    - Contact information
    - Product assignments
    - Lead times
    - Payment terms
    - Performance metrics
  - Order history per supplier
  - Supplier rating system

### 7.3. Purchase Orders
- **Saat diklik**: Menampilkan sistem purchase order.
- **Tampilan**:
  - Purchase order list
  - PO creation form dengan:
    - Supplier selection
    - Product selection with quantities
    - Pricing
    - Expected delivery date
    - Notes
  - PO status tracking
  - Receiving functionality
  - Integration dengan inventory

## 8. Content

**Saat diklik**: Menampilkan dashboard manajemen konten store.

**Tampilan**:
- Content overview
- Recent updates
- Publishing calendar
- Media usage statistics

**Submenu**:

### 8.1. Store Pages
- **Saat diklik**: Menampilkan manajemen halaman store.
- **Tampilan**:
  - Page list dengan status
  - Page editor dengan:
    - Visual editor (WYSIWYG)
    - HTML editor
    - Template selection
    - SEO settings
    - Featured image
    - Publication status and schedule
  - Page preview functionality
  - Revision history

### 8.2. Blog
- **Saat diklik**: Menampilkan manajemen blog store.
- **Tampilan**:
  - Article list dengan status
  - Blog post editor dengan:
    - Title and content
    - Category and tags
    - Featured image
    - Author assignment
    - SEO optimization
    - Social sharing preview
    - Comments settings
  - Publication schedule
  - Analytics per article

### 8.3. Media Library
- **Saat diklik**: Menampilkan manajemen asset media.
- **Tampilan**:
  - Grid/list view media files
  - Folders for organization
  - Upload zone (drag & drop)
  - Bulk upload tool
  - Image editor
  - Search and filter by file type, size, date
  - Usage tracking (which products use which media)
  - Storage usage statistics

### 8.4. Navigation
- **Saat diklik**: Menampilkan pengaturan menu navigasi.
- **Tampilan**:
  - Menu structure editor
  - Drag-and-drop menu builder
  - Link options:
    - Product categories
    - Custom pages
    - External links
    - Product links
  - Menu level settings
  - Mobile menu configuration
  - Menu preview

## 9. Settings

**Saat diklik**: Menampilkan pengaturan store.

**Tampilan**:
- Settings categories dalam sidebar
- Main configuration area

**Submenu**:

### 9.1. Store Profile
- **Saat diklik**: Menampilkan pengaturan profil store.
- **Tampilan**:
  - Store details:
    - Store name
    - Logo upload
    - Store description
    - Contact information
    - Business hours
    - Social media links
  - Store policies editor:
    - Return policy
    - Shipping policy
    - Privacy policy
    - Terms of service
  - Legal information:
    - Business registration
    - Tax information
    - Compliance settings

### 9.2. Shipping
- **Saat diklik**: Menampilkan pengaturan pengiriman.
- **Tampilan**:
  - Shipping methods manager
  - Shipping zones configuration:
    - Geographic regions
    - Shipping rates per zone
    - Free shipping thresholds
  - Shipping class definitions
  - Package dimensions presets
  - Shipping carrier integration
  - Label printing setup
  - Tracking configuration

### 9.3. Payment
- **Saat diklik**: Menampilkan pengaturan pembayaran.
- **Tampilan**:
  - Payment methods list dengan status
  - Payment gateway configuration:
    - API credentials
    - Transaction settings
    - Testing mode toggle
  - Payment icons setup
  - Order status mapping
  - Refund policy configuration
  - Tax calculation settings
  - Currency options

### 9.4. Tax
- **Saat diklik**: Menampilkan pengaturan pajak.
- **Tampilan**:
  - Tax calculation method
  - Tax rate tables:
    - Country/region rates
    - Tax classes
    - Product tax assignments
  - Tax display settings
  - VAT/GST registration
  - Tax exemption management
  - Automated tax service integration
  - Tax report generation

### 9.5. Notifications
- **Saat diklik**: Menampilkan pengaturan notifikasi.
- **Tampilan**:
  - Event triggers list
  - Notification template editor:
    - Email templates
    - SMS templates
    - Push notification settings
  - Recipient configuration
  - Schedule options
  - Notification history
  - Test notification tool

### 9.6. Staff
- **Saat diklik**: Menampilkan manajemen staff store.
- **Tampilan**:
  - Staff list dengan status
  - Staff member form:
    - Basic information
    - Role assignment
    - Permission settings
    - Contact details
    - Login credentials
  - Role editor dengan permission matrix
  - Activity logs per staff member
  - Performance metrics (if applicable)

### 9.7. Integrations
- **Saat diklik**: Menampilkan pengaturan integrasi.
- **Tampilan**:
  - Available integrations marketplace
  - Connected services list
  - API credential management
  - Webhook configuration
  - OAuth connections
  - Integration settings per service
  - Connection status monitoring
  - Log viewer untuk troubleshooting

### 9.8. Advanced
- **Saat diklik**: Menampilkan pengaturan lanjutan.
- **Tampilan**:
  - Database tools:
    - Import/export
    - Cleanup utilities
  - Developer options:
    - API access
    - Custom code injection
    - Logging level
  - Backup settings
  - Performance optimization
  - Debug mode toggle
  - Maintenance mode settings

## 10. Live Selling

**Saat diklik**: Menampilkan dashboard live shopping dan video commerce.

**Tampilan**:
- Upcoming live events calendar
- Past streams dengan metrics
- Quick start live button
- Performance overview

**Submenu**:

### 10.1. Schedule Stream
- **Saat diklik**: Menampilkan pengaturan jadwal live stream.
- **Tampilan**:
  - Stream scheduling form:
    - Title and description
    - Date and time
    - Duration
    - Featured products selection
    - Thumbnail/promotional image
    - Visibility settings
    - Notification options
  - Calendar view untuk jadwal
  - Stream template selection
  - Promotion settings for stream

### 10.2. Go Live
- **Saat diklik**: Menampilkan interface broadcast live.
- **Tampilan**:
  - Pre-stream setup:
    - Camera and microphone check
    - Background selection
    - Stream title confirmation
    - Social sharing options
  - Live control console:
    - Stream preview
    - Audience count
    - Chat monitor
    - Product showcase panel
    - Comment highlight tools
    - Screen sharing options
    - Live polls/Q&A features
  - Performance metrics in real-time
  - End stream confirmation

### 10.3. Videos
- **Saat diklik**: Menampilkan manajemen video konten.
- **Tampilan**:
  - Video library grid
  - Video upload tool
  - Video editor:
    - Trim and cut
    - Add overlay text
    - Add product tags
    - Thumbnail selection
    - Description and tags
    - Visibility settings
  - Video analytics per content
  - Video categorization
  - Comment moderation for videos

### 10.4. Live Analytics
- **Saat diklik**: Menampilkan analytics untuk live shopping.
- **Tampilan**:
  - Stream performance metrics:
    - Viewer count (peak, average)
    - Engagement rates
    - Watch time
    - Conversion metrics
    - Click-through rates
  - Comparison tools across streams
  - Audience demographics
  - Best performing products in streams
  - Optimal streaming time analysis
  - Viewer retention curves

## 11. Reports

**Saat diklik**: Menampilkan dashboard reporting and exports.

**Tampilan**:
- Report categories
- Recent reports
- Scheduled reports
- Export history

**Submenu**:

### 11.1. Sales Reports
- **Saat diklik**: Menampilkan laporan penjualan.
- **Tampilan**:
  - Report parameter selection:
    - Date range
    - Grouping (daily, weekly, monthly)
    - Product filters
    - Category filters
  - Visualization options
  - Data table with metrics
  - Export formats (PDF, Excel, CSV)
  - Scheduled report setup
  - Email delivery configuration

### 11.2. Inventory Reports
- **Saat diklik**: Menampilkan laporan inventori.
- **Tampilan**:
  - Stock level report
  - Inventory valuation
  - Stock movement history
  - Low stock alerts
  - Out of stock frequency
  - Slow-moving inventory
  - Inventory turnover
  - ABC analysis (stock classification)

### 11.3. Customer Reports
- **Saat diklik**: Menampilkan laporan customer.
- **Tampilan**:
  - Customer acquisition report
  - Customer retention analysis
  - Purchase frequency report
  - AOV (Average Order Value) trends
  - Geographic distribution
  - Device & platform usage
  - Time of purchase patterns
  - Customer lifetime value projection

### 11.4. Tax Reports
- **Saat diklik**: Menampilkan laporan pajak.
- **Tampilan**:
  - Sales tax summary
  - Tax by jurisdiction
  - Tax by period
  - Tax exemption report
  - Tax collected vs. remitted
  - Export for tax filing
  - Digital goods tax reporting
  - VAT/GST reports

## 12. Help & Support

**Saat diklik**: Menampilkan resources bantuan dan support.

**Tampilan**:
- FAQ sections
- Video tutorials
- Documentation links
- Contact support options

**Submenu**:

### 12.1. Knowledge Base
- **Saat diklik**: Menampilkan knowledge base store management.
- **Tampilan**:
  - Article categories
  - Search functionality
  - Popular articles
  - Step-by-step guides
  - Video tutorials
  - Best practices
  - Updates and announcements

### 12.2. Support Tickets
- **Saat diklik**: Menampilkan sistem tiket support.
- **Tampilan**:
  - Ticket list dengan status
  - Create ticket form:
    - Issue type
    - Priority
    - Description
    - Attachments
  - Ticket conversation thread
  - Resolution tracking
  - Satisfaction rating

### 12.3. System Status
- **Saat diklik**: Menampilkan status sistem dan platform.
- **Tampilan**:
  - Service status indicators
  - Recent incidents
  - Scheduled maintenance
  - Performance metrics
  - System notifications
  - API status
  - Integration status

## Fitur Umum di Setiap Halaman

1. **Navigasi Global**:
   - Store logo dan nama di header
   - Notifikasi icon dengan counter
   - User profile dropdown
   - Quick action menu
   - Collapsible sidebar navigation
   - Mobile responsive menu

2. **Store Performance Snapshot**:
   - Mini dashboard di header menampilkan sales hari ini
   - Order yang perlu diproses
   - Low stock alerts
   - Review yang perlu direspon

3. **Real-Time Notifications**:
   - New order alerts
   - Customer message notifications
   - Stock level warnings
   - System update notices

4. **Contextual Help**:
   - Help tooltips pada setiap section
   - Quick tutorial links
   - Inline documentation
   - Context-sensitive guides

5. **Store Preview**:
   - "View Store" button untuk melihat storefront
   - Preview mode untuk melihat perubahan sebelum publish
   - Mobile/desktop toggle preview

Dashboard Store dirancang untuk memudahkan pemilik toko mengelola seluruh aspek bisnis mereka secara efisien, dengan fokus pada usability dan akses cepat ke fungsi yang paling sering digunakan. 
Interface-nya intuitif dengan navigasi yang jelas, dan memiliki responsivitas yang baik di berbagai ukuran layar.

======================================================
# Dashboard User Sellzio SaaS (Termasuk Dashboard Store dan Affiliate)

Berikut adalah penjelasan komprehensif tentang Dashboard User Sellzio SaaS, yang mencakup akses ke Dashboard Store dan Dashboard Affiliate jika user mendaftar untuk peran tersebut.

## 1. Dashboard (Home)

**Saat diklik**: Menampilkan halaman beranda user dengan ringkasan aktivitas dan akses ke semua fitur.

**Tampilan**:
- **Greeting Banner**: Personalisasi dengan nama user dan waktu hari (Selamat Pagi/Siang/Sore/Malam)
- **Account Summary Cards**:
  - Orders: Jumlah order aktif dengan status indicators
  - Wishlist: Jumlah item dengan mini thumbnail
  - Rewards: Points balance dengan progress bar ke tier berikutnya
  - Saved Addresses: Jumlah alamat tersimpan

- **Quick Access**: Cards dengan shortcuts ke fungsi utama:
  - Track Packages
  - View Orders
  - Edit Profile
  - Contact Support
  
- **Conditional Modules**:
  - "My Store" card (jika terdaftar sebagai Store Owner) dengan store KPIs
  - "My Affiliate" card (jika terdaftar sebagai Affiliate) dengan earning metrics
  - "Become a Seller" promo (jika belum mendaftar sebagai Store Owner)
  - "Join Affiliate Program" promo (jika belum mendaftar sebagai Affiliate)

- **Recommended Products**:
  - Carousel produk berdasarkan browsing history dan preferences
  - "New Arrivals" section dari kategori favorit user
  - Wishlist items dengan status perubahan (price drop, back in stock)

- **Recent Activity**:
  - Timeline aktivitas terbaru (orders, reviews, questions, etc.)
  - Notification center dengan unread indicator

## 2. Orders

**Saat diklik**: Menampilkan halaman daftar order user dengan status tracking.

**Tampilan**:
- **Tab Filter**: All, In Progress, Completed, Cancelled
- **Search Bar**: untuk mencari order berdasarkan nomor atau produk
- **Order Cards**: Untuk setiap order menampilkan:
  - Order number dan tanggal
  - Status dengan visual indicator
  - Thumbnail produk utama + jumlah item
  - Total order
  - Shipping/delivery status
  - Action buttons: Track, View Details, Reorder, Cancel (jika available)

**Submenu**:

### 2.1. Order Details
- **Saat diklik dari list order**: Menampilkan detail lengkap order.
- **Tampilan**:
  - **Order Header**:
    - Order number, tanggal, dan status
    - Payment information
    - Shipping address dengan map preview
  
  - **Product List**:
    - Item details dengan images
    - Quantity dan harga per item
    - Product options/variants
    - Subtotal per item
  
  - **Order Summary**:
    - Subtotal
    - Shipping cost
    - Taxes
    - Discounts applied
    - Total paid
  
  - **Timeline Tracker**:
    - Visual order journey (Ordered → Processing → Shipped → Delivered)
    - Timestamp untuk setiap step
    - Carrier information dengan tracking number
    - Estimated delivery date
  
  - **Actions Area**:
    - Track Package button
    - Return/Exchange options (jika eligible)
    - Cancel Order (jika status memungkinkan)
    - Reorder button
    - Download Invoice
    - Contact Support about this order

### 2.2. Returns & Exchanges
- **Saat diklik**: Menampilkan daftar return requests dan opsi untuk membuat return baru.
- **Tampilan**:
  - **Active Returns**: List return requests dengan status
  - **Return History**: Past completed returns
  - **Create Return Form**:
    - Order selection
    - Item selection dengan quantity
    - Reason for return dropdown
    - Condition description
    - Photo upload option
    - Return method selection (refund or exchange)
    - Return shipping options

## 3. Wishlist

**Saat diklik**: Menampilkan daftar produk yang disimpan user.

**Tampilan**:
- **Grid View**: Visual grid produk wishlist dengan:
  - Product image
  - Name dan price
  - Stock status
  - "Add to Cart" button
  - "Remove" option
  - "Move to Collection" option jika collections aktif
  - Price change indicator (if price dropped)

- **Wishlist Features**:
  - Create Collection button untuk mengorganisir wishlist
  - Share Wishlist options (social media, copy link, email)
  - Sort options (Recently Added, Price, Name)
  - Filter options (In Stock, Sale Items, Price Range)

- **Product Quick View**:
  - Hover/click untuk product quick preview
  - Add to cart tanpa meninggalkan wishlist
  - View full details option

## 4. Account Settings

**Saat diklik**: Menampilkan pengaturan dan preferensi akun user.

**Tampilan**:
- **Tab Navigation**: Profile, Addresses, Payment Methods, Communication, Security, Connected Accounts

**Submenu**:

### 4.1. Profile
- **Saat diklik**: Menampilkan form edit informasi personal.
- **Tampilan**:
  - Profile picture upload/change
  - Personal information form:
    - First name, Last name
    - Email (dengan status verifikasi)
    - Phone number (dengan status verifikasi)
    - Date of birth
    - Gender (optional)
  - "Save Changes" button
  - Account verification status
  - Account created date

### 4.2. Addresses
- **Saat diklik**: Menampilkan manajemen alamat pengiriman dan penagihan.
- **Tampilan**:
  - Address cards untuk setiap alamat tersimpan
  - Default indicators (shipping/billing)
  - Edit dan Delete options
  - "Add New Address" button
  - Address form dengan:
    - Address nickname
    - Full name
    - Street address, Apt/Suite
    - City, State/Province
    - Postal code, Country
    - Phone number
    - Address type (Home, Work, Other)
    - Set as default shipping/billing checkboxes

### 4.3. Payment Methods
- **Saat diklik**: Menampilkan manajemen metode pembayaran.
- **Tampilan**:
  - Saved payment method cards (credit cards, digital wallets)
  - Card details dengan masked numbers
  - Expiration dates
  - Default payment indicator
  - Edit dan Delete options
  - "Add New Payment Method" button
  - Payment form dengan secure input fields
  - Billing address association

### 4.4. Communication Preferences
- **Saat diklik**: Menampilkan pengaturan komunikasi dan notifikasi.
- **Tampilan**:
  - Email subscription toggles:
    - Order updates
    - Promotions and sales
    - New product announcements
    - Recommendations
  - Push notification settings
  - SMS notification settings
  - Newsletter subscription management
  - Communication frequency preferences
  - Language preference for communications
  - Email history with option to view past communications

### 4.5. Security
- **Saat diklik**: Menampilkan pengaturan keamanan akun.
- **Tampilan**:
  - Password change form
  - Two-factor authentication setup
  - Login history dengan device info
  - Active sessions dengan "Log Out All Devices" option
  - Connected apps management
  - Account recovery options
  - Privacy controls

## 5. Store Application (Conditional - muncul jika user belum memiliki store)

**Saat diklik**: Menampilkan halaman pendaftaran untuk menjadi pemilik store.

**Tampilan**:
- **Introduction Banner**:
  - Benefits of becoming a seller
  - Success stories
  - Earning potential graphics
  - Commission structure overview
  - "Start Application" CTA button

- **Application Progress Tracker** (setelah memulai aplikasi):
  - Multi-step indicator
  - Save progress functionality
  - Estimated time to complete
  - Application ID

**Submenu** (Step Wizard):

### 5.1. Store Information
- **Saat diklik/di step 1**: Menampilkan form informasi dasar store.
- **Tampilan**:
  - Store name field
  - Store description textarea
  - Store category selection
  - Store logo upload
  - Business type (Individual, Registered Business)
  - Store email dan phone
  - Seller experience level

### 5.2. Product Information
- **Saat diklik/di step 2**: Menampilkan form detail produk yang akan dijual.
- **Tampilan**:
  - Product categories selection (multiple)
  - Estimated number of products
  - Product source (handmade, wholesale, etc.)
  - Sample product descriptions
  - Sample product images upload
  - Product pricing range
  - Shipping capabilities

### 5.3. Business Details
- **Saat diklik/di step 3**: Menampilkan form informasi bisnis legal.
- **Tampilan**:
  - Business registration info (jika applicable)
  - Tax identification number
  - Business address
  - Owner identification (KTP/ID Card upload)
  - Bank account information
  - Tax forms and compliance
  - Business documentation upload

### 5.4. Store Policies
- **Saat diklik/di step 4**: Menampilkan form kebijakan store.
- **Tampilan**:
  - Return policy editor (dengan template)
  - Shipping policy editor
  - Privacy policy editor
  - Terms of service
  - Customer service standards
  - Processing times
  - Shipping options

### 5.5. Review & Submit
- **Saat diklik/di step final**: Menampilkan ringkasan aplikasi untuk di-review.
- **Tampilan**:
  - Complete application summary
  - Edit links untuk setiap section
  - Terms & Conditions checkbox
  - Seller agreement
  - Commission acknowledgment
  - Submission button
  - Next steps explanation

### 5.6. Application Status
- **Setelah submit**: Menampilkan status aplikasi store.
- **Tampilan**:
  - Application status indicator (Under Review, Approved, Needs Info)
  - Estimated review time
  - Notification settings
  - Additional information requests (jika ada)
  - Approval messages
  - "Go to Store Dashboard" button (setelah disetujui)

## 6. Store Dashboard (Conditional - Muncul jika user sudah memiliki store)

**Saat diklik**: Masuk ke dashboard store untuk mengelola bisnis.

**Tampilan**: Full Store Dashboard seperti yang telah dijelaskan sebelumnya di dokumen Dashboard Store, dengan menu:
- Dashboard (overview)
- Products
- Orders
- Customers
- Marketing
- Analytics
- Inventory
- Content
- Settings
- Live Selling
- Reports
- Help & Support

## 7. Affiliate Application (Conditional - muncul jika user belum menjadi affiliate)

**Saat diklik**: Menampilkan halaman pendaftaran program affiliate.

**Tampilan**:
- **Introduction Banner**:
  - Affiliate program benefits
  - Earning potential
  - Commission structure
  - Success stories
  - "Join Now" CTA button

- **Application Progress Tracker** (setelah memulai aplikasi):
  - Simple step indicator
  - Application ID

**Submenu** (Step Wizard):

### 7.1. Personal Information
- **Saat diklik/di step 1**: Menampilkan form informasi personal.
- **Tampilan**:
  - Basic information confirmation (from existing account)
  - Professional background
  - Areas of expertise
  - Affiliate experience
  - Motivation for joining

### 7.2. Promotion Channels
- **Saat diklik/di step 2**: Menampilkan form promotion channels.
- **Tampilan**:
  - Channel type checkboxes:
    - Website/Blog
    - Social Media Profiles
    - YouTube Channel
    - Email Newsletter
    - Other
  - URL fields untuk setiap channel
  - Audience size/followers
  - Content categories
  - Engagement metrics
  - Sample content links

### 7.3. Payment Information
- **Saat diklik/di step 3**: Menampilkan pengaturan pembayaran affiliate.
- **Tampilan**:
  - Payment method selection
  - Bank account details
  - PayPal/digital wallet information
  - Tax information
  - Payment threshold preference
  - Payment frequency preference

### 7.4. Terms & Conditions
- **Saat diklik/di step 4**: Menampilkan agreement terms.
- **Tampilan**:
  - Affiliate program terms
  - Commission structure acknowledgment
  - Content guidelines
  - Prohibited practices
  - Termination conditions
  - Checkbox untuk menerima terms
  - Submission button

### 7.5. Application Status
- **Setelah submit**: Menampilkan status aplikasi affiliate.
- **Tampilan**:
  - Application status indicator
  - Estimated review timeline
  - Next steps explanation
  - "Go to Affiliate Dashboard" button (setelah disetujui)

## 8. Affiliate Dashboard (Conditional - Muncul jika user sudah menjadi affiliate)

**Saat diklik**: Masuk ke dashboard affiliate untuk mengelola aktivitas affiliate.

**Tampilan**:

### 8.1. Affiliate Overview
- **Saat masuk ke dashboard**: Menampilkan overview performa affiliate.
- **Tampilan**:
  - **Earnings Cards**:
    - Current period earnings
    - Pending commissions
    - Lifetime earnings
    - Next payout amount and date
  
  - **Performance Metrics**:
    - Conversion rate
    - Click count
    - Commission rate
    - Average order value
  
  - **Performance Graph**:
    - Earnings trend line/bar chart
    - Click vs. conversion comparison
    - Date range selector
  
  - **Quick Actions**:
    - Generate Link button
    - Access Marketing Materials
    - Request Payout (if minimum reached)
    - Support Contact

### 8.2. Links
- **Saat diklik**: Menampilkan link generator dan manajemen.
- **Tampilan**:
  - **Link Generator**:
    - Product or URL input field
    - Product search tool
    - Category selection for general links
    - Generated link display
    - Copy link button
    - QR code generation
    - Short link option
    - UTM parameter customization
  
  - **Active Links Table**:
    - Link URL (shortened)
    - Creation date
    - Clicks
    - Conversions
    - Revenue generated
    - Commission earned
    - Performance indicators
    - Edit/Delete actions

### 8.3. Marketing Materials
- **Saat diklik**: Menampilkan resource marketing.
- **Tampilan**:
  - **Banner Gallery**:
    - Various size options
    - Category filters
    - Seasonal/promotional filters
    - Preview images
    - HTML code snippet
    - Download options
  
  - **Product Widgets**:
    - Product carousel widget
    - Featured product widget
    - New arrivals widget
    - Sale items widget
    - Customization options
    - Code generator
  
  - **Content Templates**:
    - Product review templates
    - Social media post templates
    - Email newsletter templates
    - Blog article templates
    - Customize and download options

### 8.4. Analytics
- **Saat diklik**: Menampilkan detailed affiliate analytics.
- **Tampilan**:
  - **Performance Dashboard**:
    - Detailed metrics with time comparisons
    - Traffic sources breakdown
    - Device and platform analysis
    - Geographic distribution of clicks
    - Time of day/week analysis
  
  - **Product Performance**:
    - Top converting products
    - Highest commission products
    - Category performance
    - Price point analysis
  
  - **Customer Insights**:
    - New vs. returning customers
    - Average order value
    - Purchase frequency
    - Customer demographics (if available)
  
  - **Report Generator**:
    - Custom date range
    - Metric selection
    - Chart visualization options
    - Export to CSV/PDF

### 8.5. Payments
- **Saat diklik**: Menampilkan history dan status pembayaran.
- **Tampilan**:
  - **Payment Dashboard**:
    - Available balance
    - Pending balance
    - Payment threshold status
    - Next payout date
  
  - **Payment History Table**:
    - Date
    - Amount
    - Status
    - Method
    - Transaction ID
    - Reference number
    - Download receipt option
  
  - **Payment Methods**:
    - Payment method management
    - Add new payment method
    - Set default method
    - Update bank/wallet details
  
  - **Tax Documents**:
    - Tax forms for current year
    - Previous years archive
    - Download options
    - Tax information update

### 8.6. Settings
- **Saat diklik**: Menampilkan pengaturan affiliate account.
- **Tampilan**:
  - **Account Settings**:
    - Affiliate profile information
    - Promotion channels update
    - Category specializations
    - Notification preferences
  
  - **Commission Settings**:
    - Current commission structure
    - Performance tiers progress
    - Special rate eligibility
    - Commission history
  
  - **Link Preferences**:
    - Default UTM parameters
    - Auto-tagging options
    - Link format preferences
    - Default link shortening

## 9. Rewards Program

**Saat diklik**: Menampilkan status dan aktivitas program rewards.

**Tampilan**:
- **Rewards Dashboard**:
  - Points balance dengan large display
  - Current tier level with benefits
  - Progress bar ke tier berikutnya
  - Points expiration warning (jika ada)

- **Earning Opportunities**:
  - Ways to earn more points
  - Current promotions (2x points, etc.)
  - Daily/weekly challenges
  - Special events participation

- **Rewards Catalog**:
  - Available rewards by category
  - Points required per reward
  - Redemption process
  - Featured/new rewards
  - Recently redeemed items

- **Points History**:
  - Transaction list with earned/redeemed points
  - Source of points (purchase, review, referral)
  - Date and amount
  - Points balance after each transaction

## 10. Customer Service

**Saat diklik**: Menampilkan customer support options dan status.

**Tampilan**:
- **Support Dashboard**:
  - Open tickets status
  - Recent interactions
  - Suggested help articles based on activity
  - Quick contact options

**Submenu**:

### 10.1. Help Center
- **Saat diklik**: Menampilkan knowledge base dan FAQ.
- **Tampilan**:
  - Search bar dengan autocomplete suggestions
  - Popular topics
  - Category browsing
  - Article display dengan helpful rating
  - Related articles suggestions
  - Video tutorials section

### 10.2. My Tickets
- **Saat diklik**: Menampilkan support tickets user.
- **Tampilan**:
  - Ticket list dengan status indicators
  - Create new ticket button
  - Search/filter tickets
  - Ticket detail view dengan:
    - Conversation history
    - File attachments
    - Status updates
    - Response time indicator
    - Resolution rating (for closed tickets)

### 10.3. Contact Us
- **Saat diklik**: Menampilkan form dan opsi kontak.
- **Tampilan**:
  - Contact methods:
    - Live chat (dengan status availability)
    - Email form
    - Phone numbers
    - Social media support links
  - Contact form dengan:
    - Topic selection
    - Order reference (optional)
    - Message field
    - File attachment option
    - Priority selection
    - Response preference

## 11. Community

**Saat diklik**: Menampilkan fitur community platform.

**Tampilan**:
- **Community Hub**:
  - Activity feed dengan user discussions
  - Popular topics
  - Featured posts from moderators
  - Events calendar

**Submenu**:

### 11.1. Discussions
- **Saat diklik**: Menampilkan forum diskusi.
- **Tampilan**:
  - Topic categories
  - Trending threads
  - New post button
  - Discussion list dengan:
    - Topic title
    - Preview text
    - Author
    - Reply count
    - Last activity
    - View count
  - Thread view dengan full conversation
  - Reply functionality
  - Moderation tools (report, etc.)

### 11.2. Reviews
- **Saat diklik**: Menampilkan reviews user dan kemampuan menulis review.
- **Tampilan**:
  - My Reviews tab:
    - Reviews written by user
    - Draft reviews
    - Pending reviews
    - Edit/delete options
  - Write Review section:
    - Recently purchased products eligible for review
    - Product selection
    - Rating input
    - Review text editor
    - Photo/video upload
    - Pros/cons fields
    - Guidelines reminder
  - Review statistics:
    - Helpful votes received
    - Review level/badge
    - Impact metrics

### 11.3. Questions & Answers
- **Saat diklik**: Menampilkan Q&A participation.
- **Tampilan**:
  - My Questions tab:
    - Questions asked
    - Answers received
    - Status (answered/unanswered)
  - My Answers tab:
    - Questions answered by user
    - Helpful votes received
  - Ask Question section:
    - Product selection
    - Question field
    - Category tagging
    - Similar questions suggestion

### 11.4. Events
- **Saat diklik**: Menampilkan upcoming events.
- **Tampilan**:
  - Event calendar view
  - List view option
  - Event categories:
    - Live Shopping Events
    - Product Launches
    - Flash Sales
    - Community Meetups
    - Workshops/Tutorials
  - Event cards dengan:
    - Title and thumbnail
    - Date and time
    - Description
    - Host information
    - Participant count
    - Registration button
    - Add to calendar option
    - Reminder setup

## 12. Notifications

**Saat diklik**: Menampilkan notification center.

**Tampilan**:
- **Notification Feed**:
  - Tab filters: All, Unread, Orders, Promotions, System
  - Notification list dengan:
    - Icon for type
    - Preview text
    - Time/date
    - Read/unread status
    - Action button (jika applicable)
  - Mark all as read button
  - Clear all button
  - Notification detail view on click
  - Notification settings quick access

## Fitur Khusus untuk Mobile

1. **Bottom Navigation Bar**:
   - Home
   - Shop
   - Orders
   - Wishlist
   - Account

2. **Pull-to-refresh** pada semua halaman utama

3. **Gesture Navigation**:
   - Swipe gestures untuk product galleries
   - Swipe actions pada order items
   - Swipe to delete wishlist items

4. **Mobile-specific Features**:
   - Location-based store finder
   - Barcode scanner untuk quick search
   - Push notification opt-in flow
   - Mobile payment integration
   - Touch ID/Face ID login
   - Offline mode for basic browsing

## Fitur Umum di Semua Dashboard

1. **Cross-Dashboard Navigation**:
   - Seamless switching antara User, Store (jika ada), dan Affiliate (jika ada)
   - Consistent header dengan role indicator
   - Universal notification system
   - Shared profile settings

2. **Responsive Design**:
   - Adaptive layout untuk desktop, tablet, dan mobile
   - Optimized touch targets pada mobile
   - Collapsible sections untuk smaller screens
   - Critical actions tetap accessible di semua viewport

3. **Unified Branding**:
   - Konsistensi visual dengan tenant marketplace
   - Role-specific color coding:
     - User: Default tenant branding
     - Store: Purple accents
     - Affiliate: Orange accents

4. **Contextual Help**:
   - Role-specific help resources
   - Guided tours untuk new features
   - Tooltips untuk complex functionality
   - FAQ sections per major feature

Dashboard User dirancang sebagai hub sentral yang mengintegrasikan seluruh peran yang mungkin dimiliki user dalam ekosistem Sellzio SaaS,
 memungkinkan transisi yang mulus antara peran pembeli, penjual, dan affiliate. Dengan pendekatan terpadu ini, user dapat mengelola semua aktivitas mereka dari satu dashboard utama dengan navigasi intuitif ke dashboard khusus untuk peran tertentu ketika diperlukan.