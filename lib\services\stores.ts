import { getClient } from '@/lib/supabase'
import { PostgrestError } from '@supabase/supabase-js'

// Dapatkan instance Supabase client
const supabase = getClient()

// Fungsi untuk mengecek sesi yang valid
async function getValidSession() {
  try {
    console.log('[Stores] Mencoba mendapatkan sesi...')
    
    // Nonaktifkan mode development untuk menggunakan Supabase langsung
    // if (process.env.NODE_ENV === 'development' && (process.env.NEXT_PUBLIC_USE_MOCK_AUTH === 'true' || !process.env.NEXT_PUBLIC_SUPABASE_URL)) {
    //   console.log('[Stores] Menggunakan mock session untuk development')
    //   return {
    //     user: {
    //       id: 'mock-user-id',
    //       email: '<EMAIL>',
    //       role: 'admin',
    //     },
    //     // Tambahkan properti lain yang diperlukan
    //     expires_at: Date.now() + 3600,
    //     access_token: 'mock-token',
    //   }
    // }
    
    // Coba dapatkan sesi dari Supabase
    const { data: { session: supabaseSession }, error: supabaseError } = await supabase.auth.getSession()
    
    if (supabaseSession) {
      console.log('[Stores] Sesi Supabase ditemukan')
      return supabaseSession
    }
    
    // Jika tidak ada sesi Supabase, coba dapatkan token dari cookie atau localStorage
    if (typeof window !== 'undefined') {
      // Coba dari cookie dan localStorage
      let token = null
      
      // Cek dari cookie dengan berbagai format
      try {
        // Format 1: token=xxx
        token = document.cookie
          .split(';')
          .map(c => c.trim())
          .find(c => c.startsWith('token='))
          ?.split('=')[1]
        
        if (!token) {
          // Format 2: supabase-auth-token=xxx
          token = document.cookie
            .split(';')
            .map(c => c.trim())
            .find(c => c.startsWith('supabase-auth-token='))
            ?.split('=')[1]
        }
        
        // Jika masih tidak ditemukan, coba dari localStorage
        if (!token) {
          token = localStorage.getItem('token') || 
                 localStorage.getItem('supabase-auth-token') ||
                 localStorage.getItem('sb-uhtxhwhpobrbmcettmpt-auth-token')
          
          if (token && token.startsWith('{')) {
            try {
              const parsedToken = JSON.parse(token)
              token = parsedToken.access_token || parsedToken.token
            } catch (e) {
              console.error('[Stores] Error parsing token from localStorage:', e)
            }
          }
        }
      } catch (e) {
        console.error('[Stores] Error mendapatkan token dari cookie/localStorage:', e)
      }
      
      console.log('[Stores] Token ditemukan?', !!token)
      
      if (token) {
        console.log('[Stores] Token ditemukan, mencoba menyetel sesi...')
        
        try {
          // Coba set session dengan token
          const { data, error } = await supabase.auth.setSession({
            access_token: token,
            refresh_token: token, // Fallback, tidak ideal
          })
          
          if (data?.session) {
            console.log('[Stores] Berhasil menyetel sesi dengan token')
            return data.session
          } else if (error) {
            console.error('[Stores] Error saat menyetel sesi:', error)
          }
        } catch (e) {
          console.error('[Stores] Exception saat menyetel sesi:', e)
        }
      }
    }
    
    console.error('[Stores] Tidak ditemukan sesi yang valid')
    throw new Error(supabaseError?.message || 'Tidak ada sesi aktif. Silakan login terlebih dahulu.')
  } catch (error) {
    console.error('[Stores] Error saat validasi sesi:', error)
    throw new Error('Tidak ada sesi aktif. Silakan login terlebih dahulu.')
  }
}

// Fungsi untuk mendapatkan cookie
function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null
  
  const cookies = document.cookie.split(';')
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim()
    if (cookie.startsWith(name + '=')) {
      return cookie.substring(name.length + 1)
    }
  }
  return null
}

// Definisikan tipe data untuk Store
export interface Store {
  id: string;  // uuid
  name: string;
  owner: string;
  owner_name?: string; // Nama pemilik store
  email: string;
  status: string;  // USER-DEFINED type
  plan: string;
  products: number;
  orders: number;
  revenue: number;
  rating: number;
  join_date: string;  // date
  last_active: string; // text
  category: string;
 
  location: string;
  created_at?: string; // timestamp with timezone
  updated_at?: string; // timestamp with timezone
}

// Mendapatkan mock data dari localStorage (untuk development mode)
function getMockStoresFromLocalStorage(): Store[] {
  if (typeof window === 'undefined') return [];
  
  try {
    const storedData = localStorage.getItem('mock_stores');
    if (storedData) {
      return JSON.parse(storedData);
    }
  } catch (e) {
    console.error('[Stores] Error mendapatkan data mock dari localStorage:', e);
  }
  
  return [];
}

// Menyimpan mock data ke localStorage (untuk development mode)
function saveMockStoresToLocalStorage(stores: Store[]) {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('mock_stores', JSON.stringify(stores));
    console.log('[Stores] Data mock berhasil disimpan ke localStorage');
  } catch (e) {
    console.error('[Stores] Error menyimpan data mock ke localStorage:', e);
  }
}

// Mendapatkan semua store
export async function getStores() {
  console.log('[Stores] Memulai getStores')
  
  // Nonaktifkan mode development - selalu gunakan Supabase
  try {
    console.log('Menggunakan URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
    console.log('Mengambil data dari tabel stores...');
    
    // Query ke Supabase untuk mendapatkan stores
    const { data, error, status } = await supabase
      .from('stores')
      .select('*')
      .order('created_at', { ascending: false })
    
    console.log('Status response:', status)
    
    if (error) {
      console.error('Error dari Supabase:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      
      // Nonaktifkan penggunaan mock data
      // if (false) {
      //   const mockStores = getMockStoresFromLocalStorage();
      //   console.log('[Stores] Menggunakan data mock karena error Supabase. Jumlah data:', mockStores.length);
      //   return { data: mockStores, error: null };
      // }
      
      throw error;
    }
    
    console.log('Data berhasil diambil dari Supabase. Jumlah data:', data?.length || 0);
    
    // Nonaktifkan penggunaan mock data
    // if (false && (!data || data.length === 0)) {
    //   const mockStores = getMockStoresFromLocalStorage();
    //   if (mockStores.length > 0) {
    //     console.log('[Stores] Menggunakan data mock karena data Supabase kosong. Jumlah data:', mockStores.length);
    //     return { data: mockStores, error: null };
    //   }
    // }
    
    return { 
      data: data || [], 
      error: null 
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error saat mengambil data stores:', errorMessage);
    
    // Nonaktifkan penggunaan mock data
    // if (false) {
    //   const mockStores = getMockStoresFromLocalStorage();
    //   console.log('[Stores] Menggunakan data mock karena error. Jumlah data:', mockStores.length);
    //   return { data: mockStores, error: null };
    // }
    
    return { 
      data: [], 
      error: error as PostgrestError 
    };
  }
}

// Mendapatkan store berdasarkan ID
export async function getStoreById(id: string) {
  try {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return { data, error: null }
  } catch (error) {
    console.error(`Error fetching store with id ${id}:`, error)
    return { data: null, error }
  }
}

// Fungsi untuk menghasilkan random ID (untuk development mode)
function generateRandomId() {
  return 'store-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Menambah store baru
export async function createStore(storeData: Omit<Store, 'id' | 'created_at' | 'updated_at'>) {
  try {
    console.log('Membuat store baru dengan data:', storeData);
    
    // Nonaktifkan mode development untuk menggunakan Supabase langsung
    // if (typeof window !== 'undefined' && 
    //     (process.env.NODE_ENV === 'development' || window.location.host.includes('localhost'))) {
    //   console.log('[Stores] Menggunakan mock mode untuk createStore karena di development/localhost');
    //   
    //   // Data simulasi untuk respons
    //   const mockData = {
    //     id: generateRandomId(),
    //     name: storeData.name,
    //     owner: 'mock-user-id',
    //     email: storeData.email || '<EMAIL>',
    //     status: storeData.status || 'pending',
    //     plan: storeData.plan || 'Starter',
    //     products: storeData.products || 0,
    //     orders: storeData.orders || 0,
    //     revenue: storeData.revenue || 0,
    //     rating: storeData.rating || 0,
    //     join_date: new Date().toISOString(),
    //     last_active: new Date().toISOString(),
    //     category: storeData.category || 'Umum',
    //     location: storeData.location || 'Jakarta',
    //     created_at: new Date().toISOString(),
    //     updated_at: new Date().toISOString()
    //   };
    //   
    //   // Simpan data baru ke localStorage
    //   const existingStores = getMockStoresFromLocalStorage();
    //   const updatedStores = [mockData, ...existingStores];
    //   saveMockStoresToLocalStorage(updatedStores);
    //   
    //   console.log('[Stores] Store berhasil dibuat dan disimpan ke localStorage (mock mode):', mockData);
    //   return { data: mockData, error: null };
    // }
    
    // Mode produksi - gunakan Supabase
    // Dapatkan sesi yang valid
    try {
      const session = await getValidSession();
      const user = session.user;
      console.log('User yang login:', user.id);

      // Format data sesuai dengan skema database
      const formattedData = {
        name: storeData.name,
        owner: user.id, // Gunakan ID user yang login
        owner_name: storeData.owner, // Gunakan nilai owner dari form sebagai owner_name
        email: storeData.email,
        status: storeData.status || 'pending', // Default status
        plan: storeData.plan || 'Starter', // Default plan
        products: storeData.products || 0,
        orders: storeData.orders || 0,
        revenue: storeData.revenue || 0,
        rating: storeData.rating || 0,
        join_date: new Date().toISOString(), // Set tanggal saat ini
        last_active: new Date().toISOString(), // Set tanggal saat ini
        category: storeData.category,
        location: storeData.location,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      console.log('Data yang akan dikirim ke Supabase:', formattedData);
      
      // Hanya kirim field yang diperlukan
      const { data, error } = await supabase
        .from('stores')
        .insert({
          name: formattedData.name,
          owner: formattedData.owner,
          owner_name: formattedData.owner_name, // Tambahkan kolom owner_name
          email: formattedData.email,
          status: formattedData.status,
          plan: formattedData.plan,
          products: formattedData.products,
          orders: formattedData.orders,
          revenue: formattedData.revenue,
          rating: formattedData.rating,
          join_date: formattedData.join_date,
          last_active: formattedData.last_active,
          category: formattedData.category,
          location: formattedData.location
        })
        .select('*')
        .single()
      
      if (error) {
        console.error('Error dari Supabase saat membuat store:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        });
        throw error;
      }
      
      console.log('Store berhasil dibuat:', data);
      return { data, error: null };
    } catch (sessionError) {
      console.error('[Stores] Error sesi saat mencoba membuat store:', sessionError);
      
      // Jika dalam mode development dan ada error sesi, gunakan mock data
      if (process.env.NODE_ENV === 'development' || window.location.host.includes('localhost')) {
        console.log('[Stores] Menggunakan fallback mock data karena error sesi');
        
        const mockData = {
          id: generateRandomId(),
          name: storeData.name,
          owner: 'mock-user-id-fallback',
          email: storeData.email || '<EMAIL>',
          status: 'pending',
          plan: 'Starter',
          products: 0,
          orders: 0,
          revenue: 0,
          rating: 0,
          join_date: new Date().toISOString(),
          last_active: new Date().toISOString(),
          category: storeData.category || 'Umum',
          location: storeData.location || 'Jakarta',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        return { data: mockData, error: null };
      } else {
        throw sessionError; // Re-throw jika bukan di development
      }
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error saat membuat store:', {
      message: errorMessage,
      error: error
    });
    return { 
      data: null, 
      error: error instanceof Error ? error : new Error('Unknown error')
    };
  }
}

// Mengupdate store yang sudah ada
export async function updateStore(id: string, updates: Partial<Store>) {
  try {
    const { data, error } = await supabase
      .from('stores')
      .update(updates)
      .eq('id', id)
      .select()
    
    if (error) throw error
    return { data: data[0], error: null }
  } catch (error) {
    console.error(`Error updating store with id ${id}:`, error)
    return { data: null, error }
  }
}

/**
 * Updates the status of a store in the database
 * @param id Store ID
 * @param status New status (active, pending, suspended)
 * @returns Promise with success status and error if any
 */
export const updateStoreStatus = async (id: string, status: 'active' | 'pending' | 'suspended') => {
  if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_SUPABASE_URL) {
    // Mock implementation for development
    console.log(`Updating store ${id} status to ${status}`);
    return { success: true, error: null };
  }

  try {
    const { error } = await supabase
      .from('stores')
      .update({ status })
      .eq('id', id);

    if (error) throw error;

    return { success: true, error: null };
  } catch (error) {
    console.error('Error updating store status:', error);
    return { success: false, error };
  }
}

/**
 * Updates store settings in the database
 * @param id Store ID
 * @param settings Settings object with flags to update
 * @returns Promise with success status and error if any
 */
export const updateStoreSettings = async (id: string, settings: { 
  email_notifications?: boolean;
  product_verification?: boolean;
  visibility?: 'public' | 'private';
}) => {
  if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_SUPABASE_URL) {
    // Mock implementation for development
    console.log(`Updating store ${id} settings:`, settings);
    return { success: true, error: null };
  }

  try {
    const { error } = await supabase
      .from('store_settings')
      .upsert({ 
        store_id: id,
        ...settings,
        updated_at: new Date().toISOString()
      }, { 
        onConflict: 'store_id' 
      });

    if (error) throw error;

    return { success: true, error: null };
  } catch (error) {
    console.error('Error updating store settings:', error);
    return { success: false, error };
  }
}



// Menghapus store
export async function deleteStore(id: string) {
  try {
    const { error } = await supabase
      .from('stores')
      .delete()
      .eq('id', id)

    if (error) throw error

    return { success: true, error: null }
  } catch (error) {
    console.error('Error deleting store:', error)
    return { success: false, error }
  }
}