import { getClient } from '@/lib/supabase'

export interface Brand {
  id: string
  name: string
  slug: string
  description?: string
  logo_url?: string
  website_url?: string
  email?: string
  phone?: string
  address?: string
  country?: string
  is_active: boolean
  sort_order: number
  meta_title?: string
  meta_description?: string
  meta_keywords: string[]
  social_links: Record<string, string>
  created_at: string
  updated_at: string
  // Relations
  product_count?: number
}

export interface BrandFilters {
  search?: string
  is_active?: boolean
  country?: string
}

export interface BrandCreate {
  name: string
  slug: string
  description?: string
  logo_url?: string
  website_url?: string
  email?: string
  phone?: string
  address?: string
  country?: string
  is_active?: boolean
  sort_order?: number
  meta_title?: string
  meta_description?: string
  meta_keywords?: string[]
  social_links?: Record<string, string>
}

export interface BrandUpdate extends Partial<BrandCreate> {}

class BrandService {
  private supabase = getClient()

  // Get all brands with optional filters
  async getBrands(filters?: BrandFilters): Promise<Brand[]> {
    let query = this.supabase
      .from('brands')
      .select('*')
      .order('sort_order', { ascending: true })

    // Apply filters
    if (filters?.search) {
      const searchTerm = `%${filters.search}%`
      query = query.or(`name.ilike.${searchTerm},description.ilike.${searchTerm},slug.ilike.${searchTerm}`)
    }

    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active)
    }

    if (filters?.country) {
      query = query.eq('country', filters.country)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch brands: ${error.message}`)
    }

    return data || []
  }

  // Get single brand by ID
  async getBrand(id: string): Promise<Brand | null> {
    const { data, error } = await this.supabase
      .from('brands')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch brand: ${error.message}`)
    }

    return data
  }

  // Get brand by slug
  async getBrandBySlug(slug: string): Promise<Brand | null> {
    const { data, error } = await this.supabase
      .from('brands')
      .select('*')
      .eq('slug', slug)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch brand: ${error.message}`)
    }

    return data
  }

  // Create new brand
  async createBrand(brand: BrandCreate): Promise<Brand> {
    // Check if slug already exists
    const existingBrand = await this.getBrandBySlug(brand.slug)
    if (existingBrand) {
      throw new Error('Slug already exists')
    }

    const { data, error } = await this.supabase
      .from('brands')
      .insert([{
        ...brand,
        is_active: brand.is_active !== undefined ? brand.is_active : true,
        sort_order: brand.sort_order || 0,
        meta_keywords: brand.meta_keywords || [],
        social_links: brand.social_links || {}
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create brand: ${error.message}`)
    }

    return data
  }

  // Update brand
  async updateBrand(id: string, updates: BrandUpdate): Promise<Brand> {
    // If slug is being updated, check if it already exists
    if (updates.slug) {
      const existingBrand = await this.getBrandBySlug(updates.slug)
      if (existingBrand && existingBrand.id !== id) {
        throw new Error('Slug already exists')
      }
    }

    const { data, error } = await this.supabase
      .from('brands')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update brand: ${error.message}`)
    }

    return data
  }

  // Delete brand
  async deleteBrand(id: string): Promise<void> {
    // Check if brand has products
    const { count, error: countError } = await this.supabase
      .from('products')
      .select('id', { count: 'exact' })
      .eq('brand_id', id)

    if (countError) {
      throw new Error(`Failed to check products: ${countError.message}`)
    }

    if (count && count > 0) {
      throw new Error('Cannot delete brand with products')
    }

    const { error } = await this.supabase
      .from('brands')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete brand: ${error.message}`)
    }
  }

  // Toggle brand active status
  async toggleBrandStatus(id: string): Promise<Brand> {
    const brand = await this.getBrand(id)
    if (!brand) {
      throw new Error('Brand not found')
    }

    return this.updateBrand(id, { is_active: !brand.is_active })
  }

  // Reorder brands
  async reorderBrands(brandOrders: { id: string; sort_order: number }[]): Promise<void> {
    const updates = brandOrders.map(({ id, sort_order }) => 
      this.supabase
        .from('brands')
        .update({ sort_order })
        .eq('id', id)
    )

    const results = await Promise.all(updates)
    
    for (const result of results) {
      if (result.error) {
        throw new Error(`Failed to reorder brands: ${result.error.message}`)
      }
    }
  }

  // Get brand statistics
  async getBrandStats(): Promise<{
    total: number
    active: number
    inactive: number
    with_products: number
    countries: number
  }> {
    const { data, error } = await this.supabase
      .from('brands')
      .select('is_active, country')

    if (error) {
      throw new Error(`Failed to fetch brand stats: ${error.message}`)
    }

    const stats = {
      total: data.length,
      active: 0,
      inactive: 0,
      with_products: 0,
      countries: 0,
    }

    const countries = new Set<string>()

    data.forEach((brand) => {
      if (brand.is_active) {
        stats.active++
      } else {
        stats.inactive++
      }

      if (brand.country) {
        countries.add(brand.country)
      }
    })

    stats.countries = countries.size

    // Get brands with products count
    const { data: brandsWithProducts, error: productsError } = await this.supabase
      .from('products')
      .select('brand_id')
      .not('brand_id', 'is', null)

    if (!productsError && brandsWithProducts) {
      const uniqueBrands = new Set(brandsWithProducts.map(p => p.brand_id))
      stats.with_products = uniqueBrands.size
    }

    return stats
  }

  // Get brands with product counts
  async getBrandsWithProductCounts(): Promise<Brand[]> {
    const brands = await this.getBrands()
    
    // Get product counts for each brand
    const { data: productCounts, error } = await this.supabase
      .from('products')
      .select('brand_id')
      .not('brand_id', 'is', null)

    if (error) {
      throw new Error(`Failed to fetch product counts: ${error.message}`)
    }

    // Count products per brand
    const countMap = new Map<string, number>()
    productCounts?.forEach(product => {
      const count = countMap.get(product.brand_id) || 0
      countMap.set(product.brand_id, count + 1)
    })

    // Add product counts to brands
    return brands.map(brand => ({
      ...brand,
      product_count: countMap.get(brand.id) || 0
    }))
  }

  // Get countries list
  async getCountries(): Promise<string[]> {
    const { data, error } = await this.supabase
      .from('brands')
      .select('country')
      .not('country', 'is', null)
      .order('country')

    if (error) {
      throw new Error(`Failed to fetch countries: ${error.message}`)
    }

    const countries = [...new Set(data.map(item => item.country).filter(Boolean))]
    return countries
  }

  // Search brands by name
  async searchBrands(query: string, limit = 10): Promise<Brand[]> {
    const { data, error } = await this.supabase
      .from('brands')
      .select('id, name, slug, logo_url')
      .ilike('name', `%${query}%`)
      .eq('is_active', true)
      .order('name')
      .limit(limit)

    if (error) {
      throw new Error(`Failed to search brands: ${error.message}`)
    }

    return data || []
  }
}

export const brandService = new BrandService()
