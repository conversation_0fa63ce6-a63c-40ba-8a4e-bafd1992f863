"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { PageHeader } from "@/components/admin/ui/page-header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Edit, Trash, ShoppingBag, Store, BarChart, Users, Clock } from "lucide-react"
import { useConfirmation } from "@/components/admin/ui/confirmation-dialog"
import { useNotifications } from "@/components/admin/ui/notifications"

export default function StoreDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { confirm, confirmationDialog } = useConfirmation()
  const { addNotification } = useNotifications()
  const [isLoading, setIsLoading] = useState(true)
  const [store, setStore] = useState<any>(null)

  useEffect(() => {
    // Simulasi fetch data
    setTimeout(() => {
      setStore({
        id: params.id,
        name: "Fashion Store",
        tenant: "Acme Corporation",
        category: "Clothing",
        description: "A premium fashion store offering the latest trends in clothing and accessories.",
        products: 120,
        orders: 450,
        revenue: "$12,500",
        status: "Active",
        createdAt: "2023-01-15T10:00:00Z",
        owner: "John Smith",
        email: "<EMAIL>",
        phone: "+****************",
        address: "123 Fashion Ave, New York, NY 10001",
        website: "https://fashionstore.com",
      })
      setIsLoading(false)
    }, 1000)
  }, [params.id])

  const handleDelete = () => {
    confirm({
      title: "Delete Store",
      description: `Are you sure you want to delete ${store?.name}? This action cannot be undone.`,
      confirmLabel: "Delete",
      cancelLabel: "Cancel",
      variant: "destructive",
      onConfirm: () => {
        setIsLoading(true)
        setTimeout(() => {
          setIsLoading(false)
          addNotification({
            title: "Store Deleted",
            message: `${store?.name} has been deleted.`,
            type: "success",
          })
          router.push("/admin/dashboard/stores")
        }, 1000)
      },
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  if (!store) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <h2 className="text-2xl font-bold mb-2">Store Not Found</h2>
        <p className="text-muted-foreground mb-4">The store you are looking for does not exist or has been removed.</p>
        <Button onClick={() => router.push("/admin/dashboard/stores")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Stores
        </Button>
      </div>
    )
  }

  return (
    <>
      <PageHeader
        title={store.name}
        description={`View and manage details for ${store.name}`}
        breadcrumbs={[
          { title: "Dashboard", href: "/admin/dashboard" },
          { title: "Stores", href: "/admin/dashboard/stores" },
          { title: store.name, href: `/admin/dashboard/stores/${store.id}` },
        ]}
        actions={
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => router.push("/admin/dashboard/stores")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <Button variant="outline" onClick={() => router.push(`/admin/dashboard/stores/${store.id}/edit`)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              <Trash className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        }
      />

      <div className="grid gap-6 md:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{store.products}</div>
            <p className="text-xs text-muted-foreground">+8% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{store.orders}</div>
            <p className="text-xs text-muted-foreground">+5% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <BarChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{store.revenue}</div>
            <p className="text-xs text-muted-foreground">+12% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div
                className={`mr-2 h-3 w-3 rounded-full ${
                  store.status === "Active"
                    ? "bg-green-500"
                    : store.status === "Inactive"
                      ? "bg-red-500"
                      : "bg-yellow-500"
                }`}
              />
              <span className="text-lg font-medium">{store.status}</span>
            </div>
            <p className="text-xs text-muted-foreground">Since {new Date(store.createdAt).toLocaleDateString()}</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="details">
        <TabsList className="mb-4">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Store Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Store Name</h3>
                  <p className="text-base">{store.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Tenant</h3>
                  <p className="text-base">{store.tenant}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Category</h3>
                  <Badge variant="outline">{store.category}</Badge>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Created At</h3>
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                    <p className="text-base">{new Date(store.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="md:col-span-2">
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
                  <p className="text-base">{store.description}</p>
                </div>
              </div>

              <div className="border-t pt-6">
                <h3 className="text-lg font-medium mb-4">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Owner</h3>
                    <p className="text-base">{store.owner}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Email</h3>
                    <p className="text-base">{store.email}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Phone</h3>
                    <p className="text-base">{store.phone}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Website</h3>
                    <p className="text-base">{store.website}</p>
                  </div>
                  <div className="md:col-span-2">
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Address</h3>
                    <p className="text-base">{store.address}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="products">
          <Card>
            <CardHeader>
              <CardTitle>Products</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">This store has {store.products} products.</p>
              <Button className="mt-4" onClick={() => router.push(`/admin/dashboard/stores/${store.id}/products`)}>
                View Products
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">This store has {store.orders} orders.</p>
              <Button className="mt-4" onClick={() => router.push(`/admin/dashboard/stores/${store.id}/orders`)}>
                View Orders
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">View analytics for this store.</p>
              <Button className="mt-4" onClick={() => router.push(`/admin/dashboard/stores/${store.id}/analytics`)}>
                View Analytics
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {confirmationDialog}
    </>
  )
}
