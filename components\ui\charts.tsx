"use client"

import { useMemo } from "react"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  type ChartOptions,
} from "chart.js"
import { Bar, Line, Pie } from "react-chartjs-2"

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, ArcElement, Title, Tooltip, Legend)

// Common chart options
const defaultOptions: ChartOptions<"bar" | "line" | "pie"> = {
  responsive: true,
  plugins: {
    legend: {
      position: "top" as const,
    },
    title: {
      display: true,
      text: "Chart",
    },
  },
  maintainAspectRatio: false,
}

// Line Chart Component
interface LineChartProps {
  data: any[]
  index: string
  categories: string[]
  colors?: string[]
  valueFormatter?: (value: number) => string
  height?: number
  className?: string
}

export function LineChart({
  data = [],
  index,
  categories,
  colors = ["#3b82f6", "#10b981", "#ef4444", "#f59e0b"],
  valueFormatter = (value: number) => `${value}`,
  height = 300,
  className,
}: LineChartProps) {
  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return {
        labels: [],
        datasets: [],
      }
    }

    return {
      labels: data.map((item) => item[index]),
      datasets: categories.map((category, i) => ({
        label: category,
        data: data.map((item) => item[category] || 0),
        borderColor: colors[i % colors.length],
        backgroundColor: colors[i % colors.length] + "20",
        tension: 0.2,
      })),
    }
  }, [data, index, categories, colors])

  const chartOptions = useMemo(
    () => ({
      ...defaultOptions,
      plugins: {
        ...defaultOptions.plugins,
        tooltip: {
          callbacks: {
            label: (context: any) => {
              const label = context.dataset.label || ""
              const value = context.parsed.y
              return `${label}: ${valueFormatter(value)}`
            },
          },
        },
      },
    }),
    [valueFormatter],
  )

  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className={`flex h-[${height}px] items-center justify-center ${className}`}>
        <p className="text-muted-foreground">No data available</p>
      </div>
    )
  }

  return (
    <div style={{ height }} className={className}>
      <Line data={chartData} options={chartOptions} />
    </div>
  )
}

// Bar Chart Component
interface BarChartProps {
  data: any[]
  index: string
  categories: string[]
  colors?: string[]
  valueFormatter?: (value: number) => string
  height?: number
  className?: string
}

export function BarChart({
  data = [],
  index,
  categories,
  colors = ["#3b82f6", "#10b981", "#ef4444", "#f59e0b"],
  valueFormatter = (value: number) => `${value}`,
  height = 300,
  className,
}: BarChartProps) {
  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return {
        labels: [],
        datasets: [],
      }
    }

    return {
      labels: data.map((item) => item[index]),
      datasets: categories.map((category, i) => ({
        label: category,
        data: data.map((item) => item[category] || 0),
        backgroundColor: colors[i % colors.length] + "80",
        borderColor: colors[i % colors.length],
        borderWidth: 1,
      })),
    }
  }, [data, index, categories, colors])

  const chartOptions = useMemo(
    () => ({
      ...defaultOptions,
      plugins: {
        ...defaultOptions.plugins,
        tooltip: {
          callbacks: {
            label: (context: any) => {
              const label = context.dataset.label || ""
              const value = context.parsed.y
              return `${label}: ${valueFormatter(value)}`
            },
          },
        },
      },
    }),
    [valueFormatter],
  )

  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className={`flex h-[${height}px] items-center justify-center ${className}`}>
        <p className="text-muted-foreground">No data available</p>
      </div>
    )
  }

  return (
    <div style={{ height }} className={className}>
      <Bar data={chartData} options={chartOptions} />
    </div>
  )
}

// Pie Chart Component
interface PieChartProps {
  data: any[]
  index: string
  category: string
  colors?: string[]
  valueFormatter?: (value: number) => string
  height?: number
  className?: string
}

export function PieChart({
  data = [],
  index,
  category,
  colors = ["#3b82f6", "#10b981", "#ef4444", "#f59e0b", "#8b5cf6", "#ec4899", "#f97316", "#a855f7"],
  valueFormatter = (value: number) => `${value}`,
  height = 300,
  className,
}: PieChartProps) {
  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return {
        labels: [],
        datasets: [],
      }
    }

    return {
      labels: data.map((item) => item[index]),
      datasets: [
        {
          data: data.map((item) => item[category] || 0),
          backgroundColor: data.map((_, i) => colors[i % colors.length] + "80"),
          borderColor: data.map((_, i) => colors[i % colors.length]),
          borderWidth: 1,
        },
      ],
    }
  }, [data, index, category, colors])

  const chartOptions = useMemo(
    () => ({
      ...defaultOptions,
      plugins: {
        ...defaultOptions.plugins,
        tooltip: {
          callbacks: {
            label: (context: any) => {
              const label = context.label || ""
              const value = context.raw
              return `${label}: ${valueFormatter(value)}`
            },
          },
        },
      },
    }),
    [valueFormatter],
  )

  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className={`flex h-[${height}px] items-center justify-center ${className}`}>
        <p className="text-muted-foreground">No data available</p>
      </div>
    )
  }

  return (
    <div style={{ height }} className={className}>
      <Pie data={chartData} options={chartOptions} />
    </div>
  )
}
