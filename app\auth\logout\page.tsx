export const dynamic = "force-dynamic"

export default function LogoutPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <p>Redirecting to login page...</p>
      <script
        dangerouslySetInnerHTML={{
          __html: `
            if (typeof window !== 'undefined') {
              // Clear cookies and localStorage
              document.cookie.split(';').forEach(function(c) {
                document.cookie = c.trim().split('=')[0] + '=;' + 'expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;';
              });
              localStorage.clear();
              
              // Redirect to login page
              window.location.href = '/auth/login';
            }
          `,
        }}
      />
    </div>
  )
}
