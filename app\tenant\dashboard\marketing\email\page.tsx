"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Search,
  Plus,
  Mail,
  Send,
  Users,
  BarChart3,
  Calendar,
  Clock,
  Eye,
  PencilIcon,
  Trash,
  MoreHorizontal,
  Copy,
  Filter,
  ChevronDown,
  LayoutTemplate,
  FileText,
  CheckCircle2,
  XCircle,
  AlertCircle,
  PauseCircle
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk kampanye email
const emailCampaigns = [
  {
    id: "email-001",
    name: "Promo Bulan <PERSON>ri",
    status: "sent",
    subject: "Diskon Spesial Awal <PERSON>hun - <PERSON>!",
    scheduledDate: "2024-01-05T10:00:00",
    sentDate: "2024-01-05T10:00:00",
    audience: "all_customers",
    audienceCount: 5260,
    openRate: 32.5,
    clickRate: 15.2,
    unsubscribeRate: 0.8,
    template: "January_promo_2024"
  },
  {
    id: "email-002",
    name: "Peluncuran Produk Baru",
    status: "draft",
    subject: "Produk Baru Telah Tiba - Eksklusif untuk Anda",
    scheduledDate: null,
    sentDate: null,
    audience: "loyal_customers",
    audienceCount: 1870,
    openRate: 0,
    clickRate: 0,
    unsubscribeRate: 0,
    template: "new_product_launch"
  },
  {
    id: "email-003",
    name: "Newsletter Mingguan",
    status: "scheduled",
    subject: "Update Mingguan Marketplace - Minggu ke-3 Januari",
    scheduledDate: "2024-01-20T09:00:00",
    sentDate: null,
    audience: "newsletter_subscribers",
    audienceCount: 4150,
    openRate: 0,
    clickRate: 0,
    unsubscribeRate: 0,
    template: "weekly_newsletter"
  },
  {
    id: "email-004",
    name: "Reminder Abandoned Cart",
    status: "active",
    subject: "Keranjang Belanja Anda Menunggu!",
    scheduledDate: "recurring",
    sentDate: "recurring",
    audience: "abandoned_carts",
    audienceCount: "dynamic",
    openRate: 45.2,
    clickRate: 28.7,
    unsubscribeRate: 0.3,
    template: "cart_abandonment"
  },
  {
    id: "email-005",
    name: "Survei Kepuasan Pelanggan",
    status: "paused",
    subject: "Bantu Kami Meningkatkan Layanan - Dapatkan Diskon 10%",
    scheduledDate: "2024-01-15T14:00:00",
    sentDate: null,
    audience: "recent_customers",
    audienceCount: 2340,
    openRate: 0,
    clickRate: 0,
    unsubscribeRate: 0,
    template: "customer_survey"
  }
]

// Data dummy untuk template email
const emailTemplates = [
  {
    id: "template-001",
    name: "January_promo_2024",
    type: "promotion",
    category: "seasonal",
    lastModified: "2024-01-02T15:30:00",
    usageCount: 3,
    previewImage: "/assets/templates/january_promo.png",
    variables: ["customer_name", "promo_code", "expiry_date"]
  },
  {
    id: "template-002",
    name: "cart_abandonment",
    type: "transactional",
    category: "recovery",
    lastModified: "2023-12-15T11:20:00",
    usageCount: 120,
    previewImage: "/assets/templates/cart_abandonment.png",
    variables: ["customer_name", "cart_items", "cart_total", "discount_offer"]
  },
  {
    id: "template-003",
    name: "weekly_newsletter",
    type: "newsletter",
    category: "regular",
    lastModified: "2024-01-10T09:45:00",
    usageCount: 15,
    previewImage: "/assets/templates/weekly_newsletter.png",
    variables: ["customer_name", "featured_products", "latest_news", "top_deals"]
  },
  {
    id: "template-004",
    name: "new_product_launch",
    type: "promotion",
    category: "product",
    lastModified: "2023-12-28T14:15:00",
    usageCount: 2,
    previewImage: "/assets/templates/product_launch.png",
    variables: ["customer_name", "product_name", "product_details", "special_offer"]
  },
  {
    id: "template-005",
    name: "customer_survey",
    type: "engagement",
    category: "feedback",
    lastModified: "2024-01-05T16:40:00",
    usageCount: 1,
    previewImage: "/assets/templates/customer_survey.png",
    variables: ["customer_name", "survey_link", "reward_details"]
  }
]

// Data dummy untuk segmen audiens
const audienceSegments = [
  {
    id: "segment-001",
    name: "all_customers",
    description: "Semua pelanggan terdaftar",
    count: 5260,
    criteria: "Semua pelanggan aktif"
  },
  {
    id: "segment-002",
    name: "loyal_customers",
    description: "Pelanggan dengan minimal 3 pembelian",
    count: 1870,
    criteria: "Min. 3 pembelian, Terakhir order < 60 hari"
  },
  {
    id: "segment-003",
    name: "newsletter_subscribers",
    description: "Pelanggan yang berlangganan newsletter",
    count: 4150,
    criteria: "Newsletter subscription = true"
  },
  {
    id: "segment-004",
    name: "abandoned_carts",
    description: "Pelanggan yang meninggalkan keranjang belanja",
    count: "dynamic",
    criteria: "Keranjang aktif > 1 jam, Checkout tidak selesai"
  },
  {
    id: "segment-005",
    name: "recent_customers",
    description: "Pelanggan dengan pembelian dalam 30 hari terakhir",
    count: 2340,
    criteria: "Pembelian < 30 hari"
  }
]

// Fungsi untuk badge status kampanye
function getCampaignStatusBadge(status: string) {
  switch (status) {
    case "sent":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Terkirim</Badge>
    case "scheduled":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">Terjadwal</Badge>
    case "draft":
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>
    case "active":
      return <Badge variant="outline" className="bg-purple-100 text-purple-800">Aktif (Otomatis)</Badge>
    case "paused":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Dijeda</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk badge tipe template
function getTemplateTypeBadge(type: string) {
  switch (type) {
    case "promotion":
      return <Badge variant="outline" className="bg-red-100 text-red-800">Promosi</Badge>
    case "transactional":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Transaksional</Badge>
    case "newsletter":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">Newsletter</Badge>
    case "engagement":
      return <Badge variant="outline" className="bg-purple-100 text-purple-800">Engagement</Badge>
    default:
      return <Badge variant="outline">{type}</Badge>
  }
}

// Fungsi untuk format persentase
function formatPercentage(value: number) {
  return `${value}%`
}

// Fungsi untuk format tanggal
function formatDate(dateString: string | null) {
  if (!dateString) return "-"
  if (dateString === "recurring") return "Otomatis (Recurring)"
  
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Fungsi untuk format jumlah audiens
function formatAudience(count: number | string) {
  if (count === "dynamic") return "Dinamis"
  return count.toLocaleString('id-ID')
}

export default function EmailMarketingPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("campaigns")
  
  // Filter kampanye
  const filteredCampaigns = emailCampaigns.filter(campaign => 
    campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    campaign.subject.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  // Filter template
  const filteredTemplates = emailTemplates.filter(template => 
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.category.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  // Filter segmen audiens
  const filteredSegments = audienceSegments.filter(segment => 
    segment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    segment.description.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  // Statistik
  const stats = {
    totalCampaigns: emailCampaigns.length,
    sentCampaigns: emailCampaigns.filter(c => c.status === "sent").length,
    totalTemplates: emailTemplates.length,
    totalSegments: audienceSegments.length,
    avgOpenRate: emailCampaigns
      .filter(c => c.status === "sent" || c.status === "active")
      .reduce((sum, c) => sum + c.openRate, 0) / 
      emailCampaigns.filter(c => c.status === "sent" || c.status === "active").length || 0,
    avgClickRate: emailCampaigns
      .filter(c => c.status === "sent" || c.status === "active")
      .reduce((sum, c) => sum + c.clickRate, 0) / 
      emailCampaigns.filter(c => c.status === "sent" || c.status === "active").length || 0
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/marketing">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Email Marketing</h1>
            <p className="text-muted-foreground">
              Kelola kampanye email, template, dan segmentasi audiens
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Users className="h-4 w-4 mr-2" />
            Kelola Audiens
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Buat Kampanye
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Kampanye</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCampaigns}</div>
            <p className="text-xs text-muted-foreground">
              Terkirim: {stats.sentCampaigns}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rata-rata Open Rate</CardTitle>
            <Eye className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatPercentage(stats.avgOpenRate)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rata-rata Click Rate</CardTitle>
            <BarChart3 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatPercentage(stats.avgClickRate)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Segmen Audiens</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.totalSegments}</div>
            <p className="text-xs text-muted-foreground">
              Template: {stats.totalTemplates}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search & Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari kampanye, template, atau segmen..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="flex-shrink-0">
              <Filter className="h-4 w-4 mr-2" />
              Filter
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Tabs */}
      <Tabs defaultValue="campaigns" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="campaigns">
            <Mail className="h-4 w-4 mr-2" />
            Kampanye
          </TabsTrigger>
          <TabsTrigger value="templates">
            <LayoutTemplate className="h-4 w-4 mr-2" />
            Template
          </TabsTrigger>
          <TabsTrigger value="segments">
            <Users className="h-4 w-4 mr-2" />
            Segmen Audiens
          </TabsTrigger>
        </TabsList>
        
        {/* Kampanye Tab */}
        <TabsContent value="campaigns" className="space-y-4 mt-6">
          {filteredCampaigns.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <Mail className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada kampanye ditemukan</h3>
                <p className="text-muted-foreground mb-4 text-center">
                  Tidak ada kampanye yang cocok dengan pencarian Anda
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Buat Kampanye
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredCampaigns.map((campaign) => (
              <Card key={campaign.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row justify-between gap-4">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-semibold text-lg">{campaign.name}</span>
                          {getCampaignStatusBadge(campaign.status)}
                        </div>
                        <div className="text-sm text-muted-foreground mb-1">
                          Subject: {campaign.subject}
                        </div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Calendar className="h-3.5 w-3.5" />
                          <span>
                            {campaign.status === "sent" 
                              ? `Terkirim: ${formatDate(campaign.sentDate)}`
                              : campaign.status === "scheduled" 
                                ? `Dijadwalkan: ${formatDate(campaign.scheduledDate)}`
                                : campaign.status === "active"
                                  ? "Aktif (Otomatis)"
                                  : "Draft"}
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-blue-600" />
                          <span className="text-sm">
                            Audiens: <span className="font-medium">{campaign.audience}</span> 
                            {campaign.audienceCount !== "dynamic" && ` (${formatAudience(campaign.audienceCount)})`}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <LayoutTemplate className="h-4 w-4 text-purple-600" />
                          <span className="text-sm">
                            Template: <span className="font-medium">{campaign.template}</span>
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    {(campaign.status === "sent" || campaign.status === "active") && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                        <div>
                          <p className="text-xs text-muted-foreground mb-1">Open Rate</p>
                          <div className="flex items-center gap-1">
                            <Eye className="h-4 w-4 text-blue-600" />
                            <p className="font-semibold">{formatPercentage(campaign.openRate)}</p>
                          </div>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground mb-1">Click Rate</p>
                          <p className="font-semibold">{formatPercentage(campaign.clickRate)}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground mb-1">Unsubscribe Rate</p>
                          <p className="font-semibold">{formatPercentage(campaign.unsubscribeRate)}</p>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex gap-2 pt-2 border-t mt-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        Lihat
                      </Button>
                      {campaign.status === "draft" && (
                        <Button size="sm" variant="outline">
                          <PencilIcon className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                      )}
                      {campaign.status === "draft" && (
                        <Button size="sm" variant="outline" className="text-blue-600 hover:text-blue-700">
                          <Send className="h-4 w-4 mr-2" />
                          Kirim
                        </Button>
                      )}
                      {campaign.status === "active" && (
                        <Button size="sm" variant="outline" className="text-yellow-600 hover:text-yellow-700">
                          <PauseCircle className="h-4 w-4 mr-2" />
                          Jeda
                        </Button>
                      )}
                      {campaign.status === "paused" && (
                        <Button size="sm" variant="outline" className="text-green-600 hover:text-green-700">
                          <CheckCircle2 className="h-4 w-4 mr-2" />
                          Aktifkan
                        </Button>
                      )}
                      <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                        <Trash className="h-4 w-4 mr-2" />
                        Hapus
                      </Button>
                      <Button size="sm" variant="outline">
                        <Copy className="h-4 w-4 mr-2" />
                        Duplikat
                      </Button>
                      <Button size="sm" variant="ghost">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
          {filteredCampaigns.length > 0 && (
            <div className="flex justify-center mt-4">
              <Button variant="outline" className="mr-2" size="sm">
                Sebelumnya
              </Button>
              <Button variant="outline" size="sm">
                Selanjutnya
              </Button>
            </div>
          )}
        </TabsContent>
        
        {/* Template Tab */}
        <TabsContent value="templates" className="space-y-4 mt-6">
          <div className="flex justify-between mb-4">
            <h3 className="text-lg font-medium">Template Email</h3>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Buat Template
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.length === 0 ? (
              <Card className="col-span-full">
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <LayoutTemplate className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">Tidak ada template ditemukan</h3>
                  <p className="text-muted-foreground mb-4 text-center">
                    Tidak ada template yang cocok dengan pencarian Anda
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Buat Template
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredTemplates.map((template) => (
                <Card key={template.id} className="hover:shadow-md transition-shadow overflow-hidden">
                  <div className="bg-muted/50 h-40 flex items-center justify-center">
                    <LayoutTemplate className="h-20 w-20 text-muted-foreground" />
                  </div>
                  <CardContent className="p-4">
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{template.name}</h4>
                        {getTemplateTypeBadge(template.type)}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Kategori: {template.category}
                      </p>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Clock className="h-3.5 w-3.5" />
                        <span>Diperbarui: {formatDate(template.lastModified)}</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm">
                        <Mail className="h-3.5 w-3.5 text-blue-600" />
                        <span>Digunakan: {template.usageCount}x</span>
                      </div>
                      <div className="flex gap-2 pt-2 mt-2 border-t">
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-2" />
                          Lihat
                        </Button>
                        <Button size="sm" variant="outline">
                          <PencilIcon className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                        <Button size="sm" variant="ghost">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
        
        {/* Segmen Audiens Tab */}
        <TabsContent value="segments" className="space-y-4 mt-6">
          <div className="flex justify-between mb-4">
            <h3 className="text-lg font-medium">Segmen Audiens</h3>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Buat Segmen Baru
            </Button>
          </div>
          
          {filteredSegments.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <Users className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada segmen ditemukan</h3>
                <p className="text-muted-foreground mb-4 text-center">
                  Tidak ada segmen audiens yang cocok dengan pencarian Anda
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Buat Segmen Baru
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredSegments.map((segment) => (
              <Card key={segment.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row justify-between gap-4">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-semibold text-lg">{segment.name}</span>
                        </div>
                        <div className="text-sm text-muted-foreground mb-1">
                          {segment.description}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium">
                          {formatAudience(segment.count)} pengguna
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-3 bg-muted/50 rounded-lg text-sm">
                      <p className="text-xs text-muted-foreground mb-1">Kriteria Segmentasi:</p>
                      <p>{segment.criteria}</p>
                    </div>
                    
                    <div className="flex gap-2 pt-2 border-t mt-2">
                      <Button size="sm" variant="outline">
                        <FileText className="h-4 w-4 mr-2" />
                        Detail
                      </Button>
                      <Button size="sm" variant="outline">
                        <PencilIcon className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                        <Trash className="h-4 w-4 mr-2" />
                        Hapus
                      </Button>
                      <Button size="sm" variant="outline">
                        <Mail className="h-4 w-4 mr-2" />
                        Buat Kampanye
                      </Button>
                      <Button size="sm" variant="ghost">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
} 