"use client"

import type React from "react"
import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON>Header } from "@/components/admin/ui/page-header"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useNotifications } from "@/components/providers/notifications-provider"
import { ArrowLeft, Save } from "lucide-react"
import { createStore } from "@/lib/services/stores"
import { supabase } from "@/lib/supabase"

export default function CreateStoreClient() {
  const router = useRouter()
  const { showNotification } = useNotifications()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    tenant: "",
    category: "",
    description: "",
    status: "active",
  })

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Dapatkan sesi pengguna
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError || !session) {
        throw new Error('Anda harus login untuk membuat toko')
      }

      // Format data sesuai yang dibutuhkan oleh createStore
      const storeData = {
        name: formData.name,
        email: `${formData.tenant}@example.com`, // Buat email dummy dari tenant
        status: formData.status,
        category: formData.category,
        location: 'Indonesia', // Default location
        products: 0,
        orders: 0,
        revenue: 0,
        rating: 0,
        join_date: new Date().toISOString(),
        last_active: new Date().toISOString(),
        owner: session.user.id, // Gunakan ID user yang login
        plan: 'Starter' // Default plan
      }

      const { data, error } = await createStore(storeData)
      
      if (error) {
        throw error
      }

      showNotification(`${formData.name} telah berhasil dibuat.`, "success", "Toko Berhasil Dibuat")
      router.push("/admin/dashboard/stores")
    } catch (error) {
      console.error('Gagal membuat toko:', error)
      showNotification(
        error instanceof Error ? error.message : 'Terjadi kesalahan saat membuat toko',
        "error",
        "Gagal Membuat Toko"
      )
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <PageHeader
        title="Buat Toko Baru"
        description="Tambahkan toko baru ke platform"
        breadcrumbs={[
          { title: "Dashboard", href: "/admin/dashboard" },
          { title: "Toko", href: "/admin/dashboard/stores" },
          { title: "Buat", href: "/admin/dashboard/stores/create" },
        ]}
        actions={
          <Button variant="outline" onClick={() => router.push("/admin/dashboard/stores")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali ke Daftar Toko
          </Button>
        }
      />

      <form onSubmit={handleSubmit}>
        <Card>
          <CardContent className="space-y-6 pt-6">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Toko</Label>
              <Input
                id="name"
                placeholder="Masukkan nama toko"
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tenant">Tenant</Label>
              <Select value={formData.tenant} onValueChange={(value) => handleChange("tenant", value)} required>
                <SelectTrigger id="tenant">
                  <SelectValue placeholder="Pilih tenant" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="acme">Acme Corporation</SelectItem>
                  <SelectItem value="startup">Startup Inc</SelectItem>
                  <SelectItem value="tech">Tech Solutions</SelectItem>
                  <SelectItem value="fashion">Fashion Boutique</SelectItem>
                  <SelectItem value="global">Global Traders</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Kategori</Label>
              <Select value={formData.category} onValueChange={(value) => handleChange("category", value)} required>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Pilih kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="clothing">Pakaian</SelectItem>
                  <SelectItem value="electronics">Elektronik</SelectItem>
                  <SelectItem value="home">Rumah & Kebun</SelectItem>
                  <SelectItem value="sports">Olahraga</SelectItem>
                  <SelectItem value="food">Makanan & Minuman</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Deskripsi</Label>
              <Textarea
                id="description"
                placeholder="Masukkan deskripsi toko"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleChange("status", value)}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Pilih status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Aktif</SelectItem>
                  <SelectItem value="inactive">Tidak Aktif</SelectItem>
                  <SelectItem value="pending">Menunggu</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-4 pt-6">
            <Button variant="outline" onClick={() => router.push("/admin/dashboard/stores")} disabled={isLoading}>
              Batal
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <span className="animate-spin mr-2">⊚</span>
                  Membuat...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Buat Toko
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </>
  )
}
