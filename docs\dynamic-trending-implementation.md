# Dynamic Trending Keywords & Popular Products Implementation

## Overview
Implementasi keyword trending dan produk populer yang dinamis berdasarkan frekuensi pencarian pengguna di halaman Sellzio.

## Features Implemented

### 1. Search Frequency Tracking
- **State Management**: `searchFrequency` state untuk menyimpan frekuensi pencarian
- **Local Storage**: Data disimpan di localStorage untuk persistensi
- **Auto Update**: Setiap kali pengguna melakukan pencarian, frekuensi akan bertambah

### 2. Dynamic Trending Keywords
- **Data Source**: Berdasarkan keyword yang paling sering dicari
- **Fallback**: Jika belum ada data, menggunakan default trending keywords
- **Display**: Menampilkan 5 keyword teratas berdasarkan frekuensi
- **Real-time**: Update otomatis saat ada pencarian baru

### 3. Dynamic Popular Products
- **Mapping System**: Keyword pencarian dipetakan ke produk yang relevan
- **Image Integration**: Setiap produk memiliki gambar yang sesuai
- **Fallback Products**: Default products jika belum ada data pencarian
- **Grid Layout**: Menampilkan 8 produk populer dalam grid

## Technical Implementation

### State Management
```typescript
const [searchFrequency, setSearchFrequency] = useState<{ [key: string]: number }>({})
```

### Core Functions

#### 1. updateSearchFrequency()
```typescript
const updateSearchFrequency = (keyword: string) => {
  setSearchFrequency(prev => {
    const updated = { ...prev }
    updated[keyword] = (updated[keyword] || 0) + 1
    localStorage.setItem('searchFrequency', JSON.stringify(updated))
    return updated
  })
}
```

#### 2. getTrendingKeywords()
```typescript
const getTrendingKeywords = () => {
  // Default trending keywords if no search frequency data
  const defaultTrending = [
    'Tas Sekolah', 'Tas Selempang', 'Handphone', 'Tas Mata', 'Tas'
  ]

  // If no search frequency data, return default
  if (Object.keys(searchFrequency).length === 0) {
    return defaultTrending
  }

  // Sort keywords by frequency and get top 5
  const sortedKeywords = Object.entries(searchFrequency)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([keyword]) => keyword)

  // Fill with defaults if needed
  while (sortedKeywords.length < 5) {
    const nextDefault = defaultTrending[sortedKeywords.length]
    if (nextDefault && !sortedKeywords.includes(nextDefault)) {
      sortedKeywords.push(nextDefault)
    } else {
      break
    }
  }

  return sortedKeywords
}
```

#### 3. getPopularProducts()
```typescript
const getPopularProducts = () => {
  // Default popular products
  const defaultProducts = [
    { name: 'Samsung Galaxy', image: 'https://images.unsplash.com/...' },
    { name: 'Sneakers Pria', image: 'https://images.unsplash.com/...' },
    // ... more products
  ]

  // Product mapping based on search keywords
  const productMapping = {
    'samsung galaxy': { name: 'Samsung Galaxy', image: '...' },
    'smartphone': { name: 'Samsung Galaxy', image: '...' },
    'sneakers': { name: 'Sneakers Pria', image: '...' },
    // ... more mappings
  }

  // Get products based on search frequency
  const popularProducts = []
  const sortedKeywords = Object.entries(searchFrequency)
    .sort(([, a], [, b]) => b - a)

  for (const [keyword] of sortedKeywords) {
    const lowerKeyword = keyword.toLowerCase()
    for (const [productKey, product] of Object.entries(productMapping)) {
      if (lowerKeyword.includes(productKey) && !popularProducts.some(p => p.name === product.name)) {
        popularProducts.push(product)
        if (popularProducts.length >= 8) break
      }
    }
    if (popularProducts.length >= 8) break
  }

  // Fill with defaults if needed
  while (popularProducts.length < 8) {
    const nextDefault = defaultProducts[popularProducts.length]
    if (nextDefault && !popularProducts.some(p => p.name === nextDefault.name)) {
      popularProducts.push(nextDefault)
    } else {
      break
    }
  }

  return popularProducts
}
```

### Integration Points

#### 1. Search Event Handlers
- `handlePredictionClick()`: Update frequency saat klik prediction
- `handleSuggestionClick()`: Update frequency saat klik suggestion

#### 2. UI Components
- **Trending Section**: Menggunakan `getTrendingKeywords()` untuk render dinamis
- **Popular Products**: Menggunakan `getPopularProducts()` untuk render dinamis

#### 3. Prediction System
- Trending keywords di prediction juga menggunakan data dinamis
- Bobot trending keywords tetap tinggi dalam sistem scoring

## Data Flow

1. **User Search**: Pengguna melakukan pencarian
2. **Frequency Update**: `updateSearchFrequency()` dipanggil
3. **Local Storage**: Data disimpan ke localStorage
4. **UI Update**: Trending keywords dan popular products ter-update otomatis
5. **Persistence**: Data tetap tersimpan saat reload halaman

## Benefits

### 1. Personalization
- Trending keywords berdasarkan perilaku pencarian aktual
- Popular products sesuai dengan preferensi pengguna

### 2. Performance
- Data disimpan lokal, tidak perlu API call
- Update real-time tanpa lag

### 3. User Experience
- Semakin sering digunakan, semakin akurat rekomendasi
- Fallback system memastikan selalu ada konten

### 4. Analytics Ready
- Data frequency bisa digunakan untuk analytics
- Easy to extend untuk fitur recommendation lainnya

## Future Enhancements

1. **Server-side Analytics**: Kirim data ke server untuk analytics global
2. **Category-based Trending**: Trending per kategori produk
3. **Time-based Decay**: Keyword lama berkurang bobotnya
4. **User Segmentation**: Trending berbeda per segment pengguna
5. **A/B Testing**: Test different trending algorithms

## Testing

Untuk test implementasi:
1. Buka halaman `/sellzio`
2. Lakukan beberapa pencarian berbeda
3. Lihat perubahan di trending keywords section
4. Lihat perubahan di popular products section
5. Refresh halaman untuk test persistence

## Files Modified

- `app/sellzio/page.tsx`: Main implementation
- `docs/dynamic-trending-implementation.md`: Documentation

## Dependencies

- React hooks (useState, useEffect)
- localStorage API
- Existing search system
