"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, Card<PERSON>itle, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  ArrowLeft, 
  Search, 
  Users, 
  UserPlus, 
  Plus,
  Trash,
  PencilIcon,
  ChevronDown,
  MoreHorizontal,
  Info,
  ShoppingBag,
  Wallet,
  Star,
  UserCheck,
  Clock,
  Filter,
  Download,
  Mail,
  Send,
  Tag
} from "lucide-react"
import Link from "next/link"

// Data dummy untuk grup pelanggan
const customerGroups = [
  {
    id: "group-001",
    name: "VIP Members",
    description: "Pelanggan dengan total belanja lebih dari Rp 5.000.000",
    memberCount: 24,
    createdAt: "2023-02-15T10:00:00",
    updatedAt: "2024-01-10T15:30:00",
    status: "active",
    conditions: [
      { type: "spending", operator: "greaterThan", value: 5000000 },
      { type: "orderCount", operator: "greaterThan", value: 10 }
    ],
    discount: 15,
    metrics: {
      totalSpent: 175250000,
      averageOrderValue: 850000,
      totalOrders: 210,
      conversionRate: 8.7,
      reviewCount: 145,
      averageRating: 4.8
    },
    tags: ["vip", "loyal", "high-value"]
  },
  {
    id: "group-002",
    name: "Pelanggan Baru",
    description: "Pelanggan yang bergabung dalam 30 hari terakhir",
    memberCount: 67,
    createdAt: "2023-06-20T14:30:00",
    updatedAt: "2024-01-12T11:20:00",
    status: "active",
    conditions: [
      { type: "joinDate", operator: "lessThan", value: "30 days" }
    ],
    discount: 10,
    metrics: {
      totalSpent: 18450000,
      averageOrderValue: 350000,
      totalOrders: 74,
      conversionRate: 4.2,
      reviewCount: 35,
      averageRating: 4.3
    },
    tags: ["new-customers", "welcome-program"]
  },
  {
    id: "group-003",
    name: "Fashion Enthusiasts",
    description: "Pelanggan yang sering membeli produk fashion",
    memberCount: 128,
    createdAt: "2023-05-15T09:00:00",
    updatedAt: "2024-01-08T16:45:00",
    status: "active",
    conditions: [
      { type: "category", operator: "equals", value: "Fashion" },
      { type: "purchaseFrequency", operator: "greaterThan", value: "5 times" }
    ],
    discount: 8,
    metrics: {
      totalSpent: 92450000,
      averageOrderValue: 450000,
      totalOrders: 315,
      conversionRate: 7.5,
      reviewCount: 210,
      averageRating: 4.6
    },
    tags: ["fashion", "trend-setters"]
  },
  {
    id: "group-004",
    name: "Pelanggan Tidak Aktif",
    description: "Pelanggan yang tidak melakukan pembelian dalam 90 hari terakhir",
    memberCount: 215,
    createdAt: "2023-08-10T11:30:00",
    updatedAt: "2024-01-14T13:40:00",
    status: "inactive",
    conditions: [
      { type: "lastPurchase", operator: "greaterThan", value: "90 days" }
    ],
    discount: 20,
    metrics: {
      totalSpent: 104500000,
      averageOrderValue: 420000,
      totalOrders: 248,
      conversionRate: 0,
      reviewCount: 95,
      averageRating: 3.9
    },
    tags: ["inactive", "reactivation-campaign"]
  },
  {
    id: "group-005",
    name: "Tech Enthusiasts",
    description: "Pelanggan yang sering membeli produk elektronik",
    memberCount: 85,
    createdAt: "2023-04-25T09:45:00",
    updatedAt: "2024-01-15T10:15:00",
    status: "active",
    conditions: [
      { type: "category", operator: "equals", value: "Electronics" },
      { type: "purchaseFrequency", operator: "greaterThan", value: "3 times" }
    ],
    discount: 5,
    metrics: {
      totalSpent: 153750000,
      averageOrderValue: 1250000,
      totalOrders: 123,
      conversionRate: 6.8,
      reviewCount: 78,
      averageRating: 4.5
    },
    tags: ["tech", "high-aov"]
  }
]

// Fungsi untuk generate badge status
function getStatusBadge(status: string) {
  switch (status) {
    case "active":
      return <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>
    case "inactive": 
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Inactive</Badge>
    case "draft": 
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Draft</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

// Fungsi untuk format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Fungsi untuk format persentase
function formatPercentage(value: number) {
  return `${value.toFixed(1)}%`
}

// Fungsi untuk format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric'
  })
}

// Fungsi untuk menghitung waktu yang telah berlalu
function timeAgo(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(months / 12)

  if (years > 0) return `${years} tahun lalu`
  if (months > 0) return `${months} bulan lalu`
  if (days > 0) return `${days} hari lalu`
  if (hours > 0) return `${hours} jam lalu`
  if (minutes > 0) return `${minutes} menit lalu`
  return `${seconds} detik lalu`
}

// Fungsi untuk menampilkan kondisi grup dalam format yang mudah dibaca
function formatCondition(condition: { type: string; operator: string; value: any }) {
  const typeLabels: Record<string, string> = {
    spending: "Total Belanja",
    orderCount: "Jumlah Order",
    joinDate: "Tanggal Bergabung",
    lastPurchase: "Pembelian Terakhir",
    category: "Kategori",
    purchaseFrequency: "Frekuensi Pembelian"
  }
  
  const operatorLabels: Record<string, string> = {
    equals: "sama dengan",
    greaterThan: "lebih dari",
    lessThan: "kurang dari"
  }
  
  const type = typeLabels[condition.type] || condition.type
  const operator = operatorLabels[condition.operator] || condition.operator
  
  let value = condition.value
  if (condition.type === "spending") {
    value = formatCurrency(value)
  }
  
  return `${type} ${operator} ${value}`
}

export default function CustomerGroupsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [expandedGroup, setExpandedGroup] = useState<string | null>(null)

  // Filter customer groups
  const filteredGroups = customerGroups.filter(group => {
    const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          group.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || group.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Menghitung statistik grup pelanggan
  const stats = {
    totalGroups: customerGroups.length,
    activeGroups: customerGroups.filter(g => g.status === "active").length,
    totalMembers: customerGroups.reduce((sum, g) => sum + g.memberCount, 0),
    averageDiscount: customerGroups.reduce((sum, g) => sum + g.discount, 0) / customerGroups.length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/customers">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Groups</h1>
            <p className="text-muted-foreground">
              Kelola segmentasi pelanggan dan grup khusus
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Smart Segments
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Buat Grup Baru
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Groups</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalGroups}</div>
            <p className="text-xs text-muted-foreground">
              Active: {stats.activeGroups}, Inactive: {stats.totalGroups - stats.activeGroups}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <UserCheck className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.totalMembers}</div>
            <p className="text-xs text-muted-foreground">
              Dalam semua grup (beberapa pelanggan mungkin terhitung lebih dari sekali)
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Discount</CardTitle>
            <Tag className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatPercentage(stats.averageDiscount)}</div>
            <p className="text-xs text-muted-foreground">
              Rata-rata diskon yang diberikan ke grup
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <Wallet className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {formatCurrency(customerGroups.reduce((sum, g) => sum + g.metrics.totalSpent, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              Dari semua anggota grup
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari nama grup atau deskripsi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="draft">Draft</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Customer Groups List */}
      <div className="space-y-4">
        {filteredGroups.map((group) => (
          <Card key={group.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex flex-col md:flex-row justify-between gap-4">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <CardTitle>{group.name}</CardTitle>
                    {getStatusBadge(group.status)}
                  </div>
                  <CardDescription>{group.description}</CardDescription>
                </div>
                <div className="flex flex-col items-end">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Users className="h-4 w-4" />
                    <span>{group.memberCount} anggota</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                    <Clock className="h-4 w-4" />
                    <span>Diupdate {timeAgo(group.updatedAt)}</span>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Group Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Total Revenue</p>
                    <p className="font-semibold text-green-600">{formatCurrency(group.metrics.totalSpent)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Avg. Order Value</p>
                    <p className="font-semibold">{formatCurrency(group.metrics.averageOrderValue)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Total Orders</p>
                    <div className="flex items-center gap-1">
                      <ShoppingBag className="h-4 w-4 text-blue-600" />
                      <p className="font-semibold">{group.metrics.totalOrders}</p>
                    </div>
                  </div>
                </div>

                {/* Membership Conditions */}
                <div>
                  <p className="text-sm font-medium mb-2">Syarat Keanggotaan</p>
                  <ul className="space-y-1 text-sm">
                    {group.conditions.map((condition, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <Info className="h-4 w-4 text-blue-600" />
                        {formatCondition(condition)}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Tags */}
                {group.tags.length > 0 && (
                  <div>
                    <p className="text-sm font-medium mb-1">Tags</p>
                    <div className="flex flex-wrap gap-1">
                      {group.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs bg-blue-50 text-blue-800">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Discount */}
                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm font-medium text-green-800 mb-1">Discount</p>
                  <p className="text-sm font-semibold text-green-700">{group.discount}% untuk semua anggota grup</p>
                </div>

                {/* Additional metrics if expanded */}
                {expandedGroup === group.id && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Conversion Rate</p>
                      <p className="font-semibold">{formatPercentage(group.metrics.conversionRate)}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Reviews</p>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-600" />
                        <p className="font-semibold">{group.metrics.reviewCount} ({group.metrics.averageRating})</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Avg. per Member</p>
                      <p className="font-semibold">{formatCurrency(group.metrics.totalSpent / group.memberCount)}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between border-t pt-6">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setExpandedGroup(expandedGroup === group.id ? null : group.id)}
              >
                {expandedGroup === group.id ? "Sembunyikan Detail" : "Lihat Detail Lebih Lanjut"}
                <ChevronDown className={`h-4 w-4 ml-1 ${expandedGroup === group.id ? 'rotate-180' : ''} transition-transform`} />
              </Button>
              <div className="flex gap-2">
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Members
                </Button>
                <Button size="sm" variant="outline">
                  <Mail className="h-4 w-4 mr-2" />
                  Email Group
                </Button>
                <Button size="sm" variant="outline">
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </Button>
                <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                  <Trash className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </CardFooter>
          </Card>
        ))}

        {filteredGroups.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Tidak ada grup ditemukan</h3>
              <p className="text-muted-foreground mb-4">
                Tidak ada grup pelanggan yang cocok dengan filter Anda
              </p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Buat Grup Baru
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
} 