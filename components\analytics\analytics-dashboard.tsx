"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { AnalyticsMetricCard } from "./analytics-metric-card"
import { AnalyticsTable } from "./analytics-table"
import { AnalyticsTimeSeriesChart } from "./analytics-time-series-chart"
import { AnalyticsPieChart } from "./analytics-pie-chart"
import { DateRangePicker } from "./date-range-picker"

interface AnalyticsDashboardProps {
  role: "admin" | "tenant" | "store" | "buyer"
  title: string
  description: string
}

export function AnalyticsDashboard({ role, title, description }: AnalyticsDashboardProps) {
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date(),
  })

  // Dummy data untuk metrik
  const metrics = [
    { title: "Total Revenue", value: "$128,430", change: "+12.3%", trend: "up" },
    { title: "Active Users", value: "12,342", change: "+8.1%", trend: "up" },
    { title: "Conversion Rate", value: "3.2%", change: "-0.4%", trend: "down" },
    { title: "Avg. Order Value", value: "$86.12", change: "+2.5%", trend: "up" },
  ]

  // Dummy data untuk tabel
  const tableData = {
    admin: {
      title: "Top Performing Tenants",
      columns: ["Tenant", "Revenue", "Users", "Stores", "Growth"],
      data: [
        { id: 1, name: "Fashion Marketplace", revenue: "$42,500", users: "4,230", stores: "128", growth: "+18.2%" },
        { id: 2, name: "Tech Gadgets Hub", revenue: "$38,120", users: "3,845", stores: "95", growth: "+12.5%" },
        { id: 3, name: "Home Decor & Living", revenue: "$29,840", users: "2,950", stores: "87", growth: "+9.8%" },
        { id: 4, name: "Sports Equipment", revenue: "$24,680", users: "2,430", stores: "64", growth: "+7.3%" },
        { id: 5, name: "Beauty & Wellness", revenue: "$19,750", users: "1,980", stores: "52", growth: "+5.6%" },
      ],
    },
    tenant: {
      title: "Top Performing Stores",
      columns: ["Store", "Revenue", "Products", "Orders", "Growth"],
      data: [
        { id: 1, name: "Premium Apparel", revenue: "$12,840", products: "245", orders: "428", growth: "+15.7%" },
        { id: 2, name: "Footwear Collection", revenue: "$9,720", products: "182", orders: "356", growth: "+12.3%" },
        { id: 3, name: "Accessories Boutique", revenue: "$7,950", products: "164", orders: "287", growth: "+8.9%" },
        { id: 4, name: "Seasonal Fashion", revenue: "$6,430", products: "128", orders: "215", growth: "+6.2%" },
        { id: 5, name: "Designer Outlet", revenue: "$5,280", products: "97", orders: "178", growth: "+4.8%" },
      ],
    },
    store: {
      title: "Top Selling Products",
      columns: ["Product", "Revenue", "Units Sold", "Avg. Rating", "Growth"],
      data: [
        { id: 1, name: "Premium Leather Jacket", revenue: "$4,850", units: "97", rating: "4.8", growth: "+14.2%" },
        { id: 2, name: "Designer Sunglasses", revenue: "$3,720", units: "124", rating: "4.6", growth: "+11.8%" },
        { id: 3, name: "Casual Sneakers", revenue: "$2,940", units: "147", rating: "4.5", growth: "+9.3%" },
        { id: 4, name: "Slim Fit Jeans", revenue: "$2,380", units: "158", rating: "4.4", growth: "+7.6%" },
        { id: 5, name: "Graphic T-Shirt", revenue: "$1,950", units: "195", rating: "4.3", growth: "+5.2%" },
      ],
    },
    buyer: {
      title: "Recent Purchases",
      columns: ["Order ID", "Date", "Items", "Total", "Status"],
      data: [
        { id: "ORD-7842", name: "2023-05-12", items: "3", total: "$128.50", status: "Delivered" },
        { id: "ORD-7836", name: "2023-05-08", items: "1", total: "$64.99", status: "Shipped" },
        { id: "ORD-7821", name: "2023-05-03", items: "2", total: "$97.25", status: "Processing" },
        { id: "ORD-7814", name: "2023-04-28", items: "4", total: "$156.80", status: "Delivered" },
        { id: "ORD-7809", name: "2023-04-22", items: "2", total: "$82.45", status: "Delivered" },
      ],
    },
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          <p className="text-muted-foreground">{description}</p>
        </div>
        <DateRangePicker value={dateRange} onChange={setDateRange} />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric, index) => (
          <AnalyticsMetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            change={metric.change}
            trend={metric.trend as "up" | "down"}
          />
        ))}
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="traffic">Traffic</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="lg:col-span-4">
              <CardHeader>
                <CardTitle>Revenue Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <AnalyticsTimeSeriesChart />
              </CardContent>
            </Card>
            <Card className="lg:col-span-3">
              <CardHeader>
                <CardTitle>Revenue Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <AnalyticsPieChart />
              </CardContent>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>{tableData[role].title}</CardTitle>
              <CardDescription>Overview of top performing entities in your platform.</CardDescription>
            </CardHeader>
            <CardContent>
              <AnalyticsTable columns={tableData[role].columns} data={tableData[role].data} />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="sales" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sales Analytics</CardTitle>
              <CardDescription>Detailed breakdown of sales performance.</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <div className="flex h-full items-center justify-center text-muted-foreground">
                Sales analytics content will be displayed here.
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Analytics</CardTitle>
              <CardDescription>Detailed breakdown of user activity and engagement.</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <div className="flex h-full items-center justify-center text-muted-foreground">
                User analytics content will be displayed here.
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="traffic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Traffic Analytics</CardTitle>
              <CardDescription>Detailed breakdown of traffic sources and patterns.</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <div className="flex h-full items-center justify-center text-muted-foreground">
                Traffic analytics content will be displayed here.
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
