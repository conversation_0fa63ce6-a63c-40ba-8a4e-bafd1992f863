"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Paintbrush, Plus, Trash2, Check, Refresh<PERSON><PERSON>, Co<PERSON>, Eye, ImageIcon, Layout, Type } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { tenantThemesAPI } from "@/lib/api/tenant-themes"
import type { TenantTheme, CreateTenantThemeDTO } from "@/lib/models/tenant-theme"
import { cn } from "@/lib/utils"

export function ThemeManagement() {
  const router = useRouter()
  const [themes, setThemes] = useState<any[]>([])
  const [activeTheme, setActiveTheme] = useState<TenantTheme | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [activeTab, setActiveTab] = useState("colors")
  const [previewMode, setPreviewMode] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Form state for new theme
  const [newTheme, setNewTheme] = useState<CreateTenantThemeDTO>({
    tenantId: "1", // Simulasi tenant ID
    name: "New Theme",
    colors: {
      primary: "#0070f3",
      secondary: "#7928ca",
      accent: "#f5a623",
      background: "#ffffff",
      foreground: "#000000",
      muted: "#f1f5f9",
      mutedForeground: "#64748b",
      border: "#e2e8f0",
      input: "#e2e8f0",
      card: "#ffffff",
      cardForeground: "#000000",
      destructive: "#ef4444",
      destructiveForeground: "#ffffff",
    },
    fonts: {
      heading: "Inter",
      body: "Inter",
    },
    logo: {
      light: "",
      dark: "",
      favicon: "",
    },
    layout: {
      header: "default",
      footer: "default",
      sidebar: "default",
    },
  })

  // Simulasi tenant ID (dalam implementasi sebenarnya, ini akan diambil dari context atau API)
  const tenantId = "1"

  const fetchThemes = async () => {
    setLoading(true)
    try {
      const data = await tenantThemesAPI.getByTenantId(tenantId)
      setThemes(data)

      // Get active theme
      const active = data.find((theme) => theme.isActive) || data[0] || null
      setActiveTheme(active)

      setError(null)
    } catch (err) {
      console.error("Error fetching themes:", err)
      setError("Failed to load themes. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Simulasi pengambilan data tema
    setTimeout(() => {
      const mockThemes = [
        {
          id: 1,
          name: "Modern Boutique",
          description: "A clean, modern theme perfect for fashion and boutique stores",
          thumbnail: "/modern-boutique-store.png",
          category: "Fashion",
          popularity: "High",
          status: "active",
          customizable: true,
        },
        {
          id: 2,
          name: "Tech Store",
          description: "Sleek and minimalist theme designed for electronics and tech products",
          thumbnail: "/tech-store-template.png",
          category: "Electronics",
          popularity: "Medium",
          status: "active",
          customizable: true,
        },
        {
          id: 3,
          name: "Food & Grocery",
          description: "Vibrant and appetizing theme for food and grocery stores",
          thumbnail: "/food-grocery-store-template.png",
          category: "Food",
          popularity: "High",
          status: "active",
          customizable: true,
        },
        {
          id: 4,
          name: "Handmade Crafts",
          description: "Rustic and artistic theme for handmade and craft stores",
          thumbnail: "/handmade-crafts-store.png",
          category: "Crafts",
          popularity: "Medium",
          status: "active",
          customizable: true,
        },
        {
          id: 5,
          name: "Minimalist",
          description: "Ultra-clean minimalist design that puts products in focus",
          thumbnail: "/minimalist-store-template.png",
          category: "General",
          popularity: "High",
          status: "active",
          customizable: true,
        },
        {
          id: 6,
          name: "Vintage Shop",
          description: "Retro-inspired theme with a nostalgic feel",
          thumbnail: "/vintage-shop-template-retro.png",
          category: "Vintage",
          popularity: "Low",
          status: "active",
          customizable: true,
        },
        {
          id: 7,
          name: "Velozio",
          description: "Modern marketplace theme with vibrant accents and optimized for conversion",
          thumbnail: "/assorted-shoes.png",
          category: "Marketplace",
          popularity: "High",
          status: "active",
          customizable: true,
        },
      ]

      setThemes(mockThemes)
      setIsLoading(false)
    }, 1000)
  }, [])

  const handleCreateTheme = async () => {
    setIsCreating(true)
    try {
      const theme = await tenantThemesAPI.create(newTheme)
      if (theme) {
        setThemes([...themes, theme])
        setIsDialogOpen(false)
        // Reset form
        setNewTheme({
          ...newTheme,
          name: "New Theme",
        })
      }
    } catch (err) {
      console.error("Error creating theme:", err)
      setError("Failed to create theme. Please try again.")
    } finally {
      setIsCreating(false)
    }
  }

  const handleDeleteTheme = async (id: string) => {
    try {
      const success = await tenantThemesAPI.delete(id)
      if (success) {
        setThemes(themes.filter((theme) => theme.id !== id))
        if (activeTheme?.id === id) {
          setActiveTheme(themes.find((theme) => theme.id !== id) || null)
        }
      }
    } catch (err) {
      console.error("Error deleting theme:", err)
      setError("Failed to delete theme. Please try again.")
    }
  }

  const handleActivateTheme = async (id: string) => {
    try {
      const updatedTheme = await tenantThemesAPI.activate(id)
      if (updatedTheme) {
        setThemes(
          themes.map((theme) => ({
            ...theme,
            isActive: theme.id === id,
          })),
        )
        setActiveTheme(updatedTheme)
      }
    } catch (err) {
      console.error("Error activating theme:", err)
      setError("Failed to activate theme. Please try again.")
    }
  }

  const handleUpdateTheme = async (id: string, data: any) => {
    try {
      const updatedTheme = await tenantThemesAPI.update(id, data)
      if (updatedTheme) {
        setThemes(themes.map((theme) => (theme.id === id ? updatedTheme : theme)))
        if (activeTheme?.id === id) {
          setActiveTheme(updatedTheme)
        }
      }
    } catch (err) {
      console.error("Error updating theme:", err)
      setError("Failed to update theme. Please try again.")
    }
  }

  const handlePreviewTheme = (id: string) => {
    router.push(`/tenant/dashboard/themes/preview/${id}`)
  }

  // Simulasi data tema untuk demo
  // useEffect(() => {
  //   if (themes.length === 0 && !loading) {
  //     const defaultTheme: TenantTheme = {
  //       id: "1",
  //       tenantId: "1",
  //       name: "Default Theme",
  //       isActive: true,
  //       colors: {
  //         primary: "#0070f3",
  //         secondary: "#7928ca",
  //         accent: "#f5a623",
  //         background: "#ffffff",
  //         foreground: "#000000",
  //         muted: "#f1f5f9",
  //         mutedForeground: "#64748b",
  //         border: "#e2e8f0",
  //         input: "#e2e8f0",
  //         card: "#ffffff",
  //         cardForeground: "#000000",
  //         destructive: "#ef4444",
  //         destructiveForeground: "#ffffff",
  //       },
  //       fonts: {
  //         heading: "Inter",
  //         body: "Inter",
  //       },
  //       logo: {
  //         light: "/your-logo.png",
  //         dark: "/placeholder.svg?key=ll3l1",
  //         favicon: "/favicon.ico",
  //       },
  //       layout: {
  //         header: "default",
  //         footer: "default",
  //         sidebar: "default",
  //       },
  //       createdAt: new Date().toISOString(),
  //       updatedAt: new Date().toISOString(),
  //     }

  //     const darkTheme: TenantTheme = {
  //       id: "2",
  //       tenantId: "1",
  //       name: "Dark Theme",
  //       isActive: false,
  //       colors: {
  //         primary: "#3b82f6",
  //         secondary: "#8b5cf6",
  //         accent: "#f59e0b",
  //         background: "#111827",
  //         foreground: "#ffffff",
  //         muted: "#1f2937",
  //         mutedForeground: "#9ca3af",
  //         border: "#374151",
  //         input: "#374151",
  //         card: "#1f2937",
  //         cardForeground: "#ffffff",
  //         destructive: "#ef4444",
  //         destructiveForeground: "#ffffff",
  //       },
  //       fonts: {
  //         heading: "Inter",
  //         body: "Inter",
  //       },
  //       logo: {
  //         light: "/your-logo.png",
  //         dark: "/placeholder.svg?key=1bdnc",
  //         favicon: "/favicon.ico",
  //       },
  //       layout: {
  //         header: "default",
  //         footer: "default",
  //         sidebar: "default",
  //       },
  //       createdAt: new Date().toISOString(),
  //       updatedAt: new Date().toISOString(),
  //     }

  //     // Tambahkan tema Velozio
  //     const velozioThemeComplete: TenantTheme = {
  //       id: "3",
  //       tenantId: "1",
  //       name: "Velozio",
  //       isActive: false,
  //       colors: velozioTheme.colors as TenantTheme["colors"],
  //       fonts: velozioTheme.fonts as TenantTheme["fonts"],
  //       logo: {
  //         light: "/your-logo.png",
  //         dark: "/placeholder.svg?key=velozio",
  //         favicon: "/favicon.ico",
  //       },
  //       layout: velozioTheme.layout as TenantTheme["layout"],
  //       customCSS: velozioTheme.customCSS,
  //       createdAt: new Date().toISOString(),
  //       updatedAt: new Date().toISOString(),
  //     }

  //     setThemes([defaultTheme, darkTheme, velozioThemeComplete])
  //     setActiveTheme(defaultTheme)
  //   }
  // }, [themes, loading])

  // Render theme preview
  const renderThemePreview = (theme: TenantTheme) => {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-2">
          <div
            className="h-20 rounded-md flex items-center justify-center text-white font-medium"
            style={{ backgroundColor: theme.colors?.primary }}
          >
            Primary
          </div>
          <div
            className="h-20 rounded-md flex items-center justify-center text-white font-medium"
            style={{ backgroundColor: theme.colors?.secondary }}
          >
            Secondary
          </div>
        </div>

        <div className="grid grid-cols-3 gap-2">
          <div
            className="h-12 rounded-md flex items-center justify-center text-white font-medium"
            style={{ backgroundColor: theme.colors?.accent }}
          >
            Accent
          </div>
          <div
            className="h-12 rounded-md flex items-center justify-center font-medium"
            style={{
              backgroundColor: theme.colors?.muted,
              color: theme.colors?.mutedForeground,
            }}
          >
            Muted
          </div>
          <div
            className="h-12 rounded-md flex items-center justify-center text-white font-medium"
            style={{ backgroundColor: theme.colors?.destructive }}
          >
            Destructive
          </div>
        </div>

        <div
          className="p-4 rounded-md border"
          style={{
            backgroundColor: theme.colors?.background,
            color: theme.colors?.foreground,
            borderColor: theme.colors?.border,
          }}
        >
          <h3 className="text-lg font-semibold mb-2" style={{ fontFamily: theme.fonts?.heading }}>
            Sample Heading
          </h3>
          <p style={{ fontFamily: theme.fonts?.body }}>This is a sample text to preview the theme.</p>
          <div
            className="mt-2 p-3 rounded-md"
            style={{
              backgroundColor: theme.colors?.card,
              color: theme.colors?.cardForeground,
            }}
          >
            Card Component
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Theme Management</h1>
          <p className="text-muted-foreground">Customize the look and feel of your marketplace</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setPreviewMode(!previewMode)}>
            {previewMode ? (
              <>
                <Paintbrush className="mr-2 h-4 w-4" />
                Edit Mode
              </>
            ) : (
              <>
                <Eye className="mr-2 h-4 w-4" />
                Preview Mode
              </>
            )}
          </Button>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Theme
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Theme</DialogTitle>
                <DialogDescription>
                  Create a new theme for your marketplace. You can customize colors, fonts, and layout.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="theme-name">Theme Name</Label>
                  <Input
                    id="theme-name"
                    value={newTheme.name}
                    onChange={(e) => setNewTheme({ ...newTheme, name: e.target.value })}
                  />
                </div>

                <Tabs defaultValue="colors" value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid grid-cols-4 mb-4">
                    <TabsTrigger value="colors">
                      <Paintbrush className="mr-2 h-4 w-4" />
                      Colors
                    </TabsTrigger>
                    <TabsTrigger value="typography">
                      <Type className="mr-2 h-4 w-4" />
                      Typography
                    </TabsTrigger>
                    <TabsTrigger value="layout">
                      <Layout className="mr-2 h-4 w-4" />
                      Layout
                    </TabsTrigger>
                    <TabsTrigger value="assets">
                      <ImageIcon className="mr-2 h-4 w-4" />
                      Assets
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="colors" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="primary-color">Primary Color</Label>
                        <div className="flex gap-2">
                          <Input
                            id="primary-color"
                            value={newTheme.colors.primary}
                            onChange={(e) =>
                              setNewTheme({
                                ...newTheme,
                                colors: { ...newTheme.colors, primary: e.target.value },
                              })
                            }
                          />
                          <input
                            type="color"
                            value={newTheme.colors.primary}
                            onChange={(e) =>
                              setNewTheme({
                                ...newTheme,
                                colors: { ...newTheme.colors, primary: e.target.value },
                              })
                            }
                            className="w-10 h-10 p-0 border rounded"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="secondary-color">Secondary Color</Label>
                        <div className="flex gap-2">
                          <Input
                            id="secondary-color"
                            value={newTheme.colors.secondary}
                            onChange={(e) =>
                              setNewTheme({
                                ...newTheme,
                                colors: { ...newTheme.colors, secondary: e.target.value },
                              })
                            }
                          />
                          <input
                            type="color"
                            value={newTheme.colors.secondary}
                            onChange={(e) =>
                              setNewTheme({
                                ...newTheme,
                                colors: { ...newTheme.colors, secondary: e.target.value },
                              })
                            }
                            className="w-10 h-10 p-0 border rounded"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="accent-color">Accent Color</Label>
                        <div className="flex gap-2">
                          <Input
                            id="accent-color"
                            value={newTheme.colors.accent}
                            onChange={(e) =>
                              setNewTheme({
                                ...newTheme,
                                colors: { ...newTheme.colors, accent: e.target.value },
                              })
                            }
                          />
                          <input
                            type="color"
                            value={newTheme.colors.accent}
                            onChange={(e) =>
                              setNewTheme({
                                ...newTheme,
                                colors: { ...newTheme.colors, accent: e.target.value },
                              })
                            }
                            className="w-10 h-10 p-0 border rounded"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="background-color">Background Color</Label>
                        <div className="flex gap-2">
                          <Input
                            id="background-color"
                            value={newTheme.colors.background}
                            onChange={(e) =>
                              setNewTheme({
                                ...newTheme,
                                colors: { ...newTheme.colors, background: e.target.value },
                              })
                            }
                          />
                          <input
                            type="color"
                            value={newTheme.colors.background}
                            onChange={(e) =>
                              setNewTheme({
                                ...newTheme,
                                colors: { ...newTheme.colors, background: e.target.value },
                              })
                            }
                            className="w-10 h-10 p-0 border rounded"
                          />
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="typography" className="space-y-4">
                    <div className="grid gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="heading-font">Heading Font</Label>
                        <Select
                          value={newTheme.fonts?.heading}
                          onValueChange={(value) =>
                            setNewTheme({
                              ...newTheme,
                              fonts: { ...newTheme.fonts!, heading: value },
                            })
                          }
                        >
                          <SelectTrigger id="heading-font">
                            <SelectValue placeholder="Select font" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Inter">Inter</SelectItem>
                            <SelectItem value="Roboto">Roboto</SelectItem>
                            <SelectItem value="Poppins">Poppins</SelectItem>
                            <SelectItem value="Montserrat">Montserrat</SelectItem>
                            <SelectItem value="Open Sans">Open Sans</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="body-font">Body Font</Label>
                        <Select
                          value={newTheme.fonts?.body}
                          onValueChange={(value) =>
                            setNewTheme({
                              ...newTheme,
                              fonts: { ...newTheme.fonts!, body: value },
                            })
                          }
                        >
                          <SelectTrigger id="body-font">
                            <SelectValue placeholder="Select font" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Inter">Inter</SelectItem>
                            <SelectItem value="Roboto">Roboto</SelectItem>
                            <SelectItem value="Poppins">Poppins</SelectItem>
                            <SelectItem value="Montserrat">Montserrat</SelectItem>
                            <SelectItem value="Open Sans">Open Sans</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="layout" className="space-y-4">
                    <div className="grid gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="header-layout">Header Layout</Label>
                        <Select
                          value={newTheme.layout?.header}
                          onValueChange={(value: any) =>
                            setNewTheme({
                              ...newTheme,
                              layout: { ...newTheme.layout!, header: value },
                            })
                          }
                        >
                          <SelectTrigger id="header-layout">
                            <SelectValue placeholder="Select layout" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">Default</SelectItem>
                            <SelectItem value="centered">Centered</SelectItem>
                            <SelectItem value="minimal">Minimal</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="footer-layout">Footer Layout</Label>
                        <Select
                          value={newTheme.layout?.footer}
                          onValueChange={(value: any) =>
                            setNewTheme({
                              ...newTheme,
                              layout: { ...newTheme.layout!, footer: value },
                            })
                          }
                        >
                          <SelectTrigger id="footer-layout">
                            <SelectValue placeholder="Select layout" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">Default</SelectItem>
                            <SelectItem value="minimal">Minimal</SelectItem>
                            <SelectItem value="none">None</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="sidebar-layout">Sidebar Layout</Label>
                        <Select
                          value={newTheme.layout?.sidebar}
                          onValueChange={(value: any) =>
                            setNewTheme({
                              ...newTheme,
                              layout: { ...newTheme.layout!, sidebar: value },
                            })
                          }
                        >
                          <SelectTrigger id="sidebar-layout">
                            <SelectValue placeholder="Select layout" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">Default</SelectItem>
                            <SelectItem value="compact">Compact</SelectItem>
                            <SelectItem value="none">None</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="assets" className="space-y-4">
                    <div className="grid gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="logo-light">Logo (Light)</Label>
                        <Input
                          id="logo-light"
                          placeholder="URL to light logo"
                          value={newTheme.logo?.light}
                          onChange={(e) =>
                            setNewTheme({
                              ...newTheme,
                              logo: { ...newTheme.logo!, light: e.target.value },
                            })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="logo-dark">Logo (Dark)</Label>
                        <Input
                          id="logo-dark"
                          placeholder="URL to dark logo"
                          value={newTheme.logo?.dark}
                          onChange={(e) =>
                            setNewTheme({
                              ...newTheme,
                              logo: { ...newTheme.logo!, dark: e.target.value },
                            })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="favicon">Favicon</Label>
                        <Input
                          id="favicon"
                          placeholder="URL to favicon"
                          value={newTheme.logo?.favicon}
                          onChange={(e) =>
                            setNewTheme({
                              ...newTheme,
                              logo: { ...newTheme.logo!, favicon: e.target.value },
                            })
                          }
                        />
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateTheme} disabled={isCreating}>
                  {isCreating ? "Creating..." : "Create Theme"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-6">
          <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {themes.map((theme) => (
            <Card key={theme.id} className={cn("overflow-hidden", theme.isActive && "border-primary")}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle>{theme.name}</CardTitle>
                  {theme.isActive && (
                    <div className="flex items-center text-xs font-medium text-primary">
                      <Check className="mr-1 h-4 w-4" />
                      Active
                    </div>
                  )}
                </div>
                <CardDescription>Last updated: {new Date(theme.updatedAt).toLocaleDateString()}</CardDescription>
              </CardHeader>
              <CardContent>
                {previewMode ? (
                  renderThemePreview(theme)
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="space-y-1">
                        <Label className="text-xs">Primary</Label>
                        <div className="flex items-center gap-2">
                          <div
                            className="h-6 w-6 rounded-full border"
                            style={{ backgroundColor: theme.colors?.primary }}
                          />
                          <span className="text-xs">{theme.colors?.primary}</span>
                        </div>
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Secondary</Label>
                        <div className="flex items-center gap-2">
                          <div
                            className="h-6 w-6 rounded-full border"
                            style={{ backgroundColor: theme.colors?.secondary }}
                          />
                          <span className="text-xs">{theme.colors?.secondary}</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-1">
                      <Label className="text-xs">Fonts</Label>
                      <div className="text-sm">
                        Heading: <span className="font-medium">{theme.fonts?.heading}</span>, Body:{" "}
                        <span className="font-medium">{theme.fonts?.body}</span>
                      </div>
                    </div>

                    <div className="space-y-1">
                      <Label className="text-xs">Layout</Label>
                      <div className="text-sm">
                        Header: <span className="font-medium">{theme.layout?.header}</span>, Footer:{" "}
                        <span className="font-medium">{theme.layout?.footer}</span>, Sidebar:{" "}
                        <span className="font-medium">{theme.layout?.sidebar}</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-between border-t px-6 py-4">
                {!theme.isActive ? (
                  <Button variant="default" onClick={() => handleActivateTheme(theme.id)}>
                    Activate
                  </Button>
                ) : (
                  <Button variant="outline" disabled>
                    Active
                  </Button>
                )}

                <Button variant="outline" onClick={() => handlePreviewTheme(theme.id)}>
                  <Eye className="mr-2 h-4 w-4" />
                  Preview
                </Button>

                <div className="flex gap-2">
                  <Button variant="outline" size="icon">
                    <Copy className="h-4 w-4" />
                  </Button>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" size="icon">
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Theme</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this theme? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDeleteTheme(theme.id)}
                          className="bg-red-500 hover:bg-red-600"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Custom CSS</CardTitle>
          <CardDescription>Add custom CSS to further customize your marketplace.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Textarea
              placeholder="/* Add your custom CSS here */"
              className="font-mono h-40"
              value={activeTheme?.customCSS || ""}
              onChange={(e) => {
                if (activeTheme) {
                  handleUpdateTheme(activeTheme.id, { customCSS: e.target.value })
                }
              }}
            />

            <div className="flex items-center space-x-2">
              <Switch id="css-enabled" checked={true} />
              <Label htmlFor="css-enabled">Enable custom CSS</Label>
            </div>
          </div>
        </CardContent>
        <CardFooter className="border-t px-6 py-4">
          <Button
            onClick={() => {
              if (activeTheme) {
                handleUpdateTheme(activeTheme.id, { customCSS: activeTheme.customCSS })
              }
            }}
          >
            Save CSS
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
