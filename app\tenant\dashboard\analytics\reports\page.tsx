"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  FileText,
  BarChart3,
  LineChart,
  Calendar,
  Download,
  Filter,
  CalendarDays,
  Clock,
  Star,
  CheckCircle,
  PlusCircle,
  Table,
  Repeat,
  Settings,
  FileDown
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"

// Data dummy untuk laporan terjadwal
const scheduledReports = [
  {
    id: "report-001",
    name: "<PERSON><PERSON><PERSON> Penjualan Bulanan",
    schedule: "Setiap 1 bulan",
    lastRun: "2024-05-01T08:00:00",
    nextRun: "2024-06-01T08:00:00",
    recipients: ["<EMAIL>", "<EMAIL>"],
    format: "PDF",
    status: "active"
  },
  {
    id: "report-002",
    name: "Analisis Pelanggan Mingguan",
    schedule: "Setiap 1 minggu",
    lastRun: "2024-05-15T09:00:00",
    nextRun: "2024-05-22T09:00:00",
    recipients: ["<EMAIL>"],
    format: "XLSX",
    status: "active"
  },
  {
    id: "report-003",
    name: "Performa Produk Harian",
    schedule: "Setiap 1 hari",
    lastRun: "2024-05-18T07:00:00",
    nextRun: "2024-05-19T07:00:00",
    recipients: ["<EMAIL>", "<EMAIL>"],
    format: "CSV",
    status: "active"
  },
  {
    id: "report-004",
    name: "Analisis Kampanye Marketing",
    schedule: "Setiap 2 minggu",
    lastRun: "2024-05-10T10:00:00",
    nextRun: "2024-05-24T10:00:00",
    recipients: ["<EMAIL>", "<EMAIL>"],
    format: "PDF",
    status: "paused"
  }
]

// Data dummy untuk laporan yang tersedia
const availableReports = [
  {
    id: "template-001",
    name: "Ringkasan Penjualan",
    description: "Laporan pendapatan, jumlah pesanan, dan metrik penjualan lainnya",
    category: "Penjualan",
    formats: ["PDF", "XLSX", "CSV"],
    popularity: 4.8
  },
  {
    id: "template-002",
    name: "Analisis Pelanggan",
    description: "Informasi tentang pelanggan baru, churn rate, dan segmentasi",
    category: "Pelanggan",
    formats: ["PDF", "XLSX"],
    popularity: 4.6
  },
  {
    id: "template-003",
    name: "Performa Produk",
    description: "Analisis produk terlaris, tren penjualan, dan profitabilitas",
    category: "Produk",
    formats: ["PDF", "XLSX", "CSV"],
    popularity: 4.7
  },
  {
    id: "template-004",
    name: "Laporan Inventaris",
    description: "Status stok, perputaran inventaris, dan prediksi ketersediaan",
    category: "Inventaris",
    formats: ["PDF", "XLSX"],
    popularity: 4.3
  },
  {
    id: "template-005",
    name: "Analisis Marketing",
    description: "Performa kampanye, ROI, dan metrik akuisisi pelanggan",
    category: "Marketing",
    formats: ["PDF", "XLSX", "PPT"],
    popularity: 4.5
  },
  {
    id: "template-006",
    name: "Laporan Keuangan",
    description: "Ringkasan pendapatan, pengeluaran, dan proyeksi keuangan",
    category: "Keuangan",
    formats: ["PDF", "XLSX"],
    popularity: 4.9
  }
]

// Data dummy untuk laporan terbaru
const recentReports = [
  {
    id: "recent-001",
    name: "Laporan Penjualan Mei 2024",
    generatedOn: "2024-05-18T14:35:00",
    format: "PDF",
    size: "2.4 MB",
    generatedBy: "<EMAIL>"
  },
  {
    id: "recent-002",
    name: "Analisis Pelanggan Q2 2024",
    generatedOn: "2024-05-17T10:15:00",
    format: "XLSX",
    size: "1.8 MB",
    generatedBy: "<EMAIL>"
  },
  {
    id: "recent-003",
    name: "Performa Produk - Minggu ke-20",
    generatedOn: "2024-05-16T16:20:00",
    format: "PDF",
    size: "3.1 MB",
    generatedBy: "<EMAIL>"
  },
  {
    id: "recent-004",
    name: "Laporan Keuangan April 2024",
    generatedOn: "2024-05-15T09:45:00",
    format: "XLSX",
    size: "2.2 MB",
    generatedBy: "<EMAIL>"
  },
  {
    id: "recent-005",
    name: "Analisis Marketing Q1 2024",
    generatedOn: "2024-05-14T11:30:00",
    format: "PDF",
    size: "4.5 MB",
    generatedBy: "<EMAIL>"
  }
]

// Format tanggal
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Format waktu
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format tanggal dan waktu
function formatDateTime(dateString: string) {
  return `${formatDate(dateString)}, ${formatTime(dateString)}`
}

export default function ReportsPage() {
  const [selectedTab, setSelectedTab] = useState("scheduled")
  const [searchQuery, setSearchQuery] = useState("")

  // Filter laporan berdasarkan pencarian
  const filteredScheduledReports = scheduledReports.filter(report => 
    report.name.toLowerCase().includes(searchQuery.toLowerCase())
  )
  
  const filteredAvailableReports = availableReports.filter(report => 
    report.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    report.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    report.category.toLowerCase().includes(searchQuery.toLowerCase())
  )
  
  const filteredRecentReports = recentReports.filter(report => 
    report.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/analytics">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Laporan</h1>
            <p className="text-muted-foreground">
              Kelola dan buat laporan analitik bisnis Anda
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button>
            <PlusCircle className="h-4 w-4 mr-2" />
            Buat Laporan
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="flex w-full max-w-sm items-center space-x-2">
        <Input 
          type="text" 
          placeholder="Cari laporan..." 
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <Button type="submit" variant="outline">
          Cari
        </Button>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="scheduled" value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="w-full md:w-auto">
          <TabsTrigger value="scheduled">Terjadwal</TabsTrigger>
          <TabsTrigger value="available">Template</TabsTrigger>
          <TabsTrigger value="recent">Terbaru</TabsTrigger>
        </TabsList>
        
        {/* Scheduled Reports Tab Content */}
        <TabsContent value="scheduled" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-1">
            {filteredScheduledReports.length > 0 ? (
              <div className="rounded-md border">
                <div className="relative overflow-x-auto">
                  <table className="w-full text-sm text-left">
                    <thead className="text-xs uppercase bg-muted/50">
                      <tr>
                        <th scope="col" className="px-6 py-3">Nama Laporan</th>
                        <th scope="col" className="px-6 py-3">Jadwal</th>
                        <th scope="col" className="px-6 py-3">Terakhir Dijalankan</th>
                        <th scope="col" className="px-6 py-3">Jadwal Berikutnya</th>
                        <th scope="col" className="px-6 py-3">Format</th>
                        <th scope="col" className="px-6 py-3">Status</th>
                        <th scope="col" className="px-6 py-3">Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredScheduledReports.map(report => (
                        <tr key={report.id} className="border-b">
                          <td className="px-6 py-4 font-medium">
                            <Link href={`/tenant/dashboard/analytics/reports/${report.id}`} className="hover:underline">
                              {report.name}
                            </Link>
                            <div className="text-xs text-muted-foreground mt-1">
                              {report.recipients.length} penerima
                            </div>
                          </td>
                          <td className="px-6 py-4">{report.schedule}</td>
                          <td className="px-6 py-4">{formatDateTime(report.lastRun)}</td>
                          <td className="px-6 py-4">{formatDateTime(report.nextRun)}</td>
                          <td className="px-6 py-4">{report.format}</td>
                          <td className="px-6 py-4">
                            <Badge 
                              className={report.status === "active" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}
                            >
                              {report.status === "active" ? "Aktif" : "Paused"}
                            </Badge>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex space-x-2">
                              <Button variant="outline" size="sm">
                                <Settings className="h-3.5 w-3.5" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <FileDown className="h-3.5 w-3.5" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <div className="border rounded-md p-8 text-center">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">Tidak ada laporan terjadwal</h3>
                <p className="text-muted-foreground mb-4">
                  Anda belum memiliki laporan terjadwal. Buat jadwal laporan baru untuk menerima data secara teratur.
                </p>
                <Button>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Jadwalkan Laporan
                </Button>
              </div>
            )}
          </div>
        </TabsContent>
        
        {/* Available Reports Tab Content */}
        <TabsContent value="available" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredAvailableReports.map(report => (
              <Card key={report.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-base">{report.name}</CardTitle>
                      <div className="flex items-center mt-1">
                        <Badge variant="outline" className="text-xs">{report.category}</Badge>
                        <div className="flex items-center ml-2 text-xs text-muted-foreground">
                          <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                          <span>{report.popularity}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pb-3">
                  <p className="text-sm text-muted-foreground mb-3">
                    {report.description}
                  </p>
                  <div className="flex flex-wrap gap-1 mb-4">
                    {report.formats.map(format => (
                      <Badge key={format} variant="secondary" className="text-xs">
                        {format}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex justify-between">
                    <Button variant="outline" size="sm">
                      <FileText className="h-4 w-4 mr-2" />
                      Pratinjau
                    </Button>
                    <Button size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Buat
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {filteredAvailableReports.length === 0 && (
            <div className="border rounded-md p-8 text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">Tidak ada template laporan ditemukan</h3>
              <p className="text-muted-foreground mb-4">
                Tidak ada template laporan yang sesuai dengan pencarian Anda. Coba kata kunci lain.
              </p>
            </div>
          )}
        </TabsContent>
        
        {/* Recent Reports Tab Content */}
        <TabsContent value="recent" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-1">
            {filteredRecentReports.length > 0 ? (
              <div className="rounded-md border">
                <div className="relative overflow-x-auto">
                  <table className="w-full text-sm text-left">
                    <thead className="text-xs uppercase bg-muted/50">
                      <tr>
                        <th scope="col" className="px-6 py-3">Nama Laporan</th>
                        <th scope="col" className="px-6 py-3">Dibuat Pada</th>
                        <th scope="col" className="px-6 py-3">Format</th>
                        <th scope="col" className="px-6 py-3">Ukuran</th>
                        <th scope="col" className="px-6 py-3">Dibuat Oleh</th>
                        <th scope="col" className="px-6 py-3">Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredRecentReports.map(report => (
                        <tr key={report.id} className="border-b">
                          <td className="px-6 py-4 font-medium">
                            {report.name}
                          </td>
                          <td className="px-6 py-4">{formatDateTime(report.generatedOn)}</td>
                          <td className="px-6 py-4">
                            <Badge variant="outline">{report.format}</Badge>
                          </td>
                          <td className="px-6 py-4">{report.size}</td>
                          <td className="px-6 py-4">{report.generatedBy}</td>
                          <td className="px-6 py-4">
                            <div className="flex space-x-2">
                              <Button variant="outline" size="sm">
                                <FileText className="h-3.5 w-3.5 mr-1" />
                                Lihat
                              </Button>
                              <Button variant="outline" size="sm">
                                <Download className="h-3.5 w-3.5 mr-1" />
                                Unduh
                              </Button>
                              <Button variant="outline" size="sm">
                                <Repeat className="h-3.5 w-3.5 mr-1" />
                                Buat Ulang
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <div className="border rounded-md p-8 text-center">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">Tidak ada laporan terbaru</h3>
                <p className="text-muted-foreground mb-4">
                  Anda belum membuat laporan apapun. Buat laporan baru untuk melihat data bisnis Anda.
                </p>
                <Button>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Buat Laporan
                </Button>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 