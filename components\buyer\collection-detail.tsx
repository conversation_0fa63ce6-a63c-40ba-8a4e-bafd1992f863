"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ChevronLeft, Settings, Grid3X3, List, Globe, Lock, Info, Share2, ShoppingCart, Trash2, Eye, Plus } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"
import { ProductQuickView } from "@/components/buyer/product-quick-view"
import { ShareProduct } from "@/components/buyer/share-product"
import { ShareCollection } from "@/components/buyer/share-collection"
import { DeleteConfirmationDialog } from "@/components/buyer/delete-confirmation-dialog"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"

interface Collection {
  id: string
  name: string
  description: string
  isPublic: boolean
  items: string[]
  createdAt: string
  updatedAt: string
}

interface WishlistItem {
  id: string
  name: string
  price: number
  originalPrice: number
  image: string
  inStock: boolean
  store: string
  storeId: string
  dateAdded: string
  priceDropped: boolean
  productId: string
}

interface CollectionDetailProps {
  collectionId: string
}

export function CollectionDetail({ collectionId }: CollectionDetailProps) {
  const [collection, setCollection] = useState<Collection | null>(null)
  const [items, setItems] = useState<WishlistItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const router = useRouter()
  
  // Dialog untuk menambahkan produk
  const [isAddProductDialogOpen, setIsAddProductDialogOpen] = useState(false)
  const [availableProducts, setAvailableProducts] = useState<WishlistItem[]>([])
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [isAddingProducts, setIsAddingProducts] = useState(false)

  // Fungsi untuk mengambil detail koleksi
  const fetchCollectionDetail = async () => {
    setIsLoading(true)
    try {
      // Ambil detail koleksi
      const collectionResponse = await fetch(`/api/collections/${collectionId}`)
      if (!collectionResponse.ok) {
        throw new Error('Gagal mengambil detail koleksi')
      }
      const collectionData = await collectionResponse.json()
      setCollection(collectionData)

      // Ambil data produk dalam koleksi dari wishlist
      const wishlistResponse = await fetch('/api/wishlist')
      if (!wishlistResponse.ok) {
        throw new Error('Gagal mengambil data wishlist')
      }
      const wishlistData = await wishlistResponse.json()

      // Filter item yang ada dalam koleksi
      const collectionItems = wishlistData.filter((item: WishlistItem) => 
        collectionData.items.includes(item.id)
      )
      setItems(collectionItems)
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal memuat detail koleksi",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fungsi untuk menghapus item dari koleksi
  const removeItemFromCollection = async (itemId: string) => {
    try {
      const response = await fetch(`/api/collections/${collectionId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          itemId,
          action: 'removeItem'
        }),
      })

      if (!response.ok) {
        throw new Error('Gagal menghapus item dari koleksi')
      }

      // Update state lokal
      setItems(prev => prev.filter(item => item.id !== itemId))
      
      if (collection) {
        setCollection({
          ...collection,
          items: collection.items.filter(id => id !== itemId)
        })
      }

      toast({
        title: "Berhasil",
        description: "Item berhasil dihapus dari koleksi",
      })
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal menghapus item dari koleksi",
        variant: "destructive",
      })
    }
  }

  // Fungsi untuk menambahkan item ke keranjang
  const addToCart = async (item: WishlistItem) => {
    try {
      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: item.productId,
          name: item.name,
          price: item.price,
          image: item.image,
          store: item.store,
          storeId: item.storeId,
          quantity: 1
        }),
      })
      
      if (!response.ok) {
        throw new Error('Gagal menambahkan ke keranjang')
      }
      
      toast({
        title: "Berhasil",
        description: `${item.name} ditambahkan ke keranjang`,
      })
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal menambahkan item ke keranjang",
        variant: "destructive",
      })
    }
  }

  // Fungsi untuk melihat detail produk
  const viewProductDetail = (productId: string) => {
    router.push(`/products/${productId}`)
  }

  // Fungsi untuk memformat tanggal
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Fungsi untuk mengambil produk yang tersedia dari wishlist
  const fetchAvailableProducts = async () => {
    try {
      const wishlistResponse = await fetch('/api/wishlist')
      if (!wishlistResponse.ok) {
        throw new Error('Gagal mengambil data wishlist')
      }
      const wishlistData = await wishlistResponse.json()
      
      // Filter produk yang belum ada di koleksi
      if (collection) {
        const availableItems = wishlistData.filter(
          (item: WishlistItem) => !collection.items.includes(item.id)
        )
        setAvailableProducts(availableItems)
      }
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal memuat daftar produk",
        variant: "destructive",
      })
    }
  }

  // Fungsi untuk membuka dialog tambah produk
  const openAddProductDialog = () => {
    fetchAvailableProducts()
    setSelectedProducts([])
    setIsAddProductDialogOpen(true)
  }

  // Fungsi untuk menangani toggle produk yang dipilih
  const toggleProductSelection = (productId: string) => {
    setSelectedProducts(prev => 
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }

  // Fungsi untuk menambahkan produk ke koleksi
  const addProductsToCollection = async () => {
    if (selectedProducts.length === 0) {
      toast({
        title: "Peringatan",
        description: "Pilih minimal satu produk untuk ditambahkan",
      })
      return
    }

    setIsAddingProducts(true)
    try {
      // Panggil API untuk setiap produk yang dipilih
      for (const itemId of selectedProducts) {
        const response = await fetch(`/api/collections/${collectionId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            itemId,
            action: 'addItem'
          }),
        })

        if (!response.ok) {
          throw new Error('Gagal menambahkan produk ke koleksi')
        }
      }

      // Ambil data koleksi yang baru
      await fetchCollectionDetail()
      
      toast({
        title: "Berhasil",
        description: `${selectedProducts.length} produk berhasil ditambahkan ke koleksi`,
      })
      
      // Tutup dialog
      setIsAddProductDialogOpen(false)
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Gagal menambahkan produk ke koleksi",
        variant: "destructive",
      })
    } finally {
      setIsAddingProducts(false)
    }
  }

  // Effect untuk mengambil data koleksi saat komponen dimuat
  useEffect(() => {
    fetchCollectionDetail()
  }, [collectionId])

  // Tampilkan loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="gap-1">
            <ChevronLeft className="h-4 w-4" />
            <Skeleton className="h-4 w-16" />
          </Button>
        </div>
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
          {Array(6).fill(0).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <Skeleton className="aspect-square w-full" />
              <CardContent className="p-3">
                <Skeleton className="mb-1 h-4 w-16" />
                <Skeleton className="h-5 w-full" />
                <Skeleton className="mt-1 h-5 w-24" />
                <Skeleton className="mt-2 h-8 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // Tampilkan pesan jika koleksi tidak ditemukan
  if (!collection) {
    return (
      <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">
        <div className="mb-4 rounded-full bg-muted p-4">
          <Info className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="mb-2 text-xl font-medium">Koleksi Tidak Ditemukan</h3>
        <p className="mb-6 text-muted-foreground">
          Koleksi yang Anda cari tidak ditemukan atau telah dihapus.
        </p>
        <Button onClick={() => router.push('/buyer/dashboard/collections')}>
          Kembali ke Daftar Koleksi
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button 
          variant="ghost" 
          size="sm" 
          className="gap-1"
          onClick={() => router.push('/buyer/dashboard/collections')}
        >
          <ChevronLeft className="h-4 w-4" />
          <span>Kembali ke Koleksi</span>
        </Button>
      </div>

      <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold tracking-tight">{collection.name}</h1>
            {collection.isPublic ? (
              <Badge variant="outline" className="flex items-center gap-1">
                <Globe className="h-3 w-3" />
                <span>Publik</span>
              </Badge>
            ) : (
              <Badge variant="outline" className="flex items-center gap-1">
                <Lock className="h-3 w-3" />
                <span>Privat</span>
              </Badge>
            )}
          </div>
          {collection.description && (
            <p className="mt-1 text-muted-foreground">{collection.description}</p>
          )}
          <div className="mt-2 text-sm text-muted-foreground">
            <span>{collection.items.length} item • </span>
            <span>Dibuat {formatDate(collection.createdAt)}</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="default" 
            className="gap-1"
            onClick={openAddProductDialog}
          >
            <Plus className="h-4 w-4" />
            <span>Tambah Produk</span>
          </Button>
          <Button 
            variant="outline" 
            className="gap-1"
            onClick={() => router.push(`/buyer/dashboard/collections/${collectionId}/edit`)}
          >
            <Settings className="h-4 w-4" />
            <span>Edit Koleksi</span>
          </Button>
          <ShareCollection
            collectionId={collection.id}
            collectionName={collection.name}
            isPublic={collection.isPublic}
          >
            <Button 
              variant="outline" 
              className="gap-1"
            >
              <Share2 className="h-4 w-4" />
              <span>Bagikan</span>
            </Button>
          </ShareCollection>
          <div className="flex rounded-md border">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="icon"
              className="rounded-none rounded-l-md"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="icon"
              className="rounded-none rounded-r-md"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Tampilkan pesan jika koleksi kosong */}
      {items.length === 0 ? (
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">
          <div className="mb-4 rounded-full bg-muted p-4">
            <Info className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="mb-2 text-xl font-medium">Koleksi Kosong</h3>
          <p className="mb-6 text-muted-foreground">
            Koleksi ini belum memiliki produk. Tambahkan produk dari halaman wishlist.
          </p>
          <Button onClick={() => router.push('/buyer/dashboard/wishlist')}>
            Lihat Wishlist
          </Button>
        </div>
      ) : viewMode === "grid" ? (
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
          {items.map((item) => (
            <Card key={item.id} className="overflow-hidden">
              <div className="relative">
                <div 
                  className="aspect-square w-full bg-gray-100 cursor-pointer" 
                  onClick={() => viewProductDetail(item.productId)}
                >
                  <img 
                    src={item.image || "/placeholder.svg"} 
                    alt={item.name} 
                    className="h-full w-full object-cover" 
                  />
                </div>
                {!item.inStock && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                    <Badge variant="outline" className="bg-black text-white">
                      Stok Habis
                    </Badge>
                  </div>
                )}
                {item.priceDropped && <Badge className="absolute right-2 top-2 bg-red-500">Turun Harga</Badge>}
                <div className="absolute bottom-2 right-2 flex gap-1">
                  <ProductQuickView
                    productId={item.productId}
                    productName={item.name}
                    productPrice={item.price}
                    productImage={item.image}
                    storeName={item.store}
                    storeId={item.storeId}
                    inStock={item.inStock}
                    originalPrice={item.originalPrice}
                    priceDropped={item.priceDropped}
                    description={`Informasi detail produk untuk ${item.name}. Kunjungi halaman produk untuk melihat lebih detail.`}
                  >
                    <Button 
                      size="icon" 
                      variant="secondary" 
                      className="h-8 w-8 rounded-full"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </ProductQuickView>
                  
                  <ShareProduct
                    productId={item.productId}
                    productName={item.name}
                  >
                    <Button 
                      size="icon" 
                      variant="secondary" 
                      className="h-8 w-8 rounded-full"
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </ShareProduct>
                  
                  <DeleteConfirmationDialog
                    title="Hapus dari Koleksi"
                    description={`Apakah Anda yakin ingin menghapus ${item.name} dari koleksi ini?`}
                    onConfirm={() => removeItemFromCollection(item.id)}
                  >
                    <Button 
                      size="icon" 
                      variant="destructive" 
                      className="h-8 w-8 rounded-full"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </DeleteConfirmationDialog>
                </div>
              </div>
              <CardContent className="p-3">
                <div 
                  className="mb-1 text-xs text-muted-foreground cursor-pointer" 
                  onClick={() => router.push(`/store/${item.storeId}`)}
                >
                  {item.store}
                </div>
                <h3 
                  className="line-clamp-1 font-medium cursor-pointer" 
                  onClick={() => viewProductDetail(item.productId)}
                >
                  {item.name}
                </h3>
                <div className="mt-1 flex items-center gap-2">
                  <p className="font-bold">{formatCurrency(item.price)}</p>
                  {item.priceDropped && (
                    <p className="text-xs text-muted-foreground line-through">{formatCurrency(item.originalPrice)}</p>
                  )}
                </div>
                <Button 
                  className="mt-2 w-full" 
                  size="sm" 
                  disabled={!item.inStock}
                  onClick={() => addToCart(item)}
                >
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  Tambah ke Keranjang
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {items.map((item) => (
            <Card key={item.id}>
              <CardContent className="flex gap-4 p-4">
                <div 
                  className="relative h-24 w-24 flex-shrink-0 cursor-pointer" 
                  onClick={() => viewProductDetail(item.productId)}
                >
                  <img
                    src={item.image || "/placeholder.svg"}
                    alt={item.name}
                    className="h-full w-full rounded-md object-cover"
                  />
                  {!item.inStock && (
                    <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black/60">
                      <Badge variant="outline" className="bg-black text-white">
                        Stok Habis
                      </Badge>
                    </div>
                  )}
                  {item.priceDropped && <Badge className="absolute -right-2 -top-2 bg-red-500">Turun Harga</Badge>}
                </div>
                <div className="flex flex-1 flex-col justify-between">
                  <div>
                    <div 
                      className="text-xs text-muted-foreground cursor-pointer" 
                      onClick={() => router.push(`/store/${item.storeId}`)}
                    >
                      {item.store}
                    </div>
                    <h3 
                      className="font-medium cursor-pointer" 
                      onClick={() => viewProductDetail(item.productId)}
                    >
                      {item.name}
                    </h3>
                    <div className="mt-1 flex items-center gap-2">
                      <p className="font-bold">{formatCurrency(item.price)}</p>
                      {item.priceDropped && (
                        <p className="text-xs text-muted-foreground line-through">
                          {formatCurrency(item.originalPrice)}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Ditambahkan pada {new Date(item.dateAdded).toLocaleDateString("id-ID")}
                  </div>
                </div>
                <div className="flex flex-col items-end justify-between">
                  <div className="flex items-center gap-2">
                    <ProductQuickView
                      productId={item.productId}
                      productName={item.name}
                      productPrice={item.price}
                      productImage={item.image}
                      storeName={item.store}
                      storeId={item.storeId}
                      inStock={item.inStock}
                      originalPrice={item.originalPrice}
                      priceDropped={item.priceDropped}
                      description={`Informasi detail produk untuk ${item.name}. Kunjungi halaman produk untuk melihat lebih detail.`}
                    >
                      <Button 
                        variant="ghost" 
                        size="sm"
                        className="gap-1"
                      >
                        <Eye className="h-4 w-4" />
                        Lihat
                      </Button>
                    </ProductQuickView>
                    
                    <ShareProduct
                      productId={item.productId}
                      productName={item.name}
                    >
                      <Button 
                        variant="ghost" 
                        size="sm"
                        className="gap-1"
                      >
                        <Share2 className="h-4 w-4" />
                        Bagikan
                      </Button>
                    </ShareProduct>
                    
                    <DeleteConfirmationDialog
                      title="Hapus dari Koleksi"
                      description={`Apakah Anda yakin ingin menghapus ${item.name} dari koleksi ini?`}
                      onConfirm={() => removeItemFromCollection(item.id)}
                    >
                      <Button 
                        variant="ghost" 
                        size="sm"
                        className="gap-1 text-red-500"
                      >
                        <Trash2 className="h-4 w-4" />
                        Hapus
                      </Button>
                    </DeleteConfirmationDialog>
                  </div>
                  <Button 
                    className="w-full" 
                    size="sm" 
                    disabled={!item.inStock}
                    onClick={() => addToCart(item)}
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Tambah ke Keranjang
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Dialog untuk menambahkan produk */}
      <Dialog open={isAddProductDialogOpen} onOpenChange={setIsAddProductDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Tambah Produk ke Koleksi</DialogTitle>
            <DialogDescription>
              Pilih produk dari wishlist Anda untuk ditambahkan ke koleksi ini.
            </DialogDescription>
          </DialogHeader>
          
          {availableProducts.length === 0 ? (
            <div className="py-6 text-center">
              <p className="text-muted-foreground">
                Tidak ada produk baru yang dapat ditambahkan ke koleksi ini.
                Semua produk dari wishlist Anda sudah ada dalam koleksi ini.
              </p>
            </div>
          ) : (
            <ScrollArea className="h-[60vh] md:h-[40vh]">
              <div className="space-y-4">
                {availableProducts.map((product) => (
                  <div key={product.id} className="flex items-start space-x-3 py-2">
                    <Checkbox
                      id={`product-${product.id}`}
                      checked={selectedProducts.includes(product.id)}
                      onCheckedChange={() => toggleProductSelection(product.id)}
                    />
                    <div className="flex flex-1 gap-3">
                      <div className="relative h-16 w-16 flex-shrink-0">
                        <img
                          src={product.image || "/placeholder.svg"}
                          alt={product.name}
                          className="h-full w-full rounded-md object-cover"
                        />
                        {!product.inStock && (
                          <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black/60">
                            <Badge variant="outline" className="bg-black text-white text-xs">
                              Stok Habis
                            </Badge>
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <label
                          htmlFor={`product-${product.id}`}
                          className="text-sm font-medium cursor-pointer"
                        >
                          {product.name}
                        </label>
                        <p className="text-xs text-muted-foreground">{product.store}</p>
                        <div className="mt-1 flex items-center gap-2">
                          <p className="text-sm font-bold">{formatCurrency(product.price)}</p>
                          {product.priceDropped && (
                            <p className="text-xs text-muted-foreground line-through">
                              {formatCurrency(product.originalPrice)}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsAddProductDialogOpen(false)}
            >
              Batal
            </Button>
            <Button 
              onClick={addProductsToCollection} 
              disabled={selectedProducts.length === 0 || isAddingProducts}
            >
              {isAddingProducts ? (
                <>
                  <span className="animate-spin mr-2">◌</span>
                  Menambahkan...
                </>
              ) : (
                <>
                  Tambahkan {selectedProducts.length > 0 ? `(${selectedProducts.length})` : ''}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 