"use client"

import type React from "react"

import { useState, useEffect, createContext, useContext } from "react"
import { tenantThemesAPI } from "@/lib/api/tenant-themes"
import type { TenantTheme } from "@/lib/models/tenant-theme"

interface TenantThemeContextType {
  theme: TenantTheme | null
  loading: boolean
  error: string | null
}

const TenantThemeContext = createContext<TenantThemeContextType>({
  theme: null,
  loading: true,
  error: null,
})

export const useTenantTheme = () => useContext(TenantThemeContext)

// Default theme untuk fallback
const defaultTheme: TenantTheme = {
  id: "default-theme",
  tenantId: "default",
  name: "Default Theme",
  isActive: true,
  colors: {
    primary: "#3b82f6",
    secondary: "#10b981",
    accent: "#8b5cf6",
    background: "#ffffff",
    foreground: "#0f172a",
    muted: "#f1f5f9",
    mutedForeground: "#64748b",
    border: "#e2e8f0",
    input: "#e2e8f0",
    card: "#ffffff",
    cardForeground: "#0f172a",
    destructive: "#ef4444",
    destructiveForeground: "#ffffff",
  },
  fonts: {
    heading: "Inter, sans-serif",
    body: "Inter, sans-serif",
  },
  logo: {
    main: "/your-logo.png",
    favicon: "/favicon.ico",
  },
  customCSS: "",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}

interface TenantThemeProviderProps {
  tenantId?: string
  domain?: string
  children: React.ReactNode
}

export function TenantThemeProvider({ tenantId, domain, children }: TenantThemeProviderProps) {
  const [theme, setTheme] = useState<TenantTheme | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchTheme = async () => {
      setLoading(true)
      try {
        let themeData: TenantTheme | null = null

        if (domain) {
          console.log(`Fetching theme for domain: ${domain}`)
          themeData = await tenantThemesAPI.getByDomain(domain)
        } else if (tenantId) {
          console.log(`Fetching active theme for tenant: ${tenantId}`)
          themeData = await tenantThemesAPI.getActiveTenantTheme(tenantId)
        }

        if (themeData) {
          console.log("Theme fetched successfully:", themeData.name)
          setTheme(themeData)
          setError(null)
          // Apply theme to document
          applyTheme(themeData)
        } else {
          console.log("No theme found, using default theme")
          setTheme(defaultTheme)
          setError("No theme found. Using default theme.")
          // Apply default theme
          applyTheme(defaultTheme)
        }
      } catch (err) {
        console.error("Error fetching tenant theme:", err)
        setError("Failed to load theme. Using default theme.")
        // Apply default theme in case of error
        setTheme(defaultTheme)
        applyTheme(defaultTheme)
      } finally {
        setLoading(false)
      }
    }

    fetchTheme()

    // Cleanup function
    return () => {
      // Remove any theme-specific elements
      const customStyleElement = document.getElementById("tenant-custom-css")
      if (customStyleElement) {
        customStyleElement.remove()
      }

      const faviconElement = document.querySelector("link[rel='icon']") as HTMLLinkElement
      if (faviconElement && faviconElement.dataset.tenant === "true") {
        faviconElement.href = "/favicon.ico"
        faviconElement.dataset.tenant = "false"
      }
    }
  }, [tenantId, domain])

  // Apply theme to document
  const applyTheme = (theme: TenantTheme) => {
    // Apply CSS variables
    const root = document.documentElement

    // Colors
    root.style.setProperty("--primary", hexToHsl(theme.colors.primary))
    root.style.setProperty("--secondary", hexToHsl(theme.colors.secondary))
    root.style.setProperty("--accent", hexToHsl(theme.colors.accent))
    root.style.setProperty("--background", hexToHsl(theme.colors.background))
    root.style.setProperty("--foreground", hexToHsl(theme.colors.foreground))
    root.style.setProperty("--muted", hexToHsl(theme.colors.muted))
    root.style.setProperty("--muted-foreground", hexToHsl(theme.colors.mutedForeground))
    root.style.setProperty("--border", hexToHsl(theme.colors.border))
    root.style.setProperty("--input", hexToHsl(theme.colors.input))
    root.style.setProperty("--card", hexToHsl(theme.colors.card))
    root.style.setProperty("--card-foreground", hexToHsl(theme.colors.cardForeground))
    root.style.setProperty("--destructive", hexToHsl(theme.colors.destructive))
    root.style.setProperty("--destructive-foreground", hexToHsl(theme.colors.destructiveForeground))

    // Fonts
    if (theme.fonts.heading) {
      root.style.setProperty("--font-heading", theme.fonts.heading)
    }

    if (theme.fonts.body) {
      root.style.setProperty("--font-body", theme.fonts.body)
    }

    // Apply custom CSS if available
    if (theme.customCSS) {
      let customStyleElement = document.getElementById("tenant-custom-css")

      if (!customStyleElement) {
        customStyleElement = document.createElement("style")
        customStyleElement.id = "tenant-custom-css"
        document.head.appendChild(customStyleElement)
      }

      customStyleElement.textContent = theme.customCSS
    }

    // Apply favicon if available
    if (theme.logo.favicon) {
      let faviconElement = document.querySelector("link[rel='icon']") as HTMLLinkElement

      if (!faviconElement) {
        faviconElement = document.createElement("link")
        faviconElement.rel = "icon"
        document.head.appendChild(faviconElement)
      }

      faviconElement.href = theme.logo.favicon
      faviconElement.dataset.tenant = "true"
    }
  }

  // Helper function to convert hex to HSL
  const hexToHsl = (hex: string): string => {
    try {
      // Remove the # if present
      hex = hex.replace(/^#/, "")

      // Parse the hex values
      const r = Number.parseInt(hex.substring(0, 2), 16) / 255
      const g = Number.parseInt(hex.substring(2, 4), 16) / 255
      const b = Number.parseInt(hex.substring(4, 6), 16) / 255

      // Find the min and max values to compute the lightness
      const max = Math.max(r, g, b)
      const min = Math.min(r, g, b)

      // Calculate the lightness
      let l = (max + min) / 2

      // Calculate the saturation
      let s = 0
      if (max !== min) {
        s = l > 0.5 ? (max - min) / (2 - max - min) : (max - min) / (max + min)
      }

      // Calculate the hue
      let h = 0
      if (max !== min) {
        if (max === r) {
          h = (g - b) / (max - min) + (g < b ? 6 : 0)
        } else if (max === g) {
          h = (b - r) / (max - min) + 2
        } else {
          h = (r - g) / (max - min) + 4
        }
        h /= 6
      }

      // Convert to degrees and percentages
      h = Math.round(h * 360)
      s = Math.round(s * 100)
      l = Math.round(l * 100)

      return `${h} ${s}% ${l}%`
    } catch (error) {
      console.error("Error converting hex to HSL:", error, "for hex value:", hex)
      // Return a default value if conversion fails
      return "210 100% 50%"
    }
  }

  return <TenantThemeContext.Provider value={{ theme, loading, error }}>{children}</TenantThemeContext.Provider>
}
