"use client"

import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertTriangle, RefreshCw } from "lucide-react"

export default function TenantsError({ error, reset }) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error)
  }, [error])

  return (
    <div className="flex items-center justify-center min-h-[70vh]">
      <Card className="max-w-md">
        <CardHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <CardTitle>Ter<PERSON><PERSON></CardTitle>
          </div>
          <CardDescription>Terjadi kesalahan saat memuat halaman tenant. Silakan coba lagi.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground">
            <p>Detail kesalahan:</p>
            <pre className="mt-2 rounded bg-slate-100 p-2 text-xs overflow-auto dark:bg-slate-800">
              {error.message || "Unknown error occurred"}
            </pre>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => (window.location.href = "/admin/dashboard")}>
            Kembali ke Dashboard
          </Button>
          <Button onClick={() => reset()} className="gap-1">
            <RefreshCw className="h-4 w-4" />
            Coba Lagi
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
