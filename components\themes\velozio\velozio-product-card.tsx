"use client"

import { Star, Truck } from "lucide-react"
import Image from "next/image"
import { useState } from "react"

export interface ProductCardProps {
  id: number
  name: string
  price: string
  originalPrice?: string
  discount?: string
  image: string
  isMall?: boolean
  rating: number
  sold: number
  shipping: string
  cod?: boolean
}

export function VelozioProductCard({
  name,
  price,
  originalPrice,
  discount,
  image,
  isMall,
  rating,
  sold,
  shipping,
  cod,
}: ProductCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false)

  return (
    <div
      className="bg-white rounded-[3px] overflow-hidden transition-all duration-200 hover:-translate-y-[2px] hover:shadow-md"
      style={{ boxShadow: "0 2px 8px rgba(0,0,0,0.15)" }}
    >
      {/* Product Image */}
      <div className="relative pt-[100%]">
        <div className="absolute inset-0">
          <Image
            src={image || "/placeholder.svg?height=300&width=300&query=product"}
            alt={name}
            fill
            className={`object-cover transition-opacity duration-300 ${imageLoaded ? "opacity-100" : "opacity-0"}`}
            onLoad={() => setImageLoaded(true)}
          />
          {!imageLoaded && <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>}
        </div>

        {/* Mall Badge */}
        {isMall && (
          <div className="absolute top-2 left-2 bg-[#ee4d2d] text-white text-[9px] px-1.5 py-0.5 rounded-sm font-medium">
            Mall
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-2.5">
        {/* Product Name */}
        <h3 className="text-sm text-gray-800 line-clamp-2 h-10 mb-1.5">{name}</h3>

        {/* Rating & Sold */}
        <div className="flex items-center text-xs text-gray-500 mb-1.5">
          <div className="flex items-center">
            <Star className="w-3 h-3 fill-[#ee4d2d] text-[#ee4d2d] mr-0.5" />
            <span className="text-gray-800">{rating.toFixed(1)}</span>
          </div>
          <div className="mx-1.5 w-[1px] h-3 bg-gray-300"></div>
          <span>Terjual {sold}</span>
        </div>

        {/* Shipping Info */}
        <div className="flex items-center text-xs text-gray-600 mb-2">
          <Truck className="w-3 h-3 text-[#00bfa5] mr-1" />
          <span>{shipping}</span>
        </div>

        {/* Price */}
        <div className="flex items-end mb-1">
          <div className="text-[#ee4d2d] font-bold text-base">{price}</div>
          {originalPrice && <div className="ml-2 text-xs text-gray-400 line-through">{originalPrice}</div>}
        </div>

        {/* Discount & COD */}
        <div className="flex justify-between items-center">
          {discount ? (
            <div className="bg-[#ffeee8] text-[#ee4d2d] text-xs px-1 py-0.5 rounded">{discount}</div>
          ) : (
            <div></div>
          )}

          {cod && <div className="bg-[#ee4d2d] text-white text-[9px] px-1 py-0.5 rounded-sm">COD</div>}
        </div>
      </div>
    </div>
  )
}
