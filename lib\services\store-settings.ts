import { getClient } from '@/lib/supabase'

export interface StoreSetting {
  id: string
  setting_key: string
  setting_name: string
  setting_value: string | null
  setting_type: 'text' | 'textarea' | 'number' | 'boolean' | 'select' | 'file' | 'email' | 'tel' | 'json'
  category: string
  description?: string
  options?: Array<{ value: string; label: string }> | null
  validation_rules: {
    required?: boolean
    min?: number
    max?: number
    min_length?: number
    max_length?: number
    pattern?: string
    email?: boolean
    file_types?: string[]
    max_size?: number
    step?: number
    [key: string]: any
  }
  is_public: boolean
  is_required: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export interface StoreSettingFilters {
  category?: string
  is_public?: boolean
  search?: string
}

export interface StoreSettingUpdate {
  setting_value: string
}

export interface StoreSettingCreate {
  setting_key: string
  setting_name: string
  setting_value?: string
  setting_type: StoreSetting['setting_type']
  category: string
  description?: string
  options?: Array<{ value: string; label: string }>
  validation_rules?: object
  is_public?: boolean
  is_required?: boolean
  sort_order?: number
}

class StoreSettingService {
  private supabase = getClient()

  // Get all store settings with optional filters
  async getSettings(filters?: StoreSettingFilters): Promise<StoreSetting[]> {
    let query = this.supabase
      .from('store_settings')
      .select('*')
      .order('category', { ascending: true })
      .order('sort_order', { ascending: true })

    // Apply category filter
    if (filters?.category) {
      query = query.eq('category', filters.category)
    }

    // Apply public filter
    if (filters?.is_public !== undefined) {
      query = query.eq('is_public', filters.is_public)
    }

    // Apply search filter
    if (filters?.search) {
      const searchTerm = `%${filters.search}%`
      query = query.or(`setting_name.ilike.${searchTerm},setting_key.ilike.${searchTerm},description.ilike.${searchTerm}`)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch settings: ${error.message}`)
    }

    return data || []
  }

  // Get settings grouped by category
  async getSettingsByCategory(filters?: StoreSettingFilters): Promise<Record<string, StoreSetting[]>> {
    const settings = await this.getSettings(filters)
    
    const grouped = settings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = []
      }
      acc[setting.category].push(setting)
      return acc
    }, {} as Record<string, StoreSetting[]>)

    return grouped
  }

  // Get single setting by key
  async getSetting(key: string): Promise<StoreSetting | null> {
    const { data, error } = await this.supabase
      .from('store_settings')
      .select('*')
      .eq('setting_key', key)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch setting: ${error.message}`)
    }

    return data
  }

  // Get single setting by ID
  async getSettingById(id: string): Promise<StoreSetting | null> {
    const { data, error } = await this.supabase
      .from('store_settings')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch setting: ${error.message}`)
    }

    return data
  }

  // Update setting value
  async updateSetting(key: string, value: string): Promise<StoreSetting> {
    const { data, error } = await this.supabase
      .from('store_settings')
      .update({ setting_value: value })
      .eq('setting_key', key)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update setting: ${error.message}`)
    }

    return data
  }

  // Update setting by ID
  async updateSettingById(id: string, updates: Partial<StoreSetting>): Promise<StoreSetting> {
    const { data, error } = await this.supabase
      .from('store_settings')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update setting: ${error.message}`)
    }

    return data
  }

  // Update multiple settings at once
  async updateMultipleSettings(settings: Record<string, string>): Promise<StoreSetting[]> {
    const updates = Object.entries(settings).map(([key, value]) => 
      this.supabase
        .from('store_settings')
        .update({ setting_value: value })
        .eq('setting_key', key)
        .select()
        .single()
    )

    const results = await Promise.all(updates)
    
    const updatedSettings: StoreSetting[] = []
    for (const result of results) {
      if (result.error) {
        throw new Error(`Failed to update settings: ${result.error.message}`)
      }
      updatedSettings.push(result.data)
    }

    return updatedSettings
  }

  // Create new setting
  async createSetting(setting: StoreSettingCreate): Promise<StoreSetting> {
    // Check if key already exists
    const existingSetting = await this.getSetting(setting.setting_key)
    if (existingSetting) {
      throw new Error('Setting key already exists')
    }

    const { data, error } = await this.supabase
      .from('store_settings')
      .insert([{
        ...setting,
        setting_value: setting.setting_value || '',
        is_public: setting.is_public !== undefined ? setting.is_public : false,
        is_required: setting.is_required !== undefined ? setting.is_required : false,
        sort_order: setting.sort_order || 0,
        validation_rules: setting.validation_rules || {}
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create setting: ${error.message}`)
    }

    return data
  }

  // Delete setting
  async deleteSetting(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('store_settings')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete setting: ${error.message}`)
    }
  }

  // Get all categories
  async getCategories(): Promise<string[]> {
    const { data, error } = await this.supabase
      .from('store_settings')
      .select('category')
      .order('category')

    if (error) {
      throw new Error(`Failed to fetch categories: ${error.message}`)
    }

    const categories = [...new Set(data.map(item => item.category))]
    return categories
  }

  // Get public settings (for frontend display)
  async getPublicSettings(): Promise<Record<string, string>> {
    const { data, error } = await this.supabase
      .from('store_settings')
      .select('setting_key, setting_value')
      .eq('is_public', true)

    if (error) {
      throw new Error(`Failed to fetch public settings: ${error.message}`)
    }

    const settings = data.reduce((acc, setting) => {
      acc[setting.setting_key] = setting.setting_value || ''
      return acc
    }, {} as Record<string, string>)

    return settings
  }

  // Validate setting value based on validation rules
  validateSettingValue(setting: StoreSetting, value: string): { isValid: boolean; error?: string } {
    const rules = setting.validation_rules

    // Required check
    if (rules.required && (!value || value.trim() === '')) {
      return { isValid: false, error: 'Field ini wajib diisi' }
    }

    // Skip other validations if value is empty and not required
    if (!value || value.trim() === '') {
      return { isValid: true }
    }

    // Type-specific validations
    switch (setting.setting_type) {
      case 'number':
        const numValue = parseFloat(value)
        if (isNaN(numValue)) {
          return { isValid: false, error: 'Harus berupa angka' }
        }
        if (rules.min !== undefined && numValue < rules.min) {
          return { isValid: false, error: `Minimal ${rules.min}` }
        }
        if (rules.max !== undefined && numValue > rules.max) {
          return { isValid: false, error: `Maksimal ${rules.max}` }
        }
        break

      case 'text':
      case 'textarea':
        if (rules.min_length && value.length < rules.min_length) {
          return { isValid: false, error: `Minimal ${rules.min_length} karakter` }
        }
        if (rules.max_length && value.length > rules.max_length) {
          return { isValid: false, error: `Maksimal ${rules.max_length} karakter` }
        }
        if (rules.pattern) {
          const regex = new RegExp(rules.pattern)
          if (!regex.test(value)) {
            return { isValid: false, error: 'Format tidak valid' }
          }
        }
        break

      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
          return { isValid: false, error: 'Format email tidak valid' }
        }
        break

      case 'tel':
        if (rules.pattern) {
          const regex = new RegExp(rules.pattern)
          if (!regex.test(value)) {
            return { isValid: false, error: 'Format nomor telepon tidak valid' }
          }
        }
        break

      case 'boolean':
        if (!['true', 'false'].includes(value.toLowerCase())) {
          return { isValid: false, error: 'Harus berupa true atau false' }
        }
        break

      case 'select':
        if (setting.options) {
          const validValues = setting.options.map(opt => opt.value)
          if (!validValues.includes(value)) {
            return { isValid: false, error: 'Pilihan tidak valid' }
          }
        }
        break
    }

    return { isValid: true }
  }
}

export const storeSettingService = new StoreSettingService()
