import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export function middleware(request: NextRequest) {
  const { pathname, hostname } = request.nextUrl

  // Tambahkan header untuk mencegah caching
  const response = NextResponse.next()
  response.headers.set("Cache-Control", "no-store, max-age=0")

  // Untuk development, kita akan menggunakan path-based routing
  // karena menggunakan localhost/IP address
  if (pathname.startsWith('/tenant/')) {
    // Extract tenant identifier dari path
    const pathParts = pathname.split('/')
    if (pathParts.length >= 3) {
      const tenantSlug = pathParts[2]

      // Set tenant headers untuk digunakan di API routes
      response.headers.set('x-tenant-slug', tenantSlug)
      response.headers.set('x-tenant-type', 'path')
    }
  }

  // Handle admin routes
  if (pathname.startsWith('/admin/')) {
    response.headers.set('x-panel-type', 'admin')
  }

  // Handle app routes (tenant registration/login)
  if (pathname.startsWith('/app/')) {
    response.headers.set('x-panel-type', 'app')
  }

  return response
}

// Konfigurasi untuk menentukan path mana yang akan diproses oleh middleware
export const config = {
  matcher: [
    // Proses semua path kecuali yang disebutkan
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
}
