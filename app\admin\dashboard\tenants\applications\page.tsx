import type { Metadata } from "next"
import { TenantApplicationsList } from "@/components/admin/tenants/tenant-applications-list"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PageHeader } from "@/components/admin/ui/page-header"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Tenant Applications | Sellzio Admin",
  description: "Manage tenant applications in the Sellzio platform",
}

export default function TenantApplicationsPage() {
  return (
    <div className="flex flex-col gap-6">
      <PageHeader
        title="Tenant Applications"
        description="Review and process tenant applications"
        breadcrumbs={[
          { title: "Dashboard", href: "/admin/dashboard" },
          { title: "Tenants", href: "/admin/dashboard/tenants" },
          { title: "Applications", href: "/admin/dashboard/tenants/applications" },
        ]}
        actions={
          <Link href="/admin/dashboard/tenants">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Tenants
            </Button>
          </Link>
        }
      />

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Pending</CardTitle>
            <CardDescription>Applications awaiting review</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">+3 in the last 24 hours</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Approved</CardTitle>
            <CardDescription>Applications approved this month</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">28</div>
            <p className="text-xs text-muted-foreground">+15% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Rejected</CardTitle>
            <CardDescription>Applications rejected this month</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">5</div>
            <p className="text-xs text-muted-foreground">-20% from last month</p>
          </CardContent>
        </Card>
      </div>

      <TenantApplicationsList />
    </div>
  )
}
