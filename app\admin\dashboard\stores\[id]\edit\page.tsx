"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { PageHeader } from "@/components/admin/ui/page-header"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useNotifications } from "@/components/admin/ui/notifications"
import { ArrowLeft, Save } from "lucide-react"

export default function EditStorePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    name: "",
    tenant: "",
    category: "",
    description: "",
    status: "",
  })

  useEffect(() => {
    // Simulasi fetch data
    setTimeout(() => {
      setFormData({
        name: "Fashion Store",
        tenant: "acme",
        category: "clothing",
        description: "A premium fashion store offering the latest trends in clothing and accessories.",
        status: "active",
      })
      setIsLoading(false)
    }, 1000)
  }, [params.id])

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulasi API call
    setTimeout(() => {
      setIsLoading(false)
      addNotification({
        title: "Store Updated",
        message: `${formData.name} has been updated successfully.`,
        type: "success",
      })
      router.push(`/admin/dashboard/stores/${params.id}`)
    }, 1000)
  }

  if (isLoading && !formData.name) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  return (
    <>
      <PageHeader
        title="Edit Store"
        description={`Edit details for ${formData.name}`}
        breadcrumbs={[
          { title: "Dashboard", href: "/admin/dashboard" },
          { title: "Stores", href: "/admin/dashboard/stores" },
          { title: formData.name, href: `/admin/dashboard/stores/${params.id}` },
          { title: "Edit", href: `/admin/dashboard/stores/${params.id}/edit` },
        ]}
        actions={
          <Button variant="outline" onClick={() => router.push(`/admin/dashboard/stores/${params.id}`)}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Store
          </Button>
        }
      />

      <form onSubmit={handleSubmit}>
        <Card>
          <CardContent className="space-y-6 pt-6">
            <div className="space-y-2">
              <Label htmlFor="name">Store Name</Label>
              <Input
                id="name"
                placeholder="Enter store name"
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tenant">Tenant</Label>
              <Select value={formData.tenant} onValueChange={(value) => handleChange("tenant", value)} required>
                <SelectTrigger id="tenant">
                  <SelectValue placeholder="Select tenant" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="acme">Acme Corporation</SelectItem>
                  <SelectItem value="startup">Startup Inc</SelectItem>
                  <SelectItem value="tech">Tech Solutions</SelectItem>
                  <SelectItem value="fashion">Fashion Boutique</SelectItem>
                  <SelectItem value="global">Global Traders</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select value={formData.category} onValueChange={(value) => handleChange("category", value)} required>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="clothing">Clothing</SelectItem>
                  <SelectItem value="electronics">Electronics</SelectItem>
                  <SelectItem value="home">Home & Garden</SelectItem>
                  <SelectItem value="sports">Sports</SelectItem>
                  <SelectItem value="food">Food & Beverage</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Enter store description"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleChange("status", value)}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-4 pt-6">
            <Button
              variant="outline"
              onClick={() => router.push(`/admin/dashboard/stores/${params.id}`)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <span className="animate-spin mr-2">⊚</span>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </>
  )
}
