"use client"

import { useState } from "react"
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type SortingState,
  useReactTable,
  getFilteredRowModel,
  type ColumnFiltersState,
  type VisibilityState,
} from "@tanstack/react-table"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DataTableToolbar } from "@/components/admin/ui/data-table-toolbar"
import { ContextMenu } from "@/components/admin/ui/context-menu"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  searchColumn?: string
  searchPlaceholder?: string
  pageSizeOptions?: number[]
  className?: string
  filterOptions?: any[]
  onFilterChange?: (filters: Record<string, any>) => void
  viewMode?: "list" | "grid" | "card"
  onViewModeChange?: (mode: "list" | "grid" | "card") => void
  bulkActions?: any[]
  onExport?: (format: "csv" | "excel" | "pdf") => void
  primaryAction?: any
  quickActions?: any[]
  onRefresh?: () => void
  getRowId?: (row: TData) => string
  getContextMenu?: (row: TData) => any[]
  getRowClassName?: (row: TData) => string
  onRowClick?: (row: TData) => void
}

export function DataTable<TData, TValue>({
  columns,
  data,
  searchColumn,
  searchPlaceholder = "Search...",
  pageSizeOptions = [10, 20, 30, 40, 50],
  className,
  filterOptions,
  onFilterChange,
  viewMode = "list",
  onViewModeChange,
  bulkActions,
  onExport,
  primaryAction,
  quickActions,
  onRefresh,
  getRowId,
  getContextMenu,
  getRowClassName,
  onRowClick,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  const [searchValue, setSearchValue] = useState("")
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: pageSizeOptions[0],
  })

  // Apply search filter to the specified column
  const handleSearchChange = (value: string) => {
    setSearchValue(value)
    if (searchColumn) {
      table.getColumn(searchColumn)?.setFilterValue(value)
    }
  }

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
    getRowId: getRowId ? (row) => getRowId(row) : undefined,
  })

  // Get selected row IDs
  const selectedIds = table.getSelectedRowModel().rows.map((row) => row.id)

  // Get visible columns for the column visibility toggle
  const visibleColumns = Object.entries(table.getState().columnVisibility)
    .filter(([_, isVisible]) => isVisible)
    .map(([columnId]) => columnId)

  // Map table columns to the format expected by ColumnVisibilityToggle
  const columnOptions = table
    .getAllColumns()
    .filter((column) => column.getCanHide())
    .map((column) => ({
      id: column.id,
      label: column.id.charAt(0).toUpperCase() + column.id.slice(1).replace(/([A-Z])/g, " $1"),
    }))

  // Handle column visibility change
  const handleColumnVisibilityChange = (columnId: string, isVisible: boolean) => {
    table.getColumn(columnId)?.toggleVisibility(isVisible)
  }

  return (
    <div className={className}>
      <DataTableToolbar
        searchValue={searchValue}
        onSearchChange={handleSearchChange}
        filterOptions={filterOptions}
        onFilterChange={onFilterChange}
        viewMode={viewMode}
        onViewModeChange={onViewModeChange}
        selectedIds={selectedIds}
        bulkActions={bulkActions}
        onExport={onExport}
        columns={columnOptions}
        visibleColumns={visibleColumns}
        onColumnVisibilityChange={handleColumnVisibilityChange}
        primaryAction={primaryAction}
        quickActions={quickActions}
        onRefresh={onRefresh}
        className="mb-4"
      />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className={`${getRowClassName ? getRowClassName(row.original) : ""} ${onRowClick ? "cursor-pointer" : ""}`}
                  onClick={() => onRowClick && onRowClick(row.original)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                  {getContextMenu && (
                    <TableCell className="w-10 p-2">
                      <ContextMenu itemId={row.id} actions={getContextMenu(row.original)} className="ml-auto" />
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length + (getContextMenu ? 1 : 0)} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex items-center space-x-2">
          <p className="text-sm text-muted-foreground">
            Showing{" "}
            <span className="font-medium">
              {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1}
            </span>{" "}
            to{" "}
            <span className="font-medium">
              {Math.min(
                (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                table.getFilteredRowModel().rows.length,
              )}
            </span>{" "}
            of <span className="font-medium">{table.getFilteredRowModel().rows.length}</span> results
          </p>
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={(value) => {
              table.setPageSize(Number(value))
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={table.getState().pagination.pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}
